from imports import *

import threading
import asyncio
from aiohttp import web
import json
from urllib.parse import parse_qs
import aiohttp
import os
from json import JSONDecodeError

from endpoints.api_endpoint import APIEndpoint
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import PERMISSION_LEVELS
from endpoints.mybot_generic import MyBot_Generic
from managers.manager_logfire import LogFire
from config import (
    META_APP_ID,
    META_APP_SECRET,
    WHATSAPP_PHONE_NUMBER_ID,
    WHATSAPP_BUSINESS_ACCOUNT_ID,
    WHATSAPP_VERIFY_TOKEN,
    WHATSAPP_ACCESS_TOKEN,
    WHATSAPP_RECIPIENT_WAID,
)

# WhatsApp webhook endpoints for Meta Cloud API
# Configure your webhook URL in Meta Business Manager to point to your local server
# Example: http://your-server-ip:41000/whatsapp/webhook

class MyWhatsAppClient:
    async def on_ready(self):
        await MyWhatsappBot.on_ready()

    async def process_webhook(self, data):
        await MyWhatsappBot.process_webhook(data)

class MyWhatsappBot:
    _instance = None
    _initialized = False
    bot_generic: MyBot_Generic = None

    # WhatsApp Bot client
    client = MyWhatsAppClient()
    
    loop: asyncio.AbstractEventLoop = None
    bot_thread: threading.Thread = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return

        if Globals.is_debug() == True:
            return

        # Get the current event loop or create a new one
        instance.loop = asyncio.new_event_loop()
        
        # Try to get WhatsApp configuration from OAuth if available
        try:
            from endpoints.oauth._verifier_ import OAuth2Verifier
            whatsapp_config = await OAuth2Verifier.get_full_token("whatsapp")
            if whatsapp_config:
                LogFire.log("INIT", "WhatsApp OAuth configuration loaded successfully")
                # Use OAuth recipient phone number if available
                global WHATSAPP_RECIPIENT_WAID
                if whatsapp_config.get("str1"):  # Recipient WhatsApp Phone Number
                    WHATSAPP_RECIPIENT_WAID = whatsapp_config["str1"]
                    LogFire.log("INIT", f"WhatsApp recipient phone number set to: {WHATSAPP_RECIPIENT_WAID}")
                
                # All other parameters are static from config.py
                LogFire.log("INIT", "Using static WhatsApp configuration from config.py")
        except Exception as e:
            LogFire.log("DEBUG", f"WhatsApp OAuth configuration not available, using config.py values: {e}")

        # Function to run in thread
        def run_bot_in_loop():
            asyncio.set_event_loop(instance.loop)  # Set the loop for this thread
            instance.loop.run_until_complete(MyWhatsappBot.run_whatsapp_bot_internal())

        # Add routes to the API endpoint for WhatsApp webhooks
        APIEndpoint.get_instance().aio_app.add_routes([
            web.post('/whatsapp/webhook', instance.whatsapp_webhook),
            web.get('/whatsapp/webhook', instance.whatsapp_verify),
        ])

        # Create and start the thread
        instance.bot_thread = threading.Thread(target=run_bot_in_loop, daemon=True)
        instance.bot_thread.start()
        instance.bot_generic = MyBot_Generic(instance, "Whatsapp")
            
       

        instance._initialized = True

    @classmethod
    async def on_ready(cls):
        LogFire.log("INIT", "WhatsApp bot initialized and ready to receive messages!")

    @classmethod
    async def process_webhook(cls, data):
        """
        Process incoming WhatsApp webhook data.
        """
        try:
            # Extract the message data - this structure will vary based on the WhatsApp API you use
            # This is based on the Meta Cloud API format
            if 'entry' in data and len(data['entry']) > 0:
                for entry in data['entry']:
                    if 'changes' in entry and len(entry['changes']) > 0:
                        for change in entry['changes']:
                            if change.get('field') == 'messages':
                                if 'value' in change and 'messages' in change['value']:
                                    for message in change['value']['messages']:
                                        # Extract message details
                                        message_id = message.get('id')
                                        from_number = message.get('from')

                                        # Extract message text
                                        message_text = ""
                                        if 'text' in message and 'body' in message['text']:
                                            message_text = message['text']['body']

                                        # Process the message
                                        await cls.on_message(message_text, from_number, {"id": message_id, "from": from_number, "text": message_text, "raw_message": message})
        except Exception as error:
            etc.helper_functions.exception_triggered(error)

    @classmethod
    async def on_message(cls, text: str, sender_id: str, original_message):
        """
        Handle incoming messages from WhatsApp.
        """
        if len(text) <= 1:
            return

        LogFire.log("EVENT", f"Received WhatsApp message: {text} from {sender_id}")

        # Check if sender is authorized by verifying their number matches the bot token
        from endpoints.oauth._verifier_ import OAuth2Verifier
        whatsapp_tokens = await OAuth2Verifier.get_full_token("whatsapp")
        
        # Since the on_message triggers on every server, only allow if the message was received from the correct number
        if not whatsapp_tokens or (whatsapp_tokens.get("access_token") != f"+{sender_id}" and 
                                  whatsapp_tokens.get("refresh_token") != f"+{sender_id}" and 
                                  whatsapp_tokens.get("token_type") != f"+{sender_id}"):
            #print(f"Unauthorized WhatsApp message from {sender_id} - not matching configured tokens")
            return

        # Find or create a user for this sender
        user = await ZairaUserManager.get_user(sender_id)
        if user is None:
            # Temporarily create a new user if it's the first message since the server was started
            user = await ZairaUserManager.add_user(
                sender_id,
                PERMISSION_LEVELS.USER,
                ZairaUserManager.create_guid(),
                ZairaUserManager.create_guid()
            )

        async def handle_command():
            LogFire.log("EVENT", f'WhatsApp: {sender_id}: "{text}"')
            attachments = []
            await user.on_message(complete_message=text, calling_bot=cls.get_instance().bot_generic, attachments=attachments, original_message=original_message)

        if user is not None:
            await handle_command()

    @classmethod
    async def send_a_whatsapp_message(cls, message: str, recipient_number: str = None) -> bool:
        """
        Send a message to a WhatsApp user using the Meta Cloud API.
        
        Args:
            message: The message to send
            recipient_number: The recipient's WhatsApp phone number (optional, defaults to WHATSAPP_RECIPIENT_WAID)
        """
        # Input validation
        if not message or not message.strip():
            LogFire.log("DEBUG", "WhatsApp message was empty")
            return False
        
        # Use provided recipient number or fall back to default
        recipient = recipient_number if recipient_number else WHATSAPP_RECIPIENT_WAID
        
        if not recipient:
            LogFire.log("ERROR", "No WhatsApp recipient number available", severity="warning")
            return False
        
        # WhatsApp message length limit (4096 characters)
        if len(message) > 4096:
            LogFire.log("ERROR", f"WhatsApp message too long: {len(message)} characters, max is 4096", severity="warning")
            return False

        try:
            # Make sure we have the required configuration
            if not WHATSAPP_PHONE_NUMBER_ID:
                LogFire.log("ERROR", "WhatsApp Phone Number ID not configured", severity="error")
                return False

            # Use the access token directly
            access_token = WHATSAPP_ACCESS_TOKEN

            # Prepare the API endpoint
            url = f"https://graph.facebook.com/v18.0/{WHATSAPP_PHONE_NUMBER_ID}/messages"

            # Prepare the message payload
            payload = {
                "messaging_product": "whatsapp",
                "recipient_type": "individual",
                "to": recipient,
                "type": "text",
                "text": {
                    "preview_url": False,
                    "body": message
                }
            }

            # Set up headers with the access token
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {access_token}"
            }

            # Convert payload to JSON string (like the working code)
            data = json.dumps(payload)

            # Send the message using aiohttp
            async with aiohttp.ClientSession() as session:
                async with session.post(url, data=data, headers=headers) as response:
                    LogFire.log("DEBUG", f"WhatsApp API response status: {response.status}")
                    LogFire.log("DEBUG", f"WhatsApp API content-type: {response.headers.get('content-type', 'N/A')}")
                    
                    response_text = await response.text()
                    LogFire.log("DEBUG", f"WhatsApp API response body: {response_text}")

                    if response.status == 200:
                        LogFire.log("EVENT", "WhatsApp message sent successfully")
                        return True
                    else:
                        LogFire.log("ERROR", f"Failed to send WhatsApp message. Status code: {response.status}", severity="error")
                        LogFire.log("ERROR", f"WhatsApp API response: {response_text}", severity="error")
                        return False

        except aiohttp.ClientConnectorError as e:
            LogFire.log("ERROR", f"WhatsApp connection error: {str(e)}", severity="error")
            return False
        except Exception as e:
            LogFire.log("ERROR", f"Error sending WhatsApp message: {e}", severity="error")
            return False

    @classmethod
    async def run_whatsapp_bot_internal(cls):
        try:
            # Initialize the WhatsApp bot
            await cls.client.on_ready()
        except Exception as e:
            LogFire.log("ERROR", f"Error running WhatsApp bot: {e}", severity="error")

    # API Endpoints and Webhook handlers
    async def whatsapp_verify(self, request: web.Request) -> web.Response:
        """
        Handle the verification request from WhatsApp.
        WhatsApp sends a GET request with a hub.challenge parameter when you set up a webhook.
        
        Configure in Meta Business Manager:
        1. Go to your WhatsApp Business app configuration
        2. Set webhook URL to: http://your-server-ip:41000/whatsapp/webhook
        3. Set verify token to match WHATSAPP_VERIFY_TOKEN in config.py
        4. Subscribe to 'messages' webhook events
        """
        LogFire.log("EVENT", f"WhatsApp verification request received from IP: {request.remote}")

        # Get query parameters
        query_params = parse_qs(request.query_string)

        # WhatsApp sends these verification parameters
        mode = query_params.get('hub.mode', [None])[0]
        token = query_params.get('hub.verify_token', [None])[0]
        challenge = query_params.get('hub.challenge', [None])[0]

        # Verify token using the one defined at the top of the file
        if mode == 'subscribe' and token == WHATSAPP_VERIFY_TOKEN and challenge:
            LogFire.log("EVENT", "WhatsApp webhook verified successfully")
            return web.Response(text=challenge)
        else:
            LogFire.log("ERROR", f"WhatsApp webhook verification failed. Mode: {mode}, Token: {token}, Challenge: {challenge}", severity="warning")
            return web.Response(status=403)

    async def whatsapp_webhook(self, request: web.Request) -> web.Response:
        """
        Handle incoming messages from WhatsApp.
        
        This endpoint receives POST requests from Meta's WhatsApp Cloud API
        when users send messages to your WhatsApp Business number.
        """
        try:
            if request.content_type != 'application/json':
                return web.Response(status=415)

            # Parse the incoming JSON payload
            body = await request.json()
            LogFire.log("DEBUG", f"Received WhatsApp webhook: {body}")

            # Process the message in the bot's thread
            await MyWhatsappBot.client.process_webhook(body)

            # WhatsApp expects a 200 OK response
            return web.Response(status=200)

        except JSONDecodeError:
            LogFire.log("ERROR", "Invalid JSON in WhatsApp webhook request", severity="warning")
            return web.Response(status=400)
        except Exception as error:
            etc.helper_functions.exception_triggered(error)
            return web.Response(status=500)