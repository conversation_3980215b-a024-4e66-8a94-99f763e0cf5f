<div class="system-container">
    <div class="system-header">
        <div class="system-icon">
            <span class="system-initials">{initials}</span>
        </div>
        <h1 class="system-title">System Information</h1>
        <p class="system-subtitle">System metrics and configuration</p>
        <div class="status-badge active">System Active</div>
    </div>
    
    <!-- System Metrics Grid -->
    <div class="system-metrics-grid">
        <!-- Usage Statistics -->
        <div class="metric-card card">
            <h3 class="card-title">Usage Statistics</h3>
            <div class="metric-stats">
                <div class="stat-item">
                    <div class="stat-value">{tokens_used}</div>
                    <div class="stat-label">Tokens Used</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{storage_used} GB</div>
                    <div class="stat-label">Storage Used</div>
                </div>
                <div class="stat-item">
                    <div class="stat-value">{gb_remaining} GB</div>
                    <div class="stat-label">GB Remaining</div>
                </div>
            </div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: {storage_percentage}%"></div>
            </div>
            <small class="metric-help">Storage usage: {storage_percentage}% of total capacity</small>
        </div>
        
        <!-- System Configuration -->
        <div class="config-card card">
            <h3 class="card-title">System Configuration</h3>
            <form id="systemConfigForm" onsubmit="updateSystemConfig(event)">
                <div class="form-group">
                    <label class="form-checkbox">
                        <input type="checkbox" id="allow_document_generation" {document_generation_checked}>
                        <span class="form-checkbox-label">Allow document generation</span>
                    </label>
                    <small class="form-help">Enable AI-powered document creation and export features.</small>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Data Location</label>
                    <select id="data_location" class="form-input">
                        {location_options}
                    </select>
                    <small class="form-help">Current: {current_data_location}</small>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Organization ID</label>
                    <input type="text" id="organization_id" value="{organization_id}" class="form-input" placeholder="Enter organization identifier">
                </div>
                
                <div class="form-group">
                    <label class="form-label">Privacy Level</label>
                    <select id="privacy_level" class="form-input">
                        {privacy_options}
                    </select>
                    <small class="form-help">Higher levels provide enhanced data protection and security.</small>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Save Configuration</button>
                </div>
            </form>
        </div>
        
        <!-- Active Components -->
        <div class="components-card card">
            <h3 class="card-title">Active Components</h3>
            <div class="component-stats">
                <div class="component-row">
                    <span class="component-label">Active Triggers</span>
                    <span class="component-value active">{active_triggers}</span>
                </div>
                <div class="component-row">
                    <span class="component-label">Disabled Triggers</span>
                    <span class="component-value disabled">{disabled_triggers}</span>
                </div>
                <div class="component-row">
                    <span class="component-label">Total Connectors</span>
                    <span class="component-value">{total_connectors}</span>
                </div>
                <div class="component-row">
                    <span class="component-label">Total Automations</span>
                    <span class="component-value">{total_automations}</span>
                </div>
            </div>
            
            <div class="component-actions">
                <button type="button" class="btn btn-secondary" onclick="refreshComponents()">Refresh Status</button>
            </div>
        </div>
        
        <!-- Data Management -->
        <div class="data-management-card card">
            <h3 class="card-title">Data Management</h3>
            <div class="data-actions">
                <div class="action-item">
                    <h4>Chat History</h4>
                    <p>Remove all chat conversations and message history.</p>
                    <button type="button" class="btn btn-warning" onclick="deleteAllChats()">Delete All Chats</button>
                </div>
                
                <div class="action-item">
                    <h4>Scheduled Requests</h4>
                    <p>Remove all scheduled tasks and automated requests.</p>
                    <button type="button" class="btn btn-warning" onclick="removeScheduledRequests()">Remove Scheduled Requests</button>
                </div>
                
                <div class="action-item danger">
                    <h4>Complete System Reset</h4>
                    <p>Warning: This will permanently delete all system data and configurations.</p>
                    <button type="button" class="btn btn-danger" onclick="systemReset()">Complete System Reset</button>
                </div>
            </div>
        </div>
    </div>
</div>