#!/usr/bin/env python3
"""
Email Test Helper for Real System Integration Tests

This module provides email-specific functionality for test_real files,
including SMTP/IMAP setup, email supervisors, and email-specific utilities.

Usage:
    from tests.test_real.helpers.email_test_helper import EmailTestHelper
    
    class TestEmailFeature(BaseRealTest):
        async def test_send_email(self):
            setup_success = await self.setup_real_system()
            assert setup_success
            
            # Use email helper for category-specific setup
            email_helper = EmailTestHelper(self)
            await email_helper.setup_email_components()
            
            user = await self.create_test_user(email="<EMAIL>")
            bot = self.create_test_bot()
            
            result = await self.execute_and_monitor_request(
                user=user,
                query="send <NAME_EMAIL> about meeting",
                bot=bot,
                timeout=60
            )
            
            self.assert_request_success(result, expected_outputs=["email"])
"""

from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../../'))

from imports import *
from managers.manager_logfire import LogFire
import os
import pytest
from typing import List, Tuple, Dict, Any


class EmailTestHelper:
    """
    Helper class for email-specific test functionality.
    
    This class encapsulates all email-related setup, configuration,
    and utility methods that were previously in BaseRealTest.
    """
    
    def __init__(self, base_test_instance):
        """
        Initialize the email test helper.
        
        Args:
            base_test_instance: Instance of BaseRealTest that this helper supports
        """
        self.base_test = base_test_instance
        self.test_name = getattr(base_test_instance, 'test_name', 'EmailTestHelper')
    
    async def setup_email_components(self):
        """
        Set up email-specific components including generators and senders.
        
        This method creates the email generator and email sender supervisors
        that are required for email testing functionality.
        
        Returns:
            bool: True if setup successful, False otherwise
        """
        try:
            from tasks.processing.task_email_generator import create_task_email_generator
            from tasks.outputs.output_requests.task_out_email import create_supervisor_email_sender
            
            await create_task_email_generator()
            await create_supervisor_email_sender()
            LogFire.log("DEBUG", f"[{self.test_name}] Email supervisors created successfully")
            return True
        except Exception as e:
            LogFire.log("DEBUG", f"[{self.test_name}] Warning: Could not create email supervisors: {e}")
            return False
    
    def get_email_test_documents(self) -> List[Tuple[str, str]]:
        """
        Get email-specific test documents for document stores.
        
        Returns:
            List[Tuple[str, str]]: List of (filename, content) tuples for email testing
        """
        return [
            ("email_templates.txt", "Standard email templates for business communication"),
            ("smtp_config.txt", "SMTP configuration and email settings"),
            ("email_policies.txt", "Email policies and guidelines")
        ]
    
    def get_smtp_oauth_structure(self) -> Dict[str, str]:
        """
        Get the SMTP OAuth token structure for reference.
        
        Returns:
            Dict[str, str]: SMTP token structure mapping
        """
        return {
            "access_token": "smtp.gmail.com",      # Server hostname
            "expires_in": "587",                   # Port number
            "refresh_token": "<EMAIL>",   # Email address
            "token_type": "app_password_here"      # Password/App password
        }
    
    def get_imap_oauth_structure(self) -> Dict[str, str]:
        """
        Get the IMAP OAuth token structure for reference.
        
        Returns:
            Dict[str, str]: IMAP token structure mapping
        """
        return {
            "access_token": "imap.gmail.com",      # Server hostname
            "expires_in": "993",                   # Port (SSL)
            "refresh_token": "<EMAIL>",   # Email address
            "token_type": "app_password_here"      # Password
        }
    
    def get_email_pipeline_steps(self) -> List[str]:
        """
        Get expected pipeline steps for email operations.
        
        Returns:
            List[str]: List of expected pipeline step names
        """
        return [
            "email_generator",
            "email_sender",
            "email_approval"
        ]
    
    async def create_email_test_user(self, 
                                   username: str = None,
                                   email: str = "<EMAIL>"):
        """
        Create a test user specifically configured for email testing.
        
        Args:
            username: Username for the test user (default: auto-generated)
            email: Email address for the user (default: "<EMAIL>")
            
        Returns:
            ZairaUser: The created test user configured for email testing
        """
        if not username:
            username = f"email_user_{len(self.base_test.test_users) + 1}"
        
        return await self.base_test.create_test_user(
            username=username,
            email=email
        )
    
    def assert_email_pipeline_verification(self, result: Dict[str, Any]):
        """
        Assert that email-specific pipeline steps were executed.
        
        Args:
            result: Result dictionary from execute_and_monitor_request()
            
        Returns:
            List[str]: Verified email pipeline steps that were found
        """
        expected_steps = self.get_email_pipeline_steps()
        return self.base_test.assert_pipeline_verification(result, expected_steps)
    
    def get_email_test_examples(self) -> Dict[str, str]:
        """
        Get common email test query examples.
        
        Returns:
            Dict[str, str]: Dictionary mapping test scenario names to query strings
        """
        return {
            "simple_send": "send <NAME_EMAIL> about meeting",
            "with_subject": "send <NAME_EMAIL> with subject 'Test Email'",
            "multiple_recipients": "send <NAME_EMAIL> and <EMAIL>",
            "with_attachment": "send email with <NAME_EMAIL>",
            "reply_request": "reply to the last email received"
        }


def skip_if_no_smtp():
    """Skip test if SMTP is not configured"""
    # For now, assume SMTP is configured via OAuth2Verifier
    # The actual SMTP configuration check will be done at runtime
    return lambda func: func


def skip_if_no_imap():
    """Skip test if IMAP is not configured"""
    # Check if IMAP credentials are available
    imap_configured = os.environ.get('IMAP_CREDENTIALS')
    
    return pytest.mark.skipif(
        not imap_configured,
        reason="IMAP not configured - set IMAP_CREDENTIALS"
    )


# Export public API
__all__ = [
    'EmailTestHelper',
    'skip_if_no_smtp',
    'skip_if_no_imap'
]