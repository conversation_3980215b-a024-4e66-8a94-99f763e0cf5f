=== DEBUG TRACE STARTED at 2025-08-16 20:17:49.998740 ===
[2025-08-16 20:17:51.188] PromptManager imported successfully
[2025-08-16 20:17:51.188] Testing prompt access...
[2025-08-16 20:17:51.194] Prompt found: You are a top-level supervisor managing a conversation between tasks. Use chain of thought reasoning...
[2025-08-16 20:17:51.194] All prompts available:
[2025-08-16 20:17:51.194] Error accessing prompts: type object 'PromptManager' has no attribute 'get_all_prompts'
[2025-08-16 20:17:51.194] Traceback (most recent call last):
[2025-08-16 20:17:51.194]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\test_prompt_access.py", line 17, in test_prompt_access
[2025-08-16 20:17:51.194]     prompts = PromptManager.get_all_prompts()
[2025-08-16 20:17:51.194]               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-08-16 20:17:51.194] AttributeError: type object 'PromptManager' has no attribute 'get_all_prompts'
