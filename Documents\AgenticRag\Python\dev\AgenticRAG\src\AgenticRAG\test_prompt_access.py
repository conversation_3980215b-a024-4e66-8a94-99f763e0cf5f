import sys
import os
sys.path.insert(0, os.path.dirname(__file__))

def test_prompt_access():
    print("Testing prompt access...")
    
    try:
        from managers.manager_prompts import PromptManager
        print("PromptManager imported successfully")
        
        print("Testing prompt access...")
        prompt = PromptManager.get_prompt("Top_Supervisor_CoT_Prompt")
        print(f"Prompt found: {prompt[:100]}...")
        
        print("All prompts available:")
        prompts = PromptManager.get_all_prompts()
        for key in sorted(prompts.keys()):
            print(f"  - {key}")
            
    except Exception as e:
        print(f"Error accessing prompts: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_prompt_access()