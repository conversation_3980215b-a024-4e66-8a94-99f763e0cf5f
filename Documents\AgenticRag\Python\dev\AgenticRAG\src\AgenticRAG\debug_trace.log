=== DEBUG TRACE STARTED at 2025-08-20 15:59:16.958370 ===
[2025-08-20 15:59:48.992] WARNING:discord.client:PyNaCl is not installed, voice will NOT be supported
[2025-08-20 15:59:52.702] c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\langchain\tools\__init__.py:73: LangChainDeprecationWarning: Importing tools from langchain is deprecated. Importing from langchain will no longer be supported as of langchain==0.2.0. Please import from langchain-community instead:

[2025-08-20 15:59:52.702] `from langchain_community.tools import LambdaRuntimeClient`.

[2025-08-20 15:59:52.702] To install langchain-community run `pip install -U langchain-community`.
[2025-08-20 15:59:52.702]   warnings.warn(
[2025-08-20 15:59:52.819] LogEntries database table created/verified
[2025-08-20 15:59:52.959] Logfire project URL: ]8;id=649345;https://logfire-eu.pydantic.dev/askzaira/agentic-rag\https://logfire-eu.pydantic.dev/askzaira/agentic-rag]8;;\
[2025-08-20 15:59:53.897] [MAIN_DEBUG] Globals.is_docker() = False
[2025-08-20 15:59:53.898] [MAIN_DEBUG] main.py getcwd(): C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG
[2025-08-20 15:59:53.898] [MAIN_DEBUG] main.py looking for dev.env at: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG/dev.env
[2025-08-20 15:59:53.899] [MAIN_DEBUG] main.py dev.env exists: True
[2025-08-20 15:59:57.977] 13:59:57.973 [][INIT], '_run_once -> _run': dev_run.py main() started .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 13:59:53.879426
[2025-08-20 15:59:57.978] Database 'vectordb' already exists.
[2025-08-20 15:59:58.029] 13:59:58.027 [Python][INIT], '_run_once -> _run': About to check Claude environment .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 13:59:57.977648
[2025-08-20 15:59:58.095] 13:59:58.093 [Python][INIT], '_run_once -> _run': [Scrubbed due to 'Auth'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 13:59:58.029711
[2025-08-20 15:59:58.126] 13:59:58.123 [Python][INIT], '_run_once -> _run': [Scrubbed due to 'Auth'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 13:59:58.096763
[2025-08-20 16:00:00.944] 14:00:00.942 [Python][INIT], '_run_once -> _run': About to call mainFunc() .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 13:59:58.126784
[2025-08-20 16:00:00.954] INFO:discord.client:logging in using static token
[2025-08-20 16:00:00.955] logging in using static token
[2025-08-20 16:00:01.061] 14:00:01.059 [Python][INIT], '_run_once -> _run': mainFunc() started .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:00.944451
[2025-08-20 16:00:01.072] 14:00:01.070 [Python][INIT], '_run_once -> _run': No Claude environment detected .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:01.062001
[2025-08-20 16:00:01.079] 14:00:01.077 [Python][INIT], '_run_once -> _run': About to set up data directories .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:01.073001
[2025-08-20 16:00:01.087] 14:00:01.085 [Python][INIT], '_run_once -> _run': Using data subfolder: AskZaira .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:01.080009
[2025-08-20 16:00:01.110] 14:00:01.107 [Python][DEBUG], '_run_once -> _run': main.py environment check: Globals.is_docker() = False .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:01.102002
[2025-08-20 16:00:01.118] 14:00:01.116 [Python][DEBUG], '_run_once -> _run': main.py getcwd(): C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:01.110003
[2025-08-20 16:00:01.127] 14:00:01.125 [Python][DEBUG], '_run_once -> _run': main.py looking for dev.env at: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG/dev.env .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:01.118005
[2025-08-20 16:00:01.136] 14:00:01.133 [Python][DEBUG], '_run_once -> _run': main.py dev.env exists: True .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:01.127005
[2025-08-20 16:00:01.143] 14:00:01.141 [Python][DEBUG], '_run_once -> _run': After loading env files - ZAIRA_NETWORK_NAME: 'Python' .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:01.136016
[2025-08-20 16:00:01.148] INFO:managers.manager_users:Creating a new singleton instance of ZairaUserManager.
[2025-08-20 16:00:01.148] Creating a new singleton instance of ZairaUserManager.
[2025-08-20 16:00:01.149] INFO:managers.manager_users:Pydantic models rebuilt successfully to resolve forward references
[2025-08-20 16:00:01.150] Pydantic models rebuilt successfully to resolve forward references
[2025-08-20 16:00:01.151] INFO:managers.manager_users:User added: SYSTEM with GUID: 00000000-0000-0000-0000-000000000001
[2025-08-20 16:00:01.151] User added: SYSTEM with GUID: 00000000-0000-0000-0000-000000000001
[2025-08-20 16:00:01.154] WARNING:llama_index.vector_stores.qdrant.base:Both client and aclient are provided. If using `:memory:` mode, the data between clients is not synced.
[2025-08-20 16:00:01.155] Both client and aclient are provided. If using `:memory:` mode, the data between clients is not synced.
[2025-08-20 16:00:02.123] INFO:discord.gateway:Shard ID None has connected to Gateway (Session ID: 8180953daf2912db4bcc37694f5f45e8).
[2025-08-20 16:00:02.123] Shard ID None has connected to Gateway (Session ID: 8180953daf2912db4bcc37694f5f45e8).
[2025-08-20 16:00:03.652] BertForMaskedLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From 👉v4.50👈 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
[2025-08-20 16:00:03.652]   - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
[2025-08-20 16:00:03.652]   - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
[2025-08-20 16:00:03.652]   - If you are not the owner of the model architecture class, please contact the model code owner to update it.
[2025-08-20 16:00:05.990] WARNING:root:'doc_id' is deprecated and 'id_' will be used instead
[2025-08-20 16:00:05.991] 'doc_id' is deprecated and 'id_' will be used instead
[2025-08-20 16:00:08.858] 14:00:08.856 [Python][INIT], '_run_once -> _run': About to call init() .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:01.144004
[2025-08-20 16:00:09.736] 14:00:09.734 [Python][INIT], '_run_once -> _run': ZairaControl endpoint routes registered successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:08.858988
[2025-08-20 16:00:09.752] 14:00:09.751 [Python][INIT], '_run_once -> _run': [Scrubbed due to 'Auth'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.736013
[2025-08-20 16:00:09.760] 14:00:09.757 [Python][INIT], '_run_once -> _run': Discord bot setup called .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.753536
[2025-08-20 16:00:09.778] 14:00:09.776 [Python][INIT], '_run_once -> _run': Starting Discord bot connection... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.760534
[2025-08-20 16:00:09.787] 14:00:09.785 [Python][INIT], '_run_once -> _run': Discord is listening on guild ID: 1313130978343911505 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.779543
[2025-08-20 16:00:09.796] 14:00:09.793 [Python][INIT], '_run_once -> _run': Discord bot thread started .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.787540
[2025-08-20 16:00:09.804] 14:00:09.802 [Python][INIT], '_run_once -> _run': ZairaControl endpoint routes registered successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.796535
[2025-08-20 16:00:09.814] 14:00:09.812 [Python][INIT], '_run_once -> _run': Zaira#0883 has connected to Discord! .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.805559
[2025-08-20 16:00:09.823] 14:00:09.820 [Python][INIT], '_run_once -> _run': Connected to guild: BVS solutions (id: 1313130978343911505) .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.815547
[2025-08-20 16:00:09.832] 14:00:09.830 [Python][EVENT], '_run_once -> _run': Broadcast sent to zaira in BVS solutions .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.823411
[2025-08-20 16:00:09.840] 14:00:09.838 [Python][INIT], '_run_once -> _run': Database tables and indexes created/verified .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.833413
[2025-08-20 16:00:09.849] 14:00:09.847 [Python][INIT], '_run_once -> _run': Setting up new scheduled request architecture .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.841415
[2025-08-20 16:00:09.857] 14:00:09.855 [Python][INIT], '_run_once -> _run': ScheduledRequestIntegrationAdapter initialized successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.850409
[2025-08-20 16:00:09.867] 14:00:09.865 [Python][INIT], '_run_once -> _run': New scheduled request system ready .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.858412
[2025-08-20 16:00:09.907] 14:00:09.905 [Python][INIT], '_run_once -> _run': ZairaUser created: SYSTEM .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.900445
[2025-08-20 16:00:09.927] 14:00:09.924 [Python][USER], '_run_once -> _run': User created with GUID 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 1301740a-959b-40ac-bdd5-59f9013838e6 at 2025-08-20 14:00:09.908425
[2025-08-20 16:00:09.987] 14:00:09.982 [Python][USER], '_run_once -> _run': SYSTEM user created successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.971695
[2025-08-20 16:00:10.013] 14:00:10.008 [Python][USER], '_run_once -> _run': SystemUserManager initialized with SYSTEM user .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:09.999677
[2025-08-20 16:00:10.035] 14:00:10.031 [Python][EVENT], '_run_once -> _run': Loading stored index .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:10.014729
[2025-08-20 16:00:10.049] 14:00:10.045 [Python][EVENT], '_run_once -> _run': Index loaded .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:10.035779
[2025-08-20 16:00:10.070] 14:00:10.066 [Python][DEBUG], '_run_once -> _run': Web server current working directory: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:10.058845
[2025-08-20 16:00:10.082] 14:00:10.077 [Python][DEBUG], '_run_once -> _run': Web server checking for dev.env at: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG/dev.env .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:10.070360
[2025-08-20 16:00:10.090] 14:00:10.088 [Python][DEBUG], '_run_once -> _run': Web server dev.env exists: True .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:10.082580
[2025-08-20 16:00:10.100] 14:00:10.098 [Python][DEBUG], '_run_once -> _run': Web server working directory is correct: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:10.091338
[2025-08-20 16:00:10.108] 14:00:10.105 [Python][INIT], '_run_once -> _run': Server started at http://0.0.0.0:41000 (registered 1 total servers) .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:10.100849
[2025-08-20 16:00:10.115] 14:00:10.113 [Python][INIT], '_run_once -> _run': Server started at http://0.0.0.0:41000 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:10.108558
[2025-08-20 16:00:10.123] 14:00:10.120 [Python][INIT], '_run_once -> _run': Starting Slack Bot Socket Mode client... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:10.116553
[2025-08-20 16:00:10.380] INFO:slack_sdk.socket_mode.aiohttp:A new session (s_127410820557) has been established
[2025-08-20 16:00:10.380] A new session (s_127410820557) has been established
[2025-08-20 16:00:10.387] INFO:managers.manager_users:SYSTEM user GUID requested: 00000000-0000-0000-0000-000000000001
[2025-08-20 16:00:10.388] SYSTEM user GUID requested: 00000000-0000-0000-0000-000000000001
[2025-08-20 16:00:10.388] INFO:managers.manager_users:SYSTEM user found in users dict
[2025-08-20 16:00:10.388] SYSTEM user found in users dict
[2025-08-20 16:00:11.592] 14:00:11.590 [Python][REQUEST], '_run_once -> _run': Task c495831e-d3f1-444a-b2ab-b31c9cea4da5 available for recovery .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:10.486718
[2025-08-20 16:00:11.595] INFO:managers.manager_users:Generated new User GUID: cf64f0ed-990e-471f-ab93-aa716727975d
[2025-08-20 16:00:11.595] Generated new User GUID: cf64f0ed-990e-471f-ab93-aa716727975d
[2025-08-20 16:00:11.596] INFO:managers.manager_users:Generated new User GUID: 982e3dcb-8930-4448-92e8-765a2283f5b1
[2025-08-20 16:00:11.596] Generated new User GUID: 982e3dcb-8930-4448-92e8-765a2283f5b1
[2025-08-20 16:00:11.597] INFO:managers.manager_users:User added: Python with GUID: cf64f0ed-990e-471f-ab93-aa716727975d
[2025-08-20 16:00:11.597] User added: Python with GUID: cf64f0ed-990e-471f-ab93-aa716727975d
[2025-08-20 16:00:11.797] Please ask your company-specific question: [2025-08-20 16:00:11.804] 14:00:11.802 [Python][REQUEST], '_run_once -> _run': Task 9c35ee38-958c-47a9-962f-8fe7660df928 available for recovery .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:11.593262
[2025-08-20 16:00:11.813] 14:00:11.810 [Python][INIT], '_run_once -> _run': UserScheduledRequestManager created for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:11.805261
[2025-08-20 16:00:11.820] 14:00:11.818 [Python][USER], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 932e5f1b-080f-489e-88ca-5ac32486b926 at 2025-08-20 14:00:11.813264
[2025-08-20 16:00:11.829] 14:00:11.827 [Python][REQUEST], '_run_once -> _run': Thread started for scheduled request c495831e-d3f1-444a-b2ab-b31c9cea4da5 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:11.820261
[2025-08-20 16:00:11.837] 14:00:11.835 [Python][REQUEST], '_run_once -> _run': Thread started for scheduled request 9c35ee38-958c-47a9-962f-8fe7660df928 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:11.830290
[2025-08-20 16:00:11.845] 14:00:11.843 [Python][REQUEST], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 932e5f1b-080f-489e-88ca-5ac32486b926 at 2025-08-20 14:00:11.837261
[2025-08-20 16:00:11.854] 14:00:11.852 [Python][INIT], '_run_once -> _run': New request started with question of length: 74. .  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 932e5f1b-080f-489e-88ca-5ac32486b926 at 2025-08-20 14:00:11.846283
[2025-08-20 16:00:11.864] 14:00:11.861 [Python][REQUEST], '_run_once -> _run': Waiting 399589.454675 seconds until next execution at 2025-08-25 05:00:00+00:00 .  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session dacca1e6-859c-4d1a-a84d-2eb7c26e591b at 2025-08-20 14:00:11.855258
[2025-08-20 16:00:11.872] 14:00:11.869 [Python][INIT], '_run_once -> _run': New request started with question of length: 40. .  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 932e5f1b-080f-489e-88ca-5ac32486b926 at 2025-08-20 14:00:11.864263
[2025-08-20 16:00:11.881] 14:00:11.878 [Python][TASK], '_run_once -> _run': Using provided schedule parameters .  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 932e5f1b-080f-489e-88ca-5ac32486b926 at 2025-08-20 14:00:11.872259
[2025-08-20 16:00:11.889] 14:00:11.886 [Python][EVENT], '_run_once -> _run': Added request c495831e-d3f1-444a-b2ab-b31c9cea4da5 to user SYSTEM. Total requests: 1 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:11.881262
[2025-08-20 16:00:11.898] 14:00:11.896 [Python][REQUEST], '_run_once -> _run': Started thread for scheduled request c495831e-d3f1-444a-b2ab-b31c9cea4da5 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:11.889262
[2025-08-20 16:00:11.907] 14:00:11.904 [Python][REQUEST], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 932e5f1b-080f-489e-88ca-5ac32486b926 at 2025-08-20 14:00:11.899268
[2025-08-20 16:00:11.918] 14:00:11.915 [Python][REQUEST], '_run_once -> _run': Created scheduled request c495831e-d3f1-444a-b2ab-b31c9cea4da5 for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:11.908264
[2025-08-20 16:00:11.935] 14:00:11.930 [Python][REQUEST], '_run_once -> _run': Waiting 1799.999005 seconds until next execution at 2025-08-20 14:30:10.543323+00:00 .  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 932e5f1b-080f-489e-88ca-5ac32486b926 at 2025-08-20 14:00:11.918265
[2025-08-20 16:00:11.948] 14:00:11.946 [Python][REQUEST], '_run_once -> _run': Successfully recovered task c495831e-d3f1-444a-b2ab-b31c9cea4da5 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:11.936266
[2025-08-20 16:00:11.957] 14:00:11.954 [Python][DEBUG], '_run_once -> _run': Starting call trace monitoring for execution request (max 15 attempts) .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:11.948268
[2025-08-20 16:00:11.966] 14:00:11.963 [Python][USER], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session dacca1e6-859c-4d1a-a84d-2eb7c26e591b at 2025-08-20 14:00:11.957264
[2025-08-20 16:00:11.988] 14:00:11.986 [Python][INIT], '_run_once -> _run': New request started with question of length: 65. .  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session dacca1e6-859c-4d1a-a84d-2eb7c26e591b at 2025-08-20 14:00:11.980266
[2025-08-20 16:00:11.998] 14:00:11.995 [Python][TASK], '_run_once -> _run': Using provided schedule parameters .  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session dacca1e6-859c-4d1a-a84d-2eb7c26e591b at 2025-08-20 14:00:11.989268
[2025-08-20 16:00:12.006] 14:00:12.004 [Python][EVENT], '_run_once -> _run': Added request 9c35ee38-958c-47a9-962f-8fe7660df928 to user SYSTEM. Total requests: 2 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:11.998277
[2025-08-20 16:00:12.022] 14:00:12.019 [Python][REQUEST], '_run_once -> _run': Started thread for scheduled request 9c35ee38-958c-47a9-962f-8fe7660df928 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.014263
[2025-08-20 16:00:12.047] 14:00:12.044 [Python][REQUEST], '_run_once -> _run': Created scheduled request 9c35ee38-958c-47a9-962f-8fe7660df928 for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.038275
[2025-08-20 16:00:12.063] 14:00:12.061 [Python][REQUEST], '_run_once -> _run': Successfully recovered task 9c35ee38-958c-47a9-962f-8fe7660df928 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.055266
[2025-08-20 16:00:12.087] 14:00:12.084 [Python][REQUEST], '_run_once -> _run': Loaded 2 requests for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.078294
[2025-08-20 16:00:12.097] 14:00:12.094 [Python][INIT], '_run_once -> _run': User manager initialized with 2 active requests .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.087266
[2025-08-20 16:00:12.107] 14:00:12.105 [Python][INIT], '_run_once -> _run': Manager initialized for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.097266
[2025-08-20 16:00:12.118] 14:00:12.115 [Python][INIT], '_run_once -> _run': Created user manager for 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.108269
[2025-08-20 16:00:12.129] 14:00:12.125 [Python][REQUEST], '_run_once -> _run': User manager created for 00000000-0000-0000-0000-000000000001 to recover 2 tasks .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.118264
[2025-08-20 16:00:12.139] 14:00:12.137 [Python][REQUEST], '_run_once -> _run': Task recovery completed: 2 tasks available for 1 users .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.129267
[2025-08-20 16:00:12.148] 14:00:12.146 [Python][USER], '_run_once -> _run': Environment detection: Claude=False, Docker=False, Debug=True .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.140269
[2025-08-20 16:00:12.180] 14:00:12.178 [Python][REQUEST], '_run_once -> _run': Saved scheduled request c495831e-d3f1-444a-b2ab-b31c9cea4da5 for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.171262
[2025-08-20 16:00:12.188] 14:00:12.185 [Python][USER], '_run_once -> _run': Weekly log report task already exists, skipping creation .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.180265
[2025-08-20 16:00:12.197] 14:00:12.195 [Python][USER], '_run_once -> _run': Weekly log report task created for SYSTEM user .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.189267
[2025-08-20 16:00:12.205] 14:00:12.202 [Python][USER], '_run_once -> _run': Environment-specific scheduled tasks created .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.197264
[2025-08-20 16:00:12.213] 14:00:12.211 [Python][INIT], '_run_once -> _run': Setup has completed. .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.205266
[2025-08-20 16:00:12.221] 14:00:12.219 [Python][INIT], '_run_once -> _run': init() completed .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.213267
[2025-08-20 16:00:12.229] 14:00:12.227 [Python][INIT], '_run_once -> _run': ZairaUser created: Python .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:12.221269
[2025-08-20 16:00:12.237] 14:00:12.234 [Python][USER], '_run_once -> _run': User created with GUID cf64f0ed-990e-471f-ab93-aa716727975d .  Metadata: {"chat length#":0}. User cf64f0ed-990e-471f-ab93-aa716727975d on session 1a17dc67-d1c9-4cdf-bdf5-f070eb6f4ebc at 2025-08-20 14:00:12.229268
[2025-08-20 16:00:15.405] 14:00:15.403 [Python][DEBUG], '_run_once -> _run': Execution request status check 1/15: status=running, call_trace=0 entries .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:15.389299
[2025-08-20 16:00:25.500] 14:00:25.497 [Python][IMAP IDLE], '_run_once -> _run': Task called with state: user_guid=00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 932e5f1b-080f-489e-88ca-5ac32486b926 at 2025-08-20 14:00:25.492215
[2025-08-20 16:00:25.509] 14:00:25.507 [Python][IMAP IDLE], '_run_once -> _run': Found user: True .  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 932e5f1b-080f-489e-88ca-5ac32486b926 at 2025-08-20 14:00:25.500983
[2025-08-20 16:00:25.521] 14:00:25.516 [Python][IMAP IDLE], '_run_once -> _run': Starting task for user: 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 932e5f1b-080f-489e-88ca-5ac32486b926 at 2025-08-20 14:00:25.509550
[2025-08-20 16:00:25.534] 14:00:25.529 [Python][IMAP IDLE], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 932e5f1b-080f-489e-88ca-5ac32486b926 at 2025-08-20 14:00:25.522555
[2025-08-20 16:00:25.549] 14:00:25.545 [Python][IMAP CONFIG], '_run_once -> _run': Server: mail.askzaira.com:143, Email: <EMAIL>, SSL: False .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:25.535907
[2025-08-20 16:00:25.562] 14:00:25.560 [Python][IMAP CONNECTION], '_run_once -> _run': Successfully established IMAP connection .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:25.549582
[2025-08-20 16:00:30.673] WARNING:managers.manager_users:User not found by username: simonboot.
[2025-08-20 16:00:30.673] User not found by username: simonboot.
[2025-08-20 16:00:30.673] INFO:managers.manager_users:Generated new User GUID: 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3
[2025-08-20 16:00:30.673] Generated new User GUID: 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3
[2025-08-20 16:00:30.673] INFO:managers.manager_users:Generated new User GUID: 2f72322c-08ad-4eb8-a994-f6329bd80b9b
[2025-08-20 16:00:30.673] Generated new User GUID: 2f72322c-08ad-4eb8-a994-f6329bd80b9b
[2025-08-20 16:00:30.675] INFO:managers.manager_users:User added: simonboot. with GUID: 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3
[2025-08-20 16:00:30.675] User added: simonboot. with GUID: 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3
[2025-08-20 16:00:31.091] INFO:managers.manager_users:User found: GUID 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3
[2025-08-20 16:00:31.091] User found: GUID 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3
[2025-08-20 16:00:31.453] 14:00:31.451 [Python][EVENT], '_run_once -> _run': Discord message - Is Reply: False, From: simonboot., Content: ...een <NAME_EMAIL> om te vragen hoe het gaat' .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:31.445941
[2025-08-20 16:00:31.460] 14:00:31.458 [Python][INIT], '_run_once -> _run': ZairaUser created: simonboot. .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:31.453903
[2025-08-20 16:00:31.469] 14:00:31.466 [Python][USER], '_run_once -> _run': User created with GUID 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":1}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:31.460901
[2025-08-20 16:00:31.476] 14:00:31.473 [Python][EVENT], '_run_once -> _run': Discord received a message on guild ID: 1313130978343911505, guild name: BVS solutions .  Metadata: {"chat length#":1}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:31.469900
[2025-08-20 16:00:31.488] 14:00:31.486 [Python][EVENT], '_run_once -> _run': [public] simonboot.: "stuur een <NAME_EMAIL> om te vragen hoe het gaat" .  Metadata: {"chat length#":1}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:31.477904
[2025-08-20 16:00:31.495] 14:00:31.492 [Python][INIT], '_run_once -> _run': New request started with question of length: 66. .  Metadata: {"chat length#":1}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:31.488923
[2025-08-20 16:00:31.503] 14:00:31.502 [Python][EVENT], '_run_once -> _run': Added request d2618ffd-47a0-43d7-8976-83bf829560a5 to user simonboot.. Total requests: 1 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:31.495902
[2025-08-20 16:00:40.445] 14:00:40.437 [Python][TASK], '_run_once -> _run': tool_call .  Metadata: {"chat length#":1}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:40.417488
[2025-08-20 16:00:40.469] 14:00:40.467 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Starting email generation with content_request: Vraag aan Simon hoe het met hem gaat. .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:40.451522
[2025-08-20 16:00:40.479] 14:00:40.476 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] subject_hint: None, sender: None, recipient: None .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:40.469490
[2025-08-20 16:00:40.486] 14:00:40.484 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] state.user_guid: 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:40.479537
[2025-08-20 16:00:40.495] 14:00:40.492 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Found user: simonboot. .  Metadata: {"chat length#":1}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:40.486491
[2025-08-20 16:00:40.509] 14:00:40.501 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Parameters received - sender: 'None', rec...ne', content_request: 'Vraag aan Simon hoe het met hem gaat.' .  Metadata: {"chat length#":1}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:40.496491
[2025-08-20 16:00:40.557] 14:00:40.547 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] User email from profile: '' .  Metadata: {"chat length#":1}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:40.510493
[2025-08-20 16:00:40.588] 14:00:40.585 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Initial final_sender: None .  Metadata: {"chat length#":1}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:40.558500
[2025-08-20 16:00:40.600] 14:00:40.596 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] No valid sender, checking user.email:  .  Metadata: {"chat length#":1}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:40.588056
[2025-08-20 16:00:40.611] 14:00:40.607 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Requesting sender email from user via HITL .  Metadata: {"chat length#":1}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:40.600057
[2025-08-20 16:00:40.621] 14:00:40.618 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] HITL requested: Je e-mail adres is n... bij ons. Van welk e-mailadres moet de mail verzonden worden? .  Metadata: {"chat length#":1}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:40.612060
[2025-08-20 16:00:40.650] 14:00:40.642 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Request GUID: d2618ffd-47a0-43d7-8976-83bf829560a5 .  Metadata: {"chat length#":1}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:40.621058
[2025-08-20 16:00:40.697] 14:00:40.690 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Bot: Discord .  Metadata: {"chat length#":1}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:40.657054
[2025-08-20 16:00:40.704] 14:00:40.702 [Python][OUTPUT], '_run_once -> _run': Sent email notification to Discord for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":1}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:40.697057
[2025-08-20 16:00:41.499] 14:00:41.498 [Python][EVENT], '_run_once -> _run': Broadcast sent to zaira in BVS solutions .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:41.484066
[2025-08-20 16:00:41.507] 14:00:41.504 [Python][OUTPUT], '_run_once -> _run': Testing bot broadcast: Je e-mail adres is nog niet bekend bij ons. Van welk e-mailadres moet de mail verzonden worden?... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:41.500708
[2025-08-20 16:00:41.524] 14:00:41.518 [Python][OUTPUT], '_run_once -> _run': Testing bot broadcast: Je e-mail adres is nog niet bekend bij ons. Van welk e-mailadres moet de mail verzonden worden?... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:41.507740
[2025-08-20 16:00:41.534] 14:00:41.532 [Python][DEBUG], '_run_once -> _run': [TESTING BOT] Broadcast captured for testing: Je e-mail adres ...j ons. Van welk e-mailadres moet de mail verzonden worden?... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:41.525714
[2025-08-20 16:00:41.545] 14:00:41.542 [Python][OUTPUT], '_run_once -> _run': Sent email notification to Testing bot for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:41.534703
[2025-08-20 16:00:41.553] 14:00:41.550 [Python][TASK], '_run_once -> _run': Email notification broadcast completed for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:41.545706
[2025-08-20 16:00:41.562] 14:00:41.560 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Calling bot's request_human_in_the_loop_internal .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:41.553317
[2025-08-20 16:00:41.570] 14:00:41.567 [Python][DEBUG], '_run_once -> _run': MyBot extracted call_trace: ['top_level_supervisor: start', 't...p_level_supervisor: goto email_generator_task (chosen task)'] .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:41.562291
[2025-08-20 16:00:41.578] 14:00:41.576 [Python][DEBUG], '_run_once -> _run': MyBot final call_trace before message creation: ['top_level_su...p_level_supervisor: goto email_generator_task (chosen task)'] .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:41.570291
[2025-08-20 16:00:49.345] INFO:managers.manager_users:User found by username: simonboot.
[2025-08-20 16:00:49.346] User found by username: simonboot.
[2025-08-20 16:00:50.464] 14:00:50.457 [Python][EVENT], '_run_once -> _run': Discord message - Is Reply: False, From: simonboot., Content: '!<EMAIL>' .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:50.432136
[2025-08-20 16:00:50.473] 14:00:50.470 [Python][EVENT], '_run_once -> _run': Discord received a message on guild ID: 1313130978343911505, guild name: BVS solutions .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:50.464745
[2025-08-20 16:00:50.479] 14:00:50.477 [Python][EVENT], '_run_once -> _run': [public] simonboot.: "<EMAIL>" .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:50.473698
[2025-08-20 16:00:50.495] 14:00:50.493 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] HITL callback called with response: <EMAIL> .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:50.488698
[2025-08-20 16:00:50.503] 14:00:50.500 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Set user email to: <EMAIL> .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:50.495700
[2025-08-20 16:00:50.510] 14:00:50.508 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] HITL request completed, final_sender: <EMAIL> .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:50.503703
[2025-08-20 16:00:50.518] 14:00:50.515 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Initial final_recipient: None .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:50.510698
[2025-08-20 16:00:50.526] 14:00:50.524 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Requesting recipient email from user via HITL .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:50.519706
[2025-08-20 16:00:50.538] 14:00:50.531 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] HITL requested: Het is mij niet duidelijk naar welk email adres de mail gestuurd moet worden? .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:50.526732
[2025-08-20 16:00:50.549] 14:00:50.547 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Request GUID: d2618ffd-47a0-43d7-8976-83bf829560a5 .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:50.538729
[2025-08-20 16:00:50.557] 14:00:50.555 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Bot: Discord .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:50.549699
[2025-08-20 16:00:50.563] 14:00:50.561 [Python][OUTPUT], '_run_once -> _run': Sent email notification to Discord for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:50.557703
[2025-08-20 16:00:50.571] 14:00:50.569 [Python][OUTPUT], '_run_once -> _run': Testing bot broadcast: Het is mij niet duidelijk naar welk email adres de mail gestuurd moet worden?... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:50.563703
[2025-08-20 16:00:50.578] 14:00:50.575 [Python][OUTPUT], '_run_once -> _run': Testing bot broadcast: Het is mij niet duidelijk naar welk email adres de mail gestuurd moet worden?... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:50.571703
[2025-08-20 16:00:50.586] 14:00:50.583 [Python][DEBUG], '_run_once -> _run': [TESTING BOT] Broadcast captured for testing: Het is mij niet duidelijk naar welk email adres de mail gestuurd moet worden?... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:50.578700
[2025-08-20 16:00:50.593] 14:00:50.591 [Python][OUTPUT], '_run_once -> _run': Sent email notification to Testing bot for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:50.586701
[2025-08-20 16:00:50.601] 14:00:50.598 [Python][TASK], '_run_once -> _run': Email notification broadcast completed for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:50.594702
[2025-08-20 16:00:50.611] 14:00:50.608 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Calling bot's request_human_in_the_loop_internal .  Metadata: {"chat length#":2}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:50.602710
[2025-08-20 16:00:51.528] 14:00:51.526 [Python][EVENT], '_run_once -> _run': Broadcast sent to zaira in BVS solutions .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:00:51.521645
[2025-08-20 16:00:51.536] 14:00:51.534 [Python][DEBUG], '_run_once -> _run': MyBot extracted call_trace: ['top_level_supervisor: start', 't...p_level_supervisor: goto email_generator_task (chosen task)'] .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:51.528638
[2025-08-20 16:00:51.548] 14:00:51.545 [Python][DEBUG], '_run_once -> _run': MyBot final call_trace before message creation: ['top_level_su...p_level_supervisor: goto email_generator_task (chosen task)'] .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:00:51.536644
[2025-08-20 16:01:05.463] 14:01:05.460 [Python][EVENT], '_run_once -> _run': Discord message - Is Reply: False, From: simonboot., Content: '!<EMAIL>' .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:05.444518
[2025-08-20 16:01:05.470] 14:01:05.468 [Python][EVENT], '_run_once -> _run': Discord received a message on guild ID: 1313130978343911505, guild name: BVS solutions .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:05.463875
[2025-08-20 16:01:05.485] 14:01:05.481 [Python][EVENT], '_run_once -> _run': [public] simonboot.: "<EMAIL>" .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:05.471876
[2025-08-20 16:01:05.503] 14:01:05.501 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] HITL recipient callback called with response: <EMAIL> .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:05.493109
[2025-08-20 16:01:05.515] 14:01:05.512 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Set recipient email to: <EMAIL> .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:05.504061
[2025-08-20 16:01:05.523] 14:01:05.521 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] HITL request completed, final_recipient: <EMAIL> .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:05.515564
[2025-08-20 16:01:05.535] 14:01:05.530 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Getting signature data for user .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:05.523571
[2025-08-20 16:01:05.543] 14:01:05.540 [Python][INIT], '_run_once -> _run': SignatureManager initialized successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:05.535566
[2025-08-20 16:01:05.554] 14:01:05.551 [Python][DEBUG], '_run_once -> _run': [Scrubbed due to 'Auth'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:05.543572
[2025-08-20 16:01:05.563] 14:01:05.561 [Python][DEBUG], '_run_once -> _run': Using default signature for user simonboot. .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:05.554566
[2025-08-20 16:01:05.571] 14:01:05.569 [Python][DEBUG], '_run_once -> _run': Successfully encoded image: C:\Users\<USER>\Documents\AgenticRa...t files\KIxIv6vR0pWoxgxUkvrr03UWs0sm49uDqw.png (184578 bytes) .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:05.563598
[2025-08-20 16:01:05.593] 14:01:05.587 [Python][DEBUG], '_run_once -> _run': Generated HTML signature with image .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:05.571733
[2025-08-20 16:01:05.603] 14:01:05.601 [Python][DEBUG], '_run_once -> _run': Prepared signature for user simonboot.: with image .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:05.593826
[2025-08-20 16:01:05.613] 14:01:05.610 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] User has signature configured - preventing double closings .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:05.604826
[2025-08-20 16:01:05.621] 14:01:05.619 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Starting email content generation with LLM .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:05.614824
[2025-08-20 16:01:05.640] 14:01:05.628 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Final email addresses - sender: '<EMAIL>', recipient: '<EMAIL>' .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:05.621916
[2025-08-20 16:01:05.663] 14:01:05.654 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Using signature instruction: CRITICAL: En...e have a meeting tommorow? 
                
[2025-08-20 16:01:05.663]                  .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:05.641482
[2025-08-20 16:01:06.592] 14:01:06.589 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Signature data: text=yes, image=yes .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:06.564408
[2025-08-20 16:01:06.602] 14:01:06.600 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] About to request user approval for email preview .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:06.593942
[2025-08-20 16:01:06.609] 14:01:06.606 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Requesting HITL approval for email .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:06.602932
[2025-08-20 16:01:06.618] 14:01:06.615 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Email preview: From: <EMAIL>
[2025-08-20 16:01:06.618] To:...il.nl
[2025-08-20 16:01:06.618] Subject: Onderzoek naar Simon's welzijn

[2025-08-20 16:01:06.618] Hey Simon,
[2025-08-20 16:01:06.618] ... .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:06.609937
[2025-08-20 16:01:06.632] 14:01:06.622 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] HITL requested: Ik heb de volgende m...je dat ik deze goedkeur voor verzending? (j/n)

[2025-08-20 16:01:06.632] From: api@ask .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:06.618932
[2025-08-20 16:01:06.671] 14:01:06.662 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Request GUID: d2618ffd-47a0-43d7-8976-83bf829560a5 .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:06.637983
[2025-08-20 16:01:06.697] 14:01:06.691 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Bot: Discord .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:06.677023
[2025-08-20 16:01:06.704] 14:01:06.702 [Python][OUTPUT], '_run_once -> _run': Sent email notification to Discord for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":3}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:06.697989
[2025-08-20 16:01:08.534] 14:01:08.531 [Python][OUTPUT], '_run_once -> _run': Testing bot broadcast: Ik heb de volgende mail opgesteld. Wil je dat ik deze goedkeur voor verzending? (j/n)

[2025-08-20 16:01:08.534] From: api@ask... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:08.362690
[2025-08-20 16:01:08.543] 14:01:08.540 [Python][OUTPUT], '_run_once -> _run': Testing bot broadcast: Ik heb de volgende mail opgesteld. Wil ...: Onderzoek naar Simon's welzijn

[2025-08-20 16:01:08.543] Hey Simon,

[2025-08-20 16:01:08.543] Ik hoop dat ... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:08.534896
[2025-08-20 16:01:08.552] 14:01:08.549 [Python][DEBUG], '_run_once -> _run': [TESTING BOT] Broadcast captured for testing: Ik heb de volgen...dat ik deze goedkeur voor verzending? (j/n)

[2025-08-20 16:01:08.552] From: api@ask... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:08.543360
[2025-08-20 16:01:08.562] 14:01:08.559 [Python][OUTPUT], '_run_once -> _run': Sent email notification to Testing bot for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":4}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:08.552363
[2025-08-20 16:01:08.569] 14:01:08.567 [Python][TASK], '_run_once -> _run': Email notification broadcast completed for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":4}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:08.562363
[2025-08-20 16:01:08.577] 14:01:08.574 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Calling bot's request_human_in_the_loop_internal .  Metadata: {"chat length#":4}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:08.569361
[2025-08-20 16:01:08.584] 14:01:08.582 [Python][EVENT], '_run_once -> _run': Broadcast sent to zaira in BVS solutions .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:08.577378
[2025-08-20 16:01:08.594] 14:01:08.589 [Python][DEBUG], '_run_once -> _run': MyBot extracted call_trace: ['top_level_supervisor: start', 't...p_level_supervisor: goto email_generator_task (chosen task)'] .  Metadata: {"chat length#":4}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:08.584364
[2025-08-20 16:01:08.604] 14:01:08.601 [Python][DEBUG], '_run_once -> _run': MyBot final call_trace before message creation: ['top_level_su...p_level_supervisor: goto email_generator_task (chosen task)'] .  Metadata: {"chat length#":4}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:08.594397
[2025-08-20 16:01:20.459] 14:01:20.457 [Python][EVENT], '_run_once -> _run': Discord message - Is Reply: False, From: simonboot., Content: '!n' .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:20.443795
[2025-08-20 16:01:20.466] 14:01:20.464 [Python][EVENT], '_run_once -> _run': Discord received a message on guild ID: 1313130978343911505, guild name: BVS solutions .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:20.459550
[2025-08-20 16:01:20.474] 14:01:20.472 [Python][EVENT], '_run_once -> _run': [public] simonboot.: "n" .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:20.467598
[2025-08-20 16:01:20.488] 14:01:20.486 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Approval callback called with response: n .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:20.479578
[2025-08-20 16:01:20.495] 14:01:20.493 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] User declined approval, requesting edit choice .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:20.488551
[2025-08-20 16:01:20.503] 14:01:20.501 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] HITL requested: Wat wil je aanpassen...derwerp van de mail
[2025-08-20 16:01:20.503] 2. Inhoud van de mail
[2025-08-20 16:01:20.503] 3. Mailadres ontvan .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:20.495580
[2025-08-20 16:01:20.511] 14:01:20.508 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Request GUID: d2618ffd-47a0-43d7-8976-83bf829560a5 .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:20.504552
[2025-08-20 16:01:20.519] 14:01:20.516 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Bot: Discord .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:20.511549
[2025-08-20 16:01:20.526] 14:01:20.524 [Python][OUTPUT], '_run_once -> _run': Sent email notification to Discord for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:20.519554
[2025-08-20 16:01:20.533] 14:01:20.530 [Python][OUTPUT], '_run_once -> _run': Testing bot broadcast: Wat wil je aanpassen aan de mail?
[2025-08-20 16:01:20.533] 1. Onderwerp van de mail
[2025-08-20 16:01:20.533] 2. Inhoud van de mail
[2025-08-20 16:01:20.533] 3. Mailadres ontvan... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:20.526694
[2025-08-20 16:01:20.541] 14:01:20.539 [Python][OUTPUT], '_run_once -> _run': Testing bot broadcast: Wat wil je aanpassen aan de mail?
[2025-08-20 16:01:20.541] 1. On...ger
[2025-08-20 16:01:20.541] 4. Annuleren

[2025-08-20 16:01:20.541] Typ het nummer in wat u wilt veranderen:... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:20.534699
[2025-08-20 16:01:20.549] 14:01:20.546 [Python][DEBUG], '_run_once -> _run': [TESTING BOT] Broadcast captured for testing: Wat wil je aanpa...werp van de mail
[2025-08-20 16:01:20.549] 2. Inhoud van de mail
[2025-08-20 16:01:20.549] 3. Mailadres ontvan... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:20.542695
[2025-08-20 16:01:20.557] 14:01:20.555 [Python][OUTPUT], '_run_once -> _run': Sent email notification to Testing bot for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:20.550698
[2025-08-20 16:01:20.564] 14:01:20.562 [Python][TASK], '_run_once -> _run': Email notification broadcast completed for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:20.558695
[2025-08-20 16:01:20.572] 14:01:20.570 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Calling bot's request_human_in_the_loop_internal .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:20.564720
[2025-08-20 16:01:20.579] 14:01:20.577 [Python][EVENT], '_run_once -> _run': Broadcast sent to zaira in BVS solutions .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:20.572697
[2025-08-20 16:01:20.587] 14:01:20.584 [Python][DEBUG], '_run_once -> _run': MyBot extracted call_trace: ['top_level_supervisor: start', 't...p_level_supervisor: goto email_generator_task (chosen task)'] .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:20.579695
[2025-08-20 16:01:20.594] 14:01:20.592 [Python][DEBUG], '_run_once -> _run': MyBot final call_trace before message creation: ['top_level_su...p_level_supervisor: goto email_generator_task (chosen task)'] .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:20.587700
[2025-08-20 16:01:21.642] 14:01:21.640 [Python][EVENT], '_run_once -> _run': Discord message - Is Reply: False, From: simonboot., Content: '!2' .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:21.616646
[2025-08-20 16:01:21.650] 14:01:21.648 [Python][EVENT], '_run_once -> _run': Discord received a message on guild ID: 1313130978343911505, guild name: BVS solutions .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:21.643645
[2025-08-20 16:01:21.658] 14:01:21.656 [Python][EVENT], '_run_once -> _run': [public] simonboot.: "2" .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:21.650645
[2025-08-20 16:01:21.680] 14:01:21.671 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Edit choice callback called with response: 2 .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:21.665647
[2025-08-20 16:01:21.714] 14:01:21.706 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Requesting body content edit instructions .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:21.680643
[2025-08-20 16:01:21.740] 14:01:21.737 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] HITL requested: Huidige inhoud:
[2025-08-20 16:01:21.740] Hey ... het goed met je gaat. Ik wilde even vragen hoe het met je is .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:21.720643
[2025-08-20 16:01:21.748] 14:01:21.745 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Request GUID: d2618ffd-47a0-43d7-8976-83bf829560a5 .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:21.740680
[2025-08-20 16:01:21.758] 14:01:21.755 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Bot: Discord .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:21.749645
[2025-08-20 16:01:21.781] 14:01:21.773 [Python][OUTPUT], '_run_once -> _run': Sent email notification to Discord for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":5}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:21.758644
[2025-08-20 16:01:25.466] 14:01:25.463 [Python][EVENT], '_run_once -> _run': Broadcast sent to zaira in BVS solutions .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:25.447319
[2025-08-20 16:01:25.474] 14:01:25.471 [Python][OUTPUT], '_run_once -> _run': Testing bot broadcast: Huidige inhoud:
[2025-08-20 16:01:25.474] Hey Simon,

[2025-08-20 16:01:25.474] Ik hoop dat het goed met je gaat. Ik wilde even vragen hoe het met je is... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:25.466422
[2025-08-20 16:01:25.485] 14:01:25.481 [Python][OUTPUT], '_run_once -> _run': Testing bot broadcast: Huidige inhoud:
[2025-08-20 16:01:25.485] Hey Simon,

[2025-08-20 16:01:25.485] Ik hoop dat...f instructies voor de wijziging (of 'annuleren' om te stop... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:25.474421
[2025-08-20 16:01:25.492] 14:01:25.490 [Python][DEBUG], '_run_once -> _run': [TESTING BOT] Broadcast captured for testing: Huidige inhoud:
[2025-08-20 16:01:25.492] ...t goed met je gaat. Ik wilde even vragen hoe het met je is... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:25.485705
[2025-08-20 16:01:25.504] 14:01:25.501 [Python][OUTPUT], '_run_once -> _run': Sent email notification to Testing bot for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":6}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:25.493099
[2025-08-20 16:01:25.511] 14:01:25.508 [Python][TASK], '_run_once -> _run': Email notification broadcast completed for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":6}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:25.504870
[2025-08-20 16:01:25.521] 14:01:25.518 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Calling bot's request_human_in_the_loop_internal .  Metadata: {"chat length#":6}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:25.511462
[2025-08-20 16:01:25.528] 14:01:25.526 [Python][DEBUG], '_run_once -> _run': MyBot extracted call_trace: ['top_level_supervisor: start', 't...p_level_supervisor: goto email_generator_task (chosen task)'] .  Metadata: {"chat length#":6}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:25.521219
[2025-08-20 16:01:25.539] 14:01:25.536 [Python][DEBUG], '_run_once -> _run': MyBot final call_trace before message creation: ['top_level_su...p_level_supervisor: goto email_generator_task (chosen task)'] .  Metadata: {"chat length#":6}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:25.528933
[2025-08-20 16:01:35.465] 14:01:35.462 [Python][EVENT], '_run_once -> _run': Discord message - Is Reply: False, From: simonboot., Content: '!vraag hem ook om koffie te drinken' .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:35.447141
[2025-08-20 16:01:35.472] 14:01:35.470 [Python][EVENT], '_run_once -> _run': Discord received a message on guild ID: 1313130978343911505, guild name: BVS solutions .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.466054
[2025-08-20 16:01:35.483] 14:01:35.480 [Python][EVENT], '_run_once -> _run': [public] simonboot.: "vraag hem ook om koffie te drinken" .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.473053
[2025-08-20 16:01:35.498] 14:01:35.495 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Regenerating email content with user instructions: vraag hem ook om koffie te drinken .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.488733
[2025-08-20 16:01:35.505] 14:01:35.503 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Language instruction: BELANGRIJK: Schrijf...het Nederlands. Gebruik ALLEEN Nederlandse woorden en zinnen. .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.498517
[2025-08-20 16:01:35.517] 14:01:35.514 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Email approval request initiated, awaiting user response .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.506112
[2025-08-20 16:01:35.619] 14:01:35.618 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Regenerated email content with LLM .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.613063
[2025-08-20 16:01:35.628] 14:01:35.625 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Re-requesting approval with updated email .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.619619
[2025-08-20 16:01:35.637] 14:01:35.634 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] HITL requested: Aangepaste mail. Wil...dkeur voor verzending? (j/n)

[2025-08-20 16:01:35.637] From: <EMAIL>
[2025-08-20 16:01:35.637] To: simo .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.628879
[2025-08-20 16:01:35.648] 14:01:35.646 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Request GUID: d2618ffd-47a0-43d7-8976-83bf829560a5 .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.637872
[2025-08-20 16:01:35.657] 14:01:35.653 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Bot: Discord .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.648873
[2025-08-20 16:01:35.670] 14:01:35.665 [Python][OUTPUT], '_run_once -> _run': Sent email notification to Discord for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.657875
[2025-08-20 16:01:35.690] 14:01:35.688 [Python][OUTPUT], '_run_once -> _run': Testing bot broadcast: Aangepaste mail. Wil je dat ik deze goedkeur voor verzending? (j/n)

[2025-08-20 16:01:35.690] From: <EMAIL>
[2025-08-20 16:01:35.690] To: simo... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:35.670873
[2025-08-20 16:01:35.702] 14:01:35.699 [Python][OUTPUT], '_run_once -> _run': Testing bot broadcast: Aangepaste mail. Wil je dat ik deze goe...imon's welzijn

[2025-08-20 16:01:35.702] Hey Simon,

[2025-08-20 16:01:35.702] Ik hoop dat het goed met je ga... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:35.691874
[2025-08-20 16:01:35.709] 14:01:35.707 [Python][DEBUG], '_run_once -> _run': [TESTING BOT] Broadcast captured for testing: Aangepaste mail....ur voor verzending? (j/n)

[2025-08-20 16:01:35.709] From: <EMAIL>
[2025-08-20 16:01:35.709] To: simo... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:35.702870
[2025-08-20 16:01:35.715] 14:01:35.714 [Python][OUTPUT], '_run_once -> _run': Sent email notification to Testing bot for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.710877
[2025-08-20 16:01:35.721] 14:01:35.720 [Python][TASK], '_run_once -> _run': Email notification broadcast completed for user 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.716871
[2025-08-20 16:01:35.729] 14:01:35.726 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Calling bot's request_human_in_the_loop_internal .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.721869
[2025-08-20 16:01:35.735] 14:01:35.733 [Python][DEBUG], '_run_once -> _run': MyBot extracted call_trace: ['top_level_supervisor: start', 't...always_call_LAST)', 'output_processing_supervisor: goto END'] .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.729872
[2025-08-20 16:01:35.743] 14:01:35.741 [Python][DEBUG], '_run_once -> _run': MyBot final call_trace before message creation: ['top_level_su...always_call_LAST)', 'output_processing_supervisor: goto END'] .  Metadata: {"chat length#":7}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:35.735871
[2025-08-20 16:01:35.750] 14:01:35.748 [Python][EVENT], '_run_once -> _run': Broadcast sent to zaira in BVS solutions .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:35.744873
[2025-08-20 16:01:39.234] Backend TkAgg is interactive backend. Turning interactive mode on.
[2025-08-20 16:01:58.848] INFO:slack_sdk.socket_mode.aiohttp:The session (s_127410820557) seems to be stale. Reconnecting... reason: disconnected for 23+ seconds)
[2025-08-20 16:01:58.848] The session (s_127410820557) seems to be stale. Reconnecting... reason: disconnected for 23+ seconds)
[2025-08-20 16:01:58.856] 14:01:58.855 [Python][DEBUG], '_run_once -> _run': MyBot extracted call_trace: ['top_level_supervisor: start', 't...always_call_LAST)', 'output_processing_supervisor: goto END'] .  Metadata: {"chat length#":8}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:58.801760
[2025-08-20 16:01:58.866] 14:01:58.863 [Python][DEBUG], '_run_once -> _run': MyBot final call_trace before message creation: ['top_level_su...always_call_LAST)', 'output_processing_supervisor: goto END'] .  Metadata: {"chat length#":8}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:58.856996
[2025-08-20 16:01:58.871] 14:01:58.869 [Python][OUTPUT], '_run_once -> _run': Discord: .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:58.866030
[2025-08-20 16:01:58.887] 14:01:58.884 [Python][OUTPUT], 'top_output': Task finished with question of length: 241.
[2025-08-20 16:01:58.887] Call trace: 
[2025-08-20 16:01:58.887] top_l...rvisor: goto END
[2025-08-20 16:01:58.887] continue_question: add
[2025-08-20 16:01:58.887] discord_out: llm_call .  Metadata: {"chat length#":8}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:58.871996
[2025-08-20 16:01:58.982] 14:01:58.979 [Python][DEBUG], '_run_once -> _run': MyBot extracted call_trace: ['top_level_supervisor: start', 't...', 'discord_out: llm_call', 'top_level_supervisor: goto END'] .  Metadata: {"chat length#":8}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:58.945009
[2025-08-20 16:01:58.991] 14:01:58.988 [Python][DEBUG], '_run_once -> _run': MyBot final call_trace before message creation: ['top_level_su...', 'discord_out: llm_call', 'top_level_supervisor: goto END'] .  Metadata: {"chat length#":8}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:58.983001
[2025-08-20 16:01:59.135] INFO:slack_sdk.socket_mode.aiohttp:The old session (s_127410820557) has been abandoned
[2025-08-20 16:01:59.135] The old session (s_127410820557) has been abandoned
[2025-08-20 16:01:59.141] 14:01:59.139 [Python][ERROR], '_run_once -> _run': Caught exception: 'pydantic_core._pydantic_core.ValidationError' object has no attribute 'stderr' .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:59.132057
[2025-08-20 16:01:59.150] 14:01:59.148 [Python][EVENT], '_run_once -> _run': Discord message - Is Reply: False, From: simonboot., Content: '!j' .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:01:59.141000
[2025-08-20 16:01:59.156] 14:01:59.155 [Python][EVENT], '_run_once -> _run': Discord received a message on guild ID: 1313130978343911505, guild name: BVS solutions .  Metadata: {"chat length#":8}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:59.150000
[2025-08-20 16:01:59.165] 14:01:59.162 [Python][EVENT], '_run_once -> _run': [public] simonboot.: "j" .  Metadata: {"chat length#":8}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:59.156999
[2025-08-20 16:01:59.181] 14:01:59.179 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Recursive approval callback with response: j .  Metadata: {"chat length#":8}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:59.171998
[2025-08-20 16:01:59.190] 14:01:59.187 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Added 'email' to output_demands via processing_data .  Metadata: {"chat length#":8}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:59.182005
[2025-08-20 16:01:59.201] 14:01:59.198 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Added EmailProcessingData to processing_data. Current demands: ['Discord', 'email'] .  Metadata: {"chat length#":8}. User 65947e8b-fcbc-4a9e-a53b-c1aadf0fffe3 on session fe0d9a93-4d46-4615-859f-9cf43f7796d5 at 2025-08-20 14:01:59.191004
[2025-08-20 16:01:59.459] INFO:slack_sdk.socket_mode.aiohttp:A new session (s_127411393937) has been established
[2025-08-20 16:01:59.462] A new session (s_127411393937) has been established
[2025-08-20 16:02:59.194] 14:02:59.164 [Python][METRICS], '_run_once -> _run': Factory: 1 managers, 2 active tasks .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-20 14:02:59.124714
