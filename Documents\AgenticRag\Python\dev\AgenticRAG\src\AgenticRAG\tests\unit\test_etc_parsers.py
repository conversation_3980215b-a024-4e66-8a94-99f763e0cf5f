from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import patch, MagicMock

# Import the module we're testing
from etc.parsers import multimodal_reader, basic_reader, file_extractor

class TestParsers:
    """Test suite for parsers.py"""

    def test_multimodal_reader_exists(self):
        """Test multimodal_reader is created and accessible"""
        assert multimodal_reader is not None
        
    def test_multimodal_reader_type(self):
        """Test multimodal_reader is UnstructuredReader instance"""
        from llama_index.readers.file.unstructured import UnstructuredReader
        assert isinstance(multimodal_reader, UnstructuredReader)

    def test_basic_reader_exists(self):
        """Test basic_reader is created and accessible"""
        assert basic_reader is not None
        
    def test_basic_reader_type(self):
        """Test basic_reader is UnstructuredReader instance"""
        from llama_index.readers.file.unstructured import UnstructuredReader
        assert isinstance(basic_reader, UnstructuredReader)

    def test_multimodal_reader_vs_basic_reader(self):
        """Test multimodal_reader and basic_reader are different instances"""
        # They should be different instances even if same type
        assert multimodal_reader is not basic_reader

    def test_file_extractor_exists(self):
        """Test file_extractor dictionary exists"""
        assert file_extractor is not None
        assert isinstance(file_extractor, dict)

    def test_file_extractor_keys(self):
        """Test file_extractor has expected file extension keys"""
        expected_extensions = ['.pdf', '.docx', '.pptx', '.doc', '.txt']
        
        for ext in expected_extensions:
            assert ext in file_extractor, f"Extension {ext} not found in file_extractor"

    def test_file_extractor_values(self):
        """Test file_extractor has correct reader assignments"""
        from llama_index.readers.file.unstructured import UnstructuredReader
        
        # Check multimodal extensions use multimodal_reader
        multimodal_extensions = ['.pdf', '.docx', '.pptx']
        for ext in multimodal_extensions:
            assert file_extractor[ext] is multimodal_reader, f"Extension {ext} should use multimodal_reader"
        
        # Check basic extensions use basic_reader
        basic_extensions = ['.doc', '.txt']
        for ext in basic_extensions:
            assert file_extractor[ext] is basic_reader, f"Extension {ext} should use basic_reader"

    def test_file_extractor_pdf_mapping(self):
        """Test PDF files are mapped to multimodal_reader"""
        assert file_extractor['.pdf'] is multimodal_reader

    def test_file_extractor_docx_mapping(self):
        """Test DOCX files are mapped to multimodal_reader"""
        assert file_extractor['.docx'] is multimodal_reader

    def test_file_extractor_pptx_mapping(self):
        """Test PPTX files are mapped to multimodal_reader"""
        assert file_extractor['.pptx'] is multimodal_reader

    def test_file_extractor_doc_mapping(self):
        """Test DOC files are mapped to basic_reader"""
        assert file_extractor['.doc'] is basic_reader

    def test_file_extractor_txt_mapping(self):
        """Test TXT files are mapped to basic_reader"""
        assert file_extractor['.txt'] is basic_reader

    def test_file_extractor_completeness(self):
        """Test file_extractor has exactly the expected number of entries"""
        expected_count = 5  # .pdf, .docx, .pptx, .doc, .txt
        assert len(file_extractor) == expected_count

    def test_file_extractor_no_unexpected_extensions(self):
        """Test file_extractor doesn't contain unexpected extensions"""
        expected_extensions = {'.pdf', '.docx', '.pptx', '.doc', '.txt'}
        actual_extensions = set(file_extractor.keys())
        
        assert actual_extensions == expected_extensions

    def test_readers_are_unstructured_reader_instances(self):
        """Test all readers in file_extractor are UnstructuredReader instances"""
        from llama_index.readers.file.unstructured import UnstructuredReader
        
        for ext, reader in file_extractor.items():
            assert isinstance(reader, UnstructuredReader), f"Reader for {ext} is not UnstructuredReader"

    def test_multimodal_reader_functionality(self):
        """Test multimodal_reader has expected methods"""
        # Check that it has the expected methods from UnstructuredReader
        assert hasattr(multimodal_reader, 'load_data')
        assert callable(multimodal_reader.load_data)

    def test_basic_reader_functionality(self):
        """Test basic_reader has expected methods"""
        # Check that it has the expected methods from UnstructuredReader
        assert hasattr(basic_reader, 'load_data')
        assert callable(basic_reader.load_data)

    def test_reader_method_signatures(self):
        """Test readers have expected method signatures"""
        import inspect
        
        # Check load_data method exists and is callable
        for reader in [multimodal_reader, basic_reader]:
            load_data_method = getattr(reader, 'load_data', None)
            assert load_data_method is not None
            assert callable(load_data_method)

    def test_file_extractor_case_sensitivity(self):
        """Test file_extractor uses lowercase extensions"""
        for ext in file_extractor.keys():
            assert ext.islower(), f"Extension {ext} should be lowercase"

    def test_file_extractor_extension_format(self):
        """Test file_extractor extensions start with dot"""
        for ext in file_extractor.keys():
            assert ext.startswith('.'), f"Extension {ext} should start with dot"

    def test_commented_code_not_active(self):
        """Test that commented code is not active"""
        # Ensure commented parsers are not in file_extractor
        assert 'doc_parser' not in str(file_extractor)
        assert 'pdf_parser' not in str(file_extractor)
        
        # Ensure DEFAULT_FILE_EXTRACTOR is not used
        assert 'DEFAULT_FILE_EXTRACTOR' not in str(file_extractor)

    def test_imports_successful(self):
        """Test that all required imports are successful"""
        # If we can import the module without errors, imports are successful
        from etc.parsers import multimodal_reader, basic_reader, file_extractor
        
        assert multimodal_reader is not None
        assert basic_reader is not None
        assert file_extractor is not None

    def test_unstructured_reader_import(self):
        """Test UnstructuredReader can be imported"""
        from llama_index.readers.file.unstructured import UnstructuredReader
        assert UnstructuredReader is not None

    def test_file_extractor_immutability(self):
        """Test file_extractor can be modified (not frozen)"""
        # Test that we can modify the dict (it's not frozen)
        original_length = len(file_extractor)
        
        # Add a temporary entry
        file_extractor['.test'] = basic_reader
        assert len(file_extractor) == original_length + 1
        
        # Remove the temporary entry
        del file_extractor['.test']
        assert len(file_extractor) == original_length

    def test_file_extractor_values_consistency(self):
        """Test file_extractor values are consistent"""
        # All values should be either multimodal_reader or basic_reader
        valid_readers = {multimodal_reader, basic_reader}
        
        for ext, reader in file_extractor.items():
            assert reader in valid_readers, f"Reader for {ext} is not a valid reader"

    def test_extension_coverage_for_document_types(self):
        """Test file_extractor covers common document types"""
        # Test coverage for major document formats
        document_types = {
            '.pdf': 'PDF documents',
            '.docx': 'Word documents (new format)',
            '.doc': 'Word documents (old format)',
            '.txt': 'Plain text files',
            '.pptx': 'PowerPoint presentations'
        }
        
        for ext, description in document_types.items():
            assert ext in file_extractor, f"Missing support for {description} ({ext})"

    def test_multimodal_vs_basic_assignment_logic(self):
        """Test the logic behind multimodal vs basic reader assignment"""
        # Multimodal formats (newer, more complex)
        multimodal_formats = ['.pdf', '.docx', '.pptx']
        for ext in multimodal_formats:
            assert file_extractor[ext] is multimodal_reader, f"{ext} should use multimodal reader"
        
        # Basic formats (older, simpler)
        basic_formats = ['.doc', '.txt']
        for ext in basic_formats:
            assert file_extractor[ext] is basic_reader, f"{ext} should use basic reader"

    def test_readers_not_none(self):
        """Test that no reader in file_extractor is None"""
        for ext, reader in file_extractor.items():
            assert reader is not None, f"Reader for {ext} is None"

    def test_module_level_variables(self):
        """Test module-level variables are properly defined"""
        import etc.parsers as parsers_module
        
        # Check that the main variables are defined at module level
        assert hasattr(parsers_module, 'multimodal_reader')
        assert hasattr(parsers_module, 'basic_reader')
        assert hasattr(parsers_module, 'file_extractor')

    def test_file_extractor_types_consistency(self):
        """Test file_extractor maintains type consistency"""
        # All keys should be strings
        for key in file_extractor.keys():
            assert isinstance(key, str), f"Key {key} is not a string"
        
        # All values should be UnstructuredReader instances
        from llama_index.readers.file.unstructured import UnstructuredReader
        for value in file_extractor.values():
            assert isinstance(value, UnstructuredReader), f"Value {value} is not UnstructuredReader"