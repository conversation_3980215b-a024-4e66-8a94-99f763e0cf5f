from imports import *

from langchain_core.tools import BaseTool
import logging
import smtplib
import base64
from typing import Optional
import json
from datetime import datetime, timezone
from pathlib import Path

from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTaskState, SupervisorTask_Base
from endpoints.oauth._verifier_ import OAuth2Verifier
from managers.manager_users import ZairaUserManager
from managers.manager_signature import SignatureManager
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
from tasks.data import EmailProcessingData


class EmailSenderTool(BaseTool):
    """Tool for sending emails using various SMTP providers"""
    name: str = "email_sender_tool"
    description: str = "Sends emails using SMTP with OAuth2 authentication for Gmail and other providers. " \
                      "Use this tool to send emails when email data is provided. " \
                      "Requires subject, content, sender, and recipient parameters. " \
                      "Handles OAuth authentication and SMTP configuration automatically."
    
    def _run(self, subject: str, content: str, sender: str = None, recipient: str = None, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, subject: str, content: str, sender: str = None, recipient: str = None, state: SupervisorTaskState = None) -> str:
        """Sends an email using the provided email data"""
        try:
            user = await ZairaUserManager.get_instance().find_user(state.user_guid)
            
            # Get email data from processing_data
            email_data = None
            if hasattr(user, 'my_requests') and state.scheduled_guid in user.my_requests:
                processing_data = user.my_requests[state.scheduled_guid].processing_data
                if processing_data and isinstance(processing_data, EmailProcessingData):
                    email_data = processing_data
            
            # If no structured data available, try to get from parameters
            if not email_data:
                if not subject or not content or not sender or not recipient:
                    return "Missing email data - no structured processing data found and parameters incomplete"
                # Create email data from parameters for backward compatibility
                email_data = EmailProcessingData(
                    subject=subject,
                    content=content,
                    sender=sender,
                    recipient=recipient
                )
            
            # Check if email was approved
            if not email_data.user_approved:
                return "Email sending cancelled - not approved by user."
            
            # Create EmailClient for sending operations
            class EmailClient:
                """Handles mail sending operations"""
                def __init__(self, sender_email: str = None, recipient_email: str = None):
                    self.sender: str = sender_email or "<EMAIL>"  # Default sender
                    self.recipient: str = recipient_email or "<EMAIL>"  # Default recipient 
                    self.smtp_server: str = ""
                    self.smtp_port: int = 0
                    self.bearer_token: str = ""
                    self.refresh_token: str = ""

                def _smart_smtp_login(self, session, email: str, password: str) -> bool:
                    """
                    Smart SMTP login that handles different username formats
                    Some providers don't allow extension in username
                    """
                    try:
                        session.login(email, password)
                    except Exception:
                        # Some providers don't allow extension in username
                        username = email.rsplit(".", 1)[0]
                        session.login(username, password)
                    return True

                async def send_email(self, subject: str, content: str, email_data: EmailProcessingData = None) -> bool:
                    """Send an email to the specified recipient"""
                    try:
                        if not self.smtp_server or self.smtp_port == 0:
                            LogFire.log("ERROR", f"{self.sender}'s email provider is nog niet geïmplementeerd. "
                                              f"Contacteer <EMAIL> met het verzoek om deze toe te voegen.", severity="warning")
                            return False
                        
                        if self.smtp_server == "smtp.gmail.com":
                            return await self._send_via_gmail(subject, content)
                        else:
                            return await self._send_via_smtp(subject, content, email_data)
                            
                    except Exception as e:
                        LogFire.log("ERROR", f"Failed to send email: {e}", severity="error")
                        from etc.helper_functions import exception_triggered
                        exception_triggered(e, "EmailClient.send_email", user.user_guid if user else None)
                        return False

                async def _send_via_gmail(self, subject: str, content: str) -> bool:
                    """Send email via Gmail API"""
                    if not self.bearer_token:
                        logging.error("Failed to obtain a valid Gmail token. Email not sent.")
                        return False
                    
                    from google.oauth2.credentials import Credentials
                    from googleapiclient.discovery import build
                    from email.message import EmailMessage

                    # Call the Gmail API
                    token_info = {
                        "client_id": OAuth2Verifier.get_instance().oauth_client_keys["google"][0],
                        "client_secret": OAuth2Verifier.get_instance().oauth_client_keys["google"][1],
                        "refresh_token": await OAuth2Verifier.get_instance().get_token(identifier="google", token_type="refresh_token"),
                        "token_uri": OAuth2Verifier.get_instance().oauth_auth_token_urls["google"][1],
                        "access_token": await OAuth2Verifier.get_instance().get_token(identifier="google"),
                        "expires_in": await OAuth2Verifier.get_instance().get_token(identifier="google", token_type="expires_in"),
                        "scopes": OAuth2Verifier.get_instance().apps["google"].oauth_scopes,
                    }
                    
                    service = build("gmail", "v1", credentials=Credentials.from_authorized_user_info(token_info))
                    
                    # Create message
                    message = EmailMessage()
                    message.set_content(content)
                    message['To'] = self.recipient
                    message['From'] = self.sender
                    message['Subject'] = subject

                    raw_message = base64.urlsafe_b64encode(message.as_bytes()).decode()
                    gmail_message = {'raw': raw_message}
                    
                    sent_message = service.users().messages().send(userId="me", body=gmail_message).execute()
                    LogFire.log("EVENT", f"Gmail message sent. ID: {sent_message['id']}")
                    
                    LogFire.log("EVENT", "Mail is verzonden via Gmail.")
                    return True

                async def _send_via_smtp(self, subject: str, content: str, email_data: EmailProcessingData = None) -> bool:
                    """Send email via SMTP with HTML support and embedded images"""
                    # Determine connection type based on port
                    if self.smtp_port == 465:
                        session = smtplib.SMTP_SSL(self.smtp_server, self.smtp_port)
                    else:
                        session = smtplib.SMTP(self.smtp_server, self.smtp_port)
                        # Check if STARTTLS is supported
                        ehlo_response = session.ehlo()
                        if "starttls" in ehlo_response[1].decode().lower():
                            session.starttls()
                    
                    # Authenticate using smart username fallback
                    self._smart_smtp_login(session, self.sender, self.refresh_token)
                    
                    # Prepare and send the email
                    from email.mime.text import MIMEText
                    from email.mime.multipart import MIMEMultipart
                    from email.mime.image import MIMEImage
                    
                    # Create message with HTML support if signature is present
                    if email_data and email_data.include_signature and email_data.signature_html:
                        msg = MIMEMultipart('related')
                        msg['From'] = self.sender
                        msg['To'] = self.recipient
                        msg['Subject'] = subject
                        
                        # Create alternative container for HTML and plain text
                        msg_alternative = MIMEMultipart('alternative')
                        msg.attach(msg_alternative)
                        
                        # Plain text version (content + plain signature)
                        plain_content = content
                        if email_data.signature_text:
                            plain_content += f"\n\n{email_data.signature_text}"
                        
                        msg_text = MIMEText(plain_content, 'plain', 'utf-8')
                        msg_alternative.attach(msg_text)
                        
                        # HTML version (content + HTML signature)
                        html_content = f"""
                        <html>
                        <body style="font-family: Calibri, sans-serif; font-size: 16px;">
                            <div>
                                {content.replace(chr(10), '<br/>')}
                            </div>
                            <br/>
                            {email_data.signature_html}
                        </body>
                        </html>
                        """
                        
                        msg_html = MIMEText(html_content, 'html', 'utf-8')
                        msg_alternative.attach(msg_html)
                        
                        # Attach signature image if present
                        if email_data.signature_image_path:
                            try:
                                signature_manager = SignatureManager.get_instance()
                                image_data = signature_manager.image_to_base64(email_data.signature_image_path)
                                if image_data:
                                    base64_data, mime_type = image_data
                                    img_data = base64.b64decode(base64_data)
                                    
                                    image_filename = Path(email_data.signature_image_path).name
                                    msg_image = MIMEImage(img_data)
                                    msg_image.add_header('Content-ID', f'<{image_filename}>')
                                    msg_image.add_header('Content-Disposition', f'inline; filename="{image_filename}"')
                                    msg.attach(msg_image)
                                    
                                    LogFire.log("DEBUG", f"Attached signature image: {image_filename}")
                            except Exception as e:
                                LogFire.log("ERROR", f"Failed to attach signature image: {e}", severity="warning")
                    else:
                        # Simple plain text email (no signature or plain signature)
                        msg = MIMEMultipart()
                        msg['From'] = self.sender
                        msg['To'] = self.recipient
                        msg['Subject'] = subject
                        
                        plain_content = content
                        if email_data and email_data.include_signature and email_data.signature_text:
                            plain_content += f"\n\n{email_data.signature_text}"
                        
                        msg.attach(MIMEText(plain_content, 'plain', 'utf-8'))
                    
                    session.sendmail(self.sender, self.recipient, msg.as_string())
                    session.quit()
                    
                    LogFire.log("EVENT", "Mail is verzonden via SMTP.")
                    return True

            # Initialize email client with structured data
            email_client = EmailClient(sender_email=email_data.sender, recipient_email=email_data.recipient)
            
            # Configure SMTP settings based on sender domain
            smtp_token = await OAuth2Verifier.get_full_token("smtp")
            gmail_token = await OAuth2Verifier.get_full_token("gmail")
            if smtp_token is not None:
                # Use generic SMTP configuration from OAuth
                email_client.smtp_server = smtp_token["access_token"]  # Server Naam
                email_client.smtp_port = int(smtp_token["expires_in"])  # Netwerk port
                email_client.bearer_token = smtp_token["refresh_token"]  # E-mail adres (username)
                email_client.sender = email_client.bearer_token
                email_client.refresh_token = smtp_token["token_type"]  # E-mail wachtwoord
            elif gmail_token is not None:
                email_client.smtp_server = "smtp.gmail.com"
                # Needs improved
                email_client.sender = email_client.smtp_server
                email_client.smtp_port = 587
                email_client.bearer_token = gmail_token["access_token"]
                email_client.refresh_token = gmail_token["refresh_token"]
            
            # Send the email using structured data
            success = await email_client.send_email(email_data.subject, email_data.content, email_data)
            
            if success:
                # Mark email data as sent
                email_data.mark_sent()
                
                # Update the processing data in the request
                if hasattr(user, 'my_requests') and state.scheduled_guid in user.my_requests:
                    user.my_requests[state.scheduled_guid].processing_data = email_data
                
                return f"Email successfully sent from {email_data.sender} to {email_data.recipient} with subject '{email_data.subject}'"
            else:
                return "Failed to send email. Please check configuration and try again."
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "email_sender_tool", state.user_guid if state else None)
            return f"Error sending email: {e}"


class EmailSenderDirectTool(BaseTool):
    """Tool for sending emails directly with provided parameters (bypass generation)"""
    name: str = "email_sender_direct_tool"
    description: str = "Sends emails directly with provided subject, content, sender, and recipient"
    
    def _run(self, subject: str, content: str, sender: str, recipient: str, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, subject: str, content: str, sender: str, recipient: str, state: SupervisorTaskState = None) -> str:
        """Sends an email directly with provided parameters"""
        # Create email data structure
        email_data = {
            "subject": subject,
            "content": content,
            "sender": sender,
            "recipient": recipient,
            "generated_at": datetime.now(timezone.utc).isoformat(),
            "user_guid": state.user_guid if state else "unknown"
        }
        
        # Use the main email sender tool
        email_sender = EmailSenderTool()
        return await email_sender._arun(subject, content, sender, recipient, state)


# Create tool instances
email_sender_tool = EmailSenderTool()
email_sender_direct_tool = EmailSenderDirectTool()


async def create_supervisor_email_sender() -> SupervisorSupervisor:
    """Create supervisor for email sending tasks"""
    class TaskCreator:
        email_sending_task: SupervisorTask_SingleAgent = None
        email_direct_sending_task: SupervisorTask_SingleAgent = None

        async def create_tasks(self):
            self.email_sending_task = SupervisorManager.get_instance().register_task(
                SupervisorTask_SingleAgent(
                    name="email_out", #email_sender_task
                    tools=[email_sender_tool], 
                    prompt="You are an email sending specialist. Your task is to send emails that have been generated and approved. "
                           "When you receive email data with subject, content, sender, and recipient information, use the email_sender_tool to send the email. "
                           "Parse the provided email data and extract the necessary fields (subject, content, sender, recipient) to call the tool. "
                           "Handle both JSON formatted email data and structured text formats. "
                           "Always use the email_sender_tool when email data is provided - do not respond with explanations, just send the email."
                )
            )
            
            self.email_direct_sending_task = SupervisorManager.get_instance().register_task(
                SupervisorTask_SingleAgent(
                    name="email_direct_sender_task", 
                    tools=[email_sender_direct_tool], 
                    prompt="Do not use this task"
                )
            )
        
        async def create_supervisor(self) -> SupervisorSupervisor:
            return SupervisorManager.get_instance().register_supervisor(
                SupervisorSupervisor(
                    name="email_out", 
                    prompt="Activate email_sender_task"
                )
            ).add_task(self.email_sending_task) \
             .add_task(self.email_direct_sending_task) \
             .compile()

    creator = TaskCreator()
    await creator.create_tasks()
    await creator.create_supervisor()
    return creator.email_sending_task

async def create_out_task_email() -> SupervisorTask_Base:
    return SupervisorManager.register_task(await create_supervisor_email_sender())