from imports import *

import asyncio
from datetime import datetime, timedelta, timezone
from typing import Optional, Dict, Any
from uuid import uuid4
from pydantic import Field
from enum import Enum
from threading import Lock
from time import time as time_time

from userprofiles.LongRunningZairaRequest import Long<PERSON>unningZairaRequest
from userprofiles.ZairaUser import <PERSON><PERSON><PERSON><PERSON><PERSON>
from endpoints.mybot_generic import MyBot_Generic

class ScheduleType(Enum):
    ONCE = "once"
    RECURRING = "recurring"


class ScheduledZairaRequest(LongRunningZairaRequest):
    schedule_prompt: str = Field(default="", description="The original scheduling prompt")
    target_prompt: str = Field(default="", description="The prompt to execute after delay")
    delay_seconds: float = Field(default=0.0, description="Delay in seconds before execution")
    start_delay_seconds: float = Field(default=0.0, description="Initial delay in seconds before first execution")
    schedule_type: ScheduleType = Field(default=ScheduleType.ONCE, description="Whether to run once or repeatedly")
    next_execution: Optional[datetime] = Field(None, description="Next scheduled execution time")
    is_active: bool = Field(default=True, description="Whether this scheduled request is active")
    creation_time: Optional[datetime] = Field(default=None, description="Time when request was created for proper timing calculations")
    run_on_startup: bool = Field(default=False, description="Whether to execute this request immediately when server restarts")
    
    # Additional fields specific to scheduled requests
    execution_started_at: Optional[float] = None
    last_execution_result: str = ""
    call_trace_captured_at: Optional[float] = None
    last_check: Optional[float] = None
    cancelled_at: Optional[float] = None
    deactivated_at: Optional[float] = None
    
    def __init__(self, user: ZairaUser, calling_bot: MyBot_Generic, original_message, schedule_prompt: str, 
                 target_prompt: str, start_delay_seconds: float, 
                 delay_seconds: float, schedule_type: ScheduleType, run_on_startup: bool = False):
        """
        Initialize a ScheduledZairaRequest.
        
        Args:
            user: The user creating the request
            calling_bot: The bot instance
            original_message: The original message
            schedule_prompt: The original scheduling prompt
            target_prompt: The parsed target prompt to execute
            start_delay_seconds: Initial delay before first execution
            delay_seconds: Recurring delay between executions
            schedule_type: Type of schedule (once or recurring)
            run_on_startup: Whether to execute this request immediately when server restarts
        """
        
        # Use provided parsed parameters directly
        final_target_prompt = target_prompt
        final_delay_seconds = float(delay_seconds)
        final_start_delay_seconds = float(start_delay_seconds)
        final_schedule_type = schedule_type if isinstance(schedule_type, ScheduleType) else ScheduleType(schedule_type)
            
        # Use start_delay_seconds for initial execution timing
        next_execution = datetime.now(timezone.utc) + timedelta(seconds=final_start_delay_seconds)
        
        scheduled_guid = uuid4()
        complete_message = f"Scheduled request: {schedule_prompt}"
        
        # Call parent constructor with correct parameters
        super().__init__(user, complete_message, calling_bot, original_message, scheduled_guid)
       
        # Then set our specific Pydantic fields directly
        object.__setattr__(self, 'schedule_prompt', schedule_prompt)
        object.__setattr__(self, 'target_prompt', final_target_prompt)
        object.__setattr__(self, 'delay_seconds', final_delay_seconds)
        object.__setattr__(self, 'start_delay_seconds', final_start_delay_seconds)
        object.__setattr__(self, 'schedule_type', final_schedule_type)
        object.__setattr__(self, 'next_execution', next_execution)
        object.__setattr__(self, 'is_active', True)
        object.__setattr__(self, 'run_on_startup', run_on_startup)
        
        # Store creation time for proper timing calculation
        object.__setattr__(self, 'creation_time', datetime.now(timezone.utc))
        
        # Now we have access to chat session, log the schedule parameters
        chat_session = self.user.chat_history[self.chat_session_guid]
        LogFire.log("TASK", "Using provided schedule parameters", chat=chat_session)
    
    async def run_scheduled_request(self):
        is_first_execution = True
        
        while self.is_active:
            # Check if this should run immediately on startup (first execution only)
            if is_first_execution and self.run_on_startup:
                chat_session = self.user.chat_history[self.chat_session_guid]
                LogFire.log("REQUEST", f"Executing startup request immediately: {self.target_prompt}", chat=chat_session)
            else:
                # Wait for the scheduled time
                if self.next_execution:
                    now = datetime.now(timezone.utc)
                    # Ensure next_execution is timezone-aware
                    next_exec = self.next_execution
                    if next_exec.tzinfo is None:
                        next_exec = next_exec.replace(tzinfo=timezone.utc)
                    
                    if next_exec > now:
                        delay = (next_exec - now).total_seconds()
                        if delay > 0:
                            # Temporary: Log all waits for debugging call trace issue (including short waits)
                            chat_session = self.user.chat_history[self.chat_session_guid]
                            LogFire.log("REQUEST", f"Waiting {delay} seconds until next execution at {next_exec}", chat=chat_session)
                            await asyncio.sleep(delay)
                else:
                    # Fallback to delay_seconds if no next_execution set
                    sleep_time = self.start_delay_seconds if is_first_execution else self.delay_seconds
                    if sleep_time > 0:
                        # Temporary: Log all waits for debugging call trace issue
                        chat_session = self.user.chat_history[self.chat_session_guid]
                        LogFire.log("REQUEST", f"Waiting {sleep_time} seconds for {'initial' if is_first_execution else 'recurring'} execution", chat=chat_session)
                        await asyncio.sleep(sleep_time)
            
            if not self.is_active:
                break
                
            # Update status to indicate execution is about to start
            self.status = 'executing'
            self.message = f'Executing scheduled request: {self.target_prompt}'
            self.execution_started_at = datetime.now(timezone.utc).timestamp()
            
            # Execute the target prompt by calling parent class
            try:
                # Create a new LongRunningZairaRequest for the actual execution
                # Pass our scheduled_guid so supervisor sees the correct UUID
                # Pass our chat_session_guid so execution uses the same session (prevents 4th session creation)
                execution_request = LongRunningZairaRequest(
                    user=self.user,
                    complete_message=self.target_prompt,
                    calling_bot=self.calling_bot,
                    original_message=self.original_physical_message,
                    scheduled_guid=self.scheduled_guid,
                    parent_chat_session_guid=self.chat_session_guid
                )
                
                # Run the request in a separate async task to avoid blocking the scheduler
                from asyncio import create_task
                from etc.helper_functions import handle_asyncio_task_result_errors
                
                # Create background async task for execution
                execution_background_task = create_task(execution_request.run_request())
                
                # Create background async task for status monitoring with call trace capture
                async def monitor_and_capture_call_trace():
                    # For long-running requests, periodically check for call traces instead of waiting for completion
                    max_wait_time = 30  # Wait up to 30 seconds for call trace to appear
                    check_interval = 2  # Check every 2 seconds
                    attempts = 0
                    max_attempts = max_wait_time // check_interval
                    
                    LogFire.log("DEBUG", f"Starting call trace monitoring for execution request (max {max_attempts} attempts)")
                    
                    # Track previous state to avoid redundant logging
                    previous_status = None
                    previous_call_trace_count = 0
                    
                    while attempts < max_attempts:
                        await asyncio.sleep(check_interval)
                        attempts += 1
                        
                        # Get current execution status - use direct attributes
                        execution_status = {
                            'status': execution_request.status,
                            'message': execution_request.message,
                            'call_trace': execution_request.call_trace
                        }
                        
                        # Check for call trace
                        call_trace = execution_status.get('call_trace', [])
                        current_status = execution_status.get('status')
                        
                        # Only log when state actually changes (avoid redundant logging)
                        state_changed = (
                            current_status != previous_status or
                            len(call_trace) != previous_call_trace_count or
                            attempts == 1 or  # Always log first attempt
                            attempts == max_attempts  # Always log last attempt
                        )
                        
                        if state_changed:
                            LogFire.log("DEBUG", f"Execution request status check {attempts}/{max_attempts}: status={current_status}, call_trace={len(call_trace)} entries")
                            
                            previous_status = current_status
                            previous_call_trace_count = len(call_trace)
                        
                        if call_trace:
                            # Update our scheduled request's status with the call trace
                            self.call_trace = call_trace
                            self.last_execution_result = execution_status.get('message', '')
                            self.call_trace_captured_at = datetime.now(timezone.utc).timestamp()
                            
                            LogFire.log("DEBUG", f"Captured from execution request - call_trace: {len(call_trace)} entries")
                            return  # Success - exit monitoring
                        
                        
                        # If request completed without call trace, log and exit
                        if execution_status.get('status') in ['completed', 'failed', 'cancelled']:
                            LogFire.log("DEBUG", f"Request completed with status '{execution_status.get('status')}' but no call_trace found. Keys: {list(execution_status.keys())}")
                            
                            
                            return
                    
                    # Max attempts reached
                    LogFire.log("DEBUG", f"Call trace monitoring timeout after {max_wait_time}s - no call_trace captured")
                
                status_background_task = create_task(monitor_and_capture_call_trace())
                
                # Add error handling callbacks
                execution_background_task.add_done_callback(lambda t: handle_asyncio_task_result_errors(t))
                status_background_task.add_done_callback(lambda t: handle_asyncio_task_result_errors(t))
                
                # Async tasks are now running in background - scheduler continues without blocking
                
                chat_session = self.user.chat_history[self.chat_session_guid]
                LogFire.log("REQUEST", f"Executed scheduled request: {self.target_prompt}", chat=chat_session)
                
            except Exception as e:
                chat_session = self.user.chat_history[self.chat_session_guid]
                LogFire.log("ERROR", f"Scheduled request execution failed: {str(e)}", chat=chat_session)
                await self.calling_bot.send_reply(f"Scheduled request failed: {str(e)}", self, self.original_physical_message, False)
            
            # Handle recurring vs one-time requests
            if self.schedule_type == ScheduleType.ONCE:
                self.is_active = False
                break
            else:
                # Schedule next execution using delay_seconds (not start_delay_seconds)
                self.next_execution = datetime.now(timezone.utc) + timedelta(seconds=self.delay_seconds)
                # Ensure next_execution is timezone-aware
                if self.next_execution.tzinfo is None:
                    object.__setattr__(self, 'next_execution', self.next_execution.replace(tzinfo=timezone.utc))
                self.status = 'scheduled'
                self.message = f'Next execution at {self.next_execution.strftime("%Y-%m-%d %H:%M:%S")}'
                
                # Update persistence with new schedule
                from etc.helper_functions import handle_asyncio_task_result_errors
                task = asyncio.create_task(self._update_persistence())
                task.add_done_callback(handle_asyncio_task_result_errors)
                
                # Mark that first execution is complete
                is_first_execution = False
    
    async def run_request(self):
        # Override parent's run_request to start the scheduling loop
        # Ensure next_execution is timezone-aware before using it
        next_exec = self.next_execution
        if next_exec and next_exec.tzinfo is None:
            next_exec = next_exec.replace(tzinfo=timezone.utc)
            object.__setattr__(self, 'next_execution', next_exec)
        
        self.status = 'scheduled'
        self.message = f'Request scheduled for {next_exec.strftime("%Y-%m-%d %H:%M:%S")}' if next_exec else 'Request scheduled'
        self.started_at = datetime.now(timezone.utc).timestamp()
        
        # Start the scheduling loop
        await self.run_scheduled_request()
        
        # Mark as completed when done
        self.status = 'completed'
        self.message = 'Scheduled request completed'
        self.completed_at = datetime.now(timezone.utc).timestamp()
    
    def cancel_schedule(self, reason: str = "User requested cancellation"):
        """Cancel the scheduled request"""
        self.is_active = False
        self.status = 'cancelled'
        self.message = f'Scheduled request cancelled: {reason}'
        self.cancelled_at = datetime.now(timezone.utc).timestamp()
        
        # Save cancellation to persistence
        from etc.helper_functions import handle_asyncio_task_result_errors
        task = asyncio.create_task(self._save_cancellation_to_persistence(reason))
        task.add_done_callback(handle_asyncio_task_result_errors)
    
    def get_schedule_info(self) -> Dict[str, Any]:
        """Get information about this scheduled request"""
        return {
            'schedule_prompt': self.schedule_prompt,
            'target_prompt': self.target_prompt,
            'delay_seconds': self.delay_seconds,
            'start_delay_seconds': self.start_delay_seconds,
            'schedule_type': self.schedule_type.value,
            'next_execution': self.next_execution.isoformat() if self.next_execution else None,
            'is_active': self.is_active,
            'scheduled_guid': str(self.scheduled_guid),
            'creation_time': self.creation_time.isoformat() if self.creation_time else None
        }
    
    async def _save_to_persistence(self):
        """Save this request to persistence layer (legacy fallback)"""
        try:
            from managers.scheduled_requests import ScheduledRequestPersistenceManager
            persistence_manager = ScheduledRequestPersistenceManager.get_instance()
            await persistence_manager.save_task(self)
        except Exception as e:
            LogFire.log("ERROR", f"Failed to save scheduled request to persistence: {str(e)}")
    
    async def _save_cancellation_to_persistence(self, reason: str):
        """Save request cancellation to persistence layer"""
        try:
            from managers.scheduled_requests import ScheduledRequestPersistenceManager
            persistence_manager = ScheduledRequestPersistenceManager.get_instance()
            await persistence_manager.cancel_task(str(self.scheduled_guid), reason)
        except Exception as e:
            LogFire.log("ERROR", f"Failed to save request cancellation to persistence: {str(e)}")
    
    async def _update_persistence(self):
        """Update this request in persistence (called when schedule changes)"""
        await self._save_to_persistence()
    
    def _extract_parsed_info_from_llm_result(self, result, prompt: str):
        """Extract parsed information from LLM result - for backward compatibility with tests"""
        # Parse the result string which should contain structured output
        target_prompt = ""
        delay_seconds = 0.0
        start_delay_seconds = 0.0
        schedule_type = ScheduleType.ONCE
        
        # Handle different result formats
        result_text = ""
        if hasattr(result, 'messages') and len(result.messages) > 0:
            # Mock result format from tests
            result_text = result.messages[0].content
        elif hasattr(result, 'content'):
            result_text = result.content
        elif isinstance(result, str):
            result_text = result
        else:
            return target_prompt, delay_seconds, start_delay_seconds, schedule_type
            
        if not result_text:
            return target_prompt, delay_seconds, start_delay_seconds, schedule_type
            
        # Look for TARGET_PROMPT, DELAY_SECONDS, START_DELAY_SECONDS, SCHEDULE_TYPE
        lines = result_text.split('\n')
        for line in lines:
            line = line.strip()
            if line.startswith('TARGET_PROMPT:'):
                target_prompt = line.replace('TARGET_PROMPT:', '').strip()
            elif line.startswith('DELAY_SECONDS:'):
                try:
                    delay_seconds = float(line.replace('DELAY_SECONDS:', '').strip())
                except ValueError:
                    delay_seconds = 0.0
            elif line.startswith('START_DELAY_SECONDS:'):
                try:
                    start_delay_seconds = float(line.replace('START_DELAY_SECONDS:', '').strip())
                except ValueError:
                    start_delay_seconds = 0.0
            elif line.startswith('SCHEDULE_TYPE:'):
                schedule_type_str = line.replace('SCHEDULE_TYPE:', '').strip().lower()
                if schedule_type_str == 'recurring':
                    schedule_type = ScheduleType.RECURRING
                else:
                    schedule_type = ScheduleType.ONCE
        
        return target_prompt, delay_seconds, start_delay_seconds, schedule_type
    
    def _convert_to_seconds(self, interval: int, unit: str) -> float:
        """Convert time interval to seconds - for backward compatibility with tests"""
        multipliers = {
            'second': 1,
            'minute': 60,
            'hour': 3600,
            'day': 86400
        }
        return float(interval * multipliers.get(unit, 1))
    
    
    def get_schedule_info(self) -> Dict[str, Any]:
        """Get schedule information for this request"""
        
        return {
            'schedule_prompt': self.schedule_prompt,
            'target_prompt': self.target_prompt,
            'delay_seconds': self.delay_seconds,
            'start_delay_seconds': self.start_delay_seconds,
            'schedule_type': self.schedule_type.value,
            'next_execution': self.next_execution.isoformat() if self.next_execution else None,
            'is_active': self.is_active,
            'run_on_startup': self.run_on_startup,
            'creation_time': self.creation_time.isoformat() if self.creation_time else None
        }


# Model rebuild handled in userprofiles/__init__.py after all classes are defined
