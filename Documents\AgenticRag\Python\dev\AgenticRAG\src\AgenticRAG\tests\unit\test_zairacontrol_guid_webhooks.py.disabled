from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from aiohttp import web
from uuid import uuid4

@pytest.mark.asyncio
class TestZairaControlGUIDWebhooks:
    """Test that ZairaControl webhooks only accept GUIDs and fetch data from app"""
    
    @pytest.fixture
    async def setup_endpoint(self):
        """Setup ZairaControl endpoint for testing"""
        from endpoints.zairacontrol_endpoint import ZairaControlEndpoint
        endpoint = ZairaControlEndpoint()
        return endpoint
    
    @pytest.fixture
    def mock_request(self):
        """Create mock request object"""
        request = AsyncMock(spec=web.Request)
        request.json = AsyncMock()
        request.query = {}
        request.remote = "127.0.0.1"
        return request
    
    @pytest.fixture
    def valid_guids(self):
        """Generate valid test GUIDs"""
        return {
            'user_guid': str(uuid4()),
            'scheduled_guid': str(uuid4()),
            'task_guid': str(uuid4())
        }
    
    @pytest.mark.asyncio
    async def test_cancel_request_accepts_only_guids(self, setup_endpoint, mock_request, valid_guids):
        """Test that api_cancel_request only accepts GUIDs"""
        endpoint = await setup_endpoint
        
        # Setup mock request with only GUIDs
        mock_request.json.return_value = {
            'user_guid': valid_guids['user_guid'],
            'scheduled_guid': valid_guids['scheduled_guid']
            # Note: NO 'reason' field - should be fetched from app
        }
        
        with patch('endpoints.zairacontrol_endpoint.get_admin_interface') as mock_admin:
            with patch('endpoints.zairacontrol_endpoint.ZairaUserManager') as mock_user_manager:
                with patch('endpoints.zairacontrol_endpoint.get_integration_adapter') as mock_adapter:
                    # Setup mocks
                    mock_admin_instance = AsyncMock()
                    mock_admin_instance.get_request_details = AsyncMock(return_value={
                        'cancellation_reason': 'Test reason from app data'
                    })
                    mock_admin.return_value = mock_admin_instance
                    
                    mock_user = Mock()
                    mock_user.user_guid = valid_guids['user_guid']
                    mock_user_manager_instance = Mock()
                    mock_user_manager_instance.find_user = AsyncMock(return_value=mock_user)
                    mock_user_manager.get_instance.return_value = mock_user_manager_instance
                    
                    mock_adapter_instance = AsyncMock()
                    mock_adapter_instance.cancel_scheduled_request = AsyncMock(return_value=True)
                    mock_adapter.return_value = mock_adapter_instance
                    
                    # Call the endpoint
                    response = await endpoint.api_cancel_request(mock_request)
                    
                    # Verify GUIDs were used correctly
                    mock_user_manager_instance.find_user.assert_called_once_with(valid_guids['user_guid'])
                    mock_admin_instance.get_request_details.assert_called_once_with(valid_guids['scheduled_guid'])
                    
                    # Verify reason was fetched from app data
                    mock_adapter_instance.cancel_scheduled_request.assert_called_once()
                    call_args = mock_adapter_instance.cancel_scheduled_request.call_args
                    assert call_args[0][2] == 'Test reason from app data'  # Third argument is reason
                    
                    # Verify successful response
                    assert response.status == 200
    
    @pytest.mark.asyncio
    async def test_cancel_request_missing_guids_error(self, setup_endpoint, mock_request):
        """Test that missing GUIDs return error"""
        endpoint = await setup_endpoint
        
        # Test with missing user_guid
        mock_request.json.return_value = {
            'scheduled_guid': str(uuid4())
        }
        
        response = await endpoint.api_cancel_request(mock_request)
        assert response.status == 400
        
        # Test with missing scheduled_guid
        mock_request.json.return_value = {
            'user_guid': str(uuid4())
        }
        
        response = await endpoint.api_cancel_request(mock_request)
        assert response.status == 400
    
    @pytest.mark.asyncio
    async def test_fetch_app_data_user_type(self, setup_endpoint, valid_guids):
        """Test _fetch_app_data for user type"""
        endpoint = await setup_endpoint
        
        with patch('endpoints.zairacontrol_endpoint.ZairaUserManager') as mock_user_manager:
            mock_user = Mock()
            mock_user.user_guid = valid_guids['user_guid']
            mock_user.username = "testuser"
            mock_user.email = "<EMAIL>"
            mock_user.rank = Mock(value="USER")
            mock_user.DeviceGUID = uuid4()
            
            mock_user_manager_instance = Mock()
            mock_user_manager_instance.find_user = AsyncMock(return_value=mock_user)
            mock_user_manager.get_instance.return_value = mock_user_manager_instance
            
            result = await endpoint._fetch_app_data('user', valid_guids['user_guid'])
            
            assert result is not None
            assert result['user_guid'] == valid_guids['user_guid']
            assert result['username'] == "testuser"
            assert result['email'] == "<EMAIL>"
    
    @pytest.mark.asyncio
    async def test_fetch_app_data_request_type(self, setup_endpoint, valid_guids):
        """Test _fetch_app_data for request type"""
        endpoint = await setup_endpoint
        
        with patch('endpoints.zairacontrol_endpoint.get_admin_interface') as mock_admin:
            mock_admin_instance = AsyncMock()
            mock_admin_instance.get_request_details = AsyncMock(return_value={
                'scheduled_guid': valid_guids['scheduled_guid'],
                'status': 'active',
                'content': 'Test request'
            })
            mock_admin.return_value = mock_admin_instance
            
            result = await endpoint._fetch_app_data('request', valid_guids['scheduled_guid'])
            
            assert result is not None
            assert result['scheduled_guid'] == valid_guids['scheduled_guid']
            assert result['status'] == 'active'
    
    @pytest.mark.asyncio
    async def test_fetch_app_data_invalid_type(self, setup_endpoint, valid_guids):
        """Test _fetch_app_data with invalid data type"""
        endpoint = await setup_endpoint
        
        result = await endpoint._fetch_app_data('invalid_type', valid_guids['user_guid'])
        assert result is None
    
    @pytest.mark.asyncio
    async def test_user_details_endpoint_uses_guid(self, setup_endpoint, mock_request, valid_guids):
        """Test that user details endpoint uses GUID to fetch data"""
        endpoint = await setup_endpoint
        
        # Setup query parameters with GUID
        mock_request.query = {'user_guid': valid_guids['user_guid']}
        
        with patch('endpoints.zairacontrol_endpoint.get_admin_interface') as mock_admin:
            mock_admin_instance = AsyncMock()
            mock_admin_instance.get_user_details = AsyncMock(return_value={
                'user_info': {
                    'user_guid': valid_guids['user_guid'],
                    'username': 'testuser'
                },
                'quota_status': {},
                'metrics': {}
            })
            mock_admin.return_value = mock_admin_instance
            
            response = await endpoint.api_user_details(mock_request)
            
            # Verify GUID was used
            mock_admin_instance.get_user_details.assert_called_once_with(valid_guids['user_guid'])
            assert response.status == 200
    
    @pytest.mark.asyncio
    async def test_cancel_request_fetches_reason_from_app(self, setup_endpoint, mock_request, valid_guids):
        """Test that cancellation reason is fetched from app data, not request"""
        endpoint = await setup_endpoint
        
        # Request has NO reason field
        mock_request.json.return_value = {
            'user_guid': valid_guids['user_guid'],
            'scheduled_guid': valid_guids['scheduled_guid']
        }
        
        with patch('endpoints.zairacontrol_endpoint.get_admin_interface') as mock_admin:
            with patch('endpoints.zairacontrol_endpoint.ZairaUserManager') as mock_user_manager:
                with patch('endpoints.zairacontrol_endpoint.get_integration_adapter') as mock_adapter:
                    # Setup admin to return request details with reason
                    mock_admin_instance = AsyncMock()
                    mock_admin_instance.get_request_details = AsyncMock(return_value={
                        'cancellation_reason': 'App-stored cancellation reason'
                    })
                    mock_admin.return_value = mock_admin_instance
                    
                    # Setup user manager
                    mock_user = Mock()
                    mock_user_manager_instance = Mock()
                    mock_user_manager_instance.find_user = AsyncMock(return_value=mock_user)
                    mock_user_manager.get_instance.return_value = mock_user_manager_instance
                    
                    # Setup adapter
                    mock_adapter_instance = AsyncMock()
                    mock_adapter_instance.cancel_scheduled_request = AsyncMock(return_value=True)
                    mock_adapter.return_value = mock_adapter_instance
                    
                    await endpoint.api_cancel_request(mock_request)
                    
                    # Verify reason was fetched from app, not request
                    mock_admin_instance.get_request_details.assert_called_once_with(valid_guids['scheduled_guid'])
                    
                    # Verify the app-stored reason was used
                    call_args = mock_adapter_instance.cancel_scheduled_request.call_args
                    assert call_args[0][2] == 'App-stored cancellation reason'
    
    @pytest.mark.asyncio
    async def test_cancel_request_default_reason_when_no_app_data(self, setup_endpoint, mock_request, valid_guids):
        """Test that default reason is used when app has no stored reason"""
        endpoint = await setup_endpoint
        
        mock_request.json.return_value = {
            'user_guid': valid_guids['user_guid'],
            'scheduled_guid': valid_guids['scheduled_guid']
        }
        
        with patch('endpoints.zairacontrol_endpoint.get_admin_interface') as mock_admin:
            with patch('endpoints.zairacontrol_endpoint.ZairaUserManager') as mock_user_manager:
                with patch('endpoints.zairacontrol_endpoint.get_integration_adapter') as mock_adapter:
                    # Admin returns None (no app data)
                    mock_admin_instance = AsyncMock()
                    mock_admin_instance.get_request_details = AsyncMock(return_value=None)
                    mock_admin.return_value = mock_admin_instance
                    
                    # Setup user manager
                    mock_user = Mock()
                    mock_user_manager_instance = Mock()
                    mock_user_manager_instance.find_user = AsyncMock(return_value=mock_user)
                    mock_user_manager.get_instance.return_value = mock_user_manager_instance
                    
                    # Setup adapter
                    mock_adapter_instance = AsyncMock()
                    mock_adapter_instance.cancel_scheduled_request = AsyncMock(return_value=True)
                    mock_adapter.return_value = mock_adapter_instance
                    
                    await endpoint.api_cancel_request(mock_request)
                    
                    # Verify default reason was used
                    call_args = mock_adapter_instance.cancel_scheduled_request.call_args
                    assert call_args[0][2] == 'Admin cancellation'