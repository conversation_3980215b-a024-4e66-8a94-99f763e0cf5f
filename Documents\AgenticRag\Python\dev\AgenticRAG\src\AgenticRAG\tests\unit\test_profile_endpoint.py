from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import Mock, patch, AsyncMock
from aiohttp import web
from aiohttp.test_utils import AioHTTPTestCase, unittest_run_loop
from pydantic import ValidationError
from uuid import uuid4

from endpoints.profile_endpoint import ProfileEndpoint
from userprofiles.ZairaUser import <PERSON>aira<PERSON><PERSON>, PERMISSION_LEVELS
from tests.unit.test_logging_capture import with_unit_test_logging

class TestProfileEndpoint(AioHTTPTestCase):
    
    async def get_application(self):
        app = web.Application()
        self.profile_endpoint = ProfileEndpoint()
        await self.profile_endpoint.setup_routes(app)
        return app
    
    @with_unit_test_logging
    @unittest_run_loop
    async def test_profile_page_render(self):
        """Test profile page renders correctly with user data"""
        user_guid = str(uuid4())
        
        # Mock user data
        mock_user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        mock_user.first_name = "Test"
        mock_user.last_name = "User"
        mock_user.email = "<EMAIL>"
        mock_user.job_title = "Software Developer"
        mock_user.company = "Test Company"
        mock_user.personal_prompt = "I prefer detailed technical explanations."
        
        with patch('managers.manager_users.ZairaUserManager.get_instance') as mock_manager:
            mock_manager.return_value.find_user = AsyncMock(return_value=mock_user)
            
            # Test successful page render
            resp = await self.client.request("GET", f"/profile?user_guid={user_guid}")
            assert resp.status == 200
            
            content = await resp.text()
            assert "AskZaira Profile" in content
            assert "Test User" in content
            assert "Software Developer" in content
            assert "<EMAIL>" in content
    
    @with_unit_test_logging
    @unittest_run_loop 
    async def test_profile_page_demo_user_creation(self):
        """Test demo user creation when user not found"""
        user_guid = "non-existent-user"
        
        with patch('managers.manager_users.ZairaUserManager.get_instance') as mock_manager:
            mock_manager.return_value.find_user = AsyncMock(return_value=None)
            mock_manager.return_value.add_user = AsyncMock()
            
            resp = await self.client.request("GET", f"/profile?user_guid={user_guid}")
            assert resp.status == 200
            
            content = await resp.text()
            assert "Demo User" in content
            assert "AI Specialist" in content
    
    @with_unit_test_logging
    @unittest_run_loop
    async def test_update_profile_success(self):
        """Test successful profile update"""
        user_guid = str(uuid4())
        
        mock_user = ZairaUser(
            username="test_user", 
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        with patch('managers.manager_users.ZairaUserManager.get_instance') as mock_manager:
            mock_manager.return_value.find_user = AsyncMock(return_value=mock_user)
            
            update_data = {
                'first_name': 'Updated',
                'last_name': 'Name',
                'email': '<EMAIL>', 
                'job_title': 'Senior Developer',
                'company': 'New Company',
                'personal_prompt': 'Updated prompt preferences',
                'preferred_language': 'nl',
                'timezone': 'Europe/Amsterdam'
            }
            
            resp = await self.client.request("POST", f"/profile/update?user_guid={user_guid}", data=update_data)
            assert resp.status == 200
            
            result = await resp.json()
            assert result['success'] is True
            assert result['message'] == 'Profile updated successfully'
            
            # Verify user object was updated
            assert mock_user.first_name == 'Updated'
            assert mock_user.last_name == 'Name'  
            assert mock_user.email == '<EMAIL>'
            assert mock_user.job_title == 'Senior Developer'
            assert mock_user.company == 'New Company'
            assert mock_user.personal_prompt == 'Updated prompt preferences'
            assert mock_user.preferred_language == 'nl'
            assert mock_user.timezone == 'Europe/Amsterdam'
            assert mock_user.real_name == 'Updated Name'
    
    @with_unit_test_logging
    @unittest_run_loop
    async def test_update_profile_invalid_email(self):
        """Test profile update with invalid email"""
        user_guid = str(uuid4())
        
        mock_user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER, 
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        with patch('managers.manager_users.ZairaUserManager.get_instance') as mock_manager:
            mock_manager.return_value.find_user = AsyncMock(return_value=mock_user)
            
            update_data = {
                'first_name': 'Test',
                'last_name': 'User',
                'email': 'invalid-email-format',
                'job_title': 'Developer'
            }
            
            resp = await self.client.request("POST", f"/profile/update?user_guid={user_guid}", data=update_data)
            assert resp.status == 400
            
            result = await resp.json()
            assert 'error' in result
            assert 'Invalid email address' in result['error']
    
    @with_unit_test_logging 
    @unittest_run_loop
    async def test_update_profile_user_not_found(self):
        """Test profile update when user not found"""
        user_guid = "non-existent-user"
        
        with patch('managers.manager_users.ZairaUserManager.get_instance') as mock_manager:
            mock_manager.return_value.find_user = AsyncMock(return_value=None)
            
            update_data = {'first_name': 'Test'}
            
            resp = await self.client.request("POST", f"/profile/update?user_guid={user_guid}", data=update_data)
            assert resp.status == 404
            
            result = await resp.json()
            assert result['error'] == 'User not found'
    
    @with_unit_test_logging
    @unittest_run_loop 
    async def test_get_profile_stats(self):
        """Test profile statistics endpoint"""
        user_guid = str(uuid4())
        
        mock_user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.ADMIN,
            guid=uuid4(), 
            device_guid=uuid4()
        )
        
        # Mock some requests and chat history
        mock_user.my_requests = {uuid4(): Mock(), uuid4(): Mock()}
        mock_user.chat_history = {uuid4(): Mock(), uuid4(): Mock()}
        
        with patch('managers.manager_users.ZairaUserManager.get_instance') as mock_manager:
            mock_manager.return_value.find_user = AsyncMock(return_value=mock_user)
            
            resp = await self.client.request("GET", f"/profile/api/stats?user_guid={user_guid}")
            assert resp.status == 200
            
            stats = await resp.json()
            assert stats['total_requests'] == 2
            assert stats['active_sessions'] == 2
            assert stats['permission_level'] == 'ADMIN'
            assert stats['last_activity'] == 'Just now'
            assert stats['chat_sessions'] == 2

class TestProfileEndpointHelpers:
    """Test helper methods of ProfileEndpoint"""
    
    @with_unit_test_logging
    def test_get_user_initials_full_name(self):
        """Test user initials with first and last name"""
        profile_endpoint = ProfileEndpoint()
        
        user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        user.first_name = "John"
        user.last_name = "Doe"
        
        initials = profile_endpoint._get_user_initials(user)
        assert initials == "JD"
    
    @with_unit_test_logging
    def test_get_user_initials_real_name(self):
        """Test user initials with real_name field"""
        profile_endpoint = ProfileEndpoint()
        
        user = ZairaUser(
            username="test_user", 
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        user.real_name = "Jane Smith"
        
        initials = profile_endpoint._get_user_initials(user)
        assert initials == "JS"
    
    @with_unit_test_logging
    def test_get_user_initials_username_fallback(self):
        """Test user initials fallback to username"""
        profile_endpoint = ProfileEndpoint()
        
        user = ZairaUser(
            username="testuser",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        initials = profile_endpoint._get_user_initials(user)
        assert initials == "TE"
    
    @with_unit_test_logging
    def test_get_user_initials_empty_fallback(self):
        """Test user initials fallback when no name data"""
        profile_endpoint = ProfileEndpoint()
        
        user = ZairaUser(
            username="",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        initials = profile_endpoint._get_user_initials(user)
        assert initials == "U"
    
    @with_unit_test_logging
    def test_is_valid_email(self):
        """Test email validation method"""
        profile_endpoint = ProfileEndpoint()
        
        # Valid emails
        assert profile_endpoint._is_valid_email("<EMAIL>") is True
        assert profile_endpoint._is_valid_email("<EMAIL>") is True
        assert profile_endpoint._is_valid_email("<EMAIL>") is True
        
        # Invalid emails
        assert profile_endpoint._is_valid_email("invalid-email") is False
        assert profile_endpoint._is_valid_email("@example.com") is False
        assert profile_endpoint._is_valid_email("test@") is False
        assert profile_endpoint._is_valid_email("test@.com") is False
        assert profile_endpoint._is_valid_email("") is False

class TestZairaUserProfileFields:
    """Test the enhanced ZairaUser model with profile fields"""
    
    @with_unit_test_logging
    def test_zaira_user_profile_fields_creation(self):
        """Test ZairaUser creation with new profile fields"""
        user = ZairaUser(
            username="profile_test",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        # Test default values
        assert user.first_name == ""
        assert user.last_name == ""
        assert user.job_title == ""
        assert user.company == ""
        assert user.personal_prompt == ""
        assert user.preferred_language == "en"
        assert user.timezone == "UTC"
    
    @with_unit_test_logging
    def test_zaira_user_profile_fields_assignment(self):
        """Test assignment of profile field values"""
        user = ZairaUser(
            username="profile_test",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        # Assign profile values
        user.first_name = "Profile"
        user.last_name = "Tester"
        user.job_title = "QA Engineer"
        user.company = "Test Corp"
        user.personal_prompt = "I like detailed step-by-step instructions."
        user.preferred_language = "nl"
        user.timezone = "Europe/Amsterdam"
        
        # Verify assignments
        assert user.first_name == "Profile"
        assert user.last_name == "Tester"
        assert user.job_title == "QA Engineer"
        assert user.company == "Test Corp"
        assert user.personal_prompt == "I like detailed step-by-step instructions."
        assert user.preferred_language == "nl"
        assert user.timezone == "Europe/Amsterdam"
    
    @with_unit_test_logging
    def test_zaira_user_profile_backward_compatibility(self):
        """Test that existing ZairaUser functionality still works"""
        user = ZairaUser(
            username="compat_test",
            rank=PERMISSION_LEVELS.ADMIN,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        # Test existing fields still work
        assert user.username == "compat_test"
        assert user.rank == PERMISSION_LEVELS.ADMIN
        assert user.real_name == ""
        assert user.email == ""
        assert user.is_system_user is False
        
        # Test GUID properties
        assert user.user_guid == str(user.GUID)
        assert isinstance(user.GUID, UUID)
        assert isinstance(user.DeviceGUID, UUID)
        
        # Test request management
        assert user.has_active_requests() is False
        assert len(user.my_requests) == 0