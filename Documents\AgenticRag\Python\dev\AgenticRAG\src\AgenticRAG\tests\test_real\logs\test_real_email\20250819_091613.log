=== TEST DEBUG TRACE STARTED ===
Test Function: test_real_email
Started: 2025-08-19 09:16:13.918877
================================================================================

[2025-08-19 09:16:13.919] Test debug trace capture activated - all console output will be logged to tests\test_real\logs\test_real_email\20250819_091613.log
[2025-08-19 09:16:15.933] [TEST_DEBUG] TestRealEmail: Changing directory from C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG to C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG
[2025-08-19 09:16:15.933] [TEST_DEBUG] TestRealEmail: test_real getcwd(): C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG
[2025-08-19 09:16:15.933] [TEST_DEBUG] TestRealEmail: test_real looking for dev.env at: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG/dev.env
[2025-08-19 09:16:15.933] [TEST_DEBUG] TestRealEmail: test_real dev.env exists: True
[2025-08-19 09:16:15.995] [MAIN_DEBUG] Globals.is_docker() = False
[2025-08-19 09:16:15.995] [MAIN_DEBUG] main.py getcwd(): C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG
[2025-08-19 09:16:15.995] [MAIN_DEBUG] main.py looking for dev.env at: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG/dev.env
[2025-08-19 09:16:15.995] [MAIN_DEBUG] main.py dev.env exists: True
[2025-08-19 09:16:18.361] Database 'vectordb' already exists.
[2025-08-19 09:16:18.536] LogEntries database table created/verified
[2025-08-19 09:16:19.175] [GLOBAL_TEST] 07:16:19.175[INIT], 'setup -> setup': ZairaControl endpoint routes registered successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:19.175102+00:00
[2025-08-19 09:16:19.354] [GLOBAL_TEST] 07:16:19.353[INIT], 'setup -> _create_oauth_table': OAuth database table created/verified .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:19.353102+00:00
[2025-08-19 09:16:19.488] 07:16:19.487 [Python][INIT], '_run_once -> _run': ZairaControl endpoint routes registered successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:16:19.224097
[2025-08-19 09:16:19.599] 07:16:19.598 [Python][INIT], '_run_once -> _run': [Scrubbed due to 'Auth'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:16:19.488097
[2025-08-19 09:16:23.120] [GLOBAL_TEST] 07:16:23.120[INIT], 'init -> setup': ZairaControl endpoint routes registered successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:23.120245+00:00
[2025-08-19 09:16:23.195] 07:16:23.193 [Python][INIT], '_run_once -> _run': ZairaControl endpoint routes registered successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:16:23.187079
[2025-08-19 09:16:23.301] [GLOBAL_TEST] 07:16:23.301[INIT], 'setup -> _create_tables_and_indexes': Database tables and indexes created/verified .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:23.301507+00:00
[2025-08-19 09:16:23.359] [GLOBAL_TEST] 07:16:23.359[INIT], 'mainFunc -> init': Setting up new scheduled request architecture .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:23.359781+00:00
[2025-08-19 09:16:23.420] [GLOBAL_TEST] 07:16:23.420[INIT], 'init -> setup': ScheduledRequestIntegrationAdapter initialized successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:23.420058+00:00
[2025-08-19 09:16:23.485] [GLOBAL_TEST] 07:16:23.485[INIT], 'mainFunc -> init': New scheduled request system ready .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:23.485057+00:00
[2025-08-19 09:16:23.545] [GLOBAL_TEST] 07:16:23.545[DEBUG], 'setup -> _create_system_user': Starting SYSTEM user creation process - GUID: 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:23.545745+00:00
[2025-08-19 09:16:23.625] [GLOBAL_TEST] 07:16:23.625[DEBUG], 'setup -> _create_system_user': Current users in registry: 0 users .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:23.625749+00:00
[2025-08-19 09:16:23.691] [GLOBAL_TEST] 07:16:23.691[DEBUG], 'setup -> _create_system_user': User registry keys: [] .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:23.691749+00:00
[2025-08-19 09:16:23.748] [GLOBAL_TEST] 07:16:23.747[DEBUG], 'setup -> _create_system_user': SYSTEM user not found in registry, creating new user .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:23.747748+00:00
[2025-08-19 09:16:23.813] [GLOBAL_TEST] 07:16:23.813[DEBUG], 'setup -> _create_system_user': Calling ZairaUserManager.add_user for SYSTEM user .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:23.813553+00:00
[2025-08-19 09:16:23.884] [GLOBAL_TEST] 07:16:23.884[INIT], 'add_user -> __init__': ZairaUser created: SYSTEM .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:23.884201+00:00
[2025-08-19 09:16:23.960] [GLOBAL_TEST] 07:16:23.960[USER], 'add_user -> __init__': User created with GUID 00000000-0000-0000-0000-000000000001 . Username: SYSTEM, rank: PERMISSION_LEVELS.ADMIN.  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 6da41922-ea85-414e-a745-8534b852d64a at 2025-08-19 07:16:23.960115+00:00
[2025-08-19 09:16:24.071] [GLOBAL_TEST] 07:16:24.070[DEBUG], 'setup -> _create_system_user': ZairaUserManager.add_user returned: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:24.070124+00:00
[2025-08-19 09:16:24.173] [GLOBAL_TEST] 07:16:24.173[DEBUG], 'setup -> _create_system_user': Set SYSTEM user properties - email: <EMAIL>, is_system_user: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:24.173586+00:00
[2025-08-19 09:16:24.232] [GLOBAL_TEST] 07:16:24.232[DEBUG], 'setup -> _create_system_user': Verification check - SYSTEM GUID in users registry: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:24.232583+00:00
[2025-08-19 09:16:24.286] [GLOBAL_TEST] 07:16:24.286[DEBUG], 'setup -> _create_system_user': Updated registry size: 1 users .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:24.286595+00:00
[2025-08-19 09:16:24.340] [GLOBAL_TEST] 07:16:24.340[DEBUG], 'setup -> _create_system_user': SYSTEM user automatically registered in users registry during creation .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:24.340587+00:00
[2025-08-19 09:16:24.396] [GLOBAL_TEST] 07:16:24.396[USER], 'setup -> _create_system_user': SYSTEM user created successfully 00000000-0000-0000-0000-000000000001.  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:24.396615+00:00
[2025-08-19 09:16:24.448] [GLOBAL_TEST] 07:16:24.448[DEBUG], 'setup -> _create_system_user': Final verification - can retrieve SYSTEM user from registry: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:24.448582+00:00
[2025-08-19 09:16:24.497] [GLOBAL_TEST] 07:16:24.497[USER], 'init -> setup': SystemUserManager initialized with SYSTEM user .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:24.497588+00:00
[2025-08-19 09:16:24.546] [GLOBAL_TEST] 07:16:24.545[EVENT], 'init -> loadEmbedding': Loading stored index .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:24.545582+00:00
[2025-08-19 09:16:26.895] [GLOBAL_TEST] 07:16:26.895[EVENT], 'init -> loadEmbedding': Index loaded .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:26.895899+00:00
[2025-08-19 09:16:26.945] [GLOBAL_TEST] 07:16:26.945[DEBUG], 'create_top_level_supervisor -> create_small_tasks': create_small_tasks called .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:26.945944+00:00
[2025-08-19 09:16:28.910] [GLOBAL_TEST] 07:16:28.910[DEBUG], 'test_real_email -> setup_real_system': [TestRealEmail] Restoring directory to C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:28.910801+00:00
[2025-08-19 09:16:28.958] [GLOBAL_TEST] 07:16:28.958[DEBUG], 'test_real_email -> setup_real_system': [TestRealEmail] Production mainFunc() failed: "ExcelAnalyzerTool" object has no field "df" .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:28.958767+00:00
[2025-08-19 09:16:28.958] 07:16:28.958[DEBUG], 'test_real_email -> setup_real_system': [TestRealEmail] Production mainFunc() failed: "ExcelAnalyzerTool" object has no field "df" .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:28.958767+00:00
[2025-08-19 09:16:28.960] Traceback (most recent call last):
[2025-08-19 09:16:28.960]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\tests\test_real\base_real_test.py", line 567, in setup_real_system
[2025-08-19 09:16:28.960]     await mainFunc()
[2025-08-19 09:16:28.960]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\tests\test_real\../..\tests\test_real\../..\main.py", line 105, in mainFunc
[2025-08-19 09:16:28.960]     await init(newProject, DATA_DIR, PERSIST_DIR, parsers)
[2025-08-19 09:16:28.960]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\tests\test_real\../..\etc\setup.py", line 181, in init
[2025-08-19 09:16:28.960]     await late_init()
[2025-08-19 09:16:28.960]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\tests\test_real\../..\etc\setup.py", line 184, in late_init
[2025-08-19 09:16:28.960]     await create_top_level_supervisor()
[2025-08-19 09:16:28.960]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\tests\test_real\../..\tasks\task_top_level_supervisor.py", line 128, in create_top_level_supervisor
[2025-08-19 09:16:28.960]     return await creator.create_top_level_supervisor()
[2025-08-19 09:16:28.960]            ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-08-19 09:16:28.960]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\tests\test_real\../..\tasks\task_top_level_supervisor.py", line 106, in create_top_level_supervisor
[2025-08-19 09:16:28.960]     await self.create_supervisors()
[2025-08-19 09:16:28.960]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\tests\test_real\../..\tasks\task_top_level_supervisor.py", line 88, in create_supervisors
[2025-08-19 09:16:28.960]     self.excel_analyzer_supervisor = await create_supervisor_excel_analyzer()
[2025-08-19 09:16:28.960]                                      ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-08-19 09:16:28.960]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\tests\test_real\../..\tasks\processing\task_excel_analyzer.py", line 187, in create_supervisor_excel_analyzer
[2025-08-19 09:16:28.960]     await creator.create_tasks()
[2025-08-19 09:16:28.961]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\tests\test_real\../..\tasks\processing\task_excel_analyzer.py", line 165, in create_tasks
[2025-08-19 09:16:28.961]     excel_tool = ExcelAnalyzerTool()
[2025-08-19 09:16:28.961]                  ^^^^^^^^^^^^^^^^^^^
[2025-08-19 09:16:28.961]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\tests\test_real\../..\tasks\processing\task_excel_analyzer.py", line 23, in __init__
[2025-08-19 09:16:28.961]     self.df = None
[2025-08-19 09:16:28.961]     ^^^^^^^
[2025-08-19 09:16:28.961]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\pydantic\main.py", line 1000, in __setattr__
[2025-08-19 09:16:28.961]     elif (setattr_handler := self._setattr_handler(name, value)) is not None:
[2025-08-19 09:16:28.961]                              ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-08-19 09:16:28.961]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\pydantic\main.py", line 1047, in _setattr_handler
[2025-08-19 09:16:28.961]     raise ValueError(f'"{cls.__name__}" object has no field "{name}"')
[2025-08-19 09:16:28.961] ValueError: "ExcelAnalyzerTool" object has no field "df"
[2025-08-19 09:16:29.006] [GLOBAL_TEST] 07:16:29.006[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: Setup result = False .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:29.006804+00:00
[2025-08-19 09:16:29.007] 07:16:29.006[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: Setup result = False .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:29.006804+00:00
[2025-08-19 09:16:29.049] [GLOBAL_TEST] 07:16:29.049[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ERROR: Production system setup failed! .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:29.049772+00:00
[2025-08-19 09:16:29.050] 07:16:29.049[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ERROR: Production system setup failed! .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:29.049772+00:00
[2025-08-19 09:16:29.094] [GLOBAL_TEST] 07:16:29.093[DEBUG], '_run -> wrapper': [TestRealEmail] Performing server cleanup... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:29.093801+00:00
[2025-08-19 09:16:29.094] 07:16:29.093[DEBUG], '_run -> wrapper': [TestRealEmail] Performing server cleanup... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:29.093801+00:00
[2025-08-19 09:16:29.142] [GLOBAL_TEST] 07:16:29.142[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Starting comprehensive server cleanup... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:29.142767+00:00
[2025-08-19 09:16:29.143] 07:16:29.142[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Starting comprehensive server cleanup... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:29.142767+00:00
[2025-08-19 09:16:29.188] [GLOBAL_TEST] 07:16:29.188[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] No servers in global registry .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:29.188768+00:00
[2025-08-19 09:16:29.189] 07:16:29.188[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] No servers in global registry .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:29.188768+00:00
[2025-08-19 09:16:29.230] [GLOBAL_TEST] 07:16:29.230[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Running final port cleanup check... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:29.230804+00:00
[2025-08-19 09:16:29.231] 07:16:29.230[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Running final port cleanup check... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:29.230804+00:00
[2025-08-19 09:16:29.279] [GLOBAL_TEST] 07:16:29.278[DEBUG], '_check_and_cleanup_ports -> _log_compact': [TR] Checking ports: [40999, 8083] .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:29.278801+00:00
[2025-08-19 09:16:29.279] 07:16:29.278[DEBUG], '_check_and_cleanup_ports -> _log_compact': [TR] Checking ports: [40999, 8083] .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:29.278801+00:00
[2025-08-19 09:16:31.369] [GLOBAL_TEST] 07:16:31.369[DEBUG], '_check_and_cleanup_single_port -> _log_compact': [TR] Port 40999 (ZAIRA_PYTHON_PORT) is available .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:31.369113+00:00
[2025-08-19 09:16:31.370] 07:16:31.369[DEBUG], '_check_and_cleanup_single_port -> _log_compact': [TR] Port 40999 (ZAIRA_PYTHON_PORT) is available .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:31.369113+00:00
[2025-08-19 09:16:33.466] [GLOBAL_TEST] 07:16:33.466[DEBUG], '_check_and_cleanup_single_port -> _log_compact': [TR] Port 8083 (RELAY_PORT) is available .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:33.466687+00:00
[2025-08-19 09:16:33.467] 07:16:33.466[DEBUG], '_check_and_cleanup_single_port -> _log_compact': [TR] Port 8083 (RELAY_PORT) is available .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:33.466687+00:00
[2025-08-19 09:16:33.521] [GLOBAL_TEST] 07:16:33.520[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Port cleanup completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:33.520672+00:00
[2025-08-19 09:16:33.521] 07:16:33.520[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Port cleanup completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:33.520672+00:00
[2025-08-19 09:16:33.578] [GLOBAL_TEST] 07:16:33.578[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Comprehensive server cleanup completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:33.578698+00:00
[2025-08-19 09:16:33.579] 07:16:33.578[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Comprehensive server cleanup completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:16:33.578698+00:00

================================================================================
=== TEST DEBUG TRACE COMPLETED ===
Completed: 2025-08-19 09:16:35.580374
[2025-08-19 09:16:35.580] [TEST_ENV] Allowing background tasks 5 seconds to complete logging...
[2025-08-19 09:16:40.581] [TEST_ENV] Global test environment cleared
[2025-08-19 09:16:40.581] [TEST_ENV] Global test environment cleared
