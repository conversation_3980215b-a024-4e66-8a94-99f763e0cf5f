from imports import *

import re
from datetime import datetime, timedelta, timezone
from langchain_core.tools import BaseTool
from managers.manager_supervisors import SupervisorTaskState
from typing import Optional
from enum import Enum
from langchain_core.messages import SystemMessage, HumanMessage
from etc.ZairaSettings import ZairaSettings

class ScheduleType(Enum):
    ONCE = "once"
    RECURRING = "recurring"

class ParseRecurringScheduleTool(BaseTool):
    """Tool for parsing recurring schedule patterns"""
    name: str = "parse_recurring_schedule"
    description: str = "Parse recurring schedule patterns like 'every X minutes/hours/days' or 'trigger X every Y time_unit'"
    
    def _run(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Parse recurring schedule patterns using LLM"""
        try:
            system_prompt = """You are a schedule parser specialized in recurring patterns. Analyze the user input and extract:
1. The action/task to be performed
2. The interval between repetitions
3. The time unit (seconds, minutes, hours, days)

Return ONLY in this exact format:
TARGET_PROMPT: [the action to perform]
DELAY_SECONDS: [interval in seconds as a number]
SCHEDULE_TYPE: recurring

If no recurring pattern is found, return exactly: "No recurring pattern found."

Examples:
Input: "trigger IMAP IDLE every 30 minutes"
Output: TARGET_PROMPT: trigger IMAP IDLE
DELAY_SECONDS: 1800
SCHEDULE_TYPE: recurring

Input: "send email every 2 hours"
Output: TARGET_PROMPT: send email
DELAY_SECONDS: 7200
SCHEDULE_TYPE: recurring"""

            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=prompt)
            ]
            
            result = await ZairaSettings.llm.ainvoke(input=messages)
            if hasattr(result, 'content'):
                return result.content
            return str(result)
            
        except Exception as e:
            return f"Error parsing recurring schedule: {str(e)}"
    


class ParseDailyScheduleTool(BaseTool):
    """Tool for parsing daily schedule patterns"""
    name: str = "parse_daily_schedule"
    description: str = "Parse daily schedule patterns like 'at 9am' or 'monday to friday' or 'send me X at Y time'"
    
    def _run(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Parse daily schedule patterns using LLM"""
        try:
            system_prompt = """You are a schedule parser specialized in daily and weekly patterns. Analyze the user input and extract:
1. The action/task to be performed
2. The specific time (hour)
3. The day pattern (specific weekday, daily, weekdays, etc.)

Calculate timing based on UTC timezone and return ONLY in this exact format:
TARGET_PROMPT: [the action to perform]
START_DELAY_SECONDS: [seconds until first execution]
DELAY_SECONDS: [interval between recurring executions in seconds]
SCHEDULE_TYPE: recurring

For daily patterns: DELAY_SECONDS should be 86400 (24 hours)
For weekly patterns: DELAY_SECONDS should be 604800 (7 days)
For weekday patterns: DELAY_SECONDS should be 86400 but skip weekends

If no daily/weekly pattern is found, return exactly: "No daily pattern found."

Examples:
Input: "Monday 9am Weekly Report"
Output: TARGET_PROMPT: Weekly Report
START_DELAY_SECONDS: [calculated seconds until next Monday 9am]
DELAY_SECONDS: 604800
SCHEDULE_TYPE: recurring

Input: "send me a good morning message at 9am daily"
Output: TARGET_PROMPT: send message: a good morning message
START_DELAY_SECONDS: [calculated seconds until next 9am]
DELAY_SECONDS: 86400
SCHEDULE_TYPE: recurring"""

            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"Current UTC time: {datetime.now(timezone.utc).isoformat()}. Parse: {prompt}")
            ]
            
            result = await ZairaSettings.llm.ainvoke(input=messages)
            if hasattr(result, 'content'):
                return result.content
            return str(result)
            
        except Exception as e:
            return f"Error parsing daily schedule: {str(e)}"


class ParseMonthlyScheduleTool(BaseTool):
    """Tool for parsing monthly schedule patterns"""
    name: str = "parse_monthly_schedule"
    description: str = "Parse monthly schedule patterns like 'first of the month' or 'every 15th' or 'email me X every Y of the month'"
    
    def _run(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Parse monthly schedule patterns using LLM"""
        try:
            system_prompt = """You are a schedule parser specialized in monthly patterns. Analyze the user input and extract:
1. The action/task to be performed
2. The specific day of the month (1st, 15th, last, etc.)
3. The time if specified (default to 9am if not specified)

Calculate timing based on UTC timezone and return ONLY in this exact format:
TARGET_PROMPT: [the action to perform]
START_DELAY_SECONDS: [seconds until first execution]
DELAY_SECONDS: [average seconds in a month: 2629746]
SCHEDULE_TYPE: recurring

Handle special cases:
- "first" = 1st day of month
- "last" = last day of month (varies by month)
- Numbered days like "15th", "1st", "30th"

If no monthly pattern is found, return exactly: "No monthly pattern found."

Examples:
Input: "email me a report every first of the month"
Output: TARGET_PROMPT: email report: a report
START_DELAY_SECONDS: [calculated seconds until next 1st at 9am]
DELAY_SECONDS: 2629746
SCHEDULE_TYPE: recurring

Input: "send summary on 15th of each month"
Output: TARGET_PROMPT: send summary
START_DELAY_SECONDS: [calculated seconds until next 15th at 9am]
DELAY_SECONDS: 2629746
SCHEDULE_TYPE: recurring"""

            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=f"Current UTC time: {datetime.now(timezone.utc).isoformat()}. Parse: {prompt}")
            ]
            
            result = await ZairaSettings.llm.ainvoke(input=messages)
            if hasattr(result, 'content'):
                return result.content
            return str(result)
            
        except Exception as e:
            return f"Error parsing monthly schedule: {str(e)}"


class ParseGenericScheduleTool(BaseTool):
    """Tool for parsing generic schedule patterns"""
    name: str = "parse_generic_schedule"
    description: str = "Parse generic schedule patterns like 'in X minutes' or immediate tasks, or any other schedule format"
    
    def _run(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, prompt: str, state: Optional[SupervisorTaskState] = None) -> str:
        """Parse generic schedule patterns using LLM"""
        try:
            system_prompt = """You are a schedule parser for generic scheduling patterns. Analyze the user input and extract:
1. The action/task to be performed
2. The delay before execution (if any)
3. Whether it's a one-time or immediate task

Return ONLY in this exact format:
TARGET_PROMPT: [the action to perform]
DELAY_SECONDS: [delay in seconds as a number, 0 for immediate]
SCHEDULE_TYPE: once

Common patterns:
- "in X minutes/hours/days" = delayed one-time task
- "do X in Y time" = delayed one-time task
- No time specified = immediate task (DELAY_SECONDS: 0)

Examples:
Input: "send email in 30 minutes"
Output: TARGET_PROMPT: send email
DELAY_SECONDS: 1800
SCHEDULE_TYPE: once

Input: "check status in 2 hours"
Output: TARGET_PROMPT: check status
DELAY_SECONDS: 7200
SCHEDULE_TYPE: once

Input: "run backup now"
Output: TARGET_PROMPT: run backup now
DELAY_SECONDS: 0
SCHEDULE_TYPE: once"""

            messages = [
                SystemMessage(content=system_prompt),
                HumanMessage(content=prompt)
            ]
            
            result = await ZairaSettings.llm.ainvoke(input=messages)
            if hasattr(result, 'content'):
                return result.content
            return str(result)
            
        except Exception as e:
            return f"Error parsing generic schedule: {str(e)}"
    
    def _convert_to_seconds(self, interval: int, unit: str) -> float:
        """Convert time interval to seconds"""
        multipliers = {
            'second': 1,
            'minute': 60,
            'hour': 3600,
            'day': 86400
        }
        return float(interval * multipliers.get(unit, 1))