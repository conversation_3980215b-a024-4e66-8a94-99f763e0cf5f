from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

from imports import *
import pytest
import asyncio
import tempfile
import base64
from pathlib import Path
from unittest.mock import patch, MagicMock
from managers.manager_multimodal import MultimodalManager
from tools.tool_document_processor import DocumentProcessorTool
from tools.tool_multimodal_retrieval import MultimodalRetrievalTool

class TestMultimodalProcessing:
    """Test suite for multimodal document processing functionality."""
    
    def setup_method(self):
        """Set up test environment."""
        self.multimodal_manager = MultimodalManager.get_instance()
        self.document_processor = DocumentProcessorTool()
        self.retrieval_tool = MultimodalRetrievalTool()
    
    def test_multimodal_manager_singleton(self):
        """Test that MultimodalManager follows singleton pattern."""
        manager1 = MultimodalManager.get_instance()
        manager2 = MultimodalManager.get_instance()
        assert manager1 is manager2
    
    @pytest.mark.asyncio
    async def test_multimodal_manager_setup(self):
        """Test MultimodalManager setup process."""
        await MultimodalManager.setup()
        assert self.multimodal_manager._initialized is True
    
    def test_get_images_base64(self):
        """Test extraction of base64 images from elements."""
        # Create mock CompositeElement with embedded image
        mock_orig_element = MagicMock()
        mock_orig_element.metadata.image_base64 = "test_base64_data"
        
        mock_metadata = MagicMock()
        mock_metadata.orig_elements = [mock_orig_element]
        
        mock_element = MagicMock()
        mock_element.metadata = mock_metadata
        
        # Mock the type checking
        with patch('builtins.str') as mock_str:
            mock_str.side_effect = lambda x: {
                type(mock_element): "CompositeElement",
                type(mock_orig_element): "Image"
            }.get(x, str(x))
            
            elements = [mock_element]
            images = MultimodalManager._get_images_base64(elements)
            
            assert len(images) == 1
            assert images[0] == "test_base64_data"
    
    def test_is_numeric_validation(self):
        """Test numeric value validation."""
        assert MultimodalManager._is_numeric("123") is True
        assert MultimodalManager._is_numeric("123.45") is True
        assert MultimodalManager._is_numeric("$123.45") is True
        assert MultimodalManager._is_numeric("123,456") is True
        assert MultimodalManager._is_numeric("50%") is True
        assert MultimodalManager._is_numeric("abc") is False
        assert MultimodalManager._is_numeric("") is False
    
    def test_is_date_validation(self):
        """Test date value validation."""
        assert MultimodalManager._is_date("2023-12-25") is True
        assert MultimodalManager._is_date("12/25/2023") is True
        assert MultimodalManager._is_date("12-25-2023") is True
        assert MultimodalManager._is_date("not_a_date") is False
        assert MultimodalManager._is_date("123") is False
    
    def test_structured_table_to_markdown(self):
        """Test conversion of structured table data to markdown."""
        table_data = [
            ["Name", "Age", "City"],
            ["John", "30", "New York"],
            ["Jane", "25", "Los Angeles"]
        ]
        
        markdown = MultimodalManager._structured_table_to_markdown(table_data)
        
        expected_lines = [
            "| Name | Age | City |",
            "| --- | --- | --- |",
            "| John | 30 | New York |",
            "| Jane | 25 | Los Angeles |"
        ]
        
        for line in expected_lines:
            assert line in markdown
    
    def test_structured_table_to_markdown_empty(self):
        """Test markdown conversion with empty table data."""
        assert MultimodalManager._structured_table_to_markdown([]) == ""
        assert MultimodalManager._structured_table_to_markdown([[]]) == ""
    
    def test_html_to_markdown_conversion(self):
        """Test HTML table to markdown conversion."""
        html_content = """
        <table>
            <tr>
                <th>Header 1</th>
                <th>Header 2</th>
            </tr>
            <tr>
                <td>Data 1</td>
                <td>Data 2</td>
            </tr>
        </table>
        """
        
        markdown = MultimodalManager._html_to_markdown(html_content)
        
        # Should contain table structure
        assert "| Header 1 | Header 2 |" in markdown
        assert "| --- | --- |" in markdown
        assert "| Data 1 | Data 2 |" in markdown
    
    def test_get_surrounding_context(self):
        """Test extraction of surrounding context for elements."""
        all_elements = [
            {"type": "Title", "text": "Chapter 1"},
            {"type": "NarrativeText", "text": "This is the introduction."},
            {"type": "Image", "text": "Figure 1"},
            {"type": "NarrativeText", "text": "This explains the figure."},
            {"type": "Table", "text": "Data table"}
        ]
        
        # Test context for image (index 2)
        context = MultimodalManager._get_surrounding_context(all_elements, 2, context_window=1)
        
        # Should include preceding and following narrative text
        assert "This is the introduction." in context
        assert "This explains the figure." in context
        assert "Chapter 1" not in context  # Title should be excluded from context
    
    @pytest.mark.asyncio
    async def test_extract_table_key_info(self):
        """Test extraction of key information from table markdown."""
        table_markdown = """
        | Name | Age | Score |
        | --- | --- | --- |
        | John | 30 | 85.5 |
        | Jane | 25 | 92.0 |
        | Bob | 35 | 78.2 |
        """
        
        key_info = await MultimodalManager._extract_table_key_info(table_markdown)
        
        assert key_info["num_columns"] == 3
        assert key_info["num_rows"] == 3
        assert key_info["total_cells"] == 9
        assert key_info["has_headers"] is True
        assert key_info["headers"] == ["Name", "Age", "Score"]
    
    @pytest.mark.asyncio
    async def test_infer_column_types(self):
        """Test column type inference."""
        data_rows = [
            ["John", "30", "85.5"],
            ["Jane", "25", "92.0"],
            ["Bob", "35", "78.2"]
        ]
        headers = ["Name", "Age", "Score"]
        
        column_types = await MultimodalManager._infer_column_types(data_rows, headers)
        
        assert column_types["Name"] == "text"
        assert column_types["Age"] == "numeric"
        assert column_types["Score"] == "numeric"
    
    @pytest.mark.asyncio
    async def test_document_processor_tool_validation(self):
        """Test document processor tool input validation."""
        # Test with non-existent file
        result = await self.document_processor._arun(
            file_path="/non/existent/file.pdf",
            extract_multimodal=True
        )
        
        assert "Error: File not found" in result
    
    @pytest.mark.asyncio
    async def test_multimodal_retrieval_tool_error_handling(self):
        """Test multimodal retrieval tool error handling."""
        # Mock retrieval manager to raise exception
        with patch('managers.manager_retrieval.RetrievalManager.get_instance') as mock_manager:
            mock_manager.side_effect = Exception("Test error")
            
            result = await self.retrieval_tool._arun(
                query="test query",
                content_types=["text", "image"],
                max_results=5
            )
            
            assert "Error performing multimodal search" in result
    
    def test_multimodal_search_input_validation(self):
        """Test multimodal search input validation."""
        from tools.tool_multimodal_retrieval import MultimodalSearchInput
        
        # Test valid input
        valid_input = MultimodalSearchInput(
            query="test query",
            content_types=["text", "image"],
            max_results=10
        )
        
        assert valid_input.query == "test query"
        assert valid_input.content_types == ["text", "image"]
        assert valid_input.max_results == 10
        assert valid_input.include_assets is True
        assert valid_input.filter_by_doc is None
    
    def test_document_processing_input_validation(self):
        """Test document processing input validation."""
        from tools.tool_document_processor import DocumentProcessingInput
        
        # Test valid input
        valid_input = DocumentProcessingInput(
            file_path="/path/to/document.pdf",
            extract_multimodal=True,
            store_in_vector_db=True
        )
        
        assert valid_input.file_path == "/path/to/document.pdf"
        assert valid_input.extract_multimodal is True
        assert valid_input.store_in_vector_db is True
        assert valid_input.generate_summaries is True
        assert valid_input.doc_id is None
    
    @pytest.mark.asyncio
    async def test_save_image_asset_functionality(self):
        """Test image asset saving functionality."""
        # Create temporary image data
        test_image_data = b"fake_image_data_for_testing"
        doc_id = "test_doc_123"
        element_id = "test_element_456"
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Mock BASE_DIR to use temp directory
            with patch('managers.manager_multimodal.BASE_DIR') as mock_base_dir:
                mock_base_dir.return_value = Path(temp_dir)
                
                asset_path = await MultimodalManager._save_image_asset(
                    test_image_data, doc_id, element_id
                )
                
                # Verify file was created
                assert asset_path
                assert Path(asset_path).exists()
                
                # Verify file content
                with open(asset_path, 'rb') as f:
                    saved_data = f.read()
                    assert saved_data == test_image_data
    
    @pytest.mark.asyncio
    async def test_save_base64_image_functionality(self):
        """Test base64 image saving functionality."""
        # Create test base64 data
        test_image_data = b"fake_image_data_for_testing"
        test_base64 = base64.b64encode(test_image_data).decode('utf-8')
        doc_id = "test_doc_123"
        element_id = "test_element_456"
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Mock BASE_DIR to use temp directory
            with patch('managers.manager_multimodal.BASE_DIR') as mock_base_dir:
                mock_base_dir.return_value = Path(temp_dir)
                
                asset_path = await MultimodalManager._save_base64_image(
                    test_base64, doc_id, element_id
                )
                
                # Verify file was created
                assert asset_path
                assert Path(asset_path).exists()
                
                # Verify file content matches original
                with open(asset_path, 'rb') as f:
                    saved_data = f.read()
                    assert saved_data == test_image_data
    
    @pytest.mark.asyncio
    async def test_cleanup_assets(self):
        """Test asset cleanup functionality."""
        doc_id = "test_doc_cleanup"
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Mock BASE_DIR to use temp directory
            with patch('managers.manager_multimodal.BASE_DIR') as mock_base_dir:
                mock_base_dir.return_value = Path(temp_dir)
                
                # Create test assets directory
                assets_dir = Path(temp_dir) / "assets" / "documents" / doc_id
                assets_dir.mkdir(parents=True, exist_ok=True)
                
                # Create test file
                test_file = assets_dir / "test_image.png"
                test_file.write_bytes(b"test data")
                
                # Verify directory exists
                assert assets_dir.exists()
                assert test_file.exists()
                
                # Cleanup assets
                await MultimodalManager.cleanup_assets(doc_id)
                
                # Verify directory was removed
                assert not assets_dir.exists()
                assert not test_file.exists()
    
    @pytest.mark.asyncio
    async def test_get_asset_path(self):
        """Test asset path retrieval."""
        doc_id = "test_doc_asset_path"
        element_id = "test_element_123"
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Mock BASE_DIR to use temp directory
            with patch('managers.manager_multimodal.BASE_DIR') as mock_base_dir:
                mock_base_dir.return_value = Path(temp_dir)
                
                # Create test assets directory
                assets_dir = Path(temp_dir) / "assets" / "documents" / doc_id
                assets_dir.mkdir(parents=True, exist_ok=True)
                
                # Create test asset file
                test_file = assets_dir / f"{element_id}_hash123.png"
                test_file.write_bytes(b"test data")
                
                # Test asset path retrieval
                asset_path = await MultimodalManager.get_asset_path(doc_id, element_id)
                
                assert asset_path == str(test_file)
                
                # Test with non-existent asset
                missing_path = await MultimodalManager.get_asset_path(doc_id, "missing_element")
                assert missing_path is None

class TestMultimodalIntegration:
    """Integration tests for multimodal functionality."""
    
    @pytest.mark.asyncio
    async def test_end_to_end_processing_workflow(self):
        """Test complete multimodal processing workflow."""
        # This test would require actual document files
        # For now, we'll test the workflow structure
        pass
    
    def test_multimodal_metadata_structure(self):
        """Test multimodal metadata structure consistency."""
        # Test that metadata structures are consistent across components
        expected_image_metadata = {
            "content_type", "image_index", "element_id", "asset_path", "has_asset"
        }
        
        expected_table_metadata = {
            "content_type", "table_index", "element_id", "has_structure", 
            "num_columns", "num_rows"
        }
        
        expected_text_metadata = {
            "chunk_index", "chunk_id", "has_images", "has_tables", 
            "image_count", "table_count"
        }
        
        # These would be tested in actual integration scenarios
        assert expected_image_metadata
        assert expected_table_metadata
        assert expected_text_metadata

if __name__ == "__main__":
    pytest.main([__file__, "-v"])