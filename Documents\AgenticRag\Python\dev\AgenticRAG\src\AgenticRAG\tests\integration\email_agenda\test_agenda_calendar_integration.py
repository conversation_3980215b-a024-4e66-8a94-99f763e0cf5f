"""
Integration test for Agenda and Calendar functionality
This test validates agenda planning and calendar integration workflows
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
import asyncio
from uuid import uuid4, UUID
from unittest.mock import AsyncMock, MagicMock, patch

from managers.manager_supervisors import SupervisorManager
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import ZairaUser, PERMISSION_LEVELS
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest


@pytest.mark.integration
@pytest.mark.asyncio
class TestAgendaCalendarIntegration:
    """Integration tests for Agenda and Calendar functionality"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.test_session_guid = str(uuid4())
        self.test_scheduled_guid = str(uuid4())
    
    async def _create_test_user(self):
        """Create a test user for agenda testing"""
        user_manager = ZairaUserManager.get_instance()
        
        # Create test user
        test_user = ZairaUser(
            username="admin_user",
            rank=PERMISSION_LEVELS.ADMIN,
            guid=UUID(self.test_user_guid),
            device_guid=uuid4()
        )
        test_user.email = "<EMAIL>"
        
        # Add to manager
        await user_manager.add_user(test_user.username, test_user.rank, test_user.GUID, test_user.DeviceGUID)
        
        # Create a mock task for human-in-the-loop interactions
        mock_task = MagicMock(spec=LongRunningZairaRequest)
        mock_task.request_human_in_the_loop = AsyncMock()
        test_user.my_requests[self.test_scheduled_guid] = mock_task
        
        return test_user
    
    async def test_create_supervisor_agenda_planner_integration(self):
        """Test full agenda planner supervisor creation and integration"""
        from tasks.processing.task_agenda_planner import create_supervisor_agenda_planner
        
        with patch('tasks.outputs.output_tasks.task_out_agenda.get_calendar_tools') as mock_get_tools:
            # Mock calendar tools
            mock_tools = [MagicMock(name="calendar_tool_1"), MagicMock(name="calendar_tool_2")]
            mock_get_tools.return_value = mock_tools
            
            with patch('tasks.processing.task_agenda_planner.SupervisorManager') as mock_sm:
                # Mock task registration (class method)
                mock_task = MagicMock()
                mock_task.name = "agenda_expert"
                mock_sm.register_task.return_value = mock_task
                
                # Mock supervisor registration and chaining (class method)
                mock_supervisor = MagicMock()
                mock_supervisor.name = "agenda_supervisor"
                mock_supervisor.add_task.return_value = mock_supervisor
                mock_supervisor.compile.return_value = mock_supervisor
                mock_sm.register_supervisor.return_value = mock_supervisor
                
                # Create supervisor
                result = await create_supervisor_agenda_planner()
                
                # Verify supervisor creation workflow
                assert result is not None
                mock_get_tools.assert_called_once()
                mock_sm.register_task.assert_called_once()
                mock_sm.register_supervisor.assert_called_once()
                mock_supervisor.add_task.assert_called_once_with(mock_task, priority=1)
                mock_supervisor.compile.assert_called_once()
                
                # Verify task was created with correct tools
                task_call_args = mock_sm.register_task.call_args[0][0]
                assert task_call_args.name == "agenda_expert"
                assert len(task_call_args.get_tools()) == len(mock_tools) + 2  # 2 planning tools + calendar tools
    
    async def test_calendar_tools_oauth_integration(self):
        """Test calendar tools integration with OAuth2 system"""
        from tasks.outputs.output_tasks.task_out_agenda import get_calendar_tools
        
        # Test case 1: OAuth token available
        with patch('tasks.outputs.output_tasks.task_out_agenda.OAuth2Verifier') as mock_oauth:
            mock_instance = MagicMock()
            mock_oauth.get_instance.return_value = mock_instance
            mock_oauth.get_token = AsyncMock(return_value="valid_oauth_token")
            
            # Mock the gcalendar app configuration
            mock_app = MagicMock()
            mock_app.client_id = "test_client_id"
            mock_app.client_secret = "test_client_secret"
            mock_app.token_url = "https://oauth2.googleapis.com/token"
            mock_app.oauth_scopes = ["https://www.googleapis.com/auth/calendar"]
            mock_instance.apps = {"gcalendar": mock_app}
            
            # Mock calendar imports availability
            with patch('tasks.outputs.output_tasks.task_out_agenda.CALENDAR_IMPORTS_AVAILABLE', True):
                with patch('tasks.outputs.output_tasks.task_out_agenda.CalendarToolkit') as mock_toolkit:
                    with patch('tasks.outputs.output_tasks.task_out_agenda.build_resource_service'):
                        with patch('tasks.outputs.output_tasks.task_out_agenda.Credentials'):
                            # Create mock calendar tools
                            mock_create_tool = MagicMock()
                            mock_create_tool.name = "CalendarCreateEvent"
                            mock_search_tool = MagicMock()
                            mock_search_tool.name = "CalendarSearchEvents"
                            
                            mock_toolkit_instance = MagicMock()
                            mock_toolkit_instance.get_tools.return_value = [mock_create_tool, mock_search_tool]
                            mock_toolkit.return_value = mock_toolkit_instance
                            
                            # Get calendar tools
                            tools = await get_calendar_tools()
                            
                            # Verify tools were created (including custom confirmation tool replacement)
                            assert len(tools) == 2
                            
                            # Verify OAuth was checked (multiple calls with different parameters)
                            assert mock_oauth.get_token.call_count >= 2
                            mock_oauth.get_token.assert_any_call("gcalendar")
                            mock_oauth.get_token.assert_any_call("gcalendar", "refresh_token")
        
        # Test case 2: No OAuth token available
        with patch('tasks.outputs.output_tasks.task_out_agenda.OAuth2Verifier') as mock_oauth:
            mock_instance = MagicMock()
            mock_oauth.get_instance.return_value = mock_instance
            mock_oauth.get_token = AsyncMock(return_value=None)
            
            # Get calendar tools
            tools = await get_calendar_tools()
            
            # Verify no tools were created
            assert tools == []
            mock_oauth.get_token.assert_any_call("gcalendar")
    
    async def test_calendar_imports_unavailable_fallback(self):
        """Test behavior when calendar imports are not available"""
        from tasks.outputs.output_tasks.task_out_agenda import get_calendar_tools
        
        # Mock calendar imports as unavailable
        with patch('tasks.outputs.output_tasks.task_out_agenda.CALENDAR_IMPORTS_AVAILABLE', False):
            # Get calendar tools
            tools = await get_calendar_tools()
            
            # Verify no tools were created due to missing imports
            assert tools == []
    
    async def test_format_event_preview_comprehensive(self):
        """Test comprehensive event preview formatting"""
        from tasks.outputs.output_tasks.task_out_agenda import format_event_preview
        
        # Test case 1: Complete event details
        complete_event = {
            'summary': 'Annual Strategy Meeting',
            'start': {'dateTime': '2024-02-15T09:00:00'},
            'end': {'dateTime': '2024-02-15T17:00:00'},
            'location': 'Executive Conference Room',
            'description': 'Comprehensive annual strategy planning session for 2024',
            'attendees': [
                {'email': '<EMAIL>', 'displayName': 'CEO'},
                {'email': '<EMAIL>', 'displayName': 'CTO'},
                {'email': '<EMAIL>'}
            ]
        }
        
        preview = format_event_preview(complete_event)
        
        # Verify all components are included
        assert 'Annual Strategy Meeting' in preview
        assert 'Executive Conference Room' in preview
        assert '<EMAIL>' in preview
        assert '<EMAIL>' in preview
        assert '<EMAIL>' in preview
        
        # Test case 2: Minimal event details
        minimal_event = {
            'summary': 'Quick Sync',
            'start': {'dateTime': '2024-02-16T10:00:00'},
            'end': {'dateTime': '2024-02-16T10:30:00'}
        }
        
        minimal_preview = format_event_preview(minimal_event)
        
        # Verify basic components are handled
        assert 'Quick Sync' in minimal_preview
        assert len(minimal_preview) > 0
        
        # Test case 3: Event with missing fields
        partial_event = {
            'summary': 'Team Meeting',
            'location': 'Room 101',
            'attendees': [{'email': '<EMAIL>'}]
        }
        
        partial_preview = format_event_preview(partial_event)
        
        # Verify partial event is handled gracefully
        assert 'Team Meeting' in partial_preview
        assert 'Room 101' in partial_preview
        assert '<EMAIL>' in partial_preview
    
    async def test_agenda_supervisor_workflow_integration(self):
        """Test complete agenda supervisor workflow integration"""
        from tasks.processing.task_agenda_planner import create_supervisor_agenda_planner
        
        test_user = await self._create_test_user()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = self.test_user_guid
        mock_state.scheduled_guid = self.test_scheduled_guid
        mock_state.sections = {}
        
        with patch('tasks.outputs.output_tasks.task_out_agenda.get_calendar_tools') as mock_get_tools:
            # Create mock calendar tools
            mock_calendar_tool = MagicMock()
            mock_calendar_tool.name = "calendar_access_tool"
            mock_calendar_tool._arun = AsyncMock(return_value="Calendar events retrieved")
            
            mock_analyzer_tool = MagicMock()
            mock_analyzer_tool.name = "agenda_analyzer_tool"
            mock_analyzer_tool._arun = AsyncMock(return_value="Agenda analysis complete")
            
            mock_creator_tool = MagicMock()
            mock_creator_tool.name = "calendar_event_creator_tool"
            mock_creator_tool._arun = AsyncMock(return_value="Event created successfully")
            
            mock_get_tools.return_value = [mock_calendar_tool, mock_analyzer_tool, mock_creator_tool]
            
            with patch('tasks.processing.task_agenda_planner.SupervisorManager') as mock_sm:
                # Mock supervisor manager and task
                mock_instance = MagicMock()
                mock_sm.get_instance.return_value = mock_instance
                
                # Create a real-looking task that can execute tools
                class MockTask:
                    def __init__(self, name, tools, prompt):
                        self.name = name
                        self.tools = tools
                        self.prompt = prompt
                    
                    async def execute_tools(self, state):
                        # Simulate tool execution
                        for tool in self.tools:
                            result = await tool._arun("test_input", state=state)
                            state.sections[f'{tool.name}_result'] = result
                        return "Task execution complete"
                
                mock_task = MockTask("agenda_expert", [mock_calendar_tool, mock_analyzer_tool, mock_creator_tool], "agenda_prompt")
                mock_instance.register_task.return_value = mock_task
                
                # Mock supervisor
                mock_supervisor = MagicMock()
                mock_supervisor.name = "agenda_supervisor"
                mock_supervisor.add_task.return_value = mock_supervisor
                mock_supervisor.compile.return_value = mock_supervisor
                mock_supervisor.execute = AsyncMock(return_value="Supervisor execution complete")
                mock_instance.register_supervisor.return_value = mock_supervisor
                
                # Create and test supervisor
                supervisor = await create_supervisor_agenda_planner()
                
                # Simulate workflow execution
                with patch('tasks.processing.task_agenda_planner.ZairaUserManager') as mock_zum:
                    mock_zum.get_instance.return_value.find_user = AsyncMock(return_value=test_user)
                    
                    # Execute task tools to simulate workflow
                    await mock_task.execute_tools(mock_state)
                    
                    # Verify tools were executed
                    mock_calendar_tool._arun.assert_called_once()
                    mock_analyzer_tool._arun.assert_called_once()
                    mock_creator_tool._arun.assert_called_once()
                    
                    # Verify state was updated with tool results
                    assert 'calendar_access_tool_result' in mock_state.sections
                    assert 'agenda_analyzer_tool_result' in mock_state.sections
                    assert 'calendar_event_creator_tool_result' in mock_state.sections
                    
                    # Verify results
                    assert mock_state.sections['calendar_access_tool_result'] == "Calendar events retrieved"
                    assert mock_state.sections['agenda_analyzer_tool_result'] == "Agenda analysis complete"
                    assert mock_state.sections['calendar_event_creator_tool_result'] == "Event created successfully"
    
    async def test_agenda_calendar_error_handling_integration(self):
        """Test error handling in agenda calendar integration"""
        from tasks.outputs.output_tasks.task_out_agenda import get_calendar_tools
        
        # Test OAuth error handling
        with patch('tasks.outputs.output_tasks.task_out_agenda.OAuth2Verifier') as mock_oauth:
            mock_instance = MagicMock()
            mock_oauth.get_instance.return_value = mock_instance
            mock_instance.get_token = AsyncMock(side_effect=Exception("OAuth error"))
            
            # Get calendar tools - should handle error gracefully
            tools = await get_calendar_tools()
            
            # Verify error was handled and empty list returned
            assert tools == []
    
    async def test_agenda_tool_execution_with_state_persistence(self):
        """Test agenda tool execution with proper state persistence"""
        from tasks.outputs.output_tasks.task_out_agenda import format_event_preview
        
        test_user = await self._create_test_user()
        
        # Create mock state with initial data
        mock_state = MagicMock()
        mock_state.user_guid = self.test_user_guid
        mock_state.scheduled_guid = self.test_scheduled_guid
        mock_state.sections = {
            'initial_context': 'user_meeting_request',
            'workflow_stage': 'agenda_planning'
        }
        
        # Simulate agenda tool workflow
        calendar_events = [
            {
                'summary': 'Existing Meeting',
                'start': {'dateTime': '2024-02-15T14:00:00'},
                'end': {'dateTime': '2024-02-15T15:00:00'},
                'location': 'Room A',
                'attendees': [{'email': '<EMAIL>'}]
            },
            {
                'summary': 'Project Review',
                'start': {'dateTime': '2024-02-16T10:00:00'},
                'end': {'dateTime': '2024-02-16T11:00:00'},
                'location': 'Room B',
                'attendees': [{'email': '<EMAIL>'}]
            }
        ]
        
        # Process calendar events
        event_previews = []
        for event in calendar_events:
            preview = format_event_preview(event)
            event_previews.append(preview)
        
        # Update state with agenda processing results
        mock_state.sections.update({
            'calendar_events': calendar_events,
            'event_previews': event_previews,
            'agenda_analysis': {
                'total_events': len(calendar_events),
                'conflicts_detected': False,
                'availability_windows': ['2024-02-15T09:00:00-14:00:00', '2024-02-16T11:00:00-17:00:00']
            },
            'workflow_stage': 'agenda_complete'
        })
        
        # Verify state consistency
        assert mock_state.sections['initial_context'] == 'user_meeting_request'
        assert mock_state.sections['workflow_stage'] == 'agenda_complete'
        assert len(mock_state.sections['calendar_events']) == 2
        assert len(mock_state.sections['event_previews']) == 2
        assert mock_state.sections['agenda_analysis']['total_events'] == 2
        assert mock_state.sections['agenda_analysis']['conflicts_detected'] == False
        
        # Verify event previews contain expected content
        combined_previews = ' '.join(event_previews)
        assert 'Existing Meeting' in combined_previews
        assert 'Project Review' in combined_previews
        assert 'Room A' in combined_previews
        assert 'Room B' in combined_previews
        assert '<EMAIL>' in combined_previews
        assert '<EMAIL>' in combined_previews
    
    async def test_calendar_tool_creation_patterns(self):
        """Test different calendar tool creation patterns"""
        from tasks.outputs.output_tasks.task_out_agenda import get_calendar_tools
        
        # Test successful tool creation with all imports available
        with patch('tasks.outputs.output_tasks.task_out_agenda.CALENDAR_IMPORTS_AVAILABLE', True):
            with patch('tasks.outputs.output_tasks.task_out_agenda.OAuth2Verifier') as mock_oauth:
                mock_instance = MagicMock()
                mock_oauth.get_instance.return_value = mock_instance
                mock_oauth.get_token = AsyncMock(return_value="valid_token")
                
                # Mock the gcalendar app configuration
                mock_app = MagicMock()
                mock_app.client_id = "test_client_id"
                mock_app.client_secret = "test_client_secret"
                mock_app.token_url = "https://oauth2.googleapis.com/token"
                mock_app.oauth_scopes = ["https://www.googleapis.com/auth/calendar"]
                mock_instance.apps = {"gcalendar": mock_app}
                
                # Mock calendar toolkit components
                with patch('tasks.outputs.output_tasks.task_out_agenda.CalendarToolkit') as mock_toolkit:
                    with patch('tasks.outputs.output_tasks.task_out_agenda.build_resource_service'):
                        with patch('tasks.outputs.output_tasks.task_out_agenda.Credentials'):
                            # Mock toolkit and tools
                            mock_create_tool = MagicMock()
                            mock_create_tool.name = "CalendarCreateEvent"
                            mock_search_tool = MagicMock()  
                            mock_search_tool.name = "CalendarSearchEvents"
                            mock_update_tool = MagicMock()
                            mock_update_tool.name = "CalendarUpdateEvent"
                            
                            mock_toolkit_instance = MagicMock()
                            mock_toolkit_instance.get_tools.return_value = [mock_create_tool, mock_search_tool, mock_update_tool]
                            mock_toolkit.return_value = mock_toolkit_instance
                            
                            # Get tools
                            tools = await get_calendar_tools()
                            
                            # Verify correct tool creation pattern  
                            assert len(tools) == 3
                            
                            # Verify toolkit was instantiated
                            mock_toolkit.assert_called_once()
        
        # Test pattern when imports are available but OAuth fails
        with patch('tasks.outputs.output_tasks.task_out_agenda.CALENDAR_IMPORTS_AVAILABLE', True):
            with patch('tasks.outputs.output_tasks.task_out_agenda.OAuth2Verifier') as mock_oauth:
                mock_instance = MagicMock()
                mock_oauth.get_instance.return_value = mock_instance
                mock_instance.get_token = AsyncMock(return_value=None)
                
                # Get tools
                tools = await get_calendar_tools()
                
                # Verify empty list when OAuth fails
                assert tools == []
                mock_instance.get_token.assert_called_with("gcalendar")
        
        # Test pattern when imports are not available
        with patch('tasks.outputs.output_tasks.task_out_agenda.CALENDAR_IMPORTS_AVAILABLE', False):
            # Get tools
            tools = await get_calendar_tools()
            
            # Verify empty list when imports unavailable
            assert tools == []