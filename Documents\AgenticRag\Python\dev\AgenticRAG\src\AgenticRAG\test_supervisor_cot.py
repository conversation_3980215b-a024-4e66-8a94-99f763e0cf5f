import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

async def test_supervisor_cot():
    print("Starting CoT supervisor test...")
    
    # Import minimal setup
    from etc.ZairaSettings import ZairaSettings
    ZairaSettings.IsDebugMode = True
    
    # Set up minimal LLM
    from langchain_openai import ChatOpenAI
    llm = ChatOpenAI(model_name="gpt-4o-mini", temperature=0.5, timeout=30)
    ZairaSettings.llm = llm
    
    # Setup supervisor manager
    from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor_ChainOfThought
    await SupervisorManager.setup()
    print("SupervisorManager setup complete")
    
    try:
        print("About to create SupervisorSupervisor_ChainOfThought...")
        supervisor = SupervisorSupervisor_ChainOfThought(
            name="test_supervisor",
            prompt_id="Top_Supervisor_CoT_Prompt"
        )
        print(f"SupervisorSupervisor_ChainOfThought created successfully: {supervisor}")
        
        print("About to register supervisor...")
        registered = SupervisorManager.register_supervisor(supervisor)
        print(f"Supervisor registered successfully: {registered}")
        
    except Exception as e:
        print(f"Error creating SupervisorSupervisor_ChainOfThought: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Running test_supervisor_cot...")
    asyncio.run(test_supervisor_cot())
    print("Test completed")