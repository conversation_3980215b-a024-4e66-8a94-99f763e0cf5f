from imports import *
from langchain_core.tools import BaseTool
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from pathlib import Path

class DebugMultimodalInput(BaseModel):
    """Input for debugging multimodal extraction."""
    file_path: str = Field(..., description="Path to the file to debug")
    use_basic_partition: bool = Field(default=False, description="Use basic partition instead of PDF-specific")

class DebugMultimodalTool(BaseTool):
    """Tool for debugging multimodal extraction issues."""
    
    name: str = "debug_multimodal"
    description: str = """
    Debug multimodal extraction to understand what's being extracted from documents.
    
    This tool:
    - Tests different partitioning strategies
    - Shows detailed element information
    - Reveals metadata content
    - Identifies extraction issues
    
    Use this tool to diagnose why multimodal extraction isn't working as expected.
    """
    
    args_schema = DebugMultimodalInput
    
    def _run(self, file_path: str, use_basic_partition: bool = False) -> str:
        raise NotImplementedError("Use async version")
    
    async def _arun(self, file_path: str, use_basic_partition: bool = False) -> str:
        """Debug multimodal extraction for a specific file."""
        try:
            if not Path(file_path).exists():
                return f"Error: File not found at {file_path}"
            
            output = []
            output.append(f"Debugging Multimodal Extraction")
            output.append(f"File: {file_path}")
            output.append(f"=" * 60)
            
            # Test basic partition first
            output.append(f"\n1. BASIC PARTITION TEST")
            output.append(f"-" * 30)
            
            try:
                from unstructured.partition.auto import partition
                basic_elements = partition(filename=file_path)
                
                output.append(f"Basic partition successful!")
                output.append(f"Found {len(basic_elements)} elements")
                
                element_types = {}
                for element in basic_elements:
                    element_type = type(element).__name__
                    element_types[element_type] = element_types.get(element_type, 0) + 1
                
                output.append(f"Element types: {element_types}")
                
                # Show first few elements
                output.append(f"\nFirst 3 elements:")
                for i, element in enumerate(basic_elements[:3]):
                    output.append(f"  {i+1}. {type(element).__name__}: {str(element)[:100]}...")
                
            except Exception as e:
                output.append(f"Basic partition failed: {str(e)}")
            
            # Test PDF-specific partition if it's a PDF
            if Path(file_path).suffix.lower() == '.pdf':
                output.append(f"\n2. PDF-SPECIFIC PARTITION TEST")
                output.append(f"-" * 30)
                
                try:
                    from unstructured.partition.pdf import partition_pdf
                    pdf_elements = partition_pdf(
                        filename=file_path,
                        strategy="hi_res",
                        infer_table_structure=True,
                        extract_image_block_types=["Image", "Table"],
                        extract_image_block_to_payload=True
                    )
                    
                    output.append(f"PDF partition successful!")
                    output.append(f"Found {len(pdf_elements)} elements")
                    
                    element_types = {}
                    for element in pdf_elements:
                        element_type = type(element).__name__
                        element_types[element_type] = element_types.get(element_type, 0) + 1
                    
                    output.append(f"Element types: {element_types}")
                    
                    # Check for multimodal content
                    images_found = 0
                    tables_found = 0
                    base64_images = 0
                    
                    output.append(f"\nDetailed analysis:")
                    for i, element in enumerate(pdf_elements):
                        element_type = type(element).__name__
                        
                        if "Image" in element_type:
                            images_found += 1
                            output.append(f"  {i+1}. Image element found!")
                            if hasattr(element, 'metadata'):
                                if hasattr(element.metadata, 'image_base64') and element.metadata.image_base64:
                                    base64_images += 1
                                    output.append(f"      - Has base64 data: {len(element.metadata.image_base64)} chars")
                                else:
                                    output.append(f"      - No base64 data")
                        
                        elif "Table" in element_type:
                            tables_found += 1
                            output.append(f"  {i+1}. Table element found!")
                            if hasattr(element, 'metadata'):
                                if hasattr(element.metadata, 'text_as_html'):
                                    output.append(f"      - Has HTML: {element.metadata.text_as_html[:100]}...")
                        
                        elif "CompositeElement" in element_type:
                            output.append(f"  {i+1}. CompositeElement: {str(element)[:100]}...")
                            
                            # Check for embedded content
                            if hasattr(element, 'metadata') and hasattr(element.metadata, 'orig_elements'):
                                orig_count = len(element.metadata.orig_elements)
                                output.append(f"      - Has {orig_count} original elements")
                                
                                for j, orig_el in enumerate(element.metadata.orig_elements):
                                    orig_type = type(orig_el).__name__
                                    if "Image" in orig_type:
                                        output.append(f"        - Original element {j+1}: Image")
                                        if hasattr(orig_el, 'metadata') and hasattr(orig_el.metadata, 'image_base64'):
                                            if orig_el.metadata.image_base64:
                                                base64_images += 1
                                                output.append(f"          - Has base64: {len(orig_el.metadata.image_base64)} chars")
                    
                    output.append(f"\nSummary:")
                    output.append(f"  - Direct Image elements: {images_found}")
                    output.append(f"  - Direct Table elements: {tables_found}")
                    output.append(f"  - Base64 images found: {base64_images}")
                    
                except Exception as e:
                    output.append(f"PDF partition failed: {str(e)}")
                    import traceback
                    output.append(f"Full error: {traceback.format_exc()}")
            
            # Test with different strategy
            output.append(f"\n3. ALTERNATIVE STRATEGY TEST")
            output.append(f"-" * 30)
            
            try:
                from unstructured.partition.auto import partition
                alt_elements = partition(
                    filename=file_path,
                    strategy="fast",  # Try fast strategy
                    infer_table_structure=True
                )
                
                output.append(f"Alternative strategy successful!")
                output.append(f"Found {len(alt_elements)} elements")
                
                # Check for table-like content in text
                table_like_content = []
                for i, element in enumerate(alt_elements):
                    text = str(element)
                    if any(keyword in text.lower() for keyword in ['jan', 'feb', 'maart', 'april', 'mei', 'juni']):
                        table_like_content.append((i, text))
                
                if table_like_content:
                    output.append(f"Found {len(table_like_content)} elements with table-like content:")
                    for i, (idx, text) in enumerate(table_like_content):
                        output.append(f"  {i+1}. Element {idx}: {text[:200]}...")
                
            except Exception as e:
                output.append(f"Alternative strategy failed: {str(e)}")
            
            # Test what our multimodal manager would extract
            output.append(f"\n4. MULTIMODAL MANAGER TEST")
            output.append(f"-" * 30)
            
            try:
                from managers.manager_multimodal import MultimodalManager
                await MultimodalManager.setup()
                
                multimodal_data = await MultimodalManager.extract_multimodal_elements(file_path)
                
                if "error" in multimodal_data:
                    output.append(f"Multimodal manager failed: {multimodal_data['error']}")
                else:
                    output.append(f"Multimodal manager successful!")
                    output.append(f"Text elements: {len(multimodal_data.get('text_elements', []))}")
                    output.append(f"Images: {len(multimodal_data.get('images', []))}")
                    output.append(f"Tables: {len(multimodal_data.get('tables', []))}")
                    output.append(f"Total elements: {len(multimodal_data.get('all_elements', []))}")
                    
                    # Show some sample content
                    if multimodal_data.get('text_elements'):
                        output.append(f"\nSample text element:")
                        sample_text = multimodal_data['text_elements'][0]
                        output.append(f"  {sample_text.get('text', '')[:200]}...")
                    
                    if multimodal_data.get('images'):
                        output.append(f"\nSample image element:")
                        sample_image = multimodal_data['images'][0]
                        output.append(f"  Summary: {sample_image.get('summary', 'No summary')}")
                        output.append(f"  Has asset: {sample_image.get('has_asset', False)}")
                    
                    if multimodal_data.get('tables'):
                        output.append(f"\nSample table element:")
                        sample_table = multimodal_data['tables'][0]
                        output.append(f"  Summary: {sample_table.get('summary', 'No summary')}")
                        output.append(f"  Markdown: {sample_table.get('markdown', 'No markdown')[:200]}...")
                
            except Exception as e:
                output.append(f"Multimodal manager failed: {str(e)}")
                import traceback
                output.append(f"Full error: {traceback.format_exc()}")
            
            return "\n".join(output)
            
        except Exception as e:
            return f"Debug tool failed: {str(e)}"

# Create tool instance
debug_multimodal_tool = DebugMultimodalTool()