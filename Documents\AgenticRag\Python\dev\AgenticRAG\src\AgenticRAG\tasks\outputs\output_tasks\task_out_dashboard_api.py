from imports import *

# https://langchain-ai.github.io/langgraph/tutorials/workflows/#agent
from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTaskState
from managers.manager_users import ZairaUserManager

class SupervisorTask_Dashboard_API(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        # input_message = state.messages[-1].content if len(state.messages) > 1 else state.messages[0].content
        # user = await ZairaUserManager.find_user(state.user_guid)
        
        # if state.scheduled_guid in user.my_requests:
        #     await user.my_requests[state.scheduled_guid].send_response(input_message)
        #     LogFire.log("OUTPUT", "Dashboard_API:", input_message)
        # else:
        #     print(f"No Dashboard_API task found for user {state.user_guid}")
        pass

async def create_out_task_dashboard_api() -> SupervisorTask_Base:
    return SupervisorManager.register_task(SupervisorTask_Dashboard_API(name="dashboard_api_out", prompt_id="Output_Sender_Dashboard_API"))
