=== DEBUG TRACE STARTED at 2025-08-19 09:19:28.083947 ===
[2025-08-19 09:19:36.638] Traceback (most recent call last):
[2025-08-19 09:19:36.638]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\test_excel_analyzer_manual.py", line 15, in <module>
    [2025-08-19 09:19:36.638] from tasks.processing.task_excel_analyzer import ExcelAnalyzerTool, create_supervisor_excel_analyzer
[2025-08-19 09:19:36.638]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\tasks\processing\task_excel_analyzer.py", line 14, in <module>
    [2025-08-19 09:19:36.639] from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTask_Create_agent, SupervisorTaskState
[2025-08-19 09:19:36.639]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\managers\manager_supervisors.py", line 34, in <module>
    [2025-08-19 09:19:36.639] from managers.manager_users import ZairaUserManager
[2025-08-19 09:19:36.639]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\managers\manager_users.py", line 8, in <module>
    [2025-08-19 09:19:36.640] from userprofiles.ZairaUser import PERMISSION_LEVELS
[2025-08-19 09:19:36.640]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\__init__.py", line 4, in <module>
    [2025-08-19 09:19:36.640] from .ZairaUser import ZairaUser
[2025-08-19 09:19:36.640]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\ZairaUser.py", line 14, in <module>
    [2025-08-19 09:19:36.640] from managers.manager_multimodal import MultimodalManager
[2025-08-19 09:19:36.640]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\managers\manager_multimodal.py", line 10, in <module>
    [2025-08-19 09:19:36.641] from unstructured.partition.pdf import partition_pdf
[2025-08-19 09:19:36.641]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured\partition\pdf.py", line 19, in <module>
    [2025-08-19 09:19:36.641] from unstructured_inference.inference.layout import DocumentLayout
[2025-08-19 09:19:36.641]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured_inference\inference\layout.py", line 18, in <module>
    [2025-08-19 09:19:36.642] from unstructured_inference.models.base import get_model
[2025-08-19 09:19:36.642]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured_inference\models\base.py", line 7, in <module>
    [2025-08-19 09:19:36.643] from unstructured_inference.models.detectron2onnx import (
[2025-08-19 09:19:36.643]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured_inference\models\detectron2onnx.py", line 6, in <module>
    [2025-08-19 09:19:36.644] import onnxruntime
[2025-08-19 09:19:36.644]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\onnxruntime\__init__.py", line 61, in <module>
    [2025-08-19 09:19:36.645] raise import_capi_exception
[2025-08-19 09:19:36.645]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\onnxruntime\__init__.py", line 24, in <module>
    [2025-08-19 09:19:36.645] from onnxruntime.capi._pybind_state import (
[2025-08-19 09:19:36.645]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\onnxruntime\capi\_pybind_state.py", line 32, in <module>
    [2025-08-19 09:19:36.646] from .onnxruntime_pybind11_state import *  # noqa
    [2025-08-19 09:19:36.646] ^[2025-08-19 09:19:36.646] ^[2025-08-19 09:19:36.646] ^[2025-08-19 09:19:36.646] ^[2025-08-19 09:19:36.646] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.647] ^[2025-08-19 09:19:36.648] ^[2025-08-19 09:19:36.648] ^[2025-08-19 09:19:36.648] ^[2025-08-19 09:19:36.648] ^
[2025-08-19 09:19:36.648] ImportError[2025-08-19 09:19:36.648] : [2025-08-19 09:19:36.648] DLL load failed while importing onnxruntime_pybind11_state: A dynamic link library (DLL) initialization routine failed.
