"""
Unit tests for task_out_agenda.py - Agenda output/API operations
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from imports import *
import json
import sys
from os import path as os_path

# Add the parent directory to the sys.path
sys.path.insert(0, os_path.join(os_path.dirname(__file__), '../../../'))

@pytest.mark.unit
@pytest.mark.asyncio
class TestAgendaOutputTask:
    """Unit tests for agenda output/API functionality"""
    
    async def test_agenda_sender_tool_initialization(self):
        """Test AgendaSenderTool initialization"""
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderTool
        
        tool = AgendaSenderTool()
        assert tool.name == "agenda_sender_tool"
        assert tool.description == "Creates calendar events using Google Calendar API with OAuth2 authentication"
    
    async def test_agenda_sender_tool_run_not_implemented(self):
        """Test that sync _run method raises NotImplementedError"""
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderTool
        
        tool = AgendaSenderTool()
        
        with pytest.raises(NotImplementedError):
            tool._run({})
    
    async def test_agenda_sender_tool_approval_cancelled(self):
        """Test agenda creation cancelled due to lack of approval"""
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderTool
        
        tool = AgendaSenderTool()
        
        # Create mock state with approval set to False
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {"agenda_approved": False}
        
        result = await tool._arun({}, state=mock_state)
        
        assert "cancelled - not approved" in result
    
    async def test_agenda_sender_tool_successful_creation(self):
        """Test successful calendar event creation"""
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderTool
        
        tool = AgendaSenderTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {"agenda_approved": True}
        
        # Mock user manager
        with patch('tasks.outputs.output_tasks.task_out_agenda.ZairaUserManager') as mock_zum:
            mock_user = MagicMock()
            mock_zum.get_instance.return_value.find_user = AsyncMock(return_value=mock_user)
            
            # Mock the calendar event creation
            mock_result = {
                "success": True,
                "event_id": "test_event_123",
                "message": "Event created successfully"
            }
            
            with patch.object(tool, '_create_calendar_event', return_value=mock_result):
                event_details = {"summary": "Test Event", "start": "2024-01-15", "end": "2024-01-15"}
                result = await tool._arun(event_details, state=mock_state)
                
                assert "Calendar event created successfully" in result
                assert "test_event_123" in result
                assert mock_state.sections['agenda_sent'] == True
                assert mock_state.sections['agenda_event_id'] == "test_event_123"
    
    async def test_agenda_sender_tool_creation_failure(self):
        """Test agenda creation failure handling"""
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderTool
        
        tool = AgendaSenderTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {"agenda_approved": True}
        
        # Mock user manager
        with patch('tasks.outputs.output_tasks.task_out_agenda.ZairaUserManager') as mock_zum:
            mock_user = MagicMock()
            mock_zum.get_instance.return_value.find_user = AsyncMock(return_value=mock_user)
            
            # Mock failed calendar event creation
            mock_result = {
                "success": False,
                "error": "OAuth token expired"
            }
            
            with patch.object(tool, '_create_calendar_event', return_value=mock_result):
                event_details = {"summary": "Test Event"}
                result = await tool._arun(event_details, state=mock_state)
                
                assert "Failed to create calendar event" in result
                assert "OAuth token expired" in result
    
    async def test_agenda_sender_tool_exception_handling(self):
        """Test agenda sender tool exception handling"""
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderTool
        
        tool = AgendaSenderTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {"agenda_approved": True}
        
        # Mock user manager to throw exception
        with patch('tasks.outputs.output_tasks.task_out_agenda.ZairaUserManager') as mock_zum:
            mock_zum.get_instance.return_value.find_user = AsyncMock(side_effect=Exception("Database error"))
            
            with patch('etc.helper_functions.exception_triggered', return_value=None):
                result = await tool._arun({}, state=mock_state)
                
                assert "Failed to create calendar event due to an error" in result
    
    async def test_create_calendar_event_no_imports(self):
        """Test calendar event creation when imports not available"""
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderTool
        
        tool = AgendaSenderTool()
        
        with patch('tasks.outputs.output_tasks.task_out_agenda.CALENDAR_IMPORTS_AVAILABLE', False):
            result = await tool._create_calendar_event({})
            
            assert result['success'] == False
            assert "Google Calendar dependencies not available" in result['error']
    
    async def test_create_calendar_event_oauth_failure(self):
        """Test calendar event creation OAuth failure"""
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderTool
        
        tool = AgendaSenderTool()
        
        with patch('tasks.outputs.output_tasks.task_out_agenda.CALENDAR_IMPORTS_AVAILABLE', True):
            with patch('tasks.outputs.output_tasks.task_out_agenda.OAuth2Verifier') as mock_oauth:
                mock_oauth.get_token = AsyncMock(side_effect=Exception("OAuth failed"))
                
                result = await tool._create_calendar_event({})
                
                assert result['success'] == False
                assert "Authentication failed" in result['error']
                assert "OAuth failed" in result['error']
    
    async def test_create_calendar_event_no_bearer_token(self):
        """Test calendar event creation when no bearer token"""
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderTool
        
        tool = AgendaSenderTool()
        
        with patch('tasks.outputs.output_tasks.task_out_agenda.CALENDAR_IMPORTS_AVAILABLE', True):
            with patch('tasks.outputs.output_tasks.task_out_agenda.OAuth2Verifier') as mock_oauth:
                mock_oauth.get_token = AsyncMock(return_value=None)
                
                result = await tool._create_calendar_event({})
                
                assert result['success'] == False
                assert "No valid authentication token available" in result['error']
    
    async def test_create_calendar_event_missing_required_fields(self):
        """Test calendar event creation with missing required fields"""
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderTool
        
        tool = AgendaSenderTool()
        
        with patch('tasks.outputs.output_tasks.task_out_agenda.CALENDAR_IMPORTS_AVAILABLE', True):
            with patch('tasks.outputs.output_tasks.task_out_agenda.OAuth2Verifier') as mock_oauth:
                # Setup valid OAuth
                mock_oauth.get_token = AsyncMock(return_value="valid_token")
                mock_oauth_instance = MagicMock()
                mock_oauth.get_instance.return_value = mock_oauth_instance
                mock_oauth_instance.apps = {"gcalendar": MagicMock(
                    client_id="test_id",
                    client_secret="test_secret",
                    token_url="test_url",
                    scopes=["calendar"]
                )}
                
                with patch('tasks.outputs.output_tasks.task_out_agenda.Credentials'):
                    with patch('tasks.outputs.output_tasks.task_out_agenda.build_resource_service'):
                        with patch('tasks.outputs.output_tasks.task_out_agenda.CalendarCreateEvent'):
                            
                            # Test missing 'summary'
                            event_data = {"start": "start_time", "end": "end_time"}
                            result = await tool._create_calendar_event(event_data)
                            
                            assert result['success'] == False
                            assert "Missing required field: summary" in result['error']
    
    async def test_create_calendar_event_success(self):
        """Test successful calendar event creation"""
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderTool
        
        tool = AgendaSenderTool()
        
        with patch('tasks.outputs.output_tasks.task_out_agenda.CALENDAR_IMPORTS_AVAILABLE', True):
            with patch('tasks.outputs.output_tasks.task_out_agenda.OAuth2Verifier') as mock_oauth:
                # Setup valid OAuth
                mock_oauth.get_token = AsyncMock(return_value="valid_token")
                mock_oauth_instance = MagicMock()
                mock_oauth.get_instance.return_value = mock_oauth_instance
                mock_oauth_instance.apps = {"gcalendar": MagicMock(
                    client_id="test_id",
                    client_secret="test_secret",
                    token_url="test_url",
                    scopes=["calendar"]
                )}
                
                with patch('tasks.outputs.output_tasks.task_out_agenda.Credentials'):
                    with patch('tasks.outputs.output_tasks.task_out_agenda.build_resource_service'):
                        with patch('tasks.outputs.output_tasks.task_out_agenda.CalendarCreateEvent') as mock_calendar_tool:
                            
                            # Mock successful event creation
                            mock_tool_instance = MagicMock()
                            mock_tool_instance.ainvoke = AsyncMock(return_value={
                                "id": "event_123",
                                "summary": "Test Event"
                            })
                            mock_calendar_tool.from_api_resource.return_value = mock_tool_instance
                            
                            event_data = {
                                "summary": "Test Event",
                                "start": "2024-01-15T14:00:00",
                                "end": "2024-01-15T15:00:00"
                            }
                            
                            result = await tool._create_calendar_event(event_data)
                            
                            assert result['success'] == True
                            assert result['event_id'] == "event_123"
                            assert "Successfully created event: Test Event" in result['message']
    
    async def test_agenda_sender_direct_tool_initialization(self):
        """Test AgendaSenderDirectTool initialization"""
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderDirectTool
        
        tool = AgendaSenderDirectTool()
        assert tool.name == "agenda_sender_direct_tool"
        assert tool.description == "Creates calendar events directly without user approval - use for automated/system events"
    
    async def test_agenda_sender_direct_tool_success(self):
        """Test direct agenda creation success"""
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderDirectTool, AgendaSenderTool
        
        tool = AgendaSenderDirectTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {}
        
        # Mock successful result
        mock_result = {
            "success": True,
            "event_id": "direct_event_123",
            "message": "Event created"
        }
        
        with patch.object(AgendaSenderTool, '_create_calendar_event', return_value=mock_result):
            event_details = {"summary": "Direct Event", "start": "2024-01-15", "end": "2024-01-15"}
            result = await tool._arun(event_details, state=mock_state)
            
            assert "Calendar event created directly" in result
            assert "direct_event_123" in result
            assert mock_state.sections['agenda_sent'] == True
            assert mock_state.sections['agenda_event_id'] == "direct_event_123"
    
    async def test_format_event_preview(self):
        """Test event preview formatting"""
        from tasks.outputs.output_tasks.task_out_agenda import format_event_preview
        
        event_details = {
            'summary': 'Test Meeting',
            'start': {'dateTime': '2024-01-15T14:00:00Z'},
            'end': {'dateTime': '2024-01-15T15:00:00Z'},
            'location': 'Conference Room A',
            'attendees': [
                {'email': '<EMAIL>'},
                {'email': '<EMAIL>'}
            ]
        }
        
        result = format_event_preview(event_details)
        
        assert 'Test Meeting' in result
        assert '2024-01-15T14:00:00Z' in result
        assert '2024-01-15T15:00:00Z' in result
        assert 'Conference Room A' in result
        assert '<EMAIL>' in result
        assert '<EMAIL>' in result
    
    async def test_format_event_preview_exception(self):
        """Test event preview formatting with exception"""
        from tasks.outputs.output_tasks.task_out_agenda import format_event_preview
        
        # Test with None input to trigger exception
        result = format_event_preview(None)
        
        assert 'Fout bij het formatteren' in result
        assert 'afspraak details' in result
    
    async def test_calendar_create_event_input_schema(self):
        """Test CalendarCreateEventInput Pydantic model"""
        from tasks.outputs.output_tasks.task_out_agenda import CalendarCreateEventInput
        
        # Test valid input
        event_data = {"summary": "Test", "start": "time"}
        mock_state = MagicMock()
        
        input_obj = CalendarCreateEventInput(event_details=event_data, state=mock_state)
        assert input_obj.event_details == event_data
        assert input_obj.state == mock_state
        
        # Test default state
        input_obj_default = CalendarCreateEventInput(event_details=event_data)
        assert input_obj_default.event_details == event_data
        assert input_obj_default.state is None
    
    async def test_direct_calendar_create_tool_success(self):
        """Test direct_calendar_create_tool success"""
        from tasks.outputs.output_tasks.task_out_agenda import direct_calendar_create_tool
        
        mock_result = {
            "success": True,
            "event_id": "tool_event_123",
            "message": "Event created"
        }
        
        with patch('tasks.outputs.output_tasks.task_out_agenda.AgendaSenderDirectTool') as mock_tool_class:
            mock_tool_instance = MagicMock()
            mock_tool_instance._arun = AsyncMock(return_value="Calendar event created directly!\n\nEvent: Test Meeting\nEvent ID: tool_event_123")
            mock_tool_class.return_value = mock_tool_instance
            
            # Test the @tool decorated function
            result = await direct_calendar_create_tool.ainvoke({
                "summary": "Test Meeting",
                "start_datetime": "2024-01-15T14:00:00Z",
                "end_datetime": "2024-01-15T15:30:00Z",
                "description": "Test description",
                "location": "Test location"
            })
            
            assert "Calendar event created directly" in result
            assert "tool_event_123" in result
    
    async def test_direct_calendar_create_tool_exception(self):
        """Test direct_calendar_create_tool exception handling"""
        from tasks.outputs.output_tasks.task_out_agenda import direct_calendar_create_tool
        
        # Mock datetime.fromisoformat to throw an exception
        with patch('tasks.outputs.output_tasks.task_out_agenda.datetime') as mock_datetime:
            mock_datetime.fromisoformat.side_effect = ValueError("Invalid datetime format")
            
            result = await direct_calendar_create_tool.ainvoke({
                "summary": "Test Meeting",
                "start_datetime": "invalid-datetime",
                "end_datetime": "invalid-datetime"
            })
            
            assert "Error creating calendar event" in result
            assert ("Invalid datetime format" in result or "Invalid isoformat string" in result)
    
    async def test_get_calendar_tools_no_imports(self):
        """Test get_calendar_tools when imports not available"""
        from tasks.outputs.output_tasks.task_out_agenda import get_calendar_tools
        
        with patch('tasks.outputs.output_tasks.task_out_agenda.CALENDAR_IMPORTS_AVAILABLE', False):
            result = await get_calendar_tools()
            assert result == []
    
    async def test_get_calendar_tools_oauth_exception(self):
        """Test get_calendar_tools OAuth exception handling"""
        from tasks.outputs.output_tasks.task_out_agenda import get_calendar_tools
        
        with patch('tasks.outputs.output_tasks.task_out_agenda.CALENDAR_IMPORTS_AVAILABLE', True):
            with patch('tasks.outputs.output_tasks.task_out_agenda.OAuth2Verifier') as mock_oauth:
                mock_oauth.get_token = AsyncMock(side_effect=Exception("OAuth service down"))
                
                result = await get_calendar_tools()
                assert result == []
    
    async def test_get_calendar_tools_no_bearer_token(self):
        """Test get_calendar_tools when no bearer token"""
        from tasks.outputs.output_tasks.task_out_agenda import get_calendar_tools
        
        with patch('tasks.outputs.output_tasks.task_out_agenda.CALENDAR_IMPORTS_AVAILABLE', True):
            with patch('tasks.outputs.output_tasks.task_out_agenda.OAuth2Verifier') as mock_oauth:
                mock_oauth.get_token = AsyncMock(return_value=None)
                
                result = await get_calendar_tools()
                assert result == []
    
    async def test_get_calendar_tools_success(self):
        """Test get_calendar_tools success"""
        from tasks.outputs.output_tasks.task_out_agenda import get_calendar_tools, CalendarCreateEventWithConfirmation
        
        with patch('tasks.outputs.output_tasks.task_out_agenda.CALENDAR_IMPORTS_AVAILABLE', True):
            with patch('tasks.outputs.output_tasks.task_out_agenda.OAuth2Verifier') as mock_oauth:
                # Setup valid OAuth
                mock_oauth.get_token = AsyncMock(return_value="valid_token")
                mock_oauth_instance = MagicMock()
                mock_oauth.get_instance.return_value = mock_oauth_instance
                mock_oauth_instance.apps = {"gcalendar": MagicMock(
                    client_id="test_id",
                    client_secret="test_secret",
                    token_url="test_url",
                    scopes=["calendar"]
                )}
                
                with patch('tasks.outputs.output_tasks.task_out_agenda.Credentials'):
                    with patch('tasks.outputs.output_tasks.task_out_agenda.build_resource_service'):
                        with patch('tasks.outputs.output_tasks.task_out_agenda.CalendarToolkit') as mock_toolkit:
                            
                            # Create mock tools
                            mock_create_tool = MagicMock()
                            mock_create_tool.name = "CalendarCreateEvent"
                            mock_other_tool = MagicMock()
                            mock_other_tool.name = "CalendarSearchEvents"
                            
                            mock_toolkit_instance = MagicMock()
                            mock_toolkit_instance.get_tools.return_value = [mock_create_tool, mock_other_tool]
                            mock_toolkit.return_value = mock_toolkit_instance
                            
                            # Patch CalendarCreateEventWithConfirmation to avoid abstract method issues
                            with patch('tasks.outputs.output_tasks.task_out_agenda.CalendarCreateEventWithConfirmation') as mock_custom_tool:
                                mock_custom_instance = MagicMock()
                                mock_custom_tool.return_value = mock_custom_instance
                                
                                result = await get_calendar_tools()
                                
                                # Should return modified tools
                                assert len(result) == 2
                                assert result[0] == mock_custom_instance  # Custom replacement
                                assert result[1] == mock_other_tool       # Unchanged tool
    
    async def test_supervisor_task_agenda_initialization(self):
        """Test SupervisorTask_Agenda initialization"""
        from tasks.outputs.output_tasks.task_out_agenda import SupervisorTask_Agenda
        
        task = SupervisorTask_Agenda(name="test_agenda_task", prompt_id="test_prompt")
        
        assert task.name == "test_agenda_task"
        assert task.prompt_id == "test_prompt"
        assert hasattr(task, 'llm_call')