// Dashboard State Management JavaScript - Global variables and state persistence
// Contains: Global variables, state management, initialization functions

<script>
    // Global variables
    let isMenuExpanded = false;
    // let autoRefreshEnabled = localStorage.getItem('autoRefresh') === 'true';  // REMOVED: Now declared in dashboard-ui.js module
    // let autoRefreshInterval = null;  // REMOVED: Now declared in dashboard-ui.js module
    let currentPage = 'overview';
    
    // API Base URL - use window origin which will work for both Windows and WSL
    const API_BASE_URL = window.location.origin;
    
    // State persistence for F5 refresh
    const STATE_STORAGE_KEY = 'askzaira_dashboard_state';
    
    // Global script loading coordinator
    window.waitForDashboardReady = function(callback, maxRetries = 50) {
        let retries = 0;
        function checkReady() {
            if (window.dashboardJsLoaded && typeof initializeLiveLogViewer === 'function') {
                console.log('[DEBUG] Dashboard confirmed ready - executing callback');
                callback();
            } else if (retries < maxRetries) {
                retries++;
                setTimeout(checkReady, 100);
            } else {
                console.error('[ERROR] Dashboard failed to become ready after maximum retries');
            }
        }
        checkReady();
    };
    
    // Initialize dashboard
    document.addEventListener('DOMContentLoaded', function() {
        initializeParticles();
        initializeMenu();
        
        // Try to restore previous state first
        const stateRestored = restoreApplicationState();
        
        if (!stateRestored) {
            // No saved state, initialize normally
            setCurrentPage();
        }
        
        initializeAutoRefresh();
        initializeStatusCheck();
        
        // Set up state saving on various events
        setupStatePersistence();
    });
    
    // Particles animation
    function initializeParticles() {
        const particlesContainer = document.getElementById('particles');
        const particleCount = 50;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'particle';
            
            const size = Math.random() * 4 + 2;
            const x = Math.random() * 100;
            const y = Math.random() * 100;
            const duration = Math.random() * 3 + 4;
            const delay = Math.random() * 2;
            
            particle.style.width = size + 'px';
            particle.style.height = size + 'px';
            particle.style.left = x + '%';
            particle.style.top = y + '%';
            particle.style.animationDuration = duration + 's';
            particle.style.animationDelay = delay + 's';
            
            particlesContainer.appendChild(particle);
        }
    }
    
    // Menu functionality
    function initializeMenu() {
        const menuToggle = document.getElementById('menuToggle');
        const rightMenu = document.getElementById('rightMenu');
        const mainContainer = document.getElementById('mainContainer');
        const menuIcon = document.getElementById('menuIcon');
        
        menuToggle.addEventListener('click', function() {
            isMenuExpanded = !isMenuExpanded;
            
            if (isMenuExpanded) {
                rightMenu.classList.add('expanded');
                mainContainer.classList.add('menu-expanded');
                menuIcon.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                `;
            } else {
                rightMenu.classList.remove('expanded');
                mainContainer.classList.remove('menu-expanded');
                menuIcon.innerHTML = `
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="3" y1="6" x2="21" y2="6"></line>
                        <line x1="3" y1="12" x2="21" y2="12"></line>
                        <line x1="3" y1="18" x2="21" y2="18"></line>
                    </svg>
                `;
            }
        });
        
        // Menu item click handlers
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                const page = this.getAttribute('data-page');
                if (page) {
                    navigateToPage(page);
                }
            });
        });
    }
    
    // Set current page based on URL path
    function setCurrentPage() {
        const path = window.location.pathname;
        const pathToPage = {
            '/dashboard': 'overview',
            '/dashboard/': 'overview',
            '/dashboard/profile': 'profile',
            '/dashboard/zaira': 'zaira',
            '/dashboard/account': 'account',
            '/connectors': 'connectors',
            '/dashboard/system': 'system',
            '/dashboard/subscription': 'subscription',
            '/dashboard/grasshopper': 'grasshopper'
        };
        
        currentPage = pathToPage[path] || 'overview';
        console.log('[DEBUG] Current page set to:', currentPage);
        updateActiveMenuItem();
        loadPageContent(currentPage);
    }
    
    // Update active menu item
    function updateActiveMenuItem() {
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.classList.remove('active');
            if (item.getAttribute('data-page') === currentPage) {
                item.classList.add('active');
            }
        });
    }
    
    // Navigate to a specific page
    function navigateToPage(page) {
        currentPage = page;
        const pageUrls = {
            'overview': '/dashboard',
            'profile': '/dashboard/profile',
            'zaira': '/dashboard/zaira',
            'account': '/dashboard/account',
            'connectors': '/connectors',
            'system': '/dashboard/system',
            'subscription': '/dashboard/subscription',
            'grasshopper': '/dashboard/grasshopper'
        };
        
        const url = pageUrls[page] || '/dashboard';
        window.history.pushState({page: page}, '', url);
        updateActiveMenuItem();
        loadPageContent(page);
        
        // Save navigation state
        saveApplicationState();
    }
    
    // Load page content
    function loadPageContent(page) {
        const pageContent = document.getElementById('pageContent');
        pageContent.innerHTML = '<div class="loading">Loading ' + page + '...</div>';
        
        fetch(`${API_BASE_URL}/api/page-content?page=${page}`)
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    pageContent.innerHTML = data.content;
                    
                    // Initialize page-specific functionality
                    if (page === 'zaira') {
                        initializeZairaPage();
                    } else if (page === 'grasshopper') {
                        initializeGrasshopperPage();
                    } else if (page === 'overview') {
                        initializeOverviewPage();
                    }
                } else {
                    pageContent.innerHTML = '<div class="error">Failed to load page content: ' + (data.error || 'Unknown error') + '</div>';
                }
            })
            .catch(error => {
                console.error('Error loading page content:', error);
                pageContent.innerHTML = '<div class="error">Failed to load page content: Network error</div>';
            });
    }
    
    // Initialize auto-refresh functionality
    function initializeAutoRefresh() {
        const autoRefreshToggle = document.getElementById('autoRefreshToggle');
        const refreshTimer = document.getElementById('refreshTimer');
        
        updateAutoRefreshDisplay();
        
        autoRefreshToggle.addEventListener('click', function() {
            autoRefreshEnabled = !autoRefreshEnabled;
            localStorage.setItem('autoRefresh', autoRefreshEnabled);
            updateAutoRefreshDisplay();
            
            if (autoRefreshEnabled) {
                startAutoRefresh();
            } else {
                stopAutoRefresh();
            }
        });
        
        if (autoRefreshEnabled) {
            startAutoRefresh();
        }
    }
    
    function updateAutoRefreshDisplay() {
        const toggle = document.getElementById('autoRefreshToggle');
        const slider = toggle.querySelector('.refresh-toggle-slider');
        const timer = document.getElementById('refreshTimer');
        
        if (autoRefreshEnabled) {
            toggle.style.background = 'rgba(34, 197, 94, 0.8)';
            slider.style.transform = 'translateX(26px)';
            timer.textContent = 'Auto-refresh: Enabled';
        } else {
            toggle.style.background = 'rgba(100, 116, 139, 0.3)';
            slider.style.transform = 'translateX(0)';
            timer.textContent = 'Auto-refresh: Disabled';
        }
    }
    
    function startAutoRefresh() {
        if (window.autoRefreshInterval) {
            clearInterval(window.autoRefreshInterval);
        }
        
        window.autoRefreshInterval = setInterval(() => {
            if (currentPage === 'overview') {
                loadPageContent('overview');
            }
            updateStatusCheck();
        }, 30000); // 30 seconds
    }
    
    function stopAutoRefresh() {
        if (window.autoRefreshInterval) {
            clearInterval(window.autoRefreshInterval);
            window.autoRefreshInterval = null;
        }
    }
    
    // Status check functionality
    function initializeStatusCheck() {
        updateStatusCheck();
        // Check status every 60 seconds regardless of auto-refresh
        setInterval(updateStatusCheck, 60000);
    }
    
    function updateStatusCheck() {
        fetch(`${API_BASE_URL}/api/system-health`)
            .then(response => response.json())
            .then(data => {
                const statusBadge = document.getElementById('headerStatusBadge');
                if (data.success && data.status) {
                    const status = data.status.overall_status || 'unknown';
                    statusBadge.textContent = status.charAt(0).toUpperCase() + status.slice(1);
                    
                    // Update badge color based on status
                    if (status === 'healthy') {
                        statusBadge.style.background = '#10b981';
                    } else if (status === 'warning') {
                        statusBadge.style.background = '#f59e0b';
                    } else if (status === 'error') {
                        statusBadge.style.background = '#ef4444';
                    } else {
                        statusBadge.style.background = '#6c757d';
                    }
                } else {
                    statusBadge.textContent = 'Unknown';
                    statusBadge.style.background = '#6c757d';
                }
            })
            .catch(error => {
                console.error('Error checking system status:', error);
                const statusBadge = document.getElementById('headerStatusBadge');
                statusBadge.textContent = 'Error';
                statusBadge.style.background = '#ef4444';
            });
    }
    
    // State persistence functions
    function saveApplicationState() {
        try {
            const state = {
                currentPage: currentPage,
                isMenuExpanded: isMenuExpanded,
                autoRefresh: autoRefreshEnabled,
                timestamp: Date.now(),
                url: window.location.href
            };
            
            // Add page-specific state
            if (currentPage === 'zaira') {
                state.zairaState = captureZairaState();
            } else if (currentPage === 'grasshopper') {
                state.grasshopperState = captureGrasshopperState();
            }
            
            localStorage.setItem(STATE_STORAGE_KEY, JSON.stringify(state));
            console.log('[DEBUG] Application state saved:', state);
        } catch (error) {
            console.error('[ERROR] Failed to save application state:', error);
        }
    }
    
    function restoreApplicationState() {
        try {
            const savedState = localStorage.getItem(STATE_STORAGE_KEY);
            if (!savedState) {
                console.log('[DEBUG] No saved state found');
                return false;
            }
            
            const state = JSON.parse(savedState);
            const now = Date.now();
            const stateAge = now - (state.timestamp || 0);
            
            // State expires after 24 hours
            if (stateAge > 24 * 60 * 60 * 1000) {
                console.log('[DEBUG] Saved state expired, clearing');
                localStorage.removeItem(STATE_STORAGE_KEY);
                return false;
            }
            
            console.log('[DEBUG] Restoring application state:', state);
            
            // Restore basic state
            currentPage = state.currentPage || 'overview';
            isMenuExpanded = state.isMenuExpanded || false;
            autoRefreshEnabled = state.autoRefresh !== undefined ? state.autoRefresh : false;
            
            // Update UI to match restored state
            updateActiveMenuItem();
            loadPageContent(currentPage);
            
            if (isMenuExpanded) {
                const rightMenu = document.getElementById('rightMenu');
                const mainContainer = document.getElementById('mainContainer');
                rightMenu.classList.add('expanded');
                mainContainer.classList.add('menu-expanded');
            }
            
            // Restore page-specific state
            if (state.zairaState && currentPage === 'zaira') {
                setTimeout(() => restoreZairaState(state.zairaState), 1000);
            } else if (state.grasshopperState && currentPage === 'grasshopper') {
                setTimeout(() => restoreGrasshopperState(state.grasshopperState), 1000);
            }
            
            return true;
        } catch (error) {
            console.error('[ERROR] Failed to restore application state:', error);
            localStorage.removeItem(STATE_STORAGE_KEY);
            return false;
        }
    }
    
    // Page-specific state capture/restore functions
    function captureZairaState() {
        const userGuidInput = document.getElementById('userGuidInput');
        const activeTab = document.querySelector('.tab-button.active');
        
        return {
            userGuid: userGuidInput ? userGuidInput.value : '',
            activeTab: activeTab ? activeTab.getAttribute('data-tab') : 'userList'
        };
    }
    
    function restoreZairaState(state) {
        if (state.userGuid) {
            const userGuidInput = document.getElementById('userGuidInput');
            if (userGuidInput) {
                userGuidInput.value = state.userGuid;
            }
        }
        
        if (state.activeTab && typeof showTab === 'function') {
            showTab(state.activeTab);
        }
    }
    
    function captureGrasshopperState() {
        const grasshopperUserGuidInput = document.getElementById('grasshopperUserGuidInput');
        
        return {
            userGuid: grasshopperUserGuidInput ? grasshopperUserGuidInput.value : ''
        };
    }
    
    function restoreGrasshopperState(state) {
        if (state.userGuid) {
            const grasshopperUserGuidInput = document.getElementById('grasshopperUserGuidInput');
            if (grasshopperUserGuidInput) {
                grasshopperUserGuidInput.value = state.userGuid;
            }
        }
    }
    
    function setupStatePersistence() {
        // Save state periodically
        setInterval(saveApplicationState, 5000); // Every 5 seconds
        
        // Save state on page unload (F5, close, navigate)
        window.addEventListener('beforeunload', saveApplicationState);
        
        // Save state on visibility change (alt-tab, minimize)
        document.addEventListener('visibilitychange', saveApplicationState);
        
        // Save state when forms change
        document.addEventListener('input', function(e) {
            if (e.target.id === 'userGuidInput' || e.target.id === 'grasshopperUserGuidInput') {
                // Debounce form changes
                clearTimeout(window.stateDebounceTimer);
                window.stateDebounceTimer = setTimeout(saveApplicationState, 1000);
            }
        });
        
        // Save state when tabs change
        document.addEventListener('click', function(e) {
            if (e.target.classList.contains('tab-button') || e.target.classList.contains('menu-item')) {
                setTimeout(saveApplicationState, 100); // Small delay to capture state after change
            }
        });
    }
    
    // Page initialization functions
    function initializeOverviewPage() {
        console.log('[DEBUG] Initializing overview page');
        // Overview page specific initialization
    }
    
    function initializeZairaPage() {
        console.log('[DEBUG] Initializing zaira page');
        // Wait for dashboard functions to be available
        window.waitForDashboardReady(() => {
            if (typeof loadUserList === 'function') {
                loadUserList();
            }
        });
    }
    
    function initializeGrasshopperPage() {
        console.log('[DEBUG] Initializing grasshopper page');
        // Wait for dashboard functions to be available
        window.waitForDashboardReady(() => {
            if (typeof initializeLiveLogViewer === 'function') {
                initializeLiveLogViewer();
            }
        });
    }
    
    // Handle browser back/forward navigation
    window.addEventListener('popstate', function(event) {
        if (event.state && event.state.page) {
            currentPage = event.state.page;
            updateActiveMenuItem();
            loadPageContent(currentPage);
        } else {
            setCurrentPage();
        }
    });
    
    // Export functions to global scope
    window.navigateToPage = navigateToPage;
    window.saveApplicationState = saveApplicationState;
    window.restoreApplicationState = restoreApplicationState;
    window.initializeOverviewPage = initializeOverviewPage;
    window.initializeZairaPage = initializeZairaPage;
    window.initializeGrasshopperPage = initializeGrasshopperPage;
    window.updateStatusCheck = updateStatusCheck;
    window.currentPage = currentPage;
    
    console.log('[DEBUG] Dashboard state management loaded');
</script>