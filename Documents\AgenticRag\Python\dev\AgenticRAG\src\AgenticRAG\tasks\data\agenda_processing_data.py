from imports import *

from typing import Optional, Dict, Any
from datetime import datetime
from pydantic import Field
from .processing_output_data import ProcessingOutputDataBase

class AgendaProcessingData(ProcessingOutputDataBase):
    """Data structure for agenda planning → calendar creation workflow"""
    
    # Core calendar event fields  
    summary: str = Field(..., min_length=1, description="Event title/summary")
    description: Optional[str] = Field(default=None, description="Event description") 
    location: Optional[str] = Field(default=None, description="Event location")
    start_datetime: datetime = Field(..., description="Event start time")
    end_datetime: datetime = Field(..., description="Event end time")
    
    # Agenda workflow state
    content_generated: bool = Field(default=False)
    schedule_planned: bool = Field(default=False)
    calendar_validated: bool = Field(default=False)
    
    # Calendar-specific fields
    attendees: list[str] = Field(default_factory=list, description="Attendee email addresses")
    calendar_id: Optional[str] = Field(default=None, description="Target calendar ID")
    event_id: Optional[str] = Field(default=None, description="Created event ID") 
    recurrence_rules: Optional[list[str]] = Field(default=None, description="Recurrence rules")
    reminders: list[Dict[str, Any]] = Field(default_factory=list, description="Event reminders")
    
    # Agenda planning metadata
    agenda_items: list[str] = Field(default_factory=list, description="Agenda topics")
    preparation_notes: Optional[str] = Field(default=None)
    expected_outcomes: Optional[str] = Field(default=None)
    
    def get_output_type(self) -> str:
        return "agenda"
        
    def get_preview_text(self) -> str:
        attendee_list = ', '.join(self.attendees) if self.attendees else 'Geen'
        return f"""
Afspraak details:
Titel: {self.summary}  
Start: {self.start_datetime.strftime('%Y-%m-%d %H:%M')}
Eind: {self.end_datetime.strftime('%Y-%m-%d %H:%M')}
Locatie: {self.location or 'Geen'}
Deelnemers: {attendee_list}

{self.description or ''}
        """.strip()
        
    def to_calendar_event_format(self) -> dict:
        """Convert to Google Calendar API format"""
        event_data = {
            "summary": self.summary,
            "start": {"dateTime": self.start_datetime.isoformat()},
            "end": {"dateTime": self.end_datetime.isoformat()}
        }
        
        if self.description:
            event_data["description"] = self.description
        if self.location:
            event_data["location"] = self.location  
        if self.attendees:
            event_data["attendees"] = [{"email": email} for email in self.attendees]
        if self.reminders:
            event_data["reminders"] = {"useDefault": False, "overrides": self.reminders}
            
        return event_data