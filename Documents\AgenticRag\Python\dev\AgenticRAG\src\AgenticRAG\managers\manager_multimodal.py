from imports import *
import base64
import hashlib
import json
import os
import uuid
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from unstructured.documents.elements import Image, Table, CompositeElement
from unstructured.partition.pdf import partition_pdf
from unstructured.partition.auto import partition
import asyncio

class MultimodalManager:
    _instance = None
    _initialized = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_instance(cls):
        return cls()
    
    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return
        
        # Create assets directory if it doesn't exist
        assets_dir = BASE_DIR() / "assets" / "documents"
        assets_dir.mkdir(parents=True, exist_ok=True)
        
        # Set up configuration from settings
        instance.config = {
            "vision_model": "gpt-4o-mini",  # OpenAI vision model
            "table_model": "gpt-4o-mini",   # Model for table summarization
            "max_image_size": 5 * 1024 * 1024,  # 5MB max image size
            "supported_image_formats": [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff"],
            "vision_max_tokens": 500,
            "table_max_tokens": 400,
            "enable_image_processing": True,
            "enable_table_processing": True,
            "enable_figure_processing": True,
            "context_window": 2,  # Number of surrounding elements to consider for context
        }
        
        instance._initialized = True
    
    @classmethod
    async def extract_multimodal_elements(cls, file_path: str, doc_id: str = None) -> Dict[str, Any]:
        """
        Extract images and tables from a document using unstructured.
        Returns a dictionary containing extracted elements and their metadata.
        """
        if doc_id is None:
            doc_id = str(uuid.uuid4())
        
        try:
            # Determine file type and use appropriate partitioning
            file_ext = Path(file_path).suffix.lower()
            
            LogFire.log("DEBUG", f"MULTIMODAL: Processing file {file_path} (ext: {file_ext})", severity="debug")
            
            if file_ext == '.pdf':
                LogFire.log("DEBUG", f"MULTIMODAL: Using PDF hi_res strategy for {file_path}", severity="debug")
                try:
                    # Use PDF-specific partitioning with enhanced multimodal extraction
                    elements = partition_pdf(
                        filename=file_path,
                        infer_table_structure=True,
                        strategy="hi_res",
                        extract_image_block_types=["Image", "Table"],
                        extract_image_block_to_payload=True,
                        chunking_strategy="by_title",
                        max_characters=10000,
                        combine_text_under_n_chars=2000,
                        new_after_n_chars=6000,
                        overlap=200
                    )
                    LogFire.log("DEBUG", f"PDF partitioning successful, found {len(elements)} elements", severity="debug")
                except Exception as pdf_error:
                    LogFire.log("DEBUG", f"PDF partitioning failed: {str(pdf_error)}", severity="debug")
                    LogFire.log("DEBUG", f"Falling back to basic partitioning", severity="debug")
                    # Fallback to basic partitioning
                    try:
                        elements = partition(filename=file_path)
                        LogFire.log("DEBUG", f"Basic partitioning successful, found {len(elements)} elements", severity="debug")
                    except Exception as basic_error:
                        LogFire.log("DEBUG", f"Basic partitioning also failed: {str(basic_error)}", severity="debug")
                        # Last resort: try fast strategy
                        elements = partition(filename=file_path, strategy="fast")
                        LogFire.log("DEBUG", f"Fast strategy successful, found {len(elements)} elements", severity="debug")
            else:
                LogFire.log("DEBUG", f"Using general partitioning for non-PDF file", severity="debug")
                # Use general partitioning for other file types
                elements = partition(
                    filename=file_path,
                    strategy="hi_res",
                    infer_table_structure=True,
                    extract_images=True,
                    keep_elements=["Title", "NarrativeText", "Table", "Image", "Figure", "FigureCaption"],
                    chunking_strategy="by_title",
                    max_characters=4000,
                    new_after_n_chars=3200,
                    overlap=200
                )
            
            # Process elements and extract multimodal content
            result = {
                "doc_id": doc_id,
                "text_elements": [],
                "images": [],
                "tables": [],
                "figures": [],
                "captions": [],
                "all_elements": []
            }
            
            # Debug: Print all element types found
            LogFire.log("DEBUG", f"Found {len(elements)} total elements", severity="debug")
            element_types = {}
            for element in elements:
                element_type = type(element).__name__
                element_types[element_type] = element_types.get(element_type, 0) + 1
            LogFire.log("DEBUG", f"Element types found: {element_types}", severity="debug")
            
            # Extract images from CompositeElement objects (embedded images)
            images_b64 = cls._get_images_base64(elements)
            LogFire.log("DEBUG", f"Extracted {len(images_b64)} base64 images from elements", severity="debug")
            
            for i, element in enumerate(elements):
                element_data = {
                    "id": f"{doc_id}_{i}",
                    "type": type(element).__name__,
                    "text": str(element),
                    "metadata": element.metadata.to_dict() if hasattr(element.metadata, "to_dict") else str(element.metadata),
                    "element_index": i
                }
                
                result["all_elements"].append(element_data)
                
                # Debug: Log element details conditionally to reduce verbosity
                element_preview = str(element)[:100].replace('\n', ' ')
                LogFire.log_conditionally("DEBUG", f"Element {i}: {type(element).__name__} - '{element_preview}...'", 
                                        condition=i < 3, severity="debug")  # Only log first 3 elements
                
                # Check for table-like content in metadata
                if hasattr(element, 'metadata'):
                    if hasattr(element.metadata, 'text_as_html') and element.metadata.text_as_html:
                        LogFire.log("DEBUG", f"Has HTML content (likely table)", severity="debug")
                    if hasattr(element.metadata, 'orig_elements'):
                        LogFire.log("DEBUG", f"Has {len(element.metadata.orig_elements)} original elements", severity="debug")
                
                # Handle different element types
                if isinstance(element, Image):
                    LogFire.log("DEBUG", f"Processing Image element {i}", severity="debug")
                    await cls._process_image_element(element, element_data, doc_id, result)
                elif isinstance(element, Table):
                    LogFire.log("DEBUG", f"Processing Table element {i}", severity="debug")
                    await cls._process_table_element(element, element_data, doc_id, result)
                elif "Table" in str(type(element)):
                    LogFire.log("DEBUG", f"Processing Table-like element {i}", severity="debug")
                    await cls._process_table_like_element(element, element_data, doc_id, result)
                elif isinstance(element, CompositeElement):
                    LogFire.log("DEBUG", f"Processing CompositeElement {i}", severity="debug")
                    await cls._process_composite_element(element, element_data, doc_id, result, images_b64)
                else:
                    # Check if text element contains table-like content
                    text_content = str(element)
                    if any(keyword in text_content.lower() for keyword in ['jan', 'feb', 'maart', 'april', 'mei', 'juni']) and any(char.isdigit() for char in text_content):
                        LogFire.log("DEBUG", f"Text element {i} appears to contain table data", severity="debug")
                        # Process as table-like element
                        element_data["content_type"] = "table_in_text"
                        await cls._process_table_like_element(element, element_data, doc_id, result)
                    else:
                        result["text_elements"].append(element_data)
            
            # Process standalone images found in base64 format
            if images_b64:
                LogFire.log("DEBUG", f"Processing {len(images_b64)} standalone images", severity="debug")
                await cls._process_standalone_images(images_b64, doc_id, result)
            
            # Final summary
            LogFire.log("DEBUG", f"Multimodal extraction complete:", severity="debug")
            LogFire.log("DEBUG", f"Text elements: {len(result['text_elements'])}, Images: {len(result['images'])}, Tables: {len(result['tables'])}, Total: {len(result['all_elements'])}", severity="debug")
            
            return result
            
        except Exception as e:
            LogFire.log("ERROR", f"Error extracting multimodal elements from {file_path}: {str(e)}", severity="error")
            return {
                "doc_id": doc_id,
                "text_elements": [],
                "images": [],
                "tables": [],
                "figures": [],
                "captions": [],
                "all_elements": [],
                "error": str(e)
            }
    
    @classmethod
    def _get_images_base64(cls, elements: List) -> List[str]:
        """Extract base64 images from CompositeElement objects."""
        images_b64 = []
        for element in elements:
            try:
                # Check for direct image base64 in element metadata
                if hasattr(element, 'metadata'):
                    if hasattr(element.metadata, 'image_base64') and element.metadata.image_base64:
                        images_b64.append(element.metadata.image_base64)
                        LogFire.log("DEBUG", f"Found direct base64 image in element metadata", severity="debug")
                
                # Check for images in CompositeElement orig_elements
                if "CompositeElement" in str(type(element)):
                    if hasattr(element, 'metadata') and hasattr(element.metadata, 'orig_elements'):
                        for orig_el in element.metadata.orig_elements:
                            if "Image" in str(type(orig_el)):
                                if hasattr(orig_el, 'metadata') and hasattr(orig_el.metadata, 'image_base64'):
                                    if orig_el.metadata.image_base64:
                                        images_b64.append(orig_el.metadata.image_base64)
                                        LogFire.log("DEBUG", f"Found base64 image in CompositeElement orig_elements", severity="debug")
                
                # Check for images in element itself if it's an Image type
                if "Image" in str(type(element)):
                    if hasattr(element, 'metadata') and hasattr(element.metadata, 'image_base64'):
                        if element.metadata.image_base64:
                            images_b64.append(element.metadata.image_base64)
                            LogFire.log("DEBUG", f"Found base64 image in Image element", severity="debug")
                    
                    # Also check for image_path or other image data
                    if hasattr(element, 'metadata'):
                        if hasattr(element.metadata, 'image_path') and element.metadata.image_path:
                            LogFire.log("DEBUG", f"Found image path: {element.metadata.image_path}", severity="debug")
                        if hasattr(element.metadata, 'image_data') and element.metadata.image_data:
                            LogFire.log("DEBUG", f"Found image data (not base64)", severity="debug")
                        
            except Exception as e:
                LogFire.log("DEBUG", f"Error extracting images from element: {str(e)}", severity="debug")
        
        return images_b64
    
    @classmethod
    async def _process_image_element(cls, element: Image, element_data: Dict, doc_id: str, result: Dict):
        """Process an Image element and save it to the asset store."""
        try:
            # Extract image data if available
            image_data = None
            if hasattr(element, 'image_data') and element.image_data:
                image_data = element.image_data
            elif hasattr(element.metadata, 'image_data') and element.metadata.image_data:
                image_data = element.metadata.image_data
            
            if image_data:
                # Save image to assets directory
                asset_path = await cls._save_image_asset(image_data, doc_id, element_data["id"])
                element_data["asset_path"] = asset_path
                element_data["has_asset"] = True
                
                # Generate image summary
                context = cls._get_surrounding_context(result["all_elements"], element_data["element_index"])
                element_data["summary"] = await cls.generate_image_summary(asset_path, context)
            else:
                element_data["has_asset"] = False
                element_data["summary"] = "No image data available"
            
            result["images"].append(element_data)
            
        except Exception as e:
            element_data["error"] = str(e)
            result["images"].append(element_data)
    
    @classmethod
    async def _process_composite_element(cls, element: CompositeElement, element_data: Dict, doc_id: str, result: Dict, images_b64: List[str]):
        """Process a CompositeElement which may contain embedded images."""
        try:
            # Check if this composite element contains images
            if hasattr(element, 'metadata') and hasattr(element.metadata, 'orig_elements'):
                for orig_el in element.metadata.orig_elements:
                    if "Image" in str(type(orig_el)):
                        # Process embedded image
                        if hasattr(orig_el, 'metadata') and hasattr(orig_el.metadata, 'image_base64'):
                            image_b64 = orig_el.metadata.image_base64
                            if image_b64:
                                # Save image and generate summary
                                asset_path = await cls._save_base64_image(image_b64, doc_id, element_data["id"])
                                if asset_path:
                                    element_data["has_embedded_image"] = True
                                    element_data["image_asset_path"] = asset_path
                                    
                                    # Generate image summary
                                    context = cls._get_surrounding_context(result["all_elements"], element_data["element_index"])
                                    element_data["image_summary"] = await cls.generate_image_summary(asset_path, context)
            
            result["text_elements"].append(element_data)
            
        except Exception as e:
            element_data["error"] = str(e)
            result["text_elements"].append(element_data)
    
    @classmethod
    async def _process_table_like_element(cls, element, element_data: Dict, doc_id: str, result: Dict):
        """Process table-like elements that may not be strict Table instances."""
        try:
            # Check if element has table-like metadata
            if hasattr(element, 'metadata'):
                if hasattr(element.metadata, 'text_as_html'):
                    element_data["html_content"] = element.metadata.text_as_html
                    element_data["has_html"] = True
                else:
                    element_data["has_html"] = False
            
            # Convert to markdown format
            element_data["markdown"] = await cls._element_to_markdown(element)
            
            # Generate table summary
            context = cls._get_surrounding_context(result["all_elements"], element_data["element_index"])
            element_data["summary"] = await cls.generate_table_summary(element_data["markdown"], context)
            
            result["tables"].append(element_data)
            
        except Exception as e:
            element_data["error"] = str(e)
            result["tables"].append(element_data)
    
    @classmethod
    async def _process_standalone_images(cls, images_b64: List[str], doc_id: str, result: Dict):
        """Process standalone base64 images that were extracted."""
        for i, image_b64 in enumerate(images_b64):
            try:
                element_id = f"{doc_id}_standalone_image_{i}"
                asset_path = await cls._save_base64_image(image_b64, doc_id, element_id)
                
                if asset_path:
                    image_data = {
                        "id": element_id,
                        "type": "StandaloneImage",
                        "text": "Extracted image from document",
                        "asset_path": asset_path,
                        "has_asset": True,
                        "element_index": len(result["all_elements"]) + i,
                        "metadata": {"source": "base64_extraction"}
                    }
                    
                    # Generate image summary
                    image_data["summary"] = await cls.generate_image_summary(asset_path, "")
                    result["images"].append(image_data)
                    
            except Exception as e:
                LogFire.log("DEBUG", f"Error processing standalone image {i}: {str(e)}", severity="debug")
    
    @classmethod
    async def _process_table_element(cls, element: Table, element_data: Dict, doc_id: str, result: Dict):
        """Process a Table element and extract structured data."""
        try:
            # Extract table structure if available
            table_data = None
            if hasattr(element, 'table_data') and element.table_data:
                table_data = element.table_data
            elif hasattr(element.metadata, 'table_data') and element.metadata.table_data:
                table_data = element.metadata.table_data
            
            if table_data:
                element_data["table_structure"] = table_data
                element_data["has_structure"] = True
            else:
                element_data["has_structure"] = False
            
            # Convert table to markdown format
            element_data["markdown"] = await cls._table_to_markdown(element)
            
            # Generate table summary
            context = cls._get_surrounding_context(result["all_elements"], element_data["element_index"])
            element_data["summary"] = await cls.generate_table_summary(element_data["markdown"], context)
            
            # Extract key information
            element_data["key_info"] = await cls._extract_table_key_info(element_data["markdown"])
            
            result["tables"].append(element_data)
            
        except Exception as e:
            element_data["error"] = str(e)
            result["tables"].append(element_data)
    
    # @classmethod
    # async def _process_figure_element(cls, element: Figure, element_data: Dict, doc_id: str, result: Dict):
    #     """Process a Figure element."""
    #     try:
    #         # Figures are often combinations of images and text
    #         result["figures"].append(element_data)
            
    #     except Exception as e:
    #         element_data["error"] = str(e)
    #         result["figures"].append(element_data)
    
    @classmethod
    async def _save_base64_image(cls, image_b64: str, doc_id: str, element_id: str) -> str:
        """Save base64 encoded image to the assets directory."""
        try:
            # Decode base64 to bytes
            image_data = base64.b64decode(image_b64)
            return await cls._save_image_asset(image_data, doc_id, element_id)
        except Exception as e:
            LogFire.log("DEBUG", f"Error saving base64 image: {str(e)}", severity="debug")
            return ""
    
    @classmethod
    async def _save_image_asset(cls, image_data: bytes, doc_id: str, element_id: str) -> str:
        """Save image data to the assets directory."""
        try:
            # Create doc-specific directory
            doc_assets_dir = BASE_DIR() / "assets" / "documents" / doc_id
            doc_assets_dir.mkdir(parents=True, exist_ok=True)
            
            # Generate filename
            image_hash = hashlib.md5(image_data).hexdigest()[:8]
            filename = f"{element_id}_{image_hash}.png"
            asset_path = doc_assets_dir / filename
            
            # Save image
            with open(asset_path, 'wb') as f:
                f.write(image_data)
            
            return str(asset_path)
            
        except Exception as e:
            LogFire.log("DEBUG", f"Error saving image asset: {str(e)}", severity="debug")
            return ""
    
    @classmethod
    async def _element_to_markdown(cls, element) -> str:
        """Convert any element to markdown format."""
        try:
            # Check if element has HTML representation
            if hasattr(element, 'metadata') and hasattr(element.metadata, 'text_as_html'):
                return cls._html_to_markdown(element.metadata.text_as_html)
            # Check if it's a Table element
            elif isinstance(element, Table):
                return await cls._table_to_markdown(element)
            else:
                # Fallback to text representation
                return str(element)
        except Exception as e:
            return str(element)
    
    @classmethod
    def _html_to_markdown(cls, html_content: str) -> str:
        """Convert HTML table to markdown format."""
        try:
            from bs4 import BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            table = soup.find('table')
            
            if not table:
                return html_content
            
            rows = table.find_all('tr')
            if not rows:
                return html_content
            
            markdown = ""
            
            for i, row in enumerate(rows):
                cells = row.find_all(['th', 'td'])
                if cells:
                    cell_texts = [cell.get_text(strip=True) for cell in cells]
                    markdown += "| " + " | ".join(cell_texts) + " |\n"
                    
                    # Add separator after header row
                    if i == 0:
                        markdown += "| " + " | ".join(["---"] * len(cell_texts)) + " |\n"
            
            return markdown
            
        except Exception as e:
            LogFire.log("DEBUG", f"Error converting HTML to markdown: {str(e)}", severity="debug")
            return html_content
    
    @classmethod
    async def _table_to_markdown(cls, table_element: Table) -> str:
        """Convert a table element to markdown format."""
        try:
            # If table has structured data, convert to markdown
            if hasattr(table_element, 'table_data') and table_element.table_data:
                return cls._structured_table_to_markdown(table_element.table_data)
            else:
                # Fallback to text representation
                return str(table_element)
                
        except Exception as e:
            return str(table_element)
    
    @classmethod
    def _structured_table_to_markdown(cls, table_data: List[List[str]]) -> str:
        """Convert structured table data to markdown format."""
        if not table_data or not table_data[0]:
            return ""
        
        markdown = ""
        
        # Add header row
        header = table_data[0]
        markdown += "| " + " | ".join(header) + " |\n"
        
        # Add separator
        markdown += "| " + " | ".join(["---"] * len(header)) + " |\n"
        
        # Add data rows
        for row in table_data[1:]:
            # Pad row to match header length
            padded_row = row + [""] * (len(header) - len(row))
            markdown += "| " + " | ".join(padded_row[:len(header)]) + " |\n"
        
        return markdown
    
    @classmethod
    async def generate_image_summary(cls, image_path: str, context: str = "") -> str:
        """
        Generate a summary/caption for an image using vision models.
        """
        try:
            # Import OpenAI for vision
            from openai import OpenAI
            
            # Initialize OpenAI client
            client = OpenAI()
            
            # Encode image to base64
            image_base64 = await cls._encode_image_to_base64(image_path)
            
            # Create vision prompt
            vision_prompt = f"""
            Please analyze this image and provide a detailed description that includes:
            1. What objects, people, or scenes are visible
            2. Key visual elements (colors, shapes, text if any)
            3. The overall context or purpose of the image
            4. Any relevant details that would help in document understanding
            
            Context from document: {context}
            
            Provide a concise but comprehensive description that would be useful for document retrieval and understanding.
            """
            
            # Make API call to GPT-4 Vision
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": vision_prompt
                            },
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"data:image/jpeg;base64,{image_base64}"
                                }
                            }
                        ]
                    }
                ],
                max_tokens=500
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            LogFire.log("DEBUG", f"Error generating image summary: {str(e)}", severity="debug")
            return f"Image description unavailable - Error: {str(e)}"
    
    @classmethod
    async def generate_table_summary(cls, table_markdown: str, context: str = "") -> str:
        """
        Generate a summary for a table using LLM.
        """
        try:
            # Import OpenAI for table summarization
            from openai import OpenAI
            
            # Initialize OpenAI client
            client = OpenAI()
            
            # Create table summarization prompt
            table_prompt = f"""
            Please analyze this table and provide a comprehensive summary that includes:
            1. What the table represents (its purpose or subject)
            2. Key data points, trends, or patterns
            3. Column headers and their meanings
            4. Notable values, ranges, or relationships
            5. Any insights that would be valuable for document retrieval
            
            Table content:
            {table_markdown}
            
            Context from document: {context}
            
            Provide a clear, concise summary that captures the essence and key information of this table.
            """
            
            # Make API call to GPT-4
            response = client.chat.completions.create(
                model="gpt-4o-mini",
                messages=[
                    {
                        "role": "user",
                        "content": table_prompt
                    }
                ],
                max_tokens=400
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            LogFire.log("DEBUG", f"Error generating table summary: {str(e)}", severity="debug")
            return f"Table summary unavailable - Error: {str(e)}"
    
    @classmethod
    async def _encode_image_to_base64(cls, image_path: str) -> str:
        """Encode image file to base64 string."""
        try:
            with open(image_path, 'rb') as image_file:
                return base64.b64encode(image_file.read()).decode('utf-8')
        except Exception as e:
            LogFire.log("DEBUG", f"Error encoding image to base64: {str(e)}", severity="debug")
            return ""
    
    @classmethod
    def _get_surrounding_context(cls, all_elements: List[Dict], current_index: int, context_window: int = 2) -> str:
        """Get surrounding text context for an element."""
        try:
            context_parts = []
            
            # Get preceding context
            start_idx = max(0, current_index - context_window)
            for i in range(start_idx, current_index):
                if i < len(all_elements):
                    element = all_elements[i]
                    if element["type"] in ["NarrativeText", "Title"]:
                        context_parts.append(element["text"])
            
            # Get following context
            end_idx = min(len(all_elements), current_index + context_window + 1)
            for i in range(current_index + 1, end_idx):
                if i < len(all_elements):
                    element = all_elements[i]
                    if element["type"] in ["NarrativeText", "Title"]:
                        context_parts.append(element["text"])
            
            return " ".join(context_parts)
            
        except Exception as e:
            LogFire.log("DEBUG", f"Error getting surrounding context: {str(e)}", severity="debug")
            return ""
    
    @classmethod
    async def _extract_table_key_info(cls, table_markdown: str) -> Dict[str, Any]:
        """Extract key information from table markdown."""
        try:
            lines = table_markdown.strip().split('\n')
            if len(lines) < 3:  # Must have header, separator, and at least one data row
                return {"error": "Invalid table format"}
            
            # Extract headers
            headers = [h.strip() for h in lines[0].split('|')[1:-1]]  # Remove empty first/last
            
            # Extract data rows
            data_rows = []
            for line in lines[2:]:  # Skip header and separator
                if line.strip():
                    row = [cell.strip() for cell in line.split('|')[1:-1]]
                    data_rows.append(row)
            
            # Calculate basic statistics
            key_info = {
                "headers": headers,
                "num_columns": len(headers),
                "num_rows": len(data_rows),
                "total_cells": len(headers) * len(data_rows),
                "has_headers": len(headers) > 0,
                "column_types": await cls._infer_column_types(data_rows, headers)
            }
            
            return key_info
            
        except Exception as e:
            LogFire.log("DEBUG", f"Error extracting table key info: {str(e)}", severity="debug")
            return {"error": str(e)}
    
    @classmethod
    async def _infer_column_types(cls, data_rows: List[List[str]], headers: List[str]) -> Dict[str, str]:
        """Infer data types for table columns."""
        try:
            column_types = {}
            
            for col_idx, header in enumerate(headers):
                if col_idx < len(data_rows[0]) if data_rows else False:
                    # Sample values from this column
                    sample_values = [row[col_idx] for row in data_rows[:5] if col_idx < len(row)]
                    
                    # Simple type inference
                    if all(cls._is_numeric(val) for val in sample_values):
                        column_types[header] = "numeric"
                    elif all(cls._is_date(val) for val in sample_values):
                        column_types[header] = "date"
                    else:
                        column_types[header] = "text"
                else:
                    column_types[header] = "unknown"
            
            return column_types
            
        except Exception as e:
            LogFire.log("DEBUG", f"Error inferring column types: {str(e)}", severity="debug")
            return {}
    
    @classmethod
    def _is_numeric(cls, value: str) -> bool:
        """Check if a value is numeric."""
        try:
            float(value.replace(',', '').replace('$', '').replace('%', ''))
            return True
        except ValueError:
            return False
    
    @classmethod
    def _is_date(cls, value: str) -> bool:
        """Check if a value looks like a date."""
        import re
        date_patterns = [
            r'\d{4}-\d{2}-\d{2}',  # YYYY-MM-DD
            r'\d{2}/\d{2}/\d{4}',  # MM/DD/YYYY
            r'\d{2}-\d{2}-\d{4}',  # MM-DD-YYYY
        ]
        return any(re.match(pattern, value.strip()) for pattern in date_patterns)
    
    @classmethod
    async def cleanup_assets(cls, doc_id: str):
        """Clean up assets for a specific document."""
        try:
            doc_assets_dir = BASE_DIR() / "assets" / "documents" / doc_id
            if doc_assets_dir.exists():
                import shutil
                shutil.rmtree(doc_assets_dir)
        except Exception as e:
            LogFire.log("DEBUG", f"Error cleaning up assets for doc {doc_id}: {str(e)}", severity="debug")
    
    @classmethod
    async def get_asset_path(cls, doc_id: str, element_id: str) -> Optional[str]:
        """Get the path to an asset file."""
        try:
            doc_assets_dir = BASE_DIR() / "assets" / "documents" / doc_id
            for asset_file in doc_assets_dir.glob(f"{element_id}_*"):
                return str(asset_file)
            return None
        except Exception as e:
            LogFire.log("DEBUG", f"Error getting asset path for {doc_id}/{element_id}: {str(e)}", severity="debug")
            return None