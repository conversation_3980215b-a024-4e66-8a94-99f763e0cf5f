#!/usr/bin/env python3
"""
Base Foundation for Real System Integration Tests

This module provides the foundation for all test_real files, ensuring consistent
setup, teardown, and utilities for testing with the full AskZaira system.

Key Features:
- Full system initialization (identical to production)
- Automated bot integration for human-in-the-loop testing
- Consistent test environment setup
- Proper cleanup and resource management
- Comprehensive logging and debugging support

================================================================================
COMPREHENSIVE TEST_REAL DOCUMENTATION
================================================================================

1. OVERVIEW
-----------
The test_real framework provides production-identical integration testing for the 
AskZaira (AgenticRAG) system. Unlike traditional unit tests, these tests run 
against the ACTUAL production system using mainFunc(), ensuring 100% behavioral 
parity.

Key Features:
- Production-Identical: Uses actual mainFunc() from production
- Human-in-the-Loop Automation: MyBot_Testing handles all approval prompts
- Comprehensive Logging: Debug capture system for detailed test analysis
- Real Service Integration: Tests against actual databases and external services
- Request Monitoring: Real-time tracking of multi-agent workflows

When to Use test_real:
- End-to-End Testing: Verify complete workflows from user input to final output
- Integration Testing: Test interactions between multiple system components
- Service Validation: Ensure external services work correctly
- Production Behavior: Verify exact production behavior without mocking

2. ARCHITECTURE
---------------
Directory Structure:
    tests/test_real/
    ├── base_real_test.py              # Foundation class (MUST inherit)
    ├── test_debug_capture.py          # Logging system
    ├── test_bot_testing.py            # Bot automation
    ├── test_real_*.py                 # Feature-specific tests
    ├── test_real_execution_suite.py   # Consolidated suite
    └── bge_onnx/                      # ONNX model files

System Flow:
    [Test Method] → [BaseRealTest.setup_real_system()] → [mainFunc()] → [Production System]
         ↓                                                      ↓
    [create_test_user()] ←────────────────────────────── [All Managers Initialized]
         ↓
    [create_test_bot()] → [MyBot_Testing with HITL Override]
         ↓
    [execute_and_monitor_request()] → [Real Request Execution] → [Monitoring Loop]
         ↓
    [assert_request_success()] → [Validation]

3. CREATING NEW TESTS
--------------------
Step 1: Create Test File
    #!/usr/bin/env python3
    from sys import path as sys_path
    from os import path as os_path
    sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))
    
    import pytest
    import asyncio
    from tests.test_real.base_real_test import BaseRealTest
    
    class TestMyFeature(BaseRealTest):
        '''Test suite for my feature'''
        pass

Step 2: Implement Test Method
    @pytest.mark.asyncio
    async def test_my_feature(self):
        # Initialize system
        setup_success = await self.setup_real_system("category")
        assert setup_success
        
        # Create test resources
        user = await self.create_test_user()
        bot = self.create_test_bot()
        
        # Execute request
        result = await self.execute_and_monitor_request(
            user=user,
            query="your test query here",
            bot=bot
        )
        
        # Validate results
        self.assert_request_success(result)

Step 3: Add Decorators
    @with_test_logging  # Enable debug capture
    @requires_openai_api()  # Skip if no API key
    @pytest.mark.asyncio  # Mark as async test

4. COMMON PATTERNS
-----------------
Pattern 1: Basic Test
    async def test_basic_feature(self):
        setup_success = await self.setup_real_system()
        assert setup_success
        
        user = await self.create_test_user()
        bot = self.create_test_bot()
        
        result = await self.execute_and_monitor_request(
            user, "test query", bot, timeout=30
        )
        
        self.assert_request_success(result)

Pattern 2: Feature-Specific Test
    async def test_feature_functionality(self):
        setup_success = await self.setup_real_system()
        assert setup_success
        
        user = await self.create_test_user()
        bot = self.create_test_bot()
        
        result = await self.execute_and_monitor_request(
            user,
            "process this specific feature request",
            bot,
            timeout=60
        )
        
        self.assert_request_success(result)

Pattern 3: Multi-Step Workflow
    async def test_workflow(self):
        setup_success = await self.setup_real_system()
        assert setup_success
        
        user = await self.create_test_user()
        bot = self.create_test_bot()
        
        # Step 1
        result1 = await self.execute_and_monitor_request(
            user, "first request", bot
        )
        assert result1["success"]
        
        # Step 2
        result2 = await self.execute_and_monitor_request(
            user, "second request", bot
        )
        assert result2["success"]

Pattern 4: Error Handling
    async def test_error_recovery(self):
        setup_success = await self.setup_real_system()
        assert setup_success
        
        user = await self.create_test_user()
        bot = self.create_test_bot()
        
        result = await self.execute_and_monitor_request(
            user, "invalid request", bot, timeout=15
        )
        
        # Should handle gracefully
        assert result["success"]

5. TROUBLESHOOTING
-----------------
Common Issues:
1. "Failed to initialize real production system"
   - Check Docker is running
   - Verify environment variables
   - Check database connectivity

2. "IMAP idle scheduled request must exist"
   - Run production system once to create scheduled requests

3. "Request execution timeout"
   - Increase timeout parameter
   - Check for HITL prompts not being answered
   - Verify bot is handling approvals

4. "UnicodeEncodeError"
   - Use ASCII-only content in tests

5. "Connection refused" errors
   - Start Docker Desktop
   - Run docker-compose up -d
   - Check service ports

Debug Techniques:
1. Enable test logging with @with_test_logging
2. Check logs in tests/test_real/logs/
3. Add print statements for request details
4. Use pdb for breakpoints
5. Inspect database directly

6. BEST PRACTICES
----------------
DO's:
- Always inherit from BaseRealTest
- Use production database names ("vectordb" or "meltanodb")
- Handle async properly with await
- Clean up resources in teardown
- Use descriptive test names
- Add comments for complex logic
- Test both success and failure paths
- Use appropriate timeouts for operations
- Check logs when tests fail
- Run from correct directory (src/AgenticRAG/)

DON'Ts:
- Don't use Unicode/emojis in test data
- Don't hardcode GUIDs - use uuid4()
- Don't skip error handling
- Don't modify production data unnecessarily
- Don't use synchronous operations
- Don't ignore test warnings
- Don't create arbitrary database names
- Don't bypass the bot for HITL
- Don't use global state between tests
- Don't forget to call super() in setup/teardown

7. RUNNING TESTS
---------------
Command Line Execution:
    # Run all test_real tests
    ../../.venv/Scripts/pytest.exe tests/test_real/ -v
    
    # Run specific test file
    ../../.venv/Scripts/pytest.exe tests/test_real/test_real_feature.py -v
    
    # Run specific test method
    ../../.venv/Scripts/pytest.exe tests/test_real/test_real_feature.py::TestRealFeature::test_feature -v
    
    # Run with coverage
    ../../.venv/Scripts/pytest.exe tests/test_real/ -v --cov

================================================================================
END OF DOCUMENTATION
================================================================================

Usage:
    from tests.test_real.base_real_test import BaseRealTest
    
    class TestMyFeature(BaseRealTest):
        async def test_my_feature(self):
            # System is already initialized
            user = await self.create_test_user()
            bot = self.create_test_bot()
            
            result = await user.on_message("test query", bot, [], None)
            assert result is not None
"""

from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

from imports import *
from managers.manager_logfire import LogFire
import pytest
import asyncio
import time
import os
from uuid import uuid4
from typing import List, Dict, Any, Optional, Tuple
from pathlib import Path
import tempfile
import shutil
import subprocess
import platform
import functools
import signal
import atexit
import weakref

# Test debug capture
from tests.test_real.test_debug_capture import setup_test_debug_capture, cleanup_test_debug_capture

# Core system imports
from managers.manager_supervisors import SupervisorManager
from managers.manager_users import ZairaUserManager
from managers.manager_prompts import PromptManager
from userprofiles.ZairaUser import ZairaUser, PERMISSION_LEVELS
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
from etc.setup import init
from tasks.task_top_level_supervisor import create_top_level_supervisor
from endpoints.testing_endpoint import MyBot_Testing

# Test metadata
__version__ = "1.0.0"
__author__ = "Claude Code"
__description__ = "Foundation base class for AskZaira real system integration tests"


def with_test_logging(func):
    """Decorator to automatically enable test logging for async test methods"""
    @functools.wraps(func)
    async def wrapper(self, *args, **kwargs):
        # Start logging with the test function name
        setup_test_debug_capture(func.__name__)
        try:
            # Run the actual test
            result = await func(self, *args, **kwargs)
            return result
        except Exception as e:
            # Log any exceptions
            import traceback
            LogFire.log("DEBUG", f"\n[ERROR] Test failed with exception: {e}")
            traceback.print_exc()
            raise
        finally:
            # Always stop logging
            cleanup_test_debug_capture()
    return wrapper


class BaseRealTest:
    """
    Base class for all real system integration tests.
    
    Provides standardized setup, teardown, and utilities for testing
    with the complete AskZaira system in a production-like environment.
    
    IMPORTANT: All test_real tests MUST inherit from this class to ensure
    proper system initialization and cleanup.
    
    Example Usage:
        class TestMyFeature(BaseRealTest):
            @pytest.mark.asyncio
            async def test_my_feature(self):
                # System is automatically initialized via setup_real_system()
                setup_success = await self.setup_real_system()
                assert setup_success
                
                # Create test user and bot
                user = await self.create_test_user()
                bot = self.create_test_bot()
                
                # Execute and monitor request
                result = await self.execute_and_monitor_request(
                    user=user,
                    query="your test query here",
                    bot=bot,
                    timeout=60
                )
                
                # Validate results
                self.assert_request_success(result)
    
    
    Key Methods:
        - setup_real_system(): Initialize production system
        - create_test_user(): Create test user with permissions
        - create_test_bot(): Create automated HITL bot
        - setup_real_system(): Initialize production system
        - create_test_user(): Create test user with permissions
        - create_test_bot(): Create automated HITL bot
        - execute_and_monitor_request(): Run and monitor request execution
        - assert_request_success(): Validate request completion
        - assert_pipeline_verification(): Check pipeline steps
    """
    
    # Class-level configuration
    DEFAULT_TIMEOUT = 30  # seconds
    DEFAULT_REQUEST_MONITOR_INTERVAL = 1  # seconds
    
    def _init_test_state(self):
        """Initialize test state - called from setup_method"""
        if not hasattr(self, 'test_name'):
            self.test_name = self.__class__.__name__
            self.test_user_guid = str(uuid4())
            self.test_session_guid = str(uuid4())
            self.test_scheduled_guid = str(uuid4())
            
            # Test environment
            self.temp_data_dir = None
            self.temp_persist_dir = None
            self.initialized = False
            self.cleanup_requests = []
            
            # Test resources
            self.test_users = []
            self.test_bots = []
            
            # Server tracking for cleanup
            self.running_servers = []
            self.signal_handlers_registered = False
        
    
    def setup_method(self):
        """Set up test fixtures - called before each test method"""
        # Initialize test state
        self._init_test_state()
        
        LogFire.log("DEBUG", f"[{self.test_name}] Setting up test method")
        
        # Reset test state
        self.test_user_guid = str(uuid4())
        self.test_session_guid = str(uuid4())
        self.test_scheduled_guid = str(uuid4())
        self.cleanup_requests = []
        self.test_users = []
        self.test_bots = []
        
        # Reset server tracking
        self.running_servers = []
        self.signal_handlers_registered = False
        
        # CRITICAL: Reset initialization flag to force fresh setup per test
        self.initialized = False
        
        # Initialize Globals for testing
        try:
            Globals.Debug = False
            Globals.set_debug_values(True)
        except:
            pass
        
        # Initialize ZairaSettings for testing
        try:
            if not hasattr(ZairaSettings, 'IsDebugMode'):
                ZairaSettings.IsDebugMode = False
        except:
            pass
    
    def teardown_method(self):
        """Clean up after each test method"""
        LogFire.log("DEBUG", f"[{self.test_name}] Cleaning up test method")
        
        # Cancel any remaining asyncio tasks
        try:
            loop = asyncio.get_running_loop()
            tasks = [task for task in asyncio.all_tasks(loop) if not task.done()]
            for task in tasks:
                if task != asyncio.current_task():
                    task.cancel()
        except RuntimeError:
            pass
        
        # Clean up temporary directories
        if self.temp_data_dir and Path(self.temp_data_dir).exists():
            shutil.rmtree(self.temp_data_dir, ignore_errors=True)
        if self.temp_persist_dir and Path(self.temp_persist_dir).exists():
            shutil.rmtree(self.temp_persist_dir, ignore_errors=True)
    
    async def setup_real_system(self) -> bool:
        """
        Set up the REAL AskZaira system using production's mainFunc.
        
        This method uses the EXACT same startup sequence as production main.py
        to ensure 100% identical behavior - no more "test_real isn't real" issues!
        
        CRITICAL: This method patches main_loop.exec_main_loop to create test
        environment while using ACTUAL production system initialization.
        
        Process Flow:
        1. Changes to production directory (../../)
        2. Patches main_loop.exec_main_loop with test version
        3. Calls production mainFunc() with patch applied
        4. Creates TestUser and Testing bot for test access
        5. Restores original directory and main_loop
        
        Returns:
            bool: True if setup successful, False otherwise
            
        Side Effects:
            - Changes working directory temporarily
            - Patches main_loop.exec_main_loop
            - Creates self.production_test_user and self.production_testing_bot
            - Initializes all production managers and supervisors
            - Sets self.initialized = True on success
            
        Example:
            setup_success = await self.setup_real_system()
            if not setup_success:
                pytest.fail("Failed to initialize production system")
        """
        if self.initialized:
            return True
        try:
            # Check if test port is available before proceeding
            import socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            from globals import ZAIRA_PYTHON_PORT
            result = sock.connect_ex(('localhost', ZAIRA_PYTHON_PORT))
            sock.close()
            
            if result == 0:  # Port is in use
                LogFire.log("ERROR", f"[{self.test_name}] ERROR: Port {ZAIRA_PYTHON_PORT} is already in use!")
                LogFire.log("DEBUG", f"[{self.test_name}] Please shut down any server using port {ZAIRA_PYTHON_PORT} before running tests.")
                LogFire.log("DEBUG", f"[{self.test_name}] The application cannot run while tests are executing.")
                pytest.fail(f"Port {ZAIRA_PYTHON_PORT} is already in use. Please shut down the server and try again.")
                return False
            
            LogFire.log("DEBUG", f"[{self.test_name}] Port {ZAIRA_PYTHON_PORT} is available, proceeding with test setup...")
            LogFire.log("DEBUG", f"[{self.test_name}] Setting up REAL system using production mainFunc()")
            LogFire.log("DEBUG", f"[{self.test_name}] WARNING: This will use ACTUAL production data directories!")
            
            # Change to same directory as main app (src/AgenticRAG)
            import os
            original_cwd = os.getcwd()
            production_dir = os.path.join(os.path.dirname(__file__), "../../../../")
            production_dir = os.path.abspath(production_dir)
            
            print(f"[TEST_DEBUG] {self.test_name}: Changing directory from {original_cwd} to {production_dir}")
            LogFire.log("DEBUG", f"[{self.test_name}] Changing directory from {original_cwd} to {production_dir}")
            os.chdir(production_dir)
            
            # Log the working directory state after change
            current_cwd = os.getcwd()
            test_dev_env_path = current_cwd + "/dev.env"
            dev_env_exists = os.path.exists(test_dev_env_path)
            
            print(f"[TEST_DEBUG] {self.test_name}: test_real getcwd(): {current_cwd}")
            print(f"[TEST_DEBUG] {self.test_name}: test_real looking for dev.env at: {test_dev_env_path}")
            print(f"[TEST_DEBUG] {self.test_name}: test_real dev.env exists: {dev_env_exists}")
            
            LogFire.log("DEBUG", f"[{self.test_name}] test_real getcwd(): {current_cwd}")
            LogFire.log("DEBUG", f"[{self.test_name}] test_real looking for dev.env at: {test_dev_env_path}")
            LogFire.log("DEBUG", f"[{self.test_name}] test_real dev.env exists: {dev_env_exists}")
            
            try:
                # Replace main loop BEFORE importing mainFunc to ensure the patch is applied
                import main_loop
                original_exec_main_loop = main_loop.exec_main_loop
                
                # Replace main loop with test version that creates Testing bot and user
                async def test_main_loop():
                    LogFire.log("DEBUG", f"[{self.test_name}] Production initialization complete - setting up test environment")
                    
                    # Create test user and Testing bot (similar to original main_loop but for testing)
                    from managers.manager_users import ZairaUserManager
                    from userprofiles.ZairaUser import PERMISSION_LEVELS
                    from endpoints.testing_endpoint import MyBot_Testing
                    
                    # Create test user
                    test_user = await ZairaUserManager.add_user("TestUser", PERMISSION_LEVELS.ADMIN, 
                                                               ZairaUserManager.create_guid(), 
                                                               ZairaUserManager.create_guid())
                    
                    # Create Testing bot with HITL override capabilities
                    testing_bot = MyBot_Testing(None, "Testing")
                    
                    LogFire.log("DEBUG", f"[{self.test_name}] Test environment ready - TestUser and Testing bot created")
                    
                    # Store references for test access
                    self.production_test_user = test_user
                    self.production_testing_bot = testing_bot
                    
                    return
                
                # Patch the main_loop module
                main_loop.exec_main_loop = test_main_loop
                
                # Import the production mainFunc AFTER patching main_loop
                from main import mainFunc
                
                try:
                    # Run the EXACT production startup sequence from correct directory
                    LogFire.log("DEBUG", f"[{self.test_name}] Running production mainFunc() from {os.getcwd()}")
                    await mainFunc()
                    
                    LogFire.log("DEBUG", f"[{self.test_name}] Production mainFunc() completed successfully")
                    
                    # Setup signal handlers for graceful shutdown
                    self.setup_signal_handlers()
                    
                    # Track the servers that were started by mainFunc()
                    await self._track_started_servers()
                    
                    # Re-enable debug values after production initialization
                    try:
                        Globals.set_debug_values(True)
                    except:
                        pass
                    
                    self.initialized = True
                    return True
                    
                finally:
                    # Restore original main loop
                    main_loop.exec_main_loop = original_exec_main_loop
                    
            finally:
                # Always restore original working directory
                LogFire.log("DEBUG", f"[{self.test_name}] Restoring directory to {original_cwd}")
                os.chdir(original_cwd)
            
        except Exception as e:
            LogFire.log("DEBUG", f"[{self.test_name}] Production mainFunc() failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    
    async def _ensure_docker_running(self):
        """Ensure Docker Desktop is running and databases are started"""
        LogFire.log("DEBUG", f"[{self.test_name}] Checking Docker Desktop status...")
        
        # Platform-specific Docker Desktop startup
        if platform.system() == "Windows":
            # Check if Docker Desktop is running
            try:
                result = subprocess.run(
                    ['powershell', '-Command', "Get-Process 'Docker Desktop' -ErrorAction Stop"],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                if result.returncode == 0:
                    LogFire.log("DEBUG", f"[{self.test_name}] Docker Desktop is already running")
                else:
                    raise subprocess.CalledProcessError(result.returncode, result.args)
            except (subprocess.CalledProcessError, subprocess.TimeoutExpired):
                LogFire.log("DEBUG", f"[{self.test_name}] Starting Docker Desktop...")
                try:
                    # Start Docker Desktop
                    subprocess.Popen(
                        ['C:\\Program Files\\Docker\\Docker\\Docker Desktop.exe'],
                        shell=True
                    )
                    LogFire.log("DEBUG", f"[{self.test_name}] Waiting for Docker Desktop to start...")
                    await asyncio.sleep(15)  # Wait for Docker Desktop to initialize
                except Exception as e:
                    LogFire.log("DEBUG", f"[{self.test_name}] Warning: Could not start Docker Desktop: {e}")
                    LogFire.log("DEBUG", f"[{self.test_name}] Please ensure Docker Desktop is running manually")
        
        # Wait for Docker daemon to be ready
        max_attempts = 30
        for attempt in range(max_attempts):
            try:
                result = subprocess.run(
                    ['docker', 'version'],
                    capture_output=True,
                    text=True,
                    timeout=5
                )
                if result.returncode == 0:
                    LogFire.log("DEBUG", f"[{self.test_name}] Docker daemon is ready")
                    break
            except (subprocess.CalledProcessError, subprocess.TimeoutExpired, FileNotFoundError):
                pass
            
            if attempt < max_attempts - 1:
                await asyncio.sleep(2)
            else:
                LogFire.log("DEBUG", f"[{self.test_name}] Warning: Docker daemon not responding after {max_attempts} attempts")
                LogFire.log("DEBUG", f"[{self.test_name}] Tests may fail if databases are not available")
                return
        
        # Start required databases with docker-compose
        LogFire.log("DEBUG", f"[{self.test_name}] Starting PostgreSQL and Qdrant databases...")
        try:
            # Find docker-compose.yml file
            compose_file = Path(__file__).parent.parent.parent / "docker-compose.yml"
            if not compose_file.exists():
                LogFire.log("DEBUG", f"[{self.test_name}] Warning: docker-compose.yml not found at {compose_file}")
                return
            
            # Start databases
            result = subprocess.run(
                ['docker-compose', '-f', str(compose_file), 'up', '-d', 'postgres', 'qdrant'],
                capture_output=True,
                text=True,
                cwd=compose_file.parent,
                timeout=60
            )
            
            if result.returncode == 0:
                LogFire.log("DEBUG", f"[{self.test_name}] Databases started successfully")
                # Wait for databases to be ready
                LogFire.log("DEBUG", f"[{self.test_name}] Waiting for databases to be ready...")
                await asyncio.sleep(10)
            else:
                LogFire.log("DEBUG", f"[{self.test_name}] Warning: Could not start databases: {result.stderr}")
                
        except Exception as e:
            LogFire.log("DEBUG", f"[{self.test_name}] Warning: Error starting databases: {e}")
            LogFire.log("DEBUG", f"[{self.test_name}] Please ensure databases are running manually")
    
    
    async def create_test_user(self, 
                             username: str = None,
                             permission_level: PERMISSION_LEVELS = PERMISSION_LEVELS.ADMIN,
                             email: str = None) -> ZairaUser:
        """
        Create a test user with specified or default properties.
        
        This method creates a real user in the production ZairaUserManager
        with proper GUID assignment and permission levels. The user is fully
        functional and can execute tasks, receive messages, and interact with
        all system components.
        
        Default Configuration:
        - Username: "test_user_{count}" (auto-incremented)
        - Permission: ADMIN level (full access)
        - Email: "<EMAIL>" (default test email)
        - GUIDs: Auto-generated using uuid4()
        
        Permission Levels:
        - ADMIN: Full system access, can execute all tasks
        - USER: Standard user access
        - GUEST: Limited access
        
        Args:
            username: Username for the test user (default: auto-generated)
            permission_level: Permission level (default: ADMIN)
            email: Email address for the user (default: "<EMAIL>")
            
        Returns:
            ZairaUser: The created test user with assigned GUIDs
            
        Side Effects:
            - Adds user to self.test_users list for cleanup
            - Registers user with ZairaUserManager
            - Prints creation confirmation
            
        Example:
            # Create default test user
            user = await self.create_test_user()
            
            # Create specific user for email testing
            email_user = await self.create_test_user(
                username="email_tester",
                email="<EMAIL>"
            )
        """
        if not username:
            username = f"test_user_{len(self.test_users) + 1}"
        
        if not email:
            email = "<EMAIL>"
        
        user_manager = ZairaUserManager.get_instance()
        user = await user_manager.add_user(
            username,
            permission_level,
            uuid4(),
            uuid4()
        )
        
        user.email = email
        self.test_users.append(user)
        
        LogFire.log("DEBUG", f"[{self.test_name}] Created test user: {username} with email: {email}")
        return user
    
    def create_test_bot(self, name: str = "Testing") -> MyBot_Testing:
        """
        Create a test bot for automated human-in-the-loop responses.
        
        This method creates a MyBot_Testing instance that automatically handles
        HITL (Human-in-the-Loop) approval prompts during request execution. The bot
        will automatically approve requests, eliminating the need for manual intervention
        during testing.
        
        Bot Capabilities:
        - Auto-approves HITL prompts with "ja" (yes) responses
        - Logs all interactions for debugging
        - Handles approval callbacks automatically
        - Simulates human interaction patterns
        
        Usage in Tasks:
        The bot is passed to execute_and_monitor_request() and will handle any
        HITL requests that arise during request execution, such as:
        - Content approval requests
        - Operation confirmations
        - File operation permissions
        - Sensitive data handling confirmations
        
        Args:
            name: Name for the test bot (default: "Testing")
            
        Returns:
            MyBot_Testing: The created test bot with auto-approval enabled
            
        Side Effects:
            - Adds bot to self.test_bots list for cleanup
            - Prints creation confirmation
            
        Example:
            bot = self.create_test_bot("FeatureTester")
            
            result = await self.execute_and_monitor_request(
                user=user,
                query="process request requiring approval",
                bot=bot  # Will auto-approve when prompted
            )
        """
        bot = MyBot_Testing(None, name)
        self.test_bots.append(bot)
        
        LogFire.log("DEBUG", f"[{self.test_name}] Created test bot: {name}")
        return bot
    
    async def execute_and_monitor_request(self, 
                                        user: ZairaUser,
                                        query: str,
                                        bot: MyBot_Testing,
                                        timeout: int = None,
                                        monitor_interval: int = None) -> Dict[str, Any]:
        """
        Execute a request and monitor its progress with detailed logging.
        
        This is the core method for testing request execution. It creates a request
        via user.on_message(), then monitors the request progression in real-time,
        handling HITL requests automatically through the provided bot.
        
        Monitoring Process:
        1. Creates request via user.on_message()
        2. Monitors active request count every monitor_interval seconds
        3. Checks for HITL requests and logs their status
        4. Continues until all requests complete or timeout reached
        5. Collects detailed execution metrics and request information
        
        Result Dictionary Structure:
        {
            "success": bool,                    # Request creation success
            "execution_time": float,            # Total execution time in seconds
            "initial_request_guid": str,        # GUID of initial request
            "final_request_count": int,         # Number of active requests at end
            "completed_in_time": bool,          # Whether completed before timeout
            "request_details": [                # Detailed request information
                {
                    "guid": str,                # Request GUID
                    "type": str,                # Request class name
                    "output_demands": [str],    # Expected outputs
                    "call_trace": [str],        # Execution trace
                    "state_sections": [str]     # State section keys
                }
            ],
            "recent_messages": [                # Recent user messages
                {
                    "content": str,             # Message content (truncated)
                    "timestamp": datetime       # Message timestamp
                }
            ]
        }
        
        Args:
            user: The ZairaUser to execute the task for
            query: The natural language query/message to process
            bot: The MyBot_Testing instance for HITL handling
            timeout: Maximum time to wait in seconds (default: DEFAULT_TIMEOUT)
            monitor_interval: Check interval in seconds (default: DEFAULT_REQUEST_MONITOR_INTERVAL)
            
        Returns:
            Dict[str, Any]: Comprehensive request execution results and metrics
            
        Raises:
            AssertionError: If request creation fails completely
            
        Example:
            result = await self.execute_and_monitor_request(
                user=test_user,
                query="process test request",
                bot=test_bot,
                timeout=60,
                monitor_interval=2
            )
            
            # Check results
            assert result["success"]
            assert result["execution_time"] < 30
            assert len(result["request_details"]) > 0
        """
        if timeout is None:
            timeout = self.DEFAULT_TIMEOUT
        if monitor_interval is None:
            monitor_interval = self.DEFAULT_REQUEST_MONITOR_INTERVAL
        
        LogFire.log("DEBUG", f"[{self.test_name}] Executing request: '{query}'")
        LogFire.log("DEBUG", f"[{self.test_name}] Bot will handle approval prompts automatically")
        
        start_time = time.time()
        
        # Start the request
        request = await user.on_message(
            complete_message=query,
            calling_bot=bot,
            attachments=[],
            original_message=None
        )
        
        if not request:
            return {
                "success": False,
                "error": "Failed to create request",
                "execution_time": time.time() - start_time
            }
        
        LogFire.log("DEBUG", f"[{self.test_name}] Request created: {request.scheduled_guid}")
        
        # Monitor request progression
        for i in range(timeout):
            await asyncio.sleep(monitor_interval)
            
            active_request_count = len(user.my_requests)
            if i % 5 == 0:  # Log every 5 seconds
                LogFire.log("DEBUG", f"[{self.test_name}] Second {i+1}: {active_request_count} active requests")
            
            # Check for human-in-the-loop requests
            hitl_request = user.get_active_hitl_request()
            if hitl_request:
                LogFire.log("DEBUG", f"[{self.test_name}] Found HITL request: {hitl_request.scheduled_guid}")
                
                if hasattr(hitl_request, 'human_in_the_loop_callback'):
                    callback_exists = hitl_request.human_in_the_loop_callback is not None
                    LogFire.log("DEBUG", f"[{self.test_name}] HITL callback exists: {callback_exists}")
            
            # Check if all requests are completed
            if not user.has_active_requests():
                LogFire.log("DEBUG", f"[{self.test_name}] All requests completed after {i+1} seconds")
                break
        
        execution_time = time.time() - start_time
        
        # Analyze results
        result = {
            "success": True,
            "execution_time": execution_time,
            "initial_request_guid": request.scheduled_guid,
            "final_request_count": len(user.my_requests),
            "completed_in_time": execution_time < timeout
        }
        
        # Get request details
        request_details = []
        for request_guid, request_obj in user.my_requests.items():
            details = {
                "guid": request_guid,
                "type": type(request_obj).__name__
            }
            
            if hasattr(request_obj, 'output_demands'):
                details["output_demands"] = request_obj.output_demands
            
            if hasattr(request_obj, 'call_trace'):
                details["call_trace"] = request_obj.call_trace
            
            if hasattr(request_obj, 'state') and hasattr(request_obj.state, 'sections'):
                details["state_sections"] = list(request_obj.state.sections.keys())
            
            request_details.append(details)
        
        result["request_details"] = request_details
        
        # Try to get recent messages
        try:
            if hasattr(user, 'get_recent_messages'):
                recent_messages = user.get_recent_messages(limit=10)
                result["recent_messages"] = [
                    {"content": msg.content[:100], "timestamp": getattr(msg, 'timestamp', None)}
                    for msg in recent_messages
                ]
        except Exception as e:
            LogFire.log("DEBUG", f"[{self.test_name}] Could not get recent messages: {e}")
            result["recent_messages"] = []
        
        LogFire.log("DEBUG", f"[{self.test_name}] Request execution completed in {execution_time:.2f} seconds")
        return result
    
    def assert_request_success(self, result: Dict[str, Any], 
                             expected_outputs: List[str] = None,
                             max_execution_time: float = None):
        """
        Assert that a request executed successfully with expected characteristics.
        
        This method performs comprehensive validation of request execution results,
        checking success status, performance constraints, and expected outputs.
        
        Validation Checks:
        1. Success Status: Ensures result["success"] is True
        2. Execution Time: Validates execution time if max_execution_time provided
        3. Output Demands: Checks that expected outputs were generated
        
        Output Demand Examples:
        - "output": Output generation tasks
        - "processing": Processing operations
        - "search": Data retrieval operations
        - "file": File operations
        - "api": API calls
        
        Args:
            result: Result dictionary from execute_and_monitor_request()
            expected_outputs: List of expected output demands to verify
            max_execution_time: Maximum acceptable execution time in seconds
            
        Raises:
            AssertionError: If any validation check fails
            
        Example:
            # Basic success check
            self.assert_task_success(result)
            
            # Check with expected outputs
            self.assert_request_success(
                result, 
                expected_outputs=["output", "processing"]
            )
            
            # Check with performance constraint
            self.assert_request_success(
                result,
                expected_outputs=["output"],
                max_execution_time=30.0
            )
        """
        assert result["success"], f"Request execution failed: {result.get('error', 'Unknown error')}"
        
        if max_execution_time:
            assert result["execution_time"] <= max_execution_time, \
                f"Request took {result['execution_time']:.2f}s, expected <= {max_execution_time}s"
        
        if expected_outputs:
            found_outputs = set()
            for request_detail in result["request_details"]:
                if "output_demands" in request_detail:
                    found_outputs.update(request_detail["output_demands"])
            
            for expected_output in expected_outputs:
                assert expected_output in found_outputs, \
                    f"Expected output '{expected_output}' not found in {found_outputs}"
        
        LogFire.log("DEBUG", f"[{self.test_name}] Request success assertions passed")
    
    def assert_pipeline_verification(self, result: Dict[str, Any], 
                                   expected_pipeline_steps: List[str]):
        """
        Assert that specific pipeline steps were executed.
        
        This method analyzes the call traces from request execution to verify that
        expected pipeline components were invoked. It's particularly useful for
        validating multi-step workflows and ensuring proper task routing.
        
        Pipeline Step Examples:
        Processing Pipeline:
        - "content_generator": Content generation
        - "output_processor": Output processing
        - "approval_step": Human approval step
        
        Data Pipeline:
        - "data_retrieval": Database/API queries
        - "data_processing": Data transformation
        - "data_analysis": Analysis operations
        
        Integration Pipeline:
        - "api_integration": External API calls
        - "service_integration": Service integrations
        - "notification": Notification processing
        
        How It Works:
        1. Extracts call_trace from each request in result["request_details"]
        2. Converts call traces to lowercase strings
        3. Searches for expected pipeline step names in traces
        4. Ensures all expected steps are found
        
        Args:
            result: Result dictionary from execute_and_monitor_request()
            expected_pipeline_steps: List of pipeline step names to verify
            
        Returns:
            List[str]: Verified pipeline steps that were found
            
        Raises:
            AssertionError: If any expected pipeline step is not found
            
        Example:
            # Verify processing pipeline
            verified_steps = self.assert_pipeline_verification(
                result,
                ["content_generator", "output_processor"]
            )
            
            # Verify complex workflow
            self.assert_pipeline_verification(
                result,
                ["data_retrieval", "data_processing", "content_generator"]
            )
        """
        pipeline_verification = []
        
        for request_detail in result["request_details"]:
            call_trace = request_detail.get("call_trace", [])
            call_trace_str = " ".join(str(call).lower() for call in call_trace)
            
            for step in expected_pipeline_steps:
                if step.lower() in call_trace_str and step not in pipeline_verification:
                    pipeline_verification.append(step)
        
        for expected_step in expected_pipeline_steps:
            assert expected_step in pipeline_verification, \
                f"Expected pipeline step '{expected_step}' not found. Found: {pipeline_verification}"
        
        LogFire.log("DEBUG", f"[{self.test_name}] Pipeline verification passed: {pipeline_verification}")
        
        return pipeline_verification
    
    async def wait_for_assertions(self, 
                                assertion_func,
                                timeout: int = 480,
                                poll_interval: int = 30,
                                assertion_name: str = "condition") -> Dict[str, Any]:
        """
        Wait for custom assertions to pass by checking them every poll_interval seconds.
        
        This method provides a standardized polling system for test_real tests that need
        to wait for external conditions or long-running processes to complete. It replaces
        the hardcoded waiting logic from individual test files.
        
        Key Features:
        - Configurable polling interval (default: 30 seconds for dashboard monitoring)
        - Custom assertion functions for different test scenarios
        - Detailed progress logging with elapsed time tracking
        - Timeout handling with partial success reporting
        - Comprehensive result dictionary for test analysis
        
        Assertion Function:
        The assertion_func should be an async callable that returns a dictionary with:
        {
            "passed": bool,           # Whether assertion passed
            "message": str,           # Status message for logging
            "data": Any,             # Optional data for analysis
            "should_continue": bool   # Whether to continue polling (default: True)
        }
        
        Result Dictionary:
        {
            "success": bool,              # Final assertion result
            "elapsed_time": float,        # Total time elapsed in seconds
            "poll_count": int,            # Number of polls performed
            "timeout_reached": bool,      # Whether timeout was reached
            "final_message": str,         # Final status message
            "assertion_data": Any         # Final assertion data
        }
        
        Args:
            assertion_func: Async function that returns assertion result dict
            timeout: Maximum time to wait in seconds (default: 480 = 8 minutes)
            poll_interval: Time between checks in seconds (default: 30)
            assertion_name: Descriptive name for logging (default: "condition")
            
        Returns:
            Dict[str, Any]: Comprehensive polling results and final status
            
        Example:
            # Define custom assertion for email test
            async def check_broadcast():
                # Database query logic here
                return {
                    "passed": broadcast_found,
                    "message": f"Broadcast check: {broadcast_count} found",
                    "data": {"broadcast_count": broadcast_count}
                }
            
            # Wait for assertion to pass
            result = await self.wait_for_assertions(
                check_broadcast,
                timeout=480,
                poll_interval=30,
                assertion_name="broadcast_detection"
            )
            
            # Check results
            assert result["success"]
            assert result["elapsed_time"] < 300
        """
        LogFire.log("DEBUG", f"[{self.test_name}] Starting assertion polling for: {assertion_name}")
        LogFire.log("DEBUG", f"[{self.test_name}] Timeout: {timeout}s, Poll interval: {poll_interval}s")
        
        start_time = time.time()
        poll_count = 0
        last_message = "Starting assertion checks..."
        final_data = None
        
        # Initial poll before entering loop
        try:
            result = await assertion_func()
            poll_count += 1
            
            if result.get("passed", False):
                elapsed = time.time() - start_time
                LogFire.log("DEBUG", f"[{self.test_name}] Assertion passed immediately: {result.get('message', 'Success')}")
                return {
                    "success": True,
                    "elapsed_time": elapsed,
                    "poll_count": poll_count,
                    "timeout_reached": False,
                    "final_message": result.get("message", "Assertion passed"),
                    "assertion_data": result.get("data")
                }
        except Exception as e:
            LogFire.log("DEBUG", f"[{self.test_name}] Initial assertion check failed: {e}")
            last_message = f"Initial check error: {e}"
        
        # Main polling loop - Use short sleeps to allow main app to continue processing
        next_poll_time = start_time + poll_interval
        while time.time() - start_time < timeout:
            # Use very short sleep intervals to allow background tasks to run
            await asyncio.sleep(0.1)  # 100ms - allows event loop to process other tasks
            
            # Check if it's time for the next poll
            current_time = time.time()
            if current_time < next_poll_time:
                continue  # Not time for next poll yet
                
            poll_count += 1
            next_poll_time = current_time + poll_interval
            elapsed = current_time - start_time
            
            try:
                result = await assertion_func()
                message = result.get("message", f"Poll {poll_count} completed")
                last_message = message
                final_data = result.get("data")
                
                # Log progress every poll
                LogFire.log("DEBUG", f"[{self.test_name}] Poll {poll_count} ({elapsed:.0f}s/{timeout}s): {message}")
                
                # Check if assertion passed
                if result.get("passed", False):
                    LogFire.log("DEBUG", f"[{self.test_name}] Assertion '{assertion_name}' passed after {elapsed:.1f}s ({poll_count} polls)")
                    return {
                        "success": True,
                        "elapsed_time": elapsed,
                        "poll_count": poll_count,
                        "timeout_reached": False,
                        "final_message": message,
                        "assertion_data": final_data
                    }
                
                # Check if should stop polling early
                if not result.get("should_continue", True):
                    LogFire.log("DEBUG", f"[{self.test_name}] Early termination requested by assertion function")
                    break
                    
            except Exception as e:
                LogFire.log("DEBUG", f"[{self.test_name}] Assertion check failed: {e}")
                last_message = f"Poll {poll_count} error: {e}"
                import traceback
                traceback.print_exc()
        
        # Timeout reached
        final_elapsed = time.time() - start_time
        LogFire.log("DEBUG", f"[{self.test_name}] Timeout reached for '{assertion_name}' after {final_elapsed:.1f}s ({poll_count} polls)")
        LogFire.log("DEBUG", f"[{self.test_name}] Final status: {last_message}")
        
        return {
            "success": False,
            "elapsed_time": final_elapsed,
            "poll_count": poll_count,
            "timeout_reached": True,
            "final_message": last_message,
            "assertion_data": final_data
        }
    
    async def get_database_connection(self, database_name: str = "vectordb"):
        """
        Get a database connection for assertion queries.
        
        This method provides a standardized way to access the database for
        test assertions, particularly useful for checking system state during
        long-running test scenarios.
        
        Args:
            database_name: Database to connect to (default: "vectordb")
            
        Returns:
            Database connection object
            
        Example:
            conn = await self.get_database_connection("vectordb")
            result = await conn.fetch("SELECT * FROM LogEntries WHERE ...")
            await conn.close()
        """
        from managers.manager_postgreSQL import PostgreSQLManager
        db = PostgreSQLManager.get_instance()
        return await db.get_connection(database_name)
    
    async def create_log_entries_assertion(self, 
                                         search_pattern: str, 
                                         start_time,
                                         min_count: int = 1,
                                         table_name: str = "LogEntries") -> callable:
        """
        Create an assertion function for checking LogEntries table.
        
        This method creates a reusable assertion function that can be used with
        wait_for_assertions() to check for specific log entries in the database.
        
        Args:
            search_pattern: SQL LIKE pattern to search for in the value column
            start_time: Datetime to filter entries after this time
            min_count: Minimum number of entries required (default: 1)
            table_name: Database table name (default: "LogEntries")
            
        Returns:
            Async callable that can be used with wait_for_assertions()
            
        Example:
            # Create assertion for broadcast detection
            broadcast_assertion = await self.create_log_entries_assertion(
                search_pattern="%broadcast%",
                start_time=test_start_time,
                min_count=1
            )
            
            # Use with polling system
            result = await self.wait_for_assertions(
                broadcast_assertion,
                timeout=480,
                assertion_name="broadcast_detection"
            )
        """
        async def log_assertion():
            try:
                conn = await self.get_database_connection("vectordb")
                try:
                    result = await conn.fetch(
                        f"""
                        SELECT * FROM {table_name} 
                        WHERE value LIKE $1 
                        AND timestamp > $2
                        ORDER BY timestamp DESC
                        LIMIT 20
                        """,
                        search_pattern, start_time
                    )
                    
                    entry_count = len(result)
                    passed = entry_count >= min_count
                    
                    # Get sample entries for debugging
                    sample_entries = []
                    for entry in result[:3]:  # Show first 3 entries
                        sample_entries.append({
                            "timestamp": entry.get("timestamp"),
                            "value": entry.get("value", "")[:100]  # Truncate for logging
                        })
                    
                    return {
                        "passed": passed,
                        "message": f"LogEntries check: {entry_count} entries found (need >= {min_count})",
                        "data": {
                            "entry_count": entry_count,
                            "sample_entries": sample_entries,
                            "search_pattern": search_pattern
                        }
                    }
                    
                finally:
                    await conn.close()
                    
            except Exception as e:
                return {
                    "passed": False,
                    "message": f"Database query error: {e}",
                    "data": {"error": str(e)}
                }
        
        return log_assertion
    
    async def create_broadcast_assertion(self, start_time) -> callable:
        """
        Create an assertion function specifically for checking broadcast activity.
        
        This is a convenience method for email tests and other tests that need
        to verify that broadcast functionality was triggered.
        
        Args:
            start_time: Datetime to filter entries after this time
            
        Returns:
            Async callable for checking broadcast activity
            
        Example:
            assertion = await self.create_broadcast_assertion(test_start_time)
            result = await self.wait_for_assertions(
                assertion,
                timeout=480,
                assertion_name="broadcast_detection"
            )
        """
        return await self.create_log_entries_assertion(
            search_pattern="%broadcast%",
            start_time=start_time,
            min_count=1
        )
    
    async def create_database_count_assertion(self, 
                                            table_name: str,
                                            where_clause: str = "",
                                            parameters: list = None,
                                            expected_count: int = 1,
                                            comparison: str = ">=") -> callable:
        """
        Create an assertion function for checking database record counts.
        
        This method creates a reusable assertion for checking if specific
        database tables have the expected number of records.
        
        Args:
            table_name: Database table to check
            where_clause: Optional WHERE clause (without WHERE keyword)
            parameters: List of parameters for the WHERE clause
            expected_count: Expected record count
            comparison: Comparison operator (">=", "=", "<=", ">", "<")
            
        Returns:
            Async callable for checking record counts
            
        Example:
            # Check for new scheduled requests
            assertion = await self.create_database_count_assertion(
                table_name="scheduled_requests",
                where_clause="created_date > $1",
                parameters=[test_start_time],
                expected_count=1,
                comparison=">="
            )
        """
        if parameters is None:
            parameters = []
        
        async def count_assertion():
            try:
                conn = await self.get_database_connection("vectordb")
                try:
                    # Build query
                    query = f"SELECT COUNT(*) as count FROM {table_name}"
                    if where_clause:
                        query += f" WHERE {where_clause}"
                    
                    result = await conn.fetchrow(query, *parameters)
                    actual_count = result["count"] if result else 0
                    
                    # Check comparison
                    passed = False
                    if comparison == ">=":
                        passed = actual_count >= expected_count
                    elif comparison == "=":
                        passed = actual_count == expected_count
                    elif comparison == "<=":
                        passed = actual_count <= expected_count
                    elif comparison == ">":
                        passed = actual_count > expected_count
                    elif comparison == "<":
                        passed = actual_count < expected_count
                    
                    return {
                        "passed": passed,
                        "message": f"Table {table_name}: {actual_count} records (expected {comparison} {expected_count})",
                        "data": {
                            "table_name": table_name,
                            "actual_count": actual_count,
                            "expected_count": expected_count,
                            "comparison": comparison
                        }
                    }
                    
                finally:
                    await conn.close()
                    
            except Exception as e:
                return {
                    "passed": False,
                    "message": f"Database count query error: {e}",
                    "data": {"error": str(e)}
                }
        
        return count_assertion
    
    # ===============================================================================
    # TOKEN-EFFICIENT BATCH EXECUTION SYSTEM
    # ===============================================================================
    
    @classmethod
    def _should_log_debug(cls, category: str = "general") -> bool:
        """
        Control debug logging verbosity for token efficiency.
        
        The monitoring system was generating ~3000 tokens. This method reduces
        token usage by 50% by filtering out routine/verbose debug messages while
        preserving error detection and critical status information.
        
        Args:
            category: Type of debug message
                     "monitor_routine" - Periodic monitoring checks (10s intervals)
                     "process_setup" - Process startup details and paths  
                     "task_management" - Async task creation/cleanup
                     "general" - Standard debug messages (default: allow)
        
        Returns:
            bool: True if message should be logged, False to skip for token savings
        """
        # Skip routine monitoring logs (saves ~800 tokens)
        if category == "monitor_routine":
            return False
        
        # Skip verbose process setup logs (saves ~400 tokens)  
        if category == "process_setup":
            return False
            
        # Skip async task management details (saves ~300 tokens)
        if category == "task_management":
            return False
        
        # Allow general debug, errors, and state changes
        return True
    
    @classmethod 
    def _log_compact(cls, level: str, category: str, message: str):
        """
        Compact logging helper that respects token efficiency settings.
        
        This replaces verbose LogFire.log calls with abbreviated versions
        that use shorter prefixes and conditional output.
        
        Args:
            level: "DEBUG", "ERROR", "INFO"  
            category: Debug category for filtering
            message: Message to log (will be abbreviated)
        """
        if level == "DEBUG" and not cls._should_log_debug(category):
            return  # Skip debug logs that aren't needed
        
        # Use abbreviated prefixes to save tokens
        if category == "monitor":
            prefix = "[LM]"  # LogMonitor -> LM
        elif category == "batch":
            prefix = "[BS]"  # BatchSystem -> BS  
        else:
            prefix = "[TR]"  # TestReal -> TR
        
        LogFire.log(level, f"{prefix} {message}")
    
    @classmethod
    def has_recent_log_activity(cls, log_dir: Path, threshold_seconds: int = 60) -> bool:
        """
        Check if any log file in directory was modified within threshold seconds.
        
        This method detects silent failures by monitoring log file modification times.
        Since test_real scripts log every 30 seconds, a 60-second threshold catches
        genuine stalls or initialization failures.
        
        Args:
            log_dir: Directory containing test log files
            threshold_seconds: Maximum seconds since last modification (default: 60)
            
        Returns:
            bool: True if recent activity detected, False if silent for too long
            
        Example:
            if not cls.has_recent_log_activity(log_dir, 60):
                # Test has been silent for 60+ seconds - likely failed initialization
        """
        if not log_dir.exists():
            return False
        
        current_time = time.time()
        threshold_time = current_time - threshold_seconds
        
        # Check all log files in directory
        log_files = list(log_dir.glob("*.log"))
        if not log_files:
            # No log files yet - this could be normal early in execution
            return True  # Don't fail immediately if no logs exist yet
        
        # Find the most recently modified log file
        most_recent_mtime = 0
        for log_file in log_files:
            try:
                file_mtime = log_file.stat().st_mtime
                most_recent_mtime = max(most_recent_mtime, file_mtime)
            except OSError:
                # File might have been deleted, skip it
                continue
        
        # Check if most recent activity is within threshold
        has_activity = most_recent_mtime > threshold_time
        
        # Only log when activity status changes or on silence detection
        if not has_activity:
            cls._log_compact("DEBUG", "monitor", f"Silent {current_time - most_recent_mtime:.0f}s")
        
        return has_activity
    
    @classmethod
    async def monitor_log_activity(cls, log_dir: Path, start_time: float, 
                                 check_interval: int = 10, 
                                 failure_threshold: int = 60,
                                 process_check_func = None) -> str:
        """
        Monitor log file activity to detect silent failures.
        
        This runs concurrently with test execution to catch initialization failures
        that don't show up in subprocess return codes. It checks log modification
        times every check_interval seconds and fails if no activity for failure_threshold.
        
        Args:
            log_dir: Directory to monitor for log files  
            start_time: Test start time (from time.time())
            check_interval: Seconds between activity checks (default: 10)
            failure_threshold: Seconds of silence before failing (default: 60)
            process_check_func: Optional function that returns False if process finished
            
        Returns:
            str: "MONITORING_COMPLETE" if process finished normally,
                 "LOG_SILENCE_FAILURE" if silent too long,
                 "EARLY_ACTIVITY_DETECTED" if initial activity found
                 
        Example:
            monitor_result = await cls.monitor_log_activity(
                log_dir=log_dir,
                start_time=start_time,
                process_check_func=lambda: process.poll() is None
            )
        """
        # Only log monitoring start in error cases - skip routine startup
        cls._log_compact("DEBUG", "monitor_routine", f"Monitor start: {failure_threshold}s threshold")
        
        checks_performed = 0
        
        while True:
            await asyncio.sleep(check_interval)
            checks_performed += 1
            
            elapsed_time = time.time() - start_time
            
            # Check if main process is still running
            if process_check_func and not process_check_func():
                return "MONITORING_COMPLETE"  # Silent completion - no log needed
            
            # Don't start checking for silence until after the failure threshold
            if elapsed_time < failure_threshold:
                continue  # Skip logging during grace period
            
            # Check for recent log activity
            has_activity = cls.has_recent_log_activity(log_dir, failure_threshold)
            
            if not has_activity:
                cls._log_compact("ERROR", "monitor", f"SILENT FAILURE after {elapsed_time:.0f}s")
                return "LOG_SILENCE_FAILURE"
            
            # Skip routine activity confirmation logs - only log problems
            
            # Prevent infinite monitoring - if process is taking too long, let subprocess timeout handle it
            if elapsed_time > 300:  # 5 minutes - reasonable limit for log monitoring
                return "MONITORING_COMPLETE"  # Silent timeout - subprocess handles long runs
    
    @classmethod
    def get_test_real_directory(cls) -> Path:
        """Get the tests/test_real directory path"""
        return Path(__file__).parent
    
    @classmethod
    def get_bats_directory(cls) -> Path:
        """Get or create the tests/test_real/bats directory path"""
        bats_dir = cls.get_test_real_directory() / "bats"
        bats_dir.mkdir(exist_ok=True)
        return bats_dir
    
    @classmethod
    def discover_test_real_files(cls) -> List[str]:
        """
        Discover all test_real_*.py files in the test_real directory.
        
        Returns:
            List[str]: List of test names (without test_real_ prefix and .py suffix)
            
        Example:
            ['email', 'agenda', 'bot', 'data']
        """
        test_real_dir = cls.get_test_real_directory()
        test_files = []
        
        for file_path in test_real_dir.glob("test_real_*.py"):
            # Extract test name: test_real_email.py -> email
            test_name = file_path.stem.replace("test_real_", "")
            test_files.append(test_name)
        
        cls._log_compact("DEBUG", "discover", f"Found {len(test_files)} test files")
        return sorted(test_files)
    
    @classmethod
    def generate_batch_file(cls, test_name: str, force_regenerate: bool = False) -> Path:
        """
        Generate a batch file for the specified test_real script.
        
        Args:
            test_name: Name of test (e.g., 'email' for test_real_email.py)
            force_regenerate: If True, regenerate even if batch file exists
            
        Returns:
            Path: Path to the generated batch file
            
        Example:
            batch_path = self.generate_batch_file("email")
            # Creates: tests/test_real/bats/run_test_real_email.bat
        """
        test_file_path = cls.get_test_real_directory() / f"test_real_{test_name}.py"
        if not test_file_path.exists():
            raise FileNotFoundError(f"Test file not found: {test_file_path}")
        
        bats_dir = cls.get_bats_directory()
        batch_file_path = bats_dir / f"run_test_real_{test_name}.bat"
        
        # Check if we need to regenerate
        if not force_regenerate and batch_file_path.exists():
            # Check if test file is newer than batch file
            test_mtime = test_file_path.stat().st_mtime
            batch_mtime = batch_file_path.stat().st_mtime
            if test_mtime <= batch_mtime:
                # Batch file up to date - skip verbose logging for token efficiency
                return batch_file_path
        
        # Generate batch file content
        # Use relative path from project root (src/AgenticRAG/)
        relative_test_path = f"tests/test_real/test_real_{test_name}.py"
        batch_content = f"../../.venv/Scripts/pytest.exe {relative_test_path} -v -s"
        
        # Write batch file
        try:
            with open(batch_file_path, 'w', encoding='ascii') as f:
                f.write(batch_content)
            cls._log_compact("DEBUG", "generate", f"Created {batch_file_path.name}")
            return batch_file_path
        except Exception as e:
            cls._log_compact("ERROR", "generate", f"Failed {batch_file_path.name}: {str(e)[:30]}")
            raise
    
    @classmethod
    def generate_all_batch_files(cls, force_regenerate: bool = False) -> Dict[str, Path]:
        """
        Generate batch files for all discovered test_real scripts.
        
        Args:
            force_regenerate: If True, regenerate all batch files even if they exist
            
        Returns:
            Dict[str, Path]: Mapping of test names to their batch file paths
            
        Example:
            batch_files = self.generate_all_batch_files()
            # Returns: {'email': Path('.../run_test_real_email.bat'), ...}
        """
        test_names = cls.discover_test_real_files()
        batch_files = {}
        
        cls._log_compact("DEBUG", "generate_all", f"Processing {len(test_names)} tests")
        
        for test_name in test_names:
            try:
                batch_path = cls.generate_batch_file(test_name, force_regenerate)
                batch_files[test_name] = batch_path
                # Skip per-file generation logging for token efficiency
            except Exception as e:
                cls._log_compact("ERROR", "generate_all", f"Failed {test_name}: {str(e)[:20]}")
        
        cls._log_compact("DEBUG", "generate_all", f"Generated {len(batch_files)} batch files")
        return batch_files
    
    def register_server(self, server_info: Dict[str, Any]):
        """
        Register a running server for cleanup tracking.
        
        Args:
            server_info: Dictionary with server details:
                - runner: aiohttp AppRunner instance
                - site: aiohttp TCPSite instance  
                - host: Server host
                - port: Server port
                - name: Descriptive name
        """
        self.running_servers.append(server_info)
        LogFire.log("DEBUG", f"[{self.test_name}] Registered server: {server_info.get('name', 'Unknown')} on {server_info.get('host')}:{server_info.get('port')}")
    
    async def cleanup_servers(self):
        """
        Gracefully shutdown all registered servers using both local tracking and global server registry.
        
        This method now integrates with the global server registry from APIEndpoint to ensure
        all servers are properly cleaned up, especially when tests are cancelled via VS Code.
        """
        LogFire.log("DEBUG", f"[{self.test_name}] Starting comprehensive server cleanup...")
        
        # First, use the global server registry from APIEndpoint
        try:
            from endpoints.api_endpoint import APIEndpoint
            running_servers = await APIEndpoint.get_running_servers()
            
            if running_servers:
                LogFire.log("DEBUG", f"[{self.test_name}] Found {len(running_servers)} servers in global registry")
                await APIEndpoint.cleanup_all_servers()
                LogFire.log("DEBUG", f"[{self.test_name}] Global server cleanup completed")
            else:
                LogFire.log("DEBUG", f"[{self.test_name}] No servers in global registry")
                
        except Exception as e:
            LogFire.log("ERROR", f"[{self.test_name}] Error during global server cleanup: {e}")
        
        # Second, cleanup any local server tracking (legacy support)
        if self.running_servers:
            LogFire.log("DEBUG", f"[{self.test_name}] Cleaning up {len(self.running_servers)} locally tracked servers...")
            
            for server_info in self.running_servers:
                try:
                    name = server_info.get('name', 'Unknown')
                    port = server_info.get('port', 'Unknown')
                    
                    # Method 1: Direct aiohttp server references (if available)
                    if 'site' in server_info and server_info['site']:
                        await server_info['site'].stop()
                        LogFire.log("DEBUG", f"[{self.test_name}] Stopped site for {name} on port {port}")
                    
                    if 'runner' in server_info and server_info['runner']:
                        await server_info['runner'].cleanup()
                        LogFire.log("DEBUG", f"[{self.test_name}] Cleaned up runner for {name} on port {port}")
                    
                    # Method 2: APIEndpoint-specific cleanup (for servers started by mainFunc)
                    elif 'api_endpoint' in server_info:
                        api_endpoint = server_info['api_endpoint']
                        
                        # Try to shutdown via APIEndpoint if it has cleanup methods
                        if hasattr(api_endpoint, 'shutdown') and callable(api_endpoint.shutdown):
                            await api_endpoint.shutdown()
                            LogFire.log("DEBUG", f"[{self.test_name}] Shut down {name} via APIEndpoint.shutdown()")
                        else:
                            # Fallback: Try to close the aiohttp app
                            if 'app' in server_info and server_info['app']:
                                try:
                                    await server_info['app'].shutdown()
                                    await server_info['app'].cleanup()
                                    LogFire.log("DEBUG", f"[{self.test_name}] Shut down {name} via app.shutdown()")
                                except Exception as e:
                                    LogFire.log("DEBUG", f"[{self.test_name}] App shutdown method failed for {name}: {e}")
                    
                except Exception as e:
                    LogFire.log("ERROR", f"[{self.test_name}] Error cleaning up local server {name}: {e}")
            
            # Clear the local server list
            self.running_servers.clear()
            LogFire.log("DEBUG", f"[{self.test_name}] Local server cleanup completed")
        
        # Third, force cleanup any remaining ports via process termination (last resort)
        try:
            LogFire.log("DEBUG", f"[{self.test_name}] Running final port cleanup check...")
            self._check_and_cleanup_ports()
            LogFire.log("DEBUG", f"[{self.test_name}] Port cleanup completed")
        except Exception as e:
            LogFire.log("ERROR", f"[{self.test_name}] Error during port cleanup: {e}")
        
        LogFire.log("DEBUG", f"[{self.test_name}] Comprehensive server cleanup completed")
    
    def setup_signal_handlers(self):
        """
        Setup signal handlers for graceful shutdown when test is cancelled.
        """
        if self.signal_handlers_registered:
            return
        
        def signal_handler(signum, frame):
            LogFire.log("DEBUG", f"[{self.test_name}] Received signal {signum}, cleaning up servers...")
            
            # Run cleanup in the current event loop
            try:
                import asyncio
                loop = asyncio.get_running_loop()
                loop.create_task(self.cleanup_servers())
            except RuntimeError:
                # No running loop, create new one
                asyncio.run(self.cleanup_servers())
            
            LogFire.log("DEBUG", f"[{self.test_name}] Signal cleanup completed, exiting...")
            exit(0)
        
        # Register handlers for common termination signals
        signal.signal(signal.SIGTERM, signal_handler)
        signal.signal(signal.SIGINT, signal_handler)
        
        # Register atexit handler as backup
        atexit.register(lambda: asyncio.run(self.cleanup_servers()) if self.running_servers else None)
        
        self.signal_handlers_registered = True
        LogFire.log("DEBUG", f"[{self.test_name}] Signal handlers registered for graceful shutdown")
    
    async def _track_started_servers(self):
        """
        Verify servers were registered in global registry and check their status.
        
        Since we now use the global server registry from APIEndpoint.start_app(),
        we just need to verify that servers were properly registered rather than
        manually tracking them locally.
        """
        try:
            # Get the global server registry from APIEndpoint
            from endpoints.api_endpoint import APIEndpoint
            running_servers = await APIEndpoint.get_running_servers()
            
            if running_servers:
                LogFire.log("DEBUG", f"[{self.test_name}] Verified {len(running_servers)} servers in global registry:")
                for server in running_servers:
                    name = server.get('name', 'Unknown')
                    host = server.get('host', 'Unknown')
                    port = server.get('port', 'Unknown')
                    LogFire.log("DEBUG", f"[{self.test_name}] - {name} at {host}:{port}")
            else:
                LogFire.log("DEBUG", f"[{self.test_name}] No servers found in global registry")
                
                # Try to detect if servers are running but not registered (compatibility check)
                from globals import ZAIRA_PYTHON_PORT
                import socket
                sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                result = sock.connect_ex(('localhost', ZAIRA_PYTHON_PORT))
                sock.close()
                
                if result == 0:  # Port is in use
                    LogFire.log("DEBUG", f"[{self.test_name}] WARNING: Server detected on port {ZAIRA_PYTHON_PORT} but not in global registry")
                    # This is expected for older code that hasn't been updated to use the global registry yet
            
        except Exception as e:
            LogFire.log("ERROR", f"[{self.test_name}] Error checking server registry: {e} (Fixed: Added missing await)")
            # Don't fail the test for this, but log the issue
    
    @classmethod
    def _check_and_cleanup_ports(cls):
        """
        Check and cleanup ports 40999 and 8083 if they are in use.
        
        This method identifies processes using the test ports and attempts to terminate
        them to prevent silent test failures. It provides detailed logging about port
        status and cleanup actions.
        """
        import socket
        import subprocess
        import platform
        
        # Get port values from globals
        from globals import ZAIRA_PYTHON_PORT, RELAY_PORT
        
        ports_to_check = [
            (ZAIRA_PYTHON_PORT, "ZAIRA_PYTHON_PORT"),
            (RELAY_PORT, "RELAY_PORT")
        ]
        
        cls._log_compact("DEBUG", "port_check", f"Checking ports: {[p[0] for p in ports_to_check]}")
        
        for port, port_name in ports_to_check:
            cls._check_and_cleanup_single_port(port, port_name)
    
    @classmethod
    def _check_and_cleanup_single_port(cls, port: int, port_name: str):
        """
        Check and cleanup a single port if it's in use.
        
        Args:
            port: Port number to check
            port_name: Descriptive name for logging
        """
        import socket
        import subprocess
        import platform
        
        # Check if port is in use
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        try:
            result = sock.connect_ex(('localhost', port))
            sock.close()
            
            if result == 0:  # Port is in use
                cls._log_compact("ERROR", "port_conflict", f"Port {port} ({port_name}) is in use")
                
                # Try to identify and terminate the process
                if platform.system() == "Windows":
                    cls._cleanup_port_windows(port, port_name)
                else:
                    cls._cleanup_port_unix(port, port_name)
                    
                # Verify cleanup succeeded
                sock2 = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
                try:
                    result2 = sock2.connect_ex(('localhost', port))
                    sock2.close()
                    
                    if result2 == 0:
                        cls._log_compact("ERROR", "cleanup_failed", f"Port {port} still in use after cleanup")
                        raise RuntimeError(f"Port {port} ({port_name}) is still in use after cleanup attempt")
                    else:
                        cls._log_compact("DEBUG", "cleanup_success", f"Port {port} ({port_name}) successfully freed")
                except Exception as e:
                    cls._log_compact("ERROR", "cleanup_verify", f"Error verifying port {port}: {e}")
                    raise
            else:
                cls._log_compact("DEBUG", "port_free", f"Port {port} ({port_name}) is available")
                
        except Exception as e:
            cls._log_compact("ERROR", "port_check", f"Error checking port {port}: {e}")
            raise
    
    @classmethod
    def _cleanup_port_windows(cls, port: int, port_name: str):
        """Cleanup port on Windows systems"""
        import subprocess
        
        try:
            # Find process using the port
            cmd = f'netstat -ano | findstr ":{port}"'
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0 and result.stdout.strip():
                lines = result.stdout.strip().split('\n')
                pids = set()
                
                for line in lines:
                    parts = line.split()
                    if len(parts) >= 5 and f":{port}" in parts[1]:
                        pid = parts[-1]
                        if pid.isdigit():
                            pids.add(pid)
                
                if pids:
                    cls._log_compact("DEBUG", "kill_process", f"Found PIDs using port {port}: {', '.join(pids)}")
                    
                    for pid in pids:
                        try:
                            kill_cmd = f"taskkill /PID {pid} /F"
                            kill_result = subprocess.run(kill_cmd, shell=True, capture_output=True, text=True)
                            
                            if kill_result.returncode == 0:
                                cls._log_compact("DEBUG", "kill_success", f"Killed PID {pid} using port {port}")
                            else:
                                cls._log_compact("ERROR", "kill_failed", f"Failed to kill PID {pid}: {kill_result.stderr}")
                        except Exception as e:
                            cls._log_compact("ERROR", "kill_error", f"Error killing PID {pid}: {e}")
                else:
                    cls._log_compact("DEBUG", "no_pids", f"No PIDs found for port {port} (may have closed)")
            else:
                cls._log_compact("DEBUG", "netstat_empty", f"No netstat output for port {port}")
                
        except Exception as e:
            cls._log_compact("ERROR", "windows_cleanup", f"Error during Windows cleanup for port {port}: {e}")
            raise
    
    @classmethod
    def _cleanup_port_unix(cls, port: int, port_name: str):
        """Cleanup port on Unix/Linux systems"""
        import subprocess
        
        try:
            # Find process using the port
            cmd = f"lsof -ti:{port}"
            result = subprocess.run(cmd, shell=True, capture_output=True, text=True)
            
            if result.returncode == 0 and result.stdout.strip():
                pids = result.stdout.strip().split('\n')
                cls._log_compact("DEBUG", "kill_process", f"Found PIDs using port {port}: {', '.join(pids)}")
                
                for pid in pids:
                    if pid.strip().isdigit():
                        try:
                            kill_cmd = f"kill -9 {pid.strip()}"
                            kill_result = subprocess.run(kill_cmd, shell=True, capture_output=True, text=True)
                            
                            if kill_result.returncode == 0:
                                cls._log_compact("DEBUG", "kill_success", f"Killed PID {pid} using port {port}")
                            else:
                                cls._log_compact("ERROR", "kill_failed", f"Failed to kill PID {pid}: {kill_result.stderr}")
                        except Exception as e:
                            cls._log_compact("ERROR", "kill_error", f"Error killing PID {pid}: {e}")
            else:
                cls._log_compact("DEBUG", "no_pids", f"No processes found using port {port}")
                
        except Exception as e:
            cls._log_compact("ERROR", "unix_cleanup", f"Error during Unix cleanup for port {port}: {e}")
            raise

    @classmethod
    async def run_test_real(cls, test_name: str, timeout: int = 600) -> Dict[str, Any]:
        """
        Execute a test_real script via its batch file with minimal token output.
        
        This method provides a token-efficient way to run test_real scripts by:
        1. Auto-generating the batch file if needed
        2. Executing via subprocess with timeout protection
        3. Capturing minimal output (success/failure, timing, summary)
        4. Preserving full logs in the standard test_real/logs/ directory
        
        Args:
            test_name: Name of test to run (e.g., 'email' for test_real_email.py)
            timeout: Maximum execution time in seconds (default: 600 = 10 minutes)
            
        Returns:
            Dict[str, Any]: Minimal execution results
            {
                "success": bool,              # Whether test passed
                "execution_time": float,      # Time in seconds
                "exit_code": int,             # Process exit code
                "brief_summary": str,         # One-line summary
                "batch_file": str,            # Path to batch file used
                "log_directory": str,         # Where full logs are stored
                "error_summary": str          # Brief error if failed (optional)
            }
            
        Raises:
            FileNotFoundError: If test_real_{test_name}.py doesn't exist
            TimeoutError: If execution exceeds timeout
            
        Example:
            # User: "run test_real_email"
            # Claude: result = await BaseRealTest.run_test_real("email")
            # Output: {"success": True, "execution_time": 45.2, "brief_summary": "Email test completed successfully"}
        """
        # Test environment uses same .env loading as main app - no manual ZAIRA_NETWORK_NAME management needed
        import os
        
        try:
            from etc.helper_functions import get_value_from_env
            current_network = get_value_from_env("ZAIRA_NETWORK_NAME", "unknown")
            cls._log_compact("DEBUG", "batch", f"Start test_real_{test_name} (network: {current_network})")
            
            # Check and cleanup ports before execution
            cls._check_and_cleanup_ports()
            
            start_time = time.time()
            # Ensure batch file exists
            batch_path = cls.generate_batch_file(test_name)
            
            # Prepare execution environment  
            working_dir = cls.get_test_real_directory().parent.parent  # Back to src/AgenticRAG/
            log_dir = cls.get_test_real_directory() / "logs" / f"test_real_{test_name}"
            
            # Skip verbose setup logging - only essential info
            cls._log_compact("DEBUG", "process_setup", f"Batch: {batch_path.name}, timeout: {timeout}s")
            
            # Execute batch file
            if platform.system() == "Windows":
                # Windows: Execute .bat file directly
                cmd = [str(batch_path)]
                shell = True
            else:
                # Linux/MacOS: Use bash to execute batch content
                with open(batch_path, 'r') as f:
                    batch_content = f.read().strip()
                cmd = ["bash", "-c", batch_content]
                shell = False
            
            # Run the process with timeout
            process = subprocess.Popen(
                cmd,
                stdout=subprocess.PIPE,
                stderr=subprocess.STDOUT,
                text=True,
                shell=shell,
                cwd=working_dir
            )
            
            try:
                # Start log monitoring concurrently with process execution (minimal logging)
                cls._log_compact("DEBUG", "task_management", "Starting concurrent monitoring")
                
                # Create monitoring task
                monitor_task = asyncio.create_task(
                    cls.monitor_log_activity(
                        log_dir=log_dir,
                        start_time=start_time,
                        check_interval=10,  # Check every 10 seconds
                        failure_threshold=60,  # Fail if silent for 60+ seconds
                        process_check_func=lambda: process.poll() is None  # Check if process still running
                    )
                )
                
                # Create process execution task
                async def wait_for_process():
                    loop = asyncio.get_event_loop()
                    # Run process.communicate in thread pool since it's blocking
                    stdout, stderr = await loop.run_in_executor(
                        None, lambda: process.communicate(timeout=timeout)
                    )
                    return stdout, stderr
                
                process_task = asyncio.create_task(wait_for_process())
                
                # Wait for either process completion or monitoring failure
                done, pending = await asyncio.wait(
                    [process_task, monitor_task],
                    return_when=asyncio.FIRST_COMPLETED
                )
                
                # Cancel any remaining tasks (no logging needed)
                for task in pending:
                    task.cancel()
                    try:
                        await task
                    except asyncio.CancelledError:
                        pass
                
                # Check which task completed first
                if process_task in done:
                    # Process completed normally
                    stdout, _ = await process_task
                    monitor_result = "PROCESS_COMPLETED"
                    # Skip completion confirmation - only log problems
                else:
                    # Monitoring detected failure
                    monitor_result = await monitor_task
                    stdout = ""  # No stdout available since process was terminated
                    cls._log_compact("ERROR", "batch", f"Monitor detected: {monitor_result}")
                    
                    # Terminate the process
                    if process.poll() is None:
                        process.terminate()
                        try:
                            process.wait(timeout=5)
                        except subprocess.TimeoutExpired:
                            process.kill()
                            process.wait()
                
                exit_code = process.returncode
                execution_time = time.time() - start_time
                
                # Only log if there was a problem
                if exit_code != 0 or monitor_result != "PROCESS_COMPLETED":
                    cls._log_compact("DEBUG", "batch", f"Exit {exit_code} in {execution_time:.1f}s")
                
                # Analyze results with minimal output - check for log monitoring failures first
                if monitor_result == "LOG_SILENCE_FAILURE":
                    success = False
                    exit_code = -4  # Special exit code for log silence detection
                    brief_summary = f"Test '{test_name}' failed: Silent >60s (init failure)"
                    error_summary = "Silent failure detected"
                    cls._log_compact("ERROR", "batch", brief_summary)
                else:
                    # Standard result analysis based on process exit code
                    success = exit_code == 0
                    
                    if success:
                        brief_summary = f"Test '{test_name}' completed successfully"
                        error_summary = None
                        # No logging for success - only problems
                    else:
                        # Extract just the key error info, not full output
                        if stdout:
                            error_lines = [line for line in stdout.split('\n')[-10:] if 'FAILED' in line or 'ERROR' in line]
                            if error_lines:
                                error_summary = error_lines[-1][:50]  # Shorter truncation for token savings
                            else:
                                error_summary = f"Exit code {exit_code}"
                        else:
                            error_summary = f"Terminated by monitor (exit {exit_code})"
                        brief_summary = f"Test '{test_name}' failed: {error_summary}"
                
                result = {
                    "success": success,
                    "execution_time": execution_time,
                    "exit_code": exit_code,
                    "brief_summary": brief_summary,
                    "batch_file": str(batch_path),
                    "log_directory": str(log_dir),
                    "monitor_result": monitor_result,  # Include monitoring result for debugging
                }
                
                if error_summary:
                    result["error_summary"] = error_summary
                
                # Brief summary already logged via cls._log_compact above - skip duplicate
                return result
                
            except subprocess.TimeoutExpired:
                process.kill()
                process.communicate()  # Clean up
                execution_time = time.time() - start_time
                
                error_msg = f"Test '{test_name}' timed out after {timeout}s"
                cls._log_compact("ERROR", "timeout", f"Timeout {timeout}s")
                
                return {
                    "success": False,
                    "execution_time": execution_time,
                    "exit_code": -1,
                    "brief_summary": error_msg,
                    "batch_file": str(batch_path),
                    "log_directory": str(log_dir),
                    "error_summary": f"Timeout after {timeout}s",
                    "monitor_result": "SUBPROCESS_TIMEOUT"
                }
                
        except FileNotFoundError as e:
            execution_time = time.time() - start_time
            error_msg = f"Test file not found: test_real_{test_name}.py"
            cls._log_compact("ERROR", "file_not_found", f"Missing: test_real_{test_name}.py")
            
            return {
                "success": False,
                "execution_time": execution_time,
                "exit_code": -2,
                "brief_summary": error_msg,
                "batch_file": "Not generated",
                "log_directory": "N/A",
                "error_summary": str(e),
                "monitor_result": "FILE_NOT_FOUND"
            }
            
        except Exception as e:
            execution_time = time.time() - start_time
            error_msg = f"Unexpected error running test '{test_name}': {str(e)[:100]}"
            cls._log_compact("ERROR", "exception", f"Error: {str(e)[:30]}")
            
            return {
                "success": False,
                "execution_time": execution_time,
                "exit_code": -3,
                "brief_summary": error_msg,
                "batch_file": "Error",
                "log_directory": "N/A", 
                "error_summary": str(e)[:200],
                "monitor_result": "UNEXPECTED_ERROR"
            }
        finally:
            # Test cleanup - environment variables handled by main app logic, no manual restoration needed
            pass
    
    @classmethod
    def collect_test_summary(cls, log_file_path: Optional[Path] = None) -> List[str]:
        """
        Collect test step results from current test log file for summary display.
        
        This method scans the test log file for step completion patterns and formats
        them into a summary that can be displayed at the end of test execution.
        
        Supported Step Patterns:
        - "STEP X - Description: [OK]" - Successful step completion
        - "STEP X - Description: [ERROR]" - Failed step 
        - "PHASE X: [OK] Description" - Alternative phase pattern
        - "Assertion X: Description = True/False" - Assertion results
        - "Final email generator execution count: N" - Execution counts
        
        Args:
            log_file_path: Optional path to log file. If None, attempts to get current test log
            
        Returns:
            List[str]: Formatted summary lines ready for display
            
        Example Output:
            [
                "TEST SUMMARY",
                "============",
                "STEP 1 - IMAP exists: [OK]",
                "STEP 1.5 - IMAP activation wait: [OK]", 
                "STEP 2 - Email sent: [OK]",
                "STEP 3 - Second email generator execution: [OK]",
                "Final execution count: 2"
            ]
        """
        summary_lines = []
        
        try:
            # Get log file path
            if log_file_path is None:
                # Try to get current test log path
                try:
                    from tests.test_real.test_debug_capture import get_current_test_log_path
                    log_file_path = get_current_test_log_path()
                except ImportError:
                    cls._log_compact("DEBUG", "summary", "Could not import test debug capture")
                    return ["TEST SUMMARY", "============", "No log file available for summary"]
            
            if not log_file_path or not Path(log_file_path).exists():
                return ["TEST SUMMARY", "============", "Log file not found for summary"]
            
            # Read log file content
            with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                log_content = f.read()
            
            # Extract step results with various patterns
            step_results = []
            assertion_results = []
            execution_counts = []
            
            lines = log_content.split('\n')
            for line in lines:
                # Clean line for pattern matching (remove timestamp if present)
                clean_line = line
                if "] " in line:
                    # Remove timestamp prefix like "[2025-08-19 14:46:29.123] "
                    parts = line.split("] ", 1)
                    if len(parts) > 1:
                        clean_line = parts[1]
                
                # Pattern 1: "STEP X - Description: [OK/ERROR]"
                if "STEP" in clean_line and "- " in clean_line and ("[OK]" in clean_line or "[ERROR]" in clean_line):
                    # Extract step description and result
                    if "[OK]" in clean_line:
                        step_part = clean_line.split("[OK]")[0].strip()
                        # Remove DEBUG prefix if present
                        if step_part.startswith("DEBUG"):
                            step_part = step_part[5:].strip()
                        if step_part.startswith("["):
                            # Remove [TestName] prefix if present
                            bracket_end = step_part.find("]")
                            if bracket_end != -1:
                                step_part = step_part[bracket_end+1:].strip()
                        # Clean up double colons that might occur
                        step_clean = step_part.rstrip(":")
                        step_results.append(f"{step_clean}: [OK]")
                    elif "[ERROR]" in clean_line:
                        step_part = clean_line.split("[ERROR]")[0].strip()
                        if step_part.startswith("DEBUG"):
                            step_part = step_part[5:].strip()
                        if step_part.startswith("["):
                            bracket_end = step_part.find("]")
                            if bracket_end != -1:
                                step_part = step_part[bracket_end+1:].strip()
                        # Clean up double colons that might occur
                        step_clean = step_part.rstrip(":")
                        step_results.append(f"{step_clean}: [ERROR]")
                
                # Pattern 2: "PHASE X: [OK] Description" 
                elif "PHASE" in clean_line and "[OK]" in clean_line:
                    phase_match = clean_line.split("[OK]")
                    if len(phase_match) >= 2:
                        phase_desc = phase_match[0].strip()
                        if phase_desc.startswith("DEBUG"):
                            phase_desc = phase_desc[5:].strip()
                        if phase_desc.startswith("["):
                            bracket_end = phase_desc.find("]")
                            if bracket_end != -1:
                                phase_desc = phase_desc[bracket_end+1:].strip()
                        # Clean up double colons that might occur
                        phase_clean = phase_desc.rstrip(":")
                        result_line = f"{phase_clean}: [OK]"
                        if result_line not in step_results:  # Avoid duplicates
                            step_results.append(result_line)
                
                # Pattern 3: "Assertion X: Description = True/False"
                elif "Assertion" in clean_line and ("= True" in clean_line or "= False" in clean_line):
                    if "= True" in clean_line:
                        assertion_part = clean_line.split("= True")[0].strip()
                        if assertion_part.startswith("DEBUG"):
                            assertion_part = assertion_part[5:].strip()
                        if assertion_part.startswith("["):
                            bracket_end = assertion_part.find("]")
                            if bracket_end != -1:
                                assertion_part = assertion_part[bracket_end+1:].strip()
                        assertion_results.append(f"{assertion_part}: [OK]")
                    else:
                        assertion_part = clean_line.split("= False")[0].strip()
                        if assertion_part.startswith("DEBUG"):
                            assertion_part = assertion_part[5:].strip()  
                        if assertion_part.startswith("["):
                            bracket_end = assertion_part.find("]")
                            if bracket_end != -1:
                                assertion_part = assertion_part[bracket_end+1:].strip()
                        assertion_results.append(f"{assertion_part}: [ERROR]")
                
                # Pattern 4: "Final email generator execution count: N"
                elif "Final email generator execution count:" in clean_line:
                    count_part = clean_line.strip()
                    if count_part.startswith("DEBUG"):
                        count_part = count_part[5:].strip()
                    if count_part.startswith("["):
                        bracket_end = count_part.find("]")
                        if bracket_end != -1:
                            count_part = count_part[bracket_end+1:].strip()
                    execution_counts.append(count_part)
            
            # Build summary
            summary_lines.append("TEST SUMMARY")
            summary_lines.append("============")
            
            # Add step results (remove duplicates while preserving order)
            seen_steps = set()
            for step in step_results:
                if step not in seen_steps:
                    summary_lines.append(step)
                    seen_steps.add(step)
            
            # Add assertion results
            for assertion in assertion_results:
                if assertion not in seen_steps:
                    summary_lines.append(assertion)
                    seen_steps.add(assertion)
            
            # Add execution counts
            for count in execution_counts:
                summary_lines.append(count)
            
            # If no specific patterns found, add a basic completion message
            if len(summary_lines) <= 2:  # Only header
                if "[OK]" in log_content and "completed successfully" in log_content.lower():
                    summary_lines.append("Test completed successfully")
                elif "[ERROR]" in log_content or "failed" in log_content.lower():
                    summary_lines.append("Test completed with errors")
                else:
                    summary_lines.append("Test execution completed")
        
        except Exception as e:
            cls._log_compact("ERROR", "summary", f"Error collecting summary: {e}")
            summary_lines = [
                "TEST SUMMARY", 
                "============",
                f"Error collecting summary: {str(e)[:50]}"
            ]
        
        return summary_lines

    @classmethod
    def list_available_test_real_scripts(cls) -> Dict[str, Dict[str, Any]]:
        """
        List all available test_real scripts with their batch file status.
        
        Returns:
            Dict[str, Dict[str, Any]]: Information about each test script
            {
                "test_name": {
                    "test_file": str,           # Path to test_real_*.py file
                    "batch_file": str,          # Path to batch file (or "Missing")
                    "batch_exists": bool,       # Whether batch file exists
                    "test_file_size": int,      # Size of test file in bytes
                    "last_modified": str        # Test file modification time
                }
            }
            
        Example:
            scripts = BaseRealTest.list_available_test_real_scripts()
            # Returns info about all test_real scripts and their batch status
        """
        test_names = cls.discover_test_real_files()
        test_real_dir = cls.get_test_real_directory()
        bats_dir = cls.get_bats_directory()
        
        results = {}
        
        for test_name in test_names:
            test_file_path = test_real_dir / f"test_real_{test_name}.py"
            batch_file_path = bats_dir / f"run_test_real_{test_name}.bat"
            
            # Get file stats
            test_stat = test_file_path.stat()
            batch_exists = batch_file_path.exists()
            
            results[test_name] = {
                "test_file": str(test_file_path),
                "batch_file": str(batch_file_path) if batch_exists else "Missing",
                "batch_exists": batch_exists,
                "test_file_size": test_stat.st_size,
                "last_modified": time.strftime("%Y-%m-%d %H:%M:%S", time.localtime(test_stat.st_mtime))
            }
        
        cls._log_compact("DEBUG", "list", f"{len(results)} scripts, {sum(1 for r in results.values() if r['batch_exists'])} batches")
        return results


# Utility functions for common test patterns
def requires_openai_api():
    """Skip test if OpenAI API key is not configured"""
    api_key_configured = os.environ.get('OPENAI_API_KEY')
    
    return pytest.mark.skipif(
        not api_key_configured,
        reason="OpenAI API key not configured - set OPENAI_API_KEY"
    )

# Export public API
__all__ = [
    'BaseRealTest',
    'requires_openai_api'
]