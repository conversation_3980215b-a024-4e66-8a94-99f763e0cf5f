# imports.py

# Setup debug capture early to catch all output
try:
    # Check if we're in a test environment first
    import os
    import sys
    
    # Skip debug capture setup if we're in test_real environment
    is_test_real = any('test_real' in arg for arg in sys.argv) or 'test_real' in __file__ if hasattr(__builtins__, '__file__') else False
    is_pytest_env = 'pytest' in sys.modules or os.environ.get('TEST_CAPTURE_ACTIVE') == '1'
    
    
    if not is_test_real and not is_pytest_env:
        from etc.debug_stdout_capture import setup_debug_capture
        setup_debug_capture()
    else:
        pass  # No debug capture needed in production
except Exception as e:
    # Early import warning - use print since LogFire may not be available yet
    import traceback
    traceback.print_exc()

# ------------------------------------------------
# -            Standard library imports          -
# ------------------------------------------------

# ------------------------------------------------
# -             Third-party imports              -
# ------------------------------------------------

# ------------------------------------------------
# - Local application / library specific imports -
# ------------------------------------------------
import etc.helper_functions
# LogFire import must remain in imports for global access - if Claude detects problems with this line then fix the other end
from managers.manager_logfire import LogFire

# ------------------------------------------------
# -     Common functions or configurations       -
# ------------------------------------------------
from globals import *
from config import *
from etc.ZairaSettings import *
# LogFire import removed to prevent circular dependency
# Import LogFire directly in files that need it: from managers.manager_logfire import LogFire
