from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from managers.manager_logfire import LogFire
from tests.unit.test_logging_capture import with_unit_test_logging
from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest, ScheduleType
from userprofiles.ScheduleParsingTools import ParseRecurringScheduleTool, ParseDailyScheduleTool, ParseMonthlyScheduleTool, ParseGenericScheduleTool
from userprofiles.ZairaUser import PERMISSION_LEVELS
from endpoints.mybot_generic import MyBot_Generic
import pytest
import asyncio
from datetime import datetime, timedelta
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4

class TestScheduleParsingLLM:
    """Test LLM-based schedule parsing functionality"""
    
    def test_creation_with_llm_parsing(self):
        """Test that ScheduledZairaRequest creation works with LLM parsing"""
        # Create mock user and bot
        user = MagicMock()
        user.email = "<EMAIL>"
        user.real_name = "Test User"
        user.username = "test_user"
        user.rank = PERMISSION_LEVELS.USER
        user.GUID = uuid4()
        user.DeviceGUID = uuid4()
        
        bot = MagicMock()
        bot.name = "test_bot"
        
        # Test with a simple schedule prompt
        schedule_prompt = "send me a reminder in 5 minutes"
        
        # Create the task with pre-parsed parameters (parsing is now done by agents)
        task = ScheduledZairaRequest(
            user=user,
            calling_bot=bot,
            original_message=None,
            schedule_prompt=schedule_prompt,
            target_prompt="send reminder",
            start_delay_seconds=300.0,  # 5 minutes
            delay_seconds=0.0,
            schedule_type=ScheduleType.ONCE
        )
        
        # Verify the task was created successfully
        assert task.schedule_prompt == schedule_prompt
        assert task.is_active is True
        assert task.schedule_type == ScheduleType.ONCE
        assert task.delay_seconds == 0.0
        assert task.target_prompt == "send reminder"
    
    @pytest.mark.asyncio
    async def test_recurring_schedule_tool(self):
        """Test the ParseRecurringScheduleTool"""
        # Mock the LLM
        from etc.ZairaSettings import ZairaSettings
        mock_llm = AsyncMock()
        
        # Mock response for recurring pattern
        mock_response = MagicMock()
        mock_response.content = "TARGET_PROMPT: trigger IMAP IDLE\nDELAY_SECONDS: 1800\nSCHEDULE_TYPE: recurring"
        mock_llm.ainvoke.return_value = mock_response
        ZairaSettings.llm = mock_llm
        
        tool = ParseRecurringScheduleTool()
        
        # Test recurring pattern
        result = await tool._arun("trigger IMAP IDLE every 30 minutes")
        assert "TARGET_PROMPT:" in result
        assert "DELAY_SECONDS:" in result
        assert "SCHEDULE_TYPE: recurring" in result
        assert "1800" in result  # 30 minutes = 1800 seconds
        
        # Mock response for generic recurring pattern
        mock_response.content = "TARGET_PROMPT: check email\nDELAY_SECONDS: 7200\nSCHEDULE_TYPE: recurring"
        result = await tool._arun("check email every 2 hours")
        assert "TARGET_PROMPT:" in result
        assert "DELAY_SECONDS:" in result
        assert "SCHEDULE_TYPE: recurring" in result
        assert "7200" in result  # 2 hours = 7200 seconds
        
        # Mock response for no match
        mock_response.content = "No recurring pattern found."
        result = await tool._arun("send me a message at 9am")
        assert "No recurring pattern found" in result
    
    @pytest.mark.asyncio
    async def test_daily_schedule_tool(self):
        """Test the ParseDailyScheduleTool"""
        # Mock the LLM
        from etc.ZairaSettings import ZairaSettings
        mock_llm = AsyncMock()
        
        # Mock response for daily pattern
        mock_response = MagicMock()
        mock_response.content = "TARGET_PROMPT: send message: a good morning message\nSTART_DELAY_SECONDS: 43200\nDELAY_SECONDS: 86400\nSCHEDULE_TYPE: recurring"
        mock_llm.ainvoke.return_value = mock_response
        ZairaSettings.llm = mock_llm
        
        tool = ParseDailyScheduleTool()
        
        # Test daily pattern
        result = await tool._arun("send me a good morning message at 9am monday to friday")
        assert "TARGET_PROMPT:" in result
        assert "send message: a good morning message" in result
        assert "DELAY_SECONDS:" in result
        assert "SCHEDULE_TYPE: recurring" in result
        
        # Mock response for no match
        mock_response.content = "No daily pattern found."
        result = await tool._arun("check email every 30 minutes")
        assert "No daily pattern found" in result
    
    @pytest.mark.asyncio
    async def test_monthly_schedule_tool(self):
        """Test the ParseMonthlyScheduleTool"""
        # Mock the LLM
        from etc.ZairaSettings import ZairaSettings
        mock_llm = AsyncMock()
        
        # Mock response for monthly pattern
        mock_response = MagicMock()
        mock_response.content = "TARGET_PROMPT: email report: a report\nSTART_DELAY_SECONDS: 1209600\nDELAY_SECONDS: 2629746\nSCHEDULE_TYPE: recurring"
        mock_llm.ainvoke.return_value = mock_response
        ZairaSettings.llm = mock_llm
        
        tool = ParseMonthlyScheduleTool()
        
        # Test monthly pattern
        result = await tool._arun("email me a report every first of the month")
        assert "TARGET_PROMPT:" in result
        assert "email report: a report" in result
        assert "DELAY_SECONDS:" in result
        assert "SCHEDULE_TYPE: recurring" in result
        
        # Mock response for no match
        mock_response.content = "No monthly pattern found."
        result = await tool._arun("send me a reminder in 5 minutes")
        assert "No monthly pattern found" in result
    
    @pytest.mark.asyncio
    async def test_generic_schedule_tool(self):
        """Test the ParseGenericScheduleTool"""
        # Mock the LLM
        from etc.ZairaSettings import ZairaSettings
        mock_llm = AsyncMock()
        
        # Mock response for "in X time" pattern
        mock_response = MagicMock()
        mock_response.content = "TARGET_PROMPT: remind me to call John\nDELAY_SECONDS: 1800\nSCHEDULE_TYPE: once"
        mock_llm.ainvoke.return_value = mock_response
        ZairaSettings.llm = mock_llm
        
        tool = ParseGenericScheduleTool()
        
        # Test "in X time" pattern
        result = await tool._arun("remind me to call John in 30 minutes")
        assert "TARGET_PROMPT:" in result
        assert "remind me to call John" in result
        assert "DELAY_SECONDS: 1800" in result  # 30 minutes = 1800 seconds
        assert "SCHEDULE_TYPE: once" in result
        
        # Mock response for fallback pattern (immediate task)
        mock_response.content = "TARGET_PROMPT: check the weather\nDELAY_SECONDS: 0\nSCHEDULE_TYPE: once"
        result = await tool._arun("check the weather")
        assert "TARGET_PROMPT: check the weather" in result
        assert "DELAY_SECONDS: 0" in result
        assert "SCHEDULE_TYPE: once" in result
    
    @pytest.mark.asyncio
    async def test_llm_parsing_agent_creation(self):
        """Test that the LLM parsing agent can be created"""
        user = MagicMock()
        user.email = "<EMAIL>"
        user.real_name = "Test User"
        user.username = "test_user"
        user.rank = PERMISSION_LEVELS.USER
        user.GUID = uuid4()
        user.DeviceGUID = uuid4()
        
        bot = MagicMock()
        bot.name = "test_bot"
        # Create task with pre-parsed parameters (no parsing in ScheduledZairaRequest anymore)
        task = ScheduledZairaRequest(
            user=user,
            calling_bot=bot,
            original_message=None,
            schedule_prompt="test prompt",
            target_prompt="test target",
            start_delay_seconds=300.0,
            delay_seconds=0.0,
            schedule_type=ScheduleType.ONCE
        )
        
        # Test that parsing tools still exist and work (they're now used by agents, not ScheduledZairaRequest)
        from userprofiles.ScheduleParsingTools import ParseRecurringScheduleTool, ParseDailyScheduleTool, ParseMonthlyScheduleTool, ParseGenericScheduleTool
        
        # Verify all parsing tools can be instantiated
        tools = [
            ParseRecurringScheduleTool(),
            ParseDailyScheduleTool(), 
            ParseMonthlyScheduleTool(),
            ParseGenericScheduleTool()
        ]
        
        expected_tool_names = ["parse_recurring_schedule", "parse_daily_schedule", "parse_monthly_schedule", "parse_generic_schedule"]
        actual_tool_names = [tool.name for tool in tools]
        for expected_tool in expected_tool_names:
            assert expected_tool in actual_tool_names
    
    def test_extract_parsed_info_from_llm_result(self):
        """Test extraction of parsing results from LLM output (now handled by parsing tools)"""
        user = MagicMock()
        user.email = "<EMAIL>"
        user.real_name = "Test User"
        user.username = "test_user"
        user.rank = PERMISSION_LEVELS.USER
        user.GUID = uuid4()
        user.DeviceGUID = uuid4()
        
        bot = MagicMock()
        bot.name = "test_bot"
        
        # Create task with pre-parsed parameters
        task = ScheduledZairaRequest(
            user=user,
            calling_bot=bot,
            original_message=None,
            schedule_prompt="test prompt",
            target_prompt="test action",
            start_delay_seconds=300.0,
            delay_seconds=0.0,
            schedule_type=ScheduleType.ONCE
        )
        
        # Test that the task stores the parsed values correctly
        assert task.target_prompt == "test action"
        assert task.start_delay_seconds == 300.0
        assert task.schedule_type == ScheduleType.ONCE
    
    def test_fallback_to_regex_parsing(self):
        """Test that the system falls back to regex parsing when LLM fails"""
        user = MagicMock()
        user.email = "<EMAIL>"
        user.real_name = "Test User"
        user.username = "test_user"
        user.rank = PERMISSION_LEVELS.USER
        user.GUID = uuid4()
        user.DeviceGUID = uuid4()
        
        bot = MagicMock()
        bot.name = "test_bot"
        
        # Create task with parsed parameters (parsing now done by agents)
        schedule_prompt = "remind me to call in 5 minutes"
        task = ScheduledZairaRequest(
            user=user,
            calling_bot=bot,
            original_message=None,
            schedule_prompt=schedule_prompt,
            target_prompt="remind to call",
            start_delay_seconds=300.0,  # 5 minutes
            delay_seconds=0.0,
            schedule_type=ScheduleType.ONCE
        )
        
        # Verify the task stores the values correctly
        assert task.target_prompt == "remind to call"
        assert task.start_delay_seconds == 300.0
        assert task.schedule_type == ScheduleType.ONCE

if __name__ == "__main__":
    # Run async tests for tools only (agent creation requires full system setup)
    async def run_async_tests():
        test_instance = TestScheduleParsingLLM()
        await test_instance.test_recurring_schedule_tool()
        await test_instance.test_daily_schedule_tool()
        await test_instance.test_monthly_schedule_tool()
        await test_instance.test_generic_schedule_tool()
        LogFire.log("DEBUG", "All async tool tests passed!", severity="debug")
    
    # Run sync tests
    test_instance = TestScheduleParsingLLM()
    test_instance.test_creation_with_llm_parsing()
    test_instance.test_extract_parsed_info_from_llm_result()
    test_instance.test_fallback_to_regex_parsing()
    LogFire.log("DEBUG", "All sync tests passed!", severity="debug")
    
    # Run async tests
    asyncio.run(run_async_tests())
    LogFire.log("DEBUG", "All tests completed successfully!", severity="debug")