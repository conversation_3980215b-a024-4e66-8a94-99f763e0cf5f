from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from endpoints.mybot_generic import MyBot_Generic, ReplyContext, ChannelType
from userprofiles.ZairaMessage import ZairaMessage
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, call
from uuid import uuid4
import asyncio

class TestMyBotGeneric:
    """Comprehensive test class for MyBot_Generic"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.parent_instance = MagicMock()
        self.bot_name = "TestBot"
        self.bot = MyBot_Generic(
            parent_instance=self.parent_instance,
            name=self.bot_name
        )
        
        # Mock task and user
        self.mock_task = MagicMock()
        self.mock_user = MagicMock()
        self.mock_user.session_guid = uuid4()
        from userprofiles.ZairaChat import ZairaChat
        self.mock_user.chat_history = {
            self.mock_user.session_guid: ZairaChat(
                session_guid=self.mock_user.session_guid,
                user_guid=str(self.mock_user.GUID)
            )
        }
        self.mock_user.my_task = None
        self.mock_task.user = self.mock_user
        self.mock_task.original_physical_message = MagicMock()
        self.mock_task.human_in_the_loop_callback = None
    
    def test_mybot_generic_creation(self):
        """Test MyBot_Generic creation"""
        bot = MyBot_Generic(
            parent_instance=self.parent_instance,
            name="TestBot"
        )
        
        assert bot.parent_instance == self.parent_instance
        assert bot.name == "TestBot"
        assert bot.asyncio_Task is None
    
    @pytest.mark.asyncio
    async def test_mybot_generic_pydantic_config(self):
        """Test Pydantic configuration allows arbitrary types"""
        bot = MyBot_Generic(
            parent_instance=self.parent_instance,
            name="TestBot"
        )
        
        # Should allow asyncio.Task
        task = asyncio.create_task(asyncio.sleep(0))
        bot.asyncio_Task = task
        assert bot.asyncio_Task == task
        task.cancel()  # Clean up
    
    @pytest.mark.asyncio
    async def test_on_ready(self):
        """Test on_ready method"""
        with patch.object(self.bot, 'send_broadcast') as mock_broadcast:
            await self.bot.on_ready()
            
            mock_broadcast.assert_called_once_with(
                "Gegroet collega's! Hoe mag ik u vandaag gehoorzamen? Om zeker te zijn dat ik weet dat je het tegen mij hebt hoef je enkel en alleen je berichten met een '!' te beginnen."
            )
    
    @pytest.mark.asyncio
    async def test_on_message(self):
        """Test on_message method (base implementation should pass)"""
        # Create mock user and parameters matching the actual method signature
        mock_user = MagicMock()
        mock_user.username = "test_user"
        mock_user.on_message = AsyncMock()
        
        # Test with correct parameters: channel_type, user, text, attachments, original_message
        result = await self.bot.on_message(
            channel_type="private",
            user=mock_user, 
            text="test message",
            attachments=[],
            original_message=MagicMock()
        )
        assert result is None
    
    @pytest.mark.asyncio
    async def test_on_member_join_python_call(self):
        """Test on_member_join with Python call (hacking attempt)"""
        bot = MyBot_Generic(parent_instance=None, name="TestBot")
        
        with patch('builtins.print') as mock_print:
            await bot.on_member_join("test_user", MagicMock())
            mock_print.assert_called_once_with("HACKING ATTEMPT! Should never occur!")
    
    @pytest.mark.asyncio
    async def test_on_member_join_teams(self):
        """Test on_member_join with Teams bot"""
        # Import the actual Teams class
        from endpoints.teams_endpoint import MyTeamsBot
        
        # Create a mock instance of the actual class
        mock_parent = MagicMock(spec=MyTeamsBot)
        mock_parent.__class__ = MyTeamsBot
        bot = MyBot_Generic(parent_instance=mock_parent, name="TestBot")
        
        mock_message = MagicMock()
        mock_message.send_activity = AsyncMock()
        
        with patch('botbuilder.core.MessageFactory') as mock_message_factory:
            mock_activity = MagicMock()
            mock_message_factory.text.return_value = mock_activity
            
            await bot.on_member_join("test_user", mock_message)
            
            # Verify MessageFactory.text was called with the correct text
            mock_message_factory.text.assert_called_once_with("Hello and welcome test_user!")
            # Verify send_activity was called with the MessageFactory result
            mock_message.send_activity.assert_called_once_with(mock_activity)
    
    @pytest.mark.asyncio
    async def test_on_member_join_discord(self):
        """Test on_member_join with Discord bot"""
        # Import the actual Discord class
        from endpoints.discord_endpoint import MyDiscordBot
        
        # Create a mock instance of the actual class
        mock_parent = MagicMock(spec=MyDiscordBot)
        mock_parent.__class__ = MyDiscordBot
        bot = MyBot_Generic(parent_instance=mock_parent, name="TestBot")
        
        mock_message = MagicMock()
        mock_message.create_dm = AsyncMock()
        mock_message.dm_channel = MagicMock()
        mock_message.dm_channel.send = AsyncMock()
        
        await bot.on_member_join("test_user", mock_message)
        
        mock_message.create_dm.assert_called_once()
        mock_message.dm_channel.send.assert_called_once_with("Hello and welcome test_user!")
    
    @pytest.mark.asyncio
    async def test_on_member_join_whatsapp(self):
        """Test on_member_join with WhatsApp bot"""
        # Import the actual WhatsApp class
        from endpoints.whatsapp_endpoint import MyWhatsappBot
        
        # Create a mock instance of the actual class
        mock_parent = MagicMock(spec=MyWhatsappBot)
        mock_parent.__class__ = MyWhatsappBot
        bot = MyBot_Generic(parent_instance=mock_parent, name="TestBot")
        
        # Mock the class method
        with patch.object(MyWhatsappBot, 'send_a_whatsapp_message', new=AsyncMock()) as mock_send:
            await bot.on_member_join("test_user", "phone_number")
            
            mock_send.assert_called_once_with("Hello and welcome test_user!", "phone_number")
    
    @pytest.mark.asyncio
    async def test_request_human_in_the_loop_python(self):
        """Test request_human_in_the_loop_internal for Python"""
        bot = MyBot_Generic(parent_instance=None, name="Python")
        
        with patch('builtins.input', return_value="user_response") as mock_input:
            # Make the on_message method async
            self.mock_task.user.on_message = AsyncMock()
            
            await bot.request_human_in_the_loop_internal(
                "Please respond:", self.mock_task, MagicMock()
            )
            
            mock_input.assert_called_once_with("Please respond:")
            self.mock_task.user.on_message.assert_called_once_with(
                "user_response", bot, [], self.mock_task.original_physical_message
            )
    
    @pytest.mark.asyncio
    async def test_request_human_in_the_loop_discord(self):
        """Test request_human_in_the_loop_internal for Discord"""
        bot = MyBot_Generic(parent_instance=self.parent_instance, name="Discord")
        
        mock_message = MagicMock()
        with patch.object(bot, 'send_reply') as mock_send_reply:
            await bot.request_human_in_the_loop_internal(
                "Please respond:", self.mock_task, mock_message
            )
            
            mock_send_reply.assert_called_once_with(
                "Please respond:", self.mock_task, mock_message
            )
    
    @pytest.mark.asyncio
    async def test_request_human_in_the_loop_teams(self):
        """Test request_human_in_the_loop_internal for Teams"""
        bot = MyBot_Generic(parent_instance=self.parent_instance, name="Teams")
        
        mock_message = MagicMock()
        with patch.object(bot, 'send_reply') as mock_send_reply:
            await bot.request_human_in_the_loop_internal(
                "Please respond:", self.mock_task, mock_message
            )
            
            mock_send_reply.assert_called_once_with(
                "Please respond:", self.mock_task, mock_message
            )
    
    @pytest.mark.asyncio
    async def test_request_human_in_the_loop_whatsapp(self):
        """Test request_human_in_the_loop_internal for WhatsApp"""
        bot = MyBot_Generic(parent_instance=self.parent_instance, name="Whatsapp")
        
        mock_message = MagicMock()
        with patch.object(bot, 'send_reply') as mock_send_reply:
            await bot.request_human_in_the_loop_internal(
                "Please respond:", self.mock_task, mock_message
            )
            
            mock_send_reply.assert_called_once_with(
                "Please respond:", self.mock_task, mock_message
            )
    
    @pytest.mark.asyncio
    async def test_request_human_in_the_loop_halt_until_response(self):
        """Test request_human_in_the_loop_internal with halt_until_response=True"""
        bot = MyBot_Generic(parent_instance=self.parent_instance, name="Discord")
        
        # Set up callback that will be cleared after one iteration
        self.mock_task.human_in_the_loop_callback = MagicMock()
        
        async def clear_callback():
            await asyncio.sleep(0.1)  # Small delay
            self.mock_task.human_in_the_loop_callback = None
        
        with patch.object(bot, 'send_reply') as mock_send_reply, \
             patch('asyncio.sleep', return_value=None) as mock_sleep:
            
            # Start the clearing task
            clear_task = asyncio.create_task(clear_callback())
            
            # This should wait until callback is None
            await bot.request_human_in_the_loop_internal(
                "Please respond:", self.mock_task, MagicMock(), halt_until_response=True
            )
            
            await clear_task
            mock_send_reply.assert_called_once()
            mock_sleep.assert_called()
    
    @pytest.mark.asyncio
    async def test_send_broadcast_python(self):
        """Test send_broadcast for Python"""
        bot = MyBot_Generic(parent_instance=None, name="Python")
        
        with patch('builtins.print') as mock_print:
            await bot.send_broadcast("Test broadcast")
            mock_print.assert_called_once_with("Test broadcast")
    
    @pytest.mark.asyncio
    async def test_send_broadcast_discord(self):
        """Test send_broadcast for Discord"""
        bot = MyBot_Generic(parent_instance=self.parent_instance, name="Discord")
        
        with patch('endpoints.discord_endpoint.MyDiscordBot') as mock_discord_bot:
            mock_discord_bot.send_discord_broadcast = AsyncMock()
            
            await bot.send_broadcast("Test broadcast")
            
            mock_discord_bot.send_discord_broadcast.assert_called_once_with("Test broadcast")
    
    @pytest.mark.asyncio
    async def test_send_broadcast_other_platforms(self):
        """Test send_broadcast for other platforms (should do nothing)"""
        bot = MyBot_Generic(parent_instance=self.parent_instance, name="Teams")
        
        # Should complete without error and do nothing
        await bot.send_broadcast("Test broadcast")
    
    @pytest.mark.asyncio
    async def test_send_reply_python(self):
        """Test send_reply for Python"""
        bot = MyBot_Generic(parent_instance=None, name="Python")
        
        with patch('builtins.print') as mock_print, \
             patch('endpoints.mybot_generic.create_task') as mock_create_task, \
             patch('etc.helper_functions.handle_asyncio_task_result_errors') as mock_handler:
            
            mock_asyncio_task = MagicMock()
            mock_asyncio_task.add_done_callback = MagicMock()
            mock_create_task.return_value = mock_asyncio_task
            
            await bot.send_reply("Test message", self.mock_task, MagicMock())
            
            mock_create_task.assert_called_once()
            mock_asyncio_task.add_done_callback.assert_called_once_with(mock_handler)
            assert bot.asyncio_Task == mock_asyncio_task
    
    @pytest.mark.asyncio
    async def test_send_reply_teams(self):
        """Test send_reply for Teams"""
        bot = MyBot_Generic(parent_instance=self.parent_instance, name="Teams")
        
        mock_message = MagicMock()
        
        with patch('endpoints.teams_endpoint.MyTeamsBot.send_a_teams_message', new=AsyncMock()) as mock_send:
            with patch('userprofiles.ZairaMessage.ZairaMessage.create_assistant_message') as mock_create_message:
                mock_zaira_message = MagicMock()
                mock_create_message.return_value = mock_zaira_message
                
                await bot.send_reply("Test message", self.mock_task, mock_message)
                
                mock_send.assert_called_once_with(mock_message, "Test message")
            mock_create_message.assert_called_once_with(
                "Test message",
                self.mock_task.user.session_guid,
                self.mock_task.user.session_guid,
                len("Test message")
            )
            assert mock_zaira_message in self.mock_task.user.chat_history[self.mock_task.user.session_guid].messages
    
    @pytest.mark.asyncio
    async def test_send_reply_discord_short_message(self):
        """Test send_reply for Discord with short message"""
        bot = MyBot_Generic(parent_instance=self.parent_instance, name="Discord")
        
        mock_message = MagicMock()
        mock_message.reply = AsyncMock()
        
        with patch('userprofiles.ZairaMessage.ZairaMessage.create_assistant_message') as mock_create_message:
            mock_zaira_message = MagicMock()
            mock_create_message.return_value = mock_zaira_message
            
            await bot.send_reply("Short message", self.mock_task, mock_message)
            
            mock_message.reply.assert_called_once_with("Short message")
            mock_create_message.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_send_reply_discord_long_message(self):
        """Test send_reply for Discord with long message that needs splitting"""
        bot = MyBot_Generic(parent_instance=self.parent_instance, name="Discord")
        
        # Create a long message that exceeds 2000 characters
        long_message = "This is a test message. " * 100  # About 2400 characters
        
        mock_message = MagicMock()
        mock_message.reply = AsyncMock()
        
        with patch('userprofiles.ZairaMessage.ZairaMessage.create_assistant_message') as mock_create_message:
            mock_zaira_message = MagicMock()
            mock_create_message.return_value = mock_zaira_message
            
            await bot.send_reply(long_message, self.mock_task, mock_message)
            
            # Should be called multiple times due to splitting
            assert mock_message.reply.call_count >= 2
            mock_create_message.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_send_reply_whatsapp(self):
        """Test send_reply for WhatsApp"""
        bot = MyBot_Generic(parent_instance=self.parent_instance, name="Whatsapp")
        
        with patch('endpoints.whatsapp_endpoint.MyWhatsappBot') as mock_whatsapp_bot:
            mock_whatsapp_bot.send_a_whatsapp_message = AsyncMock()
            
            with patch('userprofiles.ZairaMessage.ZairaMessage.create_assistant_message') as mock_create_message:
                mock_zaira_message = MagicMock()
                mock_create_message.return_value = mock_zaira_message
                
                sender_id = "1234567890"
                physical_message = {"from": sender_id}
                await bot.send_reply("Test message", self.mock_task, physical_message)
                
                mock_whatsapp_bot.send_a_whatsapp_message.assert_called_once_with("Test message", sender_id)
                mock_create_message.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_send_reply_whatsapp_long_message(self):
        """Test send_reply for WhatsApp with long message"""
        bot = MyBot_Generic(parent_instance=self.parent_instance, name="Whatsapp")
        
        # Create a long message for WhatsApp (max 1000 chars)
        long_message = "This is a test message for WhatsApp. " * 50  # About 1850 characters
        
        with patch('endpoints.whatsapp_endpoint.MyWhatsappBot') as mock_whatsapp_bot:
            mock_whatsapp_bot.send_a_whatsapp_message = AsyncMock()
            
            with patch('userprofiles.ZairaMessage.ZairaMessage.create_assistant_message'):
                sender_id = "1234567890"
                await bot.send_reply(long_message, self.mock_task, sender_id)
                
                # Should be called multiple times due to splitting
                assert mock_whatsapp_bot.send_a_whatsapp_message.call_count >= 2
    
    @pytest.mark.asyncio
    async def test_send_reply_without_chat_history(self):
        """Test send_reply with add_to_chat_history=False"""
        bot = MyBot_Generic(parent_instance=self.parent_instance, name="Teams")
        
        mock_message = MagicMock()
        
        initial_chat_length = self.mock_task.user.chat_history[self.mock_task.user.session_guid].message_count
        
        with patch('endpoints.teams_endpoint.MyTeamsBot.send_a_teams_message', new=AsyncMock()) as mock_send:
            await bot.send_reply("Test message", self.mock_task, mock_message, add_to_chat_history=False)
            
            mock_send.assert_called_once_with(mock_message, "Test message")
        # Chat history should not have changed
        assert self.mock_task.user.chat_history[self.mock_task.user.session_guid].message_count == initial_chat_length

class TestMyBotGenericTextSplitting:
    """Test text splitting functionality in send_reply"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.bot = MyBot_Generic(parent_instance=MagicMock(), name="Discord")
        self.mock_task = MagicMock()
        self.mock_user = MagicMock()
        self.mock_user.session_guid = uuid4()
        from userprofiles.ZairaChat import ZairaChat
        self.mock_user.chat_history = {
            self.mock_user.session_guid: ZairaChat(
                session_guid=self.mock_user.session_guid,
                user_guid=str(self.mock_user.GUID)
            )
        }
        self.mock_task.user = self.mock_user
    
    @pytest.mark.asyncio
    async def test_text_splitting_short_text(self):
        """Test text splitting with short text"""
        mock_message = MagicMock()
        mock_message.reply = AsyncMock()
        
        short_text = "This is a short message."
        
        with patch('endpoints.mybot_generic.ZairaMessage.create_assistant_message'):
            await self.bot.send_reply(short_text, self.mock_task, mock_message)
            
            mock_message.reply.assert_called_once_with(short_text)
    
    @pytest.mark.asyncio
    async def test_text_splitting_sentence_boundaries(self):
        """Test text splitting respects sentence boundaries"""
        mock_message = MagicMock()
        mock_message.reply = AsyncMock()
        
        # Create text with clear sentence boundaries
        sentences = ["This is sentence one. ", "This is sentence two. ", "This is sentence three. "]
        long_text = "".join(sentences * 100)  # Make it long enough to split
        
        with patch('endpoints.mybot_generic.ZairaMessage.create_assistant_message'):
            await self.bot.send_reply(long_text, self.mock_task, mock_message)
            
            # Should be called multiple times
            assert mock_message.reply.call_count >= 2
            
            # Check that all calls have reasonable length
            for call in mock_message.reply.call_args_list:
                chunk = call[0][0]
                assert len(chunk) <= 2000
    
    @pytest.mark.asyncio
    async def test_text_splitting_no_sentences(self):
        """Test text splitting with no sentence boundaries"""
        mock_message = MagicMock()
        mock_message.reply = AsyncMock()
        
        # Create text without sentence boundaries
        long_text = "a" * 3000  # 3000 characters without sentences
        
        with patch('endpoints.mybot_generic.ZairaMessage.create_assistant_message'):
            await self.bot.send_reply(long_text, self.mock_task, mock_message)
            
            # Should be called multiple times
            assert mock_message.reply.call_count >= 2
            
            # Check that all calls respect max length
            for call in mock_message.reply.call_args_list:
                chunk = call[0][0]
                assert len(chunk) <= 2000
    
    @pytest.mark.asyncio
    async def test_text_splitting_last_chunk_rebalancing(self):
        """Test last chunk rebalancing logic"""
        mock_message = MagicMock()
        mock_message.reply = AsyncMock()
        
        # Create text that would result in a very small last chunk
        text = "Long sentence. " * 130 + "Short."  # Last chunk would be "Short."
        
        with patch('endpoints.mybot_generic.ZairaMessage.create_assistant_message'):
            await self.bot.send_reply(text, self.mock_task, mock_message)
            
            # Should handle rebalancing correctly
            assert mock_message.reply.call_count >= 1
            
            # Verify no chunk is too short (unless it's the only chunk)
            if mock_message.reply.call_count > 1:
                for call in mock_message.reply.call_args_list[:-1]:  # All but last
                    chunk = call[0][0]
                    assert len(chunk) >= 500 or len(chunk) <= 2000

class TestMyBotGenericEdgeCases:
    """Test edge cases for MyBot_Generic"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.bot = MyBot_Generic(parent_instance=MagicMock(), name="TestBot")
    
    def test_creation_with_kwargs(self):
        """Test creation with additional kwargs"""
        extra_param = "extra_value"
        bot = MyBot_Generic(
            parent_instance=MagicMock(),
            name="TestBot",
            extra_param=extra_param
        )
        
        assert bot.name == "TestBot"
        assert hasattr(bot, 'extra_param')
        assert bot.extra_param == extra_param
    
    @pytest.mark.asyncio
    async def test_unknown_platform_handling(self):
        """Test handling of unknown platform names"""
        bot = MyBot_Generic(parent_instance=MagicMock(), name="UnknownPlatform")
        
        # These should complete without errors
        await bot.send_broadcast("Test")
        
        mock_task = MagicMock()
        mock_user = MagicMock()
        mock_user.session_guid = uuid4()
        from userprofiles.ZairaChat import ZairaChat
        mock_user.chat_history = {
            mock_user.session_guid: ZairaChat(
                session_guid=mock_user.session_guid,
                user_guid=str(mock_user.GUID)
            )
        }
        mock_task.user = mock_user
        
        # Should complete without errors (no platform-specific handling)
        await bot.send_reply("Test", mock_task, MagicMock(), add_to_chat_history=False)

class TestReplyContext:
    """Test suite for ReplyContext class"""
    
    def test_reply_context_create_no_reply(self):
        """Test ReplyContext.create_no_reply factory method"""
        context = ReplyContext.create_no_reply()
        
        assert context.is_reply == False
        assert context.replied_message_id is None
        assert context.replied_message_content is None
        assert context.replied_message_author is None
        assert context.platform is None
        
    def test_reply_context_create_reply(self):
        """Test ReplyContext.create_reply factory method"""
        context = ReplyContext.create_reply(
            replied_message_id="123",
            replied_message_content="Original message",
            replied_message_author="Author",
            platform="Discord"
        )
        
        assert context.is_reply == True
        assert context.replied_message_id == "123"
        assert context.replied_message_content == "Original message"
        assert context.replied_message_author == "Author"
        assert context.platform == "Discord"
        
    def test_reply_context_format_reply_context_for_ai(self):
        """Test format_reply_context_for_ai method"""
        # Test with reply
        context = ReplyContext.create_reply(
            replied_message_id="789",
            replied_message_content="Hello there",
            replied_message_author="TestUser",
            platform="Discord"
        )
        
        expected = "\n\n[Reply Context: This message is replying to TestUser: 'Hello there']"
        assert context.format_reply_context_for_ai() == expected
        
        # Test with no reply
        context = ReplyContext.create_no_reply()
        assert context.format_reply_context_for_ai() == ""
        
    def test_reply_context_format_reply_context_for_ai_no_content(self):
        """Test format_reply_context_for_ai with no content"""
        context = ReplyContext.create_reply(
            replied_message_id="789",
            replied_message_author="TestUser",
            platform="Discord"
        )
        
        expected = "\n\n[Reply Context: This message is replying to message ID 789]"
        assert context.format_reply_context_for_ai() == expected
        
    def test_reply_context_format_reply_context_for_ai_no_author(self):
        """Test format_reply_context_for_ai with no author"""
        context = ReplyContext.create_reply(
            replied_message_id="789",
            replied_message_content="Hello there",
            platform="Discord"
        )
        
        expected = "\n\n[Reply Context: This message is replying to message ID 789]"
        assert context.format_reply_context_for_ai() == expected
        
    def test_reply_context_validation(self):
        """Test ReplyContext validation"""
        # Test valid reply context
        context = ReplyContext(
            is_reply=True,
            replied_message_id="123",
            replied_message_content="Test",
            replied_message_author="Author",
            platform="Discord"
        )
        
        assert context.is_reply == True
        assert context.replied_message_id == "123"
        
        # Test no reply context
        context = ReplyContext(
            is_reply=False,
            replied_message_id=None,
            replied_message_content=None,
            replied_message_author=None,
            platform=None
        )
        
        assert context.is_reply == False
        assert context.replied_message_id is None
        
class TestChannelType:
    """Test suite for ChannelType constants"""
    
    def test_channel_type_constants(self):
        """Test ChannelType constants are defined"""
        assert hasattr(ChannelType, 'PRIVATE')
        assert hasattr(ChannelType, 'PUBLIC')
        assert ChannelType.PRIVATE == "private"
        assert ChannelType.PUBLIC == "public"