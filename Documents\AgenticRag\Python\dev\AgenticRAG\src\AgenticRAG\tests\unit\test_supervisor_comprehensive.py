"""
Comprehensive unit tests for SupervisorSupervisor and SupervisorSupervisor_ChainOfThought
Tests focus on the critical llm_call_router functions and edge cases
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock, call
from uuid import uuid4
from typing import Dict, List, Any, Optional
from langchain_core.messages import HumanMessage, SystemMessage, AIMessage
from pydantic import BaseModel

from managers.manager_supervisors import (
    SupervisorManager, SupervisorSupervisor, SupervisorSupervisor_ChainOfThought, 
    SupervisorTask_Base, SupervisorTaskState, SupervisorSection, SupervisorRouteState
)
from managers.manager_users import ZairaUserManager
from managers.manager_prompts import PromptManager
from userprofiles.ZairaUser import <PERSON>aira<PERSON>ser
from userprofiles.ZairaUser import PERMISSION_LEVELS
from langgraph.types import Command


class MockTask(SupervisorTask_Base):
    """Mock task for testing supervisor routing"""
    
    def __init__(self, name: str, always_call_first: bool = False, always_call_last: bool = False):
        super().__init__(name=name)
        self.always_call_FIRST = always_call_first
        self.always_call_LAST = always_call_last
        self._completed = False
    
    async def llm_call(self, state: SupervisorTaskState) -> SupervisorTaskState:
        """Mock task execution"""
        self._completed = True
        state.sections[self.name] = SupervisorSection.from_values(self.name, f"Completed {self.name}")
        return state
    
    def get_prompt(self, include_tools: bool = False) -> str:
        return f"Mock prompt for {self.name}"
    
    def get_tools(self):
        return []


@pytest.mark.unit
@pytest.mark.asyncio
class TestSupervisorSupervisor:
    """Comprehensive tests for SupervisorSupervisor llm_call_router"""
    
    def setup_method(self):
        """Set up test fixtures for each test"""
        self.test_user_guid = str(uuid4())
        self.mock_user = MagicMock(spec=ZairaUser)
        self.mock_user.username = "test_user"
        self.mock_user.GUID = self.test_user_guid
        self.mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        
        # Mock model responses
        self.mock_model = MagicMock()
        self.mock_model.with_structured_output = MagicMock()
        self.mock_model.ainvoke = AsyncMock()
        
    def create_test_supervisor(self, name: str = "test_supervisor") -> SupervisorSupervisor:
        """Create a test supervisor with mocked dependencies"""
        supervisor = SupervisorSupervisor(name=name, model=self.mock_model)
        return supervisor
    
    def create_test_state(self, original_input: str = "test input", completed_tasks: List[str] = None) -> SupervisorTaskState:
        """Create a test state for supervisor routing"""
        return SupervisorTaskState(
            user_guid=self.test_user_guid,
            original_input=original_input,
            additional_input={},
            messages=[HumanMessage(content=original_input)],
            call_trace=[],
            completed_tasks=completed_tasks or [],
            sections={},
            reasoning_steps=[],
            conversation_history=[]
        )
    
    async def test_basic_router_functionality(self):
        """Test basic router functionality with conditional tasks"""
        supervisor = self.create_test_supervisor()
        
        # Add conditional tasks
        task1 = MockTask("task1")
        task2 = MockTask("task2")
        supervisor.add_task(task1)
        supervisor.add_task(task2)
        
        # Mock model decision
        mock_decision = SupervisorRouteState(step="task1")
        mock_structured_output = AsyncMock()
        mock_structured_output.ainvoke.return_value = mock_decision
        self.mock_model.with_structured_output.return_value = mock_structured_output
        
        # Mock dependencies
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="Test prompt"):
                
                state = self.create_test_state()
                result = await supervisor.llm_call_router(state)
                
                # Verify result
                assert isinstance(result, Command)
                assert result.goto == "task1"
                assert "goto task1" in result.update["call_trace"][-1]
                assert supervisor.name in result.update["completed_tasks"]
    
    async def test_always_call_first_routing(self):
        """Test routing with always_call_FIRST tasks"""
        supervisor = self.create_test_supervisor()
        
        # Add tasks with priorities
        first_task = MockTask("first_task", always_call_first=True)
        conditional_task = MockTask("conditional_task")
        supervisor.add_task(first_task)
        supervisor.add_task(conditional_task)
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="Test prompt"):
                
                state = self.create_test_state()
                
                # Mock model to decide END (should trigger always_call_LAST tasks)
                mock_decision = SupervisorRouteState(step="END")
                mock_structured_output = AsyncMock()
                mock_structured_output.ainvoke.return_value = mock_decision
                self.mock_model.with_structured_output.return_value = mock_structured_output
                
                result = await supervisor.llm_call_router(state)
                
                # Should not go to END when no always_call_LAST tasks exist
                assert result.goto == "END"
    
    async def test_always_call_last_routing(self):
        """Test routing with always_call_LAST tasks"""
        supervisor = self.create_test_supervisor()
        
        # Add tasks with priorities
        conditional_task = MockTask("conditional_task")
        last_task = MockTask("last_task", always_call_last=True)
        supervisor.add_task(conditional_task)
        supervisor.add_task(last_task)
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="Test prompt"):
                
                state = self.create_test_state()
                
                # Mock model to decide END
                mock_decision = SupervisorRouteState(step="END")
                mock_structured_output = AsyncMock()
                mock_structured_output.ainvoke.return_value = mock_decision
                self.mock_model.with_structured_output.return_value = mock_structured_output
                
                result = await supervisor.llm_call_router(state)
                
                # Should route to always_call_LAST task instead of END
                assert result.goto == "last_task"
                assert "goto last_task (always_call)" in result.update["call_trace"][-1]
    
    async def test_completed_tasks_exclusion(self):
        """Test that completed tasks are excluded from routing options"""
        supervisor = self.create_test_supervisor()
        
        # Add tasks
        task1 = MockTask("task1")
        task2 = MockTask("task2")
        supervisor.add_task(task1)
        supervisor.add_task(task2)
        
        # Mock model decision
        mock_decision = SupervisorRouteState(step="task2")
        mock_structured_output = AsyncMock()
        mock_structured_output.ainvoke.return_value = mock_decision
        self.mock_model.with_structured_output.return_value = mock_structured_output
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="Test prompt"):
                
                # State with task1 already completed
                state = self.create_test_state(completed_tasks=["task1"])
                result = await supervisor.llm_call_router(state)
                
                # Verify the model was called and result is valid
                assert mock_structured_output.ainvoke.called
                assert result.goto == "task2"
    
    async def test_error_handling_invalid_task(self):
        """Test error handling when router returns invalid task name"""
        supervisor = self.create_test_supervisor()
        
        # Add tasks
        task1 = MockTask("task1")
        supervisor.add_task(task1)
        
        # Mock model to return invalid task
        mock_decision = SupervisorRouteState(step="nonexistent_task")
        mock_structured_output = AsyncMock()
        mock_structured_output.ainvoke.return_value = mock_decision
        self.mock_model.with_structured_output.return_value = mock_structured_output
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="Test prompt"):
                
                state = self.create_test_state()
                
                # Should handle gracefully and still return a Command
                result = await supervisor.llm_call_router(state)
                assert isinstance(result, Command)
                assert result.goto == "nonexistent_task"  # Returns as-is for now
    
    async def test_empty_requests_list(self):
        """Test router behavior with no tasks"""
        supervisor = self.create_test_supervisor()
        
        # Mock model to return END
        mock_decision = SupervisorRouteState(step="END")
        mock_structured_output = AsyncMock()
        mock_structured_output.ainvoke.return_value = mock_decision
        self.mock_model.with_structured_output.return_value = mock_structured_output
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="Test prompt"):
                
                state = self.create_test_state()
                result = await supervisor.llm_call_router(state)
                
                assert result.goto == "END"
    
    async def test_router_wrapper_functionality(self):
        """Test the router wrapper function"""
        supervisor = self.create_test_supervisor()
        
        # Mock the actual router method
        with patch.object(supervisor, 'llm_call_router') as mock_router:
            mock_command = Command(update={"test": "value"}, goto="test_task")
            mock_router.return_value = mock_command
            
            with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
                with patch.object(Globals, 'is_debug_values', return_value=True):
                    
                    state = self.create_test_state()
                    result = await supervisor.llm_call_router_wrapper(state)
                    
                    assert result == mock_command
                    mock_router.assert_called_once_with(state)
    
    async def test_router_wrapper_string_return(self):
        """Test router wrapper when router returns string instead of Command"""
        supervisor = self.create_test_supervisor()
        
        # Mock the actual router method to return string
        with patch.object(supervisor, 'llm_call_router') as mock_router:
            mock_router.return_value = "test_task"
            
            with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
                
                state = self.create_test_state()
                result = await supervisor.llm_call_router_wrapper(state)
                
                assert isinstance(result, Command)
                assert result.goto == "test_task"
                assert supervisor.name in result.update["completed_tasks"]
    
    async def test_router_wrapper_none_return(self):
        """Test router wrapper when router returns None"""
        supervisor = self.create_test_supervisor()
        
        # Mock the actual router method to return None
        with patch.object(supervisor, 'llm_call_router') as mock_router:
            mock_router.return_value = None
            
            with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
                
                state = self.create_test_state()
                result = await supervisor.llm_call_router_wrapper(state)
                
                assert isinstance(result, Command)
                assert result.goto == "END"


@pytest.mark.unit
@pytest.mark.asyncio
class TestSupervisorSupervisor_ChainOfThought:
    """Comprehensive tests for SupervisorSupervisor_ChainOfThought llm_call_router"""
    
    def setup_method(self):
        """Set up test fixtures for each test"""
        self.test_user_guid = str(uuid4())
        self.mock_user = MagicMock(spec=ZairaUser)
        self.mock_user.username = "test_user"
        self.mock_user.GUID = self.test_user_guid
        self.mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        
        # Mock model responses
        self.mock_model = MagicMock()
        self.mock_model.with_structured_output = MagicMock()
        self.mock_model.ainvoke = AsyncMock()
        
    def create_test_supervisor_cot(self, name: str = "test_cot_supervisor") -> SupervisorSupervisor_ChainOfThought:
        """Create a test CoT supervisor with mocked dependencies"""
        supervisor = SupervisorSupervisor_ChainOfThought(name=name, model=self.mock_model)
        return supervisor
    
    def create_test_state(self, original_input: str = "test input", completed_tasks: List[str] = None) -> SupervisorTaskState:
        """Create a test state for supervisor routing"""
        return SupervisorTaskState(
            user_guid=self.test_user_guid,
            original_input=original_input,
            additional_input={},
            messages=[HumanMessage(content=original_input)],
            call_trace=[],
            completed_tasks=completed_tasks or [],
            sections={},
            reasoning_steps=[],
            conversation_history=[]
        )
    
    async def test_cot_always_call_first_priority(self):
        """Test CoT handles always_call_FIRST tasks with priority"""
        supervisor = self.create_test_supervisor_cot()
        
        # Add tasks with priorities
        first_task = MockTask("first_task", always_call_first=True)
        second_first_task = MockTask("second_first_task", always_call_first=True)
        conditional_task = MockTask("conditional_task")
        
        supervisor.add_task(first_task)
        supervisor.add_task(second_first_task)
        supervisor.add_task(conditional_task)
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            
            # Test first call - should route to first always_call_FIRST task
            state = self.create_test_state()
            result = await supervisor.llm_call_router(state)
            
            assert isinstance(result, Command)
            assert result.goto == "first_task"
            assert "always_call_FIRST" in result.update["call_trace"][-1]
            assert "CoT routing" in result.update["reasoning_steps"][0]
    
    async def test_cot_always_call_first_sequence(self):
        """Test CoT executes always_call_FIRST tasks in sequence"""
        supervisor = self.create_test_supervisor_cot()
        
        # Add tasks with priorities
        first_task = MockTask("first_task", always_call_first=True)
        second_first_task = MockTask("second_first_task", always_call_first=True)
        conditional_task = MockTask("conditional_task")
        
        supervisor.add_task(first_task)
        supervisor.add_task(second_first_task)
        supervisor.add_task(conditional_task)
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            
            # First call - should route to first_task
            state1 = self.create_test_state()
            result1 = await supervisor.llm_call_router(state1)
            assert result1.goto == "first_task"
            
            # Second call with first_task completed - should route to second_first_task
            state2 = self.create_test_state(completed_tasks=["first_task"])
            result2 = await supervisor.llm_call_router(state2)
            assert result2.goto == "second_first_task"
    
    async def test_cot_reasoning_with_model_response(self):
        """Test CoT reasoning process with model interaction"""
        supervisor = self.create_test_supervisor_cot()
        supervisor.enable_cot_routing = True
        
        # Add conditional tasks
        task1 = MockTask("task1")
        task2 = MockTask("task2")
        supervisor.add_task(task1)
        supervisor.add_task(task2)
        
        # Mock reasoning response
        mock_reasoning_response = MagicMock()
        mock_reasoning_response.content = "Let me think about this task..."
        
        # Mock final decision
        mock_decision = SupervisorRouteState(step="task1")
        mock_structured_output = AsyncMock()
        mock_structured_output.ainvoke.return_value = mock_decision
        self.mock_model.with_structured_output.return_value = mock_structured_output
        
        # Mock the reasoning call
        self.mock_model.ainvoke.return_value = mock_reasoning_response
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="CoT prompt"):
                
                state = self.create_test_state(completed_tasks=["some_previous_task"])
                result = await supervisor.llm_call_router(state)
                
                # Verify reasoning process was called
                assert self.mock_model.ainvoke.called
                assert mock_structured_output.ainvoke.called
                
                # Verify final result
                assert result.goto == "task1"
    
    async def test_cot_disabled_reasoning(self):
        """Test CoT with reasoning disabled falls back to regular routing"""
        supervisor = self.create_test_supervisor_cot()
        supervisor.enable_cot_routing = False
        
        # Add conditional tasks
        task1 = MockTask("task1")
        supervisor.add_task(task1)
        
        # Mock decision
        mock_decision = SupervisorRouteState(step="task1")
        mock_structured_output = AsyncMock()
        mock_structured_output.ainvoke.return_value = mock_decision
        self.mock_model.with_structured_output.return_value = mock_structured_output
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="Regular prompt"):
                
                state = self.create_test_state(completed_tasks=["some_previous_task"])
                result = await supervisor.llm_call_router(state)
                
                # Should not call reasoning model
                assert not self.mock_model.ainvoke.called
                # Should only call structured output
                assert mock_structured_output.ainvoke.called
                
                assert result.goto == "task1"
    
    async def test_cot_task_exclusion_logic(self):
        """Test CoT properly excludes tasks based on completion status"""
        supervisor = self.create_test_supervisor_cot()
        
        # Add mixed priority tasks
        first_task = MockTask("first_task", always_call_first=True)
        conditional_task = MockTask("conditional_task")
        last_task = MockTask("last_task", always_call_last=True)
        
        supervisor.add_task(first_task)
        supervisor.add_task(conditional_task)
        supervisor.add_task(last_task)
        
        # Mock decision
        mock_decision = SupervisorRouteState(step="conditional_task")
        mock_structured_output = AsyncMock()
        mock_structured_output.ainvoke.return_value = mock_decision
        self.mock_model.with_structured_output.return_value = mock_structured_output
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="CoT prompt"):
                
                # All always_call_FIRST tasks completed
                state = self.create_test_state(completed_tasks=["first_task"])
                result = await supervisor.llm_call_router(state)
                
                # Should now route to conditional task
                assert result.goto == "conditional_task"
                
                # Verify model was called and routing decision is correct
                assert mock_structured_output.ainvoke.called
                assert result.goto == "conditional_task"
    
    async def test_cot_model_error_handling(self):
        """Test CoT handles model errors gracefully"""
        supervisor = self.create_test_supervisor_cot()
        supervisor.enable_cot_routing = True
        
        # Add tasks
        task1 = MockTask("task1")
        supervisor.add_task(task1)
        
        # Mock model to raise exception during reasoning
        self.mock_model.ainvoke.side_effect = Exception("Model error")
        
        # Mock structured output as fallback
        mock_decision = SupervisorRouteState(step="task1")
        mock_structured_output = AsyncMock()
        mock_structured_output.ainvoke.return_value = mock_decision
        self.mock_model.with_structured_output.return_value = mock_structured_output
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="CoT prompt"):
                
                state = self.create_test_state(completed_tasks=["some_task"])
                
                # Should handle exception and fall back to structured output
                result = await supervisor.llm_call_router(state)
                assert result.goto == "task1"
    
    async def test_cot_empty_reasoning_response(self):
        """Test CoT handles empty or invalid reasoning response"""
        supervisor = self.create_test_supervisor_cot()
        supervisor.enable_cot_routing = True
        
        # Add tasks
        task1 = MockTask("task1")
        supervisor.add_task(task1)
        
        # Mock empty reasoning response
        mock_reasoning_response = MagicMock()
        mock_reasoning_response.content = None  # Empty content
        self.mock_model.ainvoke.return_value = mock_reasoning_response
        
        # Mock decision
        mock_decision = SupervisorRouteState(step="task1")
        mock_structured_output = AsyncMock()
        mock_structured_output.ainvoke.return_value = mock_decision
        self.mock_model.with_structured_output.return_value = mock_structured_output
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="CoT prompt"):
                
                state = self.create_test_state(completed_tasks=["some_task"])
                result = await supervisor.llm_call_router(state)
                
                # Should fallback to structured output without reasoning
                assert result.goto == "task1"
    
    async def test_cot_debug_logging(self):
        """Test CoT debug logging functionality"""
        supervisor = self.create_test_supervisor_cot()
        
        # Add tasks
        task1 = MockTask("task1")
        last_task = MockTask("last_task", always_call_last=True)
        supervisor.add_task(task1)
        supervisor.add_task(last_task)
        
        # Mock decision
        mock_decision = SupervisorRouteState(step="task1")
        mock_structured_output = AsyncMock()
        mock_structured_output.ainvoke.return_value = mock_decision
        self.mock_model.with_structured_output.return_value = mock_structured_output
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="CoT prompt"):
                with patch('builtins.print') as mock_print:
                    
                    state = self.create_test_state(completed_tasks=["some_task"])
                    result = await supervisor.llm_call_router(state)
                    
                    # Verify debug information was logged
                    debug_calls = [call for call in mock_print.call_args_list if '[DEBUG]' in str(call)]
                    assert len(debug_calls) > 0
                    
                    # Verify specific debug information is present
                    debug_messages = [str(call) for call in debug_calls]
                    routing_decision_logged = any("routing decision" in msg for msg in debug_messages)
                    assert routing_decision_logged


@pytest.mark.unit
@pytest.mark.asyncio
class TestSupervisorCriticalEdgeCases:
    """Test critical edge cases and potential bugs in supervisor routing"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.mock_user = MagicMock(spec=ZairaUser)
        self.mock_user.username = "test_user"
        self.mock_user.GUID = self.test_user_guid
        self.mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        self.mock_model = MagicMock()
        self.mock_model.with_structured_output = MagicMock()
        self.mock_model.ainvoke = AsyncMock()
        
    async def test_concurrent_task_execution_safety(self):
        """Test thread safety of supervisor routing with concurrent calls"""
        # Use the proper constructor from the test class
        supervisor = SupervisorSupervisor("concurrent_test", model=self.mock_model)
        
        # Add tasks
        for i in range(5):
            task = MockTask(f"task_{i}")
            supervisor.add_task(task)
        
        # Mock decisions for different calls
        decisions = [SupervisorRouteState(step=f"task_{i}") for i in range(5)]
        mock_structured_output = AsyncMock()
        mock_structured_output.ainvoke.side_effect = decisions
        self.mock_model.with_structured_output.return_value = mock_structured_output
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="Test prompt"):
                
                # Create multiple concurrent routing calls
                states = [SupervisorTaskState(
                    user_guid=self.test_user_guid,
                    original_input=f"test input {i}",
                    additional_input={},
                    messages=[HumanMessage(content=f"test input {i}")],
                    call_trace=[],
                    completed_tasks=[],
                    sections={},
                    reasoning_steps=[],
                    conversation_history=[]
                ) for i in range(5)]
                
                # Execute concurrently
                tasks = [supervisor.llm_call_router(state) for state in states]
                results = await asyncio.gather(*tasks)
                
                # Verify all results are valid Commands
                for i, result in enumerate(results):
                    assert isinstance(result, Command)
                    assert result.goto == f"task_{i}"
    
    async def test_state_mutation_isolation(self):
        """Test that state mutations don't leak between routing calls"""
        supervisor = SupervisorSupervisor("isolation_test", model=self.mock_model)
        
        task1 = MockTask("task1")
        supervisor.add_task(task1)
        
        # Mock decision
        mock_decision = SupervisorRouteState(step="task1")
        mock_structured_output = AsyncMock()
        mock_structured_output.ainvoke.return_value = mock_decision
        self.mock_model.with_structured_output.return_value = mock_structured_output
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="Test prompt"):
                
                # Create two states with different data
                state1 = SupervisorTaskState(
                    user_guid=self.test_user_guid,
                    original_input="input1",
                    additional_input={"data": "state1"},
                    messages=[HumanMessage(content="input1")],
                    call_trace=["trace1"],
                    completed_tasks=["completed1"],
                    sections={"section1": SupervisorSection.from_values("test", "value1")},
                    reasoning_steps=["step1"],
                    conversation_history=[]
                )
                
                state2 = SupervisorTaskState(
                    user_guid=self.test_user_guid,
                    original_input="input2",
                    additional_input={"data": "state2"},
                    messages=[HumanMessage(content="input2")],
                    call_trace=["trace2"],
                    completed_tasks=["completed2"],
                    sections={"section2": SupervisorSection.from_values("test", "value2")},
                    reasoning_steps=["step2"],
                    conversation_history=[]
                )
                
                # Execute routing on both states
                result1 = await supervisor.llm_call_router(state1)
                result2 = await supervisor.llm_call_router(state2)
                
                # Verify states remain isolated
                assert state1.original_input == "input1"
                assert state2.original_input == "input2"
                assert state1.additional_input["data"] == "state1"
                assert state2.additional_input["data"] == "state2"
                assert "completed1" in state1.completed_tasks
                assert "completed2" in state2.completed_tasks
    
    async def test_memory_leak_prevention(self):
        """Test that supervisor doesn't accumulate memory over multiple calls"""
        supervisor = SupervisorSupervisor("memory_test", model=self.mock_model)
        
        task1 = MockTask("task1")
        supervisor.add_task(task1)
        
        # Mock decision
        mock_decision = SupervisorRouteState(step="task1")
        mock_structured_output = AsyncMock()
        mock_structured_output.ainvoke.return_value = mock_decision
        self.mock_model.with_structured_output.return_value = mock_structured_output
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="Test prompt"):
                
                # Execute many routing calls
                for i in range(100):
                    state = SupervisorTaskState(
                        user_guid=self.test_user_guid,
                        original_input=f"input_{i}",
                        additional_input={},
                        messages=[HumanMessage(content=f"input_{i}")],
                        call_trace=[],
                        completed_tasks=[],
                        sections={},
                        reasoning_steps=[],
                        conversation_history=[]
                    )
                    
                    result = await supervisor.llm_call_router(state)
                    assert isinstance(result, Command)
                
                # Verify supervisor internal state doesn't grow
                assert len(supervisor._requests) == 1  # Only the task we added
                assert len(supervisor._task_map) == 1
    
    async def test_malformed_state_handling(self):
        """Test handling of malformed or incomplete state objects"""
        supervisor = SupervisorSupervisor("malformed_test", model=self.mock_model)
        
        task1 = MockTask("task1")
        supervisor.add_task(task1)
        
        # Mock decision
        mock_decision = SupervisorRouteState(step="task1")
        mock_structured_output = AsyncMock()
        mock_structured_output.ainvoke.return_value = mock_decision
        self.mock_model.with_structured_output.return_value = mock_structured_output
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=self.mock_user):
            with patch('managers.manager_prompts.PromptManager.get_prompt', return_value="Test prompt"):
                
                # Test with minimal state
                minimal_state = SupervisorTaskState(
                    user_guid=self.test_user_guid,
                    original_input="test",
                    additional_input={},
                    messages=[],  # Empty messages
                    call_trace=[],
                    completed_tasks=[],
                    sections={},
                    reasoning_steps=[],
                    conversation_history=[]
                )
                
                result = await supervisor.llm_call_router(minimal_state)
                assert isinstance(result, Command)
                assert result.goto == "task1"


if __name__ == "__main__":
    # Run tests if called directly
    pytest.main([__file__, "-v"])