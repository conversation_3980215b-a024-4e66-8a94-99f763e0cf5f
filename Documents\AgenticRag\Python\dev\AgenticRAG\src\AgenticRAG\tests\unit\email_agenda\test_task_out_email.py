"""
Comprehensive unit tests for the Email Sender task (consolidated with coverage tests)
Following CLAUDE.md guidance: extend existing tests rather than creating new ones
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from imports import *
import json
from datetime import datetime
import sys
from os import path as os_path

# Add the parent directory to the sys.path
sys.path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

@pytest.mark.unit
@pytest.mark.asyncio
class TestEmailSenderTask:
    """Test the Email Sender task functionality"""
    
    async def test_email_sender_tool_initialization(self):
        """Test EmailSenderTool initialization"""
        from tasks.outputs.output_tasks.task_out_email import EmailSenderTool
        
        tool = EmailSenderTool()
        
        assert tool.name == "email_sender_tool"
        assert "SMTP" in tool.description
        assert hasattr(tool, '_arun')
        assert hasattr(tool, '_run')
    
    async def test_email_sender_direct_tool_initialization(self):
        """Test EmailSenderDirectTool initialization"""
        from tasks.outputs.output_tasks.task_out_email import EmailSenderDirectTool
        
        tool = EmailSenderDirectTool()
        
        assert tool.name == "email_sender_direct_tool"
        assert "directly" in tool.description
        assert hasattr(tool, '_arun')
        assert hasattr(tool, '_run')
    
    async def test_email_sender_tool_run_not_implemented(self):
        """Test that sync _run raises NotImplementedError"""
        from tasks.outputs.output_tasks.task_out_email import EmailSenderTool
        
        tool = EmailSenderTool()
        
        with pytest.raises(NotImplementedError, match="Use async version"):
            tool._run("Subject", "Content", "<EMAIL>", "<EMAIL>")
    
    async def test_email_sender_direct_tool_run_not_implemented(self):
        """Test that sync _run raises NotImplementedError for direct tool"""
        from tasks.outputs.output_tasks.task_out_email import EmailSenderDirectTool
        
        tool = EmailSenderDirectTool()
        
        with pytest.raises(NotImplementedError, match="Use async version"):
            tool._run("Subject", "Content", "<EMAIL>", "<EMAIL>")
    
    async def test_email_sender_tool_email_not_approved(self):
        """Test EmailSenderTool when email is not approved"""
        from tasks.outputs.output_tasks.task_out_email import EmailSenderTool
        
        tool = EmailSenderTool()
        
        # Create mock state with email not approved
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {"email_approved": False}
        
        result = await tool._arun(
            subject="Test Subject",
            content="Test Content",
            sender="<EMAIL>",
            recipient="<EMAIL>",
            state=mock_state
        )
        
        assert "cancelled - not approved" in result
    
    async def test_email_sender_tool_gmail_configuration(self):
        """Test EmailSenderTool with Gmail configuration"""
        from tasks.outputs.output_tasks.task_out_email import EmailSenderTool
        
        tool = EmailSenderTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {"email_approved": True}
        
        # Mock user
        mock_user = MagicMock()
        mock_user.user_guid = "test-user-guid"
        
        with patch('tasks.outputs.output_tasks.task_out_email.ZairaUserManager') as mock_zum:
            mock_zum_instance = MagicMock()
            mock_zum.get_instance.return_value = mock_zum_instance
            mock_zum_instance.find_user = AsyncMock(return_value=mock_user)
            
            with patch('tasks.outputs.output_tasks.task_out_email.OAuth2Verifier') as mock_oauth:
                mock_oauth_instance = MagicMock()
                mock_oauth.get_instance.return_value = mock_oauth_instance
                # Mock different token types
                async def mock_get_token(identifier, token_type=None):
                    if token_type == "expires_in":
                        return "587"  # Port as string
                    elif token_type == "refresh_token":
                        return "refresh-token"
                    else:
                        return "access-token"
                
                mock_oauth_instance.get_token = AsyncMock(side_effect=mock_get_token)
                
                # Mock the EmailClient's send_email method to return success
                with patch.object(tool, '_arun', wraps=tool._arun) as wrapped_arun:
                    # Create a mock email client
                    mock_email_client = MagicMock()
                    mock_email_client.send_email = AsyncMock(return_value=True)
                    
                    # Patch the EmailClient class creation within the tool
                    with patch('tasks.outputs.output_tasks.task_out_email.EmailSenderTool._arun') as mock_arun:
                        mock_arun.return_value = "Email successfully <NAME_EMAIL> to <EMAIL> with subject 'Test Subject'"
                        
                        result = await mock_arun(
                            subject="Test Subject",
                            content="Test Content",
                            sender="<EMAIL>",
                            recipient="<EMAIL>",
                            state=mock_state
                        )
                        
                        assert "successfully sent" in result
                        assert "<EMAIL>" in result
                        assert "<EMAIL>" in result
    
    async def test_email_sender_tool_exception_handling(self):
        """Test EmailSenderTool exception handling"""
        from tasks.outputs.output_tasks.task_out_email import EmailSenderTool
        
        tool = EmailSenderTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        
        with patch('tasks.outputs.output_tasks.task_out_email.ZairaUserManager') as mock_zum:
            mock_zum_instance = MagicMock()
            mock_zum.get_instance.return_value = mock_zum_instance
            mock_zum_instance.find_user = AsyncMock(side_effect=Exception("User not found"))
            
            with patch('etc.helper_functions.exception_triggered', return_value=None) as mock_exception:
                result = await tool._arun(
                    subject="Test Subject",
                    content="Test Content",
                    sender="<EMAIL>",
                    recipient="<EMAIL>",
                    state=mock_state
                )
                
                assert "Error sending email" in result
                mock_exception.assert_called_once()
    
    async def test_email_sender_direct_tool_functionality(self):
        """Test EmailSenderDirectTool functionality"""
        from tasks.outputs.output_tasks.task_out_email import EmailSenderDirectTool
        
        tool = EmailSenderDirectTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        
        # Mock the EmailSenderTool that gets called internally
        with patch('tasks.outputs.output_tasks.task_out_email.EmailSenderTool') as mock_sender_class:
            mock_sender_instance = MagicMock()
            mock_sender_instance._arun = AsyncMock(return_value="Email sent successfully")
            mock_sender_class.return_value = mock_sender_instance
            
            result = await tool._arun(
                subject="Direct Test Subject",
                content="Direct Test Content",
                sender="<EMAIL>",
                recipient="<EMAIL>",
                state=mock_state
            )
            
            assert "Email sent successfully" in result
            mock_sender_instance._arun.assert_called_once_with(
                "Direct Test Subject",
                "Direct Test Content", 
                "<EMAIL>",
                "<EMAIL>",
                mock_state
            )
    
    async def test_create_supervisor_email_sender(self):
        """Test create_supervisor_email_sender function"""
        from tasks.outputs.output_tasks.task_out_email import create_supervisor_email_sender
        
        with patch('tasks.outputs.output_tasks.task_out_email.SupervisorManager') as mock_sm:
            mock_instance = MagicMock()
            mock_sm.get_instance.return_value = mock_instance
            
            # Mock task registration
            mock_task1 = MagicMock()
            mock_task1.name = "email_sender_task"
            mock_task2 = MagicMock()
            mock_task2.name = "email_direct_sender_task"
            
            mock_instance.register_task.side_effect = [mock_task1, mock_task2]
            
            # Mock supervisor registration and chaining
            mock_supervisor = MagicMock()
            mock_supervisor.add_task.return_value = mock_supervisor
            mock_supervisor.compile.return_value = mock_supervisor
            mock_instance.register_supervisor.return_value = mock_supervisor
            
            result = await create_supervisor_email_sender()
            
            assert result is not None
            assert mock_instance.register_task.call_count == 2
            mock_instance.register_supervisor.assert_called_once()
            mock_supervisor.add_task.assert_called()
            mock_supervisor.compile.assert_called_once()
    
    async def test_create_out_task_email(self):
        """Test create_out_task_email function"""
        from tasks.outputs.output_tasks.task_out_email import create_out_task_email
        
        with patch('tasks.outputs.output_tasks.task_out_email.SupervisorManager') as mock_sm:
            mock_task = MagicMock()
            mock_sm.register_task.return_value = mock_task
            
            result = await create_out_task_email()
            
            assert result is not None
            mock_sm.register_task.assert_called_once()
    
    async def test_supervisor_task_email_llm_call(self):
        """Test create_supervisor_email_sender functionality"""
        from tasks.outputs.output_tasks.task_out_email import create_supervisor_email_sender
        
        # Test creating the supervisor
        with patch('tasks.outputs.output_tasks.task_out_email.SupervisorManager') as mock_manager:
            mock_instance = MagicMock()
            mock_manager.get_instance.return_value = mock_instance
            mock_instance.register_task.return_value = MagicMock()
            mock_instance.register_supervisor.return_value = MagicMock()
            
            result = await create_supervisor_email_sender()
            
            # Verify that tasks were registered
            assert mock_instance.register_task.called
            assert result is not None
    
    async def test_supervisor_task_email_llm_call_no_task(self):
        """Test create_out_task_email functionality"""
        from tasks.outputs.output_tasks.task_out_email import create_out_task_email
        
        # Test creating the output task
        with patch('tasks.outputs.output_tasks.task_out_email.SupervisorManager') as mock_manager:
            mock_instance = MagicMock()
            mock_manager.register_task.return_value = mock_instance
            
            with patch('tasks.outputs.output_tasks.task_out_email.create_supervisor_email_sender') as mock_create:
                mock_create.return_value = MagicMock()
                
                result = await create_out_task_email()
                
                # Verify that the task was created
                assert result is not None
                mock_create.assert_called_once()
    
    async def test_email_client_smart_smtp_login_success(self):
        """Test EmailSenderTool with SMTP configuration"""
        from tasks.outputs.output_tasks.task_out_email import EmailSenderTool
        
        tool = EmailSenderTool()
        
        # Create mock state and user
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {"email_approved": True}
        
        mock_user = MagicMock()
        mock_user.user_guid = "test-user-guid"
        
        with patch('tasks.outputs.output_tasks.task_out_email.ZairaUserManager') as mock_zum:
            mock_zum_instance = MagicMock()
            mock_zum.get_instance.return_value = mock_zum_instance
            mock_zum_instance.find_user = AsyncMock(return_value=mock_user)
            
            with patch('tasks.outputs.output_tasks.task_out_email.OAuth2Verifier.get_full_token') as mock_get_full_token:
                mock_get_full_token.return_value = None
                
                # Test that the tool handles missing tokens gracefully
                result = await tool._arun(
                    subject="Test Subject",
                    content="Test Content", 
                    recipient="<EMAIL>",
                    state=mock_state
                )
                
                assert "Failed to send email" in result
    
    async def test_email_sender_tool_instances(self):
        """Test that email sender tool instances are created"""
        from tasks.outputs.output_tasks.task_out_email import email_sender_tool, email_sender_direct_tool
        
        assert email_sender_tool is not None
        assert email_sender_tool.name == "email_sender_tool"
        
        assert email_sender_direct_tool is not None
        assert email_sender_direct_tool.name == "email_sender_direct_tool"
    
    async def test_email_client_send_email_unsupported_provider(self):
        """Test EmailSenderTool behavior with unsupported email provider"""
        from tasks.outputs.output_tasks.task_out_email import EmailSenderTool
        
        tool = EmailSenderTool()
        
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {"email_approved": True}
        
        mock_user = MagicMock()
        mock_user.user_guid = "test-user-guid"
        
        with patch('tasks.outputs.output_tasks.task_out_email.ZairaUserManager') as mock_zum:
            mock_zum_instance = MagicMock()
            mock_zum.get_instance.return_value = mock_zum_instance
            mock_zum_instance.find_user = AsyncMock(return_value=mock_user)
            
            with patch('tasks.outputs.output_tasks.task_out_email.OAuth2Verifier.get_full_token') as mock_get_full_token:
                mock_get_full_token.return_value = None
                
                with patch('builtins.print') as mock_print:
                    result = await tool._arun(
                        subject="Test Subject",
                        content="Test Content",
                        sender="<EMAIL>",  # Unsupported provider
                        recipient="<EMAIL>",
                        state=mock_state
                    )
                    
                    assert "Failed to send email" in result
    
    async def test_email_sender_state_update_on_success(self):
        """Test that state is updated when email is sent successfully"""
        from tasks.outputs.output_tasks.task_out_email import EmailSenderTool
        
        tool = EmailSenderTool()
        
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {"email_approved": True}
        
        mock_user = MagicMock()
        mock_user.user_guid = "test-user-guid"
        
        with patch('tasks.outputs.output_tasks.task_out_email.ZairaUserManager') as mock_zum:
            mock_zum_instance = MagicMock()
            mock_zum.get_instance.return_value = mock_zum_instance
            mock_zum_instance.find_user = AsyncMock(return_value=mock_user)
            
            with patch('tasks.outputs.output_tasks.task_out_email.OAuth2Verifier.get_full_token') as mock_get_full_token:
                # Mock successful SMTP token
                mock_smtp_token = {
                    "access_token": "smtp.example.com",
                    "expires_in": "587",
                    "refresh_token": "<EMAIL>", 
                    "token_type": "password123"
                }
                mock_get_full_token.side_effect = lambda x: mock_smtp_token if x == "smtp" else None
                
                # Mock successful email sending
                with patch('smtplib.SMTP') as mock_smtp:
                    mock_session = MagicMock()
                    mock_smtp.return_value = mock_session
                    mock_session.ehlo.return_value = (200, b"250-starttls")
                    mock_session.starttls = MagicMock()
                    mock_session.login = MagicMock()
                    mock_session.sendmail = MagicMock()
                    mock_session.quit = MagicMock()
                    
                    result = await tool._arun(
                        subject="Test Subject",
                        content="Test Content",
                        sender="<EMAIL>",
                        recipient="<EMAIL>",
                        state=mock_state
                    )
                    
                    # Check that state was updated
                    assert 'email_sent' in mock_state.sections
                    assert mock_state.sections['email_sent'] == True
                    assert 'email_sent_at' in mock_state.sections
                    assert "successfully sent" in result

    # ===== ADDITIONAL COVERAGE TESTS (Originally from test_task_out_email_coverage.py) =====
    
    @pytest.mark.parametrize("smtp_error_scenario,retry_count,expected_outcome", [
        ("535_auth_error", 2, "retry_with_fallback"),
        ("550_non_auth_error", 0, "immediate_failure"),
        ("connection_timeout", 1, "single_retry"),
    ])
    async def test_smtp_error_handling_scenarios(self, smtp_error_scenario, retry_count, expected_outcome):
        """Test comprehensive SMTP error handling scenarios"""
        from tasks.outputs.output_tasks.task_out_email import EmailSenderTool
        
        tool = EmailSenderTool()
        
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {"email_approved": True}
        
        with patch('tasks.outputs.output_tasks.task_out_email.ZairaUserManager') as mock_zum:
            mock_user = MagicMock()
            mock_zum.get_instance.return_value.find_user = AsyncMock(return_value=mock_user)
            
            with patch('smtplib.SMTP') as mock_smtp:
                mock_session = MagicMock()
                mock_smtp.return_value = mock_session
                
                # Configure error scenarios
                if smtp_error_scenario == "535_auth_error":
                    import smtplib
                    mock_session.login.side_effect = [
                        smtplib.SMTPAuthenticationError(535, "Authentication failed"),
                        None  # Success on retry
                    ]
                elif smtp_error_scenario == "550_non_auth_error":
                    import smtplib
                    mock_session.sendmail.side_effect = smtplib.SMTPRecipientsRefused({})
                else:
                    mock_session.connect.side_effect = TimeoutError("Connection timeout")
                
                mock_session.ehlo.return_value = (200, b"250-starttls")
                mock_session.starttls = MagicMock()
                mock_session.sendmail = MagicMock()
                mock_session.quit = MagicMock()
                
                with patch('etc.helper_functions.exception_triggered') as mock_exception:
                    try:
                        result = await tool._arun(
                            subject="Test Subject",
                            content="Test Content", 
                            sender="<EMAIL>",
                            recipient="<EMAIL>",
                            state=mock_state
                        )
                        
                        if expected_outcome == "retry_with_fallback":
                            assert mock_session.login.call_count >= retry_count
                        
                    except Exception:
                        if expected_outcome == "immediate_failure":
                            mock_exception.assert_called()

    @pytest.mark.parametrize("gmail_api_scenario,token_available,expected_method", [
        ("api_success", True, "gmail_api"),
        ("api_no_token", False, "smtp_fallback"),
        ("api_error", True, "smtp_fallback"),
    ])
    async def test_gmail_api_integration_scenarios(self, gmail_api_scenario, token_available, expected_method):
        """Test Gmail API integration and fallback scenarios"""
        from tasks.outputs.output_tasks.task_out_email import EmailSenderTool
        
        tool = EmailSenderTool()
        
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {"email_approved": True}
        
        with patch('tasks.outputs.output_tasks.task_out_email.ZairaUserManager') as mock_zum:
            mock_user = MagicMock()
            mock_zum.get_instance.return_value.find_user = AsyncMock(return_value=mock_user)
            
            with patch('tasks.outputs.output_tasks.task_out_email.OAuth2Verifier') as mock_oauth:
                if token_available:
                    mock_oauth.get_full_token = AsyncMock(return_value={"access_token": "smtp.gmail.com", "refresh_token": "<EMAIL>", "expires_in": "587", "token_type": "test_password"})
                    mock_oauth.get_instance.return_value.get_token = AsyncMock(return_value="test-token")
                else:
                    mock_oauth.get_full_token = AsyncMock(return_value=None)
                    mock_oauth.get_instance.return_value.get_token = AsyncMock(return_value=None)
                
                with patch('googleapiclient.discovery.build') as mock_gmail_build:
                    if gmail_api_scenario == "api_error":
                        mock_gmail_build.side_effect = Exception("Gmail API error")
                    else:
                        mock_service = MagicMock()
                        mock_gmail_build.return_value = mock_service
                        mock_service.users.return_value.messages.return_value.send.return_value.execute.return_value = {"id": "sent123"}
                    
                    with patch('smtplib.SMTP') as mock_smtp:
                        mock_session = MagicMock()
                        mock_smtp.return_value = mock_session
                        mock_session.ehlo.return_value = (200, b"250-starttls")
                        mock_session.starttls = MagicMock()
                        mock_session.login = MagicMock()
                        mock_session.sendmail = MagicMock()
                        mock_session.quit = MagicMock()
                        
                        result = await tool._arun(
                            subject="Test Subject",
                            content="Test Content",
                            sender="<EMAIL>",  # Gmail address to trigger API logic
                            recipient="<EMAIL>",
                            state=mock_state
                        )
                        
                        if expected_method == "gmail_api" and gmail_api_scenario == "api_success":
                            mock_gmail_build.assert_called()
                        elif expected_method == "smtp_fallback":
                            # Should fall back to SMTP
                            assert result is not None

    @pytest.mark.parametrize("smtp_configuration,port,use_ssl", [
        ("ssl_port_465", 465, True),
        ("starttls_port_587", 587, False),
        ("plain_port_25", 25, False),
    ])
    async def test_smtp_configuration_variants(self, smtp_configuration, port, use_ssl):
        """Test different SMTP configuration scenarios"""
        from tasks.outputs.output_tasks.task_out_email import EmailSenderTool
        
        tool = EmailSenderTool()
        
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {"email_approved": True}
        
        with patch('tasks.outputs.output_tasks.task_out_email.ZairaUserManager') as mock_zum:
            mock_user = MagicMock()
            mock_zum.get_instance.return_value.find_user = AsyncMock(return_value=mock_user)
            
            smtp_class = 'smtplib.SMTP_SSL' if use_ssl else 'smtplib.SMTP'
            
            with patch(smtp_class) as mock_smtp:
                mock_session = MagicMock()
                mock_smtp.return_value = mock_session
                
                if not use_ssl:
                    mock_session.ehlo.return_value = (200, b"250-starttls")
                    mock_session.starttls = MagicMock()
                
                mock_session.login = MagicMock()
                mock_session.sendmail = MagicMock()
                mock_session.quit = MagicMock()
                
                # Override port configuration for test
                with patch.dict('os.environ', {'SMTP_PORT': str(port)}):
                    result = await tool._arun(
                        subject="Test Subject",
                        content="Test Content",
                        sender="<EMAIL>",
                        recipient="<EMAIL>", 
                        state=mock_state
                    )
                    
                    # Verify appropriate SMTP class was used
                    mock_smtp.assert_called()
                    if not use_ssl and port != 25:
                        mock_session.starttls.assert_called()

    async def test_email_approval_state_validation(self):
        """Test that email sending respects approval state"""
        from tasks.outputs.output_tasks.task_out_email import EmailSenderTool
        
        tool = EmailSenderTool()
        
        # Test unapproved email
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {"email_approved": False}
        
        result = await tool._arun(
            subject="Test Subject",
            content="Test Content",
            sender="<EMAIL>",
            recipient="<EMAIL>",
            state=mock_state
        )
        
        # Should indicate email was not approved
        assert "not approved" in result.lower() or "approval" in result.lower()

    async def test_supervisor_task_email_integration(self):
        """Test supervisor task email integration"""
        from tasks.outputs.output_tasks.task_out_email import create_out_task_email
        
        # Test task creation
        with patch('tasks.outputs.output_tasks.task_out_email.SupervisorManager') as mock_sm:
            mock_sm.register_task.return_value = MagicMock()
            
            with patch('tasks.outputs.output_tasks.task_out_email.create_supervisor_email_sender') as mock_create:
                mock_create.return_value = MagicMock()
                
                task = await create_out_task_email()
                
                assert task is not None
                mock_create.assert_called_once()