"""
Unit tests for ZairaChat class.
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from datetime import datetime, timezone, timedelta
from uuid import uuid4, UUID
from pydantic import ValidationError

from userprofiles.ZairaChat import ZairaChat, ChatSessionStatus, ChatSessionType
from userprofiles.ZairaMessage import ZairaMessage, MessageRole

class TestZairaChat:
    """Test cases for ZairaChat class"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.test_session_guid = uuid4()
        self.test_conversation_guid = uuid4()
        
        # Sample messages for testing
        self.sample_messages = [
            ZairaMessage.create_user_message("Hello, how are you?", str(self.test_conversation_guid), str(self.test_session_guid)),
            ZairaMessage.create_assistant_message("I'm doing well, thank you!", str(self.test_conversation_guid), str(self.test_session_guid), 10),
            ZairaMessage.create_user_message("What's the weather like?", str(self.test_conversation_guid), str(self.test_session_guid)),
            ZairaMessage.create_assistant_message("I don't have access to current weather data.", str(self.test_conversation_guid), str(self.test_session_guid), 15)
        ]
    
    def test_basic_creation(self):
        """Test basic ZairaChat creation"""
        chat = ZairaChat(
            user_guid=self.test_user_guid,
            session_guid=self.test_session_guid,
            conversation_guid=self.test_conversation_guid
        )
        
        assert chat.user_guid == self.test_user_guid
        assert chat.session_guid == self.test_session_guid
        assert chat.conversation_guid == self.test_conversation_guid
        assert chat.status == ChatSessionStatus.ACTIVE
        assert chat.session_type == ChatSessionType.STANDARD
        assert len(chat.messages) == 0
        assert chat.message_count == 0
        assert chat.is_empty
        assert chat.total_tokens_used == 0
    
    def test_creation_with_defaults(self):
        """Test ZairaChat creation uses proper defaults"""
        chat = ZairaChat(user_guid=self.test_user_guid)
        
        assert chat.user_guid == self.test_user_guid
        assert isinstance(chat.session_guid, UUID)
        assert chat.status == ChatSessionStatus.ACTIVE
        assert chat.session_type == ChatSessionType.STANDARD
        assert chat.auto_archive_after_days == 30
        assert isinstance(chat.created_at, datetime)
        assert isinstance(chat.updated_at, datetime)
        assert isinstance(chat.last_activity_at, datetime)
    
    def test_add_message_success(self):
        """Test successfully adding messages"""
        chat = ZairaChat(user_guid=self.test_user_guid)
        
        # Add first message
        result = chat.add_message(self.sample_messages[0])
        assert result is True
        assert chat.message_count == 1
        assert not chat.is_empty
        
        # Verify message was properly integrated
        added_message = chat.messages[0]
        assert added_message.session_id == str(chat.session_guid)
        
        # Add second message
        result = chat.add_message(self.sample_messages[1])
        assert result is True
        assert chat.message_count == 2
        
        # Verify token counting
        expected_tokens = (self.sample_messages[0].tokens_used or 0) + (self.sample_messages[1].tokens_used or 0)
        assert chat.total_tokens_used == expected_tokens
    
    def test_add_message_with_limits(self):
        """Test message limits enforcement"""
        chat = ZairaChat(
            user_guid=self.test_user_guid,
            max_messages=2,
            max_tokens=20
        )
        
        # Add messages within limit
        assert chat.add_message(self.sample_messages[0]) is True
        assert chat.add_message(self.sample_messages[1]) is True
        
        # Try to exceed message limit
        assert chat.add_message(self.sample_messages[2]) is False
        assert chat.message_count == 2
    
    def test_add_message_token_limit(self):
        """Test token limit enforcement"""
        chat = ZairaChat(
            user_guid=self.test_user_guid,
            max_tokens=15
        )
        
        # Add message with 10 tokens
        assert chat.add_message(self.sample_messages[1]) is True
        assert chat.total_tokens_used == 10
        
        # Try to add message with 15 tokens (would exceed limit)
        assert chat.add_message(self.sample_messages[3]) is False
        assert chat.total_tokens_used == 10
        assert chat.message_count == 1
    
    def test_auto_title_generation(self):
        """Test automatic title generation"""
        chat = ZairaChat(user_guid=self.test_user_guid)
        
        # No title initially
        assert chat.title is None
        
        # Add first user message - should generate title immediately
        chat.add_message(self.sample_messages[0])
        assert chat.title is not None
        assert "Hello, how are you?" in chat.title
        
        # Add assistant message - title should remain unchanged
        original_title = chat.title
        chat.add_message(self.sample_messages[1])
        assert chat.title == original_title
    
    def test_get_messages_filtering(self):
        """Test message filtering and retrieval"""
        chat = ZairaChat(user_guid=self.test_user_guid)
        
        # Add all sample messages
        for msg in self.sample_messages:
            chat.add_message(msg)
        
        # Get all messages
        all_messages = chat.get_messages()
        assert len(all_messages) == 4
        
        # Get only user messages
        user_messages = chat.get_messages(role=MessageRole.USER)
        assert len(user_messages) == 2
        assert all(msg.is_from_user() for msg in user_messages)
        
        # Get only assistant messages
        assistant_messages = chat.get_messages(role=MessageRole.ASSISTANT)
        assert len(assistant_messages) == 2
        assert all(msg.is_from_assistant() for msg in assistant_messages)
        
        # Get limited messages
        limited_messages = chat.get_messages(limit=2)
        assert len(limited_messages) == 2
        # Should return last 2 messages
        assert limited_messages[-1] == self.sample_messages[-1]
    
    def test_get_last_messages(self):
        """Test getting last messages of different types"""
        chat = ZairaChat(user_guid=self.test_user_guid)
        
        # Empty chat
        assert chat.get_last_message() is None
        assert chat.get_last_user_message() is None
        assert chat.get_last_assistant_message() is None
        
        # Add messages
        for msg in self.sample_messages:
            chat.add_message(msg)
        
        # Test last message retrieval
        last_message = chat.get_last_message()
        assert last_message == self.sample_messages[-1]
        
        last_user_message = chat.get_last_user_message()
        assert last_user_message.is_from_user()
        assert last_user_message.content == "What's the weather like?"
        
        last_assistant_message = chat.get_last_assistant_message()
        assert last_assistant_message.is_from_assistant()
        assert "weather data" in last_assistant_message.content
    
    def test_remove_message(self):
        """Test message removal"""
        chat = ZairaChat(user_guid=self.test_user_guid)
        
        # Add messages
        for msg in self.sample_messages:
            chat.add_message(msg)
        
        original_count = chat.message_count
        original_tokens = chat.total_tokens_used
        message_to_remove = self.sample_messages[1]
        
        # Remove message
        result = chat.remove_message(message_to_remove.message_id)
        assert result is True
        assert chat.message_count == original_count - 1
        
        # Verify token count updated
        expected_tokens = original_tokens - (message_to_remove.tokens_used or 0)
        assert chat.total_tokens_used == expected_tokens
        
        # Try to remove non-existent message
        result = chat.remove_message("non-existent-id")
        assert result is False
    
    def test_clear_messages(self):
        """Test clearing all messages"""
        chat = ZairaChat(user_guid=self.test_user_guid)
        
        # Add messages
        for msg in self.sample_messages:
            chat.add_message(msg)
        
        assert chat.message_count > 0
        assert chat.total_tokens_used > 0
        
        # Clear messages
        chat.clear_messages()
        assert chat.message_count == 0
        assert chat.total_tokens_used == 0
        assert chat.is_empty
    
    def test_session_status_management(self):
        """Test session status changes"""
        chat = ZairaChat(user_guid=self.test_user_guid)
        
        # Initially active
        assert chat.status == ChatSessionStatus.ACTIVE
        assert chat.is_active
        
        # Archive session
        chat.archive()
        assert chat.status == ChatSessionStatus.ARCHIVED
        assert not chat.is_active
        
        # Reactivate
        chat.activate()
        assert chat.status == ChatSessionStatus.ACTIVE
        assert chat.is_active
        
        # Delete session
        chat.delete()
        assert chat.status == ChatSessionStatus.DELETED
        
        # Suspend session
        active_chat = ZairaChat(user_guid=self.test_user_guid)
        active_chat.suspend()
        assert active_chat.status == ChatSessionStatus.SUSPENDED
    
    def test_auto_archive_logic(self):
        """Test auto-archive functionality"""
        # Create chat with short auto-archive period
        chat = ZairaChat(
            user_guid=self.test_user_guid,
            auto_archive_after_days=1
        )
        
        # Recent activity - should not auto-archive
        assert not chat.should_auto_archive()
        
        # Simulate old activity
        old_time = datetime.now(timezone.utc) - timedelta(days=2)
        chat.last_activity_at = old_time
        
        # Should auto-archive now
        assert chat.should_auto_archive()
        
        # No auto-archive setting
        chat.auto_archive_after_days = None
        assert not chat.should_auto_archive()
    
    def test_computed_fields(self):
        """Test computed properties"""
        chat = ZairaChat(user_guid=self.test_user_guid)
        
        # Add messages with time gaps
        first_time = datetime.now(timezone.utc)
        second_time = first_time + timedelta(minutes=5)
        
        msg1 = ZairaMessage.create_user_message("First", str(self.test_conversation_guid), str(self.test_session_guid))
        msg1.timestamp = first_time
        
        msg2 = ZairaMessage.create_assistant_message("Second", str(self.test_conversation_guid), str(self.test_session_guid), 10)
        msg2.timestamp = second_time
        
        chat.add_message(msg1)
        chat.add_message(msg2)
        
        # Test computed fields
        assert chat.message_count == 2
        assert not chat.is_empty
        assert chat.duration_seconds == 300.0  # 5 minutes
        
        # Test days since activity
        assert chat.days_since_activity >= 0
    
    def test_tags_and_metadata(self):
        """Test tags and metadata management"""
        chat = ZairaChat(user_guid=self.test_user_guid)
        
        # Add tags
        chat.add_tag("important")
        chat.add_tag("customer-support")
        assert "important" in chat.tags
        assert "customer-support" in chat.tags
        
        # Add duplicate tag (should not duplicate)
        chat.add_tag("important")
        assert chat.tags.count("important") == 1
        
        # Remove tag
        result = chat.remove_tag("important")
        assert result is True
        assert "important" not in chat.tags
        
        # Remove non-existent tag
        result = chat.remove_tag("non-existent")
        assert result is False
        
        # Update metadata
        chat.update_metadata(priority="high", source="web")
        assert chat.metadata["priority"] == "high"
        assert chat.metadata["source"] == "web"
        
        # Update context
        chat.update_context(user_preferences={"theme": "dark"})
        assert chat.session_context["user_preferences"]["theme"] == "dark"
    
    def test_export_methods(self):
        """Test various export/conversion methods"""
        chat = ZairaChat(
            user_guid=self.test_user_guid,
            title="Test Chat",
            session_type=ChatSessionType.SUPPORT
        )
        
        # Add messages
        for msg in self.sample_messages[:2]:
            chat.add_message(msg)
        
        # Test to_dict
        chat_dict = chat.to_dict()
        assert chat_dict["title"] == "Test Chat"
        assert chat_dict["session_type"] == "support"
        assert "messages" in chat_dict
        assert len(chat_dict["messages"]) == 2
        
        # Test to_dict without messages
        chat_dict_no_msgs = chat.to_dict(include_messages=False)
        assert "messages" not in chat_dict_no_msgs
        
        # Test LangChain conversion
        lc_messages = chat.to_langchain_messages()
        assert len(lc_messages) == 2
        
        # Test OpenAI conversion
        openai_messages = chat.to_openai_messages()
        assert len(openai_messages) == 2
        assert openai_messages[0]["role"] == "user"
        assert openai_messages[1]["role"] == "assistant"
        
        # Test summary stats
        stats = chat.get_summary_stats()
        assert stats["message_count"] == 2
        assert stats["user_message_count"] == 1
        assert stats["assistant_message_count"] == 1
        assert stats["title"] == "Test Chat"
    
    def test_factory_methods(self):
        """Test factory method for creating chats"""
        # Test create_from_messages
        chat = ZairaChat.create_from_messages(
            user_guid=self.test_user_guid,
            messages=self.sample_messages,
            title="Factory Chat",
            session_type=ChatSessionType.SCHEDULED
        )
        
        assert chat.user_guid == self.test_user_guid
        assert chat.title == "Factory Chat"
        assert chat.session_type == ChatSessionType.SCHEDULED
        assert chat.message_count == len(self.sample_messages)
        assert chat.total_tokens_used == sum(msg.tokens_used or 0 for msg in self.sample_messages)
    
    def test_migration_from_message_list(self):
        """Test migration from old format"""
        # Test with ZairaMessage objects
        chat = ZairaChat.migrate_from_message_list(
            user_guid=self.test_user_guid,
            session_guid=self.test_session_guid,
            messages=self.sample_messages
        )
        
        assert chat.user_guid == self.test_user_guid
        assert chat.session_guid == self.test_session_guid
        assert chat.message_count == len(self.sample_messages)
        
        # Test with dict format (old format)
        old_format_messages = [
            {"role": "user", "content": "Hello", "timestamp": datetime.now(timezone.utc)},
            {"role": "assistant", "content": "Hi there", "timestamp": datetime.now(timezone.utc)}
        ]
        
        migrated_chat = ZairaChat.migrate_from_message_list(
            user_guid=self.test_user_guid,
            session_guid=str(self.test_session_guid),
            messages=old_format_messages
        )
        
        assert migrated_chat.message_count == 2
        assert migrated_chat.messages[0].role == MessageRole.USER
        assert migrated_chat.messages[1].role == MessageRole.ASSISTANT
    
    def test_validation_errors(self):
        """Test validation and error handling"""
        # Test invalid token limit
        with pytest.raises(ValidationError):
            chat = ZairaChat(
                user_guid=self.test_user_guid,
                max_tokens=100,
                total_tokens_used=150  # Exceeds limit
            )
        
        # Test invalid enum values
        with pytest.raises(ValidationError):
            ZairaChat(
                user_guid=self.test_user_guid,
                status="invalid_status"
            )
    
    def test_string_representations(self):
        """Test string representation methods"""
        chat = ZairaChat(
            user_guid=self.test_user_guid,
            title="Test Chat for String Representation"
        )
        
        # Test __str__
        str_repr = str(chat)
        assert "Test Chat for String Represent" in str_repr  # Truncated to 30 chars
        assert str(chat.session_guid)[:8] in str_repr
        assert "messages=0" in str_repr
        
        # Test __repr__
        repr_str = repr(chat)
        assert "ZairaChat" in repr_str
        assert self.test_user_guid in repr_str
        assert str(chat.session_guid) in repr_str
    
    def test_time_filtering(self):
        """Test time-based message filtering"""
        chat = ZairaChat(user_guid=self.test_user_guid)
        
        # Create messages with specific timestamps
        now = datetime.now(timezone.utc)
        hour_ago = now - timedelta(hours=1)
        two_hours_ago = now - timedelta(hours=2)
        
        msg1 = ZairaMessage.create_user_message("Old message", str(self.test_conversation_guid), str(self.test_session_guid))
        msg1.timestamp = two_hours_ago
        
        msg2 = ZairaMessage.create_user_message("Recent message", str(self.test_conversation_guid), str(self.test_session_guid))
        msg2.timestamp = hour_ago
        
        msg3 = ZairaMessage.create_user_message("Latest message", str(self.test_conversation_guid), str(self.test_session_guid))
        msg3.timestamp = now
        
        chat.add_message(msg1)
        chat.add_message(msg2)
        chat.add_message(msg3)
        
        # Filter messages from last hour
        recent_messages = chat.get_messages(start_time=hour_ago)
        assert len(recent_messages) == 2
        assert "Recent message" in [msg.content for msg in recent_messages]
        assert "Latest message" in [msg.content for msg in recent_messages]
        
        # Filter messages before one hour ago
        old_messages = chat.get_messages(end_time=hour_ago)
        assert len(old_messages) == 2  # Includes the message at exactly hour_ago
    
    def test_tags_validation(self):
        """Test tag validation and cleaning"""
        chat = ZairaChat(
            user_guid=self.test_user_guid,
            tags=["  duplicate  ", "normal", "duplicate", "  ", ""]
        )
        
        # Should clean and deduplicate tags
        assert "duplicate" in chat.tags
        assert chat.tags.count("duplicate") == 1  # No duplicates
        assert "normal" in chat.tags
        assert "  " not in chat.tags  # Whitespace-only removed
        assert "" not in chat.tags  # Empty strings removed
    
    def test_call_trace_functionality(self):
        """Test call_trace functionality in chat sessions"""
        chat = ZairaChat(user_guid=self.test_user_guid)
        
        # Add messages with call_trace
        msg1 = ZairaMessage.create_assistant_message(
            "First response", 
            str(self.test_conversation_guid), 
            str(self.test_session_guid),
            call_trace=["supervisor1", "tool1", "output1"]
        )
        msg2 = ZairaMessage.create_assistant_message(
            "Second response",
            str(self.test_conversation_guid), 
            str(self.test_session_guid), 
            call_trace=["supervisor2", "tool1", "output2"]  # tool1 repeated
        )
        msg3 = ZairaMessage.create_user_message(
            "User message",
            str(self.test_conversation_guid), 
            str(self.test_session_guid)
        )
        
        chat.add_message(msg1)
        chat.add_message(msg2)
        chat.add_message(msg3)
        
        # Test getting messages with call_trace
        messages_with_trace = chat.get_messages_with_call_trace()
        assert len(messages_with_trace) == 2  # Only assistant messages
        assert all(msg.is_from_assistant() for msg in messages_with_trace)
        assert all(msg.call_trace for msg in messages_with_trace)
        
        # Test call trace summary
        trace_summary = chat.get_call_trace_summary()
        expected_summary = {
            "supervisor1": 1, 
            "tool1": 2,  # Appears in both messages
            "output1": 1,
            "supervisor2": 1,
            "output2": 1
        }
        assert trace_summary == expected_summary
        
        # Test summary stats include call_trace info
        stats = chat.get_summary_stats()
        assert "messages_with_call_trace_count" in stats
        assert stats["messages_with_call_trace_count"] == 2
        assert "call_trace_summary" in stats
        assert stats["call_trace_summary"] == expected_summary