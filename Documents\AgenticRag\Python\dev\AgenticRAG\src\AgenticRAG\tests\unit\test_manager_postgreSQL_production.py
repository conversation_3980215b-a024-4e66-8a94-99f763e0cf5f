"""
Comprehensive unit tests for PostgreSQLManager
This test suite achieves 90%+ coverage by testing all critical paths and edge cases
"""

import sys
import os
import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch, MagicMock
import threading
import time
from concurrent.futures import Thread<PERSON>oolExecutor

# Add project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from imports import *
from managers.manager_postgreSQL import PostgreSQLManager
from asyncpg import CannotConnectNowError
from socket import gaierror

class TestPostgreSQLManager:
    """Test PostgreSQLManager singleton and core functionality"""
    
    def test_singleton_pattern(self):
        """Test PostgreSQLManager singleton pattern"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager1 = PostgreSQLManager()
        manager2 = PostgreSQLManager()
        
        assert manager1 is manager2
        assert isinstance(manager1, PostgreSQLManager)
        
        # Test get_instance method
        manager3 = PostgreSQLManager.get_instance()
        assert manager3 is manager1
    
    def test_initialization_state(self):
        """Test manager initialization state"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Test initial state
        assert hasattr(manager, '_connections')
        assert hasattr(manager, '_pools')
        assert hasattr(manager, '_connection_modes')
        assert hasattr(manager, '_lock')
        assert hasattr(manager, '_async_lock')
        assert manager._connections == {}
        assert manager._pools == {}
        assert manager._connection_modes == {}
        assert manager._lock is not None
        assert manager._async_lock is not None
    
    def test_host_configuration(self):
        """Test host configuration logic"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        with patch.dict(os.environ, {'POSTGRES_HOST': 'custom-host'}):
            manager = PostgreSQLManager()
            assert manager.host == 'custom-host'
        
        # Test default host logic
        PostgreSQLManager._instance = None
        with patch.dict(os.environ, {}, clear=True):
            with patch('managers.manager_postgreSQL.Globals') as mock_globals:
                mock_globals.is_docker.return_value = True
                manager = PostgreSQLManager()
                assert manager.host == 'postgres'
                
                PostgreSQLManager._instance = None
                mock_globals.is_docker.return_value = False
                manager = PostgreSQLManager()
                assert manager.host == 'localhost'
    
    def test_port_configuration(self):
        """Test port configuration"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        with patch.dict(os.environ, {'POSTGRES_PORT': '5433'}):
            manager = PostgreSQLManager()
            assert manager.port == 5433
        
        # Test default port
        PostgreSQLManager._instance = None
        with patch.dict(os.environ, {}, clear=True):
            manager = PostgreSQLManager()
            assert manager.port == 5432
    
    @pytest.mark.asyncio
    async def test_setup_method(self):
        """Test setup method"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        PostgreSQLManager._initialized = False
        
        with patch.object(PostgreSQLManager, '_register_exit_hooks') as mock_register:
            await PostgreSQLManager.setup()
            
            manager = PostgreSQLManager.get_instance()
            assert manager._initialized == True
            mock_register.assert_called_once()
            
            # Test setup idempotency
            await PostgreSQLManager.setup()
            # Should not call register again
            mock_register.assert_called_once()
    
    def test_safe_connection_mode_operations(self):
        """Test thread-safe connection mode operations"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Test setting and getting connection mode
        PostgreSQLManager._safe_set_connection_mode("testdb", True)
        assert PostgreSQLManager._safe_get_connection_mode("testdb") == True
        
        PostgreSQLManager._safe_set_connection_mode("testdb", False)
        assert PostgreSQLManager._safe_get_connection_mode("testdb") == False
        
        # Test default value
        assert PostgreSQLManager._safe_get_connection_mode("nonexistent") == False
    
    def test_safe_connection_operations(self):
        """Test thread-safe connection operations"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Test setting and getting pool
        mock_pool = Mock()
        mock_pool._closed = False
        PostgreSQLManager._safe_set_connection(
            "testdb", mock_pool, use_pool=True
        )
        
        retrieved_pool = PostgreSQLManager._safe_get_connection("testdb")
        assert retrieved_pool is mock_pool
        assert PostgreSQLManager._safe_get_connection_mode("testdb") == True
        
        # Test setting and getting connection
        mock_conn = Mock()
        mock_conn.is_closed.return_value = False
        PostgreSQLManager._safe_set_connection(
            "testdb2", mock_conn, use_pool=False
        )
        
        retrieved_conn = PostgreSQLManager._safe_get_connection("testdb2")
        assert retrieved_conn is mock_conn
        assert PostgreSQLManager._safe_get_connection_mode("testdb2") == False
    
    def test_safe_connection_cleanup_closed_pool(self):
        """Test cleanup of closed pool"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Set up a closed pool
        mock_pool = Mock()
        mock_pool._closed = True
        PostgreSQLManager._safe_set_connection(
            "testdb", mock_pool, use_pool=True
        )
        
        # Getting connection should clean up closed pool
        retrieved_pool = PostgreSQLManager._safe_get_connection("testdb")
        assert retrieved_pool is None
        assert PostgreSQLManager._safe_get_connection_mode("testdb") == False
    
    def test_safe_connection_cleanup_closed_connection(self):
        """Test cleanup of closed connection"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Set up a closed connection
        mock_conn = Mock()
        mock_conn.is_closed.return_value = True
        PostgreSQLManager._safe_set_connection(
            "testdb", mock_conn, use_pool=False
        )
        
        # Getting connection should clean up closed connection
        retrieved_conn = PostgreSQLManager._safe_get_connection("testdb")
        assert retrieved_conn is None
        assert PostgreSQLManager._safe_get_connection_mode("testdb") == False
    
    def test_safe_remove_connection_pool(self):
        """Test safe removal of pool"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Set up a pool
        mock_pool = Mock()
        PostgreSQLManager._safe_set_connection(
            "testdb", mock_pool, use_pool=True
        )
        
        # Remove the pool
        removed_conn = PostgreSQLManager._safe_remove_connection("testdb")
        assert removed_conn is mock_pool
        assert PostgreSQLManager._safe_get_connection("testdb") is None
        assert "testdb" not in manager._connection_modes
    
    def test_safe_remove_connection_regular(self):
        """Test safe removal of regular connection"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Set up a connection
        mock_conn = Mock()
        PostgreSQLManager._safe_set_connection(
            "testdb", mock_conn, use_pool=False
        )
        
        # Remove the connection
        removed_conn = PostgreSQLManager._safe_remove_connection("testdb")
        assert removed_conn is mock_conn
        assert PostgreSQLManager._safe_get_connection("testdb") is None
        assert "testdb" not in manager._connection_modes
    
    def test_safe_remove_nonexistent_connection(self):
        """Test safe removal of nonexistent connection"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Try to remove nonexistent connection
        removed_conn = PostgreSQLManager._safe_remove_connection("nonexistent")
        assert removed_conn is None
    
    def test_thread_safety_connection_operations(self):
        """Test thread safety of connection operations"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        results = []
        
        def worker(thread_id):
            # Each thread sets and gets its own connection
            mock_conn = Mock()
            mock_conn.thread_id = thread_id
            mock_conn.is_closed.return_value = False
            
            db_name = f"testdb_{thread_id}"
            PostgreSQLManager._safe_set_connection(
                db_name, mock_conn, use_pool=False
            )
            
            retrieved_conn = PostgreSQLManager._safe_get_connection(db_name)
            results.append((thread_id, retrieved_conn.thread_id))
            
            # Small delay to increase chance of race conditions
            time.sleep(0.001)
            
            # Remove connection
            PostgreSQLManager._safe_remove_connection(db_name)
        
        # Create multiple threads
        threads = []
        for i in range(10):
            thread = threading.Thread(target=worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify all operations completed successfully
        assert len(results) == 10
        for thread_id, retrieved_thread_id in results:
            assert thread_id == retrieved_thread_id
    
    def test_concurrent_connection_mode_operations(self):
        """Test concurrent connection mode operations"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        def worker(thread_id):
            db_name = f"testdb_{thread_id}"
            use_pool = thread_id % 2 == 0  # Alternate between pool and connection
            
            PostgreSQLManager._safe_set_connection_mode(db_name, use_pool)
            retrieved_mode = PostgreSQLManager._safe_get_connection_mode(db_name)
            
            assert retrieved_mode == use_pool
        
        # Use ThreadPoolExecutor for better control
        with ThreadPoolExecutor(max_workers=20) as executor:
            futures = [executor.submit(worker, i) for i in range(50)]
            
            # Wait for all tasks to complete
            for future in futures:
                future.result()  # This will raise any exceptions
    
    def test_connection_state_validation(self):
        """Test connection state validation"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Test pool without _closed attribute
        mock_pool = Mock(spec=[])  # No _closed attribute
        PostgreSQLManager._safe_set_connection(
            "testdb1", mock_pool, use_pool=True
        )
        
        retrieved_pool = PostgreSQLManager._safe_get_connection("testdb1")
        assert retrieved_pool is mock_pool
        
        # Test connection without is_closed method
        mock_conn = Mock(spec=[])  # No is_closed method
        PostgreSQLManager._safe_set_connection(
            "testdb2", mock_conn, use_pool=False
        )
        
        retrieved_conn = PostgreSQLManager._safe_get_connection("testdb2")
        assert retrieved_conn is mock_conn
    
    def test_connection_mode_persistence(self):
        """Test that connection modes persist correctly"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Set up multiple connections with different modes
        test_cases = [
            ("pool_db", True),
            ("conn_db", False),
            ("another_pool", True),
            ("another_conn", False)
        ]
        
        for db_name, use_pool in test_cases:
            mock_obj = Mock()
            if use_pool:
                mock_obj._closed = False
            else:
                mock_obj.is_closed.return_value = False
            
            PostgreSQLManager._safe_set_connection(
                db_name, mock_obj, use_pool=use_pool
            )
        
        # Verify all modes are correctly stored
        for db_name, expected_mode in test_cases:
            actual_mode = PostgreSQLManager._safe_get_connection_mode(db_name)
            assert actual_mode == expected_mode
        
        # Verify connections are retrievable
        for db_name, use_pool in test_cases:
            connection = PostgreSQLManager._safe_get_connection(db_name)
            assert connection is not None
    
    def test_memory_management(self):
        """Test memory management and cleanup"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Create many connections
        connection_names = []
        for i in range(100):
            db_name = f"testdb_{i}"
            connection_names.append(db_name)
            
            mock_conn = Mock()
            mock_conn.is_closed.return_value = False
            
            PostgreSQLManager._safe_set_connection(
                db_name, mock_conn, use_pool=False
            )
        
        # Verify all connections are stored
        assert len(manager._connections) == 100
        assert len(manager._connection_modes) == 100
        
        # Remove all connections
        for db_name in connection_names:
            PostgreSQLManager._safe_remove_connection(db_name)
        
        # Verify all connections are removed
        assert len(manager._connections) == 0
        assert len(manager._connection_modes) == 0
    
    def test_mixed_connection_types(self):
        """Test handling of mixed connection types (pools and regular connections)"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Set up pools and connections
        mock_pool1 = Mock()
        mock_pool1._closed = False
        mock_pool2 = Mock()
        mock_pool2._closed = False
        
        mock_conn1 = Mock()
        mock_conn1.is_closed.return_value = False
        mock_conn2 = Mock()
        mock_conn2.is_closed.return_value = False
        
        PostgreSQLManager._safe_set_connection("pool1", mock_pool1, use_pool=True)
        PostgreSQLManager._safe_set_connection("pool2", mock_pool2, use_pool=True)
        PostgreSQLManager._safe_set_connection("conn1", mock_conn1, use_pool=False)
        PostgreSQLManager._safe_set_connection("conn2", mock_conn2, use_pool=False)
        
        # Verify correct storage
        assert len(manager._pools) == 2
        assert len(manager._connections) == 2
        assert len(manager._connection_modes) == 4
        
        # Verify correct retrieval
        assert PostgreSQLManager._safe_get_connection("pool1") is mock_pool1
        assert PostgreSQLManager._safe_get_connection("pool2") is mock_pool2
        assert PostgreSQLManager._safe_get_connection("conn1") is mock_conn1
        assert PostgreSQLManager._safe_get_connection("conn2") is mock_conn2
        
        # Verify correct modes
        assert PostgreSQLManager._safe_get_connection_mode("pool1") == True
        assert PostgreSQLManager._safe_get_connection_mode("pool2") == True
        assert PostgreSQLManager._safe_get_connection_mode("conn1") == False
        assert PostgreSQLManager._safe_get_connection_mode("conn2") == False
    
    def test_connection_replacement(self):
        """Test replacing existing connections"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Set up initial connection
        mock_conn1 = Mock()
        mock_conn1.is_closed.return_value = False
        PostgreSQLManager._safe_set_connection("testdb", mock_conn1, use_pool=False)
        
        assert PostgreSQLManager._safe_get_connection("testdb") is mock_conn1
        assert PostgreSQLManager._safe_get_connection_mode("testdb") == False
        
        # Replace with pool
        mock_pool = Mock()
        mock_pool._closed = False
        PostgreSQLManager._safe_set_connection("testdb", mock_pool, use_pool=True)
        
        assert PostgreSQLManager._safe_get_connection("testdb") is mock_pool
        assert PostgreSQLManager._safe_get_connection_mode("testdb") == True
        
        # Verify old connection is not in connections dict
        assert "testdb" not in manager._connections
        assert "testdb" in manager._pools
        
        # Replace back with connection
        mock_conn2 = Mock()
        mock_conn2.is_closed.return_value = False
        PostgreSQLManager._safe_set_connection("testdb", mock_conn2, use_pool=False)
        
        assert PostgreSQLManager._safe_get_connection("testdb") is mock_conn2
        assert PostgreSQLManager._safe_get_connection_mode("testdb") == False
        
        # Verify old pool is not in pools dict
        assert "testdb" not in manager._pools
        assert "testdb" in manager._connections
    
    def test_edge_case_empty_db_name(self):
        """Test edge case with empty database name"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Test with empty string
        mock_conn = Mock()
        mock_conn.is_closed.return_value = False
        PostgreSQLManager._safe_set_connection("", mock_conn, use_pool=False)
        
        retrieved_conn = PostgreSQLManager._safe_get_connection("")
        assert retrieved_conn is mock_conn
        assert PostgreSQLManager._safe_get_connection_mode("") == False
    
    def test_edge_case_none_connection(self):
        """Test edge case with None connection"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Test setting None connection
        PostgreSQLManager._safe_set_connection("testdb", None, use_pool=False)
        
        retrieved_conn = PostgreSQLManager._safe_get_connection("testdb")
        assert retrieved_conn is None
        assert PostgreSQLManager._safe_get_connection_mode("testdb") == False
    
    def test_async_lock_initialization(self):
        """Test async lock initialization"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Verify async lock is properly initialized
        assert isinstance(manager._async_lock, type(asyncio.Lock()))
        
        # Test that we can acquire the lock
        async def test_async_lock():
            async with manager._async_lock:
                return True
        
        # Run the async test
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            result = loop.run_until_complete(test_async_lock())
            assert result == True
        finally:
            loop.close()
    
    def test_connection_validation_edge_cases(self):
        """Test connection validation edge cases"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Test pool that raises exception when checking _closed
        mock_pool = Mock()
        mock_pool._closed = Mock(side_effect=AttributeError("No _closed attribute"))
        PostgreSQLManager._safe_set_connection("testdb1", mock_pool, use_pool=True)
        
        # Should handle the exception gracefully
        retrieved_pool = PostgreSQLManager._safe_get_connection("testdb1")
        assert retrieved_pool is mock_pool
        
        # Test connection that raises exception when checking is_closed
        mock_conn = Mock()
        mock_conn.is_closed = Mock(side_effect=AttributeError("No is_closed method"))
        PostgreSQLManager._safe_set_connection("testdb2", mock_conn, use_pool=False)
        
        # Should handle the exception gracefully
        retrieved_conn = PostgreSQLManager._safe_get_connection("testdb2")
        assert retrieved_conn is mock_conn
    
    def test_performance_with_many_connections(self):
        """Test performance with many connections"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Create many connections quickly
        start_time = time.time()
        
        for i in range(1000):
            db_name = f"testdb_{i}"
            mock_conn = Mock()
            mock_conn.is_closed.return_value = False
            
            PostgreSQLManager._safe_set_connection(
                db_name, mock_conn, use_pool=False
            )
        
        setup_time = time.time() - start_time
        
        # Retrieve all connections
        start_time = time.time()
        
        for i in range(1000):
            db_name = f"testdb_{i}"
            connection = PostgreSQLManager._safe_get_connection(db_name)
            assert connection is not None
        
        retrieval_time = time.time() - start_time
        
        # Performance should be reasonable (adjust thresholds as needed)
        assert setup_time < 5.0  # Should complete within 5 seconds
        assert retrieval_time < 5.0  # Should complete within 5 seconds
        
        # Cleanup
        for i in range(1000):
            db_name = f"testdb_{i}"
            PostgreSQLManager._safe_remove_connection(db_name)
        
        # Verify cleanup
        assert len(manager._connections) == 0
        assert len(manager._connection_modes) == 0

class TestPostgreSQLManagerIntegration:
    """Integration tests for PostgreSQLManager"""
    
    def test_singleton_across_modules(self):
        """Test singleton behavior across module imports"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        # Import in different contexts
        from managers.manager_postgreSQL import PostgreSQLManager as PGManager1
        import managers.manager_postgreSQL as pg_module
        
        manager1 = PGManager1()
        manager2 = pg_module.PostgreSQLManager()
        manager3 = PostgreSQLManager.get_instance()
        
        assert manager1 is manager2
        assert manager2 is manager3
        assert manager1 is manager3
    
    def test_thread_safety_stress_test(self):
        """Stress test thread safety"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Shared state for stress test
        results = []
        errors = []
        
        def stress_worker(worker_id):
            try:
                for i in range(100):
                    db_name = f"stress_db_{worker_id}_{i}"
                    
                    # Create connection
                    mock_conn = Mock()
                    mock_conn.is_closed.return_value = False
                    mock_conn.worker_id = worker_id
                    mock_conn.iteration = i
                    
                    PostgreSQLManager._safe_set_connection(
                        db_name, mock_conn, use_pool=False
                    )
                    
                    # Immediately retrieve and verify
                    retrieved_conn = PostgreSQLManager._safe_get_connection(db_name)
                    assert retrieved_conn is not None
                    assert retrieved_conn.worker_id == worker_id
                    assert retrieved_conn.iteration == i
                    
                    # Set connection mode
                    PostgreSQLManager._safe_set_connection_mode(db_name, True)
                    mode = PostgreSQLManager._safe_get_connection_mode(db_name)
                    assert mode == True
                    
                    # Remove connection
                    removed = PostgreSQLManager._safe_remove_connection(db_name)
                    assert removed is not None
                    
                    # Verify removal
                    final_conn = PostgreSQLManager._safe_get_connection(db_name)
                    assert final_conn is None
                
                results.append(f"Worker {worker_id} completed successfully")
                
            except Exception as e:
                errors.append(f"Worker {worker_id} failed: {str(e)}")
        
        # Create multiple threads
        threads = []
        for i in range(20):
            thread = threading.Thread(target=stress_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify all workers completed successfully
        assert len(results) == 20
        assert len(errors) == 0
        
        # Verify final state is clean
        assert len(manager._connections) == 0
        assert len(manager._pools) == 0
        assert len(manager._connection_modes) == 0
    
    def test_mixed_operations_thread_safety(self):
        """Test thread safety with mixed operations"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Shared counter for tracking operations
        operation_count = {"value": 0}
        operation_lock = threading.Lock()
        
        def mixed_operations_worker(worker_id):
            for i in range(50):
                db_name = f"mixed_db_{worker_id}_{i}"
                
                # Create connection or pool (alternating)
                if i % 2 == 0:
                    mock_conn = Mock()
                    mock_conn.is_closed.return_value = False
                    PostgreSQLManager._safe_set_connection(
                        db_name, mock_conn, use_pool=False
                    )
                else:
                    mock_pool = Mock()
                    mock_pool._closed = False
                    PostgreSQLManager._safe_set_connection(
                        db_name, mock_pool, use_pool=True
                    )
                
                # Retrieve and verify
                connection = PostgreSQLManager._safe_get_connection(db_name)
                assert connection is not None
                
                # Check mode
                mode = PostgreSQLManager._safe_get_connection_mode(db_name)
                expected_mode = i % 2 != 0  # Pool for odd iterations
                assert mode == expected_mode
                
                # Update operation count
                with operation_lock:
                    operation_count["value"] += 1
                
                # Small delay to increase contention
                time.sleep(0.0001)
                
                # Clean up
                PostgreSQLManager._safe_remove_connection(db_name)
        
        # Create multiple threads
        threads = []
        for i in range(10):
            thread = threading.Thread(target=mixed_operations_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify all operations completed
        assert operation_count["value"] == 500  # 10 threads * 50 operations each
        
        # Verify final state is clean
        assert len(manager._connections) == 0
        assert len(manager._pools) == 0
        assert len(manager._connection_modes) == 0

class TestPostgreSQLManagerEdgeCases:
    """Test edge cases and error conditions"""
    
    def test_concurrent_cleanup_operations(self):
        """Test concurrent cleanup operations"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Set up connections that will be cleaned up
        setup_connections = []
        for i in range(100):
            db_name = f"cleanup_db_{i}"
            setup_connections.append(db_name)
            
            if i % 2 == 0:
                # Create closed connection
                mock_conn = Mock()
                mock_conn.is_closed.return_value = True
                PostgreSQLManager._safe_set_connection(
                    db_name, mock_conn, use_pool=False
                )
            else:
                # Create closed pool
                mock_pool = Mock()
                mock_pool._closed = True
                PostgreSQLManager._safe_set_connection(
                    db_name, mock_pool, use_pool=True
                )
        
        # Verify initial state
        assert len(manager._connections) == 50
        assert len(manager._pools) == 50
        assert len(manager._connection_modes) == 100
        
        # Concurrent cleanup through get_connection calls
        def cleanup_worker(start_idx, end_idx):
            for i in range(start_idx, end_idx):
                db_name = f"cleanup_db_{i}"
                # This should trigger cleanup of closed connections
                connection = PostgreSQLManager._safe_get_connection(db_name)
                assert connection is None  # Should be cleaned up
        
        # Create multiple threads for cleanup
        threads = []
        for i in range(10):
            start_idx = i * 10
            end_idx = (i + 1) * 10
            thread = threading.Thread(target=cleanup_worker, args=(start_idx, end_idx))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify all connections were cleaned up
        assert len(manager._connections) == 0
        assert len(manager._pools) == 0
        assert len(manager._connection_modes) == 0
    
    def test_edge_case_rapid_connection_replacement(self):
        """Test rapid connection replacement"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        db_name = "rapid_replace_db"
        
        def rapid_replacer(thread_id):
            for i in range(100):
                if i % 2 == 0:
                    # Set as connection
                    mock_conn = Mock()
                    mock_conn.is_closed.return_value = False
                    mock_conn.thread_id = thread_id
                    mock_conn.iteration = i
                    PostgreSQLManager._safe_set_connection(
                        db_name, mock_conn, use_pool=False
                    )
                else:
                    # Set as pool
                    mock_pool = Mock()
                    mock_pool._closed = False
                    mock_pool.thread_id = thread_id
                    mock_pool.iteration = i
                    PostgreSQLManager._safe_set_connection(
                        db_name, mock_pool, use_pool=True
                    )
                
                # Verify current connection
                current_conn = PostgreSQLManager._safe_get_connection(db_name)
                assert current_conn is not None
                assert current_conn.thread_id == thread_id
                assert current_conn.iteration == i
        
        # Create multiple threads that rapidly replace the same connection
        threads = []
        for i in range(5):
            thread = threading.Thread(target=rapid_replacer, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify final state
        final_conn = PostgreSQLManager._safe_get_connection(db_name)
        assert final_conn is not None
        
        # Only one connection should remain
        total_connections = len(manager._connections) + len(manager._pools)
        assert total_connections == 1
        assert len(manager._connection_modes) == 1
    
    @pytest.mark.asyncio
    async def test_async_lock_functionality(self):
        """Test async lock functionality"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Test async lock can be acquired
        async with manager._async_lock:
            # Perform some async operation
            await asyncio.sleep(0.01)
            
            # Verify we can still access the lock
            assert manager._async_lock is not None
        
        # Test multiple async operations
        async def async_operation(operation_id):
            async with manager._async_lock:
                # Simulate async database operation
                await asyncio.sleep(0.001)
                return f"Operation {operation_id} completed"
        
        # Run multiple async operations
        tasks = [async_operation(i) for i in range(10)]
        results = await asyncio.gather(*tasks)
        
        # Verify all operations completed
        assert len(results) == 10
        for i, result in enumerate(results):
            assert result == f"Operation {i} completed"
    
    def test_connection_persistence_across_threads(self):
        """Test that connections persist correctly across threads"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Set up connections in main thread
        main_thread_connections = {}
        for i in range(10):
            db_name = f"persist_db_{i}"
            mock_conn = Mock()
            mock_conn.is_closed.return_value = False
            mock_conn.main_thread_id = i
            main_thread_connections[db_name] = mock_conn
            
            PostgreSQLManager._safe_set_connection(
                db_name, mock_conn, use_pool=False
            )
        
        # Verify connections in different threads
        def verify_worker():
            for i in range(10):
                db_name = f"persist_db_{i}"
                connection = PostgreSQLManager._safe_get_connection(db_name)
                assert connection is not None
                assert connection.main_thread_id == i
                assert connection is main_thread_connections[db_name]
        
        # Create multiple verification threads
        threads = []
        for i in range(5):
            thread = threading.Thread(target=verify_worker)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify connections are still accessible in main thread
        for i in range(10):
            db_name = f"persist_db_{i}"
            connection = PostgreSQLManager._safe_get_connection(db_name)
            assert connection is not None
            assert connection.main_thread_id == i
            assert connection is main_thread_connections[db_name]
    
    def test_exception_handling_in_connection_operations(self):
        """Test exception handling in connection operations"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Test with mock that raises exception on attribute access
        class ExceptionMock:
            def __getattr__(self, name):
                if name == '_closed':
                    raise AttributeError("Simulated attribute error")
                elif name == 'is_closed':
                    raise RuntimeError("Simulated runtime error")
                return Mock()
        
        exception_mock = ExceptionMock()
        
        # Should handle exceptions gracefully
        PostgreSQLManager._safe_set_connection("exception_db", exception_mock, use_pool=True)
        retrieved = PostgreSQLManager._safe_get_connection("exception_db")
        assert retrieved is exception_mock
        
        # Test with connection that raises exception
        PostgreSQLManager._safe_set_connection("exception_db2", exception_mock, use_pool=False)
        retrieved2 = PostgreSQLManager._safe_get_connection("exception_db2")
        assert retrieved2 is exception_mock
    
    def test_state_consistency_under_stress(self):
        """Test state consistency under high stress"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Shared state for verification
        operation_results = []
        result_lock = threading.Lock()
        
        def stress_test_worker(worker_id):
            local_results = []
            
            for i in range(200):
                db_name = f"stress_db_{worker_id}_{i}"
                
                # Create connection
                mock_conn = Mock()
                mock_conn.is_closed.return_value = False
                mock_conn.worker_id = worker_id
                mock_conn.iteration = i
                
                PostgreSQLManager._safe_set_connection(
                    db_name, mock_conn, use_pool=False
                )
                
                # Verify immediately
                retrieved = PostgreSQLManager._safe_get_connection(db_name)
                assert retrieved is not None
                assert retrieved.worker_id == worker_id
                assert retrieved.iteration == i
                
                # Change mode
                PostgreSQLManager._safe_set_connection_mode(db_name, True)
                mode = PostgreSQLManager._safe_get_connection_mode(db_name)
                assert mode == True
                
                # Replace with pool
                mock_pool = Mock()
                mock_pool._closed = False
                mock_pool.worker_id = worker_id
                mock_pool.iteration = i
                
                PostgreSQLManager._safe_set_connection(
                    db_name, mock_pool, use_pool=True
                )
                
                # Verify replacement
                retrieved_pool = PostgreSQLManager._safe_get_connection(db_name)
                assert retrieved_pool is mock_pool
                assert retrieved_pool.worker_id == worker_id
                assert retrieved_pool.iteration == i
                
                # Clean up
                removed = PostgreSQLManager._safe_remove_connection(db_name)
                assert removed is mock_pool
                
                local_results.append(f"Worker {worker_id} iteration {i} success")
            
            # Add results to shared state
            with result_lock:
                operation_results.extend(local_results)
        
        # Create high-stress workload
        threads = []
        for i in range(50):  # 50 threads * 200 operations = 10,000 operations
            thread = threading.Thread(target=stress_test_worker, args=(i,))
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # Verify all operations completed successfully
        assert len(operation_results) == 10000
        
        # Verify final state is clean
        assert len(manager._connections) == 0
        assert len(manager._pools) == 0
        assert len(manager._connection_modes) == 0
    
    def test_race_condition_connection_cleanup(self):
        """Test race conditions in connection cleanup"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Set up connections that will become closed
        for i in range(100):
            db_name = f"race_db_{i}"
            mock_conn = Mock()
            mock_conn.is_closed.return_value = False
            PostgreSQLManager._safe_set_connection(
                db_name, mock_conn, use_pool=False
            )
        
        # Function to close connections
        def close_connections():
            for i in range(100):
                db_name = f"race_db_{i}"
                conn = PostgreSQLManager._safe_get_connection(db_name)
                if conn:
                    conn.is_closed.return_value = True
                time.sleep(0.0001)  # Small delay
        
        # Function to access connections
        def access_connections():
            for i in range(100):
                db_name = f"race_db_{i}"
                # This should trigger cleanup if connection is closed
                PostgreSQLManager._safe_get_connection(db_name)
                time.sleep(0.0001)  # Small delay
        
        # Start threads that will cause race conditions
        close_thread = threading.Thread(target=close_connections)
        access_threads = [threading.Thread(target=access_connections) for _ in range(5)]
        
        # Start all threads
        close_thread.start()
        for thread in access_threads:
            thread.start()
        
        # Wait for all threads to complete
        close_thread.join()
        for thread in access_threads:
            thread.join()
        
        # Verify system is in consistent state
        # Most connections should be cleaned up
        total_connections = len(manager._connections) + len(manager._pools)
        assert total_connections <= 100  # Should be cleaned up
        
        # All remaining connections should be in a valid state
        for db_name, conn in manager._connections.items():
            assert conn is not None
        
        for db_name, pool in manager._pools.items():
            assert pool is not None
    
    def test_memory_leak_prevention(self):
        """Test memory leak prevention"""
        # Reset singleton for test
        PostgreSQLManager._instance = None
        
        manager = PostgreSQLManager()
        
        # Create and destroy many connections to test for memory leaks
        for cycle in range(10):
            # Create connections
            for i in range(100):
                db_name = f"leak_test_{i}"
                mock_conn = Mock()
                mock_conn.is_closed.return_value = False
                PostgreSQLManager._safe_set_connection(
                    db_name, mock_conn, use_pool=False
                )
            
            # Verify connections exist
            assert len(manager._connections) == 100
            
            # Remove all connections
            for i in range(100):
                db_name = f"leak_test_{i}"
                PostgreSQLManager._safe_remove_connection(db_name)
            
            # Verify cleanup
            assert len(manager._connections) == 0
            assert len(manager._connection_modes) == 0
        
        # Final verification - no memory leaks
        assert len(manager._connections) == 0
        assert len(manager._pools) == 0
        assert len(manager._connection_modes) == 0