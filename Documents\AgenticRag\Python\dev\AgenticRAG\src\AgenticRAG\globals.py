# imports.py

# ------------------------------------------------
# -             Configuration globals            -
# ------------------------------------------------
EMBEDDING_MODEL = "BAAI/bge-small-en-v1.5"
LLM_MODEL = "dolphin-llama3"
AGENT_MODEL_OLLAMA = "llama3.1:8b"

CHUNK_SIZE = 4000
CHUNK_OVERLAP = 800
EMBEDDING_SIZE = 384
TIMEOUT_LIMIT = 360 # seconds

# Test environment detection
def is_test_environment():
    """Detect if running in test environment (pytest)"""
    import sys
    import os
    return 'pytest' in sys.modules or 'PYTEST_CURRENT_TEST' in os.environ

#USED PORTS
IP_PYTHON = ["0.0.0.0"] # localhost #Server is hosted behind IIS + Cloudflare, so only allow local connections
IP_PYTHON_PUBLIC = "proxyhttpaio.askzaira.com"
ZAIRA_PYTHON_PORT = 40999 if is_test_environment() else 41000  # Test port: 40999, Production: 41000
RELAY_PORT = 8083 if is_test_environment() else 8084  # Test port: 8083, Production: 8084
#PORT_AIRBYTE = 41001
PORT_QDRANT = 6333
PORT_OLLAMA = 11434
PORT_POSTGRESQL = 5433
#USED PORTS

# ------------------------------------------------
# -            Standard library globals          -
# ------------------------------------------------
from pathlib import Path
from typing import TYPE_CHECKING

# ------------------------------------------------
# -             Third-party globals              -
# ------------------------------------------------

# ------------------------------------------------
# - Local application / library specific globals -
# ------------------------------------------------

# ------------------------------------------------
# -     Common functions or configurations       -
# ------------------------------------------------
def BASE_DIR():
    saved_value = None
    first_run = True
    
    def save_variable_once(value):
        nonlocal saved_value, first_run
        if first_run:
            saved_value = value
            first_run = False
        return saved_value
    
    # Use globals.py path consistently - this file is in src/AgenticRAG/globals.py
    # So we need parent.parent to get to the root directory (out of src/AgenticRAG)
    globals_file_path = Path(__file__).resolve()  # Always use globals.py path, not caller's __file__
    return save_variable_once(globals_file_path.parent.parent) #Parent once for AgenticRAG folder, then once more for src folder

class Globals:
    Index = None
    Storage = None
    Debug = False
    DebugValues = False

    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from llama_index.core import VectorStoreIndex

    @staticmethod
    def set_index(Index) -> "VectorStoreIndex":
        Globals.Index = Index

    @staticmethod
    def get_index() -> "VectorStoreIndex":
        return Globals.Index

    @staticmethod
    def get_query_engine_default() -> "VectorStoreIndex":
        from llama_index.core.vector_stores import MetadataFilters
        filters = MetadataFilters(filters=[])
        index = Globals.get_index()
        if index is None:
            raise ValueError("Index is not initialized. Call Globals.set_index() first.")
        return index.as_query_engine(
            filters=filters,
            response_mode="tree_summarize",
            similarity_top_k=5,
        )
    
    @staticmethod
    def set_debug(Debug):
        Globals.Debug = Debug

    @staticmethod
    def is_debug() -> bool:
        return Globals.Debug
    
    @staticmethod
    def set_debug_values(DebugValues) -> bool:
        if isinstance(DebugValues, bool):
            Globals.DebugValues = DebugValues
        else:
            Globals.DebugValues = True if 'j' in str(DebugValues) or 'y' in str(DebugValues) else False
    
    @staticmethod
    def is_debug_values() -> bool:
        return Globals.DebugValues
    
    @staticmethod
    def get_endpoint_address() -> str:
        if Globals.is_docker() == True:
            from etc.helper_functions import get_value_from_env
            host = get_value_from_env("ZAIRA_NETWORK_NAME", None)
            port = get_value_from_env("ZAIRA_PYTHON_PORT", ZAIRA_PYTHON_PORT)
            if not host: # No .env file found
                uri = f"https://{IP_PYTHON_PUBLIC}"
            elif host == "localhost":
                uri = f"http://{host}:{port}"
            else:
                uri = f"https://{host}.askzaira.com"
            return uri
        else:
            if Globals.is_debug() == True:
                return f"http://localhost:{ZAIRA_PYTHON_PORT}"
            else:
                return f"https://{IP_PYTHON_PUBLIC}"

    @staticmethod
    def is_docker() -> bool:
        from os import path as os_path
        cgroup_path = '/proc/1/cgroup'
        if os_path.exists(cgroup_path):
            with open(cgroup_path, 'r') as f:
                return 'docker' in f.read() or 'containerd' in f.read()
        return False
    
    @classmethod
    async def favicon_endpoint(cls):
        from os import path as os_path
        from aiohttp import web
        from etc.helper_functions import exception_triggered
        from managers.manager_logfire import LogFire
        favicon_path = os_path.join(os_path.dirname(__file__), 'ui', 'favicon.ico')
        
        if os_path.exists(favicon_path):
            try:
                with open(favicon_path, 'rb') as f:
                    favicon_data = f.read()
                return web.Response(body=favicon_data, content_type='image/x-icon')
            except Exception as e:
                exception_triggered(e, f"Error reading favicon")
                return web.Response(status=404)
        else:
            LogFire.log("FAVICON", f"Favicon not found at: {favicon_path}")
            return web.Response(status=404)
