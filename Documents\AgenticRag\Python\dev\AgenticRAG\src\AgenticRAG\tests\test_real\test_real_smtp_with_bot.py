#!/usr/bin/env python3
"""
Real SMTP Test with Automated Bo<PERSON>
Tests the complete email sending pipeline with automated human-in-the-loop responses
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

from imports import *
from managers.manager_logfire import LogFire
import pytest
import asyncio
import time
from uuid import uuid4
from pathlib import Path

from managers.manager_supervisors import SupervisorManager
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import ZairaUser, PERMISSION_LEVELS
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
from etc.setup import init
from tasks.task_top_level_supervisor import create_top_level_supervisor

# Import the testing bot
import sys
sys.path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))
from endpoints.testing_endpoint import MyBot_Testing

@pytest.mark.asyncio
class TestRealSMTPWithBot:
    """Test real SMTP functionality with automated bot responses"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.test_session_guid = str(uuid4())
        self.test_scheduled_guid = str(uuid4())
        
        # Initialize Globals for testing
        try:
            Globals.Debug = False
            if not hasattr(ZairaSettings, 'IsDebugMode'):
                ZairaSettings.IsDebugMode = False
        except:
            pass
    
    def teardown_method(self):
        """Clean up after each test"""
        try:
            loop = asyncio.get_running_loop()
            tasks = [task for task in asyncio.all_requests(loop) if not task.done()]
            for task in tasks:
                if task != asyncio.current_task():
                    task.cancel()
        except RuntimeError:
            pass
    
    async def _setup_real_system(self):
        """Set up the REAL system components for testing"""
        try:
            LogFire.log("DEBUG", "[TEST] Setting up REAL system for email testing")
            
            import etc.parsers as parsers
            
            # Set up directories
            DATA_DIR = Path("/tmp/test_smtp_real_data")
            PERSIST_DIR = Path("/tmp/test_smtp_real_persist")
            
            # Create directories
            DATA_DIR.mkdir(parents=True, exist_ok=True)
            PERSIST_DIR.mkdir(parents=True, exist_ok=True)
            
            # Create test documents
            test_docs = [
                ("email_templates.txt", "Standard email templates for business communication"),
                ("smtp_config.txt", "SMTP configuration and email settings"),
                ("test_data.txt", "Test data for email verification")
            ]
            
            for filename, content in test_docs:
                doc_path = DATA_DIR / filename
                if not doc_path.exists():
                    doc_path.write_text(content)
            
            # Initialize full system
            await init(newProject=True, DATA_DIR=DATA_DIR, PERSIST_DIR=PERSIST_DIR, parsers=parsers)
            
            # Verify managers are initialized
            await SupervisorManager.setup()
            
            # Create supervisors
            LogFire.log("DEBUG", "[TEST] Creating supervisors for email testing")
            
            from managers.manager_prompts import PromptManager
            await PromptManager.setDefaultPrompts()
            
            top_supervisor = await create_top_level_supervisor()
            
            # Initialize email supervisors
            try:
                from tasks.processing.task_email_generator import create_task_email_generator
                from tasks.outputs.output_requests.task_out_email import create_supervisor_email_sender
                
                await create_task_email_generator()
                await create_supervisor_email_sender()
                LogFire.log("DEBUG", "[TEST] Email supervisors created successfully")
            except Exception as e:
                LogFire.log("DEBUG", f"[TEST] Warning: Could not create email supervisors: {e}")
            
            # Verify LLM
            if not hasattr(ZairaSettings, 'llm') or ZairaSettings.llm is None:
                LogFire.log("DEBUG", "[TEST] Warning: No LLM configured - this may affect email generation")
            
            return True
            
        except Exception as e:
            LogFire.log("DEBUG", f"[TEST] Real system setup failed: {e}")
            return False
    
    async def test_smtp_email_with_automated_bot(self):
        """Test SMTP email sending with automated bot responses"""
        
        # Set up real system
        setup_success = await self._setup_real_system()
        if not setup_success:
            pytest.skip("Could not initialize real system for email testing")
        
        try:
            # Create test user
            user_manager = ZairaUserManager.get_instance()
            test_user = await user_manager.add_user(
                "test_platform",
                PERMISSION_LEVELS.ADMIN,
                uuid4(),
                uuid4()
            )
            
            # Set user email for email functionality
            test_user.email = "<EMAIL>"
            
            # Create testing bot that will handle human-in-the-loop requests
            bot_generic = MyBot_Testing(None, "Testing")
            
            query = "send a test <NAME_EMAIL>"
            
            LogFire.log("DEBUG", f"[TEST] Executing SMTP test with query: '{query}'")
            LogFire.log("DEBUG", "[TEST] Bot will automatically handle approval prompts")
            
            # Start the request
            start_time = time.time()
            
            # Use on_message to go through the full pipeline including bot responses
            initial_request = await test_user.on_message(
                complete_message=query,
                calling_bot=bot_generic,
                attachments=[],
                original_message=None
            )
            
            LogFire.log("DEBUG", f"[TEST] Initial request created: {initial_request.scheduled_guid}")
            
            # Monitor request progression more carefully
            for i in range(30):  # 30 second timeout
                await asyncio.sleep(1)
                
                # Check for active requests
                active_request_count = len(test_user.my_requests)
                LogFire.log("DEBUG", f"[TEST] Second {i+1}: {active_request_count} active requests")
                
                # Check if there are any human-in-the-loop requests
                hitl_request = test_user.get_active_hitl_request()
                if hitl_request:
                    LogFire.log("DEBUG", f"[TEST] Found HITL request: {hitl_request.scheduled_guid}")
                    
                    # Check the callback details
                    if hasattr(hitl_request, 'human_in_the_loop_callback'):
                        LogFire.log("DEBUG", f"[TEST] HITL callback exists: {hitl_request.human_in_the_loop_callback is not None}")
                
                # If no active requests, we're done
                if not test_user.has_active_requests():
                    LogFire.log("DEBUG", f"[TEST] All requests completed after {i+1} seconds")
                    break
            
            execution_time = time.time() - start_time
            
            LogFire.log("DEBUG", f"[TEST] Execution completed in {execution_time:.2f} seconds")
            
            # Verify results
            recent_messages = []
            try:
                recent_messages = test_user.get_recent_messages(limit=10)
                LogFire.log("DEBUG", f"[TEST] Found {len(recent_messages)} recent messages")
                
                # Look for email-related messages
                email_messages = [
                    msg for msg in recent_messages 
                    if any(keyword in msg.content.lower() for keyword in ['email', 'mail', 'sent', 'goedkeur'])
                ]
                
                LogFire.log("DEBUG", f"[TEST] Found {len(email_messages)} email-related messages")
                
                if email_messages:
                    LogFire.log("DEBUG", "[TEST] Email-related messages:")
                    for i, msg in enumerate(email_messages[-3:]):  # Show last 3
                        LogFire.log("DEBUG", f"  {i+1}. {msg.content[:100]}...")
                        
            except Exception as e:
                LogFire.log("DEBUG", f"[TEST] Error getting recent messages: {e}")
            
            # Check request details
            LogFire.log("DEBUG", f"[TEST] Final request count: {len(test_user.my_requests)}")
            for request_guid, request in test_user.my_requests.items():
                LogFire.log("DEBUG", f"[TEST] Request {request_guid}: {type(request).__name__}")
                
                if hasattr(request, 'output_demands'):
                    LogFire.log("DEBUG", f"[TEST] Output demands: {request.output_demands}")
                
                if hasattr(request, 'call_trace'):
                    LogFire.log("DEBUG", f"[TEST] Call trace: {request.call_trace}")
            
            # Verify email routing worked
            success_indicators = [
                len(recent_messages) > 0,
                any("email" in msg.content.lower() for msg in recent_messages),
                execution_time < 25  # Completed in reasonable time
            ]
            
            success_count = sum(success_indicators)
            LogFire.log("DEBUG", f"[TEST] Success indicators: {success_count}/3")
            
            # Test assertions - be more lenient for debugging
            assert execution_time < 30, f"Test took too long: {execution_time:.2f} seconds"
            
            LogFire.log("DEBUG", "[TEST] SMTP email test with automated bot COMPLETED")
            
            return {
                "success": True,
                "execution_time": execution_time,
                "message_count": len(recent_messages),
                "email_message_count": len(email_messages) if 'email_messages' in locals() else 0,
                "success_indicators": success_count,
                "final_request_count": len(test_user.my_requests)
            }
            
        except Exception as e:
            LogFire.log("DEBUG", f"[TEST] SMTP test failed: {e}")
            raise
    
    async def test_verify_email_routing_pipeline(self):
        """Test that email routing from generator to sender works correctly"""
        
        # Set up real system
        setup_success = await self._setup_real_system()
        if not setup_success:
            pytest.skip("Could not initialize real system for email testing")
        
        try:
            # Create test user
            user_manager = ZairaUserManager.get_instance()
            test_user = await user_manager.add_user(
                "test_platform",
                PERMISSION_LEVELS.ADMIN,
                uuid4(),
                uuid4()
            )
            
            test_user.email = "<EMAIL>"
            
            # Create testing bot
            bot_generic = MyBot_Testing(None, "Testing")
            
            query = "send a test <NAME_EMAIL>"
            
            LogFire.log("DEBUG", f"[TEST] Verifying email routing pipeline with query: '{query}'")
            
            # Execute the query
            await test_user.on_message(
                complete_message=query,
                calling_bot=bot_generic,
                attachments=[],
                original_message=None
            )
            
            # Wait for processing
            await asyncio.sleep(3)
            
            # Check tasks and their output demands
            pipeline_verification = []
            
            for request_guid, request in test_user.my_requests.items():
                LogFire.log("DEBUG", f"[TEST] Request {request_guid}: {type(request).__name__}")
                
                if hasattr(request, 'output_demands'):
                    LogFire.log("DEBUG", f"[TEST] Output demands: {request.output_demands}")
                    if "email" in request.output_demands:
                        pipeline_verification.append("email_in_output_demands")
                
                if hasattr(request, 'call_trace'):
                    LogFire.log("DEBUG", f"[TEST] Call trace: {request.call_trace}")
                    
                    # Check for email routing
                    call_trace_str = str(request.call_trace).lower()
                    if "email_generator" in call_trace_str:
                        pipeline_verification.append("email_generator_called")
                    if "email_out" in call_trace_str:
                        pipeline_verification.append("email_out_called")
            
            LogFire.log("DEBUG", f"[TEST] Pipeline verification: {pipeline_verification}")
            
            # Verify critical components
            assert len(pipeline_verification) > 0, "Expected pipeline components to be verified"
            
            # Check that email was added to output demands (the fix we implemented)
            email_demand_added = "email_in_output_demands" in pipeline_verification
            LogFire.log("DEBUG", f"[TEST] Email added to output demands: {email_demand_added}")
            
            return {
                "pipeline_verification": pipeline_verification,
                "email_demand_added": email_demand_added,
                "total_requests": len(test_user.my_requests)
            }
            
        except Exception as e:
            LogFire.log("DEBUG", f"[TEST] Email routing verification failed: {e}")
            raise