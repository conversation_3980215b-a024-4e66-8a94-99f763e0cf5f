from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock

# Import the modules to test
from tasks.outputs.task_supervisor_output_sender import create_supervisor_output_sender
from tasks.outputs.output_tasks.task_out_system import create_out_task_system, SupervisorTask_System

class TestOutputSenderSystemIntegration:
    """Test that the output_sender supervisor correctly integrates the system task"""
    
    @pytest.fixture
    def mock_zaira_settings(self):
        """Mock ZairaSettings.llm for tests"""
        with patch('etc.ZairaSettings.ZairaSettings') as mock_settings:
            mock_llm = AsyncMock()
            mock_settings.llm = mock_llm
            yield mock_settings
    
    @pytest.fixture
    def mock_supervisor_manager(self):
        """Mock SupervisorManager for tests"""
        with patch('managers.manager_supervisors.SupervisorManager') as mock_manager:
            mock_instance = MagicMock()
            mock_manager.get_instance.return_value = mock_instance
            mock_manager.register_task = MagicMock(side_effect=lambda x: x)
            mock_manager.register_supervisor = MagicMock(side_effect=lambda x: x)
            yield mock_manager
    
    @pytest.mark.asyncio
    async def test_system_task_creation(self, mock_supervisor_manager, mock_zaira_settings):
        """Test that the system task can be created successfully"""
        system_task = await create_out_task_system()
        
        # Verify the task is created with correct properties
        assert isinstance(system_task, SupervisorTask_System)
        assert system_task.name == "system_out"
        assert system_task._prompt == "You're a task responsible when the output message needs to be directed to SYSTEM."
    
    @pytest.mark.asyncio
    async def test_output_sender_includes_system_task(self, mock_supervisor_manager, mock_zaira_settings):
        """Test that the output_sender supervisor includes the system task"""
        # Mock all the create functions to return mock tasks
        with patch('tasks.outputs.task_supervisor_output_sender.create_out_task_discord') as mock_discord, \
             patch('tasks.outputs.task_supervisor_output_sender.create_out_task_teams') as mock_teams, \
             patch('tasks.outputs.task_supervisor_output_sender.create_out_task_python') as mock_python, \
             patch('tasks.outputs.task_supervisor_output_sender.create_out_task_http') as mock_http, \
             patch('tasks.outputs.task_supervisor_output_sender.create_out_task_whatsapp') as mock_whatsapp, \
             patch('tasks.outputs.task_supervisor_output_sender.create_supervisor_email_sender') as mock_email, \
             patch('tasks.outputs.task_supervisor_output_sender.create_out_task_system') as mock_system:
            
            # Create mock task instances
            mock_discord_task = MagicMock()
            mock_discord_task.name = "discord_out"
            mock_discord.return_value = mock_discord_task
            
            mock_teams_task = MagicMock()
            mock_teams_task.name = "teams_out"
            mock_teams.return_value = mock_teams_task
            
            mock_python_task = MagicMock()
            mock_python_task.name = "python_out"
            mock_python.return_value = mock_python_task
            
            mock_http_task = MagicMock()
            mock_http_task.name = "http_out"
            mock_http.return_value = mock_http_task
            
            mock_whatsapp_task = MagicMock()
            mock_whatsapp_task.name = "whatsapp_out"
            mock_whatsapp.return_value = mock_whatsapp_task
            
            mock_email_task = MagicMock()
            mock_email_task.name = "email_out"
            mock_email.return_value = mock_email_task
            
            mock_system_task = MagicMock()
            mock_system_task.name = "system_out"
            mock_system.return_value = mock_system_task
            
            # Mock the supervisor creation and registration
            mock_supervisor = MagicMock()
            mock_supervisor.add_task = MagicMock(return_value=mock_supervisor)
            mock_supervisor.compile = MagicMock(return_value=mock_supervisor)
            
            with patch('tasks.outputs.task_supervisor_output_sender.SupervisorManager') as mock_supervisor_mgr_local, \
                 patch('tasks.outputs.task_supervisor_output_sender.SupervisorSupervisor') as mock_supervisor_class:
                
                # Setup the mock supervisor
                mock_supervisor_class.return_value = mock_supervisor
                mock_supervisor_mgr_local.register_supervisor.return_value = mock_supervisor
                
                # Create the supervisor
                supervisor = await create_supervisor_output_sender()
                
                # Verify that all tasks were created
                mock_discord.assert_called_once()
                mock_teams.assert_called_once()
                mock_python.assert_called_once()
                mock_http.assert_called_once()
                mock_whatsapp.assert_called_once()
                mock_email.assert_called_once()
                mock_system.assert_called_once()  # This is the key assertion
                
                # Verify that all tasks were added to the supervisor, including the system task
                assert mock_supervisor.add_task.call_count == 7
                
                # Verify compile was called
                mock_supervisor.compile.assert_called_once()
                
                # Verify the supervisor was registered
                mock_supervisor_mgr_local.register_supervisor.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_system_task_llm_call_executes_output_requests(self, mock_supervisor_manager, mock_zaira_settings):
        """Test that the system task's llm_call method executes other output tasks"""
        from managers.manager_users import ZairaUserManager
        from managers.manager_supervisors import SupervisorTaskState
        from langchain_core.messages import HumanMessage
        
        # Create a real system task instance
        direct_prompt = "You're a task responsible when the output message needs to be directed to SYSTEM."
        system_task = SupervisorTask_System(name="system_out", prompt=direct_prompt)
        
        # Mock the user and state
        mock_user = MagicMock()
        
        with patch.object(ZairaUserManager, 'find_user', return_value=mock_user), \
             patch('managers.manager_logfire.LogFire.log') as mock_log_fire:
            
            # Create test state
            test_message = HumanMessage(content="Test system execution")
            state = SupervisorTaskState(
                messages=[test_message],
                user_guid="test-user-guid"
            )
            
            # Mock the Path and file system operations
            with patch('tasks.outputs.output_tasks.task_out_system.Path') as mock_path_class:
                # Create mock file paths
                mock_file1 = MagicMock()
                mock_file1.name = "task_out_discord.py"
                mock_file1.stem = "task_out_discord"
                
                mock_file2 = MagicMock()
                mock_file2.name = "task_out_email.py"
                mock_file2.stem = "task_out_email"
                
                # Mock the path instance and its methods
                mock_path_instance = MagicMock()
                mock_path_instance.parent.glob.return_value = [mock_file1, mock_file2]
                mock_path_class.return_value = mock_path_instance
                
                # Mock importlib operations
                with patch('importlib.util.spec_from_file_location') as mock_spec_from_file, \
                     patch('importlib.util.module_from_spec') as mock_module_from_spec:
                    
                    # Mock module creation and execution
                    mock_spec = MagicMock()
                    mock_loader = MagicMock()
                    mock_spec.loader = mock_loader
                    mock_spec_from_file.return_value = mock_spec
                    
                    mock_module = MagicMock()
                    mock_module_from_spec.return_value = mock_module
                    
                    # Mock the create functions and task instances
                    mock_task_instance = MagicMock()
                    mock_task_instance.llm_call = AsyncMock()
                    
                    # Set up the mock modules to have create functions
                    mock_module.create_out_task_discord = AsyncMock(return_value=mock_task_instance)
                    mock_module.create_out_task_email = AsyncMock(return_value=mock_task_instance)
                    
                    # Execute the system task
                    await system_task.llm_call(state)
                    
                    # Verify that output tasks were executed
                    assert mock_task_instance.llm_call.call_count >= 1
                    
                    # Verify that LogFire.log was called with appropriate messages
                    mock_log_fire.assert_called()
                    
                    # Check that some log calls contain expected system task messages
                    log_calls = [call.args for call in mock_log_fire.call_args_list]
                    system_output_calls = [call for call in log_calls if len(call) > 1 and "System task" in str(call)]
                    assert len(system_output_calls) > 0

if __name__ == "__main__":
    # Run the tests
    pytest.main([__file__, "-v"])