# IMAP Email Send/Receive Testing Guide

This document explains how to test the complete IMAP email send/receive functionality with the AgenticRAG system.

## Test Files

### `test_imap_email_send_receive.py`

Comprehensive test suite for IMAP email functionality that includes:

1. **Mock Email Processing Test** ✅ `test_process_email_function_directly`
   - Tests `_process_email` function with synthetic email data
   - **NO REAL EMAIL SENT** - completely safe to run
   - Validates email processing, file saving, and vector store conversion

2. **OAuth Configuration Validation** ⚠️ `test_oauth_configuration_validation`
   - Validates OAuth2Verifier setup for IMAP/SMTP apps
   - Tests credential retrieval from _verifier_.py configurations
   - Skips gracefully if OAuth not configured

3. **Real Email Send/Receive Test** 🔥 `test_real_email_send_receive_with_oauth`
   - **SENDS ACTUAL EMAILS** using OAuth credentials
   - Complete workflow: send → receive → process → save
   - Only runs if real OAuth credentials are configured

4. **Connection Tests** 
   - `test_imap_connection_only` - Tests IMAP server connection
   - `test_smtp_connection_only` - Tests SMTP server connection
   - Skip if no real credentials available

## OAuth Configuration Requirements

To enable real email testing, you must configure OAuth credentials in the AgenticRAG system:

### IMAP Configuration (for receiving emails)
```
App ID: "imap"
Fields required:
- Server Name (str1): e.g., "imap.transip.nl"
- Network Port (int1): e.g., 993
- Email Address (str2): e.g., "<EMAIL>"  
- Email Password (str3): [actual password/app password]
```

### SMTP Configuration (for sending emails)
```
App ID: "smtp"
Fields required:
- Server Name (str1): e.g., "smtp.transip.nl"
- Network Port (int1): e.g., 587
- Email Address (str2): e.g., "<EMAIL>"
- Email Password (str3): [actual password/app password]
```

### OAuth Field Mapping
The test uses both legacy and new OAuth field mappings:

**Legacy mapping (fallback):**
- access_token → Server Name
- expires_in → Network Port  
- refresh_token → Email Address
- token_type → Email Password

**New mapping (preferred):**
- str1 → Server Name
- int1 → Network Port
- str2 → Email Address  
- str3 → Email Password

## Running the Tests

### Safe Tests (No Real Email)
```bash
# Test _process_email function only (safe)
../../.venv/Scripts/pytest.exe tests/integration/test_imap_email_send_receive.py::TestIMAPEmailSendReceive::test_process_email_function_directly -v

# Test OAuth configuration validation
../../.venv/Scripts/pytest.exe tests/integration/test_imap_email_send_receive.py::TestIMAPEmailSendReceive::test_oauth_configuration_validation -v
```

### Real Email Tests (Requires OAuth)
```bash
# WARNING: This sends real emails!
../../.venv/Scripts/pytest.exe tests/integration/test_imap_email_send_receive.py::TestIMAPEmailSendReceive::test_real_email_send_receive_with_oauth -v

# Test connection capabilities
../../.venv/Scripts/pytest.exe tests/integration/test_imap_email_send_receive.py::TestIMAPEmailSendReceive::test_imap_connection_only -v
../../.venv/Scripts/pytest.exe tests/integration/test_imap_email_send_receive.py::TestIMAPEmailSendReceive::test_smtp_connection_only -v
```

### Run All Tests
```bash
# Run entire test suite
../../.venv/Scripts/pytest.exe tests/integration/test_imap_email_send_receive.py -v
```

## Test Behavior

### Without OAuth Configuration
- ✅ `test_process_email_function_directly` - **RUNS** (processes mock email)
- ⏭️ `test_oauth_configuration_validation` - **SKIPS** (OAuth not configured)
- ⏭️ `test_real_email_send_receive_with_oauth` - **SKIPS** (no credentials)
- ⏭️ Connection tests - **SKIP** (no credentials)

### With OAuth Configuration  
- ✅ `test_process_email_function_directly` - **RUNS** (processes mock email)
- ✅ `test_oauth_configuration_validation` - **RUNS** (validates OAuth setup)
- 🔥 `test_real_email_send_receive_with_oauth` - **RUNS** (sends actual emails!)
- ✅ Connection tests - **RUN** (test actual connections)

## Real Email Test Workflow

When OAuth is properly configured, `test_real_email_send_receive_with_oauth` performs:

1. **OAuth Credential Retrieval** - Gets SMTP/IMAP settings from _verifier_.py
2. **Email Generation** - Creates unique test email with timestamp and ID
3. **SMTP Send** - Sends real email using configured SMTP server
4. **Delivery Wait** - 30 second delay for email propagation
5. **IMAP Connect** - Connects to IMAP server to retrieve emails
6. **Email Search** - Searches for the test email by unique ID (3 min timeout)
7. **Email Validation** - Verifies received email content matches sent email
8. **Process Email** - Calls `_process_email` function with real email data
9. **File Verification** - Confirms email saved to meltano output directory
10. **Vector Store** - Verifies email converted to vector store format

## Security and Safety

### Safe Operations
- Mock email processing (always safe)
- OAuth validation (read-only)
- Connection testing with placeholder credentials (fails safely)

### Real Operations (Require Caution)
- **Actual email sending/receiving** when OAuth configured
- **Real IMAP/SMTP connections** to external servers
- **Email storage** to file system and vector store

## Troubleshooting

### Common Issues

1. **OAuth Not Configured**
   - Tests skip gracefully
   - Configure via AgenticRAG OAuth interface

2. **Invalid Credentials**
   - Connection tests fail
   - Verify server settings and passwords

3. **Email Not Found**
   - Increase search timeout (currently 3 minutes)
   - Check email delivery delays
   - Verify IMAP folder settings

4. **Network Issues**
   - Verify server hostnames and ports
   - Check firewall/network connectivity
   - Ensure TLS/SSL settings match server requirements

### Debug Information

Tests provide detailed logging via LogFire:
- OAuth credential status
- Server connection attempts
- Email search progress
- File processing results

## Example Test Output

### With OAuth Configured (Real Email Test)
```
TASK Starting REAL email test with ID: abc12345
TASK Using SMTP: smtp.transip.nl:587
TASK Using IMAP: imap.transip.nl:993
TASK Email account: <EMAIL>
TASK Sending real test email...
TASK Successfully sent test email with subject: [AgenticRAG Test] Real Email Test abc12345
TASK Waiting for email delivery and server propagation...
TASK Establishing IMAP connection to receive email...
TASK IMAP connection established successfully
TASK Searching for test email with subject containing: abc12345
TASK Successfully found test email with ID: abc12345
TASK Email verification successful - Subject: [AgenticRAG Test] Real Email Test abc12345
TASK Processing email through _process_email function
TASK Email successfully processed through _process_email function
TASK Email successfully saved and processed as: immediate_email_20250708_143022_AgenticRAG_Test_Real_Email_Test_abc12345.eml
TASK === REAL EMAIL TEST COMPLETED SUCCESSFULLY ===
```

### Without OAuth (Skipped Tests)
```
SKIPPED [1] tests/integration/test_imap_email_send_receive.py:551: SMTP OAuth configuration not available - configure via AgenticRAG OAuth interface
```

This comprehensive test suite ensures that the IMAP email functionality works correctly at all levels, from individual function testing to complete end-to-end workflows.