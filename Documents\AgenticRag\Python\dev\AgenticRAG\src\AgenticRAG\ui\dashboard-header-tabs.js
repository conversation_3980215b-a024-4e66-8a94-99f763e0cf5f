// Dashboard TabManager JavaScript - Tab management system and navigation utilities
// Contains: TabManager class, navigation functions, utility functions

<script>
    // Generalized Tab Management System for Reusability
    class TabManager {
        constructor(containerId, options = {}) {
            this.containerId = containerId;
            this.container = document.getElementById(containerId);
            this.options = {
                tabClass: 'generic-tab',
                contentClass: 'generic-content',
                activeClass: 'active',
                onTabSwitch: null,
                persistState: true,
                ...options
            };
            this.tabs = new Map();
            this.activeTabId = null;
        }
        
        // Create tab system with header and content area
        initialize(title, subtitle = '') {
            if (!this.container) return;
            
            const html = `
                <div class="tab-system-header" style="margin-bottom: 2rem;">
                    <h3 style="color: #93c5fd; margin-bottom: 0.5rem; text-transform: uppercase; letter-spacing: 0.05em; font-weight: 600;">${title}</h3>
                    ${subtitle ? `<p style="color: #94a3b8;">${subtitle}</p>` : ''}
                </div>
                <div class="tab-navigation" style="display: flex; gap: 0.5rem; border-bottom: 2px solid rgba(59, 130, 246, 0.2); padding-bottom: 0; margin-bottom: 1rem; overflow-x: auto; overflow-y: hidden; max-width: 100%;"></div>
                <div class="tab-content-area"></div>
            `;
            
            this.container.innerHTML = html;
            return this;
        }
        
        // Add a tab with content
        addTab(id, label, content, metadata = {}) {
            const tabButton = this.createTabButton(id, label, metadata);
            const contentContainer = this.createContentContainer(id, content);
            
            this.tabs.set(id, {
                button: tabButton,
                content: contentContainer,
                metadata: metadata
            });
            
            // Add to DOM
            const navigation = this.container.querySelector('.tab-navigation');
            const contentArea = this.container.querySelector('.tab-content-area');
            
            if (navigation && contentArea) {
                navigation.appendChild(tabButton);
                contentArea.appendChild(contentContainer);
            }
            
            // Set first tab as active
            if (this.tabs.size === 1) {
                this.switchTab(id);
            }
            
            return this;
        }
        
        // Create tab button
        createTabButton(id, label, metadata) {
            const button = document.createElement('button');
            button.className = `${this.options.tabClass}`;
            button.dataset.tabId = id;
            button.style.cssText = `
                padding: 0.75rem 1.25rem; 
                border: 1px solid rgba(59, 130, 246, 0.2); 
                border-radius: 8px 8px 0 0; 
                cursor: pointer; 
                transition: all 0.2s ease; 
                background: rgba(30, 41, 59, 0.5); 
                color: #94a3b8; 
                border-bottom: 3px solid transparent; 
                backdrop-filter: blur(5px); 
                min-width: 140px; 
                white-space: nowrap; 
                font-size: 0.85rem;
            `;
            
            // Create tab content with metadata
            const tabContent = document.createElement('div');
            tabContent.style.textAlign = 'left';
            
            const mainLabel = document.createElement('div');
            mainLabel.style.cssText = 'font-weight: 600; font-size: 0.9rem;';
            mainLabel.textContent = label;
            
            tabContent.appendChild(mainLabel);
            
            if (metadata.subtitle) {
                const subtitle = document.createElement('div');
                subtitle.style.cssText = 'font-size: 0.75rem; opacity: 0.8; margin-top: 0.25rem;';
                subtitle.textContent = metadata.subtitle;
                tabContent.appendChild(subtitle);
            }
            
            button.appendChild(tabContent);
            
            // Add click handler
            button.addEventListener('click', () => this.switchTab(id));
            
            return button;
        }
        
        // Create content container
        createContentContainer(id, content) {
            const container = document.createElement('div');
            container.className = `${this.options.contentClass}`;
            container.dataset.tabId = id;
            container.style.display = 'none';
            
            if (typeof content === 'string') {
                container.innerHTML = content;
            } else if (content instanceof HTMLElement) {
                container.appendChild(content);
            }
            
            return container;
        }
        
        // Switch to a specific tab
        switchTab(id) {
            if (!this.tabs.has(id)) return;
            
            // Hide all content and deactivate all tabs
            this.tabs.forEach((tab, tabId) => {
                tab.content.style.display = 'none';
                tab.content.classList.remove(this.options.activeClass);
                
                tab.button.classList.remove(this.options.activeClass);
                tab.button.style.background = 'rgba(30, 41, 59, 0.5)';
                tab.button.style.color = '#94a3b8';
                tab.button.style.borderBottom = '3px solid transparent';
            });
            
            // Activate selected tab
            const selectedTab = this.tabs.get(id);
            selectedTab.content.style.display = 'block';
            selectedTab.content.classList.add(this.options.activeClass);
            
            selectedTab.button.classList.add(this.options.activeClass);
            selectedTab.button.style.background = 'linear-gradient(135deg, #3b82f6, #6366f1)';
            selectedTab.button.style.color = 'white';
            selectedTab.button.style.borderBottom = '3px solid #3b82f6';
            
            this.activeTabId = id;
            
            // Call callback if provided
            if (this.options.onTabSwitch) {
                this.options.onTabSwitch(id, selectedTab);
            }
            
            // Save state if enabled
            if (this.options.persistState) {
                setTimeout(saveApplicationState, 100);
            }
        }
        
        // Remove a tab
        removeTab(id) {
            if (!this.tabs.has(id)) return;
            
            const tab = this.tabs.get(id);
            tab.button.remove();
            tab.content.remove();
            this.tabs.delete(id);
            
            // If this was the active tab, switch to another
            if (this.activeTabId === id && this.tabs.size > 0) {
                const firstTabId = this.tabs.keys().next().value;
                this.switchTab(firstTabId);
            }
            
            return this;
        }
        
        // Get active tab ID
        getActiveTab() {
            return this.activeTabId;
        }
        
        // Update tab content
        updateTabContent(id, content) {
            if (!this.tabs.has(id)) return;
            
            const tab = this.tabs.get(id);
            if (typeof content === 'string') {
                tab.content.innerHTML = content;
            } else if (content instanceof HTMLElement) {
                tab.content.innerHTML = '';
                tab.content.appendChild(content);
            }
            
            return this;
        }
        
        // Get all tab IDs
        getTabIds() {
            return Array.from(this.tabs.keys());
        }
    }
    
    // Expose TabManager globally to prevent duplicate declarations in footer
    window.TabManager = TabManager;
    
    // Utility functions
    function showError(message) {
        const contentArea = document.getElementById('pageContent');
        contentArea.innerHTML = `<div class="error">${message}</div>`;
    }
    
    function showLoading() {
        const contentArea = document.getElementById('pageContent');
        contentArea.innerHTML = '<div class="loading">Loading...</div>';
    }
    
    // Navigate to Grasshopper History tab for specific grasshopper session
    function navigateToChatSession(chatSessionGuid, userGuid) {
        // Try to get user GUID from parameter, then from input field
        let effectiveUserGuid = userGuid;
        
        if (!effectiveUserGuid || effectiveUserGuid === 'Unknown') {
            const userGuidInput = document.getElementById('userGuidInput');
            if (userGuidInput && userGuidInput.value && userGuidInput.value.trim()) {
                effectiveUserGuid = userGuidInput.value.trim();
            }
        }
        
        // If still no user GUID available, show helpful message but continue
        if (!effectiveUserGuid || effectiveUserGuid === 'Unknown') {
            // Don't block navigation, just inform user
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 10000;
                background: #f59e0b; color: white; padding: 0.75rem 1rem;
                border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                font-size: 0.9rem; max-width: 300px;
            `;
            notification.textContent = `Navigating to grasshopper history. Please select a user first for best results.`;
            document.body.appendChild(notification);
            
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 4000);
            
            effectiveUserGuid = 'system-user'; // Fallback to system user
        }
        
        // Switch to chat-history tab if showTab function exists
        if (typeof showTab === 'function') {
            showTab('chat-history');
        }
        
        // Pre-fill the user GUID input if it exists
        const chatUserGuidInput = document.getElementById('chatUserGuidInput');
        if (chatUserGuidInput) {
            chatUserGuidInput.value = effectiveUserGuid;
        }
        
        // Load the grasshopper history automatically if function exists
        if (typeof loadChatHistory === 'function') {
            // Use setTimeout to ensure the tab switching completes first
            setTimeout(async () => {
                try {
                    await loadChatHistory();
                    
                    // If we have a specific chat session, navigate to it
                    if (chatSessionGuid && chatSessionGuid !== 'Unknown') {
                        // Wait for grasshopper history to render, then switch to the specific session
                        setTimeout(() => {
                            if (typeof switchChatSession === 'function') {
                                switchChatSession(chatSessionGuid);
                            } else {
                                // Fallback: find and click the session tab manually
                                findAndActivateSessionTab(chatSessionGuid);
                            }
                        }, 500);
                    }
                } catch (error) {
                    console.error('Error loading grasshopper history:', error);
                }
            }, 100);
        }
        
        // Show success notification
        const successNotification = document.createElement('div');
        successNotification.style.cssText = `
            position: fixed; top: 20px; right: 20px; z-index: 10000;
            background: #10b981; color: white; padding: 0.75rem 1rem;
            border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            font-size: 0.9rem; max-width: 300px;
        `;
        
        // Show user information in notification
        const userInfo = effectiveUserGuid === userGuid ? 'user' : `user (${effectiveUserGuid.substring(0, 8)}...)`;
        
        if (chatSessionGuid && chatSessionGuid !== 'Unknown') {
            successNotification.textContent = `Navigated to grasshopper history for ${userInfo}. Looking for session ${chatSessionGuid.substring(0, 8)}...`;
        } else {
            successNotification.textContent = `Navigated to grasshopper history for ${userInfo}.`;
        }
        
        document.body.appendChild(successNotification);
        
        // Remove notification after 3 seconds
        setTimeout(() => {
            if (successNotification.parentNode) {
                successNotification.parentNode.removeChild(successNotification);
            }
        }, 3000);
    }
    
    // Helper function to find and activate a specific chat session tab
    function findAndActivateSessionTab(sessionId) {
        try {
            // Look for session buttons/tabs that might contain the session ID
            const chatContainer = document.getElementById('chatContainer');
            if (!chatContainer) return false;
            
            // Search for clickable elements (buttons, tabs) with session ID
            const sessionIdShort = sessionId.substring(0, 8);
            const possibleSelectors = [
                `[data-session-id="${sessionId}"]`,
                `[data-session="${sessionId}"]`,
                `#session-${sessionId}`,
                `#${sessionId}`,
                `.session-tab[data-id="${sessionId}"]`,
                `.chat-session-tab[data-session-id="${sessionId}"]`
            ];
            
            // Try direct selector matches first
            for (const selector of possibleSelectors) {
                const element = chatContainer.querySelector(selector);
                if (element && (element.tagName === 'BUTTON' || element.onclick || element.click)) {
                    element.click();
                    return true;
                }
            }
            
            // Fallback: search for buttons containing the session ID in text or attributes
            const allButtons = chatContainer.querySelectorAll('button, .clickable, [onclick]');
            for (const button of allButtons) {
                const text = button.textContent || '';
                const dataAttrs = [
                    button.dataset.sessionId,
                    button.dataset.session,
                    button.id,
                    button.getAttribute('data-session-id'),
                    button.getAttribute('data-session')
                ].filter(Boolean);
                
                // Check if any of these match our session ID (full or short)
                if (text.includes(sessionId) || text.includes(sessionIdShort) ||
                    dataAttrs.some(attr => attr === sessionId || attr.includes(sessionIdShort))) {
                    button.click();
                    return true;
                }
            }
            
            return false;
            
        } catch (error) {
            console.error('Error finding session tab:', error);
            return false;
        }
    }
    
    // Export utility functions to global scope
    window.showError = showError;
    window.showLoading = showLoading;
    window.navigateToChatSession = navigateToChatSession;
    window.findAndActivateSessionTab = findAndActivateSessionTab;
    
    console.log('[DEBUG] Dashboard TabManager and utilities loaded');
</script>