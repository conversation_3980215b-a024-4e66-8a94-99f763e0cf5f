from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
import pytest_asyncio
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from aiohttp import web, ClientSession
from endpoints.whatsapp_endpoint import MyWhatsappBot
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import PERMISSION_LEVELS
from endpoints.mybot_generic import MyBot_Generic
import json
import asyncio

class TestWhatsAppWorkflow:
    """Integration tests for complete WhatsApp workflow"""
    
    @pytest_asyncio.fixture
    async def setup_whatsapp_environment(self):
        """Setup a complete WhatsApp testing environment"""
        # Reset singleton state
        MyWhatsappBot._instance = None
        MyWhatsappBot._initialized = False
        
        # Mock configuration values
        config_patches = [
            patch('endpoints.whatsapp_endpoint.WHATSAPP_PHONE_NUMBER_ID', 'test-phone-id'),
            patch('endpoints.whatsapp_endpoint.WHATSAPP_ACCESS_TOKEN', 'test-access-token'),
            patch('endpoints.whatsapp_endpoint.WHATSAPP_VERIFY_TOKEN', 'test-verify-token'),
            patch('endpoints.whatsapp_endpoint.WHATSAPP_RECIPIENT_WAID', 'default-recipient')
        ]
        
        for p in config_patches:
            p.start()
        
        yield
        
        for p in config_patches:
            p.stop()
    
    @pytest.fixture
    def complete_webhook_payload(self):
        """Complete WhatsApp webhook payload for testing"""
        return {
            "object": "whatsapp_business_account",
            "entry": [
                {
                    "id": "business_account_id",
                    "changes": [
                        {
                            "value": {
                                "messaging_product": "whatsapp",
                                "metadata": {
                                    "display_phone_number": "***********",
                                    "phone_number_id": "test-phone-id"
                                },
                                "contacts": [
                                    {
                                        "profile": {
                                            "name": "Test User"
                                        },
                                        "wa_id": "**********"
                                    }
                                ],
                                "messages": [
                                    {
                                        "from": "**********",
                                        "id": "wamid.HBgMxxx",
                                        "timestamp": "**********",
                                        "text": {
                                            "body": "Hello! This is a test message."
                                        },
                                        "type": "text"
                                    }
                                ]
                            },
                            "field": "messages"
                        }
                    ]
                }
            ]
        }
    
    @pytest.mark.asyncio
    async def test_complete_message_workflow(self, setup_whatsapp_environment, complete_webhook_payload):
        """Test complete workflow from webhook to response"""
        whatsapp_bot = MyWhatsappBot.get_instance()
        
        # Mock user manager and user
        mock_user = AsyncMock()
        mock_user.on_message = AsyncMock()
        mock_user.session_guid = "test-session-guid"
        mock_user.chat_history = {mock_user.session_guid: []}
        
        # Mock successful message sending
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.headers = {"content-type": "application/json"}
        mock_response.text = AsyncMock(return_value='{"success": true, "messages": [{"id": "sent-msg-id"}]}')
        
        with patch('endpoints.whatsapp_endpoint.ZairaUserManager.get_user', return_value=mock_user):
            with patch('aiohttp.ClientSession') as mock_session:
                mock_session.return_value.__aenter__.return_value.post.return_value.__aenter__.return_value = mock_response
                
                # Process the webhook message
                await MyWhatsappBot.process_webhook(complete_webhook_payload)
                
                # Verify user.on_message was called with correct parameters
                mock_user.on_message.assert_called_once()
                call_args = mock_user.on_message.call_args
                
                # Verify message content
                assert "Hello! This is a test message." in str(call_args)
                assert "**********" in str(call_args)
                
                # Verify original message structure
                original_message = call_args[1]['original_message']
                assert original_message['from'] == "**********"
                assert original_message['id'] == "wamid.HBgMxxx"
                assert original_message['text'] == "Hello! This is a test message."
    
    @pytest.mark.asyncio
    async def test_new_user_registration_workflow(self, setup_whatsapp_environment, complete_webhook_payload):
        """Test workflow for new user registration"""
        whatsapp_bot = MyWhatsappBot.get_instance()
        
        # Mock new user creation
        mock_new_user = AsyncMock()
        mock_new_user.on_message = AsyncMock()
        mock_new_user.session_guid = "new-session-guid"
        mock_new_user.session_guid = "new-conversation-guid"
        mock_new_user.chat_history = {mock_new_user.session_guid: []}
        
        with patch('endpoints.whatsapp_endpoint.ZairaUserManager.get_user', return_value=None):
            with patch('endpoints.whatsapp_endpoint.ZairaUserManager.add_user', return_value=mock_new_user) as mock_add_user:
                with patch('endpoints.whatsapp_endpoint.ZairaUserManager.create_guid', side_effect=["guid1", "guid2"]):
                    
                    await MyWhatsappBot.process_webhook(complete_webhook_payload)
                    
                    # Verify new user was created
                    mock_add_user.assert_called_once_with(
                        "**********",
                        PERMISSION_LEVELS.USER,
                        "guid1",
                        "guid2"
                    )
                    
                    # Verify new user received the message
                    mock_new_user.on_message.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_message_response_workflow(self, setup_whatsapp_environment):
        """Test the workflow of responding to a user message"""
        whatsapp_bot = MyWhatsappBot.get_instance()
        
        # Create bot generic instance
        bot_generic = MyBot_Generic(whatsapp_bot, "Whatsapp")
        
        # Mock task and user
        mock_task = AsyncMock()
        mock_task.user = AsyncMock()
        mock_task.user.session_guid = "test-session"
        mock_task.user.chat_history = {mock_task.user.session_guid: []}
        
        # Mock physical message with sender info
        physical_message = {
            "id": "msg123",
            "from": "**********",
            "text": "Original message",
            "raw_message": {"id": "msg123", "from": "**********"}
        }
        
        # Mock successful message sending
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.headers = {"content-type": "application/json"}
        mock_response.text = AsyncMock(return_value='{"success": true}')
        
        with patch('aiohttp.ClientSession') as mock_session:
            mock_session.return_value.__aenter__.return_value.post.return_value.__aenter__.return_value = mock_response
            
            # Send a response message
            test_response = "Thank you for your message! I have processed your request."
            await bot_generic.send_reply(test_response, mock_task, physical_message)
            
            # Verify message was sent to correct recipient
            call_args = mock_session.return_value.__aenter__.return_value.post.call_args
            payload = json.loads(call_args[1]['data'])
            
            assert payload['to'] == '**********'
            assert payload['text']['body'] == test_response
    
    @pytest.mark.asyncio
    async def test_long_message_splitting_workflow(self, setup_whatsapp_environment):
        """Test workflow for splitting long messages"""
        whatsapp_bot = MyWhatsappBot.get_instance()
        bot_generic = MyBot_Generic(whatsapp_bot, "Whatsapp")
        
        # Create a long message that should be split
        long_message = "This is a very long message. " * 50  # Exceeds 1000 char limit
        
        mock_task = AsyncMock()
        mock_task.user = AsyncMock()
        mock_task.user.session_guid = "test-session"
        mock_task.user.chat_history = {mock_task.user.session_guid: []}
        
        physical_message = {
            "id": "msg123",
            "from": "**********",
            "text": "Original message"
        }
        
        # Mock successful message sending
        mock_response = AsyncMock()
        mock_response.status = 200
        mock_response.headers = {"content-type": "application/json"}
        mock_response.text = AsyncMock(return_value='{"success": true}')
        
        call_count = 0
        
        def track_calls(*args, **kwargs):
            nonlocal call_count
            call_count += 1
            return mock_response.__aenter__()
        
        with patch('aiohttp.ClientSession') as mock_session:
            mock_session.return_value.__aenter__.return_value.post.side_effect = track_calls
            
            await bot_generic.send_reply(long_message, mock_task, physical_message)
            
            # Verify multiple messages were sent (message was split)
            assert call_count > 1
    
    @pytest.mark.asyncio
    async def test_error_handling_workflow(self, setup_whatsapp_environment, complete_webhook_payload):
        """Test error handling in the complete workflow"""
        whatsapp_bot = MyWhatsappBot.get_instance()
        
        # Mock user that raises an exception
        mock_user = AsyncMock()
        mock_user.on_message = AsyncMock(side_effect=Exception("User processing error"))
        
        with patch('endpoints.whatsapp_endpoint.ZairaUserManager.get_user', return_value=mock_user):
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                # Process webhook should not crash even if user processing fails
                await MyWhatsappBot.process_webhook(complete_webhook_payload)
                
                # Exception handler should have been called
                mock_exception.assert_called()
    
    @pytest.mark.asyncio
    async def test_webhook_verification_workflow(self, setup_whatsapp_environment):
        """Test the complete webhook verification workflow"""
        whatsapp_bot = MyWhatsappBot.get_instance()
        
        # Test successful verification
        request = Mock()
        request.remote = "************"  # Facebook IP range
        request.query_string = "hub.mode=subscribe&hub.verify_token=test-verify-token&hub.challenge=challenge123"
        
        response = await whatsapp_bot.whatsapp_verify(request)
        assert response.status == 200
        assert response.text == "challenge123"
        
        # Test verification failure
        request.query_string = "hub.mode=subscribe&hub.verify_token=wrong-token&hub.challenge=challenge123"
        response = await whatsapp_bot.whatsapp_verify(request)
        assert response.status == 403
    
    @pytest.mark.asyncio
    async def test_concurrent_message_processing(self, setup_whatsapp_environment):
        """Test handling multiple concurrent webhook messages"""
        whatsapp_bot = MyWhatsappBot.get_instance()
        
        # Create multiple webhook payloads
        webhooks = []
        for i in range(5):
            webhook = {
                "entry": [{
                    "changes": [{
                        "field": "messages",
                        "value": {
                            "messages": [{
                                "id": f"msg{i}",
                                "from": f"123456789{i}",
                                "text": {"body": f"Message {i}"}
                            }]
                        }
                    }]
                }]
            }
            webhooks.append(webhook)
        
        # Mock user manager
        mock_users = []
        for i in range(5):
            user = AsyncMock()
            user.on_message = AsyncMock()
            user.session_guid = f"session{i}"
            user.session_guid = f"conversation{i}"
            user.chat_history = {user.session_guid: []}
            mock_users.append(user)
        
        with patch('endpoints.whatsapp_endpoint.ZairaUserManager.get_user', side_effect=mock_users):
            # Process all webhooks concurrently
            tasks = [MyWhatsappBot.process_webhook(webhook) for webhook in webhooks]
            await asyncio.gather(*tasks)
            
            # Verify all users received their messages
            for user in mock_users:
                user.on_message.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_message_types_workflow(self, setup_whatsapp_environment):
        """Test handling different message types"""
        whatsapp_bot = MyWhatsappBot.get_instance()
        
        # Test text message
        text_webhook = {
            "entry": [{
                "changes": [{
                    "field": "messages",
                    "value": {
                        "messages": [{
                            "id": "text_msg",
                            "from": "1111111111",
                            "text": {"body": "Text message"},
                            "type": "text"
                        }]
                    }
                }]
            }]
        }
        
        # Test message without text body (should be handled gracefully)
        image_webhook = {
            "entry": [{
                "changes": [{
                    "field": "messages",
                    "value": {
                        "messages": [{
                            "id": "image_msg",
                            "from": "2222222222",
                            "type": "image",
                            "image": {"id": "image_id"}
                        }]
                    }
                }]
            }]
        }
        
        mock_user = AsyncMock()
        mock_user.on_message = AsyncMock()
        mock_user.session_guid = "test-session"
        mock_user.chat_history = {mock_user.session_guid: []}
        
        with patch('endpoints.whatsapp_endpoint.ZairaUserManager.get_user', return_value=mock_user):
            # Process text message
            await MyWhatsappBot.process_webhook(text_webhook)
            
            # Process image message (should handle gracefully with empty text)
            await MyWhatsappBot.process_webhook(image_webhook)
            
            # Text message should trigger user.on_message, image message should not (due to empty text)
            assert mock_user.on_message.call_count == 1