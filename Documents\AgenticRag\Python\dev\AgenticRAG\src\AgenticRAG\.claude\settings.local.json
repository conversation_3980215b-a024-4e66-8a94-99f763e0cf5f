{"permissions": {"allow": ["Bash(rm:*)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskManager::test_llm_call_structure -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskManager -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskManager::test_llm_call_error_handling -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskTools::test_create_scheduled_task_tool -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskTasks -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskPersistenceIntegration -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskManagerIntegration -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskTools::test_create_scheduled_task_tool_user_not_found -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_supervisor_comprehensive.py --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_persistence.py --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_rag_system.py --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_database_operations.py --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/ --tb=no -q --maxfail=5)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_scheduled_task_manager_integration.py::TestScheduledTaskManagerIntegration::test_create_task_workflow -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --cov=. --cov-report=term-missing)", "Bash(../../.venv/Scripts/pip list)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_task_chat_session.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_task_chat_session_simple.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --cov=. --cov-report=term-missing --cov-report=html)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_user.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_message_updated.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_message_updated.py -v --tb=short)", "Bash(../../.venv/Scripts/python.exe:*)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --collect-only -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ -x --tb=short -q)", "Bash(grep:*)", "<PERSON><PERSON>(sed:*)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py::TestDevRun::test_main_function_debug_mode -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --continue-on-collection-errors -x --tb=short -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py::TestMain::test_main_func_claude_environment -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py::TestMain::test_main_func_docker_environment -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py::TestMain::test_main_func_docker_no_dev_env -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py::TestMainEdgeCases::test_main_func_query_engine_in_production -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --collect-only)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --maxfail=5 -x --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py --cov=main --cov=dev_run --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_message.py --cov=userprofiles.ZairaMessage --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_message.py::TestZairaMessageValidation --cov=userprofiles.ZairaMessage --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_message.py::TestZairaMessage::test_to_langchain_unknown_role_fallback -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_message.py::TestZairaMessageValidation::test_enum_field_validators_with_non_string_input --cov=userprofiles.ZairaMessage --cov-report=term-missing)", "Bash(find:*)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py::TestMain::test_environment_variables_set -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_message.py --tb=line -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_main_dev_run.py --tb=line -q)", "Bash(../../.venv/Scripts/pytest.exe --cov=main --cov-report=term-missing tests/unit/test_main_dev_run.py -q)", "Bash(../../.venv/Scripts/pytest.exe --cov=dev_run --cov-report=term-missing tests/unit/test_main_dev_run.py -q)", "Bash(../../.venv/Scripts/pytest.exe --cov=globals --cov-report=term-missing tests/unit/ -q --tb=no)", "Bash(../../.venv/Scripts/pytest.exe --cov=globals --cov-report=term-missing tests/unit/test_main_dev_run.py -q)", "Bash(../../.venv/Scripts/pytest.exe --cov=main_loop --cov-report=term-missing tests/unit/test_main_dev_run.py -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_globals.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_globals.py::TestGlobals::test_is_docker_with_containerd_in_cgroup -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_globals.py::TestGlobals::test_is_docker_with_containerd_in_cgroup -v)", "Bash(../../.venv/Scripts/pytest.exe --cov=globals --cov-report=term-missing tests/unit/test_globals.py -q)", "Bash(rg:*)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_logfire_data_validation.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_recurring_bug.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_recurring_bug.py::TestRecurringTaskBugFix::test_monthly_schedule_tool_patterns -v)", "<PERSON><PERSON>(timeout:*)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task.py::test_schedule_parsing -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_output_sender_system_integration.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_output_sender_system_simple.py -v)", "<PERSON><PERSON>(python:*)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ -v -k scheduled)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_persistence.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_routing.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_scheduled_task_parsing_integration.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_authentication_oauth.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_imap_oauth_scheduled_task.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_imap_oauth_scheduled_task.py::TestIMAPOAuthScheduledTask::test_oauth_imap_setup -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_discord_reply_functionality.py -v)", "Bash(ls:*)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_multi_task_reply_routing.py::TestMultiTaskReplyRouting::test_task_management_methods -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_multi_task_reply_routing.py::TestMultiTaskReplyRouting::test_automatic_task_removal_on_completion -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_multi_task_reply_routing.py::TestMultiTaskReplyRouting::test_automatic_task_removal_logic -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ -k \"test_manager\" -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_users_comprehensive.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_update_user_success -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ -k \"manager\" -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_message_python -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_mybot_generic.py --collect-only)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_prompt_references.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_member_join_python_call -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_prompt_references.py::TestPromptReferences::test_generate_prompt_usage_report -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_python -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_mybot_generic.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_mybot_generic_pydantic_config -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_mybot_generic.py::TestMyBotGenericEdgeCases::test_creation_with_kwargs -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_prompt_references.py::TestPromptReferences::test_no_duplicate_prompt_references -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_logfire.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_logfire_data_validation.py::TestLogFireDataValidation::test_validation_exception_handling -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_output_sender_system_simple.py tests/unit/test_output_sender_system_integration.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_schedule_parsing_llm.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_verifier_create_input_meltano_validation.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_recurring_schedule_tool -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_daily_schedule_tool -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_monthly_schedule_tool -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_generic_schedule_tool -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_recurring_schedule_tool tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_daily_schedule_tool tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_monthly_schedule_tool tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_generic_schedule_tool tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_llm_parsing_agent_creation -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_schedule_parsing_llm.py -k \"not test_creation_with_llm_parsing and not test_llm_parsing_agent_creation and not test_extract_parsed_info_from_llm_result and not test_fallback_to_regex_parsing\" -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_user.py --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_user.py::TestZairaUser::test_zaira_user_creation -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_user.py::TestZairaUser::test_user_guid_property tests/unit/test_zaira_user.py::TestZairaUser::test_get_available_vector_stores_none_permission -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_user.py::TestZairaUser::test_get_chat_history_empty tests/unit/test_zaira_user.py::TestZairaUser::test_on_message_with_attachments -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_user.py::TestZairaUser::test_on_message_with_attachments -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_user.py::TestZairaUser::test_on_message_without_attachments tests/unit/test_zaira_user.py::TestZairaUser::test_on_message_with_existing_task -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_user.py::TestZairaUser::test_on_message_hitl_response -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_tell_me_about_your_data_call_trace.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_user.py::TestZairaUser::test_start_task_debug_mode -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_tell_me_about_your_data_call_trace.py::TestTellMeAboutYourDataCallTrace::test_tell_me_about_your_data_required_components -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_user.py::TestZairaUser::test_on_message_hitl_response tests/unit/test_zaira_user.py::TestZairaUser::test_start_task_debug_mode tests/unit/test_zaira_user.py::TestZairaUser::test_start_task_production_mode tests/unit/test_zaira_user.py::TestZairaUser::test_start_task_exception_handling -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_tell_me_about_your_data_call_trace.py -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_user.py::TestZairaUser::test_start_task_production_mode tests/unit/test_zaira_user.py::TestZairaUser::test_start_task_exception_handling -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_zaira_user.py::TestZairaUser::test_start_task_exception_handling -v)", "Bash(../../.venv/Scripts/pytest.exe tests/ --tb=no -q --disable-warnings)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_imap_email_send_receive.py::TestIMAPEmailSendReceive::test_imap_connection_only -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_imap_email_send_receive.py::TestIMAPEmailSendReceive::test_process_email_function_directly -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_imap_email_send_receive.py::TestIMAPEmailSendReceive::test_process_email_function_directly -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/ --collect-only --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_imap_email_send_receive.py::TestIMAPEmailSendReceive::test_imap_connection_only -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/health/test_system_health.py::TestSystemHealth::test_scheduled_task_system_health -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_imap_email_send_receive.py::TestIMAPEmailSendReceive::test_oauth_configuration_validation -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_comprehensive_user_workflow.py tests/integration/test_end_to_end_system_integration.py tests/integration/test_scheduled_task_manager_integration.py tests/integration/test_supervisor_task_coordination.py --collect-only --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_imap_email_send_receive.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_imap_email_send_receive.py::TestIMAPEmailSendReceive::test_smtp_connection_only -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task.py::test_time_calculations -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_recurring_bug.py -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_restart_persistence.py -v --tb=short -k \"test_monday_9am_task_persistence_on_time\" --no-header)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task.py::test_time_calculations -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_recurring_bug.py::TestRecurringTaskBugFix::test_schedule_task_creation_parsing_logic -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_restart_persistence.py::TestScheduledTaskRestartPersistence::test_monday_9am_task_persistence_on_time -v --no-header)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task.py::test_time_calculations -v --no-header --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_recurring_bug.py::TestRecurringTaskBugFix::test_schedule_task_creation_parsing_logic -v --no-header --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_recurring_bug.py tests/unit/test_scheduled_task_restart_persistence.py tests/unit/test_scheduled_task_user_login_persistence.py tests/unit/test_scheduled_task.py::test_time_calculations --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_restart_persistence.py::TestScheduledTaskRestartPersistence::test_30_minute_task_overdue_immediate_execution_and_reset -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_restart_persistence.py::TestScheduledTaskRestartPersistence::test_30_minute_task_overdue_immediate_execution_and_reset -v --tb=no --no-header)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_restart_persistence.py::TestScheduledTaskRestartPersistence::test_task_state_persistence_in_database -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_user_login_persistence.py::TestScheduledTaskUserLoginPersistence::test_user_logout_during_runtime_task_pausing -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_restart_persistence.py::TestScheduledTaskRestartPersistence::test_task_state_persistence_in_database -v --tb=short --no-header)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_restart_persistence.py::TestScheduledTaskRestartPersistence::test_task_state_persistence_in_database -v --tb=long --no-header)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_user_login_persistence.py::TestScheduledTaskUserLoginPersistence::test_user_logout_during_runtime_task_pausing -v --tb=long)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_user_login_persistence.py::TestScheduledTaskUserLoginPersistence::test_user_logout_during_runtime_task_pausing -v --tb=short --no-header)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_user_login_persistence.py::TestScheduledTaskUserLoginPersistence::test_user_logout_login_after_server_reboot -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_user_login_persistence.py::TestScheduledTaskUserLoginPersistence::test_user_logout_login_after_server_reboot -v --tb=short --no-header)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_user_login_persistence.py::TestScheduledTaskUserLoginPersistence::test_multiple_users_mixed_login_states -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_user_login_persistence.py::TestScheduledTaskUserLoginPersistence::test_multiple_users_mixed_login_states -v --tb=short --no-header)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_user_login_persistence.py::TestScheduledTaskUserLoginPersistence::test_task_overdue_scenarios_with_user_login_states -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_user_login_persistence.py::TestScheduledTaskUserLoginPersistence::test_task_overdue_scenarios_with_user_login_states -v --tb=short --no-header)", "<PERSON><PERSON>(chmod:*)", "<PERSON><PERSON>(docker.exe ps:*)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_simple_real_execution.py -v)", "Bash(../../.venv/Scripts/pytest.exe --collect-only tests/integration/)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_multi_task_reply_routing.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_supervisor_framework.py::TestSupervisorFramework::test_supervisor_task_state_creation -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_supervisor_framework.py -v)", "Bash(for file in \"tests/unit/test_scheduled_task_persistence.py\" \"tests/unit/test_scheduled_task_routing.py\" \"tests/unit/test_zaira_user.py\")", "Bash(do echo \"Processing $file...\")", "Bash(if [ -f \"$file\" ])", "Bash(then sed -i 's/task_id/scheduled_guid/g' \"$file\")", "<PERSON><PERSON>(echo:*)", "Bash(else echo \"File not found: $file\")", "Bash(fi)", "Bash(done)", "Bash(for file in \"tests/unit/test_scheduled_task_user_login_persistence.py\" \"tests/unit/test_scheduled_task_restart_persistence.py\" \"tests/unit/test_supervisor_framework.py\" \"tests/unit/test_database_operations.py\")", "Bash(for file in \"tests/integration/test_scheduled_task_manager_integration.py\" \"tests/integration/test_end_to_end_system_integration.py\" \"tests/integration/test_supervisor_task_coordination.py\" \"tests/integration/test_supervisor_workflows.py\")", "Bash(for file in \"tests/integration/test_tell_me_about_your_data_call_trace.py\" \"tests/integration/test_askzaira_txt_call_trace.py\" \"tests/integration/test_askzaira_website_call_trace.py\" \"tests/integration/test_email_writing_call_trace.py\")", "Bash(for file in \"tests/integration/test_identity_query_call_trace.py\" \"tests/integration/test_scheduled_task_restart_recovery.py\" \"tests/stress/test_comprehensive_system_stress.py\" \"tests/health/test_system_health.py\")", "Bash(for file in \"tests/performance/test_database_performance.py\" \"tests/performance/test_supervisor_performance.py\")", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_email_writing_call_trace.py --collect-only)", "Bash(export POSTGRES_HOST=************)", "Bash(export QDRANT_HOST=************)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_member_join_whatsapp -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_whatsapp -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_output_sender_system_integration.py::TestOutputSenderSystemIntegration::test_output_sender_includes_system_task -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_prompt_references.py::TestPromptReferences::test_prompt_reference_exactly_once -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_restart_persistence.py::TestScheduledTaskRestartPersistence::test_task_recreation_from_database_data -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_user_login_persistence.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_user_login_persistence.py::TestScheduledTaskUserLoginPersistence::test_user_login_during_runtime_task_resumption -vs)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_askzaira_txt_call_trace.py::TestAskZairaTxtCallTrace::test_askzaira_txt_call_trace_exact_match -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_askzaira_website_call_trace.py::TestAskZairaWebsiteCallTrace::test_askzaira_website_call_trace_exact_match -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_email_writing_call_trace.py::TestEmailWritingCallTrace::test_email_writing_call_trace_exact_match -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_identity_query_call_trace.py::TestIdentityQueryCallTrace::test_identity_query_call_trace_exact_match -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_supervisor_workflows.py::TestSupervisorWorkflows::test_top_level_supervisor_task_routing -v)", "Bash(../../.venv/Scripts/pytest.exe tests/ --tb=no --maxfail=20 -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --tb=no -v)", "WebFetch(domain:claude.ai)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --tb=no --maxfail=5)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_teams -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_without_chat_history -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_supervisor_framework.py::TestSupervisorFramework::test_supervisor_manager_registration -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_restart_persistence.py::TestScheduledTaskRestartPersistence::test_task_recreation_from_database_data -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_restart_persistence.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_member_join_teams -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_member_join_teams tests/unit/test_supervisor_framework.py::TestSupervisorFramework::test_supervisor_manager_registration -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_persistence.py::test_load_task_success -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_manager.py::TestScheduledTaskTools::test_cancel_scheduled_task_tool -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_multi_task_reply_routing.py::TestMultiTaskReplyRouting::test_message_to_task_mapping_removed -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_multi_task_reply_routing.py::TestMultiTaskReplyRouting::test_task_message_registration -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_multi_task_reply_routing.py::TestMultiTaskReplyRouting::test_hitl_task_detection tests/unit/test_multi_task_reply_routing.py::TestMultiTaskReplyRouting::test_automatic_task_removal_logic tests/unit/test_multi_task_reply_routing.py::TestMultiTaskReplyRouting::test_task_completion_cleanup -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_database_coordination.py::TestDatabaseCoordination::test_system_restart_recovery -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_database_coordination.py --collect-only)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_database_coordination.py::TestSystemRecovery::test_system_restart_recovery -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_authentication_oauth.py::TestOAuthTokenValidation::test_token_verification_invalid -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_multi_task_reply_routing.py::TestMultiTaskReplyRouting::test_reply_routing_to_hitl_task -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_supervisor_framework.py::TestSupervisorFramework::test_supervisor_manager_registration -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_task_agenda_planner.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --cov=managers --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --cov=managers --cov-report=term-missing --no-header)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ --cov=managers --cov-report=term-missing:skip-covered --no-cov-on-fail)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_prompts.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_prompts.py -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_prompts.py --cov=managers/manager_prompts --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_prompts.py --cov=managers.manager_prompts --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_qdrant.py --cov=managers.manager_qdrant --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_qdrant.py --cov=managers.manager_qdrant --cov-report=term-missing -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_users_comprehensive.py --cov=managers.manager_users --cov-report=term-missing -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_scheduled_task_persistence.py --cov=managers.scheduled_requests --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_scheduled_requests_comprehensive.py --cov=managers.scheduled_requests --cov-report=term-missing -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledTaskPersistenceManager::test_singleton_pattern -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_searxng_web_search_tool.py -v --cov=tasks.inputs.task_retrieval --cov-report=term-missing --cov-report=html -k \"TestSearXNGWebSearchTool and WebSearchTool\")", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_searxng_web_search_tool.py::TestSearXNGWebSearchTool::test_web_search_tool_none_content -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_scheduled_requests_comprehensive.py -v --cov=managers.scheduled_requests --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_searxng_web_search_tool.py::TestSearXNGWebSearchTool::test_web_search_tool_completely_missing_content -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_searxng_web_search_tool.py -v --cov=tasks.inputs.task_retrieval --cov-report=term-missing --cov-include=\"*/tasks/inputs/task_retrieval.py\" --cov-filter=WebSearchTool)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_searxng_web_search_tool.py -v --cov=tasks.inputs.task_retrieval --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe --cov=managers.manager_postgreSQL --cov=managers.manager_qdrant --cov-report=term-missing tests/unit/test_manager_postgreSQL.py tests/unit/test_manager_qdrant.py --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe --cov=managers.manager_postgreSQL --cov-report=term-missing tests/unit/test_manager_postgreSQL.py --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe --cov=managers.manager_postgreSQL --cov-report=term-missing tests/unit/test_manager_postgreSQL.py tests/unit/test_manager_postgreSQL_advanced.py -v)", "Bash(../../.venv/Scripts/pytest.exe --cov=managers.manager_postgreSQL --cov-report=term-missing tests/unit/test_manager_postgreSQL_advanced.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_postgreSQL_advanced.py::TestPostgreSQLManagerAdvanced::test_create_database_error_coverage -v -s)", "Bash(../../.venv/Scripts/pytest.exe --cov=managers.manager_postgreSQL --cov-report=term-missing tests/unit/test_manager_postgreSQL_targeted.py -v)", "Bash(../../.venv/Scripts/pytest.exe --cov=managers.manager_postgreSQL --cov-report=term-missing --cov-report=html tests/unit/test_manager_postgreSQL_targeted.py::TestPostgreSQLManagerTargeted::test_error_handling_paths tests/unit/test_manager_postgreSQL_advanced.py::TestPostgreSQLManagerAdvanced::test_create_database_error_coverage tests/unit/test_manager_postgreSQL_advanced.py::TestPostgreSQLManagerAdvanced::test_delete_database_error_coverage -v)", "Bash(../../.venv/Scripts/pytest.exe --cov=managers.manager_postgreSQL --cov-report=term-missing tests/unit/test_manager_postgreSQL*.py -v)", "Bash(../../.venv/Scripts/pytest.exe --cov=managers.manager_postgreSQL --cov-report=term-missing tests/unit/test_manager_postgreSQL_comprehensive.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_multimodal.py::TestMultimodalManager::test_extract_multimodal_elements_success -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_postgreSQL.py::TestPostgreSQLManager::test_new_initialization -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_postgreSQL.py::TestPostgreSQLManager::test_new_initialization -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_postgreSQL.py -x --tb=short -k \"not timeout\")", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_postgreSQL.py::TestPostgreSQLManager::test_host_configuration_docker -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_postgreSQL.py -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_qdrant.py -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_postgreSQL.py::TestPostgreSQLManager::test_host_configuration_env_override tests/unit/test_manager_postgreSQL.py::TestPostgreSQLManager::test_port_configuration_env_override -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ -k \"manager\" --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_agno.py::TestAgnoManager::test_init_vector_db_docker -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_agno.py -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_agno.py::TestAgnoManager::test_RunAgent_with_string_response -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_logfire.py::TestLogFireManager::test_log_internal_with_user -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_meltano.py -v --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_meltano.py::TestMeltanoManager::test_ConvertSQLToVectorStore_record_processing -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_users_comprehensive.py --tb=no -q)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_tool_basic_functionality -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_task_email_generator.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_task_out_email.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_task_imap.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_task_email_generator.py tests/unit/test_task_out_email.py tests/unit/test_task_imap.py tests/unit/test_task_agenda_planner.py -v)", "<PERSON><PERSON>(mkdir:*)", "<PERSON><PERSON>(mv:*)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/ -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/ --cov=tasks.processing.task_email_generator --cov=tasks.outputs.output_tasks.task_out_email --cov=tasks.inputs.task_imap --cov=tasks.processing.task_agenda_planner --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_agenda_planner.py --cov=tasks.processing.task_agenda_planner --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_agenda_planner_coverage.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_agenda_planner_coverage_fixed.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_agenda_planner_coverage_fixed.py::TestAgendaPlannerTaskCoverageFIXED::test_direct_calendar_create_tool_exception -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_agenda_planner.py tests/unit/email_agenda/test_task_agenda_planner_coverage_fixed.py --cov=tasks.processing.task_agenda_planner --cov-report=term-missing)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/ -v --tb=no)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/ --cov=tasks.processing.task_email_generator --cov=tasks.outputs.output_tasks.task_out_email --cov=tasks.inputs.task_imap --cov=tasks.processing.task_agenda_planner --cov-report=term-missing --tb=no)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_out_agenda.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_agenda_planner_refactored.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_agenda_planner_refactored.py --tb=short -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_out_agenda.py tests/unit/email_agenda/test_task_agenda_planner_refactored.py --tb=short)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/email_agenda/test_agenda_calendar_integration.py --tb=short -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/email_agenda/ -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/email_agenda/test_agenda_split_integration.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_agenda_planner_refactored.py tests/unit/email_agenda/test_task_out_agenda.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_supervisor_task_ordering.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_supervisor_task_ordering.py::TestSupervisorSupervisorTaskOrdering::test_supervisor_with_all_task_types -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_supervisor_task_ordering_simple.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_supervisor_execution_flow.py -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_supervisor_execution_flow.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_supervisor_task_ordering_simple.py tests/integration/test_supervisor_execution_flow.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_supervisor_execution_flow.py::TestSupervisorExecutionFlow::test_cot_supervisor_execution_flow -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_supervisor_execution_flow.py::TestSupervisorExecutionFlow::test_cot_supervisor_four_task_execution_order -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_email_output_routing.py::TestEmailOutputRouting::test_email_generator_adds_output_demands -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_email_output_routing_simple.py::TestEmailOutputRoutingSimple::test_email_generator_adds_output_demands_simple -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_email_output_demands.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_email_routing_fix_verification.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_email_output_routing_simple.py -v)", "Bash(../../.venv/Scripts/pytest.exe --cov=endpoints --cov-report=term-missing --cov-report=html tests/unit/ -v)", "Bash(../../.venv/Scripts/pytest.exe --cov=endpoints --cov-report=term-missing tests/unit/test_mybot_generic.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_etc_helper_functions_comprehensive.py::TestIsClaudeEnvironment::test_claude_environment_with_claude_code -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_missing_user_email_with_callback -xvs)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_subject_generation_missing -xvs)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_user_cancellation tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_subject_generation_missing tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_state_sections_missing -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_subject_generation_missing tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_state_sections_missing -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_subject_generation_missing -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_email_generator.py -v --tb=no)", "Bash(../../.venv/Scripts/pytest.exe tests/integration/test_scheduled_task_workflow.py::TestScheduledTaskWorkflow::test_scheduled_task_creation_workflow -xvs)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_setup -xvs)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_api_endpoint.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_start_app -xvs)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_api_endpoint.py::TestAPIEndpointMiddleware::test_ip_check_middleware tests/unit/test_api_endpoint.py::TestAPIEndpointMiddleware::test_ip_check_middleware_no_cf_header -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_supervisor_comprehensive.py -v --tb=no)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_supervisor_comprehensive.py::TestSupervisorManagerComprehensive::test_supervisor_manager_initialization -xvs)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_supervisor_comprehensive.py --collect-only)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_basic_router_functionality -xvs)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_supervisor_comprehensive.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/ -k \"database\" -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_create_database_success -v -s)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_late_setup -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_late_setup tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_ask_endpoint tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_ask_url_endpoint tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_ask_delayed_endpoint tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_ask_delayed_missing_params -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_ask_delayed_endpoint -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_authentication_oauth.py::TestOAuthUnified::test_environment_token_scenarios -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_config_comprehensive.py::TestConfigModule::test_config_environment_variables_loaded tests/unit/test_config_comprehensive.py::TestConfigModule::test_config_openai_api_key -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_config_comprehensive.py::TestConfigModule::test_config_environment_variables_loaded -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_config_comprehensive.py::TestConfigModule::test_config_required_vs_optional -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_config_comprehensive.py::TestConfigModule::test_config_postgres_settings tests/unit/test_config_comprehensive.py::TestConfigModule::test_config_qdrant_settings tests/unit/test_config_comprehensive.py::TestConfigModule::test_config_required_vs_optional tests/unit/test_config_comprehensive.py::TestConfigModule::test_config_security_sensitive_vars -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_config_comprehensive.py::TestConfigIntegration::test_config_with_external_api_integration -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_config_comprehensive.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_config_comprehensive.py::TestConfigIntegration::test_config_with_database_connection tests/unit/test_config_comprehensive.py::TestConfigIntegration::test_config_with_vector_database_connection -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_imports_comprehensive.py::TestImportsStarImportPattern::test_imports_available_modules -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_imports_comprehensive.py::TestImportsStarImportPattern::test_imports_type_safety -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_whatsapp_endpoint.py -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_send_whatsapp_message_success -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_send_whatsapp_message_empty -v)", "Bash(../../.venv/Scripts/pytest.exe tests/unit/test_etc_setup.py::TestSetup::test_init_new_project tests/unit/test_etc_setup.py::TestSetup::test_init_existing_project -v)"], "deny": []}}