{"python.defaultInterpreterPath": "../../.venv/Scripts/python.exe", "python.testing.pytestEnabled": true, "python.testing.unittestEnabled": false, "python.testing.nosetestsEnabled": false, "python.testing.pytestArgs": ["tests/", "-v"], "python.testing.cwd": "${workspaceFolder}", "python.testing.pytestPath": "../../.venv/Scripts/pytest.exe", "python.testing.autoTestDiscoverOnSavePattern": "tests/test_real/**/*.py", "files.associations": {"*.bat": "batch"}, "batch-runner.enableRightClickContextMenu": true, "batch-runner.showTerminal": true, "batch-runner.clearTerminal": false}