"""
Enterprise-grade chat session management with comprehensive metadata,
message handling, and session state management.
"""

from __future__ import annotations
from datetime import datetime, timezone
from typing import Dict, Any, List, Optional, Union, TYPE_CHECKING
from enum import Enum
from uuid import uuid4, UUID
from pydantic import (
    BaseModel, 
    Field, 
    ConfigDict, 
    field_validator, 
    computed_field,
    model_validator
)

from userprofiles.ZairaMessage import ZairaMessage, MessageRole

if TYPE_CHECKING:
    from userprofiles.ZairaUser import ZairaUser

# ============================================================================
# ENUMS AND CONSTANTS
# ============================================================================

class ChatSessionStatus(str, Enum):
    """Chat session status."""
    ACTIVE = "active"
    ARCHIVED = "archived"
    DELETED = "deleted"
    SUSPENDED = "suspended"

class ChatSessionType(str, Enum):
    """Type of chat session."""
    STANDARD = "standard"
    SUPPORT = "support"
    SCHEDULED = "scheduled"
    SYSTEM = "system"

# ============================================================================
# CORE ZAIRA CHAT MODEL
# ============================================================================

class ZairaChat(BaseModel):
    """
    Enterprise-grade chat session with comprehensive metadata and message management.
    
    Features:
    - Complete session metadata tracking
    - Message management with ordering and limits
    - Token counting and management
    - Session state and lifecycle management
    - Export/import capabilities
    - Search and filtering within session
    """
    
    # Pydantic configuration
    model_config = ConfigDict(
        arbitrary_types_allowed=True,
        validate_assignment=True,
        use_enum_values=True,
        extra='forbid',
        json_schema_serialization_defaults_required=True
    )
    
    # ========================================================================
    # CORE FIELDS
    # ========================================================================
    
    # Session identifiers
    session_guid: UUID = Field(
        default_factory=uuid4,
        description="Unique session identifier"
    )
    user_guid: str = Field(
        ..., 
        description="User GUID who owns this session"
    )
    conversation_guid: Optional[UUID] = Field(
        default=None,
        description="Conversation GUID for grouping related sessions"
    )
    
    # Session metadata
    title: Optional[str] = Field(
        default=None,
        max_length=200,
        description="Session title (auto-generated or user-defined)"
    )
    summary: Optional[str] = Field(
        default=None,
        max_length=500,
        description="Session summary"
    )
    
    # Timestamps
    created_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Session creation timestamp (UTC)"
    )
    updated_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Last update timestamp (UTC)"
    )
    last_activity_at: datetime = Field(
        default_factory=lambda: datetime.now(timezone.utc),
        description="Last activity timestamp (UTC)"
    )
    
    # Session state
    status: ChatSessionStatus = Field(
        default=ChatSessionStatus.ACTIVE,
        description="Session status"
    )
    session_type: ChatSessionType = Field(
        default=ChatSessionType.STANDARD,
        description="Type of session"
    )
    
    # Messages
    messages: List[ZairaMessage] = Field(
        default_factory=list,
        description="List of messages in this session"
    )
    
    # Limits and counters
    max_messages: Optional[int] = Field(
        default=None,
        ge=1,
        description="Maximum messages allowed in session"
    )
    total_tokens_used: int = Field(
        default=0,
        ge=0,
        description="Total tokens used in this session"
    )
    max_tokens: Optional[int] = Field(
        default=None,
        ge=1,
        description="Maximum tokens allowed in session"
    )
    
    # Session settings
    auto_archive_after_days: Optional[int] = Field(
        default=30,
        ge=1,
        description="Auto-archive session after N days of inactivity"
    )
    
    # Business metadata
    tags: List[str] = Field(
        default_factory=list,
        description="Tags for categorization"
    )
    metadata: Dict[str, Any] = Field(
        default_factory=dict,
        description="Custom metadata dictionary"
    )
    
    # Context and state
    session_context: Dict[str, Any] = Field(
        default_factory=dict,
        description="Session-specific context and state"
    )
    
    # User reference for logging and message routing
    user: Optional["ZairaUser"] = Field(
        default=None,
        exclude=True,
        description="Reference to the ZairaUser that owns this chat session"
    )
    
    # ========================================================================
    # COMPUTED FIELDS
    # ========================================================================
    
    @computed_field
    @property
    def message_count(self) -> int:
        """Get the number of messages in the session."""
        return len(self.messages)
    
    @computed_field
    @property
    def is_empty(self) -> bool:
        """Check if session has no messages."""
        return len(self.messages) == 0
    
    @computed_field
    @property
    def duration_seconds(self) -> float:
        """Calculate session duration in seconds."""
        if not self.messages:
            return 0.0
        first_msg_time = self.messages[0].timestamp
        last_msg_time = self.messages[-1].timestamp
        return (last_msg_time - first_msg_time).total_seconds()
    
    @computed_field
    @property
    def is_active(self) -> bool:
        """Check if session is active."""
        return self.status == ChatSessionStatus.ACTIVE
    
    @computed_field
    @property
    def days_since_activity(self) -> int:
        """Days since last activity."""
        delta = (datetime.now(timezone.utc) - self.last_activity_at).days
        return max(0, delta)  # Ensure non-negative
    
    # ========================================================================
    # VALIDATORS
    # ========================================================================
    
    @field_validator('status', mode='before')
    @classmethod
    def validate_status(cls, v):
        """Validate and convert status to enum."""
        if isinstance(v, str):
            return ChatSessionStatus(v)
        return v
    
    @field_validator('session_type', mode='before')
    @classmethod
    def validate_session_type(cls, v):
        """Validate and convert session type to enum."""
        if isinstance(v, str):
            return ChatSessionType(v)
        return v
    
    @field_validator('tags')
    @classmethod
    def validate_tags(cls, v):
        """Ensure tags are unique and cleaned."""
        if v:
            cleaned = [tag.strip() for tag in v if tag.strip()]
            return list(dict.fromkeys(cleaned))
        return v
    
    @model_validator(mode='after')
    def validate_token_limits(self):
        """Validate token usage against limits."""
        if self.max_tokens and self.total_tokens_used > self.max_tokens:
            raise ValueError(f"Token usage ({self.total_tokens_used}) exceeds limit ({self.max_tokens})")
        return self
    
    @model_validator(mode='after')
    def update_timestamps(self):
        """Update timestamps based on activity."""
        if self.messages:
            # Update last activity based on most recent message
            latest_message_time = max(msg.timestamp for msg in self.messages)
            if latest_message_time > self.last_activity_at:
                self.last_activity_at = latest_message_time
        return self
    
    # ========================================================================
    # MESSAGE MANAGEMENT
    # ========================================================================
    
    def add_message(self, message: ZairaMessage) -> bool:
        """
        Add a message to the session.
        
        Args:
            message: ZairaMessage to add
            
        Returns:
            bool: True if added successfully, False if limits exceeded
        """
        if not message:
            return None
        # Check message limit
        if self.max_messages and len(self.messages) >= self.max_messages:
            return False
        
        # Check token limit
        if self.max_tokens and message.tokens_used:
            if self.total_tokens_used + message.tokens_used > self.max_tokens:
                return False
        
        # Update session GUID in message
        message.session_id = str(self.session_guid)
        message.conversation_id = str(self.conversation_guid) if self.conversation_guid else None
        
        # Add message
        self.messages.append(message)
        
        # Update counters and timestamps
        self.updated_at = datetime.now(timezone.utc)
        self.last_activity_at = datetime.now(timezone.utc)
        
        if message.tokens_used:
            self.total_tokens_used += message.tokens_used
        
        # Auto-generate title if needed (trigger with first user message)
        if not self.title and message.is_from_user():
            self._auto_generate_title()
        
        return True
    
    def get_messages(
        self, 
        limit: Optional[int] = None,
        role: Optional[MessageRole] = None,
        start_time: Optional[datetime] = None,
        end_time: Optional[datetime] = None
    ) -> List[ZairaMessage]:
        """
        Get messages with optional filtering.
        
        Args:
            limit: Maximum number of messages to return
            role: Filter by message role
            start_time: Filter messages after this time
            end_time: Filter messages before this time
            
        Returns:
            List of filtered messages
        """
        messages = self.messages
        
        # Filter by role
        if role:
            messages = [msg for msg in messages if msg.role == role]
        
        # Filter by time range
        if start_time:
            messages = [msg for msg in messages if msg.timestamp >= start_time]
        if end_time:
            messages = [msg for msg in messages if msg.timestamp <= end_time]
        
        # Apply limit
        if limit:
            messages = messages[-limit:]
        
        return messages
    
    def get_last_message(self) -> Optional[ZairaMessage]:
        """Get the most recent message."""
        return self.messages[-1] if self.messages else None
    
    def get_last_user_message(self) -> Optional[ZairaMessage]:
        """Get the most recent user message."""
        for message in reversed(self.messages):
            if message.is_from_user():
                return message
        return None
    
    def get_last_assistant_message(self) -> Optional[ZairaMessage]:
        """Get the most recent assistant message."""
        for message in reversed(self.messages):
            if message.is_from_assistant():
                return message
        return None
    
    def find_message_by_content(self, content_fragment: str) -> Optional[ZairaMessage]:
        """Find a message containing the specified content fragment."""
        if not content_fragment:
            return None
        for message in reversed(self.messages):  # Search newest first
            if content_fragment in message.content:
                return message
        return None
    
    def get_messages_with_call_trace(self) -> List[ZairaMessage]:
        """Get all assistant messages that have call_trace information."""
        return [msg for msg in self.messages if msg.is_from_assistant() and msg.call_trace]
    
    def get_call_trace_summary(self) -> Dict[str, int]:
        """Get a summary of call trace steps across all assistant messages."""
        call_trace_counts = {}
        for message in self.messages:
            if message.is_from_assistant() and message.call_trace:
                for step in message.call_trace:
                    call_trace_counts[step] = call_trace_counts.get(step, 0) + 1
        return call_trace_counts
    
    def clear_messages(self) -> None:
        """Clear all messages from the session."""
        self.messages.clear()
        self.total_tokens_used = 0
        self.updated_at = datetime.now(timezone.utc)
    
    def remove_message(self, message_id: str) -> bool:
        """
        Remove a message by ID.
        
        Args:
            message_id: ID of message to remove
            
        Returns:
            bool: True if removed, False if not found
        """
        for i, msg in enumerate(self.messages):
            if msg.message_id == message_id:
                removed_msg = self.messages.pop(i)
                if removed_msg.tokens_used:
                    self.total_tokens_used -= removed_msg.tokens_used
                self.updated_at = datetime.now(timezone.utc)
                return True
        return False
    
    # ========================================================================
    # SESSION MANAGEMENT
    # ========================================================================
    
    def archive(self) -> None:
        """Archive the session."""
        self.status = ChatSessionStatus.ARCHIVED
        self.updated_at = datetime.now(timezone.utc)
    
    def activate(self) -> None:
        """Activate an archived session."""
        if self.status == ChatSessionStatus.ARCHIVED:
            self.status = ChatSessionStatus.ACTIVE
            self.updated_at = datetime.now(timezone.utc)
            self.last_activity_at = datetime.now(timezone.utc)
    
    def delete(self) -> None:
        """Mark session as deleted (soft delete)."""
        self.status = ChatSessionStatus.DELETED
        self.updated_at = datetime.now(timezone.utc)
    
    def suspend(self) -> None:
        """Suspend the session."""
        self.status = ChatSessionStatus.SUSPENDED
        self.updated_at = datetime.now(timezone.utc)
    
    def should_auto_archive(self) -> bool:
        """Check if session should be auto-archived."""
        if (self.status == ChatSessionStatus.ACTIVE and 
            self.auto_archive_after_days and 
            self.days_since_activity >= self.auto_archive_after_days):
            return True
        return False
    
    # ========================================================================
    # UTILITY METHODS
    # ========================================================================
    
    def _auto_generate_title(self) -> None:
        """Auto-generate title from first user message."""
        if not self.title:
            # Get first user message content
            first_user_msg = next((msg for msg in self.messages if msg.is_from_user()), None)
            if first_user_msg:
                # Take first 50 chars of content as title
                content = first_user_msg.content.strip()
                self.title = content[:50] + "..." if len(content) > 50 else content
    
    def update_metadata(self, **metadata_updates) -> None:
        """Update session metadata."""
        self.metadata.update(metadata_updates)
        self.updated_at = datetime.now(timezone.utc)
    
    def update_context(self, **context_updates) -> None:
        """Update session context."""
        self.session_context.update(context_updates)
        self.updated_at = datetime.now(timezone.utc)
    
    def add_tag(self, tag: str) -> None:
        """Add a tag to the session."""
        if tag.strip() and tag.strip() not in self.tags:
            self.tags.append(tag.strip())
            self.updated_at = datetime.now(timezone.utc)
    
    def remove_tag(self, tag: str) -> bool:
        """Remove a tag from the session."""
        if tag in self.tags:
            self.tags.remove(tag)
            self.updated_at = datetime.now(timezone.utc)
            return True
        return False
    
    # ========================================================================
    # USER INTEGRATION METHODS
    # ========================================================================
    
    def get_user(self) -> Optional["ZairaUser"]:
        """Get the ZairaUser that owns this chat session."""
        return self.user
    
    def set_user(self, user: "ZairaUser") -> None:
        """Set the ZairaUser that owns this chat session."""
        self.user = user
    
    def receive_debug_message(self, content: str, call_trace: Optional[List[str]] = None):
        """
        Add a debug message to this chat session.
        Routes through the user's receive_debug_message method with this session's GUID.
        """
        if self.user:
            self.user.receive_debug_message(content, self.session_guid, call_trace)
    
    def receive_logging_message(self, content: str, call_trace: Optional[List[str]] = None):
        """
        Add a debug message to this chat session.
        Routes through the user's receive_debug_message method with this session's GUID.
        """
        if self.user:
            self.user.receive_logging_message(content, self.session_guid, call_trace)
    
    # ========================================================================
    # EXPORT/CONVERSION METHODS
    # ========================================================================
    
    def to_dict(
        self,
        include_messages: bool = True,
        include_metadata: bool = True,
        exclude_none: bool = True
    ) -> Dict[str, Any]:
        """
        Convert to dictionary with flexible options.
        
        Args:
            include_messages: Include message list
            include_metadata: Include metadata and context
            exclude_none: Exclude None values
        """
        data = self.model_dump(exclude_none=exclude_none)
        
        if not include_messages:
            data.pop('messages', None)
        
        if not include_metadata:
            data.pop('metadata', None)
            data.pop('session_context', None)
            data.pop('tags', None)
        
        return data
    
    def to_langchain_messages(self) -> List[Any]:
        """Convert all messages to LangChain format."""
        return [msg.to_langchain() for msg in self.messages]
    
    def to_openai_messages(self) -> List[Dict[str, Any]]:
        """Convert all messages to OpenAI format."""
        return [msg.to_openai_format() for msg in self.messages]
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """Get summary statistics for the session."""
        user_messages = [msg for msg in self.messages if msg.is_from_user()]
        assistant_messages = [msg for msg in self.messages if msg.is_from_assistant()]
        messages_with_trace = self.get_messages_with_call_trace()
        
        return {
            "session_guid": str(self.session_guid),
            "title": self.title,
            "status": self.status,
            "message_count": self.message_count,
            "user_message_count": len(user_messages),
            "assistant_message_count": len(assistant_messages),
            "messages_with_call_trace_count": len(messages_with_trace),
            "total_tokens_used": self.total_tokens_used,
            "duration_seconds": self.duration_seconds,
            "days_since_activity": self.days_since_activity,
            "created_at": self.created_at.isoformat(),
            "last_activity_at": self.last_activity_at.isoformat(),
            "call_trace_summary": self.get_call_trace_summary()
        }
    
    # ========================================================================
    # FACTORY METHODS
    # ========================================================================
    
    @classmethod
    def create_from_messages(
        cls,
        user_guid: str,
        messages: List[ZairaMessage],
        title: Optional[str] = None,
        session_type: ChatSessionType = ChatSessionType.STANDARD,
        **kwargs
    ) -> ZairaChat:
        """
        Create a chat session from existing messages.
        
        Args:
            user_guid: User GUID
            messages: List of ZairaMessage objects
            title: Optional session title
            session_type: Type of session
            **kwargs: Additional fields
        """
        # Calculate total tokens
        total_tokens = sum(msg.tokens_used or 0 for msg in messages)
        
        # Get timestamps from messages
        if messages:
            created_at = messages[0].timestamp
            last_activity = messages[-1].timestamp
        else:
            created_at = datetime.now(timezone.utc)
            last_activity = created_at
        
        return cls(
            user_guid=user_guid,
            messages=messages,
            title=title,
            session_type=session_type,
            total_tokens_used=total_tokens,
            created_at=created_at,
            last_activity_at=last_activity,
            **kwargs
        )
    
    @classmethod
    def migrate_from_message_list(
        cls,
        user_guid: str,
        session_guid: Union[str, UUID],
        messages: List[Union[ZairaMessage, Dict[str, Any]]],
        **kwargs
    ) -> ZairaChat:
        """
        Migrate from old format (list of messages) to ZairaChat.
        
        Args:
            user_guid: User GUID
            session_guid: Session UUID
            messages: List of messages (ZairaMessage or dict)
            **kwargs: Additional fields
        """
        # Convert dicts to ZairaMessage if needed
        zaira_messages = []
        for msg in messages:
            if isinstance(msg, dict):
                # Handle old format dictionaries
                if 'role' in msg and 'content' in msg:
                    zaira_messages.append(ZairaMessage(
                        role=msg['role'],
                        content=msg['content'],
                        session_id=str(session_guid),
                        timestamp=msg.get('timestamp', datetime.now(timezone.utc))
                    ))
            elif isinstance(msg, ZairaMessage):
                zaira_messages.append(msg)
        
        # Create session
        return cls(
            session_guid=UUID(session_guid) if isinstance(session_guid, str) else session_guid,
            user_guid=user_guid,
            messages=zaira_messages,
            **kwargs
        )
    
    # ========================================================================
    # STRING REPRESENTATIONS
    # ========================================================================
    
    def __str__(self) -> str:
        """String representation."""
        title_str = f"'{self.title[:30]}...'" if self.title and len(self.title) > 30 else f"'{self.title}'" if self.title else "Untitled"
        status_str = self.status if isinstance(self.status, str) else self.status.value
        return f"ZairaChat(guid={str(self.session_guid)[:8]}, title={title_str}, messages={self.message_count}, status={status_str})"
    
    def __repr__(self) -> str:
        """Detailed representation."""
        return (f"ZairaChat(session_guid='{self.session_guid}', user_guid='{self.user_guid}', "
                f"title='{self.title}', message_count={self.message_count}, "
                f"status='{self.status}', created_at='{self.created_at.isoformat()}')")
