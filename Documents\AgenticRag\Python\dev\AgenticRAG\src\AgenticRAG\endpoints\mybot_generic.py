from imports import *

from asyncio import create_task, sleep, Task
from pydantic import BaseModel, Field
from typing import  Any, Optional
from pydantic import ConfigDict

from userprofiles.ZairaMessage import ZairaMessage
from userprofiles.ZairaUser import ZairaUser
from endpoints.oauth._verifier_ import OAuth2Verifier

class ChannelType:
    PRIVATE = "private"
    PUBLIC = "public"

class ReplyContext(BaseModel):
    """
    Context information for message replies across communication platforms.
    
    This class provides a standardized way to handle reply relationships
    for messages from Discord, Teams, WhatsApp, and other platforms.
    """
    
    is_reply: bool = Field(default=False, description="Whether this message is a reply to another message")
    replied_message_id: Optional[str] = Field(default=None, description="ID of the message being replied to")
    replied_message_content: Optional[str] = Field(default=None, description="Content of the message being replied to")
    replied_message_author: Optional[str] = Field(default=None, description="Author of the message being replied to")
    platform: Optional[str] = Field(default=None, description="Platform where the reply originated (Discord, Teams, etc.)")
    
    model_config = ConfigDict(arbitrary_types_allowed=True)
    
    @classmethod
    def create_no_reply(cls) -> "ReplyContext":
        """Create a ReplyContext for messages that are not replies"""
        return cls(is_reply=False)
    
    @classmethod
    def create_reply(cls, 
                    replied_message_id: str,
                    replied_message_content: Optional[str] = None,
                    replied_message_author: Optional[str] = None,
                    platform: Optional[str] = None) -> "ReplyContext":
        """Create a ReplyContext for reply messages"""
        return cls(
            is_reply=True,
            replied_message_id=replied_message_id,
            replied_message_content=replied_message_content,
            replied_message_author=replied_message_author,
            platform=platform
        )
    
    def format_reply_context_for_ai(self) -> str:
        """Format reply context for AI processing"""
        if not self.is_reply:
            return ""
        
        if self.replied_message_content and self.replied_message_author:
            return f"\n\n[Reply Context: This message is replying to {self.replied_message_author}: '{self.replied_message_content}']"
        elif self.replied_message_id:
            return f"\n\n[Reply Context: This message is replying to message ID {self.replied_message_id}]"
        else:
            return "\n\n[Reply Context: This message is a reply to a previous message]"

class MyBot_Generic(BaseModel):
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
    parent_instance: Optional[Any] = None
    name: str = ""
    asyncio_Task: Optional[Task] = None # Python-only

    model_config = ConfigDict(arbitrary_types_allowed=True, extra='allow')

    def __init__(self, parent_instance, name, **kwargs):
        super().__init__(parent_instance=parent_instance,name=name,asyncio_Task=None,**kwargs)

    async def on_ready(self):
        # Skip greeting message during tests
        import sys
        if 'pytest' in sys.modules:
            return
        await self.send_broadcast("Gegroet collega's! Hoe mag ik u vandaag gehoorzamen? Om zeker te zijn dat ik weet dat je het tegen mij hebt hoef je enkel en alleen je berichten met een '!' te beginnen.")

    async def on_message(self, channel_type: ChannelType, user: ZairaUser, text: str, attachments: list, original_message, reply_context: Optional[ReplyContext] = None):
        # Get the user's current chat session for logging
        chat_session = user.get_current_chat_session()
        LogFire.log("EVENT", f'[{channel_type}] {user.username}: "{text}"', chat=chat_session)
        # Console monitoring already handled by LogFire.log above
        if reply_context and reply_context.is_reply:
            LogFire.log("DEBUG", f'[REPLY] Message is a reply to: {reply_context.replied_message_author or "Unknown"}', chat=chat_session)
        await user.on_message(complete_message=text,calling_bot=self,attachments=attachments,original_message=original_message,reply_context=reply_context)

    async def on_member_join(self, member_name: str, message):
        from endpoints.teams_endpoint import MyTeamsBot
        from endpoints.discord_endpoint import MyDiscordBot
        from endpoints.whatsapp_endpoint import MyWhatsappBot

        welcome_text = f"Hello and welcome {member_name}!"
        if self.parent_instance == None:
            # Python call
            LogFire.log("ERROR", "HACKING ATTEMPT! Should never occur!", severity="error", chat=None)
            # Security alert already handled by LogFire.log above
        elif isinstance(self.parent_instance, MyTeamsBot):
            from botbuilder.core import MessageFactory
            await message.send_activity(MessageFactory.text(welcome_text))
        elif isinstance(self.parent_instance, MyDiscordBot):
            await message.create_dm()
            await message.dm_channel.send(welcome_text)
        elif isinstance(self.parent_instance, MyWhatsappBot):
            # For WhatsApp, message would be the phone number
            await MyWhatsappBot.send_a_whatsapp_message(welcome_text, message)

    async def request_human_in_the_loop_internal(self, request:str, request_obj: "LongRunningZairaRequest", message, halt_until_response = False):
        if self.name == "Python":
            result = input(request)
            await request_obj.user.on_message(result, self, [], request_obj.original_physical_message)
        elif self.name == "Discord":
            # # If wait is requested
            # def check(m):
            #     # Check if the reply is from the same user in the same channel
            #     return m.author == message.author and m.channel == message.channel

            # async def handle_query():
            #     try:
            #         result = await MyDiscordBot.bot.wait_for('message', check=check, timeout=360)
            #         #await self.send_reply(f'You replied: "{reply.content}" within 6 minutes! Nice!', message)
            #         #callback(result) is handled inside start_task(), so simply ignore the result and return the function
            #     except TimeoutError:
            #         await self.send_reply(f'{message.author.mention}, time\’s up! You didn\’t reply within 6 minutes.', message)
            # MyDiscordBot.bot.loop.create_task(handle_query())

            await self.send_reply(request, request_obj, message)
        elif self.name == "Teams":
            await self.send_reply(request, request_obj, message)
        elif self.name == "Whatsapp":
            await self.send_reply(request, request_obj, message)
        if halt_until_response == True:
            while True:
                await sleep(1)
                if request_obj.human_in_the_loop_callback == None:
                    break
    
    @classmethod
    async def send_broadcast_ALL(cls, user: "ZairaUser", content: str):
        """Broadcast message to all connected communication channels"""
        try:
            # Get user's current chat session for logging
            chat_session = user.get_current_chat_session()
            
            # Get all available communication channels and broadcast to each one
            # Check for Discord configuration and send if available
            discord_token = await OAuth2Verifier.get_full_token("discord")
            if discord_token:
                try:
                    from endpoints.discord_endpoint import MyDiscordBot
                    discord_bot = MyDiscordBot.get_instance()
                    if discord_bot and discord_bot.bot_generic:
                        await discord_bot.bot_generic.send_broadcast(content)
                        LogFire.log("OUTPUT", f"Sent email notification to Discord for user {user.user_guid}", chat=chat_session)
                except Exception as e:
                    LogFire.log("ERROR", f"Failed to send Discord notification: {str(e)}", chat=chat_session)
            
            # Check for Teams configuration and send if available
            teams_token = await OAuth2Verifier.get_full_token("teams")
            if teams_token:
                try:
                    from endpoints.teams_endpoint import MyTeamsBot
                    teams_bot = MyTeamsBot.get_instance()
                    if teams_bot and teams_bot.bot_generic:
                        await teams_bot.bot_generic.send_broadcast(content)
                        LogFire.log("OUTPUT", f"Sent email notification to Teams for user {user.user_guid}", chat=chat_session)
                except Exception as e:
                    LogFire.log("ERROR", f"Failed to send Teams notification: {str(e)}", chat=chat_session)
            
            # Check for WhatsApp configuration and send if available
            whatsapp_token = await OAuth2Verifier.get_full_token("whatsapp")
            if whatsapp_token:
                try:
                    from endpoints.whatsapp_endpoint import MyWhatsappBot
                    whatsapp_bot = MyWhatsappBot.get_instance()
                    if whatsapp_bot and whatsapp_bot.bot_generic:
                        await whatsapp_bot.bot_generic.send_broadcast(content)
                        LogFire.log("OUTPUT", f"Sent email notification to WhatsApp for user {user.user_guid}", chat=chat_session)
                except Exception as e:
                    LogFire.log("ERROR", f"Failed to send WhatsApp notification: {str(e)}", chat=chat_session)
            
            # Check for Slack configuration and send if available
            slack_token = await OAuth2Verifier.get_full_token("slack")
            if slack_token:
                try:
                    from endpoints.slack_endpoint import MySlackBot
                    slack_bot = MySlackBot.get_instance()
                    if slack_bot and slack_bot.bot_generic:
                        await slack_bot.bot_generic.send_broadcast(content)
                        LogFire.log("IMAP_BROADCAST", f"Sent email notification to Slack for user {user.user_guid}", chat=chat_session)
                except Exception as e:
                    LogFire.log("ERROR", f"Failed to send Slack notification: {str(e)}", chat=chat_session)
            
            # Check for Testing bot - for automated testing scenarios
            # Look for any Testing bot instances that may be active
            try:
                # Check if we have a Testing bot available (used during testing)
                from endpoints.testing_endpoint import MyBot_Testing
                # Create a temporary testing bot to handle the broadcast
                testing_bot = MyBot_Testing(None, "Testing")
                await testing_bot.send_broadcast(content)
                LogFire.log("OUTPUT", f"Sent email notification to Testing bot for user {user.user_guid}", chat=chat_session)
            except ImportError:
                # Testing endpoint not available - skip
                pass
            except Exception as e:
                LogFire.log("ERROR", f"Failed to send Testing bot notification: {str(e)}", chat=chat_session)
            
            LogFire.log("TASK", f"Email notification broadcast completed for user {user.user_guid}", chat=chat_session)
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to broadcast email notification: {str(e)}", chat=chat_session)
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "_broadcast_email_notification", user.user_guid)
    
    async def send_broadcast(self, text):
        if self.name == "Python":
            LogFire.log("OUTPUT", f"Python broadcast: {text[:200]}...", chat=None)
            # Python broadcast output already handled by LogFire.log above
        elif self.name == "Discord":
            from endpoints.discord_endpoint import MyDiscordBot
            await MyDiscordBot.send_discord_broadcast(text)
        elif self.name == "Teams":
            from endpoints.teams_endpoint import MyTeamsBot
            await MyTeamsBot.send_teams_broadcast(text)
        elif self.name == "Testing":
            # Handle Testing bot broadcasts
            # Log the broadcast for test verification
            from managers.manager_logfire import LogFire
            LogFire.log("OUTPUT", f"Testing bot broadcast: {text[:100]}...", chat=None)
            # For testing, we can also print to console
            LogFire.log("OUTPUT", f"Testing bot broadcast: {text[:200]}...", chat=None)
            # Testing bot broadcast already handled by LogFire.log above

    async def send_reply(self, text: str, request: "LongRunningZairaRequest", physical_message, add_to_chat_history = True):
        async def split_and_process_text(text, callback, max_length=2000, min_last_chunk_length=500):
            from re import split as re_split

            # Split only on dots followed by a space or end of line, keeping the dot
            sentences = re_split(r'(?<=\.)\s+', text)

            chunks = []
            current_chunk = ""

            for sentence in sentences:
                if len(current_chunk) + len(sentence) + 1 <= max_length:
                    current_chunk += sentence + " "
                else:
                    chunks.append(current_chunk.strip())
                    current_chunk = sentence + " "

            # Add the last chunk
            if current_chunk.strip():
                chunks.append(current_chunk.strip())

            # Rebalance the last two chunks if the last one is too small
            if len(chunks) >= 2 and len(chunks[-1]) < min_last_chunk_length:
                last = chunks.pop()
                prev = chunks.pop()

                merged = prev + ' ' + last
                if len(merged) <= max_length:
                    chunks.append(merged.strip())
                else:
                    # Re-split the merged chunk safely within max_length
                    split_point = merged.rfind(' ', 0, max_length)
                    if split_point == -1:
                        # If no space found, force split at max_length
                        chunks.append(merged[:max_length].strip())
                        chunks.append(merged[max_length:].strip())
                    else:
                        chunks.append(merged[:split_point].strip())
                        chunks.append(merged[split_point:].strip())

            # Ensure no chunk exceeds max_length
            final_chunks = []
            for chunk in chunks:
                while len(chunk) > max_length:
                    split_point = chunk.rfind(' ', 0, max_length)
                    if split_point == -1:
                        # No good split point, force cut
                        final_chunks.append(chunk[:max_length].strip())
                        chunk = chunk[max_length:].strip()
                    else:
                        final_chunks.append(chunk[:split_point].strip())
                        chunk = chunk[split_point:].strip()
                if chunk:
                    final_chunks.append(chunk)

            # Process chunks with callback
            for chunk in final_chunks:
                await callback(chunk)
        if self.name == "Python":
            async def wait_for_no_active_requests(self: "MyBot_Generic", text):
                while True:
                   # Wait with outputting to the Python log until there's nothing being logged anymore
                   await sleep(1)
                   if not request.user.has_active_requests():
                       break
                # Get the user's chat session for logging
                chat_session = request.user.chat_history.get(request.chat_session_guid)
                LogFire.log("OUTPUT", f"Python execution output: {text[:200]}...", chat=chat_session)
                # Python execution output already handled by LogFire.log above
                # Formatting handled by LogFire.log structure
            self.asyncio_Task = create_task(wait_for_no_active_requests(self, text))
            self.asyncio_Task.add_done_callback(etc.helper_functions.handle_asyncio_task_result_errors)
        elif self.name == "Teams":
            from endpoints.teams_endpoint import MyTeamsBot
            async def teams_reply_callback(chunk):
                return await MyTeamsBot.send_a_teams_message(physical_message, chunk)
            await split_and_process_text(text, callback=teams_reply_callback, max_length=2000, min_last_chunk_length=500)
        elif self.name == "Discord":
            # Check if the text contains image attachment paths
            import re
            import os
            from discord import File
            
            # Extract image paths from the text
            image_path_pattern = r'IMAGE_ATTACHMENT_PATH: (.+?)(?:\n|$)'
            image_paths = re.findall(image_path_pattern, text)
            
            # Remove the IMAGE_ATTACHMENT_PATH markers from the text
            clean_text = re.sub(image_path_pattern, '', text)
            
            # Send text with image attachments if found
            if image_paths:
                # Prepare Discord file attachments
                discord_files = []
                for path in image_paths:
                    path = path.strip()
                    if os.path.exists(path):
                        try:
                            # Get just the filename for display
                            filename = os.path.basename(path)
                            discord_file = File(path, filename=filename)
                            discord_files.append(discord_file)
                            # Get the user's chat session for logging
                            chat_session = request.user.chat_history.get(request.chat_session_guid)
                            LogFire.log("DEBUG", f"Prepared Discord attachment: {filename}", chat=chat_session)
                        except Exception as e:
                            # Get the user's chat session for logging
                            chat_session = request.user.chat_history.get(request.chat_session_guid)
                            LogFire.log("ERROR", f"Error preparing Discord attachment {path}: {e}", severity="warning", chat=chat_session)
                
                # If we have valid files, send them with the first chunk
                if discord_files:
                    # Split the text and send first chunk with images
                    chunks = []
                    
                    async def collect_chunks(chunk):
                        chunks.append(chunk)
                    
                    await split_and_process_text(clean_text, callback=collect_chunks, max_length=2000, min_last_chunk_length=500)
                    
                    # Send first chunk with all image attachments
                    if chunks:
                        first_chunk = chunks[0]
                        try:
                            await physical_message.reply(content=first_chunk, files=discord_files)
                            # Get the user's chat session for logging
                            chat_session = request.user.chat_history.get(request.chat_session_guid)
                            LogFire.log("OUTPUT", f"Sent Discord message with {len(discord_files)} image attachments", chat=chat_session)
                        except Exception as e:
                            # Get the user's chat session for logging
                            chat_session = request.user.chat_history.get(request.chat_session_guid)
                            LogFire.log("ERROR", f"Error sending Discord message with attachments: {e}", severity="error", chat=chat_session)
                            # Fallback to sending without attachments
                            await physical_message.reply(first_chunk)
                        
                        # Send remaining chunks without attachments
                        for chunk in chunks[1:]:
                            await physical_message.reply(chunk)
                else:
                    # No valid files, send normally
                    async def discord_reply_callback(chunk):
                        return await physical_message.reply(chunk)
                    await split_and_process_text(clean_text, callback=discord_reply_callback, max_length=2000, min_last_chunk_length=500)
            else:
                # No image paths found, send normally
                async def discord_reply_callback(chunk):
                    return await physical_message.reply(chunk)
                await split_and_process_text(text, callback=discord_reply_callback, max_length=2000, min_last_chunk_length=500)
        elif self.name == "Whatsapp":
            from endpoints.whatsapp_endpoint import MyWhatsappBot
            # For WhatsApp, physical_message contains the original message data
            # Extract the sender's phone number from the original message
            sender_id = None
            if isinstance(physical_message, dict) and 'from' in physical_message:
                sender_id = physical_message['from']
            await split_and_process_text(text, callback=(lambda c: MyWhatsappBot.send_a_whatsapp_message(c, sender_id)), max_length=1000, min_last_chunk_length=200)

        if add_to_chat_history:
            # Extract call_trace from task if available
            call_trace = []
            try:
                if hasattr(request, 'call_trace'):
                    call_trace = request.call_trace if isinstance(request.call_trace, list) else [request.call_trace]
                    # Get the user's chat session for logging
                    chat_session = request.user.chat_history.get(request.chat_session_guid)
                    LogFire.log("DEBUG", f"MyBot extracted call_trace: {call_trace}", chat=chat_session)
            except Exception as e:
                # If we can't get call_trace, continue without it
                # Get the user's chat session for logging
                chat_session = request.user.chat_history.get(request.chat_session_guid)
                LogFire.log("DEBUG", f"MyBot call_trace extraction error: {e}", chat=chat_session)
                pass
            
            # Get the user's chat session for logging
            chat_session = request.user.chat_history.get(request.chat_session_guid)
            LogFire.log("DEBUG", f"MyBot final call_trace before message creation: {call_trace}", chat=chat_session)
            
            message = ZairaMessage.create_assistant_message(
                content=text,
                conversation_id=request.user.session_guid, 
                session_id=request.user.session_guid, 
                tokens_used=len(text),
                call_trace=call_trace
            )
            # Get or create chat session
            if request.user.session_guid not in request.user.chat_history:
                from userprofiles.ZairaChat import ZairaChat
                from datetime import datetime
                timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")
                request.user.chat_history[request.user.session_guid] = ZairaChat(
                    session_guid=request.user.session_guid,
                    user_guid=str(request.user.user_guid),
                    conversation_guid=request.user.session_guid,
                    title=f"{self.name} Session - {timestamp}"
                )
            request.user.chat_history[request.user.session_guid].add_message(message)
