<div class="user-card" style="{card_style}">
    <div style="display: flex; justify-content: space-between; align-items: start;">
        <div style="flex: 1;">
            <div style="font-weight: 600; margin-bottom: 0.5rem;">
                {description}
                {incomplete_flag}
            </div>
            <div style="font-size: 0.85rem; color: #94a3b8;">
                ID: {scheduled_guid}<br>
                Created: {created_at}<br>
                Next Run: {next_run}<br>
                {schedule_info}
            </div>
            <div style="margin-top: 0.75rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
                <button onclick="showCallTrace('{scheduled_guid}')" 
                        title="View execution call trace for this request"
                        onmouseover="this.style.background='#2563eb'"
                        onmouseout="this.style.background='#3b82f6'"
                        style="padding: 0.25rem 0.5rem; background: #3b82f6; color: white; border: none; border-radius: 4px; font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                    Call Trace
                </button>
                <button onclick="showDebugMessages('{scheduled_guid}')" 
                        title="View debug messages for this request"
                        onmouseover="this.style.background='#059669'"
                        onmouseout="this.style.background='#10b981'"
                        style="padding: 0.25rem 0.5rem; background: #10b981; color: white; border: none; border-radius: 4px; font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                    Debug Messages
                </button>
                <button onclick="navigateToChatSession('{chat_session_guid}', '{user_guid}')" 
                        title="Navigate to chat history for this request"
                        onmouseover="this.style.background='#d97706'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(245, 158, 11, 0.4)'"
                        onmouseout="this.style.background='#f59e0b'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(245, 158, 11, 0.3)'"
                        style="padding: 0.25rem 0.75rem; background: #f59e0b; color: white; border: none; border-radius: 4px; font-size: 0.75rem; font-weight: 600; cursor: pointer; transition: all 0.2s; box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);">
                    📋 Chat History
                </button>
            </div>
        </div>
        <div style="text-align: right;">
            <span style="display: inline-block; padding: 0.25rem 0.75rem; border-radius: 12px; font-size: 0.8rem; font-weight: 600; background: {status_color}20; color: {status_color};">
                {status_text}
            </span>
        </div>
    </div>
</div>