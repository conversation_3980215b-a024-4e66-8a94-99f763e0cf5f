import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

async def test_supervisor_creation():
    print("Starting test...")
    
    # Import minimal setup
    from etc.ZairaSettings import ZairaSettings
    ZairaSettings.IsDebugMode = True
    
    # Setup logging
    from managers.manager_logfire import LogFire
    LogFire.log("TEST", "Starting supervisor creation test")
    
    # Import supervisor manager
    from managers.manager_supervisors import SupervisorManager
    from langchain_openai import ChatOpenAI
    
    # Set up minimal LLM
    print("Setting up LLM...")
    llm = ChatOpenAI(model_name="gpt-4o-mini", temperature=0.5, timeout=30)
    ZairaSettings.llm = llm
    
    # Setup supervisor manager
    print("Setting up SupervisorManager...")
    await SupervisorManager.setup()
    print("SupervisorManager setup complete")
    
    # Try to create a simple task
    print("Creating simple task...")
    from tasks.inputs.quick_search import create_task_quick_rag_search
    
    try:
        print("About to call create_task_quick_rag_search...")
        task = await create_task_quick_rag_search()
        print(f"Task created successfully: {task}")
    except Exception as e:
        print(f"Error creating task: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Running test_supervisor_creation...")
    asyncio.run(test_supervisor_creation())
    print("Test completed")