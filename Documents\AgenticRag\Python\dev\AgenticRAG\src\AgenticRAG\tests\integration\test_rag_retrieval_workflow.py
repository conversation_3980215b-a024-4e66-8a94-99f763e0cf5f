"""
Integration tests for RAG retrieval and response workflow
This test suite covers the complete RAG flow:
Query -> Vector Search -> Context Retrieval -> LLM Response -> Output
"""

import sys
import os
import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from typing import Dict, List, Any
from datetime import datetime
from uuid import uuid4

# Add project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from imports import *
from managers.manager_qdrant import QDrantManager
from managers.manager_postgreSQL import PostgreSQLManager
from managers.manager_supervisors import SupervisorManager
from tasks.inputs.quick_search import create_task_quick_rag_search, create_task_quick_llm_search
from tasks.inputs.task_retrieval import create_supervisor_retrieval
from userprofiles.ZairaUser import <PERSON>air<PERSON><PERSON>ser, PERMISSION_LEVELS

class TestRAGRetrievalWorkflow:
    """Test complete RAG retrieval workflow"""
    
    @pytest.fixture
    async def setup_rag_environment(self):
        """Set up test environment for RAG operations"""
        with patch('managers.manager_qdrant.QdrantClient') as mock_qdrant_client, \
             patch('managers.manager_postgreSQL.PostgreSQLManager') as mock_db, \
             patch('managers.manager_supervisors.SupervisorManager') as mock_supervisor, \
             patch('tasks.inputs.quick_search.OpenAIEmbeddings') as mock_embeddings, \
             patch('tasks.inputs.quick_search.ChatOpenAI') as mock_llm:
            
            # Mock Qdrant client
            mock_client_instance = Mock()
            mock_qdrant_client.return_value = mock_client_instance
            
            # Mock database
            mock_db_instance = Mock()
            mock_db.get_instance.return_value = mock_db_instance
            mock_connection = Mock()
            mock_db_instance.get_connection.return_value.__aenter__.return_value = mock_connection
            mock_db_instance.get_connection.return_value.__aexit__.return_value = None
            
            # Mock supervisor
            mock_supervisor_instance = Mock()
            mock_supervisor.get_instance.return_value = mock_supervisor_instance
            
            # Mock embeddings
            mock_embeddings_instance = Mock()
            mock_embeddings.return_value = mock_embeddings_instance
            mock_embeddings_instance.embed_query = AsyncMock(return_value=[0.1, 0.2, 0.3])
            
            # Mock LLM
            mock_llm_instance = Mock()
            mock_llm.return_value = mock_llm_instance
            mock_llm_instance.agenerate = AsyncMock()
            
            # Create test user
            test_user = ZairaUser(
                username="testuser",
                rank=PERMISSION_LEVELS.USER,
                guid=uuid4(),
                device_guid=uuid4()
            )
            
            yield {
                'qdrant_client': mock_qdrant_client,
                'db': mock_db,
                'supervisor': mock_supervisor,
                'embeddings': mock_embeddings,
                'llm': mock_llm,
                'test_user': test_user,
                'db_instance': mock_db_instance,
                'connection': mock_connection,
                'client_instance': mock_client_instance,
                'embeddings_instance': mock_embeddings_instance,
                'llm_instance': mock_llm_instance
            }
    
    @pytest.mark.asyncio
    async def test_rag_search_workflow(self, setup_rag_environment):
        """Test complete RAG search workflow"""
        env = setup_rag_environment
        
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        # Create Qdrant manager
        qdrant_manager = QDrantManager.get_instance()
        await QDrantManager.setup()
        
        # Mock Qdrant search response
        mock_search_results = [
            Mock(
                id="doc1",
                score=0.95,
                payload={
                    "content": "This is relevant content about the query",
                    "metadata": {"source": "document1.pdf", "page": 1}
                }
            ),
            Mock(
                id="doc2", 
                score=0.87,
                payload={
                    "content": "Additional relevant information",
                    "metadata": {"source": "document2.pdf", "page": 3}
                }
            )
        ]
        
        env['client_instance'].search.return_value = mock_search_results
        
        # Test vector search
        query = "test query about relevant topic"
        results = await qdrant_manager.search_vectors(
            collection_name="test_collection",
            query_vector=[0.1, 0.2, 0.3],
            limit=5
        )
        
        # Verify Qdrant was called
        env['client_instance'].search.assert_called_once()
        search_call = env['client_instance'].search.call_args
        
        # Verify search parameters
        assert search_call[1]['collection_name'] == "test_collection"
        assert search_call[1]['query_vector'] == [0.1, 0.2, 0.3]
        assert search_call[1]['limit'] == 5
        
        # Verify results
        assert len(results) == 2
        assert results[0].score == 0.95
        assert results[0].payload["content"] == "This is relevant content about the query"
        assert results[1].score == 0.87
        assert results[1].payload["content"] == "Additional relevant information"
    
    @pytest.mark.asyncio
    async def test_rag_embedding_workflow(self, setup_rag_environment):
        """Test RAG embedding generation workflow"""
        env = setup_rag_environment
        
        # Test query embedding
        query = "What is the meaning of life?"
        embedding = await env['embeddings_instance'].embed_query(query)
        
        # Verify embeddings were called
        env['embeddings_instance'].embed_query.assert_called_once_with(query)
        
        # Verify embedding result
        assert embedding == [0.1, 0.2, 0.3]
        assert len(embedding) == 3
        assert all(isinstance(x, float) for x in embedding)
    
    @pytest.mark.asyncio
    async def test_rag_context_retrieval_workflow(self, setup_rag_environment):
        """Test RAG context retrieval workflow"""
        env = setup_rag_environment
        
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        # Create Qdrant manager
        qdrant_manager = QDrantManager.get_instance()
        await QDrantManager.setup()
        
        # Mock comprehensive search results
        mock_search_results = [
            Mock(
                id="context1",
                score=0.92,
                payload={
                    "content": "Context paragraph 1: This provides background information about the topic. It explains the fundamental concepts and provides historical context.",
                    "metadata": {
                        "source": "knowledge_base.pdf",
                        "page": 15,
                        "section": "Background Information",
                        "timestamp": "2024-01-01T10:00:00Z"
                    }
                }
            ),
            Mock(
                id="context2",
                score=0.89,
                payload={
                    "content": "Context paragraph 2: This section details specific implementation approaches and best practices for the topic at hand.",
                    "metadata": {
                        "source": "implementation_guide.pdf", 
                        "page": 42,
                        "section": "Implementation Details",
                        "timestamp": "2024-01-02T14:30:00Z"
                    }
                }
            ),
            Mock(
                id="context3",
                score=0.85,
                payload={
                    "content": "Context paragraph 3: Here we discuss potential challenges and solutions, including troubleshooting common issues.",
                    "metadata": {
                        "source": "troubleshooting_manual.pdf",
                        "page": 7,
                        "section": "Common Issues",
                        "timestamp": "2024-01-03T09:15:00Z"
                    }
                }
            )
        ]
        
        env['client_instance'].search.return_value = mock_search_results
        
        # Test context retrieval
        query = "How do I implement this feature?"
        query_embedding = [0.1, 0.2, 0.3]
        
        context_results = await qdrant_manager.search_vectors(
            collection_name="knowledge_base",
            query_vector=query_embedding,
            limit=10
        )
        
        # Verify search was performed
        env['client_instance'].search.assert_called_once()
        
        # Verify context quality
        assert len(context_results) == 3
        
        # Verify results are sorted by score
        assert context_results[0].score >= context_results[1].score
        assert context_results[1].score >= context_results[2].score
        
        # Verify context content
        assert "background information" in context_results[0].payload["content"].lower()
        assert "implementation approaches" in context_results[1].payload["content"].lower()
        assert "challenges and solutions" in context_results[2].payload["content"].lower()
        
        # Verify metadata preservation
        assert context_results[0].payload["metadata"]["source"] == "knowledge_base.pdf"
        assert context_results[1].payload["metadata"]["page"] == 42
        assert context_results[2].payload["metadata"]["section"] == "Common Issues"
    
    @pytest.mark.asyncio
    async def test_rag_response_generation_workflow(self, setup_rag_environment):
        """Test RAG response generation workflow"""
        env = setup_rag_environment
        
        # Mock LLM response
        from langchain_core.messages import AIMessage
        mock_response = Mock()
        mock_response.generations = [[Mock(text="Based on the provided context, I can explain that the implementation requires following these steps: 1) Setup the environment, 2) Configure the parameters, 3) Execute the process. The context suggests this approach is most effective for achieving the desired outcome.")]]
        env['llm_instance'].agenerate.return_value = mock_response
        
        # Test response generation
        context = """
        Context paragraph 1: This provides background information about the topic.
        Context paragraph 2: This section details specific implementation approaches.
        Context paragraph 3: Here we discuss potential challenges and solutions.
        """
        
        query = "How do I implement this feature?"
        
        # Simulate LLM call
        messages = [
            Mock(content=f"Context: {context}"),
            Mock(content=f"Question: {query}")
        ]
        
        response = await env['llm_instance'].agenerate([messages])
        
        # Verify LLM was called
        env['llm_instance'].agenerate.assert_called_once()
        
        # Verify response structure
        assert response.generations is not None
        assert len(response.generations) == 1
        assert len(response.generations[0]) == 1
        
        # Verify response content
        response_text = response.generations[0][0].text
        assert "implementation requires" in response_text
        assert "following these steps" in response_text
        assert "setup the environment" in response_text.lower()
        assert "configure the parameters" in response_text.lower()
        assert "execute the process" in response_text.lower()
    
    @pytest.mark.asyncio
    async def test_rag_quick_search_task_workflow(self, setup_rag_environment):
        """Test RAG quick search task workflow"""
        env = setup_rag_environment
        
        # Mock task creation
        with patch('tasks.inputs.quick_search.SupervisorTask_SingleAgent') as mock_task_class:
            mock_task_instance = Mock()
            mock_task_class.return_value = mock_task_instance
            
            # Mock task execution
            mock_task_instance.llm_call = AsyncMock(return_value="RAG search completed: Found relevant information about the query topic with high confidence score.")
            
            # Create RAG search task
            rag_task = await create_task_quick_rag_search()
            
            # Verify task was created
            assert rag_task is not None
            assert isinstance(rag_task, Mock)  # Mock instance
            
            # Test task execution
            test_input = "search for information about machine learning"
            result = await rag_task.llm_call(test_input)
            
            # Verify task executed
            mock_task_instance.llm_call.assert_called_once_with(test_input)
            
            # Verify result
            assert "RAG search completed" in result
            assert "relevant information" in result
            assert "high confidence score" in result
    
    @pytest.mark.asyncio
    async def test_rag_llm_search_task_workflow(self, setup_rag_environment):
        """Test RAG LLM search task workflow"""
        env = setup_rag_environment
        
        # Mock task creation
        with patch('tasks.inputs.quick_search.SupervisorTask_SingleAgent') as mock_task_class:
            mock_task_instance = Mock()
            mock_task_class.return_value = mock_task_instance
            
            # Mock task execution
            mock_task_instance.llm_call = AsyncMock(return_value="LLM search completed: Generated response based on general knowledge without specific context retrieval.")
            
            # Create LLM search task
            llm_task = await create_task_quick_llm_search()
            
            # Verify task was created
            assert llm_task is not None
            assert isinstance(llm_task, Mock)  # Mock instance
            
            # Test task execution
            test_input = "explain quantum computing concepts"
            result = await llm_task.llm_call(test_input)
            
            # Verify task executed
            mock_task_instance.llm_call.assert_called_once_with(test_input)
            
            # Verify result
            assert "LLM search completed" in result
            assert "generated response" in result
            assert "general knowledge" in result
    
    @pytest.mark.asyncio
    async def test_rag_retrieval_supervisor_workflow(self, setup_rag_environment):
        """Test RAG retrieval supervisor workflow"""
        env = setup_rag_environment
        
        # Mock supervisor creation
        with patch('tasks.inputs.task_retrieval.SupervisorSupervisor') as mock_supervisor_class:
            mock_supervisor_instance = Mock()
            mock_supervisor_class.return_value = mock_supervisor_instance
            
            # Mock supervisor execution
            mock_supervisor_instance.llm_call = AsyncMock(return_value="Retrieval supervisor completed: Coordinated multiple retrieval tasks and consolidated results into comprehensive response.")
            
            # Create retrieval supervisor
            retrieval_supervisor = await create_supervisor_retrieval()
            
            # Verify supervisor was created
            assert retrieval_supervisor is not None
            assert isinstance(retrieval_supervisor, Mock)  # Mock instance
            
            # Test supervisor execution
            test_input = "comprehensive research on artificial intelligence"
            result = await retrieval_supervisor.llm_call(test_input)
            
            # Verify supervisor executed
            mock_supervisor_instance.llm_call.assert_called_once_with(test_input)
            
            # Verify result
            assert "Retrieval supervisor completed" in result
            assert "coordinated multiple retrieval tasks" in result
            assert "consolidated results" in result
    
    @pytest.mark.asyncio
    async def test_rag_end_to_end_workflow(self, setup_rag_environment):
        """Test complete RAG end-to-end workflow"""
        env = setup_rag_environment
        
        # Reset singletons
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        # Create Qdrant manager
        qdrant_manager = QDrantManager.get_instance()
        await QDrantManager.setup()
        
        # Mock complete workflow
        # 1. Query embedding
        query = "What are the best practices for software testing?"
        query_embedding = [0.1, 0.2, 0.3, 0.4, 0.5]
        env['embeddings_instance'].embed_query = AsyncMock(return_value=query_embedding)
        
        # 2. Vector search results
        mock_search_results = [
            Mock(
                id="testing_doc1",
                score=0.94,
                payload={
                    "content": "Unit testing is fundamental to software quality. It involves testing individual components in isolation to ensure they work correctly.",
                    "metadata": {"source": "testing_guide.pdf", "page": 12}
                }
            ),
            Mock(
                id="testing_doc2",
                score=0.91,
                payload={
                    "content": "Integration testing verifies that different components work together properly. This is crucial for identifying interface issues.",
                    "metadata": {"source": "testing_guide.pdf", "page": 24}
                }
            ),
            Mock(
                id="testing_doc3",
                score=0.88,
                payload={
                    "content": "Test-driven development (TDD) is a methodology where tests are written before the code. This ensures better code quality and design.",
                    "metadata": {"source": "development_practices.pdf", "page": 7}
                }
            )
        ]
        
        env['client_instance'].search.return_value = mock_search_results
        
        # 3. LLM response generation
        mock_llm_response = Mock()
        mock_llm_response.generations = [[Mock(text="Based on the provided context, here are the best practices for software testing: 1) Implement unit testing for individual components, 2) Use integration testing to verify component interactions, 3) Consider test-driven development (TDD) for better code quality. These practices ensure comprehensive coverage and maintainable code.")]]
        env['llm_instance'].agenerate.return_value = mock_llm_response
        
        # Execute complete workflow
        # Step 1: Generate embedding
        embedding = await env['embeddings_instance'].embed_query(query)
        
        # Step 2: Search vectors
        search_results = await qdrant_manager.search_vectors(
            collection_name="knowledge_base",
            query_vector=embedding,
            limit=5
        )
        
        # Step 3: Extract context
        context_parts = []
        for result in search_results:
            context_parts.append(f"Source: {result.payload['metadata']['source']}")
            context_parts.append(f"Content: {result.payload['content']}")
            context_parts.append(f"Relevance Score: {result.score}")
            context_parts.append("---")
        
        context = "\n".join(context_parts)
        
        # Step 4: Generate response
        messages = [
            Mock(content=f"Context: {context}"),
            Mock(content=f"Question: {query}")
        ]
        
        response = await env['llm_instance'].agenerate([messages])
        
        # Verify complete workflow
        # 1. Embedding generation
        env['embeddings_instance'].embed_query.assert_called_once_with(query)
        assert embedding == [0.1, 0.2, 0.3, 0.4, 0.5]
        
        # 2. Vector search
        env['client_instance'].search.assert_called_once()
        search_call = env['client_instance'].search.call_args
        assert search_call[1]['query_vector'] == query_embedding
        assert search_call[1]['limit'] == 5
        
        # 3. Context extraction
        assert len(search_results) == 3
        assert "Unit testing is fundamental" in context
        assert "Integration testing verifies" in context
        assert "Test-driven development" in context
        
        # 4. Response generation
        env['llm_instance'].agenerate.assert_called_once()
        response_text = response.generations[0][0].text
        assert "best practices for software testing" in response_text
        assert "unit testing" in response_text.lower()
        assert "integration testing" in response_text.lower()
        assert "test-driven development" in response_text.lower()
    
    @pytest.mark.asyncio
    async def test_rag_error_handling_workflow(self, setup_rag_environment):
        """Test RAG error handling workflow"""
        env = setup_rag_environment
        
        # Reset singleton
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        # Create Qdrant manager
        qdrant_manager = QDrantManager.get_instance()
        await QDrantManager.setup()
        
        # Test 1: Embedding generation error
        env['embeddings_instance'].embed_query = AsyncMock(side_effect=Exception("Embedding service unavailable"))
        
        query = "test query"
        
        with pytest.raises(Exception) as exc_info:
            await env['embeddings_instance'].embed_query(query)
        
        assert "Embedding service unavailable" in str(exc_info.value)
        
        # Test 2: Vector search error
        env['client_instance'].search.side_effect = Exception("Qdrant search failed")
        
        with pytest.raises(Exception) as exc_info:
            await qdrant_manager.search_vectors(
                collection_name="test_collection",
                query_vector=[0.1, 0.2, 0.3],
                limit=5
            )
        
        assert "Qdrant search failed" in str(exc_info.value)
        
        # Test 3: LLM generation error
        env['llm_instance'].agenerate.side_effect = Exception("LLM service timeout")
        
        with pytest.raises(Exception) as exc_info:
            await env['llm_instance'].agenerate([Mock()])
        
        assert "LLM service timeout" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_rag_performance_workflow(self, setup_rag_environment):
        """Test RAG performance workflow"""
        env = setup_rag_environment
        
        # Reset singleton
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        # Create Qdrant manager
        qdrant_manager = QDrantManager.get_instance()
        await QDrantManager.setup()
        
        # Mock performance metrics
        import time
        
        # Test embedding performance
        start_time = time.time()
        env['embeddings_instance'].embed_query = AsyncMock(return_value=[0.1, 0.2, 0.3])
        
        query = "performance test query"
        embedding = await env['embeddings_instance'].embed_query(query)
        
        embedding_time = time.time() - start_time
        
        # Test search performance
        start_time = time.time()
        
        # Mock fast search results
        mock_results = [Mock(id=f"doc{i}", score=0.9-i*0.1, payload={"content": f"Content {i}"}) for i in range(10)]
        env['client_instance'].search.return_value = mock_results
        
        results = await qdrant_manager.search_vectors(
            collection_name="performance_test",
            query_vector=embedding,
            limit=10
        )
        
        search_time = time.time() - start_time
        
        # Verify performance
        assert embedding_time < 1.0  # Should be fast
        assert search_time < 1.0  # Should be fast
        assert len(results) == 10
        
        # Test LLM performance
        start_time = time.time()
        
        mock_response = Mock()
        mock_response.generations = [[Mock(text="Fast response")]]
        env['llm_instance'].agenerate = AsyncMock(return_value=mock_response)
        
        response = await env['llm_instance'].agenerate([Mock()])
        
        llm_time = time.time() - start_time
        
        # Verify LLM performance
        assert llm_time < 1.0  # Should be fast
        assert response.generations[0][0].text == "Fast response"

class TestRAGDataManagement:
    """Test RAG data management workflow"""
    
    @pytest.mark.asyncio
    async def test_rag_data_ingestion_workflow(self):
        """Test RAG data ingestion workflow"""
        with patch('managers.manager_qdrant.QdrantClient') as mock_qdrant_client, \
             patch('managers.manager_postgreSQL.PostgreSQLManager') as mock_db:
            
            # Mock Qdrant client
            mock_client_instance = Mock()
            mock_qdrant_client.return_value = mock_client_instance
            
            # Mock database
            mock_db_instance = Mock()
            mock_db.get_instance.return_value = mock_db_instance
            
            # Reset singleton
            QDrantManager._instance = None
            QDrantManager._initialized = False
            
            # Create Qdrant manager
            qdrant_manager = QDrantManager.get_instance()
            await QDrantManager.setup()
            
            # Test data ingestion
            documents = [
                {
                    "id": "doc1",
                    "content": "First document content",
                    "embedding": [0.1, 0.2, 0.3],
                    "metadata": {"source": "doc1.pdf", "page": 1}
                },
                {
                    "id": "doc2", 
                    "content": "Second document content",
                    "embedding": [0.4, 0.5, 0.6],
                    "metadata": {"source": "doc2.pdf", "page": 1}
                }
            ]
            
            # Mock upsert operation
            mock_client_instance.upsert.return_value = None
            
            # Test ingestion
            await qdrant_manager.upsert_vectors(
                collection_name="test_collection",
                documents=documents
            )
            
            # Verify upsert was called
            mock_client_instance.upsert.assert_called_once()
            upsert_call = mock_client_instance.upsert.call_args
            
            # Verify upsert parameters
            assert upsert_call[1]['collection_name'] == "test_collection"
            
            # Verify documents were processed
            points = upsert_call[1]['points']
            assert len(points) == 2
    
    @pytest.mark.asyncio
    async def test_rag_collection_management_workflow(self):
        """Test RAG collection management workflow"""
        with patch('managers.manager_qdrant.QdrantClient') as mock_qdrant_client:
            
            # Mock Qdrant client
            mock_client_instance = Mock()
            mock_qdrant_client.return_value = mock_client_instance
            
            # Reset singleton
            QDrantManager._instance = None
            QDrantManager._initialized = False
            
            # Create Qdrant manager
            qdrant_manager = QDrantManager.get_instance()
            await QDrantManager.setup()
            
            # Test collection creation
            collection_name = "test_collection"
            vector_size = 384
            
            mock_client_instance.create_collection.return_value = None
            
            await qdrant_manager.create_collection(
                collection_name=collection_name,
                vector_size=vector_size
            )
            
            # Verify collection creation
            mock_client_instance.create_collection.assert_called_once()
            create_call = mock_client_instance.create_collection.call_args
            
            assert create_call[1]['collection_name'] == collection_name
            
            # Test collection deletion
            mock_client_instance.delete_collection.return_value = None
            
            await qdrant_manager.delete_collection(collection_name)
            
            # Verify collection deletion
            mock_client_instance.delete_collection.assert_called_once()
            delete_call = mock_client_instance.delete_collection.call_args
            
            assert delete_call[1]['collection_name'] == collection_name