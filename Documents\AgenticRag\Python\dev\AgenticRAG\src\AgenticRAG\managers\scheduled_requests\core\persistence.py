from imports import *
import json
import asyncio
from datetime import datetime, timedelta, timezone
from typing import Dict, List, Optional, Any
from uuid import UUID

from managers.manager_postgreSQL import PostgreSQLManager
from ..utils.exceptions import ScheduledRequestNotFoundError
from ..utils.helpers import validate_guid, safe_json_serialize

if TYPE_CHECKING:
    from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest

class ScheduledRequestPersistenceManager:
    """
    Enhanced persistence manager with user-scoped operations and security
    """
    
    _instance: Optional['ScheduledRequestPersistenceManager'] = None
    _initialized: bool = False
    
    def __init__(self):
        self._db_available = True
        self._setup_lock = asyncio.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_instance(cls) -> 'ScheduledRequestPersistenceManager':
        """Get singleton instance"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    async def setup(cls):
        """Initialize the persistence manager"""
        instance = cls.get_instance()
        if instance._initialized:
            return
        
        async with instance._setup_lock:
            if instance._initialized:
                return
            
            try:
                await instance._create_tables_and_indexes()
                instance._initialized = True
                
            except Exception as e:
                LogFire.log("ERROR", f"Failed to initialize persistence manager: {str(e)}")
                instance._db_available = False
    
    async def late_setup(self):
        """Late setup after all managers are initialized - recover active tasks"""
        try:
            if not self._db_available:
                LogFire.log("WARNING", "Database not available, skipping task recovery")
                return
            
            # Check for active requests that need recovery
            active_requests = await self.get_active_requests()
            recovered_count = len(active_requests)
            
            # Group tasks by user to trigger user manager creation
            users_with_tasks = {}
            for request_data in active_requests:
                user_guid = request_data['user_guid']
                if user_guid not in users_with_tasks:
                    users_with_tasks[user_guid] = []
                users_with_tasks[user_guid].append(request_data['scheduled_guid'])
                LogFire.log("REQUEST", f"Task {request_data['scheduled_guid']} available for recovery")
            
            # Proactively create user managers to trigger task recovery
            if users_with_tasks:
                try:
                    from .factory import ScheduledRequestManagerFactory
                    factory = ScheduledRequestManagerFactory.get_instance()
                    
                    for user_guid, task_guids in users_with_tasks.items():
                        try:
                            # This will create the user manager and trigger _load_user_requests
                            user_manager = await factory.get_user_manager(user_guid)
                            LogFire.log("REQUEST", f"User manager created for {user_guid} to recover {len(task_guids)} tasks")
                        except Exception as e:
                            LogFire.log("ERROR", f"Failed to create user manager for {user_guid}: {str(e)}")
                            
                except Exception as e:
                    LogFire.log("ERROR", f"Failed to trigger user manager creation during recovery: {str(e)}")
            
            LogFire.log("REQUEST", f"Task recovery completed: {recovered_count} tasks available for {len(users_with_tasks)} users")
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed during late setup task recovery: {str(e)}")
    
    async def _create_tables_and_indexes(self):
        """Create tables and user-specific indexes for optimal performance"""
        try:
            # Check if table exists and verify structure
            check_table_query = """
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_name = 'scheduled_requests'
            AND table_schema = 'public'
            ORDER BY ordinal_position
            """
            
            try:
                columns_result = await PostgreSQLManager.execute_query("vectordb", check_table_query)
                if columns_result:
                    column_names = [row['column_name'] for row in columns_result]
                    required_columns = ['scheduled_guid', 'user_guid', 'schedule_prompt', 'target_prompt']
                    
                    missing_columns = [col for col in required_columns if col not in column_names]
                    if missing_columns:
                        LogFire.log("WARNING", f"Table missing columns: {missing_columns}. Recreating...")
                        await self._execute_single_operation("DROP TABLE IF EXISTS scheduled_requests CASCADE")
                        
            except Exception as e:
                LogFire.log("INIT", f"Table check resulted in: {str(e)}")
            
            # Create main table with enhanced structure
            await self._execute_single_operation("""
            CREATE TABLE IF NOT EXISTS scheduled_requests (
                scheduled_guid VARCHAR(36) PRIMARY KEY,
                user_guid VARCHAR(36) NOT NULL,
                schedule_prompt TEXT NOT NULL,
                target_prompt TEXT NOT NULL,
                delay_seconds FLOAT NOT NULL,
                start_delay_seconds FLOAT DEFAULT 0.0,
                schedule_type VARCHAR(20) NOT NULL,
                next_execution TIMESTAMPTZ,
                is_active BOOLEAN DEFAULT TRUE,
                run_on_startup BOOLEAN DEFAULT FALSE,
                created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
                calling_bot_name VARCHAR(100),
                task_data JSONB,
                cancellation_reason TEXT,
                cancelled_at TIMESTAMPTZ,
                
                -- New fields for enhanced functionality
                user_permission_level VARCHAR(20),
                execution_count INTEGER DEFAULT 0,
                last_executed TIMESTAMPTZ,
                average_execution_time FLOAT DEFAULT 0.0,
                failure_count INTEGER DEFAULT 0,
                last_failure_reason TEXT,
                
                -- Security and audit fields
                created_by_ip VARCHAR(45),
                last_modified_by VARCHAR(36),
                security_hash VARCHAR(64)
            )
            """)
            
            # Create user-specific indexes for performance
            user_indexes = [
                ("idx_scheduled_requests_user_active", 
                 "CREATE INDEX IF NOT EXISTS idx_scheduled_requests_user_active ON scheduled_requests(user_guid, is_active) WHERE is_active = TRUE"),
                ("idx_scheduled_requests_user_next_exec", 
                 "CREATE INDEX IF NOT EXISTS idx_scheduled_requests_user_next_exec ON scheduled_requests(user_guid, next_execution) WHERE is_active = TRUE"),
                ("idx_scheduled_requests_user_created", 
                 "CREATE INDEX IF NOT EXISTS idx_scheduled_requests_user_created ON scheduled_requests(user_guid, created_at)"),
                ("idx_scheduled_requests_execution_due", 
                 "CREATE INDEX IF NOT EXISTS idx_scheduled_requests_execution_due ON scheduled_requests(next_execution) WHERE is_active = TRUE AND next_execution IS NOT NULL"),
                ("idx_scheduled_requests_user_type", 
                 "CREATE INDEX IF NOT EXISTS idx_scheduled_requests_user_type ON scheduled_requests(user_guid, schedule_type, is_active)"),
                ("idx_scheduled_requests_performance", 
                 "CREATE INDEX IF NOT EXISTS idx_scheduled_requests_performance ON scheduled_requests(user_guid, execution_count, average_execution_time) WHERE is_active = TRUE"),
            ]
            
            for index_name, index_query in user_indexes:
                try:
                    await self._execute_single_operation(index_query)
                except Exception as e:
                    LogFire.log("WARNING", f"Failed to create index {index_name}: {str(e)}")
            
            # Create user quota tracking table
            await self._execute_single_operation("""
            CREATE TABLE IF NOT EXISTS user_scheduled_quotas (
                user_guid VARCHAR(36) PRIMARY KEY,
                daily_tasks_used INTEGER DEFAULT 0,
                concurrent_tasks_count INTEGER DEFAULT 0,
                memory_usage_mb FLOAT DEFAULT 0,
                last_reset_date DATE DEFAULT CURRENT_DATE,
                quota_violations INTEGER DEFAULT 0,
                created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
            )
            """)
            
            # Create audit log table
            await self._execute_single_operation("""
            CREATE TABLE IF NOT EXISTS scheduled_requests_audit (
                audit_id SERIAL PRIMARY KEY,
                user_guid VARCHAR(36) NOT NULL,
                scheduled_guid VARCHAR(36),
                operation VARCHAR(50) NOT NULL,
                operation_details JSONB,
                ip_address VARCHAR(45),
                user_agent TEXT,
                timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                success BOOLEAN DEFAULT TRUE,
                error_message TEXT
            )
            """)
            
            await self._execute_single_operation("""
            CREATE INDEX IF NOT EXISTS idx_audit_user_timestamp 
            ON scheduled_requests_audit(user_guid, timestamp DESC)
            """)
            
            LogFire.log("INIT", "Database tables and indexes created/verified")
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to create tables: {str(e)}")
            self._db_available = False
            raise
    
    async def _execute_single_operation(self, query: str, params=None):
        """Execute a single database operation"""
        try:
            if params:
                await PostgreSQLManager.execute_query("vectordb", query, params)
            else:
                await PostgreSQLManager.execute_query("vectordb", query)
        except Exception as e:
            LogFire.log("WARNING", f"Database operation failed: {str(e)}")
            raise
    
    async def save_task(self, scheduled_request: 'ScheduledZairaRequest', created_by_ip: str = None) -> bool:
        """
        Save a scheduled request with user-scoped security
        
        Args:
            scheduled_request: The request to save
            created_by_ip: IP address of creator for audit
            
        Returns:
            bool: True if saved successfully
        """
        if not self._db_available:
            LogFire.log("WARNING", "Database not available, cannot save scheduled request")
            return False
        
        try:
            user_guid = str(scheduled_request.user.user_guid)
            scheduled_guid = str(scheduled_request.scheduled_guid)
            
            # Validate user ownership
            if not validate_guid(user_guid) or not validate_guid(scheduled_guid):
                return False
            
            # Prepare enhanced task data
            # Note: task_status is NOT persisted - it should be fetched from active requests only
            task_data = {
                'schedule_info': scheduled_request.get_schedule_info(),
                # 'task_status': removed - fetch separately from active requests
                'original_message_type': type(scheduled_request.original_physical_message).__name__ if scheduled_request.original_physical_message else None,
                'security_context': {
                    'created_by_user': user_guid,
                    'created_at': datetime.now(timezone.utc).isoformat(),
                    'version': '2.0'
                }
            }
            
            # Generate security hash for integrity
            security_hash = self._generate_security_hash(scheduled_request, user_guid)
            
            # Convert timezone-aware datetimes
            next_execution_naive = None
            if scheduled_request.next_execution:
                if scheduled_request.next_execution.tzinfo is not None:
                    next_execution_naive = scheduled_request.next_execution.astimezone(timezone.utc).replace(tzinfo=None)
                else:
                    next_execution_naive = scheduled_request.next_execution
            
            current_time_naive = datetime.now(timezone.utc).replace(tzinfo=None)
            
            # Enhanced insert query
            insert_query = """
            INSERT INTO scheduled_requests (
                scheduled_guid, user_guid, schedule_prompt, target_prompt, 
                delay_seconds, start_delay_seconds, schedule_type, next_execution, is_active,
                run_on_startup, calling_bot_name, task_data, created_at, updated_at,
                user_permission_level, execution_count, created_by_ip, security_hash
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18)
            ON CONFLICT (scheduled_guid) DO UPDATE SET
                schedule_prompt = EXCLUDED.schedule_prompt,
                target_prompt = EXCLUDED.target_prompt,
                delay_seconds = EXCLUDED.delay_seconds,
                start_delay_seconds = EXCLUDED.start_delay_seconds,
                schedule_type = EXCLUDED.schedule_type,
                next_execution = EXCLUDED.next_execution,
                is_active = EXCLUDED.is_active,
                run_on_startup = EXCLUDED.run_on_startup,
                calling_bot_name = EXCLUDED.calling_bot_name,
                task_data = EXCLUDED.task_data,
                updated_at = EXCLUDED.updated_at,
                security_hash = EXCLUDED.security_hash
            """
            
            await self._execute_single_operation(
                insert_query,
                [
                    scheduled_guid,
                    user_guid,
                    scheduled_request.schedule_prompt,
                    scheduled_request.target_prompt,
                    scheduled_request.delay_seconds,
                    scheduled_request.start_delay_seconds,
                    scheduled_request.schedule_type.value,
                    next_execution_naive,
                    scheduled_request.is_active,
                    scheduled_request.run_on_startup,
                    scheduled_request.calling_bot.name if scheduled_request.calling_bot else None,
                    safe_json_serialize(task_data),
                    current_time_naive,
                    current_time_naive,
                    str(scheduled_request.user.rank.value) if hasattr(scheduled_request.user, 'rank') else 'USER',
                    0,  # execution_count
                    created_by_ip,
                    security_hash
                ]
            )
            
            # Update user quota tracking
            await self._update_user_quota_tracking(user_guid, 'task_created')
            
            # Log audit trail
            await self._log_audit_event(
                user_guid, scheduled_guid, 'CREATE', 
                {'schedule_prompt': scheduled_request.schedule_prompt[:100]},
                created_by_ip, True
            )
            
            LogFire.log("REQUEST", f"Saved scheduled request {scheduled_guid} for user {user_guid}")
            return True
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to save scheduled request: {str(e)}")
            # Log failed audit event
            try:
                await self._log_audit_event(
                    user_guid, scheduled_guid, 'CREATE_FAILED', 
                    {'error': str(e)}, created_by_ip, False, str(e)
                )
            except:
                pass  # Don't fail on audit logging failure
            return False
    
    async def load_request(self, scheduled_guid: str) -> Optional[Dict[str, Any]]:
        """
        Load a specific request by GUID
        
        Args:
            scheduled_guid: The GUID of the request to load
            
        Returns:
            Request data dictionary or None if not found
        """
        if not self._db_available or not validate_guid(scheduled_guid):
            return None
        
        try:
            query = """
            SELECT * FROM scheduled_requests 
            WHERE scheduled_guid = $1
            """
            results = await PostgreSQLManager.execute_query("vectordb", query, [scheduled_guid])
            
            if results:
                return dict(results[0])
            return None
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to load request {scheduled_guid}: {str(e)}")
            return None
    
    async def get_active_requests(self, user_guid: Optional[str] = None) -> List[Dict[str, Any]]:
        """
        Get active scheduled requests, optionally filtered by user
        
        Args:
            user_guid: Optional user GUID to filter by
            
        Returns:
            List of request dictionaries
        """
        if not self._db_available:
            return []
        
        try:
            if user_guid:
                # User-specific query with security validation
                if not validate_guid(user_guid):
                    return []
                
                query = """
                SELECT * FROM scheduled_requests 
                WHERE user_guid = $1 AND is_active = TRUE 
                ORDER BY created_at DESC
                """
                results = await PostgreSQLManager.execute_query("vectordb", query, [user_guid])
            else:
                # System-wide query (admin only)
                query = """
                SELECT * FROM scheduled_requests 
                WHERE is_active = TRUE 
                ORDER BY user_guid, created_at DESC
                """
                results = await PostgreSQLManager.execute_query("vectordb", query)
            
            if results:
                # Validate security hashes and filter out tampered records
                validated_results = []
                for row in results:
                    try:
                        row_dict = dict(row)
                        if self._validate_security_hash(row_dict):
                            validated_results.append(row_dict)
                        else:
                            LogFire.log("WARNING", f"Security hash validation failed for request {row_dict.get('scheduled_guid')}")
                    except Exception as e:
                        LogFire.log("WARNING", f"Error validating request {row.get('scheduled_guid')}: {str(e)}")
                
                return validated_results
            return []
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get active requests: {str(e)}")
            return []
    
    async def cancel_task(self, scheduled_guid: str, user_guid: str, reason: str = "User requested cancellation") -> bool:
        """
        Cancel a scheduled request with user authorization
        
        Args:
            scheduled_guid: GUID of request to cancel
            user_guid: GUID of user requesting cancellation
            reason: Reason for cancellation
            
        Returns:
            bool: True if cancelled successfully
        """
        if not validate_guid(scheduled_guid) or not validate_guid(user_guid):
            return False
        
        try:
            # Verify user ownership before cancellation
            ownership_query = """
            SELECT user_guid FROM scheduled_requests 
            WHERE scheduled_guid = $1 AND is_active = TRUE
            """
            ownership_result = await PostgreSQLManager.execute_query("vectordb", ownership_query, [scheduled_guid])
            
            if not ownership_result or ownership_result[0]['user_guid'] != user_guid:
                LogFire.log("WARNING", f"Unauthorized cancellation attempt: user {user_guid} tried to cancel {scheduled_guid}")
                await self._log_audit_event(
                    user_guid, scheduled_guid, 'CANCEL_UNAUTHORIZED', 
                    {'reason': 'User does not own this request'}, None, False, "Unauthorized"
                )
                return False
            
            # Perform cancellation
            current_time_naive = datetime.now(timezone.utc).replace(tzinfo=None)
            
            update_query = """
            UPDATE scheduled_requests 
            SET is_active = FALSE, 
                cancellation_reason = $1, 
                cancelled_at = $2,
                updated_at = $2
            WHERE scheduled_guid = $3 AND user_guid = $4
            """
            
            await self._execute_single_operation(
                update_query, 
                [reason, current_time_naive, scheduled_guid, user_guid]
            )
            
            # Update quota tracking
            await self._update_user_quota_tracking(user_guid, 'task_cancelled')
            
            # Log audit event
            await self._log_audit_event(
                user_guid, scheduled_guid, 'CANCEL', 
                {'reason': reason}, None, True
            )
            
            LogFire.log("REQUEST", f"Cancelled scheduled request {scheduled_guid} for user {user_guid}: {reason}")
            return True
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to cancel task {scheduled_guid}: {str(e)}")
            return False
    
    async def update_task_execution_stats(self, scheduled_guid: str, user_guid: str, 
                                        execution_time: float, success: bool, 
                                        error_message: str = None) -> bool:
        """Update execution statistics for a task"""
        try:
            current_time = datetime.now(timezone.utc).replace(tzinfo=None)
            
            if success:
                update_query = """
                UPDATE scheduled_requests 
                SET execution_count = execution_count + 1,
                    last_executed = $1,
                    average_execution_time = CASE 
                        WHEN execution_count = 0 THEN $2
                        ELSE (average_execution_time * execution_count + $2) / (execution_count + 1)
                    END,
                    updated_at = $1
                WHERE scheduled_guid = $3 AND user_guid = $4
                """
                await self._execute_single_operation(update_query, [current_time, execution_time, scheduled_guid, user_guid])
            else:
                update_query = """
                UPDATE scheduled_requests 
                SET failure_count = failure_count + 1,
                    last_failure_reason = $1,
                    updated_at = $2
                WHERE scheduled_guid = $3 AND user_guid = $4
                """
                await self._execute_single_operation(update_query, [error_message or 'Unknown error', current_time, scheduled_guid, user_guid])
            
            return True
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to update execution stats for {scheduled_guid}: {str(e)}")
            return False
    
    async def get_user_quota_info(self, user_guid: str) -> Dict[str, Any]:
        """Get current quota usage for user"""
        if not validate_guid(user_guid):
            return {}
        
        try:
            query = """
            SELECT * FROM user_scheduled_quotas 
            WHERE user_guid = $1
            """
            result = await PostgreSQLManager.execute_query("vectordb", query, [user_guid])
            
            if result:
                return dict(result[0])
            else:
                # Initialize quota tracking for new user
                await self._initialize_user_quota_tracking(user_guid)
                return {
                    'user_guid': user_guid,
                    'daily_tasks_used': 0,
                    'concurrent_tasks_count': 0,
                    'memory_usage_mb': 0,
                    'quota_violations': 0
                }
                
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get quota info for user {user_guid}: {str(e)}")
            return {}
    
    async def _update_user_quota_tracking(self, user_guid: str, operation: str):
        """Update user quota tracking"""
        try:
            current_date = datetime.now(timezone.utc).date()
            
            # Ensure user quota record exists
            await self._initialize_user_quota_tracking(user_guid)
            
            if operation == 'task_created':
                update_query = """
                UPDATE user_scheduled_quotas 
                SET daily_tasks_used = CASE 
                        WHEN last_reset_date < $1 THEN 1 
                        ELSE daily_tasks_used + 1 
                    END,
                    concurrent_tasks_count = concurrent_tasks_count + 1,
                    last_reset_date = $1,
                    updated_at = CURRENT_TIMESTAMP
                WHERE user_guid = $2
                """
                await self._execute_single_operation(update_query, [current_date, user_guid])
                
            elif operation == 'task_cancelled' or operation == 'task_completed':
                update_query = """
                UPDATE user_scheduled_quotas 
                SET concurrent_tasks_count = GREATEST(0, concurrent_tasks_count - 1),
                    updated_at = CURRENT_TIMESTAMP
                WHERE user_guid = $1
                """
                await self._execute_single_operation(update_query, [user_guid])
                
        except Exception as e:
            LogFire.log("ERROR", f"Failed to update quota tracking for {user_guid}: {str(e)}")
    
    async def _initialize_user_quota_tracking(self, user_guid: str):
        """Initialize quota tracking for user if not exists"""
        try:
            query = """
            INSERT INTO user_scheduled_quotas (user_guid, last_reset_date)
            VALUES ($1, CURRENT_DATE)
            ON CONFLICT (user_guid) DO NOTHING
            """
            await self._execute_single_operation(query, [user_guid])
        except Exception as e:
            LogFire.log("ERROR", f"Failed to initialize quota tracking for {user_guid}: {str(e)}")
    
    async def _log_audit_event(self, user_guid: str, scheduled_guid: str, operation: str, 
                             details: Dict[str, Any], ip_address: str = None, 
                             success: bool = True, error_message: str = None):
        """Log audit event"""
        try:
            query = """
            INSERT INTO scheduled_requests_audit 
            (user_guid, scheduled_guid, operation, operation_details, ip_address, success, error_message)
            VALUES ($1, $2, $3, $4, $5, $6, $7)
            """
            await self._execute_single_operation(
                query, 
                [user_guid, scheduled_guid, operation, safe_json_serialize(details), 
                 ip_address, success, error_message]
            )
        except Exception as e:
            LogFire.log("ERROR", f"Failed to log audit event: {str(e)}")
    
    def _generate_security_hash(self, scheduled_request: 'ScheduledZairaRequest', user_guid: str) -> str:
        """Generate security hash for data integrity"""
        import hashlib
        
        # Create hash from key fields
        hash_data = f"{scheduled_request.scheduled_guid}{user_guid}{scheduled_request.schedule_prompt}{scheduled_request.target_prompt}"
        return hashlib.sha256(hash_data.encode('utf-8')).hexdigest()[:32]
    
    def _validate_security_hash(self, row_data: Dict[str, Any]) -> bool:
        """Validate security hash for data integrity"""
        try:
            stored_hash = row_data.get('security_hash')
            if not stored_hash:
                return True  # Legacy records without hash are accepted
            
            # Recreate hash from stored data
            hash_data = f"{row_data['scheduled_guid']}{row_data['user_guid']}{row_data['schedule_prompt']}{row_data['target_prompt']}"
            import hashlib
            calculated_hash = hashlib.sha256(hash_data.encode('utf-8')).hexdigest()[:32]
            
            return stored_hash == calculated_hash
            
        except Exception:
            return False  # Invalid on any error
    
    async def _recreate_task_from_data(self, task_data: Dict[str, Any]) -> Optional['ScheduledZairaRequest']:
        """Recreate task from database data - delegated to user manager"""
        # This method handles task recreation from data for this persistence manager
        # Implementation moved from legacy system to maintain compatibility
        try:
            from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest, ScheduleType
            from managers.manager_users import ZairaUserManager
            
            # Get user by GUID
            user_guid = task_data['user_guid']
            user = await ZairaUserManager.find_user(user_guid)
            if not user:
                LogFire.log("WARNING", f"User {user_guid} not found for task recreation")
                return None
            
            # Create bot instance
            from endpoints.mybot_generic import MyBot_Generic
            bot = MyBot_Generic(None, task_data.get('calling_bot_name', 'recovered_task'))
            
            # Create task instance
            task = ScheduledZairaRequest(
                user=user,
                calling_bot=bot,
                original_message=None,
                schedule_prompt=task_data['schedule_prompt'],
                target_prompt=task_data['target_prompt'],
                start_delay_seconds=task_data.get('start_delay_seconds', 0.0),
                delay_seconds=task_data['delay_seconds'],
                schedule_type=ScheduleType(task_data['schedule_type']),
                run_on_startup=task_data.get('run_on_startup', False)
            )
            
            # Override the scheduled_guid with the one from database
            from uuid import UUID
            object.__setattr__(task, 'scheduled_guid', UUID(task_data['scheduled_guid']))
            object.__setattr__(task, 'next_execution', task_data['next_execution'])
            object.__setattr__(task, 'is_active', task_data['is_active'])
            
            return task
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to recreate task from data: {str(e)}")
            return None
    
    async def cleanup_old_records(self, days_old: int = 30) -> int:
        """Clean up old cancelled/completed requests"""
        try:
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)
            
            # Clean up old requests
            delete_query = """
            DELETE FROM scheduled_requests 
            WHERE is_active = FALSE 
            AND (cancelled_at < $1 OR (cancelled_at IS NULL AND updated_at < $1))
            """
            
            result = await PostgreSQLManager.execute_query("vectordb", delete_query, [cutoff_date])
            
            # Clean up old audit logs
            audit_cleanup_query = """
            DELETE FROM scheduled_requests_audit 
            WHERE timestamp < $1
            """
            await self._execute_single_operation(audit_cleanup_query, [cutoff_date])
            
            LogFire.log("REQUEST", f"Cleaned up old records older than {days_old} days")
            return len(result) if result else 0
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to cleanup old records: {str(e)}")
            return 0