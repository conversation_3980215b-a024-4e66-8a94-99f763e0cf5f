from imports import *

import base64
import os
from pathlib import Path
from typing import Op<PERSON>, Dict, Tuple
import asyncio
from PIL import Image
import io

class SignatureManager:
    """Manager for handling email signatures with text and image support"""
    
    _instance: Optional['SignatureManager'] = None
    _initialized: bool = False
    _lock = asyncio.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_instance(cls) -> "SignatureManager":
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        async with cls._lock:
            if instance._initialized:
                return
            await instance._initialize()
            instance._initialized = True
    
    async def _initialize(self):
        """Initialize the signature manager"""
        # Cache for processed signatures to avoid repeated image processing
        self._signature_cache: Dict[str, str] = {}
        LogFire.log("INIT", "SignatureManager initialized successfully")
    
    def image_to_base64(self, image_path: str) -> Optional[Tuple[str, str]]:
        """
        Convert image file to base64 string for email embedding
        Returns tuple of (base64_data, mime_type) or None if failed
        """
        try:
            if not os.path.exists(image_path):
                LogFire.log("ERROR", f"Signature image not found: {image_path}", severity="warning")
                return None
            
            # Get file extension to determine MIME type
            ext = Path(image_path).suffix.lower()
            mime_types = {
                '.png': 'image/png',
                '.jpg': 'image/jpeg',
                '.jpeg': 'image/jpeg',
                '.gif': 'image/gif',
                '.bmp': 'image/bmp'
            }
            
            if ext not in mime_types:
                LogFire.log("ERROR", f"Unsupported image format: {ext}", severity="warning")
                return None
            
            # Read and encode image
            with open(image_path, 'rb') as img_file:
                img_data = img_file.read()
                base64_data = base64.b64encode(img_data).decode('utf-8')
                
            LogFire.log("DEBUG", f"Successfully encoded image: {image_path} ({len(img_data)} bytes)")
            return base64_data, mime_types[ext]
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to encode image {image_path}: {e}", severity="error")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "image_to_base64", None)
            return None
    
    def generate_html_signature(self, signature_text: str, image_path: Optional[str] = None) -> str:
        """
        Generate HTML signature with optional embedded image
        """
        try:
            # Cache key for this signature combination
            cache_key = f"{signature_text}:{image_path or ''}"
            if cache_key in self._signature_cache:
                return self._signature_cache[cache_key]
            
            # Start HTML signature structure (font size matches email body)
            html_signature = '<div style="font-family: Calibri, sans-serif; font-size: 16px; color: #333;">'
            
            # Add text content with proper line breaks
            signature_lines = signature_text.split('\n')
            for line in signature_lines:
                if line.strip():
                    html_signature += f'<div>{line}</div>'
                else:
                    html_signature += '<br/>'
            
            # Add image if provided
            if image_path:
                image_data = self.image_to_base64(image_path)
                if image_data:
                    base64_data, mime_type = image_data
                    # Use Content-ID for inline images
                    image_filename = Path(image_path).name
                    html_signature += f'''
<div style="margin-top: 10px;">
    <img src="cid:{image_filename}" alt="Signature" style="max-width: 200px; max-height: 200px; height: auto;"/>
</div>'''
            
            html_signature += '</div>'
            
            # Cache the result
            self._signature_cache[cache_key] = html_signature
            LogFire.log("DEBUG", f"Generated HTML signature with {'image' if image_path else 'text only'}")
            
            return html_signature
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to generate HTML signature: {e}", severity="error")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "generate_html_signature", None)
            # Return plain text fallback
            return f'<div style="font-family: Arial, sans-serif; font-size: 16px;">{signature_text.replace(chr(10), "<br/>")}</div>'
    
    async def get_default_signature(self, user_email: str = None) -> Tuple[Optional[str], Optional[str]]:
        """
        Get default signature for a user from OAuth tokens or config fallback
        Returns tuple of (signature_text, image_path)
        """
        try:
            # First try to get signature from OAuth SMTP configuration
            from endpoints.oauth._verifier_ import OAuth2Verifier
            
            smtp_token = await OAuth2Verifier.get_full_token("smtp")
            if smtp_token:
                # Get signature data from SMTP token 
                # Note: Based on form field order, signature data is stored in str1 (text) and str2 (image path)
                signature_text = smtp_token.get("str1")  # Signature text from OAuth form
                image_path = smtp_token.get("str2")      # Signature image path from OAuth form
                
                # Clean up image path (remove quotes if present)
                if image_path:
                    image_path = image_path.strip('"\'').strip()
                
                if signature_text or image_path:
                    LogFire.log("DEBUG", f"Using signature from SMTP OAuth configuration")
                    return signature_text, image_path
            
            # Fallback to config.py for backward compatibility
            import config
            
            # Check if user has H-G Sports domain or specific configuration
            if user_email and ('h-gskis.nl' in user_email or 'h-gsports.nl' in user_email):
                signature_text = getattr(config, 'HANDTEKENING_SIEP_HGSPORTS_MAIL', None)
                image_path = getattr(config, 'SIGNATURE_IMAGE_PATH', None)
                LogFire.log("DEBUG", f"Using H-G Sports config signature for {user_email}")
                return signature_text, image_path
            
            # Default signature fallback from config
            LogFire.log("DEBUG", f"Using default config signature")
            return getattr(config, 'DEFAULT_SIGNATURE_TEXT', None), getattr(config, 'DEFAULT_SIGNATURE_IMAGE_PATH', None)
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get default signature: {e}", severity="error")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "get_default_signature", None)
            return None, None
    
    async def get_user_signature(self, user) -> Tuple[Optional[str], Optional[str], bool]:
        """
        Get signature configuration for a specific user
        Returns tuple of (signature_text, image_path, include_signature)
        """
        try:
            # Check if user has custom signature settings
            if hasattr(user, 'signature_text') and user.signature_text:
                signature_text = user.signature_text
                image_path = getattr(user, 'signature_image_path', None)
                include_signature = getattr(user, 'use_signature', True)
                LogFire.log("DEBUG", f"Using custom signature for user {user.username}")
                return signature_text, image_path, include_signature
            
            # Fall back to default signature based on user email
            signature_text, image_path = await self.get_default_signature(user.email)
            include_signature = True  # Default to including signature
            
            LogFire.log("DEBUG", f"Using default signature for user {user.username}")
            return signature_text, image_path, include_signature
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get user signature: {e}", severity="error")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "get_user_signature", None)
            return None, None, False
    
    async def prepare_signature_for_email(self, user, include_signature: bool = True) -> Tuple[Optional[str], Optional[str], Optional[str]]:
        """
        Prepare signature data for email integration
        Returns tuple of (signature_text, signature_image_path, signature_html)
        """
        try:
            if not include_signature:
                return None, None, None
            
            signature_text, image_path, should_include = await self.get_user_signature(user)
            
            if not should_include or not signature_text:
                return None, None, None
            
            # Generate HTML signature
            html_signature = self.generate_html_signature(signature_text, image_path)
            
            LogFire.log("DEBUG", f"Prepared signature for user {user.username}: {'with image' if image_path else 'text only'}")
            return signature_text, image_path, html_signature
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to prepare signature for email: {e}", severity="error")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "prepare_signature_for_email", None)
            return None, None, None