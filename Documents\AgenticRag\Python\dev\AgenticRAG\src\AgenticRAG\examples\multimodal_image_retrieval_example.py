#!/usr/bin/env python3
"""
Example: Multimodal Image Retrieval with Base64 Encoding

This example demonstrates how to:
1. Search for images using the multimodal retrieval tool
2. Extract the base64 encoded image data from the response
3. Save the image to a file or display it in HTML

Usage:
    python multimodal_image_retrieval_example.py
"""

import asyncio
import sys
import os

# Add the parent directory to Python path
sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from tools.tool_multimodal_retrieval import (
    MultimodalRetrievalTool,
    extract_image_data_from_response,
    extract_image_paths_from_response,
    save_image_from_response
)

async def example_image_search():
    """Example of searching for images and retrieving the actual image files."""
    
    # Create the multimodal retrieval tool
    tool = MultimodalRetrievalTool()
    
    # Search for images about "koala"
    print("=== Searching for koala images ===")
    response = await tool._arun(
        query="koala",
        content_types=["image"],
        max_results=2,
        include_assets=True
    )
    
    print("Search Response:")
    print(response)
    print("\n" + "="*50 + "\n")
    
    # Extract image paths for Discord
    image_paths = extract_image_paths_from_response(response)
    if image_paths:
        print(f"SUCCESS: Found {len(image_paths)} image path(s) for Discord attachment:")
        for path in image_paths:
            print(f"  - {path}")
        print("\nThese images will be automatically attached when sent through Discord!")
    
    # Also check for base64 data (for other platforms)
    image_info = extract_image_data_from_response(response)
    
    if image_info['has_image']:
        print("SUCCESS: Image data found in response!")
        print(f"MIME Type: {image_info['mime_type']}")
        print(f"Data URL length: {len(image_info['data_url'])} characters")
        print(f"Base64 data length: {len(image_info['image_data'])} characters")
        
        # Save the image to a file
        output_path = "./retrieved_koala_image.png"
        success = save_image_from_response(response, output_path)
        
        if success:
            print(f"SUCCESS: Image saved to: {output_path}")
        else:
            print("ERROR: Failed to save image")
            
        # Show how to use in HTML
        print("\n=== HTML Usage Example ===")
        html_example = f'''
        <html>
        <body>
            <h1>Retrieved Koala Image</h1>
            <img src="{image_info['data_url']}" alt="Koala" style="max-width: 500px;"/>
            <p>This image was retrieved from the multimodal search system.</p>
        </body>
        </html>
        '''
        print("HTML code generated:")
        print(html_example)
        
        # Save HTML example
        with open("./koala_image_example.html", "w") as f:
            f.write(html_example)
        print("SUCCESS: HTML example saved to: koala_image_example.html")
        
    else:
        print("INFO: No image data found in response")
        print("This is expected when running outside the full application context")

async def example_table_search():
    """Example of searching for tables."""
    
    # Create the multimodal retrieval tool
    tool = MultimodalRetrievalTool()
    
    # Search for tables
    print("\n=== Searching for table data ===")
    response = await tool._arun(
        query="table data",
        content_types=["table"],
        max_results=2,
        include_assets=True
    )
    
    print("Table Search Response:")
    print(response)

async def main():
    """Main example function."""
    print("Multimodal Image Retrieval Example")
    print("=" * 40)
    
    try:
        # Run image search example
        await example_image_search()
        
        # Run table search example
        await example_table_search()
        
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(main())