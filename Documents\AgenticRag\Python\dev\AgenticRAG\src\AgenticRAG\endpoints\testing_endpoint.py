from imports import *

import asyncio
from typing import Optional, Any, List
from datetime import datetime
from endpoints.mybot_generic import MyBot_Generic
from managers.manager_logfire import LogFire

# Class-level broadcast tracking (shared across all instances) - outside Pydantic model
_shared_broadcast_history: List[dict] = []
_shared_last_broadcast_time: Optional[datetime] = None

class MyBot_Testing(MyBot_Generic):
    """Testing bot for automated responses during testing scenarios"""
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
    
    def __init__(self, parent_instance=None, name="Testing", **kwargs):
        super().__init__(parent_instance=parent_instance, name=name, **kwargs)
    
    @property
    def broadcast_history(self):
        """Access shared broadcast history"""
        return _shared_broadcast_history
    
    @property
    def last_broadcast_time(self):
        """Access shared last broadcast time"""
        return _shared_last_broadcast_time
    
    @last_broadcast_time.setter
    def last_broadcast_time(self, value):
        """Set shared last broadcast time"""
        global _shared_last_broadcast_time
        _shared_last_broadcast_time = value
    
    async def request_human_in_the_loop_internal(self, request:str, request_obj: "LongRunningZairaRequest", message, halt_until_response = False):
        """Override to provide automated testing responses"""
        if self.name == "Testing":
            # Generate test response
            response = await self._generate_test_response(request)
            
            # Print debug information
            LogFire.log("DEBUG", f"[TESTING BOT] Request: {request}")
            LogFire.log("DEBUG", f"[TESTING BOT] Response: {response}")
            LogFire.log("DEBUG", f"[TESTING BOT] Task GUID: {request_obj.scheduled_guid}")
            LogFire.log("DEBUG", f"[TESTING BOT] User GUID: {request_obj.user.session_guid}")
            # Testing request details already handled by LogFire.log above
            # Testing response details already handled by LogFire.log above
            # Testing GUID details already handled by LogFire.log above
            # Testing user GUID details already handled by LogFire.log above
            
            # Send response to user
            await request_obj.user.on_message(response, self, [], request_obj.original_physical_message)
            
            # Handle halt_until_response
            if halt_until_response:
                while request_obj.human_in_the_loop_callback is not None:
                    await asyncio.sleep(0.1)
        else:
            # For non-testing names, use parent implementation
            await super().request_human_in_the_loop_internal(request, request_obj, message, halt_until_response)
    
    async def _generate_test_response(self, request: str) -> str:
        """Generate automated test response using LLM if available, fallback otherwise"""
        try:
            # Try to use LLM for response generation
            from etc.ZairaSettings import ZairaSettings
            if hasattr(ZairaSettings, 'llm') and ZairaSettings.llm is not None:
                prompt = f"Provide a brief, helpful response to this request: {request}"
                response = await ZairaSettings.llm.ainvoke(prompt)
                return response.content if hasattr(response, 'content') else str(response)
        except Exception as e:
            LogFire.log("ERROR", f"[TESTING BOT] LLM generation failed: {e}", severity="error")
            # Testing LLM failure already handled by LogFire.log above
        
        # Fallback to pattern-based responses
        return self._get_fallback_response(request)
    
    def _get_fallback_response(self, request: str) -> str:
        """Generate fallback response based on request patterns"""
        request_lower = request.lower()
        
        # Email address patterns (highest priority)
        email_patterns = [
            "email adres", "address", "mailadres", "e-mailadres", 
            "welk email", "naar welk", "what email", "van welk"
        ]
        if any(pattern in request_lower for pattern in email_patterns):
            return "<EMAIL>"
        
        # Sender patterns (second priority)
        sender_patterns = [
            "sender", "verzender", "verzonden door"
        ]
        if any(pattern in request_lower for pattern in sender_patterns):
            return "<EMAIL>"
        
        # Email approval patterns (third priority)
        approval_patterns = [
            "goedkeur", "approve", "bevestigen", "verzending"
        ]
        if any(pattern in request_lower for pattern in approval_patterns):
            return "j"
        
        # Yes/No patterns (lowest priority)
        yes_no_patterns = [
            "ja of nee", "yes or no", "y/n"
        ]
        if any(pattern in request_lower for pattern in yes_no_patterns):
            return "ja"
        
        # Default response
        return "ja"
    
    async def send_broadcast(self, text):
        """Override parent method to capture broadcasts for testing"""
        # Call parent implementation first
        await super().send_broadcast(text)
        
        # Capture broadcast for test verification
        broadcast_entry = {
            "text": text,
            "timestamp": datetime.now(),
            "truncated_text": text[:200]
        }
        self.broadcast_history.append(broadcast_entry)
        self.last_broadcast_time = broadcast_entry["timestamp"]
        
        LogFire.log("DEBUG", f"[TESTING BOT] Broadcast captured for testing: {text[:100]}...")
    
    def get_broadcasts_since(self, since_time: datetime) -> List[dict]:
        """Get all broadcasts received since specified time"""
        return [b for b in self.broadcast_history if b["timestamp"] >= since_time]
    
    def has_broadcast_since(self, since_time: datetime) -> bool:
        """Check if any broadcast received since specified time"""
        return len(self.get_broadcasts_since(since_time)) > 0
    
    def clear_broadcast_history(self):
        """Clear broadcast history (useful for test setup)"""
        global _shared_broadcast_history, _shared_last_broadcast_time
        _shared_broadcast_history.clear()
        _shared_last_broadcast_time = None
        LogFire.log("DEBUG", "[TESTING BOT] Broadcast history cleared")
    
    def get_broadcast_count(self) -> int:
        """Get total number of broadcasts received"""
        return len(self.broadcast_history)
    
    def get_latest_broadcast(self) -> Optional[dict]:
        """Get the most recent broadcast, if any"""
        return self.broadcast_history[-1] if self.broadcast_history else None