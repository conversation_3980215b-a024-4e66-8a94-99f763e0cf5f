from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

import pytest
from pydantic import ValidationError, BaseModel, Field
from unittest.mock import Mock, patch, MagicMock
from tests.unit.test_logging_capture import with_unit_test_logging
import asyncio

class TestSignatureManager:
    @with_unit_test_logging
    def test_signature_manager_singleton(self):
        """Test that SignatureManager is a proper singleton"""
        from managers.manager_signature import SignatureManager
        
        manager1 = SignatureManager.get_instance()
        manager2 = SignatureManager.get_instance()
        
        assert manager1 is manager2
        assert isinstance(manager1, SignatureManager)
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_signature_manager_initialization(self):
        """Test SignatureManager initialization"""
        from managers.manager_signature import SignatureManager
        
        manager = SignatureManager.get_instance()
        await manager.setup()
        
        assert manager._initialized is True
        assert hasattr(manager, '_signature_cache')
    
    @with_unit_test_logging
    def test_get_default_signature_hg_sports(self):
        """Test getting default signature for H-G Sports email"""
        from managers.manager_signature import SignatureManager
        
        manager = SignatureManager.get_instance()
        signature_text, image_path = manager.get_default_signature("<EMAIL>")
        
        assert signature_text is not None
        assert "Siep van de Reijt" in signature_text
        assert "H-G Sports" in signature_text
        assert image_path is not None
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_generate_html_signature_text_only(self):
        """Test HTML signature generation with text only"""
        from managers.manager_signature import SignatureManager
        
        manager = SignatureManager.get_instance()
        await manager.setup()  # Initialize the manager first
        signature_text = "Best regards,\n\nJohn Doe\nCompany Name"
        
        html_signature = manager.generate_html_signature(signature_text)
        
        assert 'div style="font-family: Arial' in html_signature
        assert "Best regards," in html_signature
        assert "John Doe" in html_signature
        assert "Company Name" in html_signature
    
    @with_unit_test_logging 
    @patch('os.path.exists')
    @patch('builtins.open')
    @patch('base64.b64encode')
    def test_image_to_base64_success(self, mock_b64encode, mock_open, mock_exists):
        """Test successful image to base64 conversion"""
        from managers.manager_signature import SignatureManager
        
        # Mock file operations
        mock_exists.return_value = True
        mock_file_data = b"fake_image_data"
        mock_open.return_value.__enter__.return_value.read.return_value = mock_file_data
        mock_b64encode.return_value = b"encoded_data"
        
        manager = SignatureManager.get_instance()
        result = manager.image_to_base64("/path/to/image.png")
        
        assert result is not None
        base64_data, mime_type = result
        assert base64_data == "encoded_data"
        assert mime_type == "image/png"
    
    @with_unit_test_logging
    @patch('os.path.exists')
    def test_image_to_base64_file_not_found(self, mock_exists):
        """Test image to base64 conversion when file not found"""
        from managers.manager_signature import SignatureManager
        
        mock_exists.return_value = False
        
        manager = SignatureManager.get_instance()
        result = manager.image_to_base64("/path/to/nonexistent.png")
        
        assert result is None


class TestEmailProcessingDataSignature:
    @with_unit_test_logging
    def test_email_processing_data_with_signature(self):
        """Test EmailProcessingData creation with signature fields"""
        from tasks.data.email_processing_data import EmailProcessingData
        
        email_data = EmailProcessingData(
            subject="Test Subject",
            content="Test content",
            sender="<EMAIL>",
            recipient="<EMAIL>",
            include_signature=True,
            signature_text="Best regards,\nJohn Doe",
            signature_image_path="/path/to/signature.png",
            signature_html="<div>HTML signature</div>"
        )
        
        assert email_data.include_signature is True
        assert email_data.signature_text == "Best regards,\nJohn Doe"
        assert email_data.signature_image_path == "/path/to/signature.png"
        assert email_data.signature_html == "<div>HTML signature</div>"
    
    @with_unit_test_logging
    def test_email_preview_with_signature(self):
        """Test email preview includes signature information"""
        from tasks.data.email_processing_data import EmailProcessingData
        
        email_data = EmailProcessingData(
            subject="Test Subject",
            content="Test email content",
            sender="<EMAIL>",
            recipient="<EMAIL>",
            include_signature=True,
            signature_text="Best regards,\nJohn Doe",
            signature_image_path="/path/to/signature.png"
        )
        
        preview = email_data.get_preview_text()
        
        assert "Test email content" in preview
        assert "Best regards," in preview
        assert "John Doe" in preview
        assert "[Image: signature.png]" in preview
    
    @with_unit_test_logging
    def test_email_preview_without_signature(self):
        """Test email preview when signature is disabled"""
        from tasks.data.email_processing_data import EmailProcessingData
        
        email_data = EmailProcessingData(
            subject="Test Subject",
            content="Test email content",
            sender="<EMAIL>",
            recipient="<EMAIL>",
            include_signature=False
        )
        
        preview = email_data.get_preview_text()
        
        assert "Test email content" in preview
        assert "Best regards," not in preview
        assert "[Image:" not in preview


class TestZairaUserSignatureFields:
    @with_unit_test_logging 
    def test_zaira_user_signature_fields_structure(self):
        """Test signature fields structure using pydantic directly"""
        from pydantic import BaseModel, Field
        
        # Test the signature fields structure without importing ZairaUser
        class MockUserWithSignature(BaseModel):
            use_signature: bool = True
            signature_text: str = ""
            signature_image_path: str = ""
        
        user = MockUserWithSignature()
        
        assert hasattr(user, 'use_signature')
        assert hasattr(user, 'signature_text') 
        assert hasattr(user, 'signature_image_path')
        
        assert user.use_signature is True
        assert user.signature_text == ""
        assert user.signature_image_path == ""
    
    @with_unit_test_logging
    def test_custom_signature_assignment(self):
        """Test custom signature assignment"""
        from pydantic import BaseModel
        
        class MockUserWithSignature(BaseModel):
            use_signature: bool = True
            signature_text: str = ""
            signature_image_path: str = ""
        
        user = MockUserWithSignature()
        
        # Set custom signature
        user.use_signature = True
        user.signature_text = "Custom signature text"
        user.signature_image_path = "/path/to/custom/signature.png"
        
        assert user.use_signature is True
        assert user.signature_text == "Custom signature text"
        assert user.signature_image_path == "/path/to/custom/signature.png"


class TestSignatureIntegration:
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_signature_manager_get_user_signature(self):
        """Test getting signature for a user with custom settings"""
        # Create simple mock user to avoid import issues
        mock_user = Mock()
        mock_user.username = "testuser"
        mock_user.signature_text = "Custom signature"
        mock_user.signature_image_path = "/path/to/signature.png"
        mock_user.use_signature = True
        
        from managers.manager_signature import SignatureManager
        manager = SignatureManager.get_instance()
        signature_text, image_path, include_signature = manager.get_user_signature(mock_user)
        
        assert signature_text == "Custom signature"
        assert image_path == "/path/to/signature.png"
        assert include_signature is True
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_signature_manager_prepare_signature_for_email(self):
        """Test preparing signature for email integration"""
        # Create simple mock user to avoid import issues
        mock_user = Mock()
        mock_user.username = "testuser"
        mock_user.signature_text = "Test signature"
        mock_user.use_signature = True
        
        from managers.manager_signature import SignatureManager
        manager = SignatureManager.get_instance()
        signature_text, image_path, signature_html = manager.prepare_signature_for_email(mock_user, True)
        
        assert signature_text == "Test signature"
        assert signature_html is not None
        assert "Test signature" in signature_html
        assert 'div style="font-family: Arial' in signature_html