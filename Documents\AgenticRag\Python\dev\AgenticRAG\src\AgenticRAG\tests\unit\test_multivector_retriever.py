from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

from imports import *
import pytest
import asyncio
import tempfile
from datetime import datetime
from unittest.mock import patch, MagicMock, AsyncMock

# Import without triggering the ONNX issue
from managers.manager_document_store import DocumentStoreManager, DocumentMetadata

class TestDocumentStoreManager:
    """Test document store manager functionality."""
    
    def test_document_metadata_serialization(self):
        """Test DocumentMetadata serialization and deserialization."""
        metadata = DocumentMetadata(
            doc_id="test-doc-123",
            content_type="text",
            element_index=0,
            file_path="/test/file.pdf",
            file_name="file.pdf",
            created_at=datetime.now(),
            summary="Test summary",
            asset_path="/test/asset.png",
            parent_doc_id="parent-123"
        )
        
        # Test serialization
        data_dict = metadata.to_dict()
        assert data_dict["doc_id"] == "test-doc-123"
        assert data_dict["content_type"] == "text"
        assert isinstance(data_dict["created_at"], str)
        
        # Test deserialization
        restored_metadata = DocumentMetadata.from_dict(data_dict)
        assert restored_metadata.doc_id == metadata.doc_id
        assert restored_metadata.content_type == metadata.content_type
        assert isinstance(restored_metadata.created_at, datetime)
    
    def test_document_store_manager_singleton(self):
        """Test that DocumentStoreManager follows singleton pattern."""
        manager1 = DocumentStoreManager.get_instance()
        manager2 = DocumentStoreManager.get_instance()
        assert manager1 is manager2
    
    @pytest.mark.asyncio
    async def test_document_store_setup(self):
        """Test DocumentStoreManager setup with mocked database."""
        with patch('managers.manager_postgreSQL.PostgreSQLManager') as mock_pg:
            mock_conn = AsyncMock()
            mock_pg.get_instance.return_value.get_connection.return_value.__aenter__.return_value = mock_conn
            
            manager = DocumentStoreManager.get_instance()
            await manager.setup()
            
            # Verify database setup was called
            assert mock_conn.execute.called
            assert manager._initialized is True

class TestMultiVectorRetrieverLogic:
    """Test multi-vector retriever logic without database dependencies."""
    
    def test_document_id_generation(self):
        """Test document ID generation for multi-vector retriever."""
        import uuid
        
        # Test that UUIDs are generated correctly
        doc_ids = [str(uuid.uuid4()) for _ in range(5)]
        
        # All IDs should be unique
        assert len(set(doc_ids)) == 5
        
        # All IDs should be valid UUID format
        for doc_id in doc_ids:
            assert len(doc_id) == 36  # UUID string length
            assert doc_id.count('-') == 4  # UUID has 4 hyphens
    
    def test_content_type_validation(self):
        """Test content type validation for multi-vector retriever."""
        valid_types = ["text", "image", "table", "text_summary", "image_summary", "table_summary"]
        
        for content_type in valid_types:
            assert content_type in ["text", "image", "table"] or content_type.endswith("_summary")
    
    def test_metadata_structure(self):
        """Test metadata structure consistency."""
        expected_fields = {
            "doc_id", "content_type", "file_path", "file_name", 
            "parent_doc_id", "element_index", "retriever_type", "has_parent_document"
        }
        
        # Mock metadata structure
        metadata = {
            "doc_id": "test-123",
            "content_type": "text_summary",
            "file_path": "/test/file.pdf",
            "file_name": "file.pdf",
            "parent_doc_id": "parent-123",
            "element_index": 0,
            "retriever_type": "multi_vector",
            "has_parent_document": True
        }
        
        # Check that all expected fields are present
        for field in expected_fields:
            assert field in metadata
    
    @pytest.mark.asyncio
    async def test_content_preparation_logic(self):
        """Test content preparation logic for multi-vector storage."""
        # Mock multimodal data
        multimodal_data = {
            "text_elements": [
                {"text": "First paragraph", "type": "text"},
                {"text": "Second paragraph", "type": "text"}
            ],
            "images": [
                {"text": "Image description", "summary": "AI-generated image summary", "asset_path": "/test/image.png"}
            ],
            "tables": [
                {"markdown": "| Col1 | Col2 |\n|------|------|\n| A | B |", "summary": "Table summary"}
            ]
        }
        
        # Test content extraction
        texts = [elem.get("text", "") for elem in multimodal_data.get("text_elements", [])]
        assert len(texts) == 2
        assert texts[0] == "First paragraph"
        
        images = [elem.get("text", "") for elem in multimodal_data.get("images", [])]
        image_summaries = [elem.get("summary", "") for elem in multimodal_data.get("images", [])]
        assert len(images) == 1
        assert len(image_summaries) == 1
        assert image_summaries[0] == "AI-generated image summary"
        
        tables = [elem.get("markdown", "") for elem in multimodal_data.get("tables", [])]
        table_summaries = [elem.get("summary", "") for elem in multimodal_data.get("tables", [])]
        assert len(tables) == 1
        assert len(table_summaries) == 1
        assert "| Col1 | Col2 |" in tables[0]
    
    def test_search_result_formatting(self):
        """Test search result formatting logic."""
        # Mock search results
        search_results = [
            {
                "summary": "This is a test summary",
                "summary_score": 0.95,
                "metadata": {
                    "doc_id": "test-123",
                    "content_type": "text_summary",
                    "file_name": "test.pdf",
                    "element_index": 0
                },
                "parent_document": "This is the full original text content",
                "parent_metadata": MagicMock(asset_path=None)
            }
        ]
        
        # Test formatting logic
        result = search_results[0]
        assert result["summary"] == "This is a test summary"
        assert result["summary_score"] == 0.95
        assert result["metadata"]["content_type"] == "text_summary"
        assert result["parent_document"] == "This is the full original text content"
    
    def test_content_type_filtering(self):
        """Test content type filtering logic."""
        # Mock search results with different content types
        search_results = [
            {"metadata": {"content_type": "text_summary"}},
            {"metadata": {"content_type": "image_summary"}},
            {"metadata": {"content_type": "table_summary"}},
            {"metadata": {"content_type": "other_type"}}
        ]
        
        # Filter by content types
        content_types = ["text", "image"]
        
        filtered_results = []
        for result in search_results:
            metadata = result.get("metadata", {})
            content_type = metadata.get("content_type", "")
            
            if any(ct in content_type for ct in content_types):
                filtered_results.append(result)
        
        # Should include text_summary and image_summary, but not table_summary or other_type
        assert len(filtered_results) == 2
        assert filtered_results[0]["metadata"]["content_type"] == "text_summary"
        assert filtered_results[1]["metadata"]["content_type"] == "image_summary"
    
    def test_batch_processing_logic(self):
        """Test batch processing logic for multiple documents."""
        # Mock batch data
        texts = ["Text 1", "Text 2", "Text 3"]
        summaries = ["Summary 1", "Summary 2", "Summary 3"]
        
        # Test batch preparation
        assert len(texts) == len(summaries)
        
        # Mock document preparation
        documents_to_store = []
        for i, (text, summary) in enumerate(zip(texts, summaries)):
            doc_id = f"doc_{i}"
            documents_to_store.append((doc_id, text, summary))
        
        assert len(documents_to_store) == 3
        assert documents_to_store[0] == ("doc_0", "Text 1", "Summary 1")
        assert documents_to_store[2] == ("doc_2", "Text 3", "Summary 3")

class TestIntegrationScenarios:
    """Test integration scenarios without external dependencies."""
    
    def test_document_workflow_structure(self):
        """Test the overall document processing workflow structure."""
        # Mock workflow steps
        workflow_steps = [
            "extract_multimodal_content",
            "store_in_document_store",
            "store_summaries_in_vector_db",
            "create_search_index",
            "enable_retrieval"
        ]
        
        # Test workflow completeness
        assert "extract_multimodal_content" in workflow_steps
        assert "store_in_document_store" in workflow_steps
        assert "store_summaries_in_vector_db" in workflow_steps
        assert "enable_retrieval" in workflow_steps
    
    def test_error_handling_structure(self):
        """Test error handling structure."""
        # Mock error scenarios
        error_scenarios = [
            {"type": "database_connection_error", "handled": True},
            {"type": "invalid_document_format", "handled": True},
            {"type": "vector_store_error", "handled": True},
            {"type": "storage_quota_exceeded", "handled": True}
        ]
        
        # Test that all error scenarios are accounted for
        for scenario in error_scenarios:
            assert scenario["handled"] is True
    
    def test_performance_considerations(self):
        """Test performance-related considerations."""
        # Mock performance metrics
        performance_metrics = {
            "batch_size": 100,
            "max_document_size": 10 * 1024 * 1024,  # 10MB
            "max_summary_length": 500,
            "connection_pool_size": 20
        }
        
        # Test reasonable limits
        assert performance_metrics["batch_size"] > 0
        assert performance_metrics["max_document_size"] > 0
        assert performance_metrics["max_summary_length"] > 0
        assert performance_metrics["connection_pool_size"] > 0

if __name__ == "__main__":
    pytest.main([__file__, "-v"])