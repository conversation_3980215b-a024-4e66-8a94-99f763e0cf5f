[run]
source = .
omit = 
    */tests/*
    */venv/*
    */env/*
    */__pycache__/*
    */htmlcov/*
    */logs/*
    */assets/*
    */bge_onnx/*
    */ui/*
    */src/meltano/*
    */scripts/*
    capture_real_call_trace.py
    dev_run.py
    verify_coverage.py
    simple_broadcast_test.py
    yuki_extractor.py
    run_etc_tests.py
    */Context Engineering/*
    */AUTOMATED_TESTING_SOLUTION.md
    */CLAUDE.md
    */CLAUDE2.md
    */STALE_TESTS_ANALYSIS.md
    */WEDNESDAY_MANUAL_TESTING.md
    */README*
    */LICENSE*
    */requirements.txt
    */Dockerfile*
    */Docker_build*
    */test_real/*
    */manual/*
    */performance/*
    */stress/*
    */health/*
    

[report]
exclude_lines =
    pragma: no cover
    def __repr__
    if self.debug:
    if settings.DEBUG
    raise AssertionError
    raise NotImplementedError
    if 0:
    if __name__ == .__main__.:
    class .*\bProtocol\):
    @(abc\.)?abstractmethod
    except ImportError:
    except Exception as e:
    logger.debug
    logger.info
    logger.warning
    logger.error
    logger.critical

[html]
directory = htmlcov
title = AgenticRAG Test Coverage Report

[xml]
output = coverage.xml

[paths]
source = 
    .
    */AgenticRAG/src/AgenticRAG/