from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from endpoints.slack_endpoint import MySlackBot

class TestMySlackBot:
    """Test suite for MySlackBot"""
    
    def setup_method(self):
        """Setup test fixtures"""
        # Reset singleton instance
        MySlackBot._instance = None
        self.bot = MySlackBot()
        
    def test_singleton_pattern(self):
        """Test that MySlackBot follows singleton pattern"""
        bot1 = MySlackBot()
        bot2 = MySlackBot()
        bot3 = MySlackBot.get_instance()
        
        assert bot1 is bot2
        assert bot2 is bot3
        assert bot1._instance is not None
        
    def test_initialization(self):
        """Test MySlackBot initialization"""
        bot = MySlackBot()
        
        assert hasattr(bot, 'initialized')
        assert bot.initialized == True
        assert bot.client is None
        assert bot.app is None
        assert bot.asyncio_Task is None
        assert bot.bot_generic is None
        
    @pytest.mark.asyncio
    async def test_setup_debug_mode(self):
        """Test setup method in debug mode"""
        with patch('endpoints.slack_endpoint.Globals.is_debug', return_value=True):
            await MySlackBot.setup()
            
            # Should not create client in debug mode
            instance = MySlackBot.get_instance()
            assert instance.client is None
            assert instance.app is None
            
    @pytest.mark.asyncio
    async def test_setup_no_token(self):
        """Test setup method with no Slack token"""
        with patch('endpoints.slack_endpoint.Globals.is_debug', return_value=False), \
             patch('endpoints.oauth._verifier_.OAuth2Verifier.get_token', return_value=''):
            
            await MySlackBot.setup()
            
            instance = MySlackBot.get_instance()
            assert instance.client is None
            assert instance.app is None
            
    @pytest.mark.asyncio
    async def test_setup_with_token(self):
        """Test setup method with valid Slack token"""
        with patch('endpoints.slack_endpoint.Globals.is_debug', return_value=False), \
             patch('endpoints.oauth._verifier_.OAuth2Verifier.get_token', return_value='xoxb-test-token'), \
             patch('endpoints.mybot_generic.MyBot_Generic') as mock_bot_generic:
            
            mock_bot_generic.return_value = MagicMock()
            
            await MySlackBot.setup()
            
            instance = MySlackBot.get_instance()
            assert instance.bot_generic is not None
            # Client and app creation would be tested if we had the actual slack_sdk
            
    @pytest.mark.asyncio
    async def test_late_setup_debug_mode(self):
        """Test late_setup method in debug mode"""
        with patch('endpoints.slack_endpoint.Globals.is_debug', return_value=True):
            await MySlackBot.late_setup()
            
            # Should not start client in debug mode
            instance = MySlackBot.get_instance()
            assert instance.client is None
            
    @pytest.mark.asyncio
    async def test_late_setup_no_client(self):
        """Test late_setup method with no client"""
        with patch('endpoints.slack_endpoint.Globals.is_debug', return_value=False):
            instance = MySlackBot.get_instance()
            instance.client = None
            
            await MySlackBot.late_setup()
            
            # Should not crash when client is None
            assert instance.client is None
            
    @pytest.mark.asyncio
    async def test_late_setup_with_client(self):
        """Test late_setup method with client"""
        with patch('endpoints.slack_endpoint.Globals.is_debug', return_value=False):
            instance = MySlackBot.get_instance()
            
            # Mock the client
            mock_client = MagicMock()
            mock_client.connect = AsyncMock()
            instance.client = mock_client
            
            await MySlackBot.late_setup()
            
            mock_client.connect.assert_called_once()
            
    def test_respond_method(self):
        """Test respond method"""
        mock_event = {
            'type': 'app_mention',
            'text': 'Hello bot',
            'user': 'U12345',
            'channel': 'C12345'
        }
        
        # The respond method is currently just a placeholder
        # Test that it doesn't crash
        try:
            self.bot.respond(mock_event)
        except Exception as e:
            pytest.fail(f"respond method raised an exception: {e}")
            
    @pytest.mark.asyncio
    async def test_get_instance_method(self):
        """Test get_instance class method"""
        instance1 = MySlackBot.get_instance()
        instance2 = MySlackBot.get_instance()
        
        assert instance1 is instance2
        assert isinstance(instance1, MySlackBot)
        
    def test_multiple_instantiation(self):
        """Test that multiple instantiation returns same instance"""
        bot1 = MySlackBot()
        bot2 = MySlackBot()
        bot3 = MySlackBot()
        
        assert bot1 is bot2
        assert bot2 is bot3
        assert bot1.initialized == True
        
class TestMySlackBotEdgeCases:
    """Test edge cases for MySlackBot"""
    
    def setup_method(self):
        """Setup test fixtures"""
        MySlackBot._instance = None
        self.bot = MySlackBot()
        
    @pytest.mark.asyncio
    async def test_exception_handling_in_setup(self):
        """Test exception handling in setup method"""
        with patch('endpoints.slack_endpoint.Globals.is_debug', return_value=False), \
             patch('endpoints.oauth._verifier_.OAuth2Verifier.get_token', side_effect=Exception("Token error")), \
             patch('etc.helper_functions.exception_triggered') as mock_exception:
            
            await MySlackBot.setup()
            
            # Should handle exception gracefully
            mock_exception.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_exception_handling_in_late_setup(self):
        """Test exception handling in late_setup method"""
        with patch('endpoints.slack_endpoint.Globals.is_debug', return_value=False):
            instance = MySlackBot.get_instance()
            
            # Mock client that raises exception
            mock_client = MagicMock()
            mock_client.connect = AsyncMock(side_effect=Exception("Connection error"))
            instance.client = mock_client
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                await MySlackBot.late_setup()
                
                # Should handle exception gracefully
                mock_exception.assert_called_once()
                
    def test_respond_with_different_event_types(self):
        """Test respond method with different event types"""
        event_types = [
            {'type': 'message', 'text': 'Hello'},
            {'type': 'app_mention', 'text': '<@U12345> help'},
            {'type': 'reaction_added', 'reaction': 'thumbsup'},
            {'type': 'unknown_event', 'data': 'test'}
        ]
        
        for event in event_types:
            try:
                self.bot.respond(event)
            except Exception as e:
                pytest.fail(f"respond method failed for event {event['type']}: {e}")
                
    @pytest.mark.asyncio
    async def test_client_lifecycle(self):
        """Test client creation and cleanup lifecycle"""
        with patch('endpoints.slack_endpoint.Globals.is_debug', return_value=False), \
             patch('endpoints.oauth._verifier_.OAuth2Verifier.get_token', return_value='xoxb-test-token'):
            
            # Setup should create client
            await MySlackBot.setup()
            instance = MySlackBot.get_instance()
            
            # Late setup should start client
            if instance.client:
                mock_client = MagicMock()
                mock_client.connect = AsyncMock()
                instance.client = mock_client
                
                await MySlackBot.late_setup()
                mock_client.connect.assert_called_once()
                
    def test_bot_generic_integration(self):
        """Test integration with MyBot_Generic"""
        with patch('endpoints.slack_endpoint.Globals.is_debug', return_value=False), \
             patch('endpoints.oauth._verifier_.OAuth2Verifier.get_token', return_value='xoxb-test-token'), \
             patch('endpoints.mybot_generic.MyBot_Generic') as mock_bot_generic:
            
            mock_bot_instance = MagicMock()
            mock_bot_generic.return_value = mock_bot_instance
            
            # This would be called in setup
            bot_generic = mock_bot_generic(self.bot, "Slack")
            
            mock_bot_generic.assert_called_once_with(self.bot, "Slack")
            
    @pytest.mark.asyncio
    async def test_concurrent_setup_calls(self):
        """Test that concurrent setup calls don't cause issues"""
        with patch('endpoints.slack_endpoint.Globals.is_debug', return_value=False), \
             patch('endpoints.oauth._verifier_.OAuth2Verifier.get_token', return_value='xoxb-test-token'):
            
            # Simulate concurrent setup calls
            await asyncio.gather(
                MySlackBot.setup(),
                MySlackBot.setup(),
                MySlackBot.setup()
            )
            
            # Should still maintain singleton
            instance1 = MySlackBot.get_instance()
            instance2 = MySlackBot.get_instance()
            assert instance1 is instance2
            
class TestMySlackBotIntegration:
    """Test integration aspects of MySlackBot"""
    
    def setup_method(self):
        """Setup test fixtures"""
        MySlackBot._instance = None
        
    @pytest.mark.asyncio
    async def test_full_initialization_flow(self):
        """Test complete initialization flow"""
        with patch('endpoints.slack_endpoint.Globals.is_debug', return_value=False), \
             patch('endpoints.oauth._verifier_.OAuth2Verifier.get_token', return_value='xoxb-test-token'), \
             patch('endpoints.mybot_generic.MyBot_Generic') as mock_bot_generic:
            
            mock_bot_instance = MagicMock()
            mock_bot_generic.return_value = mock_bot_instance
            
            # Step 1: Setup
            await MySlackBot.setup()
            instance = MySlackBot.get_instance()
            
            # Verify setup completed
            assert instance.initialized == True
            assert instance.bot_generic is not None
            
            # Step 2: Late setup
            if instance.client:
                mock_client = MagicMock()
                mock_client.connect = AsyncMock()
                instance.client = mock_client
                
                await MySlackBot.late_setup()
                mock_client.connect.assert_called_once()
                
    @pytest.mark.asyncio
    async def test_token_validation_flow(self):
        """Test token validation during setup"""
        # Test with invalid token format
        with patch('endpoints.slack_endpoint.Globals.is_debug', return_value=False), \
             patch('endpoints.oauth._verifier_.OAuth2Verifier.get_token', return_value='invalid-token'):
            
            await MySlackBot.setup()
            instance = MySlackBot.get_instance()
            
            # Should still create instance but not client
            assert instance is not None
            
        # Test with valid token format
        with patch('endpoints.oauth._verifier_.OAuth2Verifier.get_token', return_value='xoxb-valid-token'):
            await MySlackBot.setup()
            instance = MySlackBot.get_instance()
            
            # Should create bot_generic
            assert instance.bot_generic is not None
            
    def test_class_attributes(self):
        """Test class attributes are properly set"""
        bot = MySlackBot()
        
        # Check that all expected attributes exist
        expected_attributes = [
            'initialized', 'client', 'app', 'asyncio_Task', 'bot_generic', '_instance'
        ]
        
        for attr in expected_attributes:
            assert hasattr(bot, attr), f"Missing attribute: {attr}"
            
    @pytest.mark.asyncio
    async def test_cleanup_on_error(self):
        """Test proper cleanup when errors occur"""
        with patch('endpoints.slack_endpoint.Globals.is_debug', return_value=False), \
             patch('endpoints.oauth._verifier_.OAuth2Verifier.get_token', return_value='xoxb-test-token'):
            
            instance = MySlackBot.get_instance()
            
            # Simulate error in client creation
            mock_client = MagicMock()
            mock_client.connect = AsyncMock(side_effect=Exception("Connection failed"))
            instance.client = mock_client
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                await MySlackBot.late_setup()
                
                # Should handle error gracefully
                mock_exception.assert_called_once()
                
                # Instance should still be valid
                assert instance.initialized == True
