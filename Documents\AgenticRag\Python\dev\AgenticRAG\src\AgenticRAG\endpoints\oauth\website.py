from imports import *

from aiohttp import web

from endpoints.oauth._verifier_ import OAuth2App
from managers.manager_logfire import LogFire

class OAuth2Website(OAuth2App):
    def setup(self, myname):
        super().setup(myname)
        self.create_input("input", ["str:Site URL", "str:Sitemap (Optional)"])

    async def on_success_return(self, request: web.Request) -> str:
        ret_html = await super().on_success_return(request)
        ret_html += "Website wordt opgehaald. Een ogenblik geduld."
        return ret_html

    async def on_success_execute(self, request: web.Request) -> str:
        from endpoints.oauth._verifier_ import OAuth2Verifier
        ret_html = await super().on_success_execute(request)

        # Crawl the user's website in case it changed
        site_url = await OAuth2Verifier.get_token("website")
        if site_url != "":
            from subprocess import run as subprocess_run
            #result = subprocess_run('cmd /c playwright install chromium', shell=True, capture_output=True, text=True)

            from inputs.crawler import Crawler
            LogFire.log("INIT", "Starting site-crawl")
            LogFire.log("DEBUG", "Starting site-crawl", severity="debug")  # Console output for visibility
            # Initialize the crawler
            crawler = await Crawler.setup_async()
            sitemap = await OAuth2Verifier.get_token("website", "refresh_token")
            if sitemap == "":
                # Test with a single URL
                await crawler.crawl(site_url, is_sitemap=False)
            else:
                # Test with a sitemap
                sitemap_url = f"{site_url.rstrip('/')}/{sitemap}"
                await crawler.crawl(sitemap_url, is_sitemap=True, max_concurrent=5)
            # Clean up
            await crawler.close_crawler()
            LogFire.log("EVENT", "Finished site-crawl")
            LogFire.log("DEBUG", "Finished site-crawl", severity="debug")  # Console output for visibility
            ret_html += "Website is succesvol opgehaald."
        else:
            ret_html += "Geen website data gevonden."

        return ret_html
    
    async def on_success_execute_fail(self, request: web.Request) -> str:
        # Mits success_execute, wordt getoond als on_success_execute faalt
        ret_html = await super().on_success_execute(request)
        ret_html += ""
        return ret_html
