from imports import *
from typing import Dict, Optional, List, Tuple, Any
from datetime import datetime, timezone, timedelta
import asyncio
import threading
from collections import defaultdict, deque

from ..utils.config import RateLimitConfig, ScheduledRequestsConfig
from ..utils.exceptions import UserRateLimitExceededError
from ..utils.helpers import TimeWindow, ThreadSafeCounter
from userprofiles.ZairaUser import PERMISSION_LEVELS

class UserRateLimiter:
    """Per-user rate limiting with sliding window and burst protection"""
    
    def __init__(self, user_guid: str, rate_config: RateLimitConfig):
        self.user_guid = user_guid
        self.rate_config = rate_config
        
        # Sliding windows for different time periods
        self._minute_window = TimeWindow(60)  # 1 minute
        self._hour_window = TimeWindow(3600)  # 1 hour
        self._daily_window = TimeWindow(86400)  # 24 hours
        
        # Burst protection
        self._burst_window = TimeWindow(10)  # 10 seconds
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Metrics
        self._total_requests = ThreadSafeCounter()
        self._rejected_requests = ThreadSafeCounter()
        self._last_request_time = 0.0
        
        LogFire.log("INIT", f"UserRateLimiter created for user {user_guid}")
    
    async def check_rate_limit(self, operation_type: str = "default") -> Tuple[bool, Optional[float]]:
        """
        Check if request should be allowed based on rate limits
        
        Args:
            operation_type: Type of operation for differentiated limiting
            
        Returns:
            Tuple[bool, Optional[float]]: (allowed, retry_after_seconds)
        """
        with self._lock:
            current_time = datetime.now(timezone.utc).timestamp()
            self._last_request_time = current_time
            
            # Check burst protection (10 seconds window)
            burst_count = self._burst_window.add_request(current_time)
            if burst_count > self.rate_config.burst_limit:
                self._rejected_requests.increment()
                LogFire.log("WARNING", f"Burst limit exceeded for user {self.user_guid}: {burst_count}/{self.rate_config.burst_limit}")
                return False, 10.0  # Retry after 10 seconds
            
            # Check per-minute limit
            minute_count = self._minute_window.add_request(current_time)
            if minute_count > self.rate_config.requests_per_minute:
                self._rejected_requests.increment()
                LogFire.log("WARNING", f"Per-minute limit exceeded for user {self.user_guid}: {minute_count}/{self.rate_config.requests_per_minute}")
                return False, 60.0  # Retry after 1 minute
            
            # Check per-hour limit
            hour_count = self._hour_window.add_request(current_time)
            if hour_count > self.rate_config.requests_per_hour:
                self._rejected_requests.increment()
                LogFire.log("WARNING", f"Per-hour limit exceeded for user {self.user_guid}: {hour_count}/{self.rate_config.requests_per_hour}")
                return False, 3600.0  # Retry after 1 hour
            
            # Request allowed
            self._total_requests.increment()
            return True, None
    
    def get_current_usage(self) -> Dict[str, int]:
        """Get current rate limit usage"""
        with self._lock:
            return {
                'burst_count': self._burst_window.get_count(),
                'minute_count': self._minute_window.get_count(),
                'hour_count': self._hour_window.get_count(),
                'burst_limit': self.rate_config.burst_limit,
                'minute_limit': self.rate_config.requests_per_minute,
                'hour_limit': self.rate_config.requests_per_hour,
            }
    
    def get_metrics(self) -> Dict[str, Any]:
        """Get rate limiter metrics"""
        usage = self.get_current_usage()
        
        return {
            'user_guid': self.user_guid,
            'total_requests': self._total_requests.get_value(),
            'rejected_requests': self._rejected_requests.get_value(),
            'last_request_time': self._last_request_time,
            'current_usage': usage,
            'acceptance_rate': self._calculate_acceptance_rate()
        }
    
    def _calculate_acceptance_rate(self) -> float:
        """Calculate request acceptance rate"""
        total = self._total_requests.get_value()
        rejected = self._rejected_requests.get_value()
        
        if total + rejected == 0:
            return 1.0
        
        return total / (total + rejected)
    
    def reset_counters(self):
        """Reset all counters (for testing or admin operations)"""
        with self._lock:
            self._minute_window.clear()
            self._hour_window.clear()
            self._daily_window.clear()
            self._burst_window.clear()
            self._total_requests.reset()
            self._rejected_requests.reset()

class SystemRateLimiter:
    """System-wide rate limiting manager"""
    
    def __init__(self):
        # User-specific rate limiters
        self._user_limiters: Dict[str, UserRateLimiter] = {}
        self._limiters_lock = threading.Lock()
        
        # Global system limits
        self._global_minute_window = TimeWindow(60)
        self._global_hour_window = TimeWindow(3600)
        self._global_lock = threading.Lock()
        
        # Cleanup task
        self._cleanup_task: Optional[asyncio.Task] = None
        self._shutdown = False
        
        # System-wide metrics
        self._system_metrics = {
            'total_users': ThreadSafeCounter(),
            'active_limiters': ThreadSafeCounter(),
            'global_requests': ThreadSafeCounter(),
            'global_rejections': ThreadSafeCounter(),
        }
    
    async def setup(self):
        """Initialize the system rate limiter"""
        if not self._cleanup_task:
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
    
    async def shutdown(self):
        """Shutdown the system rate limiter"""
        self._shutdown = True
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        with self._limiters_lock:
            self._user_limiters.clear()
        
        LogFire.log("INIT", "SystemRateLimiter shutdown complete")
    
    async def check_user_rate_limit(self, user_guid: str, permission_level: PERMISSION_LEVELS, 
                                  operation_type: str = "default") -> Tuple[bool, Optional[float]]:
        """
        Check rate limit for specific user
        
        Args:
            user_guid: User GUID
            permission_level: User permission level
            operation_type: Type of operation
            
        Returns:
            Tuple[bool, Optional[float]]: (allowed, retry_after_seconds)
        """
        # Check global system limits first
        if not await self._check_global_limits():
            return False, 60.0  # System overloaded, retry in 1 minute
        
        # Get or create user rate limiter
        user_limiter = self._get_user_limiter(user_guid, permission_level)
        
        # Check user-specific limits
        allowed, retry_after = await user_limiter.check_rate_limit(operation_type)
        
        # Update global metrics
        self._system_metrics['global_requests'].increment()
        if not allowed:
            self._system_metrics['global_rejections'].increment()
        
        if not allowed:
            raise UserRateLimitExceededError(user_guid, f"{operation_type}_rate_limit", retry_after)
        
        return allowed, retry_after
    
    def _get_user_limiter(self, user_guid: str, permission_level: PERMISSION_LEVELS) -> UserRateLimiter:
        """Get or create user rate limiter"""
        with self._limiters_lock:
            if user_guid not in self._user_limiters:
                rate_config = ScheduledRequestsConfig.get_rate_limit_config(permission_level)
                self._user_limiters[user_guid] = UserRateLimiter(user_guid, rate_config)
                self._system_metrics['total_users'].increment()
                LogFire.log("INIT", f"Created rate limiter for user {user_guid} with level {permission_level.value}")
            
            return self._user_limiters[user_guid]
    
    async def _check_global_limits(self) -> bool:
        """Check global system rate limits"""
        with self._global_lock:
            current_time = datetime.now(timezone.utc).timestamp()
            
            # Global limits (can be configured)
            global_minute_limit = 1000  # 1000 requests per minute system-wide
            global_hour_limit = 50000   # 50000 requests per hour system-wide
            
            minute_count = self._global_minute_window.add_request(current_time)
            if minute_count > global_minute_limit:
                LogFire.log("WARNING", f"Global minute limit exceeded: {minute_count}/{global_minute_limit}")
                return False
            
            hour_count = self._global_hour_window.add_request(current_time)
            if hour_count > global_hour_limit:
                LogFire.log("WARNING", f"Global hour limit exceeded: {hour_count}/{global_hour_limit}")
                return False
            
            return True
    
    async def get_user_rate_status(self, user_guid: str) -> Optional[Dict[str, Any]]:
        """Get rate limit status for specific user"""
        with self._limiters_lock:
            if user_guid in self._user_limiters:
                return self._user_limiters[user_guid].get_metrics()
            return None
    
    async def get_system_rate_metrics(self) -> Dict[str, Any]:
        """Get system-wide rate limiting metrics"""
        with self._limiters_lock:
            active_limiters = len(self._user_limiters)
        
        with self._global_lock:
            global_usage = {
                'minute_count': self._global_minute_window.get_count(),
                'hour_count': self._global_hour_window.get_count(),
            }
        
        return {
            'system_metrics': {
                'total_users': self._system_metrics['total_users'].get_value(),
                'active_limiters': active_limiters,
                'global_requests': self._system_metrics['global_requests'].get_value(),
                'global_rejections': self._system_metrics['global_rejections'].get_value(),
            },
            'global_usage': global_usage,
            'system_load': self._calculate_system_load(global_usage),
            'top_users': await self._get_top_users_by_usage()
        }
    
    def _calculate_system_load(self, global_usage: Dict[str, int]) -> float:
        """Calculate current system load percentage"""
        minute_load = (global_usage['minute_count'] / 1000) * 100  # Based on 1000/min limit
        hour_load = (global_usage['hour_count'] / 50000) * 100     # Based on 50000/hour limit
        
        return max(minute_load, hour_load)
    
    async def _get_top_users_by_usage(self, limit: int = 10) -> List[Dict[str, Any]]:
        """Get top users by request volume"""
        with self._limiters_lock:
            user_usage = []
            for user_guid, limiter in self._user_limiters.items():
                metrics = limiter.get_metrics()
                user_usage.append({
                    'user_guid': user_guid,
                    'total_requests': metrics['total_requests'],
                    'rejected_requests': metrics['rejected_requests'],
                    'acceptance_rate': metrics['acceptance_rate']
                })
            
            # Sort by total requests descending
            user_usage.sort(key=lambda x: x['total_requests'], reverse=True)
            return user_usage[:limit]
    
    async def reset_user_limits(self, user_guid: str) -> bool:
        """Reset rate limits for specific user (admin operation)"""
        with self._limiters_lock:
            if user_guid in self._user_limiters:
                self._user_limiters[user_guid].reset_counters()
                LogFire.log("ADMIN", f"Reset rate limits for user {user_guid}")
                return True
            return False
    
    async def remove_user_limiter(self, user_guid: str) -> bool:
        """Remove user rate limiter (when user is inactive)"""
        with self._limiters_lock:
            if user_guid in self._user_limiters:
                del self._user_limiters[user_guid]
                LogFire.log("INIT", f"Removed rate limiter for user {user_guid}")
                return True
            return False
    
    async def _periodic_cleanup(self):
        """Periodic cleanup of inactive rate limiters"""
        while not self._shutdown:
            try:
                await asyncio.sleep(1800)  # Run every 30 minutes
                await self._cleanup_inactive_limiters()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                LogFire.log("ERROR", f"Error in rate limiter cleanup: {str(e)}")
    
    async def _cleanup_inactive_limiters(self):
        """Clean up rate limiters for inactive users"""
        current_time = datetime.now(timezone.utc).timestamp()
        inactive_threshold = 3600  # 1 hour of inactivity
        
        with self._limiters_lock:
            inactive_users = []
            
            for user_guid, limiter in self._user_limiters.items():
                if current_time - limiter._last_request_time > inactive_threshold:
                    # Check if user has any activity
                    if limiter.get_current_usage()['minute_count'] == 0:
                        inactive_users.append(user_guid)
            
            # Remove inactive limiters
            for user_guid in inactive_users:
                del self._user_limiters[user_guid]
                LogFire.log("INIT", f"Cleaned up inactive rate limiter for user {user_guid}")
            
            if inactive_users:
                LogFire.log("INIT", f"Cleaned up {len(inactive_users)} inactive rate limiters")
    
    def get_limiter_count(self) -> int:
        """Get current number of active rate limiters"""
        with self._limiters_lock:
            return len(self._user_limiters)
    
    async def apply_emergency_limits(self, severity: str = "medium"):
        """Apply emergency rate limits during system stress"""
        emergency_factors = {
            "low": 0.8,     # Reduce limits by 20%
            "medium": 0.5,  # Reduce limits by 50%
            "high": 0.2,    # Reduce limits by 80%
            "critical": 0.1 # Reduce limits by 90%
        }
        
        factor = emergency_factors.get(severity, 0.5)
        
        with self._limiters_lock:
            for limiter in self._user_limiters.values():
                # Temporarily reduce rate limits
                original_config = limiter.rate_config
                limiter.rate_config = RateLimitConfig(
                    requests_per_minute=int(original_config.requests_per_minute * factor),
                    requests_per_hour=int(original_config.requests_per_hour * factor),
                    burst_limit=max(1, int(original_config.burst_limit * factor))
                )
        
        LogFire.log("WARNING", f"Applied emergency rate limits with severity: {severity} (factor: {factor})")
    
    async def restore_normal_limits(self):
        """Restore normal rate limits after emergency"""
        with self._limiters_lock:
            for user_guid, limiter in self._user_limiters.items():
                # Get user's permission level to restore proper limits
                try:
                    from managers.manager_users import ZairaUserManager
                    user_manager = ZairaUserManager.get_instance()
                    user = await user_manager.find_user(user_guid)
                    
                    if user and hasattr(user, 'rank'):
                        original_config = ScheduledRequestsConfig.get_rate_limit_config(user.rank)
                        limiter.rate_config = original_config
                    
                except Exception as e:
                    LogFire.log("ERROR", f"Failed to restore limits for user {user_guid}: {str(e)}")
        
        LogFire.log("INFO", "Restored normal rate limits")