# Email and Agenda Unit Tests

This folder contains unit tests for email and agenda functionality components.

## Test Files

### Email Functionality
- `test_task_email_generator.py` - Unit tests for email generation (EmailGeneratorTool)
- `test_task_out_email.py` - Unit tests for email sending (EmailSenderTool, EmailSenderDirectTool)
- `test_task_imap.py` - Unit tests for IMAP email retrieval (SupervisorTask_IMAP)

### Agenda Functionality  
- `test_task_agenda_planner.py` - Unit tests for agenda planning and calendar integration

## Running Tests

Run all email and agenda unit tests:
```bash
../../.venv/Scripts/pytest.exe tests/unit/email_agenda/ -v
```

Run specific test files:
```bash
# Email generation tests
../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_email_generator.py -v

# Email sending tests
../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_out_email.py -v

# IMAP tests
../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_imap.py -v

# Agenda planner tests
../../.venv/Scripts/pytest.exe tests/unit/email_agenda/test_task_agenda_planner.py -v
```

## Test Coverage

These unit tests provide comprehensive coverage for:

### Email Generation (test_task_email_generator.py)
- EmailGeneratorTool initialization
- Email generation with all parameters
- Missing sender/recipient handling
- User approval/rejection workflows
- Subject generation
- Content editing by user
- Exception handling
- State management

### Email Sending (test_task_out_email.py)
- EmailSenderTool and EmailSenderDirectTool initialization
- Gmail and SMTP configuration
- OAuth2 integration
- SMTP login with fallback mechanisms
- Email approval workflows
- State updates on successful sending
- Error handling for unsupported providers
- SupervisorTask_Email functionality

### IMAP Processing (test_task_imap.py)
- SupervisorTask_IMAP initialization
- SSL and non-SSL IMAP connections
- Email retrieval workflows
- Login fallback mechanisms
- Selective email saving
- User interaction handling
- No emails found scenarios
- Callback response state management

### Agenda Planning (test_task_agenda_planner.py)
- Supervisor creation
- Event preview formatting
- Calendar tools integration
- OAuth2 verification
- Import availability handling
- Task and supervisor properties

## Test Architecture

All tests follow the established patterns:
- Uses `@pytest.mark.unit` marker
- Async test methods with proper mocking
- Comprehensive state management testing
- Error handling validation
- Tool interaction testing
- Human-in-the-loop workflow testing

## Dependencies

Tests use standard mocking patterns:
- `AsyncMock` for async operations
- `MagicMock` for object mocking
- `patch` for component isolation
- Proper state object simulation