from imports import *

import os
import importlib.util
from pathlib import Path
from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTaskState
from managers.manager_users import ZairaUserManager

class SupervisorTask_System(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        """Execute all Python scripts in the output_tasks folder"""
        input_message = state.messages[-1].content if len(state.messages) > 1 else state.messages[0].content
        user = await ZairaUserManager.find_user(state.user_guid)
        
        try:
            # Get the current directory (output_tasks folder)
            current_dir = Path(__file__).parent
            
            # Find all Python files in the output_tasks directory
            python_files = []
            for file_path in current_dir.glob("*.py"):
                # Skip the current file and __init__.py
                if file_path.name not in ["task_out_system.py", "__init__.py"]:
                    python_files.append(file_path)
            
            LogFire.log("OUTPUT", f"System task found {len(python_files)} output tasks to execute")
            
            # Execute each output task
            executed_tasks = []
            for py_file in python_files:
                try:
                    # Import the module dynamically
                    module_name = py_file.stem  # Get filename without extension
                    spec = importlib.util.spec_from_file_location(module_name, py_file)
                    module = importlib.util.module_from_spec(spec)
                    spec.loader.exec_module(module)
                    
                    # Look for a create function (following the naming pattern)
                    create_function_name = f"create_out_task_{module_name.replace('task_out_', '')}"
                    
                    if hasattr(module, create_function_name):
                        create_function = getattr(module, create_function_name)
                        # Create the task instance
                        task_instance = await create_function()
                        
                        # Execute the task with the current state
                        if hasattr(task_instance, 'llm_call'):
                            await task_instance.llm_call(state)
                            executed_tasks.append(module_name)
                            LogFire.log("OUTPUT", f"Successfully executed {module_name}")
                        else:
                            LogFire.log("WARNING", f"Task {module_name} does not have llm_call method")
                    else:
                        LogFire.log("WARNING", f"No create function found for {module_name}")
                        
                except Exception as e:
                    LogFire.log("ERROR", f"Failed to execute {py_file.name}: {str(e)}")
                    from etc.helper_functions import exception_triggered
                    exception_triggered(e, f"Failed to execute output task {py_file.name}", user)
            
            # Send summary response
            summary_message = f"System task executed {len(executed_tasks)} output tasks: {', '.join(executed_tasks)}"
            LogFire.log("OUTPUT", "System:", summary_message)
            
            LogFire.log("USER", "SYSTEM user sent to all output paths", summary_message)
            
        except Exception as e:
            error_message = f"System task failed: {str(e)}"
            LogFire.log("ERROR", error_message)
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "System output task execution failed", user)
            
            LogFire.log("USER", "SYSTEM user sent to all output paths", f"System execution failed: {str(e)}")

async def create_out_task_system() -> SupervisorTask_Base:
    """Create the system output task that executes all output tasks"""
    direct_prompt = "You're a task responsible when the output message needs to be directed to SYSTEM."
    return SupervisorManager.register_task(SupervisorTask_System(name="system_out", prompt=direct_prompt))