from imports import *
from managers.manager_logfire import LogFire
import asyncio
from managers.manager_postgreSQL import PostgreSQLManager

async def fix_scheduled_tasks_table():
    """Drop and recreate the scheduled_tasks table with correct schema"""
    try:
        # Ensure PostgreSQL manager is set up
        await PostgreSQLManager.setup()
        
        # Get database connection
        pool = await PostgreSQLManager.get_connection("vectordb")
        
        if not pool:
            LogFire.log("ERROR", "Failed to get database connection", severity="error")
            LogFire.log("ERROR", "Make sure PostgreSQL is running:", severity="error")
            LogFire.log("ERROR", "  docker-compose up -d postgres", severity="error")
            return
        
        async with pool.acquire() as conn:
            LogFire.log("DEBUG", "Connected to database", severity="debug")
            
            # Drop the existing table
            LogFire.log("DEBUG", "\nDropping existing scheduled_tasks table...", severity="debug")
            try:
                await conn.execute("DROP TABLE IF EXISTS scheduled_tasks CASCADE")
                LogFire.log("DEBUG", "Table dropped successfully", severity="debug")
            except Exception as e:
                LogFire.log("ERROR", f"Error dropping table: {e}", severity="error")
            
            # Create the table with correct schema
            LogFire.log("DEBUG", "\nCreating scheduled_tasks table with correct schema...", severity="debug")
            create_table_query = """
            CREATE TABLE scheduled_tasks (
                scheduled_guid VARCHAR(36) PRIMARY KEY,
                user_guid VARCHAR(36) NOT NULL,
                schedule_prompt TEXT NOT NULL,
                target_prompt TEXT NOT NULL,
                delay_seconds FLOAT NOT NULL,
                start_delay_seconds FLOAT DEFAULT 0.0,
                schedule_type VARCHAR(20) NOT NULL,
                next_execution TIMESTAMPTZ,
                is_active BOOLEAN DEFAULT TRUE,
                created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
                updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
                calling_bot_name VARCHAR(100),
                task_data JSONB,
                cancellation_reason TEXT,
                cancelled_at TIMESTAMPTZ
            )
            """
            
            await conn.execute(create_table_query)
            LogFire.log("DEBUG", "Table created successfully", severity="debug")
            
            # Create indexes
            LogFire.log("DEBUG", "\nCreating indexes...", severity="debug")
            index_queries = [
                "CREATE INDEX IF NOT EXISTS idx_scheduled_tasks_user_guid ON scheduled_tasks(user_guid)",
                "CREATE INDEX IF NOT EXISTS idx_scheduled_tasks_active ON scheduled_tasks(is_active)", 
                "CREATE INDEX IF NOT EXISTS idx_scheduled_tasks_next_execution ON scheduled_tasks(next_execution)"
            ]
            
            for idx_query in index_queries:
                try:
                    await conn.execute(idx_query)
                    index_name = idx_query.split('INDEX')[1].split('ON')[0].strip()
                    LogFire.log("DEBUG", f"  Created index: {index_name}", severity="debug")
                except Exception as e:
                    LogFire.log("ERROR", f"  Error creating index: {e}", severity="error")
            
            # Verify the table structure
            LogFire.log("DEBUG", "\nVerifying table structure...", severity="debug")
            verify_query = """
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns
            WHERE table_schema = 'public' 
            AND table_name = 'scheduled_tasks'
            ORDER BY ordinal_position;
            """
            
            columns = await conn.fetch(verify_query)
            LogFire.log("DEBUG", "\nTable columns:", severity="debug")
            for col in columns:
                nullable = "NULL" if col['is_nullable'] == 'YES' else "NOT NULL"
                LogFire.log("DEBUG", f"  {col['column_name']}: {col['data_type']} {nullable}", severity="debug")
            
            LogFire.log("DEBUG", "\nTable fixed successfully!", severity="debug")
            LogFire.log("DEBUG", "You can now restart the application.", severity="debug")
        
    except Exception as e:
        LogFire.log("ERROR", f"Error: {e}", severity="error")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    LogFire.log("DEBUG", "Scheduled Tasks Table Fix Script", severity="debug")
    LogFire.log("DEBUG", "=================================", severity="debug")
    asyncio.run(fix_scheduled_tasks_table())