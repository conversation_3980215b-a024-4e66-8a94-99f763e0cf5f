/* AskZaira Dashboard Whitelabel Configuration */
/* This file contains all branding and visual elements for the AskZaira Dashboard */

:root {
    /* Brand Colors - Restored Original */
    --brand-primary: #3b82f6;
    --brand-secondary: #1e293b;
    --brand-accent: #6366f1;
    --brand-background: #000000;
    
    /* Dashboard Colors - Restored Original */
    --dashboard-glass: rgba(30, 41, 59, 0.8);
    --dashboard-glass-border: rgba(59, 130, 246, 0.2);
    --dashboard-text-primary: #f8fafc;
    --dashboard-text-secondary: #94a3b8;
    --dashboard-text-muted: #64748b;
    
    /* Menu Colors - Restored Original */
    --menu-background: rgba(30, 41, 59, 0.9);
    --menu-hover: rgba(59, 130, 246, 0.2);
    --menu-active: rgba(59, 130, 246, 0.1);
    --menu-border: rgba(59, 130, 246, 0.3);
    
    /* Status Colors - Restored Original */
    --status-success: #28a745;
    --status-warning: #ffc107;
    --status-error: #dc3545;
    --status-info: #3b82f6;
    
    /* Spacing */
    --spacing-xs: 0.25rem;
    --spacing-sm: 0.5rem;
    --spacing-md: 1rem;
    --spacing-lg: 1.5rem;
    --spacing-xl: 2rem;
    
    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-glass: 0 8px 32px 0 rgba(31, 38, 135, 0.37);
}

/* Brand Logo SVG */
.brand-logo {
    width: 356px;
    height: 357px;
}

.brand-logo svg {
    filter: drop-shadow(-5.44922px 10.8984px 19.0723px rgba(23, 27, 33, 1));
}

/* Dashboard Title */
.dashboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    color: var(--dashboard-text-primary);
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    margin-bottom: var(--spacing-lg);
}

.dashboard-subtitle {
    font-size: 1.1rem;
    color: var(--dashboard-text-secondary);
    margin-bottom: var(--spacing-xl);
}

/* Glassmorphism Base Style */
.glass-container {
    background: var(--dashboard-glass);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
    border: 1px solid var(--dashboard-glass-border);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-glass);
}

/* Right Side Menu Base */
.right-menu {
    position: fixed;
    top: 0;
    right: 0;
    height: 100vh;
    width: 80px;
    background: var(--menu-background);
    backdrop-filter: blur(20px);
    border-left: 1px solid var(--menu-border);
    transition: width 0.3s ease;
    z-index: 1000;
}

.right-menu.expanded {
    width: 320px;
}

.menu-toggle {
    position: absolute;
    top: var(--spacing-lg);
    right: var(--spacing-lg);
    background: transparent;
    border: none;
    color: var(--dashboard-text-primary);
    font-size: 1.5rem;
    cursor: pointer;
    padding: var(--spacing-sm);
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
}

.menu-toggle:hover {
    background: var(--menu-hover);
}

/* Menu Items */
.menu-items {
    padding-top: 80px;
    padding-left: var(--spacing-md);
    padding-right: var(--spacing-md);
}

.menu-item {
    display: flex;
    align-items: center;
    padding: var(--spacing-md);
    margin-bottom: var(--spacing-sm);
    color: var(--dashboard-text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all 0.3s ease;
    cursor: pointer;
    min-height: 48px;
}

.menu-item:hover {
    background: var(--menu-hover);
    color: var(--dashboard-text-primary);
}

.menu-item.active {
    background: var(--menu-active);
    color: var(--brand-primary);
    border: 1px solid var(--menu-border);
}

.menu-item-icon {
    width: 24px;
    height: 24px;
    margin-right: var(--spacing-md);
    flex-shrink: 0;
}

.menu-item-text {
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.3s ease;
    white-space: nowrap;
}

.right-menu.expanded .menu-item-text {
    opacity: 1;
    transform: translateX(0);
}

/* Status Badge */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.status-badge.success {
    background: rgba(16, 185, 129, 0.2);
    color: var(--status-success);
    border: 1px solid var(--status-success);
}

.status-badge.warning {
    background: rgba(245, 158, 11, 0.2);
    color: var(--status-warning);
    border: 1px solid var(--status-warning);
}

.status-badge.error {
    background: rgba(239, 68, 68, 0.2);
    color: var(--status-error);
    border: 1px solid var(--status-error);
}

.status-badge.info {
    background: rgba(59, 130, 246, 0.2);
    color: var(--status-info);
    border: 1px solid var(--status-info);
}

/* Expandable Section Framework */
.expandable-section {
    margin-bottom: var(--spacing-md);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all 0.3s ease;
}

.expandable-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: var(--spacing-lg);
    background: var(--dashboard-glass);
    border: 1px solid var(--dashboard-glass-border);
    cursor: pointer;
    transition: all 0.3s ease;
}

.expandable-header:hover {
    background: rgba(255, 255, 255, 0.15);
}

.expandable-header.active {
    background: var(--menu-active);
    border-color: var(--menu-border);
}

.expandable-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--dashboard-text-primary);
    margin: 0;
}

.expandable-icon {
    width: 20px;
    height: 20px;
    color: var(--dashboard-text-secondary);
    transition: transform 0.3s ease;
}

.expandable-header.active .expandable-icon {
    transform: rotate(180deg);
}

.expandable-content {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease;
    background: rgba(255, 255, 255, 0.05);
    border-left: 1px solid var(--dashboard-glass-border);
    border-right: 1px solid var(--dashboard-glass-border);
    border-bottom: 1px solid var(--dashboard-glass-border);
}

.expandable-content.active {
    max-height: 1000px;
}

.expandable-body {
    padding: var(--spacing-lg);
}

/* Auto-refresh Control */
.auto-refresh-control {
    display: flex;
    align-items: center;
    gap: var(--spacing-md);
    padding: var(--spacing-md);
    background: var(--dashboard-glass);
    border-radius: var(--radius-md);
    border: 1px solid var(--dashboard-glass-border);
}

.refresh-toggle {
    position: relative;
    width: 50px;
    height: 24px;
    background: rgba(100, 116, 139, 0.3);
    border-radius: 12px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.refresh-toggle.active {
    background: var(--brand-primary);
}

.refresh-toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 20px;
    height: 20px;
    background: white;
    border-radius: 50%;
    transition: transform 0.3s ease;
}

.refresh-toggle.active .refresh-toggle-slider {
    transform: translateX(26px);
}

/* Animations */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes slideIn {
    from {
        transform: translateX(100%);
    }
    to {
        transform: translateX(0);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}

.fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

.pulse {
    animation: pulse 2s infinite;
}

/* Responsive Design */
@media (max-width: 768px) {
    .right-menu {
        width: 100%;
        height: auto;
        position: relative;
        border-left: none;
        border-bottom: 1px solid var(--menu-border);
    }
    
    .right-menu.expanded {
        width: 100%;
    }
    
    .menu-items {
        display: flex;
        flex-wrap: wrap;
        padding-top: var(--spacing-md);
    }
    
    .menu-item {
        flex: 1;
        min-width: 120px;
        justify-content: center;
    }
    
    .menu-item-text {
        opacity: 1;
        transform: none;
    }
}

/* Utility Classes */
.text-primary { color: var(--dashboard-text-primary); }
.text-secondary { color: var(--dashboard-text-secondary); }
.text-muted { color: var(--dashboard-text-muted); }
.text-success { color: var(--status-success); }
.text-warning { color: var(--status-warning); }
.text-error { color: var(--status-error); }
.text-info { color: var(--status-info); }

.bg-glass { background: var(--dashboard-glass); }
.bg-menu { background: var(--menu-background); }

.border-glass { border: 1px solid var(--dashboard-glass-border); }
.border-menu { border: 1px solid var(--menu-border); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded-md { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }

.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow-md { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-glass { box-shadow: var(--shadow-glass); }

.p-xs { padding: var(--spacing-xs); }
.p-sm { padding: var(--spacing-sm); }
.p-md { padding: var(--spacing-md); }
.p-lg { padding: var(--spacing-lg); }
.p-xl { padding: var(--spacing-xl); }

.m-xs { margin: var(--spacing-xs); }
.m-sm { margin: var(--spacing-sm); }
.m-md { margin: var(--spacing-md); }
.m-lg { margin: var(--spacing-lg); }
.m-xl { margin: var(--spacing-xl); }