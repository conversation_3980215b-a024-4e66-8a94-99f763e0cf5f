from imports import *
from managers.manager_logfire import LogFire

import asyncio
import requests
from xml.etree import ElementTree
from typing import Optional, List, Dict, Any
from dataclasses import dataclass
from datetime import datetime, timezone
from urllib.parse import urlparse
from dotenv import load_dotenv
from crawl4ai import Async<PERSON>ebCrawler, BrowserConfig, CrawlerRunConfig, CacheMode
import uuid

from managers.manager_qdrant import QDrantManager
from managers.manager_retrieval import Retrieval<PERSON>anager

@dataclass
class ProcessedChunk:
    file_path: str  # URL for web pages
    file_name: str  # Derived from URL
    file_type: str  # "web_page"
    file_size: int  # Content length
    creation_date: str  # Crawl timestamp
    last_modified_date: str  # Crawl timestamp
    _node_content: str  # The actual chunk text content
    chunk_number: int
    title: str
    summary: str
    metadata: Dict[str, Any]
    embedding: List[float]

class Crawler:
    def __init__(self, collection_name: str = "mainCollection"):
        load_dotenv()
        self.collection_name = collection_name
        self.qdrant_client = QDrantManager.GetClient()
        self.browser_config = None
        self.crawler = None
        self.vector_size = EMBEDDING_SIZE

        # Use the configured embedding size
        LogFire.log("DEBUG", f"Using embedding size: {self.vector_size}", severity="debug")

    @classmethod
    async def setup_async(cls):
        """Initialize the Crawler class asynchronously"""
        instance = cls()
        success = await instance.test_initialization_async()  # Changed to async version
        if not success:
            LogFire.log("INIT", "Crawler setup completed with warnings", severity="warning")
        return instance

    async def test_initialization_async(self):
        """Test the crawler initialization asynchronously"""
        try:
            # Initialize the crawler
            if not self.crawler:
                self.browser_config = BrowserConfig()
                self.crawler = AsyncWebCrawler(config=self.browser_config)
                await self.crawler.start()

            # Test that crawler is properly initialized
            if self.crawler:
                LogFire.log("INIT", "[OK] Crawler initialization successful")
                return True
            else:
                LogFire.log("ERROR", "[FAIL] Crawler initialization failed", severity="error")
                return False

        except Exception as e:
            LogFire.log("ERROR", f"[FAIL] Crawler initialization error: {e}", severity="error")
            return False

    async def start_crawler(self):
        """Initialize the crawler if not already initialized"""
        if not self.crawler:
            self.browser_config = BrowserConfig()
            self.crawler = AsyncWebCrawler(config=self.browser_config)
            await self.crawler.start()

    async def close_crawler(self):
        """Close the crawler properly"""
        if self.crawler:
            try:
                await self.crawler.close()  # Try close() as fallback
            except AttributeError:
                try:
                    await self.crawler.stop()
                except AttributeError:
                    LogFire.log("ERROR", "Warning: Could not find proper close method for crawler", severity="warning")
            except Exception as e:
                LogFire.log("ERROR", f"Error closing crawler: {e}", severity="error")
            finally:
                self.crawler = None
                self.browser_config = None

    def clean_text(self, text: str) -> str:
        """Clean up the text by removing excessive repetition and unwanted elements."""
        lines = text.split('\n')
        unique_lines = []
        seen = set()

        for line in lines:
            # Clean the line
            clean_line = line.strip()
            # Skip empty lines
            if not clean_line:
                continue
            # Skip image references and navigation elements
            if clean_line.startswith('![') or '→' in clean_line:
                continue
            # Hash the content to detect duplicates
            line_hash = hash(clean_line)
            if line_hash not in seen:
                seen.add(line_hash)
                unique_lines.append(line)

        # Rejoin the deduplicated text
        return '\n'.join(unique_lines)

    async def process_chunk(self, chunk: str, chunk_number: int, url: str) -> ProcessedChunk:
        """Process a single chunk of text with duplicate detection."""
        # Skip chunks that are just repetitive content
        if chunk.count(chunk[:50]) > 2:  # If the first 50 chars repeat more than twice
            LogFire.log("DEBUG", f"Skipping repetitive chunk {chunk_number} from {url}", severity="debug")
            return None

        # Parse URL to get file name
        parsed_url = urlparse(url)
        file_name = parsed_url.path.split('/')[-1] if parsed_url.path.split('/')[-1] else parsed_url.netloc

        # Generate simple title and summary without LLM
        title = f"Content from {parsed_url.path} - Chunk {chunk_number}"
        summary = chunk[:200] + "..." if len(chunk) > 200 else chunk

        # Get embedding using standardized RetrievalManager method
        embedding = await RetrievalManager.get_embeddings_dense(chunk)

        # Create crawl timestamp
        crawl_timestamp = datetime.now(timezone.utc).strftime("%Y-%m-%d")

        # Create metadata for additional fields
        metadata = {
            "source": "web_crawl",
            "chunk_size": len(chunk),
            "crawled_at": str(datetime.now(timezone.utc).date()),
            "url_path": parsed_url.path,
            "chunk_index": chunk_number,
            "total_chunks": None  # Will be set later when we know total count
        }

        return ProcessedChunk(
            file_path=url,  # URL serves as file_path for web pages
            file_name=file_name,  # Derived from URL
            file_type="web_page",  # Standard type for web content
            file_size=len(chunk.encode('utf-8')),  # Content length in bytes
            creation_date=crawl_timestamp,  # Crawl timestamp
            last_modified_date=crawl_timestamp,  # Crawl timestamp
            _node_content=chunk,  # The actual chunk text content
            chunk_number=chunk_number,
            title=title,
            summary=summary,
            metadata=metadata,
            embedding=embedding
        )



    async def process_and_store_document(self, url: str, markdown: str):
        """Process a document and store its chunks using upsert_multiple."""
        # Clean the text first to remove duplicates and unwanted elements
        cleaned_text = self.clean_text(markdown)

        # Split into chunks using the standardized RetrievalManager method
        chunks = await RetrievalManager.chunk_text(cleaned_text)

        # Process chunks in parallel
        tasks = [
            self.process_chunk(chunk, i, url)
            for i, chunk in enumerate(chunks)
        ]
        processed_chunks = await asyncio.gather(*tasks)

        # Filter out None chunks and update total_chunks metadata
        valid_chunks = [chunk for chunk in processed_chunks if chunk is not None]
        total_chunks = len(valid_chunks)
        for chunk in valid_chunks:
            chunk.metadata["total_chunks"] = total_chunks

        # Prepare data for upsert_multiple - same pattern as manager_meltano
        chunk_ids = []
        chunk_texts = []
        chunk_metadatas = []

        for chunk in valid_chunks:
            # Create unique ID for the chunk
            chunk_id = str(uuid.uuid4())

            # Prepare metadata following the standardized structure
            metadata = {
                "file_path": chunk.file_path,
                "file_name": chunk.file_name,
                "file_type": chunk.file_type,
                "file_size": chunk.file_size,
                "creation_date": chunk.creation_date,
                "last_modified_date": chunk.last_modified_date,
                "chunk_index": chunk.chunk_number,
                "title": chunk.title,
                "source": "web_crawl",
                "crawled_at": chunk.metadata.get("crawled_at"),
                "url_path": chunk.metadata.get("url_path"),
                "chunk_size": chunk.metadata.get("chunk_size"),
                "total_chunks": total_chunks
            }

            chunk_ids.append(chunk_id)
            chunk_texts.append(chunk._node_content)
            chunk_metadatas.append(metadata)

        # Use the same line as manager_meltano
        await QDrantManager.upsert_multiple(chunk_ids, chunk_texts, metadatas=chunk_metadatas)

        LogFire.log("DEBUG", f"Successfully inserted {len(chunk_ids)} chunks for {url}", severity="debug")

    async def crawl_url(self, url: str, config: Optional[CrawlerRunConfig] = None):
        """Crawl a single URL and process it"""
        if not self.crawler:
            await self.start_crawler()

        if config is None:
            config = CrawlerRunConfig(cache_mode=CacheMode.BYPASS)

        try:
            result = await self.crawler.arun(
                url=url,
                config=config,
                session_id=f"session_{uuid.uuid4()}"
            )
            if result.success:
                await self.process_and_store_document(url, result.markdown.raw_markdown)
                LogFire.log("DEBUG", f"Successfully crawled: {url}", severity="debug")
                # Clean up crawl4ai completely after successful crawl
                await self.close_crawler()
                return True
            else:
                LogFire.log("ERROR", f"Failed: {url} - Error: {result.error_message}", severity="error")
                return False
        except Exception as e:
            LogFire.log("ERROR", f"Error crawling {url}: {e}", severity="error")
            return False

    async def crawl_parallel(self, urls: List[str], max_concurrent: int = 5):
        """Crawl multiple URLs in parallel with improved error handling."""
        if not self.crawler:
            await self.start_crawler()

        try:
            semaphore = asyncio.Semaphore(max_concurrent)

            async def process_url(url: str):
                async with semaphore:
                    try:
                        LogFire.log("DEBUG", f"Starting crawl of: {url}", severity="debug")
                        success = await self.crawl_url(url)
                        if success:
                            LogFire.log("DEBUG", f"Successfully processed: {url}", severity="debug")
                        else:
                            LogFire.log("ERROR", f"Failed to process: {url}", severity="error")
                    except Exception as e:
                        LogFire.log("ERROR", f"Error processing {url}: {e}", severity="error")

            # Create tasks for all URLs
            tasks = [process_url(url) for url in urls]

            # Process URLs in parallel
            await asyncio.gather(*tasks)

        finally:
            await self.close_crawler()

    async def crawl(self, url_or_sitemap: str, is_sitemap: bool = False, max_concurrent: int = 5):
        """Main crawl method - can crawl a single URL or a sitemap"""
        LogFire.log("DEBUG", f"Starting Crawler on: {url_or_sitemap}", severity="debug")
        LogFire.log("DEBUG", f"Mode: {'Sitemap' if is_sitemap else 'Single URL'}", severity="debug")

        if not self.crawler:
            await self.start_crawler()

        try:
            if is_sitemap:
                LogFire.log("DEBUG", "Fetching URLs from sitemap...", severity="debug")
                urls = self.get_sitemap_urls(url_or_sitemap)
                if not urls:
                    LogFire.log("ERROR", "No URLs found in sitemap to crawl", severity="warning")
                    return

                LogFire.log("DEBUG", f"Found {len(urls)} URLs to crawl from sitemap", severity="debug")
                await self.crawl_parallel(urls, max_concurrent)
            else:
                # Single URL
                await self.crawl_url(url_or_sitemap)
        except Exception as e:
            LogFire.log("ERROR", f"Error during crawl operation: {e}", severity="error")
        finally:
            await self.close_crawler()

    def get_sitemap_urls(self, sitemap_url: str) -> List[str]:
        """Get URLs from a sitemap."""
        try:
            LogFire.log("DEBUG", f"Fetching sitemap from: {sitemap_url}", severity="debug")
            response = requests.get(sitemap_url, timeout=30)
            response.raise_for_status()

            # Parse the XML
            root = ElementTree.fromstring(response.content)

            namespaces = {'sm': 'http://www.sitemaps.org/schemas/sitemap/0.9'}

            urls = []
            # Try with namespace first
            locations = root.findall('.//sm:loc', namespaces)
            if not locations:
                locations = root.findall('.//loc')

            if locations:
                urls = [loc.text for loc in locations if loc.text]
                LogFire.log("DEBUG", f"Found {len(urls)} URLs in sitemap", severity="debug")

            # Remove duplicates while preserving order
            urls = list(dict.fromkeys(urls))
            LogFire.log("DEBUG", f"Total unique URLs found in sitemap: {len(urls)}", severity="debug")
            return urls

        except requests.RequestException as e:
            LogFire.log("ERROR", f"Network error fetching sitemap: {e}", severity="error")
        except ElementTree.ParseError as e:
            LogFire.log("ERROR", f"XML parsing error: {e}", severity="error")
        except Exception as e:
            LogFire.log("ERROR", f"Unexpected error: {e}", severity="error")
        return []
