from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from uuid import uuid4
import asyncio
from datetime import datetime, timezone, timedelta
import time

from managers.scheduled_requests.resources.rate_limiter import UserRateLimiter, SystemRateLimiter
from managers.scheduled_requests.utils.config import RateLimitConfig
from managers.scheduled_requests.utils.exceptions import UserRateLimitExceededError
from managers.scheduled_requests.utils.helpers import TimeWindow, ThreadSafeCounter
from userprofiles.ZairaUser import PERMISSION_LEVELS

class TestUserRateLimiter:
    """Comprehensive test class for UserRateLimiter"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.test_rate_config = RateLimitConfig(
            requests_per_minute=10,
            requests_per_hour=100,
            burst_limit=5
        )
        
    def teardown_method(self):
        """Clean up after each test"""
        pass
    
    def test_user_rate_limiter_initialization(self):
        """Test UserRateLimiter initialization"""
        limiter = UserRateLimiter(self.test_user_guid, self.test_rate_config)
        
        assert limiter.user_guid == self.test_user_guid
        assert limiter.rate_config == self.test_rate_config
        assert hasattr(limiter, '_minute_window')
        assert hasattr(limiter, '_hour_window')
        assert hasattr(limiter, '_daily_window')
        assert hasattr(limiter, '_burst_window')
        assert hasattr(limiter, '_lock')
        assert hasattr(limiter, '_total_requests')
        assert hasattr(limiter, '_rejected_requests')
        assert isinstance(limiter._total_requests, ThreadSafeCounter)
        assert isinstance(limiter._rejected_requests, ThreadSafeCounter)
    
    @pytest.mark.asyncio
    async def test_check_rate_limit_success(self):
        """Test successful rate limit check"""
        limiter = UserRateLimiter(self.test_user_guid, self.test_rate_config)
        
        allowed, retry_after = await limiter.check_rate_limit("test_operation")
        
        assert allowed is True
        assert retry_after is None
        assert limiter._total_requests.get_value() == 1
        assert limiter._rejected_requests.get_value() == 0
    
    @pytest.mark.asyncio
    async def test_check_rate_limit_burst_exceeded(self):
        """Test rate limit check when burst limit is exceeded"""
        limiter = UserRateLimiter(self.test_user_guid, self.test_rate_config)
        
        # Fill burst window to limit
        for _ in range(self.test_rate_config.burst_limit):
            await limiter.check_rate_limit("test_operation")
        
        # Next request should be rejected
        allowed, retry_after = await limiter.check_rate_limit("test_operation")
        
        assert allowed is False
        assert retry_after == 10.0  # Should retry after 10 seconds
        assert limiter._rejected_requests.get_value() == 1
    
    @pytest.mark.asyncio
    async def test_check_rate_limit_minute_exceeded(self):
        """Test rate limit check when per-minute limit is exceeded"""
        # Create config with low minute limit for testing
        low_config = RateLimitConfig(
            requests_per_minute=2,
            requests_per_hour=100,
            burst_limit=10  # High burst limit to avoid burst restriction
        )
        limiter = UserRateLimiter(self.test_user_guid, low_config)
        
        # Make requests up to minute limit
        for _ in range(2):
            allowed, _ = await limiter.check_rate_limit("test_operation")
            assert allowed is True
        
        # Next request should be rejected
        allowed, retry_after = await limiter.check_rate_limit("test_operation")
        
        assert allowed is False
        assert retry_after == 60.0  # Should retry after 1 minute
        assert limiter._rejected_requests.get_value() == 1
    
    @pytest.mark.asyncio
    async def test_check_rate_limit_hour_exceeded(self):
        """Test rate limit check when per-hour limit is exceeded"""
        # Create config with low hour limit for testing
        low_config = RateLimitConfig(
            requests_per_minute=100,  # High minute limit
            requests_per_hour=2,      # Low hour limit
            burst_limit=10           # High burst limit
        )
        limiter = UserRateLimiter(self.test_user_guid, low_config)
        
        # Make requests up to hour limit
        for _ in range(2):
            allowed, _ = await limiter.check_rate_limit("test_operation")
            assert allowed is True
        
        # Next request should be rejected
        allowed, retry_after = await limiter.check_rate_limit("test_operation")
        
        assert allowed is False
        assert retry_after == 3600.0  # Should retry after 1 hour
        assert limiter._rejected_requests.get_value() == 1
    
    def test_get_current_usage(self):
        """Test getting current rate limit usage"""
        limiter = UserRateLimiter(self.test_user_guid, self.test_rate_config)
        
        usage = limiter.get_current_usage()
        
        assert 'burst_count' in usage
        assert 'minute_count' in usage
        assert 'hour_count' in usage
        assert 'burst_limit' in usage
        assert 'minute_limit' in usage
        assert 'hour_limit' in usage
        
        assert usage['burst_limit'] == self.test_rate_config.burst_limit
        assert usage['minute_limit'] == self.test_rate_config.requests_per_minute
        assert usage['hour_limit'] == self.test_rate_config.requests_per_hour
    
    @pytest.mark.asyncio
    async def test_get_metrics(self):
        """Test getting rate limiter metrics"""
        limiter = UserRateLimiter(self.test_user_guid, self.test_rate_config)
        
        # Make some requests
        await limiter.check_rate_limit("test_operation")
        await limiter.check_rate_limit("test_operation")
        
        metrics = limiter.get_metrics()
        
        assert 'user_guid' in metrics
        assert 'total_requests' in metrics
        assert 'rejected_requests' in metrics
        assert 'last_request_time' in metrics
        assert 'current_usage' in metrics
        assert 'acceptance_rate' in metrics
        
        assert metrics['user_guid'] == self.test_user_guid
        assert metrics['total_requests'] == 2
        assert metrics['rejected_requests'] == 0
        assert metrics['acceptance_rate'] == 1.0
    
    def test_calculate_acceptance_rate(self):
        """Test acceptance rate calculation"""
        limiter = UserRateLimiter(self.test_user_guid, self.test_rate_config)
        
        # Initially should be 1.0 (no requests)
        rate = limiter._calculate_acceptance_rate()
        assert rate == 1.0
        
        # Simulate some accepted and rejected requests
        limiter._total_requests._value = 8
        limiter._rejected_requests._value = 2
        
        rate = limiter._calculate_acceptance_rate()
        assert rate == 0.8  # 8/(8+2) = 0.8
    
    def test_reset_counters(self):
        """Test resetting all counters"""
        limiter = UserRateLimiter(self.test_user_guid, self.test_rate_config)
        
        # Add some data to counters
        limiter._total_requests._value = 10
        limiter._rejected_requests._value = 2
        
        limiter.reset_counters()
        
        assert limiter._total_requests.get_value() == 0
        assert limiter._rejected_requests.get_value() == 0
        assert limiter._minute_window.get_count() == 0
        assert limiter._hour_window.get_count() == 0
        assert limiter._burst_window.get_count() == 0

class TestSystemRateLimiter:
    """Comprehensive test class for SystemRateLimiter"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.test_user_guid_2 = str(uuid4())
        
    def teardown_method(self):
        """Clean up after each test"""
        pass
    
    def test_system_rate_limiter_initialization(self):
        """Test SystemRateLimiter initialization"""
        system_limiter = SystemRateLimiter()
        
        assert hasattr(system_limiter, '_user_limiters')
        assert isinstance(system_limiter._user_limiters, dict)
        assert hasattr(system_limiter, '_limiters_lock')
        assert hasattr(system_limiter, '_global_minute_window')
        assert hasattr(system_limiter, '_global_hour_window')
        assert hasattr(system_limiter, '_global_lock')
        assert hasattr(system_limiter, '_system_metrics')
        assert len(system_limiter._user_limiters) == 0
        assert system_limiter._shutdown is False
    
    @pytest.mark.asyncio
    async def test_system_limiter_setup(self):
        """Test system limiter setup"""
        system_limiter = SystemRateLimiter()
        
        await system_limiter.setup()
        
        assert system_limiter._cleanup_task is not None
        assert not system_limiter._cleanup_task.done()
    
    @pytest.mark.asyncio
    async def test_system_limiter_shutdown(self):
        """Test system limiter shutdown"""
        system_limiter = SystemRateLimiter()
        await system_limiter.setup()
        
        # Add some user limiters
        system_limiter._user_limiters[self.test_user_guid] = Mock()
        
        await system_limiter.shutdown()
        
        assert system_limiter._shutdown is True
        assert len(system_limiter._user_limiters) == 0
        assert system_limiter._cleanup_task is None or system_limiter._cleanup_task.cancelled()
    
    @pytest.mark.asyncio
    async def test_check_user_rate_limit_success(self):
        """Test successful user rate limit check"""
        system_limiter = SystemRateLimiter()
        
        with patch.object(system_limiter, '_check_global_limits', return_value=True), \
             patch.object(system_limiter, '_get_user_limiter') as mock_get_limiter:
            
            mock_user_limiter = Mock()
            mock_user_limiter.check_rate_limit = AsyncMock(return_value=(True, None))
            mock_get_limiter.return_value = mock_user_limiter
            
            allowed, retry_after = await system_limiter.check_user_rate_limit(
                self.test_user_guid, PERMISSION_LEVELS.USER, "test_operation"
            )
            
            assert allowed is True
            assert retry_after is None
            mock_user_limiter.check_rate_limit.assert_called_once_with("test_operation")
    
    @pytest.mark.asyncio
    async def test_check_user_rate_limit_global_limits_exceeded(self):
        """Test user rate limit check when global limits are exceeded"""
        system_limiter = SystemRateLimiter()
        
        with patch.object(system_limiter, '_check_global_limits', return_value=False):
            allowed, retry_after = await system_limiter.check_user_rate_limit(
                self.test_user_guid, PERMISSION_LEVELS.USER, "test_operation"
            )
            
            assert allowed is False
            assert retry_after == 60.0
    
    @pytest.mark.asyncio
    async def test_check_user_rate_limit_user_limit_exceeded(self):
        """Test user rate limit check when user limit is exceeded"""
        system_limiter = SystemRateLimiter()
        
        with patch.object(system_limiter, '_check_global_limits', return_value=True), \
             patch.object(system_limiter, '_get_user_limiter') as mock_get_limiter:
            
            mock_user_limiter = Mock()
            mock_user_limiter.check_rate_limit = AsyncMock(return_value=(False, 30.0))
            mock_get_limiter.return_value = mock_user_limiter
            
            with pytest.raises(UserRateLimitExceededError):
                await system_limiter.check_user_rate_limit(
                    self.test_user_guid, PERMISSION_LEVELS.USER, "test_operation"
                )
    
    def test_get_user_limiter_new_user(self):
        """Test getting user limiter for new user"""
        system_limiter = SystemRateLimiter()
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_rate_limit_config') as mock_get_config:
            mock_config = RateLimitConfig(
                requests_per_minute=10,
                requests_per_hour=100,
                burst_limit=5
            )
            mock_get_config.return_value = mock_config
            
            limiter = system_limiter._get_user_limiter(self.test_user_guid, PERMISSION_LEVELS.USER)
            
            assert isinstance(limiter, UserRateLimiter)
            assert limiter.user_guid == self.test_user_guid
            assert self.test_user_guid in system_limiter._user_limiters
            mock_get_config.assert_called_once_with(PERMISSION_LEVELS.USER)
    
    def test_get_user_limiter_existing_user(self):
        """Test getting user limiter for existing user"""
        system_limiter = SystemRateLimiter()
        
        # Pre-populate with existing limiter
        existing_limiter = Mock()
        system_limiter._user_limiters[self.test_user_guid] = existing_limiter
        
        limiter = system_limiter._get_user_limiter(self.test_user_guid, PERMISSION_LEVELS.USER)
        
        assert limiter is existing_limiter
    
    @pytest.mark.asyncio
    async def test_check_global_limits_under_limit(self):
        """Test global limits check when under limit"""
        system_limiter = SystemRateLimiter()
        
        result = await system_limiter._check_global_limits()
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_check_global_limits_minute_exceeded(self):
        """Test global limits check when minute limit exceeded"""
        system_limiter = SystemRateLimiter()
        
        # Fill global minute window to exceed limit
        current_time = datetime.now(timezone.utc).timestamp()
        for _ in range(1001):  # Exceed 1000/minute limit
            system_limiter._global_minute_window.add_request(current_time)
        
        result = await system_limiter._check_global_limits()
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_check_global_limits_hour_exceeded(self):
        """Test global limits check when hour limit exceeded"""
        system_limiter = SystemRateLimiter()
        
        # Fill global hour window to exceed limit
        current_time = datetime.now(timezone.utc).timestamp()
        for _ in range(50001):  # Exceed 50000/hour limit
            system_limiter._global_hour_window.add_request(current_time)
        
        result = await system_limiter._check_global_limits()
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_get_user_rate_status_existing(self):
        """Test getting rate status for existing user"""
        system_limiter = SystemRateLimiter()
        
        # Add mock user limiter
        mock_limiter = Mock()
        mock_metrics = {'test': 'data'}
        mock_limiter.get_metrics.return_value = mock_metrics
        system_limiter._user_limiters[self.test_user_guid] = mock_limiter
        
        status = await system_limiter.get_user_rate_status(self.test_user_guid)
        
        assert status == mock_metrics
        mock_limiter.get_metrics.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_user_rate_status_nonexistent(self):
        """Test getting rate status for non-existent user"""
        system_limiter = SystemRateLimiter()
        
        status = await system_limiter.get_user_rate_status(self.test_user_guid)
        
        assert status is None
    
    @pytest.mark.asyncio
    async def test_get_system_rate_metrics(self):
        """Test getting system rate metrics"""
        system_limiter = SystemRateLimiter()
        
        # Add some mock data
        system_limiter._system_metrics['total_users']._value = 5
        system_limiter._system_metrics['global_requests']._value = 100
        system_limiter._system_metrics['global_rejections']._value = 10
        
        # Add mock user limiter
        mock_limiter = Mock()
        mock_limiter.get_metrics.return_value = {
            'user_guid': self.test_user_guid,
            'total_requests': 50,
            'rejected_requests': 5,
            'acceptance_rate': 0.9
        }
        system_limiter._user_limiters[self.test_user_guid] = mock_limiter
        
        metrics = await system_limiter.get_system_rate_metrics()
        
        assert 'system_metrics' in metrics
        assert 'global_usage' in metrics
        assert 'system_load' in metrics
        assert 'top_users' in metrics
        
        assert metrics['system_metrics']['total_users'] == 5
        assert metrics['system_metrics']['global_requests'] == 100
        assert metrics['system_metrics']['global_rejections'] == 10
        assert len(metrics['top_users']) == 1
    
    def test_calculate_system_load(self):
        """Test system load calculation"""
        system_limiter = SystemRateLimiter()
        
        # Test with moderate load
        global_usage = {
            'minute_count': 500,  # 50% of 1000 limit
            'hour_count': 25000   # 50% of 50000 limit
        }
        
        load = system_limiter._calculate_system_load(global_usage)
        
        assert load == 50.0  # Max of 50% and 50%
        
        # Test with high minute load
        global_usage = {
            'minute_count': 800,  # 80% of 1000 limit
            'hour_count': 10000   # 20% of 50000 limit
        }
        
        load = system_limiter._calculate_system_load(global_usage)
        
        assert load == 80.0  # Max of 80% and 20%
    
    @pytest.mark.asyncio
    async def test_get_top_users_by_usage(self):
        """Test getting top users by usage"""
        system_limiter = SystemRateLimiter()
        
        # Add mock user limiters with different usage
        mock_limiter1 = Mock()
        mock_limiter1.get_metrics.return_value = {
            'user_guid': self.test_user_guid,
            'total_requests': 100,
            'rejected_requests': 10,
            'acceptance_rate': 0.9
        }
        
        mock_limiter2 = Mock()
        mock_limiter2.get_metrics.return_value = {
            'user_guid': self.test_user_guid_2,
            'total_requests': 50,
            'rejected_requests': 5,
            'acceptance_rate': 0.9
        }
        
        system_limiter._user_limiters[self.test_user_guid] = mock_limiter1
        system_limiter._user_limiters[self.test_user_guid_2] = mock_limiter2
        
        top_users = await system_limiter._get_top_users_by_usage(limit=10)
        
        assert len(top_users) == 2
        # Should be sorted by total_requests descending
        assert top_users[0]['user_guid'] == self.test_user_guid
        assert top_users[0]['total_requests'] == 100
        assert top_users[1]['user_guid'] == self.test_user_guid_2
        assert top_users[1]['total_requests'] == 50
    
    @pytest.mark.asyncio
    async def test_reset_user_limits_success(self):
        """Test resetting user limits successfully"""
        system_limiter = SystemRateLimiter()
        
        # Add mock user limiter
        mock_limiter = Mock()
        mock_limiter.reset_counters = Mock()
        system_limiter._user_limiters[self.test_user_guid] = mock_limiter
        
        success = await system_limiter.reset_user_limits(self.test_user_guid)
        
        assert success is True
        mock_limiter.reset_counters.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_reset_user_limits_nonexistent(self):
        """Test resetting limits for non-existent user"""
        system_limiter = SystemRateLimiter()
        
        success = await system_limiter.reset_user_limits(self.test_user_guid)
        
        assert success is False
    
    @pytest.mark.asyncio
    async def test_remove_user_limiter_success(self):
        """Test removing user limiter successfully"""
        system_limiter = SystemRateLimiter()
        
        # Add mock user limiter
        system_limiter._user_limiters[self.test_user_guid] = Mock()
        
        success = await system_limiter.remove_user_limiter(self.test_user_guid)
        
        assert success is True
        assert self.test_user_guid not in system_limiter._user_limiters
    
    @pytest.mark.asyncio
    async def test_remove_user_limiter_nonexistent(self):
        """Test removing non-existent user limiter"""
        system_limiter = SystemRateLimiter()
        
        success = await system_limiter.remove_user_limiter(self.test_user_guid)
        
        assert success is False
    
    @pytest.mark.asyncio
    async def test_cleanup_inactive_limiters(self):
        """Test cleanup of inactive limiters"""
        system_limiter = SystemRateLimiter()
        
        # Add mock active limiter
        active_limiter = Mock()
        active_limiter._last_request_time = datetime.now(timezone.utc).timestamp() - 1800  # 30 min ago
        active_limiter.get_current_usage.return_value = {'minute_count': 5}  # Has activity
        
        # Add mock inactive limiter
        inactive_limiter = Mock()
        inactive_limiter._last_request_time = datetime.now(timezone.utc).timestamp() - 7200  # 2 hours ago
        inactive_limiter.get_current_usage.return_value = {'minute_count': 0}  # No activity
        
        system_limiter._user_limiters[self.test_user_guid] = active_limiter
        system_limiter._user_limiters[self.test_user_guid_2] = inactive_limiter
        
        await system_limiter._cleanup_inactive_limiters()
        
        # Active limiter should remain, inactive should be removed
        assert self.test_user_guid in system_limiter._user_limiters
        assert self.test_user_guid_2 not in system_limiter._user_limiters
    
    def test_get_limiter_count(self):
        """Test getting limiter count"""
        system_limiter = SystemRateLimiter()
        
        assert system_limiter.get_limiter_count() == 0
        
        # Add some limiters
        system_limiter._user_limiters[self.test_user_guid] = Mock()
        system_limiter._user_limiters[self.test_user_guid_2] = Mock()
        
        assert system_limiter.get_limiter_count() == 2
    
    @pytest.mark.asyncio
    async def test_apply_emergency_limits(self):
        """Test applying emergency limits"""
        system_limiter = SystemRateLimiter()
        
        # Add mock user limiter with original config
        mock_limiter = Mock()
        original_config = RateLimitConfig(
            requests_per_minute=10,
            requests_per_hour=100,
            burst_limit=5
        )
        mock_limiter.rate_config = original_config
        system_limiter._user_limiters[self.test_user_guid] = mock_limiter
        
        await system_limiter.apply_emergency_limits("medium")  # 50% reduction
        
        # Config should be reduced by 50%
        new_config = mock_limiter.rate_config
        assert new_config.requests_per_minute == 5  # 10 * 0.5
        assert new_config.requests_per_hour == 50   # 100 * 0.5
        assert new_config.burst_limit == 2         # max(1, 5 * 0.5)
    
    @pytest.mark.asyncio
    async def test_restore_normal_limits(self):
        """Test restoring normal limits"""
        system_limiter = SystemRateLimiter()
        
        # Add mock user limiter
        mock_limiter = Mock()
        system_limiter._user_limiters[self.test_user_guid] = mock_limiter
        
        with patch('managers.manager_users.ZairaUserManager') as mock_user_manager_class:
            mock_user_manager = Mock()
            mock_user = Mock()
            mock_user.rank = PERMISSION_LEVELS.USER
            mock_user_manager.find_user = AsyncMock(return_value=mock_user)
            mock_user_manager_class.get_instance.return_value = mock_user_manager
            
            with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_rate_limit_config') as mock_get_config:
                original_config = RateLimitConfig(
                    requests_per_minute=10,
                    requests_per_hour=100,
                    burst_limit=5
                )
                mock_get_config.return_value = original_config
                
                await system_limiter.restore_normal_limits()
                
                # Config should be restored
                assert mock_limiter.rate_config == original_config
                mock_get_config.assert_called_once_with(PERMISSION_LEVELS.USER)
    
    @pytest.mark.asyncio
    async def test_periodic_cleanup_runs(self):
        """Test that periodic cleanup task runs"""
        system_limiter = SystemRateLimiter()
        
        with patch.object(system_limiter, '_cleanup_inactive_limiters') as mock_cleanup:
            await system_limiter.setup()
            
            # Wait a short time to see if cleanup would run
            # (We can't wait 30 minutes in a test, so we'll just verify the task is created)
            assert system_limiter._cleanup_task is not None
            assert not system_limiter._cleanup_task.done()
    
    @pytest.mark.asyncio
    async def test_concurrent_user_limiter_access(self):
        """Test concurrent access to user limiters"""
        system_limiter = SystemRateLimiter()
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_rate_limit_config') as mock_get_config:
            mock_config = RateLimitConfig(
                requests_per_minute=10,
                requests_per_hour=100,
                burst_limit=5
            )
            mock_get_config.return_value = mock_config
            
            # Create multiple concurrent tasks trying to get the same user limiter
            async def get_limiter_task():
                return system_limiter._get_user_limiter(self.test_user_guid, PERMISSION_LEVELS.USER)
            
            tasks = [asyncio.create_task(get_limiter_task()) for _ in range(5)]
            limiters = await asyncio.gather(*tasks)
            
            # All tasks should get the same limiter instance
            first_limiter = limiters[0]
            for limiter in limiters[1:]:
                assert limiter is first_limiter
            
            # Only one limiter should be created
            assert len(system_limiter._user_limiters) == 1