from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from uuid import uuid4
import asyncio
from datetime import datetime, timezone, timedelta
import json

from managers.scheduled_requests.core.persistence import ScheduledRequestPersistenceManager
from managers.scheduled_requests.utils.exceptions import ScheduledRequestNotFoundError
from managers.scheduled_requests.utils.helpers import validate_guid, safe_json_serialize
from userprofiles.ZairaUser import <PERSON>airaUser, PERMISSION_LEVELS
from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest, ScheduleType

class TestScheduledRequestPersistenceManager:
    """Comprehensive test class for ScheduledRequestPersistenceManager"""
    
    def setup_method(self):
        """Set up test fixtures"""
        # Reset singleton instance for each test
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        self.test_user_guid = uuid4()
        self.test_scheduled_guid = uuid4()
        
        # Create test user
        self.test_user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            GUID=self.test_user_guid,
            device_GUID=uuid4()
        )
        
        # Create test scheduled request
        self.test_scheduled_request = Mock(spec=ScheduledZairaRequest)
        self.test_scheduled_request.user = self.test_user
        self.test_scheduled_request.scheduled_guid = self.test_scheduled_guid
        self.test_scheduled_request.schedule_prompt = "Test schedule prompt"
        self.test_scheduled_request.target_prompt = "Test target prompt"
        self.test_scheduled_request.delay_seconds = 300.0
        self.test_scheduled_request.start_delay_seconds = 60.0
        self.test_scheduled_request.schedule_type = Mock()
        self.test_scheduled_request.schedule_type.value = "once"
        self.test_scheduled_request.next_execution = datetime.now(timezone.utc) + timedelta(hours=1)
        self.test_scheduled_request.is_active = True
        self.test_scheduled_request.run_on_startup = False
        self.test_scheduled_request.calling_bot = Mock()
        self.test_scheduled_request.calling_bot.name = "TestBot"
        self.test_scheduled_request.original_physical_message = None
        self.test_scheduled_request.get_schedule_info.return_value = {"type": "test", "data": "test_data"}
        self.test_scheduled_request.get_request_status.return_value = {"status": "pending"}
        
    def teardown_method(self):
        """Clean up after each test"""
        # Reset singleton
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
    
    def test_singleton_pattern(self):
        """Test that ScheduledRequestPersistenceManager follows singleton pattern"""
        manager1 = ScheduledRequestPersistenceManager.get_instance()
        manager2 = ScheduledRequestPersistenceManager.get_instance()
        
        assert manager1 is manager2
        assert isinstance(manager1, ScheduledRequestPersistenceManager)
    
    def test_manager_initialization(self):
        """Test manager initialization"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        assert hasattr(manager, '_db_available')
        assert hasattr(manager, '_setup_lock')
        assert manager._db_available is True
        assert isinstance(manager._setup_lock, asyncio.Lock)
    
    @pytest.mark.asyncio
    async def test_setup_success(self):
        """Test successful manager setup"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(manager, '_create_tables_and_indexes') as mock_create_tables:
            await ScheduledRequestPersistenceManager.setup()
            
            assert manager._initialized is True
            mock_create_tables.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_setup_already_initialized(self):
        """Test setup when already initialized"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        manager._initialized = True
        
        with patch.object(manager, '_create_tables_and_indexes') as mock_create_tables:
            await ScheduledRequestPersistenceManager.setup()
            
            # Should not call create_tables again
            mock_create_tables.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_setup_failure(self):
        """Test setup failure handling"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(manager, '_create_tables_and_indexes', side_effect=Exception("DB Error")):
            await ScheduledRequestPersistenceManager.setup()
            
            assert manager._initialized is False
            assert manager._db_available is False
    
    @pytest.mark.asyncio
    async def test_create_tables_and_indexes(self):
        """Test table and index creation"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query') as mock_execute:
            # Mock table check to return empty result (table doesn't exist)
            mock_execute.return_value = []
            
            with patch.object(manager, '_execute_single_operation') as mock_execute_single:
                await manager._create_tables_and_indexes()
                
                # Should call multiple operations for tables and indexes
                assert mock_execute_single.call_count > 5  # Main table + indexes + quota table + audit table
    
    @pytest.mark.asyncio
    async def test_create_tables_missing_columns(self):
        """Test table recreation when columns are missing"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query') as mock_execute:
            # Mock table check to return incomplete columns
            mock_execute.return_value = [
                {'column_name': 'scheduled_guid'},
                {'column_name': 'user_guid'}
                # Missing required columns
            ]
            
            with patch.object(manager, '_execute_single_operation') as mock_execute_single:
                await manager._create_tables_and_indexes()
                
                # Should drop and recreate table
                drop_calls = [call for call in mock_execute_single.call_args_list 
                             if 'DROP TABLE' in str(call)]
                assert len(drop_calls) > 0
    
    @pytest.mark.asyncio
    async def test_execute_single_operation_success(self):
        """Test successful single operation execution"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query') as mock_execute:
            mock_execute.return_value = [{'result': 'success'}]
            
            await manager._execute_single_operation("SELECT 1", ["param1"])
            
            mock_execute.assert_called_once_with("vectordb", "SELECT 1", ["param1"])
    
    @pytest.mark.asyncio
    async def test_execute_single_operation_no_params(self):
        """Test single operation execution without parameters"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query') as mock_execute:
            mock_execute.return_value = [{'result': 'success'}]
            
            await manager._execute_single_operation("CREATE TABLE test")
            
            mock_execute.assert_called_once_with("vectordb", "CREATE TABLE test")
    
    @pytest.mark.asyncio
    async def test_execute_single_operation_failure(self):
        """Test single operation execution failure"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', side_effect=Exception("DB Error")):
            with pytest.raises(Exception, match="DB Error"):
                await manager._execute_single_operation("SELECT 1")
    
    @pytest.mark.asyncio
    async def test_save_task_success(self):
        """Test successful task saving"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        manager._db_available = True
        
        with patch.object(manager, '_execute_single_operation') as mock_execute, \
             patch.object(manager, '_update_user_quota_tracking') as mock_update_quota, \
             patch.object(manager, '_log_audit_event') as mock_log_audit, \
             patch.object(manager, '_generate_security_hash', return_value="test_hash"):
            
            result = await manager.save_task(self.test_scheduled_request, "192.168.1.1")
            
            assert result is True
            mock_execute.assert_called_once()
            mock_update_quota.assert_called_once_with(str(self.test_user_guid), 'task_created')
            mock_log_audit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_save_task_db_unavailable(self):
        """Test task saving when database is unavailable"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        manager._db_available = False
        
        result = await manager.save_task(self.test_scheduled_request)
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_save_task_invalid_guid(self):
        """Test task saving with invalid GUID"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        manager._db_available = True
        
        # Create request with invalid GUID
        invalid_request = Mock(spec=ScheduledZairaRequest)
        invalid_request.user = Mock()
        invalid_request.user.GUID = "invalid-guid"
        invalid_request.scheduled_guid = "also-invalid"
        
        result = await manager.save_task(invalid_request)
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_save_task_database_error(self):
        """Test task saving with database error"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        manager._db_available = True
        
        with patch.object(manager, '_execute_single_operation', side_effect=Exception("DB Error")), \
             patch.object(manager, '_log_audit_event') as mock_log_audit:
            
            result = await manager.save_task(self.test_scheduled_request)
            
            assert result is False
            # Should log failed audit event
            mock_log_audit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_active_requests_user_filtered(self):
        """Test getting active requests filtered by user"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        manager._db_available = True
        
        mock_result = [{
            'scheduled_guid': str(self.test_scheduled_guid),
            'user_guid': str(self.test_user_guid),
            'schedule_prompt': 'Test prompt',
            'target_prompt': 'Test target',
            'security_hash': 'test_hash'
        }]
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', return_value=mock_result), \
             patch.object(manager, '_validate_security_hash', return_value=True):
            
            result = await manager.get_active_requests(str(self.test_user_guid))
            
            assert len(result) == 1
            assert result[0]['scheduled_guid'] == str(self.test_scheduled_guid)
    
    @pytest.mark.asyncio
    async def test_get_active_requests_invalid_user_guid(self):
        """Test getting active requests with invalid user GUID"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        manager._db_available = True
        
        result = await manager.get_active_requests("invalid-guid")
        
        assert result == []
    
    @pytest.mark.asyncio
    async def test_get_active_requests_system_wide(self):
        """Test getting all active requests (system-wide)"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        manager._db_available = True
        
        mock_result = [
            {
                'scheduled_guid': str(self.test_scheduled_guid),
                'user_guid': str(self.test_user_guid),
                'schedule_prompt': 'Test prompt 1',
                'target_prompt': 'Test target 1',
                'security_hash': 'test_hash1'
            },
            {
                'scheduled_guid': str(uuid4()),
                'user_guid': str(uuid4()),
                'schedule_prompt': 'Test prompt 2',
                'target_prompt': 'Test target 2',
                'security_hash': 'test_hash2'
            }
        ]
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', return_value=mock_result), \
             patch.object(manager, '_validate_security_hash', return_value=True):
            
            result = await manager.get_active_requests()
            
            assert len(result) == 2
    
    @pytest.mark.asyncio
    async def test_get_active_requests_security_hash_validation_failure(self):
        """Test getting active requests with security hash validation failure"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        manager._db_available = True
        
        mock_result = [{
            'scheduled_guid': str(self.test_scheduled_guid),
            'user_guid': str(self.test_user_guid),
            'schedule_prompt': 'Test prompt',
            'target_prompt': 'Test target',
            'security_hash': 'invalid_hash'
        }]
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', return_value=mock_result), \
             patch.object(manager, '_validate_security_hash', return_value=False):
            
            result = await manager.get_active_requests(str(self.test_user_guid))
            
            # Should filter out records with invalid security hash
            assert result == []
    
    @pytest.mark.asyncio
    async def test_get_active_requests_db_unavailable(self):
        """Test getting active requests when database is unavailable"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        manager._db_available = False
        
        result = await manager.get_active_requests(str(self.test_user_guid))
        
        assert result == []
    
    @pytest.mark.asyncio
    async def test_get_active_requests_database_error(self):
        """Test getting active requests with database error"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        manager._db_available = True
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', side_effect=Exception("DB Error")):
            result = await manager.get_active_requests(str(self.test_user_guid))
            
            assert result == []
    
    @pytest.mark.asyncio
    async def test_cancel_task_success(self):
        """Test successful task cancellation"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        # Mock ownership verification
        ownership_result = [{'user_guid': str(self.test_user_guid)}]
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', return_value=ownership_result), \
             patch.object(manager, '_execute_single_operation') as mock_execute, \
             patch.object(manager, '_update_user_quota_tracking') as mock_update_quota, \
             patch.object(manager, '_log_audit_event') as mock_log_audit:
            
            result = await manager.cancel_task(
                str(self.test_scheduled_guid), 
                str(self.test_user_guid), 
                "Test cancellation"
            )
            
            assert result is True
            mock_execute.assert_called_once()
            mock_update_quota.assert_called_once_with(str(self.test_user_guid), 'task_cancelled')
            mock_log_audit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cancel_task_invalid_guid(self):
        """Test task cancellation with invalid GUID"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        result = await manager.cancel_task("invalid-guid", str(self.test_user_guid), "Test")
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_cancel_task_unauthorized(self):
        """Test task cancellation by unauthorized user"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        # Mock ownership verification to return different user
        different_user_guid = str(uuid4())
        ownership_result = [{'user_guid': different_user_guid}]
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', return_value=ownership_result), \
             patch.object(manager, '_log_audit_event') as mock_log_audit:
            
            result = await manager.cancel_task(
                str(self.test_scheduled_guid), 
                str(self.test_user_guid), 
                "Test cancellation"
            )
            
            assert result is False
            # Should log unauthorized attempt
            mock_log_audit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cancel_task_not_found(self):
        """Test task cancellation when task not found"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', return_value=[]):
            result = await manager.cancel_task(
                str(self.test_scheduled_guid), 
                str(self.test_user_guid), 
                "Test cancellation"
            )
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_update_task_execution_stats_success(self):
        """Test successful task execution stats update"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(manager, '_execute_single_operation') as mock_execute:
            result = await manager.update_task_execution_stats(
                str(self.test_scheduled_guid), 
                str(self.test_user_guid), 
                2.5, 
                True
            )
            
            assert result is True
            mock_execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_task_execution_stats_failure(self):
        """Test task execution stats update for failed execution"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(manager, '_execute_single_operation') as mock_execute:
            result = await manager.update_task_execution_stats(
                str(self.test_scheduled_guid), 
                str(self.test_user_guid), 
                0.0, 
                False, 
                "Test error message"
            )
            
            assert result is True
            mock_execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_task_execution_stats_database_error(self):
        """Test task execution stats update with database error"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(manager, '_execute_single_operation', side_effect=Exception("DB Error")):
            result = await manager.update_task_execution_stats(
                str(self.test_scheduled_guid), 
                str(self.test_user_guid), 
                2.5, 
                True
            )
            
            assert result is False
    
    @pytest.mark.asyncio
    async def test_get_user_quota_info_existing(self):
        """Test getting quota info for existing user"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        mock_result = [{
            'user_guid': str(self.test_user_guid),
            'daily_tasks_used': 10,
            'concurrent_tasks_count': 2,
            'memory_usage_mb': 50.0,
            'quota_violations': 1
        }]
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', return_value=mock_result):
            result = await manager.get_user_quota_info(str(self.test_user_guid))
            
            assert result['user_guid'] == str(self.test_user_guid)
            assert result['daily_tasks_used'] == 10
            assert result['concurrent_tasks_count'] == 2
    
    @pytest.mark.asyncio
    async def test_get_user_quota_info_new_user(self):
        """Test getting quota info for new user"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', return_value=[]), \
             patch.object(manager, '_initialize_user_quota_tracking') as mock_init:
            
            result = await manager.get_user_quota_info(str(self.test_user_guid))
            
            assert result['user_guid'] == str(self.test_user_guid)
            assert result['daily_tasks_used'] == 0
            assert result['concurrent_tasks_count'] == 0
            mock_init.assert_called_once_with(str(self.test_user_guid))
    
    @pytest.mark.asyncio
    async def test_get_user_quota_info_invalid_guid(self):
        """Test getting quota info with invalid GUID"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        result = await manager.get_user_quota_info("invalid-guid")
        
        assert result == {}
    
    @pytest.mark.asyncio
    async def test_update_user_quota_tracking_task_created(self):
        """Test updating user quota tracking for task creation"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(manager, '_initialize_user_quota_tracking') as mock_init, \
             patch.object(manager, '_execute_single_operation') as mock_execute:
            
            await manager._update_user_quota_tracking(str(self.test_user_guid), 'task_created')
            
            mock_init.assert_called_once_with(str(self.test_user_guid))
            mock_execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_update_user_quota_tracking_task_completed(self):
        """Test updating user quota tracking for task completion"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(manager, '_initialize_user_quota_tracking') as mock_init, \
             patch.object(manager, '_execute_single_operation') as mock_execute:
            
            await manager._update_user_quota_tracking(str(self.test_user_guid), 'task_completed')
            
            mock_init.assert_called_once_with(str(self.test_user_guid))
            mock_execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_initialize_user_quota_tracking(self):
        """Test initializing user quota tracking"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(manager, '_execute_single_operation') as mock_execute:
            await manager._initialize_user_quota_tracking(str(self.test_user_guid))
            
            mock_execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_log_audit_event(self):
        """Test logging audit event"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(manager, '_execute_single_operation') as mock_execute:
            await manager._log_audit_event(
                str(self.test_user_guid), 
                str(self.test_scheduled_guid), 
                'CREATE', 
                {'test': 'data'}, 
                '192.168.1.1', 
                True
            )
            
            mock_execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_log_audit_event_with_error(self):
        """Test logging audit event with error"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(manager, '_execute_single_operation') as mock_execute:
            await manager._log_audit_event(
                str(self.test_user_guid), 
                str(self.test_scheduled_guid), 
                'CREATE_FAILED', 
                {'error': 'Test error'}, 
                '192.168.1.1', 
                False, 
                'Test error message'
            )
            
            mock_execute.assert_called_once()
    
    def test_generate_security_hash(self):
        """Test security hash generation"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        hash1 = manager._generate_security_hash(self.test_scheduled_request, str(self.test_user_guid))
        hash2 = manager._generate_security_hash(self.test_scheduled_request, str(self.test_user_guid))
        
        # Same input should produce same hash
        assert hash1 == hash2
        assert len(hash1) == 32  # Should be truncated to 32 chars
        
        # Different data should produce different hash
        different_request = Mock(spec=ScheduledZairaRequest)
        different_request.scheduled_guid = uuid4()
        different_request.schedule_prompt = "Different prompt"
        different_request.target_prompt = "Different target"
        
        hash3 = manager._generate_security_hash(different_request, str(self.test_user_guid))
        assert hash1 != hash3
    
    def test_validate_security_hash_success(self):
        """Test successful security hash validation"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        row_data = {
            'scheduled_guid': str(self.test_scheduled_guid),
            'user_guid': str(self.test_user_guid),
            'schedule_prompt': 'Test prompt',
            'target_prompt': 'Test target',
            'security_hash': None  # Will be calculated
        }
        
        # Calculate expected hash
        hash_data = f"{row_data['scheduled_guid']}{row_data['user_guid']}{row_data['schedule_prompt']}{row_data['target_prompt']}"
        import hashlib
        expected_hash = hashlib.sha256(hash_data.encode('utf-8')).hexdigest()[:32]
        row_data['security_hash'] = expected_hash
        
        result = manager._validate_security_hash(row_data)
        
        assert result is True
    
    def test_validate_security_hash_failure(self):
        """Test security hash validation failure"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        row_data = {
            'scheduled_guid': str(self.test_scheduled_guid),
            'user_guid': str(self.test_user_guid),
            'schedule_prompt': 'Test prompt',
            'target_prompt': 'Test target',
            'security_hash': 'invalid_hash'
        }
        
        result = manager._validate_security_hash(row_data)
        
        assert result is False
    
    def test_validate_security_hash_no_hash(self):
        """Test security hash validation with no hash (legacy record)"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        row_data = {
            'scheduled_guid': str(self.test_scheduled_guid),
            'user_guid': str(self.test_user_guid),
            'schedule_prompt': 'Test prompt',
            'target_prompt': 'Test target',
            'security_hash': None
        }
        
        result = manager._validate_security_hash(row_data)
        
        assert result is True  # Legacy records without hash are accepted
    
    def test_validate_security_hash_error(self):
        """Test security hash validation with error"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        # Invalid row data that will cause error
        row_data = {
            'scheduled_guid': None,  # Will cause error in hash calculation
            'user_guid': str(self.test_user_guid),
            'security_hash': 'some_hash'
        }
        
        result = manager._validate_security_hash(row_data)
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_recreate_task_from_data(self):
        """Test recreating task from data"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        task_data = {
            'scheduled_guid': str(self.test_scheduled_guid),
            'user_guid': str(self.test_user_guid),
            'schedule_prompt': 'Test prompt',
            'target_prompt': 'Test target'
        }
        
        # Legacy manager has been removed - this test now uses direct method
        result = await manager._recreate_task_from_data(task_data)
        
        assert result is not None  # Should recreate task successfully
    
    @pytest.mark.asyncio
    async def test_cleanup_old_records(self):
        """Test cleaning up old records"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        mock_result = [{'count': 5}]  # Mock 5 records deleted
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', return_value=mock_result), \
             patch.object(manager, '_execute_single_operation') as mock_execute:
            
            result = await manager.cleanup_old_records(30)
            
            assert result == 5
            # Should call both delete queries (requests and audit)
            mock_execute.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_cleanup_old_records_error(self):
        """Test cleanup old records with error"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', side_effect=Exception("DB Error")):
            result = await manager.cleanup_old_records(30)
            
            assert result == 0
    
    @pytest.mark.asyncio
    async def test_concurrent_setup_calls(self):
        """Test concurrent setup calls don't cause issues"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(manager, '_create_tables_and_indexes') as mock_create_tables:
            # Create multiple concurrent setup tasks
            tasks = [asyncio.create_task(ScheduledRequestPersistenceManager.setup()) for _ in range(5)]
            await asyncio.gather(*tasks)
            
            # Should only call create_tables once due to lock
            assert mock_create_tables.call_count == 1
            assert manager._initialized is True
    
    @pytest.mark.asyncio
    async def test_save_task_with_timezone_aware_datetime(self):
        """Test saving task with timezone-aware datetime"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        manager._db_available = True
        
        # Set timezone-aware datetime
        self.test_scheduled_request.next_execution = datetime.now(timezone.utc)
        
        with patch.object(manager, '_execute_single_operation') as mock_execute, \
             patch.object(manager, '_update_user_quota_tracking'), \
             patch.object(manager, '_log_audit_event'), \
             patch.object(manager, '_generate_security_hash', return_value="test_hash"):
            
            result = await manager.save_task(self.test_scheduled_request)
            
            assert result is True
            mock_execute.assert_called_once()
            
            # Verify datetime was converted to naive UTC
            call_args = mock_execute.call_args[0][1]  # Get the parameters
            next_execution_param = call_args[7]  # Index of next_execution parameter
            assert next_execution_param.tzinfo is None  # Should be naive
    
    @pytest.mark.asyncio
    async def test_save_task_with_none_next_execution(self):
        """Test saving task with None next_execution"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        manager._db_available = True
        
        # Set next_execution to None
        self.test_scheduled_request.next_execution = None
        
        with patch.object(manager, '_execute_single_operation') as mock_execute, \
             patch.object(manager, '_update_user_quota_tracking'), \
             patch.object(manager, '_log_audit_event'), \
             patch.object(manager, '_generate_security_hash', return_value="test_hash"):
            
            result = await manager.save_task(self.test_scheduled_request)
            
            assert result is True
            mock_execute.assert_called_once()
            
            # Verify next_execution parameter is None
            call_args = mock_execute.call_args[0][1]  # Get the parameters
            next_execution_param = call_args[7]  # Index of next_execution parameter
            assert next_execution_param is None