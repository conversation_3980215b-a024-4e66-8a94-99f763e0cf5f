#!/usr/bin/env python3
"""
Simple WhatsApp test after account registration
"""

import asyncio
import aiohttp
import json
from config import WHATSAPP_PHONE_NUMBER_ID, WHATSAPP_ACCESS_TOKEN, WHATSAPP_RECIPIENT_WAID

async def test_simple_message():
    """Test basic WhatsApp message sending after registration"""
    
    url = f"https://graph.facebook.com/v18.0/{WHATSAPP_PHONE_NUMBER_ID}/messages"
    
    payload = {
        "messaging_product": "whatsapp",
        "recipient_type": "individual",
        "to": WHATSAPP_RECIPIENT_WAID,
        "type": "text",
        "text": {
            "preview_url": False,
            "body": "Hi my name is <PERSON><PERSON><PERSON>, how can I assist you today?"
        }
    }
    
    headers = {
        "Content-Type": "application/json", 
        "Authorization": f"Bearer {WHATSAPP_ACCESS_TOKEN}"
    }
    
    async with aiohttp.ClientSession() as session:
        async with session.post(url, data=json.dumps(payload), headers=headers) as response:
            response_text = await response.text()
            
            print(f"Status: {response.status}")
            print(f"Response: {response_text}")
            
            if response.status == 200:
                print("SUCCESS! Message sent to WhatsApp")
            else:
                print("FAILED! Check the error above")

if __name__ == "__main__":
    asyncio.run(test_simple_message())