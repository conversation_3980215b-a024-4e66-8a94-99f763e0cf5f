from imports import *
from os import path as os_path
from typing import Dict, Any

class DashboardTemplateLoader:
    """Template loader for dashboard HTML templates and static assets"""
    
    def __init__(self):
        self.template_dir = os_path.join(os_path.dirname(__file__), 'templates')
        self.static_dir = os_path.join(os_path.dirname(__file__), 'static')
    
    def load_template(self, template_name: str) -> str:
        """Load HTML template from templates directory"""
        template_path = os_path.join(self.template_dir, template_name)
        
        if not os_path.exists(template_path):
            raise FileNotFoundError(f"Template not found: {template_path}")
        
        with open(template_path, 'r', encoding='utf-8') as f:
            return f.read()
    
    def render_template(self, template_name: str, **context) -> str:
        """
        Load template and render with context variables.
        Uses simple string formatting for template variables.
        """
        template_content = self.load_template(template_name)
        
        # Simple template variable substitution
        try:
            return template_content.format(**context)
        except KeyError as e:
            LogFire.log("ERROR", f"Missing template variable: {e}")
            return template_content
    
    
    def get_static_url(self, static_path: str) -> str:
        """Generate URL for static assets"""
        return f"/dashboard/static/{static_path}"
    

# Singleton instance
_template_loader = None

def get_dashboard_template_loader() -> DashboardTemplateLoader:
    """Get singleton instance of dashboard template loader"""
    global _template_loader
    if _template_loader is None:
        _template_loader = DashboardTemplateLoader()
    return _template_loader