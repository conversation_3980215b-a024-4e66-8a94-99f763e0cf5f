#!/usr/bin/env python3
"""
Real Bot Integration System Tests

This module contains comprehensive tests for the AskZaira bot system,
including automated testing bot functionality, human-in-the-loop workflows,
and bot interaction patterns.

Tests cover:
- Automated bot response generation
- Human-in-the-loop workflow automation
- Bot approval and decision-making
- Multi-bot interaction scenarios
- Bot error handling and recovery
- Bot integration with system components

All tests use the full AskZaira system to ensure production-like behavior.
"""

from tests.test_real.base_real_test import BaseRealTest, requires_openai_api
import pytest
import asyncio
from typing import Dict, Any, List
from uuid import uuid4

from endpoints.testing_endpoint import MyBot_Testing
from userprofiles.ZairaUser import ZairaUser, PERMISSION_LEVELS
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest


@pytest.mark.asyncio
class TestRealBot(BaseRealTest):
    """
    Test suite for real bot system functionality.
    
    This class tests the complete bot interaction pipeline from automated responses
    to complex workflow management, using the full AskZaira system.
    """
    
    # Bot-specific test configuration
    BOT_TEST_SCENARIOS = [
        ("email_approval", "Ik heb de volgende mail opgesteld. Wil je dat ik deze goedkeur voor verzending? (j/n)"),
        ("email_address_query", "Het is mij niet duidelijk naar welk email adres de mail gestuurd moet worden?"),
        ("sender_email_query", "Je e-mail adres is nog niet bekend bij ons. Van welk e-mailadres moet de mail verzonden worden?"),
        ("general_approval", "Approve this action? (yes/no)"),
        ("confirmation_request", "Wil je doorgaan met deze actie? (ja/nee)"),
        ("permission_request", "Do you want to proceed with this operation?")
    ]
    
    EXPECTED_BOT_RESPONSES = {
        "email_approval": "j",
        "email_address_query": "<EMAIL>",
        "sender_email_query": "<EMAIL>",
        "general_approval": "ja",
        "confirmation_request": "ja",
        "permission_request": "ja"
    }
    
    async def setup_bot_system(self) -> bool:
        """Set up the bot system for testing"""
        return await self.setup_real_system(
            test_category="bot",
            create_test_docs=True,
            custom_docs=[
                ("bot_instructions.txt", "Bot interaction guidelines and response patterns"),
                ("approval_workflows.txt", "Human-in-the-loop approval workflow documentation"),
                ("bot_responses.txt", "Standard bot response templates and examples"),
                ("interaction_patterns.txt", "Common bot interaction patterns and scenarios"),
                ("error_handling.txt", "Bot error handling and recovery procedures"),
                ("automation_guidelines.txt", "Guidelines for automated bot testing")
            ]
        )
    
    @requires_openai_api()
    async def test_automated_bot_responses(self):
        """Test automated bot response generation for various scenarios"""
        # Setup
        setup_success = await self.setup_bot_system()
        if not setup_success:
            pytest.skip("Could not initialize bot system")
        
        # Create testing bot
        bot = MyBot_Testing(None, "Testing")
        
        # Test various response scenarios
        successful_responses = 0
        
        for scenario_name, request in self.BOT_TEST_SCENARIOS:
            LogFire.log("DEBUG", f"[{self.test_name}] Testing {scenario_name}")
            LogFire.log("DEBUG", f"[{self.test_name}] Request: {request}")
            
            # Test response generation
            response = await bot._generate_test_response(request)
            
            LogFire.log("DEBUG", f"[{self.test_name}] Response: {response}")
            
            # Verify response is appropriate
            assert response is not None, f"Response should not be None for {scenario_name}"
            assert len(response.strip()) > 0, f"Response should not be empty for {scenario_name}"
            
            # Check specific response patterns
            if scenario_name in self.EXPECTED_BOT_RESPONSES:
                expected = self.EXPECTED_BOT_RESPONSES[scenario_name]
                if response.lower() == expected.lower():
                    successful_responses += 1
                    LogFire.log("DEBUG", f"[{self.test_name}] {scenario_name} response correct")
                else:
                    LogFire.log("DEBUG", f"[{self.test_name}] {scenario_name} response unexpected: got '{response}', expected '{expected}'")
        
        # Verify response accuracy
        success_rate = successful_responses / len(self.BOT_TEST_SCENARIOS)
        assert success_rate >= 0.7, f"Bot response accuracy too low: {success_rate:.2f}"
        
        LogFire.log("DEBUG", f"[{self.test_name}] Automated bot responses test passed")
        
        return {
            "successful_responses": successful_responses,
            "total_scenarios": len(self.BOT_TEST_SCENARIOS),
            "success_rate": success_rate
        }
    
    @requires_openai_api()
    async def test_bot_fallback_responses(self):
        """Test bot fallback response system when LLM is unavailable"""
        # Setup
        setup_success = await self.setup_bot_system()
        if not setup_success:
            pytest.skip("Could not initialize bot system")
        
        # Create testing bot
        bot = MyBot_Testing(None, "Testing")
        
        # Test fallback scenarios
        fallback_scenarios = [
            ("Wil je dat ik deze goedkeur voor verzending? (j/n)", "email_approval"),
            ("email address needed", "email_address"),
            ("Do you want to continue? (yes/no)", "general_approval"),
            ("sender email address required", "sender_email"),
            ("approve this action", "general_approval")
        ]
        
        successful_fallbacks = 0
        
        for request, scenario_type in fallback_scenarios:
            LogFire.log("DEBUG", f"[{self.test_name}] Testing fallback for {scenario_type}")
            
            # Test fallback response
            response = bot._get_fallback_response(request)
            
            LogFire.log("DEBUG", f"[{self.test_name}] Fallback response: {response}")
            
            # Verify response is appropriate
            assert response is not None, f"Fallback response should not be None for {scenario_type}"
            assert len(response.strip()) > 0, f"Fallback response should not be empty for {scenario_type}"
            
            # Check response patterns
            if scenario_type == "email_approval" and "goedkeur" in request.lower():
                if response.lower() == "j":
                    successful_fallbacks += 1
            elif scenario_type == "email_address" and "email" in request.lower():
                if "@" in response:
                    successful_fallbacks += 1
            elif scenario_type == "general_approval":
                if response.lower() in ["ja", "yes"]:
                    successful_fallbacks += 1
            elif scenario_type == "sender_email" and "sender" in request.lower():
                if "@" in response:
                    successful_fallbacks += 1
        
        # Verify fallback reliability
        fallback_rate = successful_fallbacks / len(fallback_scenarios)
        assert fallback_rate >= 0.6, f"Bot fallback reliability too low: {fallback_rate:.2f}"
        
        LogFire.log("DEBUG", f"[{self.test_name}] Bot fallback responses test passed")
        
        return {
            "successful_fallbacks": successful_fallbacks,
            "total_scenarios": len(fallback_scenarios),
            "fallback_rate": fallback_rate
        }
    
    @requires_openai_api()
    async def test_human_in_the_loop_automation(self):
        """Test human-in-the-loop automation with bot responses"""
        # Setup
        setup_success = await self.setup_bot_system()
        if not setup_success:
            pytest.skip("Could not initialize bot system")
        
        user = await self.create_test_user(email="<EMAIL>")
        bot = self.create_test_bot()
        
        # Test human-in-the-loop workflow
        query = "send a test <NAME_EMAIL>"
        result = await self.execute_and_monitor_task(user, query, bot, timeout=30)
        
        # Verify task success
        self.assert_task_success(result, max_execution_time=30)
        
        # Check for human-in-the-loop automation
        hitl_indicators = []
        for task_detail in result["task_details"]:
            call_trace = task_detail.get("call_trace", [])
            call_trace_str = " ".join(str(call).lower() for call in call_trace)
            
            if "email" in call_trace_str:
                hitl_indicators.append("email_workflow")
            if "approval" in call_trace_str or "goedkeur" in call_trace_str:
                hitl_indicators.append("approval_workflow")
        
        assert len(hitl_indicators) >= 1, f"Human-in-the-loop automation not detected: {hitl_indicators}"
        LogFire.log("DEBUG", f"[{self.test_name}] Human-in-the-loop automation test passed")
        
        return {
            "hitl_indicators": hitl_indicators,
            "execution_time": result["execution_time"]
        }
    
    @requires_openai_api()
    async def test_bot_name_behavior(self):
        """Test that bot behavior is controlled by name parameter"""
        # Setup
        setup_success = await self.setup_bot_system()
        if not setup_success:
            pytest.skip("Could not initialize bot system")
        
        # Test with "Testing" name (should provide automated responses)
        testing_bot = MyBot_Testing(None, "Testing")
        assert testing_bot.name == "Testing"
        
        # Test with different name (should not provide automated responses)
        other_bot = MyBot_Testing(None, "Other")
        assert other_bot.name == "Other"
        
        # Test response generation
        test_request = "Wil je dat ik deze goedkeur voor verzending? (j/n)"
        
        testing_response = await testing_bot._generate_test_response(test_request)
        other_response = await other_bot._generate_test_response(test_request)
        
        # Both should generate responses, but behavior may differ
        assert testing_response is not None
        assert other_response is not None
        
        LogFire.log("DEBUG", f"[{self.test_name}] Testing bot response: {testing_response}")
        LogFire.log("DEBUG", f"[{self.test_name}] Other bot response: {other_response}")
        
        LogFire.log("DEBUG", f"[{self.test_name}] Bot name behavior test passed")
        
        return {
            "testing_bot_name": testing_bot.name,
            "other_bot_name": other_bot.name,
            "testing_response": testing_response,
            "other_response": other_response
        }
    
    @requires_openai_api()
    async def test_bot_error_handling(self):
        """Test bot error handling and recovery mechanisms"""
        # Setup
        setup_success = await self.setup_bot_system()
        if not setup_success:
            pytest.skip("Could not initialize bot system")
        
        # Create testing bot
        bot = MyBot_Testing(None, "Testing")
        
        # Test error scenarios
        error_scenarios = [
            ("", "empty_request"),
            ("Invalid request with no context", "invalid_request"),
            ("Mixed language request with unclear intent", "unclear_request"),
            ("Very long request " + "x" * 1000, "long_request")
        ]
        
        handled_errors = 0
        
        for request, error_type in error_scenarios:
            LogFire.log("DEBUG", f"[{self.test_name}] Testing error handling for {error_type}")
            
            try:
                response = await bot._generate_test_response(request)
                
                # Check if error was handled gracefully
                if response is not None and len(response.strip()) > 0:
                    handled_errors += 1
                    LogFire.log("DEBUG", f"[{self.test_name}] {error_type} handled gracefully: {response}")
                else:
                    LogFire.log("DEBUG", f"[{self.test_name}] {error_type} not handled properly")
            
            except Exception as e:
                LogFire.log("DEBUG", f"[{self.test_name}] {error_type} raised exception: {e}")
                # This is acceptable for some error cases
        
        # Verify error handling
        error_handling_rate = handled_errors / len(error_scenarios)
        assert error_handling_rate >= 0.5, f"Bot error handling rate too low: {error_handling_rate:.2f}"
        
        LogFire.log("DEBUG", f"[{self.test_name}] Bot error handling test passed")
        
        return {
            "handled_errors": handled_errors,
            "total_scenarios": len(error_scenarios),
            "error_handling_rate": error_handling_rate
        }
    
    @requires_openai_api()
    async def test_bot_integration_with_workflows(self):
        """Test bot integration with different system workflows"""
        # Setup
        setup_success = await self.setup_bot_system()
        if not setup_success:
            pytest.skip("Could not initialize bot system")
        
        user = await self.create_test_user(email="<EMAIL>")
        bot = self.create_test_bot()
        
        # Test different workflow types
        workflow_tests = [
            ("send an <NAME_EMAIL>", "email_workflow"),
            ("tell me about your data", "data_workflow"),
            ("plan a meeting for tomorrow", "agenda_workflow")
        ]
        
        successful_integrations = 0
        
        for query, workflow_type in workflow_tests:
            LogFire.log("DEBUG", f"[{self.test_name}] Testing {workflow_type} integration")
            
            result = await self.execute_and_monitor_task(user, query, bot, timeout=25)
            
            # Check if workflow completed successfully
            if result["success"]:
                successful_integrations += 1
                LogFire.log("DEBUG", f"[{self.test_name}] {workflow_type} integration successful")
            else:
                LogFire.log("DEBUG", f"[{self.test_name}] {workflow_type} integration failed")
        
        # Verify integration success
        integration_rate = successful_integrations / len(workflow_tests)
        assert integration_rate >= 0.6, f"Bot workflow integration rate too low: {integration_rate:.2f}"
        
        LogFire.log("DEBUG", f"[{self.test_name}] Bot integration with workflows test passed")
        
        return {
            "successful_integrations": successful_integrations,
            "total_workflows": len(workflow_tests),
            "integration_rate": integration_rate
        }
    
    @requires_openai_api()
    async def test_bot_response_consistency(self):
        """Test consistency of bot responses across multiple requests"""
        # Setup
        setup_success = await self.setup_bot_system()
        if not setup_success:
            pytest.skip("Could not initialize bot system")
        
        # Create testing bot
        bot = MyBot_Testing(None, "Testing")
        
        # Test response consistency
        test_request = "Wil je dat ik deze goedkeur voor verzending? (j/n)"
        responses = []
        
        # Make multiple requests
        for i in range(5):
            response = await bot._generate_test_response(test_request)
            responses.append(response)
            LogFire.log("DEBUG", f"[{self.test_name}] Response {i+1}: {response}")
        
        # Check consistency
        unique_responses = set(responses)
        consistency_rate = 1 - (len(unique_responses) - 1) / len(responses)
        
        LogFire.log("DEBUG", f"[{self.test_name}] Response consistency: {consistency_rate:.2f}")
        
        # For approval requests, responses should be consistent
        if "goedkeur" in test_request.lower():
            assert consistency_rate >= 0.8, f"Approval responses should be consistent: {consistency_rate:.2f}"
        
        LogFire.log("DEBUG", f"[{self.test_name}] Bot response consistency test passed")
        
        return {
            "responses": responses,
            "unique_responses": len(unique_responses),
            "consistency_rate": consistency_rate
        }
    
    @requires_openai_api()
    async def test_bot_multi_language_support(self):
        """Test bot support for multiple languages"""
        # Setup
        setup_success = await self.setup_bot_system()
        if not setup_success:
            pytest.skip("Could not initialize bot system")
        
        # Create testing bot
        bot = MyBot_Testing(None, "Testing")
        
        # Test multi-language scenarios
        language_tests = [
            ("Wil je dat ik deze goedkeur voor verzending? (j/n)", "dutch"),
            ("Do you want to approve this email? (yes/no)", "english"),
            ("Approve this action? (y/n)", "english_short"),
            ("E-mail goedkeuring nodig", "dutch_informal")
        ]
        
        successful_languages = 0
        
        for request, language_type in language_tests:
            LogFire.log("DEBUG", f"[{self.test_name}] Testing {language_type} support")
            
            response = await bot._generate_test_response(request)
            
            LogFire.log("DEBUG", f"[{self.test_name}] {language_type} response: {response}")
            
            # Check if response is appropriate
            if response is not None and len(response.strip()) > 0:
                successful_languages += 1
                LogFire.log("DEBUG", f"[{self.test_name}] {language_type} handled successfully")
            else:
                LogFire.log("DEBUG", f"[{self.test_name}] {language_type} not handled properly")
        
        # Verify language support
        language_rate = successful_languages / len(language_tests)
        assert language_rate >= 0.7, f"Bot language support rate too low: {language_rate:.2f}"
        
        LogFire.log("DEBUG", f"[{self.test_name}] Bot multi-language support test passed")
        
        return {
            "successful_languages": successful_languages,
            "total_languages": len(language_tests),
            "language_rate": language_rate
        }
    
    @requires_openai_api()
    async def test_bot_performance_metrics(self):
        """Test bot performance metrics and response times"""
        # Setup
        setup_success = await self.setup_bot_system()
        if not setup_success:
            pytest.skip("Could not initialize bot system")
        
        # Create testing bot
        bot = MyBot_Testing(None, "Testing")
        
        # Test performance metrics
        import time
        performance_tests = [
            "Wil je dat ik deze goedkeur voor verzending? (j/n)",
            "Het is mij niet duidelijk naar welk email adres de mail gestuurd moet worden?",
            "Approve this action? (yes/no)",
            "Do you want to continue with this operation?"
        ]
        
        response_times = []
        
        for request in performance_tests:
            start_time = time.time()
            response = await bot._generate_test_response(request)
            end_time = time.time()
            
            response_time = end_time - start_time
            response_times.append(response_time)
            
            LogFire.log("DEBUG", f"[{self.test_name}] Response time: {response_time:.3f}s for '{request[:30]}...'")
        
        # Calculate performance metrics
        avg_response_time = sum(response_times) / len(response_times)
        max_response_time = max(response_times)
        min_response_time = min(response_times)
        
        LogFire.log("DEBUG", f"[{self.test_name}] Average response time: {avg_response_time:.3f}s")
        LogFire.log("DEBUG", f"[{self.test_name}] Max response time: {max_response_time:.3f}s")
        LogFire.log("DEBUG", f"[{self.test_name}] Min response time: {min_response_time:.3f}s")
        
        # Performance assertions
        assert avg_response_time < 5.0, f"Average response time too slow: {avg_response_time:.3f}s"
        assert max_response_time < 10.0, f"Max response time too slow: {max_response_time:.3f}s"
        
        LogFire.log("DEBUG", f"[{self.test_name}] Bot performance metrics test passed")
        
        return {
            "avg_response_time": avg_response_time,
            "max_response_time": max_response_time,
            "min_response_time": min_response_time,
            "total_tests": len(performance_tests)
        }


# Additional utility functions specific to bot testing
def get_bot_test_data():
    """Get test data specifically for bot testing"""
    return {
        "approval_patterns": [
            "goedkeur",
            "verzending",
            "j/n",
            "approve",
            "yes/no",
            "y/n"
        ],
        "email_patterns": [
            "email",
            "adres",
            "address",
            "mailadres",
            "e-mailadres",
            "sender"
        ],
        "response_types": [
            "approval",
            "email_address",
            "confirmation",
            "permission",
            "decision",
            "validation"
        ]
    }


if __name__ == "__main__":
    # Run tests individually for debugging
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "debug":
        # Debug mode - run a single test
        test_instance = TestRealBot()
        test_instance.setup_method()
        
        asyncio.run(test_instance.test_automated_bot_responses())
        LogFire.log("DEBUG", "Debug test completed")
    else:
        # Normal pytest execution
        pytest.main([__file__, "-v"], severity="debug")