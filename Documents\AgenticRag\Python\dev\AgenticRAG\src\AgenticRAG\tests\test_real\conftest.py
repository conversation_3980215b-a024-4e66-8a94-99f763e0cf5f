#!/usr/bin/env python3
"""
pytest configuration and hooks for test_real framework.

This module provides pytest hooks that ensure proper cleanup of servers
and resources when tests are interrupted or cancelled via VS Code.

Key Features:
- Session-level server cleanup that runs regardless of interruption method
- Automatic port cleanup before test execution
- Finalizers that run even on SIGKILL/force termination
- Integration with global server registry from APIEndpoint
"""

import pytest
import asyncio
import os
import sys
from pathlib import Path

# Add the project root to the path
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from endpoints.api_endpoint import APIEndpoint
from tests.test_real.base_real_test import BaseRealTest
from managers.manager_logfire import LogFire


@pytest.fixture(scope="session", autouse=True)
def cleanup_servers_on_exit(request):
    """
    Session-level fixture that ensures server cleanup runs no matter how tests end.
    
    This uses pytest's addfinalizer mechanism which runs even if the test session
    is interrupted, providing more reliable cleanup than atexit handlers.
    """
    def cleanup_all_servers():
        """Cleanup function that runs at session end"""
        try:
            LogFire.log("DEBUG", "pytest finalizer: Starting server cleanup...")
            
            # Run the async cleanup in a new event loop
            try:
                loop = asyncio.get_running_loop()
                # If there's already a running loop, create a task
                loop.create_task(APIEndpoint.cleanup_all_servers())
            except RuntimeError:
                # No running loop, create a new one
                asyncio.run(APIEndpoint.cleanup_all_servers())
            
            # Also run the existing port cleanup as backup
            BaseRealTest._check_and_cleanup_ports()
            
            LogFire.log("DEBUG", "pytest finalizer: Server cleanup completed")
            
        except Exception as e:
            LogFire.log("ERROR", f"pytest finalizer: Error during server cleanup: {e}")
            # Don't raise exceptions from finalizers
    
    # Register the cleanup function with pytest
    request.addfinalizer(cleanup_all_servers)
    
    LogFire.log("DEBUG", "pytest: Server cleanup finalizer registered")


def pytest_runtest_setup(item):
    """
    Hook that runs before each test.
    
    Performs pre-test cleanup to ensure clean state, especially important
    when previous test runs were interrupted.
    """
    try:
        LogFire.log("DEBUG", f"pytest setup: Preparing test {item.name}")
        
        # Check and cleanup any orphaned servers/ports before starting test
        BaseRealTest._check_and_cleanup_ports()
        
        LogFire.log("DEBUG", f"pytest setup: Test {item.name} ready to start")
        
    except Exception as e:
        LogFire.log("ERROR", f"pytest setup: Error preparing test {item.name}: {e}")
        # Don't fail the test due to setup issues, just log them


def pytest_runtest_teardown(item, nextitem):
    """
    Hook that runs after each test.
    
    Provides per-test cleanup for server resources.
    """
    try:
        LogFire.log("DEBUG", f"pytest teardown: Cleaning up after test {item.name}")
        
        # Cleanup servers started during this test
        try:
            loop = asyncio.get_running_loop()
            loop.create_task(APIEndpoint.cleanup_all_servers())
        except RuntimeError:
            asyncio.run(APIEndpoint.cleanup_all_servers())
        
        LogFire.log("DEBUG", f"pytest teardown: Test {item.name} cleanup completed")
        
    except Exception as e:
        LogFire.log("ERROR", f"pytest teardown: Error cleaning up test {item.name}: {e}")


def pytest_sessionfinish(session, exitstatus):
    """
    Hook that runs after all tests have completed or been interrupted.
    
    This provides final cleanup regardless of how the test session ended
    (normal completion, Ctrl+C, VS Code cancellation, etc.)
    """
    try:
        LogFire.log("DEBUG", f"pytest session finish: Test session ending with status {exitstatus}")
        
        # Final server cleanup
        try:
            loop = asyncio.get_running_loop()
            loop.create_task(APIEndpoint.cleanup_all_servers())
        except RuntimeError:
            asyncio.run(APIEndpoint.cleanup_all_servers())
        
        # Final port cleanup as backup
        BaseRealTest._check_and_cleanup_ports()
        
        LogFire.log("DEBUG", "pytest session finish: Final cleanup completed")
        
    except Exception as e:
        LogFire.log("ERROR", f"pytest session finish: Error during final cleanup: {e}")


def pytest_keyboard_interrupt(excinfo):
    """
    Hook that runs when Ctrl+C is pressed.
    
    This handles the KeyboardInterrupt case specifically.
    """
    try:
        LogFire.log("DEBUG", "pytest keyboard interrupt: Handling Ctrl+C interruption")
        
        # Immediate server cleanup on keyboard interrupt
        try:
            loop = asyncio.get_running_loop()
            loop.create_task(APIEndpoint.cleanup_all_servers())
        except RuntimeError:
            asyncio.run(APIEndpoint.cleanup_all_servers())
        
        LogFire.log("DEBUG", "pytest keyboard interrupt: Cleanup completed")
        
    except Exception as e:
        LogFire.log("ERROR", f"pytest keyboard interrupt: Error during cleanup: {e}")


def pytest_internalerror(excrepr, excinfo):
    """
    Hook that runs when pytest encounters an internal error.
    
    Ensures cleanup happens even on pytest internal failures.
    """
    try:
        LogFire.log("DEBUG", "pytest internal error: Handling internal error")
        
        # Cleanup on internal errors
        try:
            loop = asyncio.get_running_loop()
            loop.create_task(APIEndpoint.cleanup_all_servers())
        except RuntimeError:
            asyncio.run(APIEndpoint.cleanup_all_servers())
        
        LogFire.log("DEBUG", "pytest internal error: Cleanup completed")
        
    except Exception as e:
        LogFire.log("ERROR", f"pytest internal error: Error during cleanup: {e}")


# Export the cleanup function for manual use if needed
async def manual_server_cleanup():
    """
    Manual server cleanup function that can be called directly.
    
    Useful for debugging or manual cleanup operations.
    """
    LogFire.log("DEBUG", "Manual server cleanup: Starting...")
    
    await APIEndpoint.cleanup_all_servers()
    BaseRealTest._check_and_cleanup_ports()
    
    LogFire.log("DEBUG", "Manual server cleanup: Completed")