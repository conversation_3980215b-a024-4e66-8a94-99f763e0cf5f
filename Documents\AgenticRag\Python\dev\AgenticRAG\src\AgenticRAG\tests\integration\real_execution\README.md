# Real Execution Integration Tests

This folder contains integration tests that execute with REAL system components rather than mocks.

## Overview

Real execution tests validate actual system behavior by:
- Using real LLM calls and processing
- Executing actual supervisor workflows
- Testing real component interactions
- Validating end-to-end system functionality
- Measuring real performance characteristics

## Test Files

### Email and Agenda Real Tests
- `test_real_email_sending_execution.py` - Real email sending workflow execution
- `test_real_email_agenda_coordination.py` - Real email-agenda coordination workflows

### Data Processing Real Tests  
- `test_real_tell_me_about_your_data_execution.py` - Real data query and response execution

## Test Characteristics

### Execution Time
- **Typical Duration**: 60-90 seconds per test
- **Timeout Limits**: 90-120 seconds with graceful handling
- **Performance Monitoring**: Built-in execution time tracking

### System Requirements
- **LLM Access**: Requires configured OpenAI or other LLM service
- **Database Access**: Requires PostgreSQL and Qdrant connections
- **OAuth Services**: May require OAuth tokens for email/calendar
- **Vector Store**: Requires initialized vector database

### Cache Management
- **Result Caching**: Tests cache execution results to avoid redundant runs
- **Cache Clearing**: Automatic cache clearing between test sessions
- **Cache Keys**: Unique keys per test scenario and query type

## Running Real Execution Tests

### Run All Real Tests
```bash
../../.venv/Scripts/pytest.exe tests/integration/real_execution/ -v -s
```

### Run Specific Real Tests
```bash
# Email sending real execution
../../.venv/Scripts/pytest.exe tests/integration/real_execution/test_real_email_sending_execution.py -v -s

# Email-agenda coordination real execution  
../../.venv/Scripts/pytest.exe tests/integration/real_execution/test_real_email_agenda_coordination.py -v -s

# Data query real execution
../../.venv/Scripts/pytest.exe tests/integration/real_execution/test_real_tell_me_about_your_data_execution.py -v -s
```

### Important Flags
- `-v`: Verbose output to see test progress
- `-s`: Show print statements (important for real execution monitoring)
- `--tb=short`: Shorter tracebacks for cleaner output

## Test Patterns

### System Setup
```python
async def _setup_real_system(self):
    """Set up the REAL system components for testing"""
    # Initialize real system components
    # Create test data and directories
    # Verify all managers and supervisors
    # Check LLM and database availability
```

### Execution with Timeout
```python
# Execute with timeout and cancellation support
supervisor_task = asyncio.create_task(
    top_supervisor.call_supervisor(query, user, guid)
)
result = await asyncio.wait_for(supervisor_task, timeout=90.0)
```

### Result Analysis
```python
def _analyze_real_execution_components(self, call_trace):
    """Analyze real execution components"""
    # Parse call trace for component types
    # Identify workflow patterns
    # Validate expected components
    # Return structured analysis
```

## Test Categories by File

### Email Sending Real Execution
**File**: `test_real_email_sending_execution.py`

**Tests**:
- Real email sending call trace validation
- Email-specific component detection
- Response quality analysis
- Performance measurement
- Execution documentation

**Validation**:
- Email generation components present
- Email sending workflow executed
- Proper component coordination
- Response contains email-related content

### Email-Agenda Coordination Real Execution  
**File**: `test_real_email_agenda_coordination.py`

**Tests**:
- Multiple workflow types (agenda_email, meeting_invitation, etc.)
- Cross-component coordination validation
- Performance analysis across workflows
- Response quality for coordination tasks
- Component interaction documentation

**Validation**:
- Both email and agenda components active
- Coordination workflow executed
- State management across components
- Quality responses for complex queries

### Data Query Real Execution
**File**: `test_real_tell_me_about_your_data_execution.py`

**Tests**:
- Real data retrieval and processing
- RAG system validation
- Knowledge base interaction
- Response accuracy and completeness

**Validation**:
- Data access components active
- RAG processing executed
- Relevant information retrieved
- Coherent response generation

## Debugging Real Execution Tests

### Common Issues
1. **Timeout Errors**: Increase timeout or check system performance
2. **Component Missing**: Verify system initialization completed
3. **LLM Unavailable**: Check API keys and service status
4. **Database Connection**: Verify PostgreSQL/Qdrant connectivity

### Debug Output
Real execution tests include extensive debug output:
```
[TEST] Setting up REAL system components
[TEST] Executing REAL query with real components: 'query'
[TEST] REAL call_trace: [component1, component2, ...]
[TEST] Response length: 150
[TEST] Real execution completed successfully
```

### Performance Monitoring
```python
execution_time = end_time - start_time
assert execution_time < 120, f"Real execution took too long: {execution_time:.2f} seconds"
```

## Environment Configuration

### Required Environment Variables
```bash
# LLM Configuration
OPENAI_API_KEY=your_openai_key

# Database Configuration  
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=vectordb

# Vector Database
QDRANT_HOST=localhost
QDRANT_PORT=6333

# Debug Mode (disable for tests)
DEBUG=False
```

### System Prerequisites
- All managers properly initialized
- Vector store with test data
- Database schemas created
- OAuth tokens configured (for email/calendar tests)

## CI/CD Considerations

### Test Isolation
- Each test cleans up after execution
- Background tasks are cancelled properly
- Cache is cleared between runs
- No persistent state between tests

### Resource Management
- Tests monitor resource usage
- Memory and CPU limits respected
- Database connections properly closed
- Async tasks cleaned up

### Failure Handling
- Graceful degradation on component failure
- Skip tests when system requirements not met
- Proper error reporting and logging
- Cache invalidation on failures