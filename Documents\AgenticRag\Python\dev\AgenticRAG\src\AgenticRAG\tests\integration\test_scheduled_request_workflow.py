"""
Integration tests for scheduled task workflow
This test suite covers the complete scheduled task lifecycle:
Creation -> Storage -> Execution -> Completion -> Cleanup
"""

import sys
import os
import pytest
import pytest_asyncio
import asyncio
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from typing import Dict, List, Any
from datetime import datetime, timedelta
from uuid import uuid4

# Add project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from imports import *
from managers.scheduled_requests import ScheduledRequestPersistenceManager
from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest
from managers.manager_supervisors import SupervisorManager
from managers.manager_postgreSQL import PostgreS<PERSON>Manager
from userprofiles.ZairaUser import <PERSON>air<PERSON><PERSON><PERSON>, PERMISSION_LEVELS

class TestScheduledRequestWorkflow:
    """Test complete scheduled task workflow"""
    
    @pytest_asyncio.fixture
    async def setup_scheduled_request_environment(self):
        """Set up test environment for scheduled tasks"""
        with patch('managers.manager_postgreSQL.PostgreSQLManager') as mock_db, \
             patch('managers.manager_supervisors.SupervisorManager') as mock_supervisor, \
             patch('managers.manager_logfire.LogFire') as mock_logfire:
            
            # Mock database
            mock_db_instance = Mock()
            mock_db.get_instance.return_value = mock_db_instance
            mock_connection = Mock()
            # Setup async context manager for database connection
            mock_context_manager = AsyncMock()
            mock_context_manager.__aenter__.return_value = mock_connection
            mock_context_manager.__aexit__.return_value = None
            mock_db_instance.get_connection.return_value = mock_context_manager
            
            # Mock supervisor
            mock_supervisor_instance = Mock()
            mock_supervisor.get_instance.return_value = mock_supervisor_instance
            mock_supervisor_instance.route_to_task = AsyncMock(return_value="Task completed")
            
            # Create test user
            test_user = ZairaUser(
                username="testuser",
                rank=PERMISSION_LEVELS.USER,
                guid=uuid4(),
                device_guid=uuid4()
            )
            
            yield {
                'db': mock_db,
                'supervisor': mock_supervisor,
                'logfire': mock_logfire,
                'test_user': test_user,
                'db_instance': mock_db_instance,
                'connection': mock_connection
            }
    
    @pytest.mark.asyncio
    async def test_scheduled_request_creation_workflow(self, setup_scheduled_request_environment):
        """Test scheduled task creation workflow"""
        env = setup_scheduled_request_environment
        
        # Reset singleton for test
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        # Create scheduled task manager
        task_manager = ScheduledRequestPersistenceManager.get_instance()
        await ScheduledRequestPersistenceManager.setup()
        
        # Mock database operations
        env['connection'].fetchrow.return_value = {'scheduled_guid': 'test-guid-123'}
        env['connection'].fetch.return_value = []
        
        # Create test scheduled task
        test_task = ScheduledZairaRequest(
            user_guid=env['test_user'].user_guid,
            content="Test scheduled task",
            scheduled_time=datetime.now() + timedelta(hours=1),
            task_type="test_task",
            status="pending",
            priority=1
        )
        
        # Test task creation
        await task_manager.create_scheduled_request(test_task)
        
        # Verify database was called
        env['connection'].fetchrow.assert_called_once()
        call_args = env['connection'].fetchrow.call_args
        
        # Verify SQL contains proper INSERT
        sql_query = call_args[0][0]
        assert "INSERT INTO scheduled_requests" in sql_query
        assert "RETURNING scheduled_guid" in sql_query
        
        # Verify parameters
        params = call_args[0][1:]
        assert env['test_user'].user_guid in params
        assert "Test scheduled task" in params
        assert "test_task" in params
        assert "pending" in params
        assert 1 in params
    
    @pytest.mark.asyncio
    async def test_scheduled_request_retrieval_workflow(self, setup_scheduled_request_environment):
        """Test scheduled task retrieval workflow"""
        env = setup_scheduled_request_environment
        
        # Reset singleton for test
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        # Create scheduled task manager
        task_manager = ScheduledRequestPersistenceManager.get_instance()
        await ScheduledRequestPersistenceManager.setup()
        
        # Mock database response
        mock_task_data = {
            'scheduled_guid': 'test-guid-123',
            'user_guid': env['test_user'].user_guid,
            'content': 'Test scheduled task',
            'scheduled_time': datetime.now() + timedelta(hours=1),
            'task_type': 'test_task',
            'status': 'pending',
            'priority': 1,
            'created_at': datetime.now(),
            'updated_at': datetime.now(),
            'execution_count': 0,
            'last_execution': None,
            'error_message': None
        }
        
        env['connection'].fetch.return_value = [mock_task_data]
        
        # Test task retrieval
        tasks = await task_manager.get_pending_requests()
        
        # Verify database was called
        env['connection'].fetch.assert_called_once()
        call_args = env['connection'].fetch.call_args
        
        # Verify SQL query
        sql_query = call_args[0][0]
        assert "SELECT" in sql_query
        assert "scheduled_requests" in sql_query
        assert "WHERE status = 'pending'" in sql_query
        assert "ORDER BY priority DESC, scheduled_time ASC" in sql_query
        
        # Verify returned tasks
        assert len(tasks) == 1
        assert tasks[0]['scheduled_guid'] == 'test-guid-123'
        assert tasks[0]['content'] == 'Test scheduled task'
        assert tasks[0]['status'] == 'pending'
    
    @pytest.mark.asyncio
    async def test_scheduled_request_execution_workflow(self, setup_scheduled_request_environment):
        """Test scheduled task execution workflow"""
        env = setup_scheduled_request_environment
        
        # Reset singleton for test
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        # Create scheduled task manager
        task_manager = ScheduledRequestPersistenceManager.get_instance()
        await ScheduledRequestPersistenceManager.setup()
        
        # Mock database operations
        env['connection'].fetchrow.return_value = {'scheduled_guid': 'test-guid-123'}
        env['connection'].execute.return_value = None
        
        # Create test task data
        task_data = {
            'scheduled_guid': 'test-guid-123',
            'user_guid': env['test_user'].user_guid,
            'content': 'Execute test task',
            'scheduled_time': datetime.now() - timedelta(minutes=1),  # Past time
            'task_type': 'test_task',
            'status': 'pending',
            'priority': 1,
            'created_at': datetime.now(),
            'updated_at': datetime.now(),
            'execution_count': 0,
            'last_execution': None,
            'error_message': None
        }
        
        # Test task execution
        await task_manager.execute_scheduled_request(task_data)
        
        # Verify supervisor was called
        env['supervisor'].get_instance.return_value.route_to_task.assert_called_once()
        supervisor_call = env['supervisor'].get_instance.return_value.route_to_task.call_args
        
        # Verify supervisor call parameters
        assert supervisor_call[0][0] == 'test_task'  # Task type
        assert supervisor_call[0][1] == 'Execute test task'  # Content
        
        # Verify database was updated
        env['connection'].execute.assert_called()
        
        # Check that status was updated to 'running'
        update_calls = env['connection'].execute.call_args_list
        assert len(update_calls) >= 1
        
        # Find the status update call
        status_update_call = None
        for call in update_calls:
            if "status = 'running'" in call[0][0]:
                status_update_call = call
                break
        
        assert status_update_call is not None
        assert 'test-guid-123' in status_update_call[0][1:]
    
    @pytest.mark.asyncio
    async def test_scheduled_request_completion_workflow(self, setup_scheduled_request_environment):
        """Test scheduled task completion workflow"""
        env = setup_scheduled_request_environment
        
        # Reset singleton for test
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        # Create scheduled task manager
        task_manager = ScheduledRequestPersistenceManager.get_instance()
        await ScheduledRequestPersistenceManager.setup()
        
        # Mock database operations
        env['connection'].execute.return_value = None
        
        # Create test task data
        task_data = {
            'scheduled_guid': 'test-guid-123',
            'user_guid': env['test_user'].user_guid,
            'content': 'Complete test task',
            'scheduled_time': datetime.now() - timedelta(minutes=1),
            'task_type': 'test_task',
            'status': 'running',
            'priority': 1,
            'created_at': datetime.now(),
            'updated_at': datetime.now(),
            'execution_count': 1,
            'last_execution': datetime.now(),
            'error_message': None
        }
        
        # Test task completion
        await task_manager.complete_scheduled_request(task_data['scheduled_guid'], "Task completed successfully")
        
        # Verify database was updated
        env['connection'].execute.assert_called()
        call_args = env['connection'].execute.call_args
        
        # Verify SQL update
        sql_query = call_args[0][0]
        assert "UPDATE scheduled_requests" in sql_query
        assert "status = 'completed'" in sql_query
        assert "result = $2" in sql_query
        assert "updated_at = NOW()" in sql_query
        
        # Verify parameters
        params = call_args[0][1:]
        assert 'test-guid-123' in params
        assert "Task completed successfully" in params
    
    @pytest.mark.asyncio
    async def test_scheduled_request_error_handling_workflow(self, setup_scheduled_request_environment):
        """Test scheduled task error handling workflow"""
        env = setup_scheduled_request_environment
        
        # Reset singleton for test
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        # Create scheduled task manager
        task_manager = ScheduledRequestPersistenceManager.get_instance()
        await ScheduledRequestPersistenceManager.setup()
        
        # Mock supervisor to raise exception
        env['supervisor'].get_instance.return_value.route_to_task.side_effect = Exception("Task execution failed")
        
        # Mock database operations
        env['connection'].execute.return_value = None
        
        # Create test task data
        task_data = {
            'scheduled_guid': 'test-guid-123',
            'user_guid': env['test_user'].user_guid,
            'content': 'Failing test task',
            'scheduled_time': datetime.now() - timedelta(minutes=1),
            'task_type': 'test_task',
            'status': 'pending',
            'priority': 1,
            'created_at': datetime.now(),
            'updated_at': datetime.now(),
            'execution_count': 0,
            'last_execution': None,
            'error_message': None
        }
        
        # Test task execution with error
        await task_manager.execute_scheduled_request(task_data)
        
        # Verify supervisor was called
        env['supervisor'].get_instance.return_value.route_to_task.assert_called_once()
        
        # Verify database was updated with error status
        env['connection'].execute.assert_called()
        
        # Check for error status update
        update_calls = env['connection'].execute.call_args_list
        
        # Find the error update call
        error_update_call = None
        for call in update_calls:
            if "status = 'failed'" in call[0][0] or "error_message" in call[0][0]:
                error_update_call = call
                break
        
        # Should have updated status to failed with error message
        assert error_update_call is not None
    
    @pytest.mark.asyncio
    async def test_scheduled_request_retry_workflow(self, setup_scheduled_request_environment):
        """Test scheduled task retry workflow"""
        env = setup_scheduled_request_environment
        
        # Reset singleton for test
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        # Create scheduled task manager
        task_manager = ScheduledRequestPersistenceManager.get_instance()
        await ScheduledRequestPersistenceManager.setup()
        
        # Mock database operations
        env['connection'].execute.return_value = None
        
        # Create test task data with previous failure
        task_data = {
            'scheduled_guid': 'test-guid-123',
            'user_guid': env['test_user'].user_guid,
            'content': 'Retry test task',
            'scheduled_time': datetime.now() - timedelta(minutes=1),
            'task_type': 'test_task',
            'status': 'failed',
            'priority': 1,
            'created_at': datetime.now(),
            'updated_at': datetime.now(),
            'execution_count': 1,
            'last_execution': datetime.now() - timedelta(minutes=5),
            'error_message': 'Previous execution failed'
        }
        
        # Test task retry
        await task_manager.retry_scheduled_request(task_data['scheduled_guid'])
        
        # Verify database was updated
        env['connection'].execute.assert_called()
        call_args = env['connection'].execute.call_args
        
        # Verify SQL update
        sql_query = call_args[0][0]
        assert "UPDATE scheduled_requests" in sql_query
        assert "status = 'pending'" in sql_query
        assert "error_message = NULL" in sql_query
        assert "updated_at = NOW()" in sql_query
        
        # Verify parameters
        params = call_args[0][1:]
        assert 'test-guid-123' in params
    
    @pytest.mark.asyncio
    async def test_scheduled_request_cleanup_workflow(self, setup_scheduled_request_environment):
        """Test scheduled task cleanup workflow"""
        env = setup_scheduled_request_environment
        
        # Reset singleton for test
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        # Create scheduled task manager
        task_manager = ScheduledRequestPersistenceManager.get_instance()
        await ScheduledRequestPersistenceManager.setup()
        
        # Mock database operations
        env['connection'].execute.return_value = None
        
        # Test cleanup of old completed tasks
        cutoff_date = datetime.now() - timedelta(days=7)
        await task_manager.cleanup_old_requests(cutoff_date)
        
        # Verify database was called
        env['connection'].execute.assert_called()
        call_args = env['connection'].execute.call_args
        
        # Verify SQL delete
        sql_query = call_args[0][0]
        assert "DELETE FROM scheduled_requests" in sql_query
        assert "status IN ('completed', 'failed')" in sql_query
        assert "updated_at < $1" in sql_query
        
        # Verify parameters
        params = call_args[0][1:]
        assert cutoff_date in params
    
    @pytest.mark.asyncio
    async def test_scheduled_request_priority_workflow(self, setup_scheduled_request_environment):
        """Test scheduled task priority handling workflow"""
        env = setup_scheduled_request_environment
        
        # Reset singleton for test
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        # Create scheduled task manager
        task_manager = ScheduledRequestPersistenceManager.get_instance()
        await ScheduledRequestPersistenceManager.setup()
        
        # Mock database response with multiple tasks of different priorities
        mock_requests = [
            {
                'scheduled_guid': 'high-priority-task',
                'user_guid': env['test_user'].user_guid,
                'content': 'High priority task',
                'scheduled_time': datetime.now() + timedelta(hours=1),
                'task_type': 'urgent_task',
                'status': 'pending',
                'priority': 5,  # High priority
                'created_at': datetime.now(),
                'updated_at': datetime.now(),
                'execution_count': 0,
                'last_execution': None,
                'error_message': None
            },
            {
                'scheduled_guid': 'low-priority-task',
                'user_guid': env['test_user'].user_guid,
                'content': 'Low priority task',
                'scheduled_time': datetime.now() + timedelta(minutes=30),  # Earlier time
                'task_type': 'normal_task',
                'status': 'pending',
                'priority': 1,  # Low priority
                'created_at': datetime.now(),
                'updated_at': datetime.now(),
                'execution_count': 0,
                'last_execution': None,
                'error_message': None
            }
        ]
        
        env['connection'].fetch.return_value = mock_requests
        
        # Test task retrieval with priority ordering
        tasks = await task_manager.get_pending_requests()
        
        # Verify database was called with proper ordering
        env['connection'].fetch.assert_called_once()
        call_args = env['connection'].fetch.call_args
        
        # Verify SQL query has proper ordering
        sql_query = call_args[0][0]
        assert "ORDER BY priority DESC, scheduled_time ASC" in sql_query
        
        # Verify returned tasks are in correct order
        assert len(tasks) == 2
        # First task should be high priority despite later scheduled time
        assert tasks[0]['priority'] == 5
        assert tasks[0]['scheduled_guid'] == 'high-priority-task'
        # Second task should be low priority
        assert tasks[1]['priority'] == 1
        assert tasks[1]['scheduled_guid'] == 'low-priority-task'
    
    @pytest.mark.asyncio
    async def test_scheduled_request_concurrent_execution_workflow(self, setup_scheduled_request_environment):
        """Test scheduled task concurrent execution workflow"""
        env = setup_scheduled_request_environment
        
        # Reset singleton for test
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        # Create scheduled task manager
        task_manager = ScheduledRequestPersistenceManager.get_instance()
        await ScheduledRequestPersistenceManager.setup()
        
        # Mock database operations
        env['connection'].execute.return_value = None
        
        # Create multiple test tasks
        task_data_list = [
            {
                'scheduled_guid': f'concurrent-task-{i}',
                'user_guid': env['test_user'].user_guid,
                'content': f'Concurrent test task {i}',
                'scheduled_time': datetime.now() - timedelta(minutes=1),
                'task_type': 'concurrent_task',
                'status': 'pending',
                'priority': 1,
                'created_at': datetime.now(),
                'updated_at': datetime.now(),
                'execution_count': 0,
                'last_execution': None,
                'error_message': None
            }
            for i in range(3)
        ]
        
        # Test concurrent execution
        tasks = []
        for task_data in task_data_list:
            task = asyncio.create_task(task_manager.execute_scheduled_request(task_data))
            tasks.append(task)
        
        # Wait for all tasks to complete
        await asyncio.gather(*tasks)
        
        # Verify supervisor was called for each task
        supervisor_calls = env['supervisor'].get_instance.return_value.route_to_task.call_args_list
        assert len(supervisor_calls) == 3
        
        # Verify database was updated for each task
        database_calls = env['connection'].execute.call_args_list
        assert len(database_calls) >= 3  # At least one update per task
        
        # Verify all tasks were processed
        for i, call in enumerate(supervisor_calls):
            assert call[0][0] == 'concurrent_task'  # Task type
            assert f'Concurrent test task {i}' in call[0][1]  # Content

class TestScheduledRequestIntegration:
    """Test scheduled task integration with other components"""
    
    @pytest.mark.asyncio
    async def test_scheduled_request_with_user_management_integration(self):
        """Test scheduled task integration with user management"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db, \
             patch('managers.scheduled_requests.core.persistence.SupervisorManager') as mock_supervisor, \
             patch('managers.manager_users.PostgreSQLManager') as mock_user_db, \
             patch('managers.scheduled_requests.core.persistence.LogFire'):
            
            # Mock database
            mock_db_instance = Mock()
            mock_db.get_instance.return_value = mock_db_instance
            mock_user_db.get_instance.return_value = mock_db_instance
            mock_connection = Mock()
            mock_db_instance.get_connection.return_value.__aenter__.return_value = mock_connection
            mock_db_instance.get_connection.return_value.__aexit__.return_value = None
            
            # Mock supervisor
            mock_supervisor_instance = Mock()
            mock_supervisor.get_instance.return_value = mock_supervisor_instance
            mock_supervisor_instance.route_to_task = AsyncMock(return_value="Task completed")
            
            # Create test user
            test_user = ZairaUser(
                username="testuser",
                rank=PERMISSION_LEVELS.USER,
                guid=uuid4(),
                device_guid=uuid4()
            )
            
            # Reset singletons
            ScheduledRequestPersistenceManager._instance = None
            ScheduledRequestPersistenceManager._initialized = False
            
            # Create managers
            task_manager = ScheduledRequestPersistenceManager.get_instance()
            await ScheduledRequestPersistenceManager.setup()
            
            # Mock database operations
            mock_connection.fetchrow.return_value = {'scheduled_guid': 'test-guid-123'}
            
            # Create scheduled task for user
            test_task = ScheduledZairaRequest(
                user_guid=test_user.user_guid,
                content="User-specific task",
                scheduled_time=datetime.now() + timedelta(hours=1),
                task_type="user_task",
                status="pending",
                priority=1
            )
            
            # Test task creation with user context
            await task_manager.create_scheduled_request(test_task)
            
            # Verify database was called with user GUID
            mock_connection.fetchrow.assert_called_once()
            call_args = mock_connection.fetchrow.call_args
            params = call_args[0][1:]
            assert test_user.user_guid in params
    
    @pytest.mark.asyncio
    async def test_scheduled_request_with_supervisor_integration(self):
        """Test scheduled task integration with supervisor system"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db, \
             patch('managers.scheduled_requests.core.persistence.SupervisorManager') as mock_supervisor, \
             patch('managers.scheduled_requests.core.persistence.LogFire'):
            
            # Mock database
            mock_db_instance = Mock()
            mock_db.get_instance.return_value = mock_db_instance
            mock_connection = Mock()
            mock_db_instance.get_connection.return_value.__aenter__.return_value = mock_connection
            mock_db_instance.get_connection.return_value.__aexit__.return_value = None
            
            # Mock supervisor with specific task result
            mock_supervisor_instance = Mock()
            mock_supervisor.get_instance.return_value = mock_supervisor_instance
            mock_supervisor_instance.route_to_task = AsyncMock(return_value="Supervisor task completed with result")
            
            # Reset singleton
            ScheduledRequestPersistenceManager._instance = None
            ScheduledRequestPersistenceManager._initialized = False
            
            # Create task manager
            task_manager = ScheduledRequestPersistenceManager.get_instance()
            await ScheduledRequestPersistenceManager.setup()
            
            # Mock database operations
            mock_connection.execute.return_value = None
            
            # Create test task data
            task_data = {
                'scheduled_guid': 'supervisor-integration-test',
                'user_guid': str(uuid4()),
                'content': 'Task for supervisor integration',
                'scheduled_time': datetime.now() - timedelta(minutes=1),
                'task_type': 'integration_task',
                'status': 'pending',
                'priority': 1,
                'created_at': datetime.now(),
                'updated_at': datetime.now(),
                'execution_count': 0,
                'last_execution': None,
                'error_message': None
            }
            
            # Test task execution through supervisor
            await task_manager.execute_scheduled_request(task_data)
            
            # Verify supervisor was called with correct parameters
            mock_supervisor_instance.route_to_task.assert_called_once()
            supervisor_call = mock_supervisor_instance.route_to_task.call_args
            
            assert supervisor_call[0][0] == 'integration_task'  # Task type
            assert supervisor_call[0][1] == 'Task for supervisor integration'  # Content
            
            # Verify database was updated with supervisor result
            mock_connection.execute.assert_called()
            
            # The task should have been marked as completed with supervisor result
            update_calls = mock_connection.execute.call_args_list
            
            # Find completion update
            completion_call = None
            for call in update_calls:
                if "status = 'completed'" in call[0][0]:
                    completion_call = call
                    break
            
            assert completion_call is not None
            assert 'supervisor-integration-test' in completion_call[0][1:]