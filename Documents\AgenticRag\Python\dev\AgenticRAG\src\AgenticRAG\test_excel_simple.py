#!/usr/bin/env python3
"""
Simple Excel Analyzer Test - Bypasses complex imports
Run from src/AgenticRAG/ directory: python test_excel_simple.py
"""

import asyncio
import pandas as pd
from langchain_experimental.agents.agent_toolkits import create_pandas_dataframe_agent
from langchain_openai import ChatOpenAI
from langchain.agents import AgentType
from langchain_core.tools import BaseTool

class SimpleExcelAnalyzer(BaseTool):
    """Simplified Excel analyzer for testing"""
    name: str = "simple_excel_analyzer"
    description: str = "Analyzes Excel/CSV data"
    
    def __init__(self, **kwargs):
        super().__init__(**kwargs)
        self.df = None
        self.agent = None
    
    def _run(self, query: str) -> str:
        raise NotImplementedError("Use async version")
    
    async def _arun(self, query: str, file_path: str = None) -> str:
        try:
            # Load data
            if file_path:
                await self._load_data(file_path)
            else:
                await self._load_sample_data()
            
            # Create agent
            await self._create_agent()
            
            # Execute query
            result = self.agent.invoke(query)
            
            if isinstance(result, dict) and 'output' in result:
                return str(result['output'])
            else:
                return str(result)
                
        except Exception as e:
            return f"Error: {str(e)}"
    
    async def _load_data(self, file_path: str):
        """Load data from file"""
        if file_path.endswith('.csv'):
            self.df = pd.read_csv(file_path)
        elif file_path.endswith(('.xlsx', '.xls')):
            self.df = pd.read_excel(file_path)
        else:
            raise ValueError("Unsupported file format")
        
        print(f"Loaded: {self.df.shape[0]} rows, {self.df.shape[1]} columns")
    
    async def _load_sample_data(self):
        """Load sample data for testing"""
        sample_data = {
            'employee_id': [1, 2, 3, 4, 5],
            'name': ['Alice Johnson', 'Bob Smith', 'Charlie Brown', 'Diana Prince', 'Eve Adams'],
            'department': ['Engineering', 'Sales', 'Marketing', 'Engineering', 'HR'],
            'salary': [75000, 65000, 60000, 80000, 55000],
            'years_experience': [5, 3, 2, 7, 1]
        }
        self.df = pd.DataFrame(sample_data)
        print(f"Loaded sample data: {self.df.shape[0]} rows, {self.df.shape[1]} columns")
    
    async def _create_agent(self):
        """Create pandas agent"""
        self.agent = create_pandas_dataframe_agent(
            ChatOpenAI(temperature=0, model="gpt-4o-mini"),
            self.df,
            verbose=True,
            agent_type=AgentType.OPENAI_FUNCTIONS
        )
        print("Agent created successfully")

async def test_sample_data():
    """Test with sample data"""
    print("=== Testing with Sample Data ===")
    
    analyzer = SimpleExcelAnalyzer()
    
    queries = [
        "How many employees are there?",
        "What is the average salary?",
        "Which department has the most employees?",
        "Who has the highest salary?",
        "Show me all employees in Engineering",
        "What is the total salary cost for all employees?"
    ]
    
    for query in queries:
        print(f"\nQuery: {query}")
        result = await analyzer._arun(query)
        print(f"Answer: {result}")

async def test_your_file():
    """Test with your actual file"""
    print("\n=== Testing with Your DS Salaries File ===")
    
    file_path = r"C:\Users\<USER>\Documents\AgenticRag\Python\Test files\ds_salaries.csv"
    
    try:
        analyzer = SimpleExcelAnalyzer()
        
        queries = [
            "How many records are in this dataset?",
            "What are the column names?",
            "What is the average salary?",
            "What are the unique job titles?",
            "Which company size is most common?",
            "What is the salary range?"
        ]
        
        for query in queries:
            print(f"\nQuery: {query}")
            result = await analyzer._arun(query, file_path=file_path)
            print(f"Answer: {result}")
            
    except Exception as e:
        print(f"Error testing with file: {e}")
        print("Make sure your file path is correct and accessible")

async def test_basic_pandas():
    """Test basic pandas functionality"""
    print("\n=== Testing Basic Pandas Functionality ===")
    
    # Test file loading
    file_path = r"C:\Users\<USER>\Documents\AgenticRag\Python\Test files\ds_salaries.csv"
    
    try:
        df = pd.read_csv(file_path)
        print(f"Successfully loaded CSV: {df.shape[0]} rows, {df.shape[1]} columns")
        print(f"Columns: {list(df.columns)}")
        print(f"First few rows:\n{df.head()}")
        
        # Basic analysis
        if 'salary' in df.columns:
            print(f"Average salary: {df['salary'].mean():.2f}")
        elif 'salary_in_usd' in df.columns:
            print(f"Average salary (USD): {df['salary_in_usd'].mean():.2f}")
            
    except Exception as e:
        print(f"Error with pandas: {e}")

async def main():
    """Run all tests"""
    print("Starting Simple Excel Analyzer Tests...\n")
    
    # Test 1: Basic pandas
    await test_basic_pandas()
    
    # Test 2: Sample data
    await test_sample_data()
    
    # Test 3: Your actual file
    await test_your_file()
    
    print("\n=== All Tests Complete ===")

if __name__ == "__main__":
    asyncio.run(main())