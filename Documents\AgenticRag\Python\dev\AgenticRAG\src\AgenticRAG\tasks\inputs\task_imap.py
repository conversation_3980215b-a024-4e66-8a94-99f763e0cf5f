from imports import *

from os import path as os_path
from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTask_SingleAgent, SupervisorTaskState
from endpoints.oauth._verifier_ import OAuth2Verifier
from managers.manager_meltano import MeltanoManager
from managers.manager_users import ZairaUserManager
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest

class SupervisorTask_IMAP(SupervisorTask_Base):
    callback_response: bool = False

    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task"""
        import imaplib
        import email
        from email.header import decode_header

        # Configuration
        IMAP_SERVER = await OAuth2Verifier.get_token("imap", "access_token")  # Change for other providers
        EMAIL_ACCOUNT = await OAuth2Verifier.get_token("imap", "refresh_token")
        IMAP_PORT = int(await OAuth2Verifier.get_token("imap", "expires_in"))
        PASSWORD = await OAuth2Verifier.get_token("imap", "token_type")#"your_app_password"  # Use an App Password if 2FA is enabled
        SAVE_IN_VECTOR = None if state.scheduled_guid else await OAuth2Verifier.get_token("imap", "int1")
        
        if not all([IMAP_SERVER, EMAIL_ACCOUNT, IMAP_PORT, PASSWORD]):
            return "Incomplete IMAP configuration - missing server, email, port, or password"
        
        if Globals.is_docker():
            path = '/meltano/output'
        else:
            from os import getcwd
            path = getcwd() + '/src/meltano/output'

        # Determine SSL usage from port (993 is standard SSL)
        USE_SSL = IMAP_PORT == 993

        # Connect based on SSL flag
        try:
            if USE_SSL:
                mail = imaplib.IMAP4_SSL(IMAP_SERVER, IMAP_PORT)
            else:
                mail = imaplib.IMAP4(IMAP_SERVER, IMAP_PORT)
        except Exception as e:
            etc.helper_functions.exception_triggered(e, "imap_connection", None)
            return f"Connection failed to {IMAP_SERVER}:{IMAP_PORT}: {str(e)}"

        # Login to your account
        try:
            mail.login(EMAIL_ACCOUNT, PASSWORD)
        except Exception as e:
            try:
                # Some mail providers don't allow the extension as the username
                username = EMAIL_ACCOUNT.rsplit(".", 1)[0]
                mail.login(username, PASSWORD)
            except Exception as login_error:
                etc.helper_functions.exception_triggered(login_error, "imap_login", None)
                return f"Login failed for {EMAIL_ACCOUNT}: {str(login_error)}"

        # Select the mailbox you want to use
        mail.select("inbox")  # or 'ALL', 'INBOX', 'Sent', etc.

        # Search for all emails
        status, messages = mail.search(None, "ALL")

        # messages is a space-separated string of email IDs
        email_ids = messages[0].split()

        if not SAVE_IN_VECTOR:
            async def callback_all(task: LongRunningZairaRequest, response: str):
                self.callback_response = "j" in response or "y" in response
            
            # Get user to access active task
            user = await ZairaUserManager.find_user(state.user_guid)
            await user.my_requests[state.scheduled_guid].request_human_in_the_loop("Wil je ALLE mails opslaan in Zaira's data? (j/n)", callback_all, True)
        else:
            self.callback_response = SAVE_IN_VECTOR

        # Fetch and save each email
        for i, num in enumerate(email_ids, 1):
            status, data = mail.fetch(num, "(RFC822)")
            raw_email = data[0][1]
            
            async def handle_mail():
                # Save as .eml file
                filename = os_path.join(path, f"email_{i:04d}.eml")
                with open(filename, "wb") as f:
                    f.write(raw_email)

                chat_session = await self.get_chat_session_from_state(state)
                LogFire.log("EVENT", f"Saved: {filename}", chat=chat_session)

            if self.callback_response == False and not SAVE_IN_VECTOR:
                async def callback_specific(task: LongRunningZairaRequest, response: str):
                    callback_response = "j" in response or "y" in response
                    if callback_response:
                        await handle_mail()
                await user.my_requests[state.scheduled_guid].request_human_in_the_loop(f"Wil je de mail '{raw_email}' opslaan in Zaira's data? (j/n)", callback_specific, True)
            else:
                await handle_mail()

        # Logout
        mail.logout()

        # Mails retrieved, convert to vector store
        await MeltanoManager.ConvertFilesToVectorStore(path, user)

        return ""

async def create_task_imap_receiver() -> SupervisorTask_Base:
    return SupervisorManager.register_task(SupervisorTask_IMAP(name="imap_retrieval_task", prompt_id="Task_IMAP"))
