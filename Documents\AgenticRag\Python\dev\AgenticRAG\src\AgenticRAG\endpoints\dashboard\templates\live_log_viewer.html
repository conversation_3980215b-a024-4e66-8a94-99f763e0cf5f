<div id="liveLogViewer" class="user-card" style="margin-top: 1rem;">
    <div style="display: flex; justify-content: space-between; align-items: center; margin-bottom: 1rem;">
        <div style="display: flex; align-items: center; justify-content: space-between;">
            <div style="font-weight: 600; font-size: 1rem; color: #e2e8f0;">
                [LOG] Live System Logs
            </div>
            <!-- Log Source Selection Tabs -->
            <div style="display: flex; gap: 0.5rem; align-items: center;">
                <button id="logSourceDebug" onclick="if(typeof switchLogSource === 'function') { switchLogSource('debug'); } else { console.log('switchLogSource not ready yet'); }" 
                        class="log-source-tab active"
                        style="padding: 0.25rem 0.5rem; background: #3b82f6; color: white; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                    [DEBUG] Debug Trace
                </button>
                <button id="logSourceConsole" onclick="if(typeof switchLogSource === 'function') { switchLogSource('console'); } else { console.log('switchLogSource not ready yet'); }"
                        class="log-source-tab"
                        style="padding: 0.25rem 0.5rem; background: #374151; color: #94a3b8; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                    [CONSOLE] Console
                </button>
                <button id="logSourceSystem" onclick="if(typeof switchLogSource === 'function') { switchLogSource('system'); } else { console.log('switchLogSource not ready yet'); }"
                        class="log-source-tab"
                        style="padding: 0.25rem 0.5rem; background: #374151; color: #94a3b8; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                    [SYSTEM] System
                </button>
            </div>
        </div>
        <div style="display: flex; gap: 0.5rem; align-items: center;">
            <div id="logStatus" style="font-size: 0.8rem; color: #94a3b8; display: flex; align-items: center; gap: 0.25rem;">
                <span id="logStatusIcon" style="width: 8px; height: 8px; border-radius: 50%; background: #10b981;"></span>
                <span id="logStatusText">Connected</span>
            </div>
            <button id="testRealServerBtn" onclick="if(typeof checkAndRedirectTestReal === 'function') { checkAndRedirectTestReal(); } else { console.log('checkAndRedirectTestReal not ready yet'); }" 
                    title="Switch to test_real server"
                    onmouseover="this.style.background='#7c3aed'"
                    onmouseout="this.style.background='#8b5cf6'"
                    style="padding: 0.25rem 0.5rem; background: #8b5cf6; color: white; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                [REFRESH] Test_Real Server
            </button>
            <button onclick="if(typeof copyLogContent === 'function') { copyLogContent(); } else { console.log('copyLogContent not ready yet'); }" 
                    title="Copy logs to clipboard"
                    onmouseover="this.style.background='#0369a1'"
                    onmouseout="this.style.background='#0284c7'"
                    style="padding: 0.25rem 0.5rem; background: #0284c7; color: white; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s;">
                [COPY] Copy
            </button>
            <button onclick="if(typeof forceRefreshLogs === 'function') { forceRefreshLogs(); } else { console.log('forceRefreshLogs not ready yet'); }" 
                    title="Force refresh - clear cache and reload all content"
                    onmouseover="this.style.background='#b91c1c'"
                    onmouseout="this.style.background='#dc2626'"
                    style="padding: 0.25rem 0.5rem; background: #dc2626; color: white; border: none; border-radius: 4px; font-size: 0.75rem; cursor: pointer; transition: all 0.2s; font-weight: 600;">
                [REFRESH] Force Refresh
            </button>
        </div>
    </div>
    
    <div style="margin-bottom: 1rem;">
        <input type="text" id="logSearchInput" placeholder="Search logs... (press Enter or type to filter)"
               onkeyup="if(typeof filterLogContent === 'function') { filterLogContent(); } else { console.log('filterLogContent not ready yet'); }"
               style="width: 100%; padding: 0.5rem; background: #1e293b; border: 1px solid #374151; border-radius: 4px; color: #e2e8f0; font-size: 0.85rem;">
    </div>
    
    <!-- Log Filtering Controls -->
    <div style="margin-bottom: 1rem; padding: 1rem; background: #1e293b; border: 1px solid #374151; border-radius: 6px;">
        <div style="font-size: 0.85rem; font-weight: 600; color: #e2e8f0; margin-bottom: 0.75rem;">
            [FILTERS] Log Filters
        </div>
        
        <div style="display: grid; grid-template-columns: repeat(auto-fit, minmax(200px, 1fr)); gap: 1rem;">
            <!-- Filter Options Column 1 -->
            <div>
                <label style="display: flex; align-items: center; gap: 0.25rem; font-size: 0.8rem; color: #94a3b8; margin-bottom: 0.5rem;">
                    <input type="checkbox" id="excludeFrontendDebug" checked onchange="if(typeof applyLogFilters === 'function') { applyLogFilters(); } else { console.log('applyLogFilters not ready yet'); }"
                           style="margin: 0; accent-color: #ef4444;">
                    Hide FRONTEND_DEBUG noise
                </label>
                <label style="display: flex; align-items: center; gap: 0.25rem; font-size: 0.8rem; color: #94a3b8; margin-bottom: 0.5rem;">
                    <input type="checkbox" id="excludeScrubbedAuth" onchange="if(typeof applyLogFilters === 'function') { applyLogFilters(); } else { console.log('applyLogFilters not ready yet'); }"
                           style="margin: 0; accent-color: #f59e0b;">
                    Hide scrubbed auth messages
                </label>
            </div>
            
            <!-- Event Code Filter Column 2 -->
            <div>
                <label style="display: block; font-size: 0.8rem; color: #94a3b8; margin-bottom: 0.25rem;">
                    Show only event codes:
                </label>
                <input type="text" id="eventCodesFilter" placeholder="e.g., INIT,TASK,ERROR"
                       onkeyup="if(typeof applyLogFilters === 'function') { applyLogFilters(); } else { console.log('applyLogFilters not ready yet'); }" style="width: 100%; padding: 0.25rem; background: #0f172a; border: 1px solid #374151; border-radius: 3px; color: #e2e8f0; font-size: 0.75rem;">
                <div style="font-size: 0.7rem; color: #64748b; margin-top: 0.25rem;">
                    Comma-separated (empty = show all)
                </div>
            </div>
        </div>
        
        <!-- Filter Status -->
        <div id="filterStatus" style="margin-top: 0.75rem; padding-top: 0.5rem; border-top: 1px solid #374151; font-size: 0.75rem; color: #10b981;">
            Active: Hide FRONTEND_DEBUG
        </div>
    </div>
    
    <div id="logContent" 
         style="background: #0f172a; border: 1px solid #374151; border-radius: 6px; padding: 1rem; min-height: 300px; max-height: 400px; overflow-y: auto; font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace; font-size: 0.8rem; line-height: 1.4; color: #e2e8f0; white-space: pre-wrap; word-break: break-all;">
        <div style="color: #94a3b8; text-align: center; margin-top: 2rem;">
            [LOADING] Loading system logs...
        </div>
    </div>
    
    <div style="display: flex; justify-content: space-between; align-items: center; margin-top: 0.75rem; padding-top: 0.75rem; border-top: 1px solid #374151;">
        <div style="display: flex; gap: 1rem; align-items: center;">
            <label style="display: flex; align-items: center; gap: 0.25rem; font-size: 0.8rem; color: #94a3b8;">
                <input type="checkbox" id="logAutoScroll" checked 
                       style="margin: 0; accent-color: #3b82f6;">
                Auto-scroll to bottom
            </label>
            <span style="font-size: 0.8rem; color: #94a3b8;">
                Update every: 5 seconds
            </span>
        </div>
        <div id="logStats" style="font-size: 0.75rem; color: #94a3b8; display: flex; gap: 1rem; flex-wrap: wrap; align-items: center;">
            <span>[STATS] Lines: <span id="logLineCount" style="font-weight: 500;">0</span></span>
            <span>[SIZE] Size: <span id="logSizeInfo" style="font-weight: 500;">0 KB</span></span>
            <span>[TIME] Updated: <span id="logLastUpdate" style="font-weight: 500;">Never</span></span>
            <span>[SOURCE] Source: <span id="logSourceInfo" style="font-weight: 500; color: #3b82f6;">Debug Trace</span></span>
            <span>[FILTER] Filtered: <span id="logFilteredCount" style="font-weight: 500;">0</span></span>
        </div>
    </div>
</div>