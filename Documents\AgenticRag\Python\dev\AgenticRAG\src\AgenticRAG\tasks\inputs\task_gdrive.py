from imports import *

from os import getcwd
from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTaskState

class SupervisorTask_GDrive(SupervisorTask_Base):
    folderID: dict[str,str] = {}

    async def llm_call(self, state: SupervisorTaskState):
        """Execute the current task"""
        from userprofiles import ZairaUser
        from managers.manager_users import ZairaUserManager
        from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
        from endpoints.oauth._verifier_ import OAuth2Verifier

        user = await ZairaUserManager.find_user(state.user_guid)
        async def callback(task: LongRunningZairaRequest, response: str):
            intermediate = response.rsplit("/", 1)
            if len(intermediate) > 1:
                response = intermediate[1]
            response = response.rsplit("?", 1)[0]
            self.folderID[state.user_guid] = response
        # Get user's active task to request human input
        await user.my_requests[state.scheduled_guid].request_human_in_the_loop("Welke Google Drive folder wil je dat ik uit lees?", callback, True)
        chat_session = await self.get_chat_session_from_state(state)
        LogFire.log("DEBUG", f"Google Drive folder ID: {self.folderID[state.user_guid]}", chat=chat_session, severity="debug")
        from managers.manager_meltano import MeltanoManager
        etc.helper_functions.save_to_env({"GDRIVE_FILE_ID": self.folderID[state.user_guid], "GDRIVE_OUTPUT_PATH": "/meltano/output" if Globals.is_docker() else getcwd() + "/src/meltano/output"})
        self.folderID.pop(state.user_guid)
        await MeltanoManager.RunUtility("gdrive")

        if Globals.is_docker():
            path = '/meltano/output'
        else:
            path = getcwd() + '/src/meltano/output'
        await MeltanoManager.ConvertFilesToVectorStore(path, None)
        return ""

async def create_task_gdrive_receiver() -> SupervisorTask_Base:
    return SupervisorManager.register_task(SupervisorTask_GDrive(name="gdrive_retrieval_task", prompt_id="Task_GDrive"))
