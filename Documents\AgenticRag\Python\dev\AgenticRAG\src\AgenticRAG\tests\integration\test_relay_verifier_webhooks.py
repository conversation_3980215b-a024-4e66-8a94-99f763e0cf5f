"""
Integration test for webhook functionality between relay_main and _verifier_
Tests production scenario where relay server runs on :8084 and main app on :41000
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from managers.manager_logfire import LogFire
import pytest
import pytest_asyncio
import asyncio
import aiohttp
from aiohttp import web
import json
from typing import Dict, Any, Optional, List
from unittest.mock import AsyncMock, patch, MagicMock, Mock
import socket
from contextlib import closing
from urllib.parse import urlencode, parse_qs, urlparse

# Test constants - using test ports to avoid conflicts with production
RELAY_PORT = 8083  # Test port (production uses 8084)
MAIN_APP_PORT = 40999  # Test port (production uses 41000)
TEST_TIMEOUT = 30


def find_free_port(start_port: int = 50000) -> int:
    """Find a free port starting from start_port"""
    for port in range(start_port, start_port + 100):
        with closing(socket.socket(socket.AF_INET, socket.SOCK_STREAM)) as s:
            try:
                s.bind(('', port))
                s.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
                return port
            except OSError:
                continue
    raise RuntimeError("Could not find a free port")


class MockVerifierApp:
    """Mock _verifier_ application with OAuth endpoints"""
    def __init__(self, port: int):
        self.port = port
        self.app = web.Application()
        self.runner = None
        self.site = None
        self.requests_received = []
        self.oauth_tokens = {}
        
        # Setup routes matching _verifier_.py
        self.app.router.add_get('/oauth_redirect', self.handle_oauth_redirect)
        self.app.router.add_post('/oauth_redirect', self.handle_oauth_redirect_post)
        self.app.router.add_post('/{identifier}/oauth', self.handle_oauth_init)
        self.app.router.add_post('/{identifier}/oauth/twice', self.handle_oauth_twice)
        self.app.router.add_get('/{identifier}/oauth/status_wait', self.handle_oauth_status_wait)
        self.app.router.add_get('/{identifier}/oauth/status_get', self.handle_oauth_status_get)
    
    async def handle_oauth_redirect(self, request: web.Request) -> web.Response:
        """Handle OAuth redirect callbacks"""
        query_params = dict(request.rel_url.query)
        self.requests_received.append({
            'path': '/oauth_redirect',
            'method': 'GET',
            'query': query_params,
            'headers': dict(request.headers)
        })
        
        # Extract state and code
        state = query_params.get('state', '')
        code = query_params.get('code', '')
        
        if state and code:
            # Store token for this state
            self.oauth_tokens[state] = {
                'access_token': f'mock_access_{code}',
                'refresh_token': f'mock_refresh_{code}'
            }
            return web.json_response({'status': 'success', 'state': state})
        
        return web.json_response({'error': 'Missing state or code'}, status=400)
    
    async def handle_oauth_redirect_post(self, request: web.Request) -> web.Response:
        """Handle manual OAuth configuration"""
        data = await request.json()
        self.requests_received.append({
            'path': '/oauth_redirect',
            'method': 'POST',
            'data': data
        })
        
        identifier = data.get('identifier')
        if identifier:
            self.oauth_tokens[identifier] = {
                'access_token': data.get('access_token'),
                'refresh_token': data.get('refresh_token')
            }
            return web.json_response({'status': 'configured', 'identifier': identifier})
        
        return web.json_response({'error': 'Missing identifier'}, status=400)
    
    async def handle_oauth_init(self, request: web.Request) -> web.Response:
        """Handle OAuth initialization"""
        identifier = request.match_info['identifier']
        data = await request.json()
        self.requests_received.append({
            'path': f'/{identifier}/oauth',
            'method': 'POST',
            'data': data
        })
        
        # Generate OAuth URL
        auth_url = f"https://example.com/oauth/authorize?client_id=test&state={identifier}"
        return web.json_response({'auth_url': auth_url})
    
    async def handle_oauth_twice(self, request: web.Request) -> web.Response:
        """Handle second OAuth step"""
        identifier = request.match_info['identifier']
        data = await request.json()
        self.requests_received.append({
            'path': f'/{identifier}/oauth/twice',
            'method': 'POST',
            'data': data
        })
        return web.json_response({'status': 'input_received'})
    
    async def handle_oauth_status_wait(self, request: web.Request) -> web.Response:
        """Wait for OAuth completion"""
        identifier = request.match_info['identifier']
        self.requests_received.append({
            'path': f'/{identifier}/oauth/status_wait',
            'method': 'GET'
        })
        
        # Check if we have tokens for this identifier
        if identifier in self.oauth_tokens:
            return web.json_response({'status': 'completed', 'tokens': self.oauth_tokens[identifier]})
        
        return web.json_response({'status': 'waiting'})
    
    async def handle_oauth_status_get(self, request: web.Request) -> web.Response:
        """Get OAuth status"""
        identifier = request.match_info['identifier']
        status = 'configured' if identifier in self.oauth_tokens else 'not_configured'
        return web.json_response({'status': status})
    
    async def start(self):
        """Start the mock server"""
        self.runner = web.AppRunner(self.app)
        await self.runner.setup()
        self.site = web.TCPSite(self.runner, 'localhost', self.port)
        await self.site.start()
    
    async def stop(self):
        """Stop the mock server"""
        if self.site:
            await self.site.stop()
        if self.runner:
            await self.runner.cleanup()


class MockWhatsAppEndpoint:
    """Mock WhatsApp endpoint that handles webhook verification and messages"""
    def __init__(self, app: web.Application):
        self.requests_received = []
        self.verify_token = "test_verify_token"
        
        # Add WhatsApp routes
        app.router.add_get('/whatsapp/webhook', self.handle_whatsapp_verify)
        app.router.add_post('/whatsapp/webhook', self.handle_whatsapp_webhook)
    
    async def handle_whatsapp_verify(self, request: web.Request) -> web.Response:
        """Handle WhatsApp webhook verification"""
        query_params = dict(request.rel_url.query)
        self.requests_received.append({
            'path': '/whatsapp/webhook',
            'method': 'GET',
            'query': query_params
        })
        
        # WhatsApp verification logic
        mode = query_params.get('hub.mode')
        token = query_params.get('hub.verify_token')
        challenge = query_params.get('hub.challenge')
        
        if mode == 'subscribe' and token == self.verify_token:
            return web.Response(text=challenge)
        
        return web.Response(text='Forbidden', status=403)
    
    async def handle_whatsapp_webhook(self, request: web.Request) -> web.Response:
        """Handle WhatsApp webhook messages"""
        data = await request.json()
        self.requests_received.append({
            'path': '/whatsapp/webhook',
            'method': 'POST',
            'data': data,
            'headers': dict(request.headers)
        })
        
        # Process WhatsApp message
        entry = data.get('entry', [])
        if entry:
            return web.json_response({'status': 'received', 'message_count': len(entry)})
        
        return web.json_response({'status': 'no_messages'})


class MockMainApp(MockVerifierApp):
    """Mock main application combining verifier and WhatsApp endpoints"""
    def __init__(self, port: int):
        super().__init__(port)
        # Add WhatsApp endpoint
        self.whatsapp_endpoint = MockWhatsAppEndpoint(self.app)
    
    def get_whatsapp_requests(self) -> List[Dict]:
        """Get requests received by WhatsApp endpoint"""
        return self.whatsapp_endpoint.requests_received


class TestRelayVerifierWebhooks:
    """Integration tests for relay_main and _verifier_ webhook communication"""
    
    @pytest_asyncio.fixture
    async def test_ports(self):
        """Find free ports for testing"""
        relay_port = find_free_port(50000)
        main_port = find_free_port(relay_port + 1)
        return {'relay': relay_port, 'main': main_port}
    
    @pytest_asyncio.fixture
    async def main_app(self, test_ports):
        """Create and start mock main application"""
        app = MockMainApp(test_ports['main'])
        await app.start()
        yield app
        await app.stop()
    
    @pytest_asyncio.fixture
    async def relay_main_module(self):
        """Import relay_main module with mocked dependencies"""
        with patch.dict('os.environ', {
            'ZAIRA_PYTHON_PORT': str(MAIN_APP_PORT),
            'WHATSAPP_VERIFY_TOKEN': 'test_verify_token'
        }):
            from endpoints.oauth import relay_main
            yield relay_main
    
    @pytest.mark.asyncio
    async def test_whatsapp_webhook_relay_integration(self, main_app, test_ports):
        """Test WhatsApp webhook relay from external to internal service"""
        # Create a minimal relay simulation based on relay_main logic
        async def relay_whatsapp_webhook(external_data: Dict) -> Dict:
            """Simulate relay_main WhatsApp webhook relay logic"""
            port = test_ports['main']
            max_consecutive_failures = 3
            
            for attempt in range(max_consecutive_failures):
                try:
                    async with aiohttp.ClientSession() as session:
                        url = f"http://localhost:{port}/whatsapp/webhook"
                        async with session.post(url, json=external_data, timeout=5) as resp:
                            return await resp.json()
                except Exception:
                    port += 1
                    if attempt == max_consecutive_failures - 1:
                        raise
            
            raise Exception("Failed to relay webhook")
        
        # Test data simulating external WhatsApp webhook
        whatsapp_data = {
            "entry": [{
                "id": "123456789",
                "changes": [{
                    "value": {
                        "messages": [{
                            "from": "1234567890",
                            "id": "msg_123",
                            "timestamp": "1234567890",
                            "text": {"body": "Hello from WhatsApp"},
                            "type": "text"
                        }]
                    }
                }]
            }]
        }
        
        # Relay the webhook
        result = await relay_whatsapp_webhook(whatsapp_data)
        
        # Verify the result
        assert result['status'] == 'received'
        assert result['message_count'] == 1
        
        # Verify main app received the webhook
        whatsapp_requests = main_app.get_whatsapp_requests()
        assert len(whatsapp_requests) == 1
        assert whatsapp_requests[0]['method'] == 'POST'
        assert whatsapp_requests[0]['data'] == whatsapp_data
    
    @pytest.mark.asyncio
    async def test_oauth_redirect_flow(self, main_app, test_ports):
        """Test OAuth redirect flow through relay to verifier"""
        # Simulate OAuth callback
        oauth_params = {
            'code': 'test_auth_code_123',
            'state': 'python-whatsapp_business'
        }
        
        # Direct request to main app (simulating relay forwarding)
        async with aiohttp.ClientSession() as session:
            url = f"http://localhost:{test_ports['main']}/oauth_redirect?{urlencode(oauth_params)}"
            async with session.get(url) as resp:
                result = await resp.json()
        
        # Verify response
        assert result['status'] == 'success'
        assert result['state'] == 'python-whatsapp_business'
        
        # Verify main app processed the OAuth callback
        assert len(main_app.requests_received) == 1
        oauth_request = main_app.requests_received[0]
        assert oauth_request['path'] == '/oauth_redirect'
        assert oauth_request['query']['code'] == 'test_auth_code_123'
        assert oauth_request['query']['state'] == 'python-whatsapp_business'
        
        # Verify tokens were stored
        assert 'python-whatsapp_business' in main_app.oauth_tokens
    
    @pytest.mark.asyncio
    async def test_oauth_manual_configuration(self, main_app, test_ports):
        """Test manual OAuth configuration via POST"""
        config_data = {
            'identifier': 'whatsapp_business',
            'access_token': 'manual_access_token',
            'refresh_token': 'manual_refresh_token'
        }
        
        async with aiohttp.ClientSession() as session:
            url = f"http://localhost:{test_ports['main']}/oauth_redirect"
            async with session.post(url, json=config_data) as resp:
                result = await resp.json()
        
        # Verify response
        assert result['status'] == 'configured'
        assert result['identifier'] == 'whatsapp_business'
        
        # Verify tokens were stored
        assert main_app.oauth_tokens['whatsapp_business']['access_token'] == 'manual_access_token'
    
    @pytest.mark.asyncio
    async def test_oauth_full_flow(self, main_app, test_ports):
        """Test complete OAuth flow from initialization to completion"""
        identifier = 'test_oauth_app'
        
        # Step 1: Initialize OAuth
        async with aiohttp.ClientSession() as session:
            init_url = f"http://localhost:{test_ports['main']}/{identifier}/oauth"
            async with session.post(init_url, json={'app_name': 'test'}) as resp:
                init_result = await resp.json()
        
        assert 'auth_url' in init_result
        
        # Step 2: Simulate OAuth callback
        oauth_params = {
            'code': 'auth_code_456',
            'state': identifier
        }
        
        async with aiohttp.ClientSession() as session:
            callback_url = f"http://localhost:{test_ports['main']}/oauth_redirect?{urlencode(oauth_params)}"
            async with session.get(callback_url) as resp:
                callback_result = await resp.json()
        
        assert callback_result['status'] == 'success'
        
        # Step 3: Check status
        async with aiohttp.ClientSession() as session:
            status_url = f"http://localhost:{test_ports['main']}/{identifier}/oauth/status_wait"
            async with session.get(status_url) as resp:
                status_result = await resp.json()
        
        assert status_result['status'] == 'completed'
        assert 'tokens' in status_result
    
    @pytest.mark.asyncio
    async def test_whatsapp_webhook_verification(self, main_app, test_ports):
        """Test WhatsApp webhook verification process"""
        verify_params = {
            'hub.mode': 'subscribe',
            'hub.verify_token': 'test_verify_token',
            'hub.challenge': 'challenge_string_123'
        }
        
        async with aiohttp.ClientSession() as session:
            url = f"http://localhost:{test_ports['main']}/whatsapp/webhook?{urlencode(verify_params)}"
            async with session.get(url) as resp:
                result = await resp.text()
        
        # Verify challenge was returned
        assert result == 'challenge_string_123'
        
        # Verify request was logged
        whatsapp_requests = main_app.get_whatsapp_requests()
        assert len(whatsapp_requests) == 1
        assert whatsapp_requests[0]['method'] == 'GET'
        assert whatsapp_requests[0]['query']['hub.challenge'] == 'challenge_string_123'
    
    @pytest.mark.asyncio
    async def test_port_increment_retry_logic(self, test_ports):
        """Test relay port increment logic when main app is on different port"""
        # Create main app on incremented port
        alt_port = test_ports['main'] + 1
        alt_app = MockMainApp(alt_port)
        await alt_app.start()
        
        try:
            # Simulate relay logic with port increment
            async def relay_with_retry(data: Dict, start_port: int) -> Dict:
                current_port = start_port
                max_attempts = 3
                
                for attempt in range(max_attempts):
                    try:
                        async with aiohttp.ClientSession() as session:
                            url = f"http://localhost:{current_port}/whatsapp/webhook"
                            async with session.post(url, json=data, timeout=2) as resp:
                                if resp.status == 200:
                                    return await resp.json()
                    except Exception:
                        pass
                    
                    current_port += 1
                    if attempt == max_attempts - 1:
                        raise Exception("Max retries reached")
                
                raise Exception("Failed to connect")
            
            # Test data
            test_data = {"message": "test"}
            
            # Should succeed on second attempt (port + 1)
            result = await relay_with_retry(test_data, test_ports['main'])
            assert result['status'] in ['received', 'no_messages']
            
            # Verify alt app received the request
            assert len(alt_app.get_whatsapp_requests()) == 1
            
        finally:
            await alt_app.stop()
    
    @pytest.mark.asyncio
    async def test_concurrent_webhook_handling(self, main_app, test_ports):
        """Test handling multiple concurrent webhooks"""
        num_requests = 5
        
        async def send_webhook(index: int):
            data = {
                "entry": [{
                    "id": f"entry_{index}",
                    "changes": [{
                        "value": {
                            "messages": [{
                                "from": f"user_{index}",
                                "text": {"body": f"Message {index}"}
                            }]
                        }
                    }]
                }]
            }
            
            async with aiohttp.ClientSession() as session:
                url = f"http://localhost:{test_ports['main']}/whatsapp/webhook"
                async with session.post(url, json=data) as resp:
                    return await resp.json()
        
        # Send concurrent requests
        results = await asyncio.gather(*[send_webhook(i) for i in range(num_requests)])
        
        # Verify all succeeded
        assert all(r['status'] == 'received' for r in results)
        
        # Verify all were received
        whatsapp_requests = main_app.get_whatsapp_requests()
        assert len(whatsapp_requests) == num_requests
        
        # Verify each message was unique
        received_ids = [req['data']['entry'][0]['id'] for req in whatsapp_requests]
        assert len(set(received_ids)) == num_requests

    @pytest.mark.asyncio
    async def test_convert_endpoint_with_target_parameter(self, test_ports):
        """Test convert endpoint with new target parameter functionality"""
        
        # Simulate the relay_main convert endpoint logic
        from endpoints.standalones.relay.relay_main import handle_convert_endpoint
        from aiohttp import web
        from unittest.mock import Mock
        from urllib.parse import urlencode
        
        # Test data for conversion
        test_data = {
            'message': 'hello world',
            'user_id': '123',
            'target': 'https://example.com'  # This is the new target parameter
        }
        
        # Create mock request for POST /convert endpoint
        mock_request = Mock()
        mock_request.match_info = {'tail': 'test_path'}
        mock_request.headers = {'content-type': 'application/json'}
        mock_request.query = {}
        mock_request.text = Mock()
        mock_request.text.return_value = asyncio.Future()
        mock_request.text.return_value.set_result(json.dumps(test_data))
        
        # Test the convert endpoint handler
        response = await handle_convert_endpoint(mock_request)
        
        # Should return HTTPFound (redirect)
        assert isinstance(response, web.HTTPFound), f"Expected HTTPFound redirect, got {type(response)}"
        
        # Check redirect location
        location = response.location
        assert location is not None, "No Location in redirect response"
        
        # Should redirect to: https://example.com/test_path?message=hello+world&user_id=123
        # (target parameter should be excluded from query params)
        assert location.startswith('https://example.com/test_path'), f"Wrong redirect target: {location}"
        assert 'message=hello+world' in location, f"Missing message param in redirect: {location}"
        assert 'user_id=123' in location, f"Missing user_id param in redirect: {location}"
        assert 'target=' not in location, f"Target parameter should not be in redirect URL: {location}"


@pytest.mark.asyncio
@pytest.mark.test_real
async def test_real_relay_verifier_integration():
    """
    Real integration test using actual relay_main and _verifier_ modules
    This test validates the production webhook flow
    """
    # Set up test environment
    test_env = {
        'ZAIRA_PYTHON_PORT': str(MAIN_APP_PORT),
        'WHATSAPP_VERIFY_TOKEN': 'test_verify_token',
        'POSTGRES_HOST': 'localhost',
        'POSTGRES_DB': 'vectordb',
        'OPENAI_API_KEY': 'test_key'
    }
    
    with patch.dict('os.environ', test_env):
        # Mock database connections
        with patch('managers.manager_postgreSQL.PostgreSQLManager.get_instance') as mock_db:
            mock_db_instance = AsyncMock()
            mock_db.return_value = mock_db_instance
            
            # Test webhook data
            webhook_test_data = {
                "entry": [{
                    "id": "real_test_123",
                    "changes": [{
                        "value": {
                            "messages": [{
                                "from": "1234567890",
                                "text": {"body": "Real integration test"}
                            }]
                        }
                    }]
                }]
            }
            
            # Import actual modules
            try:
                # First, let's check if the files exist
                import os
                relay_path = os.path.join(os.path.dirname(__file__), '../../endpoints/standalones/relay/relay_main.py')
                verifier_path = os.path.join(os.path.dirname(__file__), '../../endpoints/oauth/_verifier_.py')
                whatsapp_path = os.path.join(os.path.dirname(__file__), '../../endpoints/whatsapp_endpoint.py')
                
                LogFire.log("DEBUG", "Checking paths:", severity="debug")
                LogFire.log("DEBUG", f"  relay_main.py exists: {os.path.exists(relay_path)}", severity="debug")
                LogFire.log("DEBUG", f"  _verifier_.py exists: {os.path.exists(verifier_path)}", severity="debug")
                LogFire.log("DEBUG", f"  whatsapp_endpoint.py exists: {os.path.exists(whatsapp_path)}", severity="debug")
                
                # Try importing the modules
                import sys
                sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))
                
                # Import relay_main functions
                from endpoints.standalones.relay.relay_main import handle_whatsapp_webhook, handle_proxy, create_app
                LogFire.log("DEBUG", "Successfully imported relay_main functions", severity="debug")
                
                # Import _verifier_ classes
                from endpoints.oauth._verifier_ import OAuth2Verifier, OAuth2App
                LogFire.log("DEBUG", "Successfully imported _verifier_ classes", severity="debug")
                
                # Import WhatsApp endpoint
                from endpoints import whatsapp_endpoint
                LogFire.log("DEBUG", "Successfully imported whatsapp_endpoint module", severity="debug")
                
                # Verify the imports exist
                assert handle_whatsapp_webhook is not None
                assert handle_proxy is not None
                assert create_app is not None
                assert OAuth2Verifier is not None
                assert OAuth2App is not None
                assert whatsapp_endpoint is not None
                LogFire.log("DEBUG", "All modules and functions verified", severity="debug")
                
                # Test OAuth state parsing (from relay_main logic)
                test_state = "python-test_identifier"
                parts = test_state.split('-', 1)
                assert parts[0] == "python"
                assert parts[1] == "test_identifier"
                LogFire.log("DEBUG", "OAuth state parsing test passed", severity="debug")
                
                # Verify webhook structure matches expected format
                assert 'entry' in webhook_test_data
                assert len(webhook_test_data['entry']) > 0
                assert 'changes' in webhook_test_data['entry'][0]
                LogFire.log("DEBUG", "Webhook structure verification passed", severity="debug")
                
                # Test the actual webhook relay logic would happen here
                # In production, this would involve:
                # 1. Starting the relay server on port 8084
                # 2. Starting the main app on port 41000
                # 3. Sending test webhooks through the relay
                # 4. Verifying they reach the main app correctly
                
                LogFire.log("DEBUG", "\nReal integration test completed successfully!", severity="debug")
                LogFire.log("DEBUG", "Note: This test verifies module imports and basic structure.", severity="debug")
                LogFire.log("DEBUG", "Full end-to-end testing would require running actual servers.", severity="debug")
                
            except ImportError as e:
                LogFire.log("ERROR", f"Import error details: {e}", severity="error")
                import traceback
                traceback.print_exc()
                pytest.skip(f"Required modules not available: {e}")
            except Exception as e:
                LogFire.log("ERROR", f"Unexpected error: {e}", severity="error")
                import traceback
                traceback.print_exc()
                raise