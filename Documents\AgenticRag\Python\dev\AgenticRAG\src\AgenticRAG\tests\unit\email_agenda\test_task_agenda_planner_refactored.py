"""
Unit tests for refactored task_agenda_planner.py - Planning/generation logic only
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from imports import *
import json
import sys
from os import path as os_path

# Add the parent directory to the sys.path
sys.path.insert(0, os_path.join(os_path.dirname(__file__), '../../../'))

@pytest.mark.unit
@pytest.mark.asyncio
class TestAgendaPlannerRefactored:
    """Unit tests for refactored agenda planner (planning/generation only)"""
    
    async def test_agenda_planner_tool_initialization(self):
        """Test AgendaPlannerTool initialization"""
        from tasks.processing.task_agenda_planner import AgendaPlannerTool
        
        tool = AgendaPlannerTool()
        assert tool.name == "agenda_planner_tool"
        assert tool.description == "Plans and generates agenda content, schedules, and event details"
    
    async def test_agenda_planner_tool_run_not_implemented(self):
        """Test that sync _run method raises NotImplementedError"""
        from tasks.processing.task_agenda_planner import AgendaPlannerTool
        
        tool = AgendaPlannerTool()
        
        with pytest.raises(NotImplementedError):
            tool._run("test request")
    
    async def test_agenda_planner_tool_successful_generation(self):
        """Test successful agenda content generation"""
        from tasks.processing.task_agenda_planner import AgendaPlannerTool
        
        tool = AgendaPlannerTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.scheduled_guid = "test-scheduled-guid"
        mock_state.sections = {}
        
        # Create mock user and request
        mock_user = MagicMock()
        mock_request = MagicMock()
        mock_request.output_demands = []
        mock_user.my_requests = {"test-scheduled-guid": mock_request}
        
        # Mock the calendar tools import from output task
        with patch('tasks.outputs.output_tasks.task_out_agenda.get_calendar_tools', return_value=[]):
            # Mock the user manager
            with patch('tasks.processing.task_agenda_planner.ZairaUserManager') as mock_zum:
                mock_zum.get_instance.return_value.find_user = AsyncMock(return_value=mock_user)
                
                # Mock the agenda content generation
                with patch.object(tool, '_generate_agenda_content', return_value="Generated agenda content"):
                    result = await tool._arun(
                        request="Plan a team meeting",
                        start_date="2024-01-15",
                        end_date="2024-01-15",
                        state=mock_state
                    )
                
                # Parse result as JSON
                result_data = json.loads(result)
                
                assert result_data['agenda_content'] == "Generated agenda content"
                assert result_data['start_date'] == "2024-01-15"
                assert result_data['end_date'] == "2024-01-15"
                assert result_data['request'] == "Plan a team meeting"
                assert result_data['tools_available'] == False
                
                # Check state was updated
                assert mock_state.sections['generated_agenda'] == "Generated agenda content"
                assert mock_state.sections['agenda_start_date'] == "2024-01-15"
                assert mock_state.sections['agenda_end_date'] == "2024-01-15"
    
    async def test_agenda_planner_tool_exception_handling(self):
        """Test agenda planner tool exception handling"""
        from tasks.processing.task_agenda_planner import AgendaPlannerTool
        
        tool = AgendaPlannerTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        
        # Mock calendar tools to throw exception
        with patch('tasks.outputs.output_tasks.task_out_agenda.get_calendar_tools', side_effect=Exception("Import error")):
            with patch('etc.helper_functions.exception_triggered', return_value=None):
                result = await tool._arun("test request", state=mock_state)
                
                result_data = json.loads(result)
                assert "error" in result_data
                assert "Failed to generate agenda" in result_data['error']
                assert result_data['agenda_content'] == "Error occurred during agenda planning"
    
    async def test_agenda_planner_generate_content_success(self):
        """Test successful agenda content generation using LLM"""
        from tasks.processing.task_agenda_planner import AgendaPlannerTool
        
        tool = AgendaPlannerTool()
        
        # Mock LLM components
        with patch('langchain_openai.ChatOpenAI') as mock_llm:
            with patch('langchain.prompts.ChatPromptTemplate') as mock_template:
                # Mock LLM response
                mock_result = MagicMock()
                mock_result.content = "Detailed agenda content with meeting structure"
                
                mock_chain = MagicMock()
                mock_chain.ainvoke = AsyncMock(return_value=mock_result)
                
                mock_prompt = MagicMock()
                mock_prompt.__or__ = MagicMock(return_value=mock_chain)
                mock_template.from_template.return_value = mock_prompt
                
                result = await tool._generate_agenda_content(
                    request="Plan a team meeting",
                    start_date="2024-01-15",
                    end_date="2024-01-15"
                )
                
                assert result == "Detailed agenda content with meeting structure"
                mock_chain.ainvoke.assert_called_once()
    
    async def test_agenda_planner_generate_content_exception(self):
        """Test agenda content generation exception handling"""
        from tasks.processing.task_agenda_planner import AgendaPlannerTool
        
        tool = AgendaPlannerTool()
        
        # Mock LLM to throw exception
        with patch('langchain_openai.ChatOpenAI', side_effect=Exception("LLM error")):
            result = await tool._generate_agenda_content("test request")
            
            assert "Error generating agenda content" in result
            assert "LLM error" in result
    
    async def test_agenda_generator_tool_initialization(self):
        """Test AgendaGeneratorTool initialization"""
        from tasks.processing.task_agenda_planner import AgendaGeneratorTool
        
        tool = AgendaGeneratorTool()
        assert tool.name == "agenda_generator_tool"
        assert tool.description == "Generates specific calendar event details including times, locations, and descriptions"
    
    async def test_agenda_generator_tool_successful_generation(self):
        """Test successful event details generation"""
        from tasks.processing.task_agenda_planner import AgendaGeneratorTool
        
        tool = AgendaGeneratorTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {}
        
        # Mock successful event details generation
        mock_event_details = {
            "summary": "Team Workshop",
            "description": "Detailed workshop description",
            "location": "Conference Room A"
        }
        
        with patch.object(tool, '_generate_event_details', return_value=mock_event_details):
            result = await tool._arun(
                event_type="workshop",
                duration="2 hours",
                preferences="morning preferred",
                state=mock_state
            )
            
            # Parse result as JSON
            result_data = json.loads(result)
            
            assert result_data['summary'] == "Team Workshop"
            assert result_data['description'] == "Detailed workshop description"
            assert result_data['location'] == "Conference Room A"
            
            # Check state was updated
            assert mock_state.sections['generated_event_details'] == mock_event_details
            assert mock_state.sections['event_type'] == "workshop"
            assert mock_state.sections['event_duration'] == "2 hours"
    
    async def test_agenda_generator_tool_exception_handling(self):
        """Test agenda generator tool exception handling"""
        from tasks.processing.task_agenda_planner import AgendaGeneratorTool
        
        tool = AgendaGeneratorTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        
        # Mock event details generation to throw exception
        with patch.object(tool, '_generate_event_details', side_effect=Exception("Generation error")):
            with patch('etc.helper_functions.exception_triggered', return_value=None):
                result = await tool._arun("workshop", state=mock_state)
                
                result_data = json.loads(result)
                assert "error" in result_data
                assert "Failed to generate event details" in result_data['error']
    
    async def test_agenda_generator_generate_event_details_success(self):
        """Test successful event details generation using LLM"""
        from tasks.processing.task_agenda_planner import AgendaGeneratorTool
        
        tool = AgendaGeneratorTool()
        
        # Mock LLM components
        with patch('langchain_openai.ChatOpenAI') as mock_llm:
            with patch('langchain.prompts.ChatPromptTemplate') as mock_template:
                # Mock LLM response with JSON-like content
                mock_result = MagicMock()
                mock_result.content = '{"summary": "Workshop", "description": "Detailed description"}'
                
                mock_chain = MagicMock()
                mock_chain.ainvoke = AsyncMock(return_value=mock_result)
                
                mock_prompt = MagicMock()
                mock_prompt.__or__ = MagicMock(return_value=mock_chain)
                mock_template.from_template.return_value = mock_prompt
                
                result = await tool._generate_event_details("workshop", "2 hours", "preferences")
                
                assert result['summary'] == "Workshop"
                assert result['description'] == "Detailed description"
    
    async def test_agenda_generator_generate_event_details_fallback(self):
        """Test event details generation with non-JSON response"""
        from tasks.processing.task_agenda_planner import AgendaGeneratorTool
        
        tool = AgendaGeneratorTool()
        
        # Mock LLM components
        with patch('langchain_openai.ChatOpenAI') as mock_llm:
            with patch('langchain.prompts.ChatPromptTemplate') as mock_template:
                # Mock LLM response with non-JSON content
                mock_result = MagicMock()
                mock_result.content = "This is a plain text response about the workshop"
                
                mock_chain = MagicMock()
                mock_chain.ainvoke = AsyncMock(return_value=mock_result)
                
                mock_prompt = MagicMock()
                mock_prompt.__or__ = MagicMock(return_value=mock_chain)
                mock_template.from_template.return_value = mock_prompt
                
                result = await tool._generate_event_details("workshop", "2 hours", "preferences")
                
                assert result['summary'] == "workshop - 2 hours"
                assert result['description'] == "This is a plain text response about the workshop"
                assert result['generated_content'] == "This is a plain text response about the workshop"
    
    async def test_agenda_generator_generate_event_details_exception(self):
        """Test event details generation exception handling"""
        from tasks.processing.task_agenda_planner import AgendaGeneratorTool
        
        tool = AgendaGeneratorTool()
        
        # Mock LLM to throw exception
        with patch('langchain_openai.ChatOpenAI', side_effect=Exception("LLM error")):
            result = await tool._generate_event_details("workshop", "2 hours", "preferences")
            
            assert result['summary'] == "workshop"
            assert "Error generating details" in result['description']
            assert result['error'] == "LLM error"
    
    async def test_agenda_planning_tool_decorator_success(self):
        """Test agenda_planning_tool decorator function"""
        from tasks.processing.task_agenda_planner import agenda_planning_tool
        
        # Mock the AgendaPlannerTool
        with patch('tasks.processing.task_agenda_planner.AgendaPlannerTool') as mock_tool_class:
            mock_tool_instance = MagicMock()
            mock_tool_instance._arun = AsyncMock(return_value='{"success": true}')
            mock_tool_class.return_value = mock_tool_instance
            
            result = await agenda_planning_tool.ainvoke({
                "request": "Plan meeting",
                "timeframe": "next week",
                "preferences": "morning preferred"
            })
            
            assert result == '{"success": true}'
            mock_tool_instance._arun.assert_called_once()
    
    async def test_agenda_planning_tool_decorator_exception(self):
        """Test agenda_planning_tool decorator exception handling"""
        from tasks.processing.task_agenda_planner import agenda_planning_tool
        
        # Mock the AgendaPlannerTool to throw exception
        with patch('tasks.processing.task_agenda_planner.AgendaPlannerTool', side_effect=Exception("Tool error")):
            result = await agenda_planning_tool.ainvoke({
                "request": "Plan meeting"
            })
            
            result_data = json.loads(result)
            assert "error" in result_data
            assert "Agenda planning failed" in result_data['error']
    
    async def test_event_generation_tool_decorator_success(self):
        """Test event_generation_tool decorator function"""
        from tasks.processing.task_agenda_planner import event_generation_tool
        
        # Mock the AgendaGeneratorTool
        with patch('tasks.processing.task_agenda_planner.AgendaGeneratorTool') as mock_tool_class:
            mock_tool_instance = MagicMock()
            mock_tool_instance._arun = AsyncMock(return_value='{"summary": "Workshop"}')
            mock_tool_class.return_value = mock_tool_instance
            
            result = await event_generation_tool.ainvoke({
                "event_type": "workshop",
                "duration": "2 hours",
                "requirements": "projector needed"
            })
            
            assert result == '{"summary": "Workshop"}'
            mock_tool_instance._arun.assert_called_once()
    
    async def test_event_generation_tool_decorator_exception(self):
        """Test event_generation_tool decorator exception handling"""
        from tasks.processing.task_agenda_planner import event_generation_tool
        
        # Mock the AgendaGeneratorTool to throw exception
        with patch('tasks.processing.task_agenda_planner.AgendaGeneratorTool', side_effect=Exception("Generator error")):
            result = await event_generation_tool.ainvoke({
                "event_type": "workshop"
            })
            
            result_data = json.loads(result)
            assert "error" in result_data
            assert "Event generation failed" in result_data['error']
    
    async def test_create_supervisor_agenda_planner(self):
        """Test create_supervisor_agenda_planner function"""
        from tasks.processing.task_agenda_planner import create_supervisor_agenda_planner
        
        # Mock all the required dependencies
        with patch('tasks.outputs.output_tasks.task_out_agenda.get_calendar_tools', return_value=[]):
            with patch('tasks.processing.task_agenda_planner.SupervisorManager') as mock_supervisor_manager:
                mock_supervisor = MagicMock()
                mock_supervisor.add_task.return_value = mock_supervisor
                mock_supervisor.compile.return_value = mock_supervisor
                
                mock_supervisor_manager.register_task.return_value = MagicMock()
                mock_supervisor_manager.register_supervisor.return_value = mock_supervisor
                
                result = await create_supervisor_agenda_planner()
                
                # Verify supervisor was created and configured
                mock_supervisor_manager.register_supervisor.assert_called_once()
                mock_supervisor_manager.register_task.assert_called_once()
                assert result == mock_supervisor