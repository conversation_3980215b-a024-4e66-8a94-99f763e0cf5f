from imports import *

from langchain_core.tools import BaseTool
from langchain_core.language_models.base import BaseLanguageModel
from typing import Literal

from managers.manager_supervisors import Supervisor<PERSON>anager, SupervisorSupervisor, SupervisorTask_Base, SupervisorTask_SingleAgent, SupervisorTaskState, SupervisorSection
from managers.manager_users import ZairaUserManager
from managers.scheduled_requests import ScheduledRequestPersistenceManager
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
from endpoints.mybot_generic import MyBot_Generic

class CreateScheduledRequestTool(BaseTool):
    """Tool for creating new scheduled tasks"""
    name: str = "create_scheduled_request"
    description: str = "Create a new scheduled task based on natural language prompt"

    confirm_create: bool = False
    
    def _run(self, schedule_prompt: str, target_prompt: str, start_delay_seconds: int, delay_seconds: int, schedule_type: Literal["once", "recurring"], run_on_startup: bool, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, schedule_prompt: str, target_prompt: str, start_delay_seconds: int, delay_seconds: int, schedule_type: Literal["once", "recurring"], run_on_startup: bool, state: SupervisorTaskState = None) -> str:
        """Create a new scheduled task"""
        try:
            from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest
            from managers.manager_users import ZairaUserManager
            
            user = await ZairaUserManager.find_user(state.user_guid)
            if not user:
                return f"Username not found: {state.user_guid}"
            
            # Get chat session for logging
            chat_session = user.get_current_chat_session() if user else None
                
            # Create bot instance for the task
            bot = MyBot_Generic(None, "scheduled_request_manager")
            
            # Convert schedule_type string to ScheduleType enum
            from userprofiles.ScheduledZairaRequest import ScheduleType
            schedule_type_enum = ScheduleType.RECURRING if schedule_type == "recurring" else ScheduleType.ONCE
            
            # Create the scheduled task with pre-parsed parameters from the agent
            task = ScheduledZairaRequest(
                user=user, 
                calling_bot=bot, 
                original_message=None, 
                schedule_prompt=schedule_prompt,
                target_prompt=target_prompt,
                start_delay_seconds=float(start_delay_seconds),
                delay_seconds=float(delay_seconds),
                schedule_type=schedule_type_enum,
                run_on_startup=run_on_startup
            )

            async def confirm_creation(task: LongRunningZairaRequest, response: str):
                self.confirm_create = 'j' in response
            
            if state.scheduled_guid != -1:
                await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                    f"Schedule prompt: {task.schedule_prompt}. Task prompt: {task.target_prompt}. Start delay: {task.start_delay_seconds}. Recurring: {'Once' if task.schedule_type == ScheduleType.ONCE else task.delay_seconds}. Run on startup: {task.run_on_startup}\n\nWil je deze taak in de toekomst uitvoeren? (j/n)", 
                    confirm_creation, 
                    True
                )
            else:
                self.confirm_create = True
            if self.confirm_create:
                # No need to wait for parsing since parameters are already provided
                # Check if parsing failed (shouldn't happen with pre-parsed parameters)
                if getattr(task, 'parsing_failed', False):
                    failure_reason = getattr(task, 'parsing_failure_reason', 'Unknown parsing error')
                    return f"Failed to create scheduled task due to parsing error: {failure_reason}"
                
                # Add to user's my_requests collection for immediate visibility
                user.my_requests[task.scheduled_guid] = task
                LogFire.log("TASK", f"Added scheduled task {task.scheduled_guid} to user's my_requests", chat=chat_session)
                
                # Start the task in its own thread to prevent blocking main loop
                self._start_task_in_thread(task, str(task.scheduled_guid))
            
            return f"Created scheduled task: {task.scheduled_guid} - {schedule_prompt}"
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "Failed to create scheduled task", user if 'user' in locals() else None)
            return f"Failed to create scheduled task: {str(e)}"
    
    def _start_task_in_thread(self, scheduled_request: "LongRunningZairaRequest", scheduled_guid: str):
        """Start a scheduled task in its own thread to prevent blocking main loop"""
        import threading
        import asyncio
        
        def run_task_in_thread():
            """Run the scheduled task in a separate thread with its own event loop"""
            try:
                # Create new event loop for this thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                try:
                    # Run the task in this thread's event loop
                    LogFire.log("TASK", f"Thread started for new scheduled task {scheduled_guid}", chat=None)
                    loop.run_until_complete(scheduled_request.run_request())
                    LogFire.log("TASK", f"Thread completed for new scheduled task {scheduled_guid}", chat=None)
                except Exception as e:
                    LogFire.log("ERROR", f"Thread execution failed for new task {scheduled_guid}: {str(e)}", chat=None)
                finally:
                    loop.close()
            except Exception as e:
                LogFire.log("ERROR", f"Failed to create thread for new task {scheduled_guid}: {str(e)}", chat=None)
        
        # Start the thread
        thread = threading.Thread(target=run_task_in_thread, daemon=True)
        thread.start()
        LogFire.log("TASK", f"Started thread for new scheduled task {scheduled_guid}", chat=None)


class ListScheduledRequestsTool(BaseTool):
    """Tool for listing scheduled tasks"""
    name: str = "list_scheduled_requests"
    description: str = "List all active scheduled tasks for a user"
    
    def _run(self, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, state: SupervisorTaskState = None) -> str:
        """List all active scheduled tasks"""
        try:
            persistence_manager = ScheduledRequestPersistenceManager.get_instance()
            tasks = await persistence_manager.get_active_requests(state.user_guid)
            
            if not tasks:
                return "No active scheduled tasks found"
                
            task_list = []
            for task in tasks:
                task_info = (
                    f"Scheduled GUID: {task['scheduled_guid']}\n"
                    f"Schedule: {task['schedule_prompt']}\n"
                    f"Target: {task['target_prompt']}\n"
                    f"Start delay: {task.get('start_delay_seconds', 0)} seconds\n"
                    f"Recurring delay: {task['delay_seconds']} seconds\n"
                    f"Next execution: {task['next_execution']}\n"
                    f"Created: {task['created_at']}\n"
                    "---"
                )
                task_list.append(task_info)
                
            return f"Active scheduled tasks:\n\n" + "\n".join(task_list)
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "Failed to list scheduled tasks", None)
            return f"Failed to list scheduled tasks: {str(e)}"


class CancelScheduledRequestTool(BaseTool):
    """Tool for canceling scheduled tasks"""
    name: str = "cancel_scheduled_request"
    description: str = "Cancel a scheduled task by ID"
    
    def _run(self, scheduled_guid: str, reason: str = "User requested cancellation", state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, scheduled_guid: str, reason: str = "User requested cancellation", state: SupervisorTaskState = None) -> str:
        """Cancel a scheduled task"""
        try:
            persistence_manager = ScheduledRequestPersistenceManager.get_instance()
            success = await persistence_manager.cancel_task(scheduled_guid, reason)
            
            if success:
                return f"Successfully cancelled task {scheduled_guid}"
            else:
                return f"Failed to cancel task {scheduled_guid}"
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "Failed to cancel scheduled task", None)
            return f"Failed to cancel task: {str(e)}"


class ScheduledRequestCreateTask(SupervisorTask_SingleAgent):
    """Task for creating new scheduled tasks"""
    
    def __init__(self, model: BaseLanguageModel = None):
        tools = [CreateScheduledRequestTool()]
        super().__init__(
            name="create_scheduled_request",
            prompt="You are specialized in creating scheduled tasks. Parse the user's natural language request and create the appropriate scheduled task using the create_scheduled_request tool.",
            tools=tools,
            model=model
        )


class ScheduledRequestListTask(SupervisorTask_SingleAgent):
    """Task for listing scheduled tasks"""
    
    def __init__(self, model: BaseLanguageModel = None):
        tools = [ListScheduledRequestsTool()]
        super().__init__(
            name="list_scheduled_requests",
            prompt="You are specialized in listing scheduled tasks. Use the list_scheduled_requests tool to show all active scheduled tasks for the user.",
            tools=tools,
            model=model
        )


class ScheduledRequestCancelTask(SupervisorTask_SingleAgent):
    """Task for canceling scheduled tasks"""
    
    def __init__(self, model: BaseLanguageModel = None):
        tools = [CancelScheduledRequestTool()]
        super().__init__(
            name="cancel_scheduled_request",
            prompt="You are specialized in canceling scheduled tasks. Use the cancel_scheduled_request tool to cancel tasks by ID.",
            tools=tools,
            model=model
        )


class ScheduledRequestInfoTask(SupervisorTask_Base):
    """Task for providing information about scheduled tasks"""
    
    def __init__(self, model: BaseLanguageModel = None):
        super().__init__(
            name="scheduled_request_info",
            prompt="You are specialized in providing information about scheduled tasks. Explain how scheduled tasks work and what operations are available.",
            model=model
        )
    
    async def llm_call(self, state: SupervisorTaskState) -> str:
        """Provide information about scheduled tasks"""
        info = """
        Scheduled Tasks Information:
        
        I can help you manage scheduled tasks with the following operations:
        
        1. **Create Tasks**: Schedule tasks to run at specific times or intervals
           - Example: "Send me a reminder in 30 minutes"
           - Example: "Check my emails every hour"
           
        2. **List Tasks**: Show all your active scheduled tasks
           - Example: "Show me my scheduled tasks"
           - Example: "List all my recurring tasks"
           
        3. **Cancel Tasks**: Remove scheduled tasks you no longer need
           - Example: "Cancel task [scheduled_guid]"
           - Example: "Stop my hourly email check"
           
        4. **Task Types**:
           - **One-time**: Run once at a specific time
           - **Recurring**: Run repeatedly at intervals
           
        The system supports natural language scheduling like:
        - "Remind me to call John in 2 hours"
        - "Send me a daily report at 9am"
        - "Check for new emails every 15 minutes"
        
        What would you like to do with scheduled tasks?
        """
        return info


async def create_task_scheduled_request_manager() -> SupervisorTask_Base:
    """Create and register the scheduled task manager supervisor"""
    # Create the supervisor with routing logic
    supervisor = SupervisorSupervisor(
        name="scheduled_request_manager",
        prompt="""You are the Scheduled Task Manager supervisor. Route requests to the appropriate task based on user intent:

- For creating/scheduling new tasks: route to create_scheduled_request
- For listing/showing existing tasks: route to list_scheduled_requests  
- For canceling/removing tasks: route to cancel_scheduled_request
- For general information about scheduled tasks: route to scheduled_request_info

Analyze the user's request and determine the most appropriate action."""
    )
    
    # Add all task types to the supervisor
    supervisor.add_task(ScheduledRequestCreateTask())
    supervisor.add_task(ScheduledRequestListTask())
    supervisor.add_task(ScheduledRequestCancelTask())
    supervisor.add_task(ScheduledRequestInfoTask())
    
    # Compile the supervisor
    supervisor.compile()
    
    return SupervisorManager.register_supervisor(supervisor)