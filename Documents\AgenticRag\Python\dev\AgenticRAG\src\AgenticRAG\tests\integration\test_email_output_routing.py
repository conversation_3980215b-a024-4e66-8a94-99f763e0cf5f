"""
Integration test for email output routing verification
Tests that email_generator_task properly adds "email" to output_demands for email_out routing
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from managers.manager_logfire import LogFire
import pytest
import pytest_asyncio
import asyncio
from uuid import uuid4
from unittest.mock import MagicMock, AsyncMock, patch
from typing import List

from managers.manager_supervisors import SupervisorManager, SupervisorTaskState
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import <PERSON><PERSON><PERSON><PERSON><PERSON>, PERMISSION_LEVELS
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
from tasks.processing.task_email_generator import EmailGeneratorTool, create_task_email_generator
from tasks.outputs.output_tasks.task_out_email import create_out_task_email
from langchain_core.messages import HumanMessage

# Global execution tracker
email_output_trace = []

@pytest_asyncio.fixture
async def setup_email_routing_test():
    """Setup email routing test components"""
    global email_output_trace
    email_output_trace.clear()
    
    # Simple setup without complex mocking
    yield {
        'test_setup': True
    }

@pytest_asyncio.fixture
async def setup_user_and_task():
    """Setup test user and task"""
    # Create test user
    test_user = ZairaUser(
        username="test_user",
        rank=PERMISSION_LEVELS.ADMIN,
        guid=uuid4(),
        device_guid=uuid4()
    )
    test_user.email = "<EMAIL>"
    
    # Create mock calling bot
    mock_bot = MagicMock()
    mock_bot.name = "test_bot"
    mock_bot.send_reply = AsyncMock()
    mock_bot.request_human_in_the_loop_internal = AsyncMock()
    
    # Create LongRunningZairaRequest
    test_task = LongRunningZairaRequest(
        user=test_user,
        complete_message="Test email generation",
        calling_bot=mock_bot,
        original_message=None
    )
    
    # Add task to user
    test_user.my_requests[test_task.scheduled_guid] = test_task
    
    return {
        'user': test_user,
        'task': test_task,
        'bot': mock_bot
    }

class MockEmailOutTask:
    """Mock email_out task that tracks execution"""
    def __init__(self):
        self.name = "email_out"
        self.executed = False
        self.execution_data = None
    
    async def llm_call(self, state: SupervisorTaskState):
        """Mock email_out execution"""
        global email_output_trace
        email_output_trace.append(f"email_out_executed:{state.user_guid}")
        
        self.executed = True
        self.execution_data = {
            'state': state,
            'email_data': state.sections.get('generated_email') if hasattr(state, 'sections') else None,
            'output_demands': []
        }
        
        # Simulate email sending
        return "Email sent successfully via email_out task"

@pytest.mark.integration
@pytest.mark.asyncio
class TestEmailOutputRouting:
    """Test email generator → email_out routing via output_demands"""
    
    async def test_email_generator_adds_output_demands(self, setup_email_routing_test, setup_user_and_task):
        """Test that EmailGeneratorTool adds 'email' to output_demands"""
        global email_output_trace
        email_output_trace.clear()
        
        test_data = await setup_user_and_task
        user = test_data['user']
        task = test_data['task']
        
        # Create state for email generation
        state = SupervisorTaskState(
            user_guid=user.user_guid,
            scheduled_guid=task.scheduled_guid,
            messages=[HumanMessage(content="Generate test <NAME_EMAIL>")],
            sections={}
        )
        
        # Mock human in the loop approval
        async def mock_approval_callback(task_ref, response):
            # Simulate user approval
            state.sections['email_approved'] = True
        
        # Patch request_human_in_the_loop to auto-approve
        original_hitl = task.request_human_in_the_loop
        async def auto_approve_hitl(request, callback, halt=False):
            await callback(task, "j")  # Auto-approve
        
        task.request_human_in_the_loop = auto_approve_hitl
        
        # Create EmailGeneratorTool
        email_tool = EmailGeneratorTool()
        
        # Execute email generation
        try:
            result = await email_tool._arun(
                content_request="Send test <NAME_EMAIL>",
                subject_hint="Test Email",
                sender="<EMAIL>",
                recipient="<EMAIL>",
                state=state
            )
            
            # Verify output_demands was modified
            assert "email" in task.output_demands, \
                f"Email generator should add 'email' to output_demands. Current demands: {task.output_demands}"
            
            # Verify email data was stored in state
            assert hasattr(state, 'sections'), "State should have sections"
            assert 'generated_email' in state.sections, "Email data should be stored in state sections"
            
            email_data = state.sections['generated_email']
            assert email_data['sender'] == "<EMAIL>"
            assert email_data['recipient'] == "<EMAIL>"
            assert email_data['subject'] == "Test Email"
            
            LogFire.log("DEBUG", f"SUCCESS: Email generator added to output_demands: {task.output_demands}", severity="debug")
            LogFire.log("DEBUG", f"Email data stored: {email_data}", severity="debug")
            
        except Exception as e:
            pytest.fail(f"Email generation failed: {e}")
        finally:
            # Restore original method
            task.request_human_in_the_loop = original_hitl
    
    async def test_email_output_routing_integration(self, setup_email_routing_test, setup_user_and_task):
        """Test full integration: email_generator → output supervisor → email_out"""
        global email_output_trace
        email_output_trace.clear()
        
        test_data = await setup_user_and_task
        user = test_data['user']
        task = test_data['task']
        
        # Setup SupervisorManager with mock email_out task
        mock_email_out = MockEmailOutTask()
        
        # Mock SupervisorManager.get_task to return our mock email_out
        original_get_task = SupervisorManager.get_task
        SupervisorManager.get_task = lambda name: mock_email_out if name == "email_out" else None
        
        try:
            # Step 1: Generate email with EmailGeneratorTool
            state = SupervisorTaskState(
                user_guid=user.user_guid,
                scheduled_guid=task.scheduled_guid,
                messages=[HumanMessage(content="Generate test email")],
                sections={}
            )
            
            # Mock human approval
            async def auto_approve_hitl(request, callback, halt=False):
                await callback(task, "j")  # Auto-approve
            
            task.request_human_in_the_loop = auto_approve_hitl
            
            email_tool = EmailGeneratorTool()
            await email_tool._arun(
                content_request="Send test email",
                subject_hint="Integration Test",
                sender="<EMAIL>", 
                recipient="<EMAIL>",
                state=state
            )
            
            # Verify email was added to output_demands
            assert "email" in task.output_demands, "Email should be in output_demands"
            
            # Step 2: Simulate output supervisor routing
            # This simulates the logic from task_top_output_supervisor.py:81-84
            for output_demand in task.output_demands:
                if output_demand == "email":
                    # This simulates: SupervisorManager.get_task("email_out")
                    output_task = SupervisorManager.get_task("email_out")
                    if output_task is not None:
                        await output_task.llm_call(state)
            
            # Step 3: Verify email_out was executed
            assert mock_email_out.executed, "email_out task should have been executed"
            assert len(email_output_trace) > 0, "Email output trace should contain execution"
            assert f"email_out_executed:{user.user_guid}" in email_output_trace
            
            # Verify email data was passed to email_out
            assert mock_email_out.execution_data is not None
            email_data = mock_email_out.execution_data['email_data']
            assert email_data is not None, "Email data should be passed to email_out"
            assert email_data['subject'] == "Integration Test"
            assert email_data['recipient'] == "<EMAIL>"
            
            LogFire.log("DEBUG", "SUCCESS: Complete email routing flow verified!", severity="debug")
            LogFire.log("DEBUG", f"Output trace: {email_output_trace}", severity="debug")
            LogFire.log("DEBUG", f"Email_out received data: {email_data}", severity="debug")
            
        finally:
            # Restore original method
            SupervisorManager.get_task = original_get_task
    
    async def test_email_routing_without_approval(self, setup_email_routing_test, setup_user_and_task):
        """Test that email_out is not triggered if email is not approved"""
        test_data = await setup_user_and_task
        user = test_data['user']
        task = test_data['task']
        
        # Create state
        state = SupervisorTaskState(
            user_guid=user.user_guid,
            scheduled_guid=task.scheduled_guid,
            messages=[HumanMessage(content="Generate test email")],
            sections={}
        )
        
        # Mock human rejection
        async def auto_reject_hitl(request, callback, halt=False):
            await callback(task, "n")  # Reject
        
        task.request_human_in_the_loop = auto_reject_hitl
        
        email_tool = EmailGeneratorTool()
        await email_tool._arun(
            content_request="Send test email",
            sender="<EMAIL>",
            recipient="<EMAIL>", 
            state=state
        )
        
        # Email should still be added to output_demands even if not approved
        # (The email_out task will check approval status)
        assert "email" in task.output_demands, "Email should be in output_demands even if not approved"
        
        # But email_approved should be False or missing
        email_approved = state.sections.get('email_approved', False)
        assert not email_approved, "Email should not be approved"
        
        LogFire.log("DEBUG", "SUCCESS: Email routing works correctly for non-approved emails", severity="debug")
    
    async def test_output_demands_unique_entries(self, setup_email_routing_test, setup_user_and_task):
        """Test that multiple email generations don't duplicate output_demands"""
        test_data = await setup_user_and_task
        user = test_data['user']
        task = test_data['task']
        
        # Mock auto-approval
        async def auto_approve_hitl(request, callback, halt=False):
            await callback(task, "j")
        
        task.request_human_in_the_loop = auto_approve_hitl
        
        email_tool = EmailGeneratorTool()
        
        # Generate first email
        state1 = SupervisorTaskState(
            user_guid=user.user_guid,
            scheduled_guid=task.scheduled_guid,
            messages=[HumanMessage(content="Generate first email")],
            sections={}
        )
        
        await email_tool._arun(
            content_request="First email",
            sender="<EMAIL>",
            recipient="<EMAIL>",
            state=state1
        )
        
        initial_demands = task.output_demands.copy()
        email_count_initial = initial_demands.count("email")
        
        # Generate second email
        state2 = SupervisorTaskState(
            user_guid=user.user_guid,
            scheduled_guid=task.scheduled_guid,
            messages=[HumanMessage(content="Generate second email")],
            sections={}
        )
        
        await email_tool._arun(
            content_request="Second email",
            sender="<EMAIL>", 
            recipient="<EMAIL>",
            state=state2
        )
        
        final_demands = task.output_demands
        email_count_final = final_demands.count("email")
        
        # Should only add one more "email" entry
        assert email_count_final == email_count_initial + 1, \
            f"Should only add one more email entry. Initial: {email_count_initial}, Final: {email_count_final}"
        
        LogFire.log("DEBUG", f"SUCCESS: Output demands managed correctly. Initial count: {email_count_initial}, Final count: {email_count_final}", severity="debug")
        LogFire.log("DEBUG", f"Final output_demands: {final_demands}", severity="debug")