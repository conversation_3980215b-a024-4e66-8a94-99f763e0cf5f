# VS Code Batch Test Integration

This directory contains Python wrapper tests that enable VS Code's "Run Tests" feature to execute the token-efficient batch files for `test_real_*` scripts.

## How It Works

1. **Python Wrappers**: Each `run_test_real_*.py` file is a minimal pytest wrapper that VS Code can discover
2. **Matching Names**: Python wrappers have identical names to their batch files (`.py` vs `.bat` extension)
3. **Batch Execution**: The wrappers call `BaseRealTest.run_test_real()` to execute the actual batch files
4. **Token Efficiency**: Full test execution uses ~50-100 tokens instead of ~3000 tokens
5. **Full Logging**: Complete logs are still saved in `tests/test_real/logs/` for debugging

## Usage Methods

### Method 1: Right-Click "Run Tests" (Primary Goal)
1. Navigate to `tests/test_real/bats/` folder in VS Code Explorer
2. Right-click any `run_test_real_*.py` file (e.g., `run_test_real_email.py`)
3. Select "Run Tests" - VS Code will discover and execute the wrapper test
4. The wrapper executes the corresponding batch file with minimal token output

### Method 2: VS Code Test Explorer
1. Open the Test Explorer panel (beaker icon in Activity Bar)
2. VS Code automatically discovers all `run_test_real_*.py` files in the bats folder
3. Click the play button next to any test to execute it
4. View results in the Test Explorer with success/failure status

### Method 3: Command Palette Tasks
1. Press `Ctrl+Shift+P` (or `Cmd+Shift+P` on Mac)
2. Type "Tasks: Run Task"
3. Select any "Run test_real_*" task from the list
4. The task executes the batch file directly in the terminal

### Method 4: Batch Runner Extension (For .bat files)
1. Install the "Batch Runner" extension
2. In the `tests/test_real/bats/` folder, right-click any `.bat` file
3. Select "Run Batch File" to execute directly
4. Or press F5 when a `.bat` file is open in the editor

### Method 5: Claude Code Execution (Original)
1. Ask Claude: "run test_real_email"
2. Claude uses `BaseRealTest.run_test_real("email")`
3. Returns minimal token-efficient output
4. Full logs preserved in test_real/logs/ directory

## File Structure

```
tests/test_real/
├── test_real_email.py       # Original test scripts
├── test_real_*.py           # Other original test scripts
├── bats/                    # Token-efficient batch files + Python wrappers
│   ├── run_test_real_email.bat    # Batch file for email test
│   ├── run_test_real_email.py     # Python wrapper (same name, .py extension)
│   ├── run_test_real_bot.bat      # Batch file for bot test
│   ├── run_test_real_bot.py       # Python wrapper (same name, .py extension)
│   └── ...                        # All batch/wrapper pairs follow this pattern
└── logs/                    # Full test execution logs
```

## Benefits

- **Easy Discovery**: `.py` and `.bat` files are in the same folder with identical names
- **VS Code Integration**: Right-click "Run Tests" works on the Python wrapper files
- **Token Efficiency**: Maintains 50%+ token reduction (3000 → 1500 tokens)
- **Multiple Execution Paths**: Choose the most convenient method for your workflow
- **Full Debugging**: Complete logs available for troubleshooting
- **Familiar Interface**: Uses standard VS Code testing features
- **Alphabetical Sorting**: Files sort together in Explorer for easy navigation

## Installation Requirements

For full functionality, install the following VS Code extensions:
- **Python** (Microsoft) - Core Python support and pytest integration
- **Batch Runner** (Nils Soderman) - Right-click batch file execution

Both extensions are available in the VS Code Extension Marketplace.