#!/usr/bin/env python3
"""
Test Real IMAP SMTP Broadcast Integration
=========================================

This test validates the complete email processing pipeline:
1. Activate IMAP IDLE monitoring
2. Send an email via SMTP
3. Verify that IMAP receives the email
4. Verify that broadcast notifications are sent to all configured channels

This is a comprehensive end-to-end test of the email notification system.
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

from imports import *
from managers.manager_logfire import LogFire
import pytest
import asyncio
import time
import smtplib
from email.mime.text import MIMEText
from email.mime.multipart import MIMEMultipart
from uuid import uuid4
from pathlib import Path
import tempfile
import shutil
import os
from unittest.mock import Mock, patch, AsyncMock
from datetime import datetime

from tests.test_real.base_real_test import BaseRealTest
from tasks.inputs.imap_idle_activate import SupervisorTask_IMAPIdleActivate
from endpoints.testing_endpoint import MyBot_Testing
from endpoints.oauth._verifier_ import OAuth2Verifier
from userprofiles.ZairaUser import ZairaUser, PERMISSION_LEVELS
from managers.manager_users import ZairaUserManager

@pytest.mark.asyncio
class TestRealIMAPSMTPBroadcast(BaseRealTest):
    """Test suite for IMAP SMTP broadcast integration"""
    
    def setup_method(self):
        """Set up test fixtures"""
        super().setup_method()
        self.test_name = "TestRealIMAPSMTPBroadcast"
        
        # Test configuration
        self.test_email_subject = f"Test Email for IMAP SMTP Broadcast {str(uuid4())[:8]}"
        self.test_email_body = f"This is a test email sent at {datetime.now().isoformat()}"
        
        # Mock tracking for broadcasts
        self.broadcast_messages = []
        self.discord_broadcasts = []
        self.teams_broadcasts = []
        self.whatsapp_broadcasts = []
        self.slack_broadcasts = []
        
        # IMAP monitoring
        self.imap_task = None
        self.imap_monitoring_active = False
        self.received_emails = []
        
    def teardown_method(self):
        """Clean up after each test method"""
        # Stop IMAP monitoring if active
        if self.imap_monitoring_active:
            self.imap_monitoring_active = False
            
        super().teardown_method()
    
    async def setup_broadcast_monitoring(self):
        """Set up monitoring for broadcast messages"""
        
        # Mock Discord bot broadcasts
        async def mock_discord_broadcast(message):
            self.discord_broadcasts.append(message)
            self.broadcast_messages.append(("Discord", message))
            LogFire.log("DEBUG", f"[MOCK] Discord broadcast: {message}")
        
        # Mock Teams bot broadcasts
        async def mock_teams_broadcast(message):
            self.teams_broadcasts.append(message)
            self.broadcast_messages.append(("Teams", message))
            LogFire.log("DEBUG", f"[MOCK] Teams broadcast: {message}")
        
        # Mock WhatsApp bot broadcasts  
        async def mock_whatsapp_broadcast(message):
            self.whatsapp_broadcasts.append(message)
            self.broadcast_messages.append(("WhatsApp", message))
            LogFire.log("DEBUG", f"[MOCK] WhatsApp broadcast: {message}")
        
        # Mock Slack bot broadcasts
        async def mock_slack_broadcast(message):
            self.slack_broadcasts.append(message)
            self.broadcast_messages.append(("Slack", message))
            LogFire.log("DEBUG", f"[MOCK] Slack broadcast: {message}")
        
        # Patch the broadcast methods
        self.discord_patch = patch('endpoints.discord_endpoint.MyDiscordBot.send_discord_broadcast', mock_discord_broadcast)
        self.teams_patch = patch('endpoints.teams_endpoint.MyTeamsBot.send_teams_broadcast', mock_teams_broadcast)
        self.whatsapp_patch = patch('endpoints.whatsapp_endpoint.MyWhatsappBot.send_a_whatsapp_message', mock_whatsapp_broadcast)
        self.slack_patch = patch('endpoints.slack_endpoint.MySlackBot.send_slack_broadcast', mock_slack_broadcast)
        
        # Start the patches
        self.discord_patch.start()
        self.teams_patch.start()
        self.whatsapp_patch.start()
        self.slack_patch.start()
        
        LogFire.log("DEBUG", f"[{self.test_name}] Broadcast monitoring setup completed")
    
    async def setup_imap_monitoring(self, user_guid: str):
        """Set up IMAP monitoring for incoming emails"""
        try:
            # Create IMAP idle task
            self.imap_task = SupervisorTask_IMAPIdleActivate(
                name="test_imap_idle_broadcast",
                prompt_id="Test_IMAP_Idle_Broadcast"
            )
            
            # Override the _process_email method to capture received emails
            original_process_email = self.imap_task._process_email
            
            async def mock_process_email(raw_email: bytes, subject: str, user_guid: str):
                self.received_emails.append({
                    'raw_email': raw_email,
                    'subject': subject,
                    'user_guid': user_guid,
                    'timestamp': datetime.now()
                })
                LogFire.log("DEBUG", f"[MOCK] IMAP received email: {subject}")
                # Call original method to trigger broadcasting
                await original_process_email(raw_email, subject, user_guid)
            
            self.imap_task._process_email = mock_process_email
            
            # Start monitoring (simulate IMAP idle in background)
            self.imap_monitoring_active = True
            
            LogFire.log("DEBUG", f"[{self.test_name}] IMAP monitoring setup completed for user {user_guid}")
            
        except Exception as e:
            LogFire.log("DEBUG", f"[{self.test_name}] Failed to setup IMAP monitoring: {e}")
            raise
    
    async def send_test_email_via_smtp(self) -> bool:
        """Send a test email via SMTP"""
        try:
            LogFire.log("DEBUG", f"[{self.test_name}] Sending test email via SMTP...")
            
            # Get SMTP configuration from OAuth
            smtp_token = await OAuth2Verifier.get_full_token("smtp")
            gmail_token = await OAuth2Verifier.get_full_token("gmail")
            
            if not smtp_token and not gmail_token:
                LogFire.log("DEBUG", f"[{self.test_name}] No SMTP configuration found - skipping SMTP test")
                return False
            
            # Use SMTP configuration
            if smtp_token:
                smtp_server = smtp_token.get("access_token")
                smtp_port = smtp_token.get("expires_in", 587)
                smtp_email = smtp_token.get("refresh_token")
                smtp_password = smtp_token.get("token_type")
                use_tls = smtp_port == 587
                
                LogFire.log("DEBUG", f"[{self.test_name}] Using SMTP server: {smtp_server}:{smtp_port}")
                
            elif gmail_token:
                smtp_server = "smtp.gmail.com"
                smtp_port = 587
                smtp_email = gmail_token.get("refresh_token")
                smtp_password = gmail_token.get("token_type")
                use_tls = True
                
                LogFire.log("DEBUG", f"[{self.test_name}] Using Gmail SMTP")
            
            # Create email message
            msg = MIMEMultipart()
            msg['From'] = smtp_email
            msg['To'] = "<EMAIL>"  # Send to the monitored email
            msg['Subject'] = self.test_email_subject
            
            # Email body
            body = f"""
{self.test_email_body}

This is an automated test email for IMAP SMTP broadcast integration testing.
Test ID: {self.test_name}
Timestamp: {datetime.now().isoformat()}
"""
            
            msg.attach(MIMEText(body, 'plain'))
            
            # Send email
            server = smtplib.SMTP(smtp_server, smtp_port)
            if use_tls:
                server.starttls()
            
            server.login(smtp_email, smtp_password)
            text = msg.as_string()
            server.sendmail(smtp_email, "<EMAIL>", text)
            server.quit()
            
            LogFire.log("DEBUG", f"[{self.test_name}] Test email sent successfully")
            return True
            
        except Exception as e:
            LogFire.log("DEBUG", f"[{self.test_name}] Failed to send test email: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def simulate_imap_email_reception(self, user_guid: str):
        """Simulate IMAP receiving the email and triggering broadcast"""
        try:
            LogFire.log("DEBUG", f"[{self.test_name}] Simulating IMAP email reception...")
            
            # Create mock email content
            mock_email_content = f"""From: <EMAIL>
To: <EMAIL>
Subject: {self.test_email_subject}

{self.test_email_body}
""".encode('utf-8')
            
            # Process the email through IMAP task
            await self.imap_task._process_email(
                mock_email_content, 
                self.test_email_subject, 
                user_guid
            )
            
            LogFire.log("DEBUG", f"[{self.test_name}] IMAP email processing completed")
            
        except Exception as e:
            LogFire.log("DEBUG", f"[{self.test_name}] Failed to simulate IMAP email reception: {e}")
            raise
    
    async def wait_for_broadcast_messages(self, timeout: int = 30) -> bool:
        """Wait for broadcast messages to be received"""
        LogFire.log("DEBUG", f"[{self.test_name}] Waiting for broadcast messages...")
        
        start_time = time.time()
        while time.time() - start_time < timeout:
            if len(self.broadcast_messages) > 0:
                LogFire.log("DEBUG", f"[{self.test_name}] Received {len(self.broadcast_messages)} broadcast messages")
                return True
            
            await asyncio.sleep(0.5)
        
        LogFire.log("DEBUG", f"[{self.test_name}] Timeout waiting for broadcast messages")
        return False
    
    async def test_full_imap_smtp_broadcast_integration(self):
        """Test complete IMAP SMTP broadcast integration"""
        
        # Skip if no OpenAI API key
        api_key_configured = os.environ.get('OPENAI_API_KEY')
        if not api_key_configured:
            pytest.skip("OpenAI API key not configured - set OPENAI_API_KEY")
        
        # Setup real system
        setup_success = await self.setup_real_system("email_broadcast")
        if not setup_success:
            pytest.skip("Could not initialize email broadcast system")
        
        # Create test user
        user = await self.create_test_user(email="<EMAIL>")
        bot = self.create_test_bot("BroadcastTest")
        
        LogFire.log("DEBUG", f"[{self.test_name}] Starting full IMAP SMTP broadcast integration test")
        
        # Step 1: Set up broadcast monitoring
        await self.setup_broadcast_monitoring()
        
        # Step 2: Set up IMAP monitoring
        await self.setup_imap_monitoring(user.user_guid)
        
        # Step 3: Send test email via SMTP (or simulate if SMTP not configured)
        email_sent = await self.send_test_email_via_smtp()
        
        # Step 4: Simulate IMAP receiving the email (since real IMAP might not be configured)
        await self.simulate_imap_email_reception(user.user_guid)
        
        # Step 5: Wait for broadcast messages
        broadcasts_received = await self.wait_for_broadcast_messages(timeout=10)
        
        # Verify results
        assert broadcasts_received, "No broadcast messages were received"
        assert len(self.broadcast_messages) > 0, "Expected at least one broadcast message"
        
        # Verify broadcast content
        broadcast_found = False
        for platform, message in self.broadcast_messages:
            if self.test_email_subject in message or "New email received" in message:
                broadcast_found = True
                LogFire.log("DEBUG", f"[{self.test_name}] Found expected broadcast on {platform}: {message}")
                break
        
        assert broadcast_found, f"Expected broadcast message not found. Received: {self.broadcast_messages}"
        
        # Verify email was processed
        assert len(self.received_emails) > 0, "No emails were processed by IMAP"
        
        processed_email = self.received_emails[0]
        assert processed_email['subject'] == self.test_email_subject, "Email subject mismatch"
        assert processed_email['user_guid'] == user.user_guid, "User GUID mismatch"
        
        LogFire.log("DEBUG", f"[{self.test_name}] Integration test completed successfully!")
        LogFire.log("DEBUG", f"[{self.test_name}] Summary:")
        LogFire.log("DEBUG", f"  - Email sent via SMTP: {email_sent}")
        LogFire.log("DEBUG", f"  - Emails processed by IMAP: {len(self.received_emails)}")
        LogFire.log("DEBUG", f"  - Broadcast messages sent: {len(self.broadcast_messages)}")
        
        for platform, message in self.broadcast_messages:
            LogFire.log("DEBUG", f"    - {platform}: {message}")
        
        # Cleanup patches
        self.discord_patch.stop()
        self.teams_patch.stop()
        self.whatsapp_patch.stop()
        self.slack_patch.stop()
        
        return {
            "success": True,
            "email_sent": email_sent,
            "emails_processed": len(self.received_emails),
            "broadcasts_sent": len(self.broadcast_messages),
            "broadcast_platforms": [platform for platform, _ in self.broadcast_messages]
        }
    
    async def test_imap_broadcast_without_smtp(self):
        """Test IMAP broadcast functionality without SMTP dependency"""
        
        # Skip if no OpenAI API key
        api_key_configured = os.environ.get('OPENAI_API_KEY')
        if not api_key_configured:
            pytest.skip("OpenAI API key not configured - set OPENAI_API_KEY")
        
        # Setup real system
        setup_success = await self.setup_real_system("email_broadcast")
        if not setup_success:
            pytest.skip("Could not initialize email broadcast system")
        
        # Create test user
        user = await self.create_test_user(email="<EMAIL>")
        
        LogFire.log("DEBUG", f"[{self.test_name}] Starting IMAP broadcast test without SMTP")
        
        # Step 1: Set up broadcast monitoring
        await self.setup_broadcast_monitoring()
        
        # Step 2: Set up IMAP monitoring
        await self.setup_imap_monitoring(user.user_guid)
        
        # Step 3: Simulate IMAP receiving an email
        await self.simulate_imap_email_reception(user.user_guid)
        
        # Step 4: Wait for broadcast messages
        broadcasts_received = await self.wait_for_broadcast_messages(timeout=10)
        
        # Verify results
        assert broadcasts_received, "No broadcast messages were received"
        assert len(self.broadcast_messages) > 0, "Expected at least one broadcast message"
        
        # Verify broadcast content
        broadcast_found = False
        for platform, message in self.broadcast_messages:
            if self.test_email_subject in message or "New email received" in message:
                broadcast_found = True
                LogFire.log("DEBUG", f"[{self.test_name}] Found expected broadcast on {platform}: {message}")
        
        assert broadcast_found, f"Expected broadcast message not found. Received: {self.broadcast_messages}"
        
        LogFire.log("DEBUG", f"[{self.test_name}] IMAP broadcast test completed successfully!")
        
        # Cleanup patches
        self.discord_patch.stop()
        self.teams_patch.stop()
        self.whatsapp_patch.stop()
        self.slack_patch.stop()
        
        return {
            "success": True,
            "emails_processed": len(self.received_emails),
            "broadcasts_sent": len(self.broadcast_messages)
        }


if __name__ == "__main__":
    # Run tests individually for debugging
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "debug":
        # Debug mode - run a single test
        test_instance = TestRealIMAPSMTPBroadcast()
        test_instance.setup_method()
        
        try:
            # Run the full integration test
            result = asyncio.run(test_instance.test_full_imap_smtp_broadcast_integration())
            LogFire.log("DEBUG", f"Full integration test result: {result}")
        except Exception as e:
            LogFire.log("DEBUG", f"Full integration test failed: {e}")
            import traceback
            traceback.print_exc()
        
        test_instance.teardown_method()
        LogFire.log("DEBUG", "Debug test completed")
    else:
        # Normal pytest execution
        pytest.main([__file__, "-v"], severity="debug")