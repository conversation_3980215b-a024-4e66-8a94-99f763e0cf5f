from imports import *

from langchain_core.tools import BaseTool
from typing import Optional
import json
from datetime import datetime, timezone

from managers.manager_supervisors import Supervisor<PERSON>anager, SupervisorTask_SingleAgent, SupervisorTask_Base, SupervisorTaskState
from managers.manager_users import ZairaUserManager
from managers.manager_signature import SignatureManager
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
from tasks.data import EmailProcessingData


class EmailGeneratorTool(BaseTool):
    """Tool for generating email content with proper formatting and validation"""
    name: str = "email_generator_tool"
    description: str = "Generates professional email content with subject, body, sender and recipient validation. " \
                      "ALWAYS use this tool for ANY email request. Pass the user's request as content_request parameter. " \
                      "Extract recipient from request if available (e.g., 'send <NAME_EMAIL>' -> recipient='<EMAIL>'). " \
                      "Tool handles missing information by requesting human input."
    
    def _run(self, content_request: str, subject_hint: Optional[str] = None, sender: Optional[str] = None, recipient: Optional[str] = None, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, content_request: str, subject_hint: Optional[str] = None, sender: Optional[str] = None, recipient: Optional[str] = None, state: SupervisorTaskState = None) -> str:
        """Generates professional email content based on the request"""
        try:
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Starting email generation with content_request: {content_request}", chat=None)
            LogFire.log("DEBUG", f"[EmailGeneratorTool] subject_hint: {subject_hint}, sender: {sender}, recipient: {recipient}", chat=None)
            LogFire.log("DEBUG", f"[EmailGeneratorTool] state.user_guid: {state.user_guid if state else 'No state'}", chat=None)
            
            user = await ZairaUserManager.get_instance().find_user(state.user_guid)
            # Get user's current chat session for logging
            chat_session = user.get_current_chat_session() if user else None
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Found user: {user.username if user else 'No user'}", chat=chat_session)
            
            # Log what we received as parameters
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Parameters received - sender: '{sender}', recipient: '{recipient}', content_request: '{content_request}'", chat=chat_session)
            LogFire.log("DEBUG", f"[EmailGeneratorTool] User email from profile: '{user.email}'", chat=chat_session)
            
            # Determine sender email
            final_sender = sender
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Initial final_sender: {final_sender}", chat=chat_session)
            if not final_sender or '@' not in final_sender:
                LogFire.log("DEBUG", f"[EmailGeneratorTool] No valid sender, checking user.email: {user.email}", chat=chat_session)
                if user.email:
                    final_sender = user.email
                    LogFire.log("DEBUG", f"[EmailGeneratorTool] Using user email as sender: {final_sender}", chat=chat_session)
                else:
                    # Request sender email from user
                    LogFire.log("DEBUG", "[EmailGeneratorTool] Requesting sender email from user via HITL", chat=chat_session)
                    
                    # Create a container to hold the result
                    sender_result = {"email": None}
                    
                    async def set_user_email(task: LongRunningZairaRequest, response: str):
                        try:
                            LogFire.log("DEBUG", f"[EmailGeneratorTool] HITL callback called with response: {response}", chat=chat_session)
                            if response and '@' in response:
                                user.email = response.strip()
                                sender_result["email"] = response.strip()
                                LogFire.log("DEBUG", f"[EmailGeneratorTool] Set user email to: {response.strip()}", chat=chat_session)
                            else:
                                LogFire.log("DEBUG", f"[EmailGeneratorTool] Invalid email response: {response}", chat=chat_session)
                        except Exception as e:
                            LogFire.log("ERROR", f"[EmailGeneratorTool] Error in set_user_email callback: {e}", chat=chat_session, severity="error")
                    
                    await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                        "Je e-mail adres is nog niet bekend bij ons. Van welk e-mailadres moet de mail verzonden worden?", 
                        set_user_email, 
                        True
                    )
                    
                    # Get the result from the container
                    if sender_result["email"]:
                        final_sender = sender_result["email"]
                        LogFire.log("DEBUG", f"[EmailGeneratorTool] HITL request completed, final_sender: {final_sender}", chat=chat_session)
                    else:
                        LogFire.log("ERROR", "[EmailGeneratorTool] Failed to get valid sender email from user", chat=chat_session, severity="error")
                        return "Error: Valid sender email is required to generate email."
            
            # Determine recipient email
            final_recipient = recipient
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Initial final_recipient: {final_recipient}", chat=chat_session)
            if not final_recipient or '@' not in final_recipient:
                # Create a container to hold the result
                recipient_result = {"email": None}
                
                async def set_recipient(task: LongRunningZairaRequest, response: str):
                    try:
                        LogFire.log("DEBUG", f"[EmailGeneratorTool] HITL recipient callback called with response: {response}", chat=chat_session)
                        if response and '@' in response:
                            recipient_result["email"] = response.strip()
                            LogFire.log("DEBUG", f"[EmailGeneratorTool] Set recipient email to: {response.strip()}", chat=chat_session)
                        else:
                            LogFire.log("DEBUG", f"[EmailGeneratorTool] Invalid recipient email response: {response}", chat=chat_session)
                            # For invalid email, we'll ask again outside the callback
                    except Exception as e:
                        LogFire.log("ERROR", f"[EmailGeneratorTool] Error in set_recipient callback: {e}", chat=chat_session, severity="error")

                LogFire.log("DEBUG", "[EmailGeneratorTool] Requesting recipient email from user via HITL", chat=chat_session)
                await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                    "Het is mij niet duidelijk naar welk email adres de mail gestuurd moet worden?", 
                    set_recipient, 
                    True
                )
                
                # Get the result from the container
                if recipient_result["email"]:
                    final_recipient = recipient_result["email"]
                    LogFire.log("DEBUG", f"[EmailGeneratorTool] HITL request completed, final_recipient: {final_recipient}", chat=chat_session)
                else:
                    LogFire.log("ERROR", "[EmailGeneratorTool] Failed to get valid recipient email from user", chat=chat_session, severity="error")
                    return "Error: Valid recipient email is required to generate email."
            
            # Generate email subject if not provided
            final_subject = subject_hint
            if not final_subject:
                # Use LLM to generate appropriate subject based on content request
                from langchain_openai import ChatOpenAI
                from langchain.prompts import ChatPromptTemplate
                from langchain_core.output_parsers import StrOutputParser
                
                subject_prompt = ChatPromptTemplate.from_template(
                    "Generate a professional and concise email subject line for the following email request:\n\n"
                    "Email request: {content_request}\n\n"
                    "Subject line (maximum 50 characters, professional tone):"
                    "MUST: Analyze the language of the content request and generate a subject line in the same language"
                )
                
                llm = ChatOpenAI(model_name="gpt-4o-mini", temperature=0.1)
                subject_chain = subject_prompt | llm | StrOutputParser()
                user.receive_debug_message("", state.chat_session_guid)
                
                final_subject = await subject_chain.ainvoke({"content_request": content_request})
                final_subject = final_subject.strip().strip('"').strip("'")
            
            # Get signature data for the user to determine closing instruction
            LogFire.log("DEBUG", "[EmailGeneratorTool] Getting signature data for user", chat=chat_session)
            signature_manager = SignatureManager.get_instance()
            await signature_manager.setup()
            
            signature_text, signature_image_path, signature_html = await signature_manager.prepare_signature_for_email(user, True)
            
            # Determine signature instruction based on whether user has signature configured
            if signature_text:
                signature_instruction = """CRITICAL: End the email content immediately after the main message. Do NOT write 'Met vriendelijke groet', 'Vriendelijke groet', 'Groet', 'Groeten', or ANY closing phrase. Do NOT write any signature or closing. The signature will be automatically appended. Always use the following template:" \
                "Salution"
                
                "Body Content"
                
                
                Example mail: 
                Hey Daan,
                
                I am writing to you because I want to ask you a question. At which time to we have a meeting tommorow? 
                
                """
                LogFire.log("DEBUG", f"[EmailGeneratorTool] User has signature configured - preventing double closings", chat=chat_session)
            else:
                signature_instruction = "Include an appropriate professional closing since no signature is configured."
                LogFire.log("DEBUG", f"[EmailGeneratorTool] User has no signature - including closing in email body", chat=chat_session)
            
            # Generate email content using LLM with proper prompt from manager
            LogFire.log("DEBUG", "[EmailGeneratorTool] Starting email content generation with LLM", chat=chat_session)
            from langchain_openai import ChatOpenAI
            from langchain.prompts import ChatPromptTemplate
            from langchain_core.output_parsers import StrOutputParser
            from managers.manager_prompts import PromptManager
            
            # Get the prompt template from prompt manager
            email_prompt_template = PromptManager.get_prompt("Email_Generator_Prompt")
            
            content_prompt = ChatPromptTemplate.from_template(email_prompt_template)
            
            llm = ChatOpenAI(model_name="gpt-4o-mini", temperature=0.1)
            content_chain = content_prompt | llm | StrOutputParser()
            
            # Log final email addresses before content generation
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Final email addresses - sender: '{final_sender}', recipient: '{final_recipient}'", chat=chat_session)
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Using signature instruction: {signature_instruction}", chat=chat_session)
            
            generated_content = await content_chain.ainvoke({
                "content_request": content_request,
                "sender": final_sender,
                "recipient": final_recipient,
                "signature_instruction": signature_instruction
            })
            
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Signature data: text={'yes' if signature_text else 'no'}, image={'yes' if signature_image_path else 'no'}", chat=chat_session)
            
            # Create structured email data using EmailProcessingData
            email_data = EmailProcessingData(
                subject=final_subject,
                content=generated_content.strip(),
                sender=final_sender,
                recipient=final_recipient,
                content_generated=True,
                subject_generated=True,
                sender_validated=True,
                recipient_validated=True,
                include_signature=bool(signature_text),
                signature_text=signature_text,
                signature_image_path=signature_image_path,
                signature_html=signature_html,
                processing_metadata={
                    "generated_at": datetime.now(timezone.utc).isoformat(),
                    "user_guid": state.user_guid
                }
            )
            
            # Use structured data's preview method
            email_preview = email_data.get_preview_text()
            
            # Request user approval
            LogFire.log("DEBUG", f"[EmailGeneratorTool] About to request user approval for email preview", chat=chat_session)
            
            # Create a container to hold approval result
            approval_result = {"approved": False, "completed": False}
            
            async def add_to_output():
                try:
                    # Mark email as approved and add to processing data
                    email_data.mark_approved()
                    user.my_requests[state.scheduled_guid].add_processing_output(email_data)
                    approval_result["approved"] = True
                    approval_result["completed"] = True
                    LogFire.log("DEBUG", f"[EmailGeneratorTool] Added EmailProcessingData to processing_data. Current demands: {user.my_requests[state.scheduled_guid].output_demands}", chat=chat_session)
                except Exception as e:
                    LogFire.log("ERROR", f"[EmailGeneratorTool] Error in add_to_output: {e}", chat=chat_session, severity="error")
            
            async def handle_approval(task: LongRunningZairaRequest, response: str):
                try:
                    LogFire.log("DEBUG", f"[EmailGeneratorTool] Approval callback called with response: {response}", chat=chat_session)
                    if response and response.lower().startswith('j'):
                        # User approved - email ready for sending
                        await add_to_output()
                    else:
                        # User wants to edit - ask what to change
                        LogFire.log("DEBUG", "[EmailGeneratorTool] User declined approval, requesting edit choice", chat=chat_session)
                        
                        async def handle_edit_choice(task: LongRunningZairaRequest, choice_response: str):
                            try:
                                LogFire.log("DEBUG", f"[EmailGeneratorTool] Edit choice callback called with response: {choice_response}", chat=chat_session)
                                choice = choice_response.strip()
                                
                                if choice == "1":
                                    # Edit subject
                                    await request_subject_edit()
                                elif choice == "2":
                                    # Edit body content
                                    await request_body_edit()
                                elif choice == "3":
                                    # Edit recipient
                                    await request_recipient_edit()
                                else:
                                    # Cancel or invalid choice
                                    approval_result["completed"] = True
                                    LogFire.log("DEBUG", "[EmailGeneratorTool] User cancelled or invalid choice", chat=chat_session)
                            except Exception as e:
                                LogFire.log("ERROR", f"[EmailGeneratorTool] Error in handle_edit_choice: {e}", chat=chat_session, severity="error")
                        
                        async def request_subject_edit():
                            try:
                                LogFire.log("DEBUG", "[EmailGeneratorTool] Requesting subject edit", chat=chat_session)
                                
                                async def handle_subject_edit(task: LongRunningZairaRequest, new_subject: str):
                                    try:
                                        if new_subject and new_subject.lower().strip() != 'annuleren':
                                            email_data.subject = new_subject.strip()
                                            LogFire.log("DEBUG", f"[EmailGeneratorTool] Updated subject to: {new_subject.strip()}", chat=chat_session)
                                            await re_request_approval()
                                        else:
                                            approval_result["completed"] = True
                                            LogFire.log("DEBUG", "[EmailGeneratorTool] User cancelled subject edit", chat=chat_session)
                                    except Exception as e:
                                        LogFire.log("ERROR", f"[EmailGeneratorTool] Error in handle_subject_edit: {e}", chat=chat_session, severity="error")
                                
                                await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                                    f"Huidige onderwerp: {email_data.subject}\n\nVoer het nieuwe onderwerp in (of 'annuleren' om te stoppen):",
                                    handle_subject_edit,
                                    True
                                )
                            except Exception as e:
                                LogFire.log("ERROR", f"[EmailGeneratorTool] Error in request_subject_edit: {e}", chat=chat_session, severity="error")
                        
                        async def request_body_edit():
                            try:
                                LogFire.log("DEBUG", "[EmailGeneratorTool] Requesting body content edit instructions", chat=chat_session)
                                
                                async def handle_body_edit_instructions(task: LongRunningZairaRequest, user_instructions: str):
                                    try:
                                        if user_instructions and user_instructions.lower().strip() != 'annuleren':
                                            LogFire.log("DEBUG", f"[EmailGeneratorTool] Regenerating email content with user instructions: {user_instructions}", chat=chat_session)
                                            
                                            # Regenerate email content with LLM using user's additional instructions
                                            from langchain_openai import ChatOpenAI
                                            from langchain.prompts import ChatPromptTemplate
                                            from langchain_core.output_parsers import StrOutputParser
                                            from managers.manager_prompts import PromptManager
                                            
                                            # Detect language from original email content
                                            original_content = email_data.content.lower()
                                            language_instruction = ""
                                            if any(dutch_word in original_content for dutch_word in ['hallo', 'dag', 'beste', 'groet', 'vriendelijke', 'met', 'aan', 'van', 'voor', 'een', 'het', 'de']):
                                                language_instruction = "BELANGRIJK: Schrijf de e-mail volledig in het Nederlands. Gebruik ALLEEN Nederlandse woorden en zinnen."
                                            elif any(english_word in original_content for english_word in ['hello', 'dear', 'best', 'regards', 'sincerely', 'thank', 'please', 'with', 'from', 'for', 'the', 'and']):
                                                language_instruction = "IMPORTANT: Write the email completely in English. Use ONLY English words and sentences."
                                            else:
                                                # Default to Dutch since user instructions are in Dutch
                                                language_instruction = "BELANGRIJK: Schrijf de e-mail volledig in het Nederlands. Gebruik ALLEEN Nederlandse woorden en zinnen."
                                            
                                            # Create enhanced content request with user's edit instructions and language preservation
                                            enhanced_content_request = f"{content_request}\n\nAanvullende instructies voor aanpassing: {user_instructions}\n\n{language_instruction}"
                                            
                                            # Get the prompt template from prompt manager
                                            email_prompt_template = PromptManager.get_prompt("Email_Generator_Prompt")
                                            content_prompt = ChatPromptTemplate.from_template(email_prompt_template)
                                            
                                            llm = ChatOpenAI(model_name="gpt-4o-mini", temperature=0.1)
                                            content_chain = content_prompt | llm | StrOutputParser()
                                            
                                            LogFire.log("DEBUG", f"[EmailGeneratorTool] Language instruction: {language_instruction}", chat=chat_session)
                                            
                                            # Generate new content with enhanced request, user instructions, and language preservation
                                            regenerated_content = await content_chain.ainvoke({
                                                "content_request": enhanced_content_request,
                                                "sender": email_data.sender,
                                                "recipient": email_data.recipient,
                                                "signature_instruction": signature_instruction
                                            })
                                            
                                            # Update email data with regenerated content
                                            email_data.content = regenerated_content.strip()
                                            LogFire.log("DEBUG", f"[EmailGeneratorTool] Regenerated email content with LLM", chat=chat_session)
                                            await re_request_approval()
                                        else:
                                            approval_result["completed"] = True
                                            LogFire.log("DEBUG", "[EmailGeneratorTool] User cancelled body edit", chat=chat_session)
                                    except Exception as e:
                                        LogFire.log("ERROR", f"[EmailGeneratorTool] Error in handle_body_edit_instructions: {e}", chat=chat_session, severity="error")
                                
                                await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                                    f"Huidige inhoud:\n{email_data.content}\n\nWat wil je aanpassen aan de inhoud? Geef instructies voor de wijziging (of 'annuleren' om te stoppen):",
                                    handle_body_edit_instructions,
                                    True
                                )
                            except Exception as e:
                                LogFire.log("ERROR", f"[EmailGeneratorTool] Error in request_body_edit: {e}", chat=chat_session, severity="error")
                        
                        async def request_recipient_edit():
                            try:
                                LogFire.log("DEBUG", "[EmailGeneratorTool] Requesting recipient edit", chat=chat_session)
                                
                                async def handle_recipient_edit(task: LongRunningZairaRequest, new_recipient: str):
                                    try:
                                        if new_recipient and new_recipient.lower().strip() != 'annuleren':
                                            if '@' in new_recipient:
                                                email_data.recipient = new_recipient.strip()
                                                LogFire.log("DEBUG", f"[EmailGeneratorTool] Updated recipient to: {new_recipient.strip()}", chat=chat_session)
                                                await re_request_approval()
                                            else:
                                                # Invalid email, ask again
                                                await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                                                    "Ongeldig e-mailadres. Voer een geldig e-mailadres in (of 'annuleren' om te stoppen):",
                                                    handle_recipient_edit,
                                                    True
                                                )
                                        else:
                                            approval_result["completed"] = True
                                            LogFire.log("DEBUG", "[EmailGeneratorTool] User cancelled recipient edit", chat=chat_session)
                                    except Exception as e:
                                        LogFire.log("ERROR", f"[EmailGeneratorTool] Error in handle_recipient_edit: {e}", chat=chat_session, severity="error")
                                
                                await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                                    f"Huidige ontvanger: {email_data.recipient}\n\nVoer het nieuwe e-mailadres in (of 'annuleren' om te stoppen):",
                                    handle_recipient_edit,
                                    True
                                )
                            except Exception as e:
                                LogFire.log("ERROR", f"[EmailGeneratorTool] Error in request_recipient_edit: {e}", chat=chat_session, severity="error")
                        
                        async def re_request_approval():
                            try:
                                LogFire.log("DEBUG", "[EmailGeneratorTool] Re-requesting approval with updated email", chat=chat_session)
                                # Update preview with modified email data
                                updated_preview = email_data.get_preview_text()
                                
                                # Create new approval handler that properly sets completion status
                                async def handle_recursive_approval(task: LongRunningZairaRequest, response: str):
                                    try:
                                        LogFire.log("DEBUG", f"[EmailGeneratorTool] Recursive approval callback with response: {response}", chat=chat_session)
                                        if response and response.lower().startswith('j'):
                                            # User approved - email ready for sending
                                            await add_to_output()
                                        else:
                                            # User wants to edit again - continue edit flow
                                            await handle_approval(task, response)  # This will go back to edit menu
                                    except Exception as e:
                                        LogFire.log("ERROR", f"[EmailGeneratorTool] Error in handle_recursive_approval: {e}", chat=chat_session, severity="error")
                                        approval_result["completed"] = True  # Set completed on error to prevent hanging
                                
                                # Request approval again with the updated email
                                await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                                    f"Aangepaste mail. Wil je dat ik deze goedkeur voor verzending? (j/n)\n\n{updated_preview}",
                                    handle_recursive_approval,  # Use new handler that manages completion properly
                                    True
                                )
                            except Exception as e:
                                LogFire.log("ERROR", f"[EmailGeneratorTool] Error in re_request_approval: {e}", chat=chat_session, severity="error")
                                approval_result["completed"] = True  # Set completed on error to prevent hanging
                        
                        # Ask user what they want to edit
                        # Note: We don't set completed=True here because the edit flow will handle completion
                        await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                            "Wat wil je aanpassen aan de mail?\n1. Onderwerp van de mail\n2. Inhoud van de mail\n3. Mailadres ontvanger\n4. Annuleren\n\nType het nummer in wat u wilt veranderen:",
                            handle_edit_choice,
                            True
                        )
                except Exception as e:
                    LogFire.log("ERROR", f"[EmailGeneratorTool] Error in handle_approval: {e}", chat=chat_session, severity="error")
            
            # Get user's active task to request human input
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Requesting HITL approval for email", chat=chat_session)
            LogFire.log("DEBUG", f"[EmailGeneratorTool] Email preview: {email_preview[:100]}...", chat=chat_session)
            await user.my_requests[state.scheduled_guid].request_human_in_the_loop(
                f"Ik heb de volgende mail opgesteld. Wil je dat ik deze goedkeur voor verzending? (j/n)\n\n{email_preview}",
                handle_approval,
                True
            )
            
            # The HITL system handles completion asynchronously via callbacks
            # Return empty string to avoid interfering with HITL flow
            LogFire.log("DEBUG", "[EmailGeneratorTool] Email approval request initiated, awaiting user response", chat=chat_session)
            return "D"
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "email_generator_tool", state.user_guid if state else None)
            return f"Error generating email: {e}"


# Create tool instance
email_generator_tool = EmailGeneratorTool()


async def create_task_email_generator() -> SupervisorTask_SingleAgent:
    """Create supervisor for email generation tasks"""
    from langchain_openai import ChatOpenAI

    return SupervisorManager.get_instance().register_task(
                SupervisorTask_SingleAgent(
                    name="email_generator_task", 
                    tools=[email_generator_tool], 
                    prompt="You are an email generation specialist. Your task is to create professional, well-structured emails based on user requests. "
                           "When users request email creation or sending, use the email_generator_tool to generate proper email content. "
                           "The tool handles subject generation, formatting, sender/recipient validation, user approval, and signature management. "
                           "If the request is unclear or you need more information to generate a meaningful email, you may ask for clarification first. "
                           "For clear email requests (like 'send <NAME_EMAIL>'), use the tool directly to generate and process the email. "
                           "The tool automatically handles signature inclusion and closing logic to prevent double closings.",
                    model=ChatOpenAI(model_name="gpt-4o-mini", temperature=0.1)
                )
            )