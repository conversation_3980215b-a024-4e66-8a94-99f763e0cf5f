<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>AskZaira Dashboard</title>
    <style>
        /* Import Whitelabel Styles */
        {whitelabel_css}
        
        /* Base Styles */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: var(--brand-background);
            background-image: 
                radial-gradient(at 0% 0%, #1e3a8a 0%, transparent 50%),
                radial-gradient(at 100% 100%, #1e40af 0%, transparent 50%);
            min-height: 100vh;
            overflow-x: hidden;
            color: var(--dashboard-text-primary);
        }
        
        /* Particles Background */
        .particles {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
        }
        
        .particle {
            position: absolute;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }
        
        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); }
            50% { transform: translateY(-20px) rotate(180deg); }
        }
        
        /* Main Container */
        .main-container {
            margin-right: 80px;
            padding: var(--spacing-xl);
            transition: margin-right 0.3s ease;
        }
        
        .main-container.menu-expanded {
            margin-right: 320px;
        }
        
        /* Header */
        .header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-lg) 0;
            border-bottom: 1px solid var(--dashboard-glass-border);
            margin-bottom: var(--spacing-xl);
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: var(--spacing-lg);
        }
        
        .header-logo {
            width: 60px;
            height: 60px;
        }
        
        .header-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dashboard-text-primary);
        }
        
        .header-right {
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        /* Status Box */
        .status-box {
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-weight: 600;
            min-width: 120px;
            justify-content: center;
        }
        
        .status-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }
        
        /* Auto-refresh in header */
        .header-auto-refresh {
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
            font-size: 0.9rem;
            color: var(--dashboard-text-secondary);
        }
        
        /* Page Content */
        .page-content {
            max-width: 1200px;
            margin: 0 auto;
        }
        
        .page-section {
            margin-bottom: var(--spacing-xl);
        }
        
        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--dashboard-text-primary);
            margin-bottom: var(--spacing-lg);
            display: flex;
            align-items: center;
            gap: var(--spacing-md);
        }
        
        .section-icon {
            width: 24px;
            height: 24px;
            color: var(--brand-primary);
        }
        
        /* Cards */
        .card {
            background: var(--dashboard-glass);
            backdrop-filter: blur(10px);
            border: 1px solid var(--dashboard-glass-border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-lg);
            margin-bottom: var(--spacing-md);
            box-shadow: var(--shadow-glass);
            transition: all 0.3s ease;
        }
        
        .card:hover {
            background: rgba(255, 255, 255, 0.15);
            transform: translateY(-2px);
        }
        
        .card-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: var(--spacing-md);
            color: var(--dashboard-text-primary);
        }
        
        .card-content {
            color: var(--dashboard-text-secondary);
            line-height: 1.6;
        }
        
        /* Loading States */
        .loading {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: var(--spacing-xl);
            color: var(--dashboard-text-secondary);
        }
        
        .loading::after {
            content: '';
            width: 20px;
            height: 20px;
            border: 2px solid var(--dashboard-text-secondary);
            border-top: 2px solid var(--brand-primary);
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-left: var(--spacing-md);
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        /* Notification animations */
        @keyframes slideIn {
            from {
                transform: translateX(100%);
                opacity: 0;
            }
            to {
                transform: translateX(0);
                opacity: 1;
            }
        }
        
        @keyframes slideOut {
            from {
                transform: translateX(0);
                opacity: 1;
            }
            to {
                transform: translateX(100%);
                opacity: 0;
            }
        }
        
        /* Error States */
        .error {
            background: rgba(239, 68, 68, 0.1);
            border: 1px solid var(--status-error);
            color: var(--status-error);
            padding: var(--spacing-md);
            border-radius: var(--radius-md);
            margin: var(--spacing-md) 0;
        }
        
        /* Empty States */
        .empty-state {
            text-align: center;
            padding: var(--spacing-xl);
            color: var(--dashboard-text-secondary);
        }
        
        .empty-state-icon {
            width: 64px;
            height: 64px;
            margin: 0 auto var(--spacing-lg);
            opacity: 0.5;
        }
        
        .empty-state-title {
            font-size: 1.25rem;
            font-weight: 600;
            margin-bottom: var(--spacing-md);
            color: var(--dashboard-text-primary);
        }
        
        .empty-state-description {
            line-height: 1.6;
            max-width: 400px;
            margin: 0 auto;
        }
        
        /* Expandable content animations */
        .request-expanded {
            max-height: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-in-out;
        }
        
        .request-expanded.show {
            max-height: 1600px;
        }
        
        .trace-dropdown, .debug-dropdown, .call-trace-dropdown, .debug-messages-dropdown {
            max-height: 0;
            overflow: hidden;
            opacity: 0;
            transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
        }
        
        .trace-dropdown.show, .debug-dropdown.show, .call-trace-dropdown.show, .debug-messages-dropdown.show {
            max-height: 800px;
            opacity: 1;
        }
        
        /* Profile Page Styles */
        .profile-container { max-width: 800px; margin: 0 auto; padding: var(--spacing-lg); }
        .profile-header { text-align: center; margin-bottom: var(--spacing-xl); }
        .profile-avatar { 
            width: 80px; height: 80px; border-radius: 50%; 
            background: var(--dashboard-glass); border: 2px solid var(--brand-primary);
            display: flex; align-items: center; justify-content: center;
            margin: 0 auto var(--spacing-lg); font-size: 1.5rem; color: var(--brand-primary);
            transition: all 0.3s ease;
        }
        .profile-avatar:hover { transform: scale(1.05); }
        .profile-name { font-size: 1.8rem; font-weight: 600; margin-bottom: var(--spacing-sm); color: var(--dashboard-text-primary); }
        .profile-subtitle { color: var(--dashboard-text-secondary); margin-bottom: var(--spacing-md); }
        .profile-stats { 
            display: grid; grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: var(--spacing-md); margin-bottom: var(--spacing-xl);
        }
        .stat-card { 
            background: var(--dashboard-glass); border: 1px solid var(--dashboard-glass-border);
            border-radius: var(--radius-lg); padding: var(--spacing-lg); text-align: center;
            transition: all 0.3s ease;
        }
        .stat-card:hover { transform: translateY(-2px); background: rgba(255,255,255,0.15); }
        .stat-value { font-size: 1.5rem; font-weight: 700; color: var(--brand-primary); margin-bottom: var(--spacing-sm); }
        .stat-label { font-size: 0.8rem; color: var(--dashboard-text-secondary); text-transform: uppercase; letter-spacing: 0.5px; }
        .profile-form { margin-bottom: var(--spacing-lg); }
        .form-row { display: grid; grid-template-columns: 1fr 1fr; gap: var(--spacing-md); margin-bottom: var(--spacing-md); }
        .form-group { margin-bottom: var(--spacing-md); }
        .form-label { 
            display: block; font-weight: 600; margin-bottom: var(--spacing-sm);
            color: var(--dashboard-text-primary); font-size: 0.9rem;
        }
        .form-input { 
            width: 100%; padding: var(--spacing-md); background: rgba(255,255,255,0.1);
            border: 1px solid var(--dashboard-glass-border); border-radius: var(--radius-md);
            color: var(--dashboard-text-primary); font-size: 1rem; transition: all 0.3s;
        }
        .form-input:focus { 
            outline: none; border-color: var(--brand-primary);
            background: rgba(255,255,255,0.15);
            box-shadow: 0 0 0 2px rgba(59,130,246,0.2);
        }
        .form-input::placeholder { color: var(--dashboard-text-secondary); opacity: 0.7; }
        .form-actions { 
            display: flex; justify-content: flex-end; gap: var(--spacing-md);
            margin-top: var(--spacing-xl); padding-top: var(--spacing-lg);
            border-top: 1px solid var(--dashboard-glass-border);
        }
        .btn { 
            padding: var(--spacing-md) var(--spacing-xl); border: none; border-radius: var(--radius-md);
            font-weight: 600; cursor: pointer; transition: all 0.3s; font-size: 1rem;
            display: flex; align-items: center; gap: var(--spacing-sm);
        }
        .btn-primary { background: var(--brand-primary); color: white; }
        .btn-primary:hover { background: var(--brand-accent); transform: translateY(-1px); }
        .btn-secondary { 
            background: transparent; color: var(--dashboard-text-secondary);
            border: 1px solid var(--dashboard-glass-border);
        }
        .btn-secondary:hover { background: rgba(255,255,255,0.1); color: var(--dashboard-text-primary); }
        .status-badge.active { 
            background: rgba(34,197,94,0.2); color: #22c55e;
            border: 1px solid #22c55e; padding: 0.25rem 0.75rem;
            border-radius: 12px; font-size: 0.75rem; font-weight: 600;
            display: inline-flex; align-items: center;
        }
        .status-badge.inactive { 
            background: rgba(156,163,175,0.2); color: #9ca3af;
            border: 1px solid #9ca3af; padding: 0.25rem 0.75rem;
            border-radius: 12px; font-size: 0.75rem; font-weight: 600;
            display: inline-flex; align-items: center;
        }

        /* Account Page Styles */
        .account-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-lg);
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-lg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--dashboard-glass-border);
        }
        
        .account-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
            padding-bottom: var(--spacing-lg);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .account-name {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dashboard-text-primary);
            margin: var(--spacing-md) 0;
        }
        
        .account-subtitle {
            color: var(--dashboard-text-secondary);
            font-size: 1.1rem;
            margin-bottom: var(--spacing-md);
        }
        
        .account-settings-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }
        
        .settings-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--dashboard-glass-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .settings-card:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255,255,255,0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .form-checkbox {
            display: flex;
            align-items: flex-start;
            gap: var(--spacing-sm);
            cursor: pointer;
            margin-bottom: var(--spacing-sm);
        }
        
        .form-checkbox input[type="checkbox"] {
            width: 18px;
            height: 18px;
            margin: 0;
            cursor: pointer;
            accent-color: var(--dashboard-primary);
        }
        
        .form-checkbox-label {
            font-weight: 500;
            color: var(--dashboard-text-primary);
            user-select: none;
        }
        
        .form-help {
            display: block;
            color: var(--dashboard-text-secondary);
            font-size: 0.85rem;
            margin-top: var(--spacing-xs);
            line-height: 1.4;
        }
        
        .form-actions-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .security-actions {
            margin-bottom: var(--spacing-lg);
        }
        
        .status-badge.system {
            background: rgba(147,51,234,0.2);
            color: #9333ea;
            border: 1px solid #9333ea;
        }
        
        .btn-warning {
            background: linear-gradient(135deg, #f59e0b, #d97706);
            color: white;
            border: 1px solid #f59e0b;
        }
        
        .btn-warning:hover {
            background: linear-gradient(135deg, #d97706, #b45309);
            border-color: #d97706;
            transform: translateY(-1px);
        }
        
        .btn-danger {
            background: linear-gradient(135deg, #ef4444, #dc2626);
            color: white;
            border: 1px solid #ef4444;
        }
        
        .btn-danger:hover {
            background: linear-gradient(135deg, #dc2626, #b91c1c);
            border-color: #dc2626;
            transform: translateY(-1px);
        }
        
        /* Account page responsive */
        @media (max-width: 768px) {
            .account-settings-grid {
                grid-template-columns: 1fr;
            }
            
            .form-actions-grid {
                grid-template-columns: 1fr;
            }
            
            .account-container {
                padding: var(--spacing-md);
            }
        }

        /* System Information Page Styles */
        .system-container {
            max-width: 1400px;
            margin: 0 auto;
            padding: var(--spacing-lg);
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-lg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--dashboard-glass-border);
        }
        
        .system-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
            padding-bottom: var(--spacing-lg);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .system-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto var(--spacing-md);
            background: linear-gradient(135deg, #6366f1, #8b5cf6);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(99, 102, 241, 0.3);
        }
        
        .system-initials {
            color: white;
            font-size: 1.8rem;
            font-weight: 700;
        }
        
        .system-title {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--dashboard-text-primary);
            margin: var(--spacing-md) 0;
        }
        
        .system-subtitle {
            color: var(--dashboard-text-secondary);
            font-size: 1.1rem;
            margin-bottom: var(--spacing-md);
        }
        
        .system-metrics-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }
        
        .metric-card, .config-card, .components-card, .data-management-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--dashboard-glass-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .metric-card:hover, .config-card:hover, .components-card:hover, .data-management-card:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255,255,255,0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .metric-stats {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: var(--spacing-md);
            margin-bottom: var(--spacing-lg);
        }
        
        .stat-item {
            text-align: center;
        }
        
        .stat-value {
            font-size: 1.8rem;
            font-weight: 700;
            color: var(--dashboard-primary);
            margin-bottom: var(--spacing-xs);
        }
        
        .stat-label {
            color: var(--dashboard-text-secondary);
            font-size: 0.9rem;
            font-weight: 500;
        }
        
        .progress-bar {
            width: 100%;
            height: 8px;
            background: rgba(255,255,255,0.1);
            border-radius: 4px;
            overflow: hidden;
            margin-bottom: var(--spacing-sm);
        }
        
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--dashboard-primary), #06b6d4);
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .metric-help {
            color: var(--dashboard-text-secondary);
            font-size: 0.85rem;
            display: block;
        }
        
        .component-stats {
            margin-bottom: var(--spacing-lg);
        }
        
        .component-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm) 0;
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        
        .component-row:last-child {
            border-bottom: none;
        }
        
        .component-label {
            color: var(--dashboard-text-primary);
            font-weight: 500;
        }
        
        .component-value {
            font-weight: 600;
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.85rem;
        }
        
        .component-value.active {
            background: rgba(34,197,94,0.2);
            color: #22c55e;
            border: 1px solid #22c55e;
        }
        
        .component-value.disabled {
            background: rgba(156,163,175,0.2);
            color: #9ca3af;
            border: 1px solid #9ca3af;
        }
        
        .component-actions {
            text-align: center;
        }
        
        .data-actions {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }
        
        .action-item {
            padding: var(--spacing-md);
            background: rgba(255,255,255,0.03);
            border-radius: var(--radius-sm);
            border: 1px solid rgba(255,255,255,0.05);
        }
        
        .action-item.danger {
            background: rgba(239,68,68,0.05);
            border-color: rgba(239,68,68,0.2);
        }
        
        .action-item h4 {
            color: var(--dashboard-text-primary);
            margin: 0 0 var(--spacing-xs) 0;
            font-size: 1rem;
        }
        
        .action-item.danger h4 {
            color: #ef4444;
        }
        
        .action-item p {
            color: var(--dashboard-text-secondary);
            margin: 0 0 var(--spacing-md) 0;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        .btn-secondary {
            background: rgba(100,116,139,0.2);
            color: #64748b;
            border: 1px solid #64748b;
        }
        
        .btn-secondary:hover {
            background: rgba(100,116,139,0.3);
            border-color: #94a3b8;
            transform: translateY(-1px);
        }
        
        /* System page responsive */
        @media (max-width: 1024px) {
            .system-metrics-grid {
                grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            }
        }
        
        @media (max-width: 768px) {
            .system-metrics-grid {
                grid-template-columns: 1fr;
            }
            
            .metric-stats {
                grid-template-columns: 1fr;
            }
            
            .system-container {
                padding: var(--spacing-md);
            }
            
            .component-row {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-xs);
            }
        }

        /* Subscription Page Styles */
        .subscription-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: var(--spacing-lg);
            background: rgba(255, 255, 255, 0.05);
            border-radius: var(--radius-lg);
            backdrop-filter: blur(10px);
            border: 1px solid var(--dashboard-glass-border);
        }
        
        .subscription-header {
            text-align: center;
            margin-bottom: var(--spacing-xl);
            padding-bottom: var(--spacing-lg);
            border-bottom: 1px solid rgba(255,255,255,0.1);
        }
        
        .subscription-avatar {
            width: 70px;
            height: 70px;
            margin: 0 auto var(--spacing-md);
            background: linear-gradient(135deg, #10b981, #059669);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            box-shadow: 0 8px 32px rgba(16, 185, 129, 0.3);
        }
        
        .subscription-title {
            font-size: 2rem;
            font-weight: 700;
            color: var(--dashboard-text-primary);
            margin: var(--spacing-md) 0;
        }
        
        .subscription-subtitle {
            color: var(--dashboard-text-secondary);
            font-size: 1.1rem;
            margin-bottom: var(--spacing-md);
        }
        
        .subscription-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
            gap: var(--spacing-lg);
            margin-top: var(--spacing-lg);
        }
        
        .plan-card, .billing-card, .invoices-card, .account-management-card {
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--dashboard-glass-border);
            border-radius: var(--radius-md);
            padding: var(--spacing-lg);
            backdrop-filter: blur(10px);
            transition: all 0.3s ease;
        }
        
        .plan-card:hover, .billing-card:hover, .invoices-card:hover, .account-management-card:hover {
            background: rgba(255, 255, 255, 0.08);
            border-color: rgba(255,255,255,0.2);
            transform: translateY(-2px);
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .plan-details {
            text-align: center;
        }
        
        .plan-name {
            font-size: 1.4rem;
            font-weight: 600;
            color: var(--dashboard-text-primary);
            margin-bottom: var(--spacing-sm);
        }
        
        .plan-price {
            font-size: 2.2rem;
            font-weight: 700;
            color: var(--dashboard-primary);
            margin-bottom: var(--spacing-md);
        }
        
        .plan-period {
            font-size: 1rem;
            font-weight: 400;
            color: var(--dashboard-text-secondary);
        }
        
        .plan-features {
            margin-bottom: var(--spacing-lg);
        }
        
        .feature-item {
            padding: var(--spacing-xs) 0;
            color: var(--dashboard-text-secondary);
            font-size: 0.9rem;
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        
        .feature-item:last-child {
            border-bottom: none;
        }
        
        .billing-details {
            margin-bottom: var(--spacing-md);
        }
        
        .billing-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: var(--spacing-sm) 0;
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        
        .billing-row:last-child {
            border-bottom: none;
        }
        
        .billing-label {
            color: var(--dashboard-text-secondary);
            font-weight: 500;
        }
        
        .billing-value {
            color: var(--dashboard-text-primary);
            font-weight: 600;
            display: flex;
            align-items: center;
            gap: var(--spacing-sm);
        }
        
        .btn-small {
            padding: 0.25rem 0.75rem;
            font-size: 0.8rem;
            border-radius: var(--radius-sm);
            background: rgba(255,255,255,0.1);
            border: 1px solid var(--dashboard-glass-border);
            color: var(--dashboard-text-primary);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .btn-small:hover {
            background: rgba(255,255,255,0.2);
            border-color: rgba(255,255,255,0.3);
            transform: translateY(-1px);
        }
        
        .invoices-list {
            margin-bottom: var(--spacing-lg);
        }
        
        .invoice-item {
            display: grid;
            grid-template-columns: 1fr auto auto auto;
            gap: var(--spacing-md);
            align-items: center;
            padding: var(--spacing-sm) 0;
            border-bottom: 1px solid rgba(255,255,255,0.05);
        }
        
        .invoice-item:last-child {
            border-bottom: none;
        }
        
        .invoice-date {
            color: var(--dashboard-text-primary);
            font-weight: 500;
        }
        
        .invoice-amount {
            color: var(--dashboard-text-primary);
            font-weight: 600;
        }
        
        .invoice-status {
            padding: 0.25rem 0.75rem;
            border-radius: 12px;
            font-size: 0.8rem;
            font-weight: 600;
        }
        
        .invoice-status.paid {
            background: rgba(34,197,94,0.2);
            color: #22c55e;
            border: 1px solid #22c55e;
        }
        
        .invoice-status.unpaid {
            background: rgba(239,68,68,0.2);
            color: #ef4444;
            border: 1px solid #ef4444;
        }
        
        .btn-link {
            background: none;
            border: none;
            color: var(--dashboard-primary);
            text-decoration: underline;
            cursor: pointer;
            font-size: 0.9rem;
            padding: 0.25rem 0.5rem;
            transition: opacity 0.2s ease;
        }
        
        .btn-link:hover {
            opacity: 0.8;
        }
        
        .invoices-actions {
            text-align: center;
        }
        
        .management-actions {
            display: flex;
            flex-direction: column;
            gap: var(--spacing-lg);
        }
        
        .management-actions .action-item {
            padding: var(--spacing-md);
            background: rgba(255,255,255,0.03);
            border-radius: var(--radius-sm);
            border: 1px solid rgba(255,255,255,0.05);
        }
        
        .management-actions .action-item.danger {
            background: rgba(239,68,68,0.05);
            border-color: rgba(239,68,68,0.2);
        }
        
        .management-actions .action-item h4 {
            color: var(--dashboard-text-primary);
            margin: 0 0 var(--spacing-xs) 0;
            font-size: 1rem;
        }
        
        .management-actions .action-item.danger h4 {
            color: #ef4444;
        }
        
        .management-actions .action-item p {
            color: var(--dashboard-text-secondary);
            margin: 0 0 var(--spacing-md) 0;
            font-size: 0.9rem;
            line-height: 1.4;
        }
        
        /* Subscription page responsive */
        @media (max-width: 768px) {
            .subscription-grid {
                grid-template-columns: 1fr;
            }
            
            .subscription-container {
                padding: var(--spacing-md);
            }
            
            .invoice-item {
                grid-template-columns: 1fr;
                gap: var(--spacing-xs);
                text-align: left;
            }
            
            .billing-row {
                flex-direction: column;
                align-items: flex-start;
                gap: var(--spacing-xs);
            }
        }
        
        /* Subscription Modal Styles */
        .subscription-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100vw;
            height: 100vh;
            z-index: 10000;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .modal-backdrop {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            backdrop-filter: blur(4px);
        }
        
        .modal-content {
            background: var(--dashboard-glass-bg);
            border: 1px solid var(--dashboard-glass-border);
            border-radius: var(--radius-lg);
            padding: var(--spacing-xl);
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            position: relative;
            backdrop-filter: blur(20px);
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
        }
        
        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-lg);
            padding-bottom: var(--spacing-md);
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .modal-header h3 {
            color: var(--dashboard-text-primary);
            margin: 0;
            font-size: 1.4rem;
        }
        
        .modal-close {
            background: none;
            border: none;
            color: var(--dashboard-text-secondary);
            font-size: 1.5rem;
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            width: 2rem;
            height: 2rem;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: all 0.2s ease;
        }
        
        .modal-close:hover {
            background: rgba(255, 255, 255, 0.1);
            color: var(--dashboard-text-primary);
        }
        
        .plan-options {
            display: grid;
            gap: var(--spacing-md);
            margin-top: var(--spacing-md);
        }
        
        .plan-option {
            padding: var(--spacing-lg);
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--dashboard-glass-border);
            border-radius: var(--radius-md);
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .plan-option:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--dashboard-primary);
            transform: translateY(-2px);
        }
        
        .plan-option h4 {
            color: var(--dashboard-text-primary);
            margin: 0 0 var(--spacing-xs) 0;
        }
        
        .plan-option-price {
            color: var(--dashboard-primary);
            font-weight: 700;
            font-size: 1.2rem;
            margin: var(--spacing-xs) 0;
        }
        
        .plan-option-desc {
            color: var(--dashboard-text-secondary);
            font-size: 0.9rem;
            margin: 0;
        }
        
        .payment-options {
            display: grid;
            gap: var(--spacing-sm);
            margin-top: var(--spacing-md);
        }
        
        .payment-option {
            padding: var(--spacing-md);
            background: rgba(255, 255, 255, 0.05);
            border: 1px solid var(--dashboard-glass-border);
            border-radius: var(--radius-sm);
            cursor: pointer;
            transition: all 0.2s ease;
        }
        
        .payment-option:hover {
            background: rgba(255, 255, 255, 0.1);
            border-color: var(--dashboard-primary);
        }
        
        .subscription-settings {
            margin-top: var(--spacing-md);
        }
        
        .setting-item {
            margin-bottom: var(--spacing-md);
        }
        
        .setting-actions {
            text-align: center;
            margin-top: var(--spacing-lg);
            padding-top: var(--spacing-md);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .usage-details {
            margin-top: var(--spacing-md);
        }
        
        .usage-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--spacing-md);
            flex-wrap: wrap;
            gap: var(--spacing-sm);
        }
        
        .usage-bar {
            width: 100%;
            height: 8px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
            overflow: hidden;
        }
        
        .usage-fill {
            height: 100%;
            background: linear-gradient(90deg, var(--dashboard-primary), #06b6d4);
            transition: width 0.3s ease;
        }
        
        .upgrade-section {
            text-align: center;
            margin-top: var(--spacing-lg);
            padding-top: var(--spacing-md);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .all-invoices-list {
            margin-top: var(--spacing-md);
            max-height: 400px;
            overflow-y: auto;
        }
        
        .invoice-row {
            display: grid;
            grid-template-columns: 1fr auto auto auto;
            gap: var(--spacing-md);
            align-items: center;
            padding: var(--spacing-sm) 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }
        
        .invoice-row:last-child {
            border-bottom: none;
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-container {
                margin-right: 0;
                padding: var(--spacing-md);
            }
            
            .main-container.menu-expanded {
                margin-right: 0;
            }
            
            .header {
                flex-direction: column;
                gap: var(--spacing-md);
                text-align: center;
            }
            
            .header-title {
                font-size: 1.5rem;
            }
            
            .profile-container { padding: var(--spacing-md); }
            .form-row { grid-template-columns: 1fr; gap: var(--spacing-sm); }
            .form-actions { flex-direction: column; }
        }
        
        /* Original ui_old dropdown CSS animations for request traces and debug messages */
        .request-expanded.show {
            max-height: 1600px;
        }
        
        .trace-dropdown, .debug-dropdown, .call-trace-dropdown, .debug-messages-dropdown {
            max-height: 0;
            overflow: hidden;
            opacity: 0;
            transition: max-height 0.3s ease-in-out, opacity 0.3s ease-in-out;
        }
        
        .trace-dropdown.show, .debug-dropdown.show, .call-trace-dropdown.show, .debug-messages-dropdown.show {
            max-height: 800px;
            opacity: 1;
        }
        
    </style>
</head>