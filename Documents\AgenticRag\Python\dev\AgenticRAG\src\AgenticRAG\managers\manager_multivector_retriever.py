from imports import *
import uuid
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime
from managers.manager_document_store import DocumentStoreManager, DocumentMetadata
from managers.manager_qdrant import QDrantManager
from managers.manager_retrieval import RetrievalManager

class MultiVectorRetriever:
    """
    Multi-vector retriever that stores summaries in vector database 
    and links them to original documents in PostgreSQL, similar to 
    LangChain's MultiVectorRetriever but using our existing infrastructure.
    """
    
    _instance: Optional['MultiVectorRetriever'] = None
    _initialized: bool = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_instance(cls) -> "MultiVectorRetriever":
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return
        
        # Initialize dependencies
        await DocumentStoreManager.setup()
        await QDrantManager.setup()
        await RetrievalManager.setup()
        
        instance.document_store = DocumentStoreManager.get_instance()
        instance.vector_store = QDrantManager.get_instance()
        instance.retrieval_manager = RetrievalManager.get_instance()
        
        instance._initialized = True
    
    async def add_texts(self, texts: List[str], text_summaries: List[str], 
                       file_path: str, file_name: str, parent_doc_id: str = None) -> List[str]:
        """
        Add text documents with their summaries to the multi-vector retriever.
        
        Args:
            texts: Original text documents
            text_summaries: Summaries of the texts for vector storage
            file_path: Path to the source file
            file_name: Name of the source file
            parent_doc_id: Optional parent document ID
            
        Returns:
            List[str]: List of document IDs created
        """
        try:
            if len(texts) != len(text_summaries):
                raise ValueError("Number of texts and summaries must match")
            
            doc_ids = [str(uuid.uuid4()) for _ in texts]
            current_time = datetime.now(timezone.utc)
            
            # Store original documents in PostgreSQL
            documents_to_store = []
            vector_documents = []
            
            for i, (text, summary) in enumerate(zip(texts, text_summaries)):
                doc_id = doc_ids[i]
                
                # Create metadata for document store
                metadata = DocumentMetadata(
                    doc_id=doc_id,
                    content_type="text",
                    element_index=i,
                    file_path=file_path,
                    file_name=file_name,
                    created_at=current_time,
                    summary=summary,
                    parent_doc_id=parent_doc_id
                )
                
                documents_to_store.append((doc_id, text, metadata))
                
                # Prepare for vector storage (store summary, not original text)
                vector_metadata = {
                    "doc_id": doc_id,
                    "content_type": "text_summary",
                    "file_path": file_path,
                    "file_name": file_name,
                    "parent_doc_id": parent_doc_id,
                    "element_index": i,
                    "retriever_type": "multi_vector",
                    "has_parent_document": True
                }
                
                vector_documents.append((doc_id, summary, vector_metadata))
            
            # Store documents in PostgreSQL
            await self.document_store.store_multiple_documents(documents_to_store)
            
            # Store summaries in vector database
            ids = [doc[0] for doc in vector_documents]
            summaries = [doc[1] for doc in vector_documents]
            metadatas = [doc[2] for doc in vector_documents]
            
            await self.vector_store.upsert_multiple(ids, summaries, metadatas)
            
            return doc_ids
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "multi_vector_retriever_add_texts", None)
            return []
    
    async def add_tables(self, tables: List[str], table_summaries: List[str], 
                        file_path: str, file_name: str, parent_doc_id: str = None) -> List[str]:
        """
        Add table documents with their summaries to the multi-vector retriever.
        
        Args:
            tables: Original table content (markdown format)
            table_summaries: Summaries of the tables for vector storage
            file_path: Path to the source file
            file_name: Name of the source file
            parent_doc_id: Optional parent document ID
            
        Returns:
            List[str]: List of document IDs created
        """
        try:
            if len(tables) != len(table_summaries):
                raise ValueError("Number of tables and summaries must match")
            
            doc_ids = [str(uuid.uuid4()) for _ in tables]
            current_time = datetime.now(timezone.utc)
            
            # Store original tables in PostgreSQL
            documents_to_store = []
            vector_documents = []
            
            for i, (table, summary) in enumerate(zip(tables, table_summaries)):
                doc_id = doc_ids[i]
                
                # Create metadata for document store
                metadata = DocumentMetadata(
                    doc_id=doc_id,
                    content_type="table",
                    element_index=i,
                    file_path=file_path,
                    file_name=file_name,
                    created_at=current_time,
                    summary=summary,
                    parent_doc_id=parent_doc_id
                )
                
                documents_to_store.append((doc_id, table, metadata))
                
                # Prepare for vector storage (store summary, not original table)
                vector_metadata = {
                    "doc_id": doc_id,
                    "content_type": "table_summary",
                    "file_path": file_path,
                    "file_name": file_name,
                    "parent_doc_id": parent_doc_id,
                    "element_index": i,
                    "retriever_type": "multi_vector",
                    "has_parent_document": True
                }
                
                vector_documents.append((doc_id, summary, vector_metadata))
            
            # Store documents in PostgreSQL
            await self.document_store.store_multiple_documents(documents_to_store)
            
            # Store summaries in vector database
            ids = [doc[0] for doc in vector_documents]
            summaries = [doc[1] for doc in vector_documents]
            metadatas = [doc[2] for doc in vector_documents]
            
            await self.vector_store.upsert_multiple(ids, summaries, metadatas)
            
            return doc_ids
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "multi_vector_retriever_add_tables", None)
            return []
    
    async def add_images(self, images: List[str], image_summaries: List[str], 
                        file_path: str, file_name: str, parent_doc_id: str = None,
                        asset_paths: List[str] = None) -> List[str]:
        """
        Add image documents with their summaries to the multi-vector retriever.
        
        Args:
            images: Original image content (base64 or paths)
            image_summaries: AI-generated summaries of the images for vector storage
            file_path: Path to the source file
            file_name: Name of the source file
            parent_doc_id: Optional parent document ID
            asset_paths: Optional list of paths to saved image assets
            
        Returns:
            List[str]: List of document IDs created
        """
        try:
            if len(images) != len(image_summaries):
                raise ValueError("Number of images and summaries must match")
            
            doc_ids = [str(uuid.uuid4()) for _ in images]
            current_time = datetime.now(timezone.utc)
            asset_paths = asset_paths or [None] * len(images)
            
            # Store original images in PostgreSQL
            documents_to_store = []
            vector_documents = []
            
            for i, (image, summary) in enumerate(zip(images, image_summaries)):
                doc_id = doc_ids[i]
                
                # Create metadata for document store
                metadata = DocumentMetadata(
                    doc_id=doc_id,
                    content_type="image",
                    element_index=i,
                    file_path=file_path,
                    file_name=file_name,
                    created_at=current_time,
                    summary=summary,
                    asset_path=asset_paths[i] if i < len(asset_paths) else None,
                    parent_doc_id=parent_doc_id
                )
                
                documents_to_store.append((doc_id, image, metadata))
                
                # Prepare for vector storage (store summary, not original image)
                vector_metadata = {
                    "doc_id": doc_id,
                    "content_type": "image_summary",
                    "file_path": file_path,
                    "file_name": file_name,
                    "parent_doc_id": parent_doc_id,
                    "element_index": i,
                    "retriever_type": "multi_vector",
                    "has_parent_document": True,
                    "asset_path": asset_paths[i] if i < len(asset_paths) else None
                }
                
                vector_documents.append((doc_id, summary, vector_metadata))
            
            # Store documents in PostgreSQL
            await self.document_store.store_multiple_documents(documents_to_store)
            
            # Store summaries in vector database
            ids = [doc[0] for doc in vector_documents]
            summaries = [doc[1] for doc in vector_documents]
            metadatas = [doc[2] for doc in vector_documents]
            
            await self.vector_store.upsert_multiple(ids, summaries, metadatas)
            
            return doc_ids
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "multi_vector_retriever_add_images", None)
            return []
    
    async def add_multimodal_document(self, multimodal_data: Dict[str, Any], 
                                     file_path: str, file_name: str, 
                                     parent_doc_id: str = None) -> Dict[str, List[str]]:
        """
        Add a complete multimodal document to the retriever.
        
        Args:
            multimodal_data: Multimodal content data from MultimodalManager
            file_path: Path to the source file
            file_name: Name of the source file
            parent_doc_id: Optional parent document ID
            
        Returns:
            Dict[str, List[str]]: Document IDs created for each content type
        """
        try:
            results = {
                "text_ids": [],
                "table_ids": [],
                "image_ids": []
            }
            
            # Process text elements
            text_elements = multimodal_data.get("text_elements", [])
            if text_elements:
                texts = [elem.get("text", "") for elem in text_elements]
                # Use text content as summary for text elements
                text_summaries = texts.copy()
                
                text_ids = await self.add_texts(
                    texts=texts,
                    text_summaries=text_summaries,
                    file_path=file_path,
                    file_name=file_name,
                    parent_doc_id=parent_doc_id
                )
                results["text_ids"] = text_ids
            
            # Process table elements
            table_elements = multimodal_data.get("tables", [])
            if table_elements:
                tables = [elem.get("markdown", elem.get("text", "")) for elem in table_elements]
                table_summaries = [elem.get("summary", "") for elem in table_elements]
                
                table_ids = await self.add_tables(
                    tables=tables,
                    table_summaries=table_summaries,
                    file_path=file_path,
                    file_name=file_name,
                    parent_doc_id=parent_doc_id
                )
                results["table_ids"] = table_ids
            
            # Process image elements
            image_elements = multimodal_data.get("images", [])
            if image_elements:
                images = [elem.get("text", "") for elem in image_elements]  # Text description
                image_summaries = [elem.get("summary", "") for elem in image_elements]
                asset_paths = [elem.get("asset_path") for elem in image_elements]
                
                image_ids = await self.add_images(
                    images=images,
                    image_summaries=image_summaries,
                    file_path=file_path,
                    file_name=file_name,
                    parent_doc_id=parent_doc_id,
                    asset_paths=asset_paths
                )
                results["image_ids"] = image_ids
            
            return results
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "multi_vector_retriever_add_multimodal", None)
            return {"text_ids": [], "table_ids": [], "image_ids": []}
    
    async def similarity_search(self, query: str, k: int = 5, 
                               content_types: List[str] = None,
                               return_parent_documents: bool = True) -> List[Dict[str, Any]]:
        """
        Perform similarity search and optionally return parent documents.
        
        Args:
            query: Search query
            k: Number of results to return
            content_types: Filter by content types
            return_parent_documents: Whether to return original documents
            
        Returns:
            List[Dict]: Search results with summaries and optionally parent documents
        """
        try:
            # Perform vector search on summaries
            search_results = await self.retrieval_manager.semantic_search(
                query=query,
                top_k=k,
                filter_metadata={"retriever_type": "multi_vector"}
            )
            
            # Filter by content types if specified
            if content_types:
                filtered_results = []
                for result in search_results:
                    metadata = result.get("metadata", {})
                    content_type = metadata.get("content_type", "")
                    
                    # Check if content type matches any in the filter
                    if any(ct in content_type for ct in content_types):
                        filtered_results.append(result)
                
                search_results = filtered_results[:k]
            
            # Prepare results
            results = []
            
            if return_parent_documents:
                # Get parent documents
                doc_ids = [result.get("metadata", {}).get("doc_id") for result in search_results]
                doc_ids = [doc_id for doc_id in doc_ids if doc_id]  # Remove None values
                
                parent_documents = await self.document_store.retrieve_multiple_documents(doc_ids)
                
                for result in search_results:
                    metadata = result.get("metadata", {})
                    doc_id = metadata.get("doc_id")
                    
                    result_data = {
                        "summary": result.get("content", ""),
                        "summary_score": result.get("score", 0.0),
                        "metadata": metadata,
                        "parent_document": None,
                        "parent_metadata": None
                    }
                    
                    # Add parent document if available
                    if doc_id and doc_id in parent_documents:
                        parent_content, parent_metadata = parent_documents[doc_id]
                        result_data["parent_document"] = parent_content
                        result_data["parent_metadata"] = parent_metadata
                    
                    results.append(result_data)
            else:
                # Return only summaries
                for result in search_results:
                    result_data = {
                        "summary": result.get("content", ""),
                        "summary_score": result.get("score", 0.0),
                        "metadata": result.get("metadata", {})
                    }
                    results.append(result_data)
            
            return results
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "multi_vector_retriever_search", None)
            return []
    
    async def get_parent_documents(self, doc_ids: List[str]) -> Dict[str, Tuple[str, DocumentMetadata]]:
        """
        Get parent documents for a list of document IDs.
        
        Args:
            doc_ids: List of document IDs
            
        Returns:
            Dict: {doc_id: (content, metadata)} mapping
        """
        try:
            return await self.document_store.retrieve_multiple_documents(doc_ids)
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "multi_vector_retriever_get_parents", None)
            return {}
    
    async def delete_document(self, doc_id: str) -> bool:
        """
        Delete a document from both vector store and document store.
        
        Args:
            doc_id: Document ID to delete
            
        Returns:
            bool: True if successful
        """
        try:
            # Delete from document store
            doc_deleted = await self.document_store.delete_document(doc_id)
            
            # Delete from vector store (this would need to be implemented in QDrantManager)
            # For now, we'll just log this
            print(f"Note: Vector store deletion for doc_id {doc_id} needs manual implementation")
            
            return doc_deleted
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "multi_vector_retriever_delete", None)
            return False
    
    async def get_stats(self) -> Dict[str, Any]:
        """Get statistics about the multi-vector retriever."""
        try:
            doc_stats = await self.document_store.get_document_stats()
            return {
                "document_store_stats": doc_stats,
                "retriever_type": "multi_vector"
            }
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "multi_vector_retriever_stats", None)
            return {"error": str(e)}