<body>
    <!-- Particles Background -->
    <div class="particles" id="particles"></div>
    
    <!-- Right Side Menu -->
    <div class="right-menu" id="rightMenu">
        <button class="menu-toggle" id="menuToggle">
            <span id="menuIcon">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="3" y1="6" x2="21" y2="6"></line>
                    <line x1="3" y1="12" x2="21" y2="12"></line>
                    <line x1="3" y1="18" x2="21" y2="18"></line>
                </svg>
            </span>
        </button>
        
        <div class="menu-items" id="menuItems">
            <a href="/dashboard" class="menu-item" data-page="overview">
                <div class="menu-item-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
                        <line x1="9" y1="9" x2="15" y2="9"></line>
                        <line x1="9" y1="15" x2="15" y2="15"></line>
                        <line x1="9" y1="12" x2="15" y2="12"></line>
                    </svg>
                </div>
                <div class="menu-item-text">Overview</div>
            </a>
            
            <a href="/dashboard/profile" class="menu-item" data-page="profile">
                <div class="menu-item-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                </div>
                <div class="menu-item-text">Profile</div>
            </a>
            
            <a href="/dashboard/zaira" class="menu-item" data-page="zaira">
                <div class="menu-item-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                    </svg>
                </div>
                <div class="menu-item-text">Zaira</div>
            </a>
            
            <a href="/dashboard/account" class="menu-item" data-page="account">
                <div class="menu-item-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="3"></circle>
                        <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                    </svg>
                </div>
                <div class="menu-item-text">Account</div>
            </a>
            
            <a href="/connectors" class="menu-item" data-page="connectors">
                <div class="menu-item-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"></path>
                        <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"></path>
                    </svg>
                </div>
                <div class="menu-item-text">Connectors</div>
            </a>
            
            <a href="/dashboard/system" class="menu-item" data-page="system">
                <div class="menu-item-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
                        <line x1="8" y1="21" x2="16" y2="21"></line>
                        <line x1="12" y1="17" x2="12" y2="21"></line>
                    </svg>
                </div>
                <div class="menu-item-text">System Information</div>
            </a>
            
            <a href="/dashboard/subscription" class="menu-item" data-page="subscription">
                <div class="menu-item-icon">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="1" y="4" width="22" height="16" rx="2" ry="2"></rect>
                        <line x1="1" y1="10" x2="23" y2="10"></line>
                    </svg>
                </div>
                <div class="menu-item-text">Subscription</div>
            </a>
            
            <a href="/dashboard/grasshopper" class="menu-item" data-page="grasshopper" style="opacity: 0.01; height: 1px; overflow: hidden; margin: 0; padding: 1px;">
                <div class="menu-item-icon" style="display: none;">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                        <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                        <circle cx="12" cy="16" r="1"></circle>
                    </svg>
                </div>
                <div class="menu-item-text" style="display: none;">Debug</div>
            </a>
        </div>
    </div>
    
    <!-- Main Container -->
    <div class="main-container" id="mainContainer">
        <!-- Original ZairaControl Header -->
        <div class="header" style="background: rgba(30, 41, 59, 0.8); backdrop-filter: blur(10px); padding: 1rem 2rem; box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5); border-bottom: 1px solid rgba(59, 130, 246, 0.2); z-index: 10000; position: relative;">
            <div style="display: flex; justify-content: space-between; align-items: center; width: 100%;">
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <h1 style="background: linear-gradient(135deg, #60a5fa, #a78bfa); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-size: 2rem; font-weight: 700; text-shadow: 0 0 30px rgba(96, 165, 250, 0.5); margin: 0;">AskZaira Dashboard</h1>
                    <span class="status-badge" id="headerStatusBadge" style="display: inline-block; background: #6c757d; color: white; padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem; font-weight: 500; text-transform: uppercase;">Loading...</span>
                </div>
                
                <div style="background: linear-gradient(135deg, #60a5fa, #a78bfa); -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; font-size: 1.25rem; font-weight: 600; text-shadow: 0 0 20px rgba(96, 165, 250, 0.3); letter-spacing: 0.05em;"><!--Navigation & Controls--></div>
                
                <div style="display: flex; align-items: center; gap: 1rem;">
                    <div class="auto-refresh" id="autoRefresh">
                        <span id="refreshTimer" style="color: #94a3b8; font-size: 0.85rem;">Auto-refresh: Disabled</span>
                        <div class="refresh-toggle" id="autoRefreshToggle" style="display: inline-block; margin-left: 10px; position: relative; width: 50px; height: 24px; background: rgba(100, 116, 139, 0.3); border-radius: 12px; cursor: pointer; transition: background 0.3s ease;">
                            <div class="refresh-toggle-slider" style="position: absolute; top: 2px; left: 2px; width: 20px; height: 20px; background: white; border-radius: 50%; transition: transform 0.3s ease;"></div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Page Content Area -->
        <div class="page-content" id="pageContent">
            <div class="loading">Loading overview...</div>
        </div>
    </div>
    
    <!-- JavaScript modules will be loaded here -->