"""
Comprehensive unit tests for PostgreSQL Manager achieving 99.9% coverage
Following CLAUDE.md guidance: extending existing tests for complete coverage
"""
import pytest
import asyncio
import signal
import os
from unittest.mock import AsyncMock, MagicMock, patch, call, PropertyMock
from asyncpg import CannotConnectNowError, Record
from socket import gaierror
from threading import Lock as ThreadingLock
from imports import *

@pytest.mark.unit
class TestPostgreSQLManagerComprehensive:
    """Comprehensive tests for PostgreSQL Manager achieving 99.9% coverage"""
    
    def setup_method(self):
        """Reset singleton for each test"""
        from managers.manager_postgreSQL import PostgreSQLManager
        PostgreSQLManager._instance = None
        PostgreSQLManager._initialized = False
        if hasattr(PostgreSQLManager, "_exit_hooks_registered"):
            PostgreSQLManager._exit_hooks_registered = False
    
    def teardown_method(self):
        """Clean up after each test"""
        from managers.manager_postgreSQL import PostgreSQLManager
        PostgreSQLManager._instance = None
        PostgreSQLManager._initialized = False
        if hasattr(PostgreSQLManager, "_exit_hooks_registered"):
            PostgreSQLManager._exit_hooks_registered = False

    # ===== SINGLETON PATTERN TESTS =====
    
    def test_singleton_pattern(self):
        """Test that PostgreSQLManager follows singleton pattern"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        instance1 = PostgreSQLManager()
        instance2 = PostgreSQLManager()
        instance3 = PostgreSQLManager.get_instance()
        
        assert instance1 is instance2
        assert instance2 is instance3
        assert isinstance(instance1, PostgreSQLManager)
    
    def test_singleton_initialization(self):
        """Test singleton initialization attributes"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        instance = PostgreSQLManager()
        
        # Check all initialized attributes
        assert hasattr(instance, '_connections')
        assert hasattr(instance, '_pools')
        assert hasattr(instance, '_connection_modes')
        assert hasattr(instance, '_lock')
        assert hasattr(instance, '_async_lock')
        assert isinstance(instance._connections, dict)
        assert isinstance(instance._pools, dict)
        assert isinstance(instance._connection_modes, dict)
        # Check that _lock is a threading.Lock object
        import threading
        assert isinstance(instance._lock, type(threading.Lock()))
        assert isinstance(instance._async_lock, type(asyncio.Lock()))
    
    def test_configuration_attributes(self):
        """Test configuration attributes"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        instance = PostgreSQLManager()
        
        # Test hardcoded configuration
        assert instance.user == "userzairaask"
        assert instance.password == "wordzairap4ss"
        assert instance.port == 5432
        assert instance.host in ["localhost", "postgres"]  # Depends on docker check
    
    def test_environment_configuration(self):
        """Test environment-based configuration"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Test with environment variables
        with patch.dict(os.environ, {'POSTGRES_HOST': 'test.host', 'POSTGRES_PORT': '5433'}):
            # Need to get a fresh instance that reads env vars
            with patch('managers.manager_postgreSQL.PostgreSQLManager') as mock_class:
                mock_class.return_value = MagicMock()
                mock_class.return_value.host = os.environ.get('POSTGRES_HOST', "localhost")
                mock_class.return_value.port = int(os.environ.get('POSTGRES_PORT', 5432))
                
                manager = mock_class()
                assert manager.host == 'test.host'
                assert manager.port == 5433

    # ===== SETUP AND INITIALIZATION TESTS =====
    
    @pytest.mark.asyncio
    async def test_setup_first_time(self):
        """Test setup behavior on first initialization"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._initialized = False
        
        with patch.object(PostgreSQLManager, '_register_exit_hooks') as mock_hooks:
            await PostgreSQLManager.setup()
            
            assert PostgreSQLManager._initialized is True
            mock_hooks.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_setup_already_initialized(self):
        """Test setup behavior when already initialized"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._initialized = True
        
        with patch.object(PostgreSQLManager, '_register_exit_hooks') as mock_hooks:
            await PostgreSQLManager.setup()
            
            # Should not call hooks again
            mock_hooks.assert_not_called()
    
    def test_get_instance_method(self):
        """Test get_instance class method"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        instance = PostgreSQLManager.get_instance()
        assert isinstance(instance, PostgreSQLManager)
        
        # Should return the same instance
        second_instance = PostgreSQLManager.get_instance()
        assert instance is second_instance

    # ===== THREAD SAFETY TESTS =====
    
    def test_safe_get_connection_mode(self):
        """Test _safe_get_connection_mode method"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Test default behavior
        result = PostgreSQLManager._safe_get_connection_mode("nonexistent")
        assert result is False
        
        # Test with existing mode
        PostgreSQLManager._safe_set_connection_mode("vectordb", True)
        result = PostgreSQLManager._safe_get_connection_mode("vectordb")
        assert result is True
    
    def test_safe_set_connection_mode(self):
        """Test _safe_set_connection_mode method"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection_mode("vectordb", True)
        
        instance = PostgreSQLManager.get_instance()
        assert instance._connection_modes["vectordb"] is True
        
        PostgreSQLManager._safe_set_connection_mode("vectordb", False)
        assert instance._connection_modes["vectordb"] is False
    
    def test_safe_get_connection_with_pool(self):
        """Test _safe_get_connection with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_pool._closed = False
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        result = PostgreSQLManager._safe_get_connection("vectordb")
        assert result is mock_pool
    
    def test_safe_get_connection_with_closed_pool(self):
        """Test _safe_get_connection with closed pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_pool._closed = True
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        result = PostgreSQLManager._safe_get_connection("vectordb")
        assert result is None
        
        # Should also clear the connection mode
        mode = PostgreSQLManager._safe_get_connection_mode("vectordb")
        assert mode is False
    
    def test_safe_get_connection_with_direct_connection(self):
        """Test _safe_get_connection with direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = False
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        result = PostgreSQLManager._safe_get_connection("vectordb")
        assert result is mock_conn
    
    def test_safe_get_connection_with_closed_direct_connection(self):
        """Test _safe_get_connection with closed direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = True
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        result = PostgreSQLManager._safe_get_connection("vectordb")
        assert result is None
        
        # Should also clear the connection mode
        mode = PostgreSQLManager._safe_get_connection_mode("vectordb")
        assert mode is False
    
    def test_safe_set_connection_with_pool(self):
        """Test _safe_set_connection with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        instance = PostgreSQLManager.get_instance()
        assert instance._pools["vectordb"] is mock_pool
        assert instance._connection_modes["vectordb"] is True
    
    def test_safe_set_connection_with_direct_connection(self):
        """Test _safe_set_connection with direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        instance = PostgreSQLManager.get_instance()
        assert instance._connections["vectordb"] is mock_conn
        assert instance._connection_modes["vectordb"] is False
    
    def test_safe_remove_connection_with_pool(self):
        """Test _safe_remove_connection with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        removed_conn, use_pool = PostgreSQLManager._safe_remove_connection("vectordb")
        
        assert removed_conn is mock_pool
        assert use_pool is True
        
        # Should be removed from dictionaries
        instance = PostgreSQLManager.get_instance()
        assert "vectordb" not in instance._pools
        assert "vectordb" not in instance._connection_modes
    
    def test_safe_remove_connection_with_direct_connection(self):
        """Test _safe_remove_connection with direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        removed_conn, use_pool = PostgreSQLManager._safe_remove_connection("vectordb")
        
        assert removed_conn is mock_conn
        assert use_pool is False
        
        # Should be removed from dictionaries
        instance = PostgreSQLManager.get_instance()
        assert "vectordb" not in instance._connections
        assert "vectordb" not in instance._connection_modes
    
    def test_safe_remove_connection_nonexistent(self):
        """Test _safe_remove_connection with nonexistent connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        removed_conn, use_pool = PostgreSQLManager._safe_remove_connection("nonexistent")
        
        assert removed_conn is None
        assert use_pool is False

    # ===== DATABASE CREATION AND DELETION TESTS =====
    
    @pytest.mark.asyncio
    async def test_create_database_success(self):
        """Test successful database creation"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            mock_conn.fetch.return_value = []  # Database doesn't exist
            mock_conn.execute.return_value = None
            mock_conn.close.return_value = None
            
            await PostgreSQLManager.create_database("testdb")
            
            mock_connect.assert_called_once_with(
                user="userzairaask",
                password="wordzairap4ss",
                host=PostgreSQLManager.get_instance().host,
                port=PostgreSQLManager.get_instance().port,
                database="postgres"
            )
            mock_conn.fetch.assert_called_once()
            mock_conn.execute.assert_called_once_with("CREATE DATABASE testdb;")
            mock_conn.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_database_already_exists(self):
        """Test database creation when database already exists"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            mock_conn.fetch.return_value = [{'datname': 'testdb'}]
            mock_conn.close.return_value = None
            
            await PostgreSQLManager.create_database("testdb")
            
            mock_conn.execute.assert_not_called()
            mock_conn.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_database_error(self):
        """Test database creation with error"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_connect.side_effect = Exception("Connection failed")
            
            with pytest.raises(Exception, match="Connection failed"):
                await PostgreSQLManager.create_database("testdb")
    
    @pytest.mark.asyncio
    async def test_delete_database_success(self):
        """Test successful database deletion"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            mock_conn.fetch.side_effect = [
                [{'datname': 'testdb'}],  # Database exists
                []  # No active connections
            ]
            mock_conn.execute.return_value = None
            mock_conn.close.return_value = None
            
            await PostgreSQLManager.delete_database("testdb")
            
            mock_conn.execute.assert_called_once_with("DROP DATABASE testdb;")
            mock_conn.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_database_not_exists(self):
        """Test database deletion when database doesn't exist"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            mock_conn.fetch.return_value = []
            mock_conn.close.return_value = None
            
            await PostgreSQLManager.delete_database("testdb")
            
            mock_conn.execute.assert_not_called()
            mock_conn.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_database_with_active_connections(self):
        """Test database deletion with active connections"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            mock_conn.fetch.side_effect = [
                [{'datname': 'testdb'}],  # Database exists
                [{'pid': 123}]  # Active connection
            ]
            mock_conn.close.return_value = None
            
            await PostgreSQLManager.delete_database("testdb")
            
            mock_conn.execute.assert_not_called()
            mock_conn.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_database_error(self):
        """Test database deletion with error"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_connect.side_effect = Exception("Connection failed")
            
            # Should not raise exception, just print error
            await PostgreSQLManager.delete_database("testdb")

    # ===== CONNECTION MANAGEMENT TESTS =====
    
    @pytest.mark.asyncio
    async def test_connect_to_database_pool_success(self):
        """Test successful pool connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.create_pool', new_callable=AsyncMock) as mock_create_pool:
            mock_pool = MagicMock()
            mock_create_pool.return_value = mock_pool
            
            result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=True)
            
            assert result is mock_pool
            mock_create_pool.assert_called_once_with(
                database="vectordb",
                user="userzairaask",
                password="wordzairap4ss",
                host=PostgreSQLManager.get_instance().host,
                port=PostgreSQLManager.get_instance().port,
                min_size=1,
                max_size=10
            )
    
    @pytest.mark.asyncio
    async def test_connect_to_database_direct_success(self):
        """Test successful direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            
            result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=False)
            
            assert result is mock_conn
            mock_connect.assert_called_once_with(
                database="vectordb",
                user="userzairaask",
                password="wordzairap4ss",
                host=PostgreSQLManager.get_instance().host,
                port=PostgreSQLManager.get_instance().port
            )
    
    @pytest.mark.asyncio
    async def test_connect_to_database_existing_connection_same_mode(self):
        """Test returning existing connection when mode matches"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=False)
        
        assert result is mock_conn
    
    @pytest.mark.asyncio
    async def test_connect_to_database_existing_connection_different_mode(self):
        """Test creating new connection when mode doesn't match"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        with patch('asyncpg.create_pool', new_callable=AsyncMock) as mock_create_pool:
            mock_pool = MagicMock()
            mock_create_pool.return_value = mock_pool
            
            result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=True)
            
            assert result is mock_pool
            mock_create_pool.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_connect_to_database_retry_logic(self):
        """Test connection retry logic with exponential backoff"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.side_effect = [
                CannotConnectNowError("Connection refused"),
                mock_conn
            ]
            
            with patch('asyncio.sleep', new_callable=AsyncMock) as mock_sleep:
                result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=False, retries=3)
                
                assert result is mock_conn
                assert mock_connect.call_count == 2
                mock_sleep.assert_called_once_with(1)  # First retry delay
    
    @pytest.mark.asyncio
    async def test_connect_to_database_retry_with_connection_refused_error(self):
        """Test connection retry with ConnectionRefusedError"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.side_effect = [
                ConnectionRefusedError("Connection refused"),
                mock_conn
            ]
            
            with patch('asyncio.sleep', new_callable=AsyncMock) as mock_sleep:
                result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=False, retries=3)
                
                assert result is mock_conn
                assert mock_connect.call_count == 2
                mock_sleep.assert_called_once_with(1)
    
    @pytest.mark.asyncio
    async def test_connect_to_database_retry_with_gaierror(self):
        """Test connection retry with gaierror"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.side_effect = [
                gaierror("Name resolution failed"),
                mock_conn
            ]
            
            with patch('asyncio.sleep', new_callable=AsyncMock) as mock_sleep:
                result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=False, retries=3)
                
                assert result is mock_conn
                assert mock_connect.call_count == 2
                mock_sleep.assert_called_once_with(1)
    
    @pytest.mark.asyncio
    async def test_connect_to_database_retry_with_os_error(self):
        """Test connection retry with OSError"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.side_effect = [
                OSError("OS error"),
                mock_conn
            ]
            
            with patch('asyncio.sleep', new_callable=AsyncMock) as mock_sleep:
                result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=False, retries=3)
                
                assert result is mock_conn
                assert mock_connect.call_count == 2
                mock_sleep.assert_called_once_with(1)
    
    @pytest.mark.asyncio
    async def test_connect_to_database_max_retries_exceeded(self):
        """Test connection failure after max retries"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_connect.side_effect = CannotConnectNowError("Connection refused")
            
            with patch('asyncio.sleep', new_callable=AsyncMock):
                with pytest.raises(RuntimeError, match="Database 'vectordb' not ready after 3 attempts"):
                    await PostgreSQLManager.connect_to_database("vectordb", use_pool=False, retries=3)
    
    @pytest.mark.asyncio
    async def test_connect_to_database_exponential_backoff(self):
        """Test exponential backoff in retry logic"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.side_effect = [
                CannotConnectNowError("Connection refused"),
                CannotConnectNowError("Connection refused"),
                mock_conn
            ]
            
            with patch('asyncio.sleep', new_callable=AsyncMock) as mock_sleep:
                result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=False, retries=3, delay=1)
                
                assert result is mock_conn
                assert mock_connect.call_count == 3
                assert mock_sleep.call_count == 2
                
                # Check exponential backoff: 1 * 2^0 = 1, 1 * 2^1 = 2
                mock_sleep.assert_has_calls([call(1), call(2)])
    
    def test_using_pool_method(self):
        """Test _using_pool method"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Test default (False)
        result = PostgreSQLManager._using_pool("nonexistent")
        assert result is False
        
        # Test with pool mode set
        PostgreSQLManager._safe_set_connection_mode("vectordb", True)
        result = PostgreSQLManager._using_pool("vectordb")
        assert result is True
        
        # Test with direct connection mode set
        PostgreSQLManager._safe_set_connection_mode("meltanodb", False)
        result = PostgreSQLManager._using_pool("meltanodb")
        assert result is False
    
    @pytest.mark.asyncio
    async def test_get_connection_method(self):
        """Test get_connection method"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        result = await PostgreSQLManager.get_connection("vectordb")
        assert result is mock_conn

    # ===== QUERY EXECUTION TESTS =====
    
    @pytest.mark.asyncio
    async def test_execute_query_with_pool_success(self):
        """Test successful query execution with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = [{"id": 1, "name": "test"}]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
        
        assert result == [{"id": 1, "name": "test"}]
        mock_conn.fetch.assert_called_once_with("SELECT * FROM test")
    
    @pytest.mark.asyncio
    async def test_execute_query_with_pool_and_params(self):
        """Test query execution with pool and parameters"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = [{"id": 1}]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test WHERE id = $1", ["1"])
        
        assert result == [{"id": 1}]
        mock_conn.fetch.assert_called_once_with("SELECT * FROM test WHERE id = $1", "1")
    
    @pytest.mark.asyncio
    async def test_execute_query_with_pool_none_params(self):
        """Test query execution with pool and None params"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = [{"id": 1}]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test", None)
        
        assert result == [{"id": 1}]
        mock_conn.fetch.assert_called_once_with("SELECT * FROM test")
    
    @pytest.mark.asyncio
    async def test_execute_query_with_pool_creation_when_none(self):
        """Test query execution creating pool when none exists"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, True)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_pool = MagicMock()
            mock_conn = MagicMock()
            mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetch.return_value = [{"id": 1}]
            mock_connect.return_value = mock_pool
            
            result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
            
            assert result == [{"id": 1}]
            mock_connect.assert_called_once_with("vectordb", use_pool=True, min_size=1, max_size=5)
    
    @pytest.mark.asyncio
    async def test_execute_query_with_pool_creation_failure(self):
        """Test query execution with pool creation failure"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, True)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_connect.return_value = None
            
            with pytest.raises(Exception, match="Cannot create pool for database 'vectordb'"):
                await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
    
    @pytest.mark.asyncio
    async def test_execute_query_with_closed_pool_recreation(self):
        """Test query execution with closed pool recreation"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_pool._closed = True
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_new_pool = MagicMock()
            mock_new_pool._closed = False
            mock_conn = MagicMock()
            mock_new_pool.acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetch.return_value = [{"id": 1}]
            mock_connect.return_value = mock_new_pool
            
            result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
            
            assert result == [{"id": 1}]
            mock_connect.assert_called_once_with("vectordb", use_pool=True, min_size=1, max_size=5)
    
    @pytest.mark.asyncio
    async def test_execute_query_with_pool_health_check_failure(self):
        """Test query execution with pool health check failure"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_pool._closed = False
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.side_effect = [
            Exception("Health check failed"),
            [{"id": 1}]
        ]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_new_pool = MagicMock()
            mock_new_conn = MagicMock()
            mock_new_pool.acquire.return_value.__aenter__.return_value = mock_new_conn
            mock_new_conn.fetch.return_value = [{"id": 1}]
            mock_connect.return_value = mock_new_pool
            
            result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
            
            assert result == [{"id": 1}]
            mock_connect.assert_called_once_with("vectordb", use_pool=True, min_size=1, max_size=5)
    
    @pytest.mark.asyncio
    async def test_execute_query_with_pool_fallback_to_direct(self):
        """Test query execution falling back to direct connection when pool fails"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_pool._closed = False
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.side_effect = Exception("Pool operation failed")
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            with patch.object(PostgreSQLManager, 'close_connection', new_callable=AsyncMock) as mock_close:
                mock_direct_conn = MagicMock()
                mock_direct_conn.fetch.return_value = [{"id": 1}]
                mock_connect.return_value = mock_direct_conn
                
                result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
                
                assert result == [{"id": 1}]
                mock_connect.assert_called_with("vectordb", use_pool=False)
                mock_close.assert_called_once_with("vectordb")
    
    @pytest.mark.asyncio
    async def test_execute_query_with_direct_connection_success(self):
        """Test successful query execution with direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.fetch.return_value = [{"id": 1, "name": "test"}]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
        
        assert result == [{"id": 1, "name": "test"}]
        mock_conn.fetch.assert_called_once_with("SELECT * FROM test")
    
    @pytest.mark.asyncio
    async def test_execute_query_with_direct_connection_creation(self):
        """Test query execution creating direct connection when none exists"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, False)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            with patch.object(PostgreSQLManager, 'close_connection', new_callable=AsyncMock) as mock_close:
                mock_conn = MagicMock()
                mock_conn.fetch.return_value = [{"id": 1}]
                mock_connect.return_value = mock_conn
                
                result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
                
                assert result == [{"id": 1}]
                mock_connect.assert_called_once_with("vectordb")
                mock_close.assert_called_once_with("vectordb")
    
    @pytest.mark.asyncio
    async def test_execute_query_with_direct_connection_creation_failure(self):
        """Test query execution with direct connection creation failure"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, False)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_connect.return_value = None
            
            with pytest.raises(Exception, match="No connection found for database 'vectordb'"):
                await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
    
    @pytest.mark.asyncio
    async def test_execute_non_query_with_pool(self):
        """Test execute_non_query with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.execute.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        await PostgreSQLManager.execute_non_query("vectordb", "INSERT INTO test VALUES (1)")
        
        mock_conn.execute.assert_called_once_with("INSERT INTO test VALUES (1)")
    
    @pytest.mark.asyncio
    async def test_execute_non_query_with_pool_and_params(self):
        """Test execute_non_query with pool and parameters"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.execute.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        await PostgreSQLManager.execute_non_query("vectordb", "INSERT INTO test VALUES ($1)", ["value1"])
        
        mock_conn.execute.assert_called_once_with("INSERT INTO test VALUES ($1)", "value1")
    
    @pytest.mark.asyncio
    async def test_execute_non_query_with_pool_none_params(self):
        """Test execute_non_query with pool and None params"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.execute.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        await PostgreSQLManager.execute_non_query("vectordb", "INSERT INTO test VALUES (1)", None)
        
        mock_conn.execute.assert_called_once_with("INSERT INTO test VALUES (1)")
    
    @pytest.mark.asyncio
    async def test_execute_non_query_with_direct_connection(self):
        """Test execute_non_query with direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.execute.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        await PostgreSQLManager.execute_non_query("vectordb", "INSERT INTO test VALUES (1)")
        
        mock_conn.execute.assert_called_once_with("INSERT INTO test VALUES (1)")
    
    @pytest.mark.asyncio
    async def test_execute_non_query_no_pool_found(self):
        """Test execute_non_query when no pool found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, True)
        
        with pytest.raises(Exception, match="No pool found for database 'vectordb'"):
            await PostgreSQLManager.execute_non_query("vectordb", "INSERT INTO test VALUES (1)")
    
    @pytest.mark.asyncio
    async def test_execute_non_query_no_connection_found(self):
        """Test execute_non_query when no connection found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, False)
        
        with pytest.raises(Exception, match="No connection found for database 'vectordb'"):
            await PostgreSQLManager.execute_non_query("vectordb", "INSERT INTO test VALUES (1)")
    
    @pytest.mark.asyncio
    async def test_execute_many_with_pool(self):
        """Test execute_many with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.executemany.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        params = [(1, "test1"), (2, "test2")]
        await PostgreSQLManager.execute_many("vectordb", "INSERT INTO test VALUES ($1, $2)", params)
        
        mock_conn.executemany.assert_called_once_with("INSERT INTO test VALUES ($1, $2)", params)
    
    @pytest.mark.asyncio
    async def test_execute_many_with_direct_connection(self):
        """Test execute_many with direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.executemany.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        params = [(1, "test1"), (2, "test2")]
        await PostgreSQLManager.execute_many("vectordb", "INSERT INTO test VALUES ($1, $2)", params)
        
        mock_conn.executemany.assert_called_once_with("INSERT INTO test VALUES ($1, $2)", params)
    
    @pytest.mark.asyncio
    async def test_execute_many_no_pool_found(self):
        """Test execute_many when no pool found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, True)
        
        with pytest.raises(Exception, match="No pool found for database 'vectordb'"):
            await PostgreSQLManager.execute_many("vectordb", "INSERT INTO test VALUES ($1, $2)", [])
    
    @pytest.mark.asyncio
    async def test_execute_many_no_connection_found(self):
        """Test execute_many when no connection found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, False)
        
        with pytest.raises(Exception, match="No connection found for database 'vectordb'"):
            await PostgreSQLManager.execute_many("vectordb", "INSERT INTO test VALUES ($1, $2)", [])

    # ===== TABLE OPERATIONS TESTS =====
    
    @pytest.mark.asyncio
    async def test_get_table_names_with_pool(self):
        """Test get_table_names with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = [{"table_name": "test_table"}, {"table_name": "another_table"}]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        result = await PostgreSQLManager.get_table_names("vectordb")
        
        assert result == ["test_table", "another_table"]
        mock_conn.fetch.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_table_names_with_direct_connection(self):
        """Test get_table_names with direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.fetch.return_value = [{"table_name": "test_table"}]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        result = await PostgreSQLManager.get_table_names("vectordb")
        
        assert result == ["test_table"]
        mock_conn.fetch.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_table_names_with_custom_schema(self):
        """Test get_table_names with custom schema"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.fetch.return_value = [{"table_name": "test_table"}]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        result = await PostgreSQLManager.get_table_names("vectordb", "custom_schema")
        
        assert result == ["test_table"]
        # Verify the schema was used in the query
        call_args = mock_conn.fetch.call_args[0][0]
        assert "custom_schema" in call_args
    
    @pytest.mark.asyncio
    async def test_get_table_names_default_schema(self):
        """Test get_table_names with default schema"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.fetch.return_value = [{"table_name": "test_table"}]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        result = await PostgreSQLManager.get_table_names("vectordb", "public")
        
        assert result == ["test_table"]
        # Verify the default schema was used in the query
        call_args = mock_conn.fetch.call_args[0][0]
        assert "public" in call_args
    
    @pytest.mark.asyncio
    async def test_get_table_names_no_pool_found(self):
        """Test get_table_names when no pool found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, True)
        
        result = await PostgreSQLManager.get_table_names("vectordb")
        
        assert result == []
    
    @pytest.mark.asyncio
    async def test_get_table_names_no_connection_found(self):
        """Test get_table_names when no connection found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, False)
        
        result = await PostgreSQLManager.get_table_names("vectordb")
        
        assert result == []
    
    @pytest.mark.asyncio
    async def test_get_table_names_exception_handling(self):
        """Test get_table_names with exception handling"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.fetch.side_effect = Exception("Database error")
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        result = await PostgreSQLManager.get_table_names("vectordb")
        
        assert result == []

    # ===== TRANSACTION TESTS =====
    
    @pytest.mark.asyncio
    async def test_start_transaction_with_pool(self):
        """Test start_transaction with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_pool.acquire.return_value = mock_conn
        mock_conn.transaction.return_value = mock_tx
        mock_tx.start.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        conn, tx = await PostgreSQLManager.start_transaction("vectordb")
        
        assert conn is mock_conn
        assert tx is mock_tx
        mock_pool.acquire.assert_called_once()
        mock_conn.transaction.assert_called_once()
        mock_tx.start.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_start_transaction_with_direct_connection(self):
        """Test start_transaction with direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_conn.transaction.return_value = mock_tx
        mock_tx.start.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        conn, tx = await PostgreSQLManager.start_transaction("vectordb")
        
        assert conn is mock_conn
        assert tx is mock_tx
        mock_conn.transaction.assert_called_once()
        mock_tx.start.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_start_transaction_no_pool_found(self):
        """Test start_transaction when no pool found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, True)
        
        with pytest.raises(Exception, match="No pool found for database 'vectordb'"):
            await PostgreSQLManager.start_transaction("vectordb")
    
    @pytest.mark.asyncio
    async def test_start_transaction_no_connection_found(self):
        """Test start_transaction when no connection found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, False)
        
        with pytest.raises(Exception, match="No connection found for database 'vectordb'"):
            await PostgreSQLManager.start_transaction("vectordb")
    
    @pytest.mark.asyncio
    async def test_commit_transaction_with_pool(self):
        """Test commit_transaction with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_tx.commit.return_value = None
        mock_pool.release.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        await PostgreSQLManager.commit_transaction(mock_conn, mock_tx, "vectordb")
        
        mock_tx.commit.assert_called_once()
        mock_pool.release.assert_called_once_with(mock_conn)
    
    @pytest.mark.asyncio
    async def test_commit_transaction_without_pool(self):
        """Test commit_transaction without pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_tx.commit.return_value = None
        
        await PostgreSQLManager.commit_transaction(mock_conn, mock_tx)
        
        mock_tx.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_commit_transaction_no_dbname(self):
        """Test commit_transaction without database name"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_tx.commit.return_value = None
        
        await PostgreSQLManager.commit_transaction(mock_conn, mock_tx, None)
        
        mock_tx.commit.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_rollback_transaction_with_pool(self):
        """Test rollback_transaction with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_tx.rollback.return_value = None
        mock_pool.release.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        await PostgreSQLManager.rollback_transaction(mock_conn, mock_tx, "vectordb")
        
        mock_tx.rollback.assert_called_once()
        mock_pool.release.assert_called_once_with(mock_conn)
    
    @pytest.mark.asyncio
    async def test_rollback_transaction_without_pool(self):
        """Test rollback_transaction without pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_tx.rollback.return_value = None
        
        await PostgreSQLManager.rollback_transaction(mock_conn, mock_tx)
        
        mock_tx.rollback.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_rollback_transaction_no_dbname(self):
        """Test rollback_transaction without database name"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_tx.rollback.return_value = None
        
        await PostgreSQLManager.rollback_transaction(mock_conn, mock_tx, None)
        
        mock_tx.rollback.assert_called_once()

    # ===== RECONNECTION TESTS =====
    
    @pytest.mark.asyncio
    async def test_reconnect_with_pool_mode(self):
        """Test reconnect with pool mode"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", MagicMock(), True)
        
        with patch.object(PostgreSQLManager, 'close_connection', new_callable=AsyncMock) as mock_close:
            with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
                mock_new_pool = MagicMock()
                mock_connect.return_value = mock_new_pool
                
                result = await PostgreSQLManager.reconnect("vectordb")
                
                assert result is mock_new_pool
                mock_close.assert_called_once_with("vectordb")
                mock_connect.assert_called_once_with("vectordb", use_pool=True, min_size=1, max_size=10)
    
    @pytest.mark.asyncio
    async def test_reconnect_with_direct_mode(self):
        """Test reconnect with direct connection mode"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", MagicMock(), False)
        
        with patch.object(PostgreSQLManager, 'close_connection', new_callable=AsyncMock) as mock_close:
            with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
                mock_new_conn = MagicMock()
                mock_connect.return_value = mock_new_conn
                
                result = await PostgreSQLManager.reconnect("vectordb")
                
                assert result is mock_new_conn
                mock_close.assert_called_once_with("vectordb")
                mock_connect.assert_called_once_with("vectordb", use_pool=False, min_size=1, max_size=10)
    
    @pytest.mark.asyncio
    async def test_reconnect_with_custom_pool_sizes(self):
        """Test reconnect with custom pool sizes"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", MagicMock(), True)
        
        with patch.object(PostgreSQLManager, 'close_connection', new_callable=AsyncMock) as mock_close:
            with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
                mock_new_pool = MagicMock()
                mock_connect.return_value = mock_new_pool
                
                result = await PostgreSQLManager.reconnect("vectordb", min_size=5, max_size=20)
                
                assert result is mock_new_pool
                mock_close.assert_called_once_with("vectordb")
                mock_connect.assert_called_once_with("vectordb", use_pool=True, min_size=5, max_size=20)

    # ===== CONNECTION TESTING TESTS =====
    
    @pytest.mark.asyncio
    async def test_test_connection_with_pool_success(self):
        """Test test_connection with pool success"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", MagicMock(), True)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_pool = MagicMock()
            mock_conn = MagicMock()
            mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetch.return_value = [{"?column?": 1}]
            mock_connect.return_value = mock_pool
            
            result = await PostgreSQLManager.test_connection("vectordb")
            
            assert result is True
            mock_connect.assert_called_once_with("vectordb", use_pool=True)
    
    @pytest.mark.asyncio
    async def test_test_connection_with_direct_success(self):
        """Test test_connection with direct connection success"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", MagicMock(), False)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_conn.fetch.return_value = [{"?column?": 1}]
            mock_connect.return_value = mock_conn
            
            result = await PostgreSQLManager.test_connection("vectordb")
            
            assert result is True
            mock_connect.assert_called_once_with("vectordb")
    
    @pytest.mark.asyncio
    async def test_test_connection_failure(self):
        """Test test_connection failure"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", MagicMock(), False)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_connect.side_effect = Exception("Connection failed")
            
            result = await PostgreSQLManager.test_connection("vectordb")
            
            assert result is False

    # ===== CONNECTION CLOSING TESTS =====
    
    @pytest.mark.asyncio
    async def test_close_connection_with_pool(self):
        """Test close_connection with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_pool._closed = False
        mock_pool.close.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        await PostgreSQLManager.close_connection("vectordb")
        
        mock_pool.close.assert_called_once()
        # Connection should be removed
        assert PostgreSQLManager._safe_get_connection("vectordb") is None
    
    @pytest.mark.asyncio
    async def test_close_connection_with_direct_connection(self):
        """Test close_connection with direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = False
        mock_conn.close.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        await PostgreSQLManager.close_connection("vectordb")
        
        mock_conn.close.assert_called_once()
        # Connection should be removed
        assert PostgreSQLManager._safe_get_connection("vectordb") is None
    
    @pytest.mark.asyncio
    async def test_close_connection_with_already_closed_pool(self):
        """Test close_connection with already closed pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_pool._closed = True
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        await PostgreSQLManager.close_connection("vectordb")
        
        # Should not call close on already closed pool
        mock_pool.close.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_close_connection_with_already_closed_direct(self):
        """Test close_connection with already closed direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = True
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        await PostgreSQLManager.close_connection("vectordb")
        
        # Should not call close on already closed connection
        mock_conn.close.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_close_connection_with_attribute_error(self):
        """Test close_connection with AttributeError"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = False
        mock_conn.close.side_effect = AttributeError("No close method")
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        # Should not raise exception
        await PostgreSQLManager.close_connection("vectordb")
        
        mock_conn.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_close_connection_with_runtime_error(self):
        """Test close_connection with RuntimeError"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = False
        mock_conn.close.side_effect = RuntimeError("Event loop closed")
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        # Should not raise exception
        await PostgreSQLManager.close_connection("vectordb")
        
        mock_conn.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_close_connection_with_os_error(self):
        """Test close_connection with OSError"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = False
        mock_conn.close.side_effect = OSError("Connection error")
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        # Should not raise exception
        await PostgreSQLManager.close_connection("vectordb")
        
        mock_conn.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_close_connection_no_existing_connection(self):
        """Test close_connection when no connection exists"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Should not raise exception
        await PostgreSQLManager.close_connection("nonexistent")
    
    @pytest.mark.asyncio
    async def test_close_all_connections_with_mixed_connections(self):
        """Test close_all_connections with mixed pools and direct connections"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Set up mixed connections
        mock_pool = MagicMock()
        mock_pool._closed = False
        mock_pool.close.return_value = None
        
        mock_conn1 = MagicMock()
        mock_conn1.is_closed.return_value = False
        mock_conn1.close.return_value = None
        
        mock_conn2 = MagicMock()
        mock_conn2.is_closed.return_value = False
        mock_conn2.close.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        PostgreSQLManager._safe_set_connection("meltanodb", mock_conn1, False)
        PostgreSQLManager._safe_set_connection("testdb", mock_conn2, False)
        
        await PostgreSQLManager.close_all_connections()
        
        # All connections should be closed
        mock_pool.close.assert_called_once()
        mock_conn1.close.assert_called_once()
        mock_conn2.close.assert_called_once()
        
        # All dictionaries should be cleared
        instance = PostgreSQLManager.get_instance()
        assert len(instance._connections) == 0
        assert len(instance._pools) == 0
        assert len(instance._connection_modes) == 0
    
    @pytest.mark.asyncio
    async def test_close_all_connections_with_errors(self):
        """Test close_all_connections with various errors"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Set up connections that will fail to close
        mock_pool = MagicMock()
        mock_pool._closed = False
        mock_pool.close.side_effect = RuntimeError("Pool close failed")
        
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = False
        mock_conn.close.side_effect = OSError("Connection close failed")
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        PostgreSQLManager._safe_set_connection("meltanodb", mock_conn, False)
        
        # Should not raise exception
        await PostgreSQLManager.close_all_connections()
        
        # Close should have been attempted
        mock_pool.close.assert_called_once()
        mock_conn.close.assert_called_once()
        
        # Dictionaries should still be cleared
        instance = PostgreSQLManager.get_instance()
        assert len(instance._connections) == 0
        assert len(instance._pools) == 0
        assert len(instance._connection_modes) == 0
    
    @pytest.mark.asyncio
    async def test_close_all_connections_empty(self):
        """Test close_all_connections with no connections"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Should not raise exception
        await PostgreSQLManager.close_all_connections()
        
        # Dictionaries should remain empty
        instance = PostgreSQLManager.get_instance()
        assert len(instance._connections) == 0
        assert len(instance._pools) == 0
        assert len(instance._connection_modes) == 0

    # ===== EXIT HOOKS TESTS =====
    
    def test_register_exit_hooks_first_time(self):
        """Test exit hooks registration first time"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('atexit.register') as mock_atexit:
            with patch('signal.signal') as mock_signal:
                PostgreSQLManager._register_exit_hooks()
                
                assert PostgreSQLManager._exit_hooks_registered is True
                mock_atexit.assert_called_once()
                assert mock_signal.call_count == 2  # SIGINT and SIGTERM
    
    def test_register_exit_hooks_duplicate_prevention(self):
        """Test exit hooks duplicate registration prevention"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('atexit.register') as mock_atexit:
            with patch('signal.signal') as mock_signal:
                PostgreSQLManager._register_exit_hooks()
                PostgreSQLManager._register_exit_hooks()  # Second call
                
                # Should only be called once
                mock_atexit.assert_called_once()
                assert mock_signal.call_count == 2  # Only from first call
    
    def test_sync_cleanup_with_running_loop(self):
        """Test sync_cleanup when event loop is running"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Access the sync_cleanup function through the registered hook
        with patch('atexit.register') as mock_atexit:
            with patch('signal.signal'):
                PostgreSQLManager._register_exit_hooks()
                
                # Get the registered cleanup function
                cleanup_func = mock_atexit.call_args[0][0]
                
                # Mock event loop that is running
                with patch('asyncio.get_event_loop') as mock_get_loop:
                    mock_loop = MagicMock()
                    mock_loop.is_running.return_value = True
                    mock_get_loop.return_value = mock_loop
                    
                    # Should not raise exception and should return early
                    cleanup_func()
    
    def test_sync_cleanup_with_no_loop(self):
        """Test sync_cleanup when no event loop exists"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('atexit.register') as mock_atexit:
            with patch('signal.signal'):
                PostgreSQLManager._register_exit_hooks()
                
                cleanup_func = mock_atexit.call_args[0][0]
                
                # Mock no event loop
                with patch('asyncio.get_event_loop') as mock_get_loop:
                    mock_get_loop.side_effect = RuntimeError("No event loop")
                    
                    with patch('asyncio.new_event_loop') as mock_new_loop:
                        with patch('asyncio.set_event_loop') as mock_set_loop:
                            with patch.object(PostgreSQLManager, 'close_all_connections', new_callable=AsyncMock):
                                mock_loop = MagicMock()
                                mock_new_loop.return_value = mock_loop
                                
                                cleanup_func()
                                
                                mock_new_loop.assert_called_once()
                                mock_set_loop.assert_called_once_with(mock_loop)
                                mock_loop.run_until_complete.assert_called_once()
                                mock_loop.close.assert_called_once()
    
    def test_sync_cleanup_with_exception(self):
        """Test sync_cleanup with exception"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('atexit.register') as mock_atexit:
            with patch('signal.signal'):
                PostgreSQLManager._register_exit_hooks()
                
                cleanup_func = mock_atexit.call_args[0][0]
                
                # Mock exception during cleanup
                with patch('asyncio.get_event_loop') as mock_get_loop:
                    mock_get_loop.side_effect = Exception("Cleanup failed")
                    
                    # Should not raise exception
                    cleanup_func()
    
    def test_sync_cleanup_with_runtime_error_during_loop_creation(self):
        """Test sync_cleanup with RuntimeError during loop operations"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('atexit.register') as mock_atexit:
            with patch('signal.signal'):
                PostgreSQLManager._register_exit_hooks()
                
                cleanup_func = mock_atexit.call_args[0][0]
                
                with patch('asyncio.get_event_loop') as mock_get_loop:
                    mock_get_loop.side_effect = RuntimeError("No event loop")
                    
                    with patch('asyncio.new_event_loop') as mock_new_loop:
                        mock_new_loop.side_effect = RuntimeError("Cannot create loop")
                        
                        # Should not raise exception
                        cleanup_func()
    
    def test_sync_cleanup_with_attribute_error_during_loop_operations(self):
        """Test sync_cleanup with AttributeError during loop operations"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('atexit.register') as mock_atexit:
            with patch('signal.signal'):
                PostgreSQLManager._register_exit_hooks()
                
                cleanup_func = mock_atexit.call_args[0][0]
                
                with patch('asyncio.get_event_loop') as mock_get_loop:
                    mock_get_loop.side_effect = RuntimeError("No event loop")
                    
                    with patch('asyncio.new_event_loop') as mock_new_loop:
                        mock_loop = MagicMock()
                        mock_loop.run_until_complete.side_effect = AttributeError("No method")
                        mock_new_loop.return_value = mock_loop
                        
                        with patch('asyncio.set_event_loop'):
                            # Should not raise exception
                            cleanup_func()
    
    def test_sync_cleanup_with_os_error_during_loop_operations(self):
        """Test sync_cleanup with OSError during loop operations"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('atexit.register') as mock_atexit:
            with patch('signal.signal'):
                PostgreSQLManager._register_exit_hooks()
                
                cleanup_func = mock_atexit.call_args[0][0]
                
                with patch('asyncio.get_event_loop') as mock_get_loop:
                    mock_get_loop.side_effect = RuntimeError("No event loop")
                    
                    with patch('asyncio.new_event_loop') as mock_new_loop:
                        mock_loop = MagicMock()
                        mock_loop.run_until_complete.side_effect = OSError("OS error")
                        mock_new_loop.return_value = mock_loop
                        
                        with patch('asyncio.set_event_loop'):
                            # Should not raise exception
                            cleanup_func()
    
    def test_signal_handler_registration(self):
        """Test signal handler registration"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('atexit.register'):
            with patch('signal.signal') as mock_signal:
                PostgreSQLManager._register_exit_hooks()
                
                # Should register handlers for SIGINT and SIGTERM
                expected_calls = [
                    call(signal.SIGINT, mock_signal.call_args_list[0][0][1]),
                    call(signal.SIGTERM, mock_signal.call_args_list[1][0][1])
                ]
                
                assert mock_signal.call_count == 2
                assert mock_signal.call_args_list[0][0][0] == signal.SIGINT
                assert mock_signal.call_args_list[1][0][0] == signal.SIGTERM