            </div>

            <!-- User Content Section -->
            <div class="user-content-section" id="user-content-section">
                <div class="user-file-upload-section">
                    <h3 class="user-upload-title">Upload je bestanden</h3>
                    <p class="user-upload-subtitle">Sleep bestanden hiernaartoe of klik om te selecteren</p>
                    
                    <div class="user-upload-area" onclick="document.getElementById('user-file-input').click()">
                        <div class="user-upload-icon">📁</div>
                        <div class="user-upload-text">
                            <span class="user-primary-text">Klik om bestanden te selecteren</span>
                            <span class="user-secondary-text">of sleep ze hiernaartoe</span>
                        </div>
                        <div class="user-supported-formats">PDF, DOC, XLS, CSV, TXT</div>
                    </div>
                    
                    <input type="file" id="user-file-input" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.csv,.txt" style="display: none;">
                    
                    <div id="user-file-list" class="user-file-list"></div>
                </div>

                <button class="user-connect-button" onclick="processUserFiles()" id="user-process-button" style="display: none;">
                    <span class="user-loading"></span>
                    <span class="user-button-text">Bestanden Verwerken</span>
                </button>

                <div class="user-security-note">
                    <span class="user-security-icon">🔒</span>
                    Veilig verbonden via OAuth 2.0 - Jouw gegevens blijven privé
                </div>
            </div>

            <!-- Admin Content Section -->
            <div class="admin-content-section" id="admin-content-section" style="display: none;">
                <div class="admin-file-upload-section">
                    <h3 class="admin-upload-title">Upload bestanden (Admin)</h3>
                    <p class="admin-upload-subtitle">Sleep bestanden hiernaartoe of klik om te selecteren</p>
                    
                    <div class="admin-upload-area" onclick="document.getElementById('admin-file-input').click()">
                        <div class="admin-upload-icon">📁</div>
                        <div class="admin-upload-text">
                            <span class="admin-primary-text">Klik om bestanden te selecteren</span>
                            <span class="admin-secondary-text">of sleep ze hiernaartoe</span>
                        </div>
                        <div class="admin-supported-formats">PDF, DOC, XLS, CSV, TXT</div>
                    </div>
                    
                    <input type="file" id="admin-file-input" multiple accept=".pdf,.doc,.docx,.xls,.xlsx,.csv,.txt" style="display: none;">
                    
                    <div id="admin-file-list" class="admin-file-list"></div>
                </div>

                <button class="admin-connect-button" onclick="processAdminFiles()" id="admin-process-button" style="display: none;">
                    <span class="admin-loading"></span>
                    <span class="admin-button-text">Bestanden Verwerken</span>
                </button>

                <div class="admin-security-note">
                    <span class="admin-security-icon">🔒</span>
                    Veilig verbonden via OAuth 2.0 - Admin toegang
                </div>
            </div>
        </div>
        
        <!-- Admin Debug Section (shown only in debug mode) -->
        <div class="admin-debug-section" id="admin-debug-section" style="display: none;">
            <div class="admin-card">
                <h3 class="admin-section-title">Debug</h3>
                <div class="admin-integrations-grid" id="admin-debug-integrations">
                    <!-- Admin Debug OAuth apps will be inserted here by JavaScript -->
                </div>
                
                <h3 class="section-title">Admin Controle Panel</h3>
                <div class="admin-management-buttons" style="margin-bottom: 2rem;">
                    <button type="button" class="admin-management-button" onclick="window.location.href='/'" style="background: linear-gradient(135deg, #3b82f6, #6366f1); border: none; color: white; margin-bottom: 1rem;">
                        <span class="admin-button-text">Open Control Panel</span>
                    </button>
                </div>
                
                <h3 class="section-title">Developer Opties</h3>
                <p><form class="admin-restart-form" id="admin-restart-form">
                    <div class="admin-form-group">
                        <label class="admin-form-label" for="admin-userpass">Wachtwoord:</label>
                        <input type="password" id="admin-userpass" name="admin-userpass" class="admin-form-input" placeholder="" required>
                        <div class="admin-form-help">Deze knoppen dienen enkel in nood gebruikt te worden</div>
                        <div class="admin-form-error" id="admin-server-name-error">Vul een geldige waarde in</div>
                    </div>
                    <div class="admin-management-buttons">
                        <button type="submit" name="action" value="restart" class="admin-management-button" id="admin-restart-button">
                            <span class="admin-button-text">Zaira Opnieuw Opstarten</span>
                        </button>
                        <button type="submit" name="action" value="update" class="admin-management-button" id="admin-update-button">
                            <span class="admin-button-text">Update Omgeving</span>
                        </button>
                    </div>
                </form></p>
            </div>
        </div>
    </div>

    <script>
        let userUploadedFiles = [];
        let adminUploadedFiles = [];
        let currentContext = 'user'; // 'user' or 'admin'
        
        // Store element templates for recreation
        let userContentTemplate = null;
        let adminContentTemplate = null;
        let adminDebugTemplate = null;

        // Create floating particles
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 25;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 15 + 's';
                particle.style.animationDuration = (15 + Math.random() * 10) + 's';
                
                // Vary particle sizes
                const size = 2 + Math.random() * 4;
                particle.style.width = size + 'px';
                particle.style.height = size + 'px';
                
                // Vary opacity
                particle.style.opacity = 0.1 + Math.random() * 0.4;
                
                particlesContainer.appendChild(particle);
            }
        }

        // Initialize particles on load
        createParticles();
        
        // Initialize content sections
        initializeContentSections();
        
        function initializeContentSections() {
            // Store templates for recreation
            const userContentSection = document.getElementById('user-content-section');
            const adminContentSection = document.getElementById('admin-content-section');
            const adminDebugSection = document.getElementById('admin-debug-section');
            
            // Store original HTML templates
            userContentTemplate = userContentSection.cloneNode(true);
            adminContentTemplate = adminContentSection.cloneNode(true);
            adminDebugTemplate = adminDebugSection.cloneNode(true);
            
            // Set initial state (globaal/admin tab is active)
            userContentSection.style.display = 'none';
            adminContentSection.style.display = 'block';
            
            // Set current context and tab state
            currentContext = 'admin';
            window.currentActiveTab = 'globaal';
            
            // Ensure admin tab is visually active
            const gebruikerTab = document.getElementById('gebruiker-tab');
            const globaalTab = document.getElementById('globaal-tab');
            if (gebruikerTab) gebruikerTab.classList.remove('active');
            if (globaalTab) globaalTab.classList.add('active');
            
            // Set initial debug section visibility for admin tab
            updateDebugSectionVisibility('globaal');
        }
        
        // Initialize everything after DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize admin handlers (since admin tab is default)
            initializeAdminFormHandlers();
            initializeAdminFileHandlers();
            
            // Initialize debug section after a short delay to ensure window.userApps is available
            setTimeout(() => {
                initializeDebugSection();
                // Also update debug section visibility after initialization
                updateDebugSectionVisibility('globaal');
            }, 100);
        });
        
        function initializeDebugSection() {
            console.log('initializeDebugSection called');
            console.log('window.userApps:', window.userApps);
            console.log('window object keys:', Object.keys(window));
            
            // Show admin debug section if user apps are present (debug apps are still in window.userApps)
            if (window.userApps && window.userApps.length > 0) {
                console.log('userApps found:', window.userApps);
                
                const adminDebugSection = document.getElementById('admin-debug-section');
                const adminDebugGrid = document.getElementById('admin-debug-integrations');
                
                console.log('Debug section elements:', {
                    adminDebugSection: adminDebugSection,
                    adminDebugGrid: adminDebugGrid
                });
                
                if (adminDebugSection && adminDebugGrid) {
                    // Clear existing content
                    adminDebugGrid.innerHTML = '';
                    
                    // Populate user apps in admin debug section
                    window.userApps.forEach(app => {
                        console.log('Adding debug app:', app);
                        const appDiv = document.createElement('div');
                        appDiv.className = 'integration-item';
                        appDiv.onclick = () => connectService(app.url);
                        
                        const iconClass = app.token ? 'pipeline' : 'teams';
                        const iconText = app.token ? 'OK' : app.identifier.substring(0, 2).toUpperCase();
                        
                        appDiv.innerHTML = `
                            <div class="integration-icon ${iconClass}-icon">${iconText}</div>
                            <div class="integration-name">${app.identifier}</div>
                        `;
                        
                        adminDebugGrid.appendChild(appDiv);
                    });
                    
                    // Show debug section since we're starting on admin tab
                    console.log('Setting debug section display to block');
                    adminDebugSection.style.display = 'block';
                    
                    // Force visibility
                    adminDebugSection.style.visibility = 'visible';
                    adminDebugSection.style.opacity = '1';
                    
                    console.log('Debug section after setting display:', {
                        display: adminDebugSection.style.display,
                        visibility: adminDebugSection.style.visibility,
                        opacity: adminDebugSection.style.opacity
                    });
                } else {
                    console.error('Debug section elements not found');
                }
            } else {
                console.log('No userApps found or empty array');
                console.log('Available window variables:', Object.keys(window).filter(key => key.toLowerCase().includes('app') || key.toLowerCase().includes('debug')));
            }
        }

        // Direct OAuth connection for integrations
        function connectService(url) {
            window.location.href = url;
        }

        // Tab system functionality - check for pending tab switches
        // Use window.currentActiveTab to avoid duplicate declaration
        window.currentActiveTab = window.currentActiveTab || 'globaal'; // Track current active tab
        
        // Define switchTab function first
        async function switchTab(tabType) {
            console.log(`switchTab called with tabType: ${tabType}`);
            
            const integrationsGrid = document.querySelector('.integrations-grid');
            const userContentSection = document.getElementById('user-content-section');
            const adminContentSection = document.getElementById('admin-content-section');
            const adminDebugSection = document.getElementById('admin-debug-section');
            
            console.log('Tab switching elements found:', {
                integrationsGrid: !!integrationsGrid,
                userContentSection: !!userContentSection,
                adminContentSection: !!adminContentSection,
                adminDebugSection: !!adminDebugSection
            });
            
            // Update current active tab and context
            window.currentActiveTab = tabType;
            currentContext = tabType === 'gebruiker' ? 'user' : 'admin';
            
            console.log(`Updated currentActiveTab: ${window.currentActiveTab}, currentContext: ${currentContext}`);
            
            // Update tab active states
            updateTabStates(tabType);
            
            // Show loading state
            showLoadingState();
            
            try {
                // Simulate API call for future user/global tokens
                // For now, we'll just reload the same content
                await new Promise(resolve => setTimeout(resolve, 800)); // Simulate network delay
                
                // Reload the content (for now, same content)
                await reloadDashboardContent(tabType);
                
                // Handle content section visibility based on tab
                updateContentSectionVisibility(tabType);
                
                // Handle debug section visibility based on tab
                updateDebugSectionVisibility(tabType);
                
                // Initialize handlers for the active tab
                if (tabType === 'gebruiker') {
                    initializeUserFileHandlers();
                } else if (tabType === 'globaal') {
                    initializeAdminFormHandlers();
                    initializeAdminFileHandlers();
                    // Refresh debug section in case it wasn't initialized yet
                    setTimeout(() => {
                        initializeDebugSection();
                    }, 50);
                }
                
            } catch (error) {
                console.error('Error switching tabs:', error);
                // Show error state or fallback
            } finally {
                // Hide loading state
                hideLoadingState();
            }
        }
        
        // Make switchTab available globally as early as possible
        window.switchTab = switchTab;
        console.log('switchTab function now available globally');
        
        // Check if there's a pending tab switch from header
        function checkPendingTabSwitch() {
            if (window.pendingTabSwitch) {
                console.log(`Processing pending tab switch to: ${window.pendingTabSwitch}`);
                switchTab(window.pendingTabSwitch);
                window.pendingTabSwitch = null;
            }
        }

        function updateTabStates(activeTab) {
            const gebruikerTab = document.getElementById('gebruiker-tab');
            const globaalTab = document.getElementById('globaal-tab');
            
            // Remove active class from all tabs
            [gebruikerTab, globaalTab].forEach(tab => {
                if (tab) tab.classList.remove('active');
            });
            
            // Add active class to selected tab
            if (activeTab === 'gebruiker') {
                gebruikerTab.classList.add('active');
            } else if (activeTab === 'globaal') {
                globaalTab.classList.add('active');
            }
        }

        function updateContentSectionVisibility(tabType) {
            const userContentSection = document.getElementById('user-content-section');
            const adminContentSection = document.getElementById('admin-content-section');
            
            if (tabType === 'gebruiker') {
                // Show user content, hide admin content
                userContentSection.style.display = 'block';
                adminContentSection.style.display = 'none';
            } else if (tabType === 'globaal') {
                // Show admin content, hide user content
                userContentSection.style.display = 'none';
                adminContentSection.style.display = 'block';
            }
        }

        function updateDebugSectionVisibility(tabType) {
            const adminDebugSection = document.getElementById('admin-debug-section');
            
            console.log('updateDebugSectionVisibility called:', {
                tabType: tabType,
                adminDebugSection: !!adminDebugSection,
                userApps: window.userApps ? window.userApps.length : 'not available'
            });
            
            if (adminDebugSection) {
                if (tabType === 'gebruiker') {
                    // Hide debug section on gebruiker tab
                    console.log('Hiding debug section (user tab)');
                    adminDebugSection.style.display = 'none';
                } else if (tabType === 'globaal') {
                    // Show debug section on globaal tab (admin tab) if debug apps exist
                    if (window.userApps && window.userApps.length > 0) {
                        console.log('Showing debug section (admin tab with debug apps)');
                        adminDebugSection.style.display = 'block';
                    } else {
                        console.log('Not showing debug section (no debug apps available)');
                        adminDebugSection.style.display = 'none';
                    }
                }
            } else {
                console.error('Admin debug section not found');
            }
        }

        function showLoadingState() {
            const integrationsGrid = document.querySelector('.integrations-grid');
            const userContentSection = document.getElementById('user-content-section');
            const adminContentSection = document.getElementById('admin-content-section');
            const adminDebugSection = document.getElementById('admin-debug-section');
            
            // Add loading overlay to content areas
            [integrationsGrid, userContentSection, adminContentSection, adminDebugSection].forEach(element => {
                if (element && element.style.display !== 'none') {
                    element.style.position = 'relative';
                    element.style.overflow = 'hidden';
                    
                    const overlay = document.createElement('div');
                    overlay.className = 'loading-overlay';
                    overlay.innerHTML = `
                        <div class="loading-spinner">
                            <div class="spinner"></div>
                            <div class="loading-text">Content laden...</div>
                        </div>
                    `;
                    element.appendChild(overlay);
                    
                    // Fade out existing content
                    const existingContent = Array.from(element.children).filter(child => !child.classList.contains('loading-overlay'));
                    existingContent.forEach(child => {
                        child.style.opacity = '0.3';
                        child.style.filter = 'blur(2px)';
                        child.style.transition = 'all 0.3s ease';
                    });
                }
            });
        }

        function hideLoadingState() {
            const overlays = document.querySelectorAll('.loading-overlay');
            overlays.forEach(overlay => {
                overlay.style.opacity = '0';
                overlay.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    if (overlay.parentNode) {
                        overlay.parentNode.removeChild(overlay);
                    }
                }, 300);
            });
            
            // Restore content visibility
            const integrationsGrid = document.querySelector('.integrations-grid');
            const userContentSection = document.getElementById('user-content-section');
            const adminContentSection = document.getElementById('admin-content-section');
            const adminDebugSection = document.getElementById('admin-debug-section');
            
            [integrationsGrid, userContentSection, adminContentSection, adminDebugSection].forEach(element => {
                if (element) {
                    const existingContent = Array.from(element.children).filter(child => !child.classList.contains('loading-overlay'));
                    existingContent.forEach(child => {
                        child.style.opacity = '1';
                        child.style.filter = 'blur(0)';
                        child.style.transform = 'translateY(0)';
                    });
                }
            });
        }

        async function reloadDashboardContent(tabType) {
            // For now, we'll just reload the same content
            // In the future, this will make an API call to get user-specific or global tokens
            
            console.log(`Loading content for tab: ${tabType}`);
            
            // Placeholder for future implementation:
            // const response = await fetch(`/dashboard/content?type=${tabType}`);
            // const data = await response.json();
            // updateDashboardContent(data);
            
            // For now, just trigger a subtle animation to show content refreshed
            const integrationsGrid = document.querySelector('.integrations-grid');
            if (integrationsGrid) {
                const items = integrationsGrid.querySelectorAll('.integration-item');
                items.forEach((item, index) => {
                    item.style.transform = 'translateY(20px)';
                    item.style.opacity = '0';
                    setTimeout(() => {
                        item.style.transition = 'all 0.4s ease';
                        item.style.transform = 'translateY(0)';
                        item.style.opacity = '1';
                    }, index * 50);
                });
            }
            
            // Also animate admin section buttons if on globaal tab
            if (tabType === 'globaal') {
                await animateAdminButtons();
            }
        }

        async function animateAdminButtons() {
            const adminGrid = document.getElementById('admin-debug-integrations');
            if (!adminGrid || !window.userApps) return;

            const adminButtons = adminGrid.querySelectorAll('.integration-item');
            adminButtons.forEach((button, index) => {
                button.style.transform = 'translateY(20px)';
                button.style.opacity = '0';
                setTimeout(() => {
                    button.style.transition = 'all 0.4s ease';
                    button.style.transform = 'translateY(0)';
                    button.style.opacity = '1';
                }, index * 50);
            });
        }
		
        function initializeAdminFormHandlers() {
            const adminForm = document.getElementById('admin-restart-form');
            if (!adminForm) return; // Form not present yet
            
            adminForm.addEventListener('submit', async (e) => {
                e.preventDefault()
                
                // Validate all fields
                const inputs = adminForm.querySelectorAll('.admin-form-input');
                const restartButton = document.getElementById('admin-restart-button');
                const updateButton = document.getElementById('admin-update-button');
                const buttonText1 = restartButton.querySelector('.admin-button-text');
                const buttonText2 = updateButton.querySelector('.admin-button-text');
			
			const clickedButton = e.submitter;
			const buttonValue = clickedButton.value;

			// Start loading state
			restartButton.classList.add('button-loading');
			restartButton.disabled = true;
			updateButton.classList.add('button-loading');
			updateButton.disabled = true;
			buttonText1.textContent = 'Wachtwoord...';
			buttonText2.textContent = 'bekijken...';
			
			try {
				
				// Prepare form data
				const formData = {};
				inputs.forEach(input => {
					formData[input.name.replace(/\s+/g, '')] = input.value;
				});
				formData.timestamp = new Date().toISOString();

				// Update progress
				buttonText1.textContent = 'Server...';
				buttonText2.textContent = 'uitschakelen...';

				// Send POST request to aiohttp endpoint
				const response = await fetch(`/${buttonValue}`, {
					method: 'POST',
					headers: {
						'Content-Type': 'application/json',
					},
					body: JSON.stringify(formData)
				});

				if (!response.ok) {
					throw new Error(`HTTP error! status: ${response.status}`);
				}

				// Update progress
				buttonText1.textContent = 'Server...';
				buttonText2.textContent = 'opstarten...';
				await new Promise(resolve => setTimeout(resolve, 1000));

				const result = await response.json();
				if (result.success === true) {
					buttonText1.textContent = 'Ontvangen!';
					buttonText2.textContent = 'Wacht 5min';

					console.log('Configuration saved:', result);

					// Redirect to dashboard after success
					setTimeout(() => {
						restartButton.classList.remove('button-loading');
						restartButton.disabled = false;
						window.location.href = '/'; // Replace with your dashboard URL
					}, 300000);
				}

			} catch (error) {
				console.error('Error submitting form:', error);
				buttonText1.textContent = 'Er is een...';
				buttonText2.textContent = 'fout opgetreden';
				restartButton.classList.remove('button-loading');
				restartButton.disabled = false;
				updateButton.classList.remove('button-loading');
				updateButton.disabled = false;

				setTimeout(() => {
					buttonText.textContent = 'Configuratie Voltooien';
				}, 3000);
			}
		})
        }

        function initializeUserFileHandlers() {
            // User file upload handling
            const userFileInput = document.getElementById('user-file-input');
            const userUploadArea = document.querySelector('.user-upload-area');
            
            if (userFileInput && userUploadArea) {
                userFileInput.addEventListener('change', handleUserFiles);
                
                // User drag and drop
                userUploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    userUploadArea.classList.add('dragover');
                });

                userUploadArea.addEventListener('dragleave', () => {
                    userUploadArea.classList.remove('dragover');
                });

                userUploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    userUploadArea.classList.remove('dragover');
                    handleUserFiles({ target: { files: e.dataTransfer.files } });
                });
            }
        }

        function initializeAdminFileHandlers() {
            // Admin file upload handling
            const adminFileInput = document.getElementById('admin-file-input');
            const adminUploadArea = document.querySelector('.admin-upload-area');
            
            if (adminFileInput && adminUploadArea) {
                adminFileInput.addEventListener('change', handleAdminFiles);
                
                // Admin drag and drop
                adminUploadArea.addEventListener('dragover', (e) => {
                    e.preventDefault();
                    adminUploadArea.classList.add('dragover');
                });

                adminUploadArea.addEventListener('dragleave', () => {
                    adminUploadArea.classList.remove('dragover');
                });

                adminUploadArea.addEventListener('drop', (e) => {
                    e.preventDefault();
                    adminUploadArea.classList.remove('dragover');
                    handleAdminFiles({ target: { files: e.dataTransfer.files } });
                });
            }
        }


        function handleUserFiles(event) {
            const files = Array.from(event.target.files);
            
            files.forEach(file => {
                if (!userUploadedFiles.find(f => f.name === file.name && f.size === file.size)) {
                    userUploadedFiles.push(file);
                }
            });
            
            updateUserFileList();
            updateUserProcessButton();
        }

        function handleAdminFiles(event) {
            const files = Array.from(event.target.files);
            
            files.forEach(file => {
                if (!adminUploadedFiles.find(f => f.name === file.name && f.size === file.size)) {
                    adminUploadedFiles.push(file);
                }
            });
            
            updateAdminFileList();
            updateAdminProcessButton();
        }

        function updateUserFileList() {
            const userFileList = document.getElementById('user-file-list');
            if (userUploadedFiles.length === 0) {
                userFileList.style.display = 'none';
                return;
            }

            userFileList.style.display = 'block';
            userFileList.innerHTML = userUploadedFiles.map((file, index) => `
                <div class="user-file-item">
                    <div class="user-file-info">
                        <div class="user-file-icon">${getFileIcon(file.name)}</div>
                        <div class="user-file-details">
                            <div class="user-file-name">${file.name}</div>
                            <div class="user-file-size">${formatFileSize(file.size)}</div>
                        </div>
                    </div>
                    <button class="user-remove-file" onclick="removeUserFile(${index})">Verwijder</button>
                </div>
            `).join('');
        }

        function updateAdminFileList() {
            const adminFileList = document.getElementById('admin-file-list');
            if (adminUploadedFiles.length === 0) {
                adminFileList.style.display = 'none';
                return;
            }

            adminFileList.style.display = 'block';
            adminFileList.innerHTML = adminUploadedFiles.map((file, index) => `
                <div class="admin-file-item">
                    <div class="admin-file-info">
                        <div class="admin-file-icon">${getFileIcon(file.name)}</div>
                        <div class="admin-file-details">
                            <div class="admin-file-name">${file.name}</div>
                            <div class="admin-file-size">${formatFileSize(file.size)}</div>
                        </div>
                    </div>
                    <button class="admin-remove-file" onclick="removeAdminFile(${index})">Verwijder</button>
                </div>
            `).join('');
        }

        function removeUserFile(index) {
            userUploadedFiles.splice(index, 1);
            updateUserFileList();
            updateUserProcessButton();
        }

        function removeAdminFile(index) {
            adminUploadedFiles.splice(index, 1);
            updateAdminFileList();
            updateAdminProcessButton();
        }

        function updateUserProcessButton() {
            const userProcessButton = document.getElementById('user-process-button');
            if (userUploadedFiles.length > 0) {
                userProcessButton.style.display = 'block';
                userProcessButton.querySelector('.user-button-text').textContent = `${userUploadedFiles.length} bestand${userUploadedFiles.length > 1 ? 'en' : ''} verwerken`;
            } else {
                userProcessButton.style.display = 'none';
            }
        }

        function updateAdminProcessButton() {
            const adminProcessButton = document.getElementById('admin-process-button');
            if (adminUploadedFiles.length > 0) {
                adminProcessButton.style.display = 'block';
                adminProcessButton.querySelector('.admin-button-text').textContent = `${adminUploadedFiles.length} bestand${adminUploadedFiles.length > 1 ? 'en' : ''} verwerken`;
            } else {
                adminProcessButton.style.display = 'none';
            }
        }

        function getFileIcon(filename) {
            const ext = filename.split('.').pop().toLowerCase();
            const icons = {
                'pdf': 'PDF',
                'doc': 'DOC',
                'docx': 'DOC',
                'xls': 'XLS',
                'xlsx': 'XLS',
                'csv': 'CSV',
                'txt': 'TXT'
            };
            return icons[ext] || 'FILE';
        }

        function formatFileSize(bytes) {
            if (bytes === 0) return '0 Bytes';
            const k = 1024;
            const sizes = ['Bytes', 'KB', 'MB', 'GB'];
            const i = Math.floor(Math.log(bytes) / Math.log(k));
            return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
        }

		// --- IMPORTANT: This is the updated processUserFiles function ---
		async function processUserFiles() {
			const processButton = document.getElementById('user-process-button');
			const buttonText = processButton.querySelector('.user-button-text');

			// Show loading state
			processButton.classList.add('button-loading');
			buttonText.textContent = 'Verwerken...';

			const formData = new FormData();
			userUploadedFiles.forEach(file => {
				formData.append('files', file); // 'files' must match the name attribute in your old form input
			});

			try {
				// Create a timeout for the request (5 minutes)
				const controller = new AbortController();
				const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minutes

				const response = await fetch('/file_upload', {
					method: 'POST',
					body: formData,
					signal: controller.signal,
					// When using FormData, Content-Type header is automatically set by the browser
					// to multipart/form-data with the correct boundary. Do not set it manually.
				});

				clearTimeout(timeoutId);

				if (response.ok) {
					const result = await response.text(); // Assuming your backend returns text (e.g., "File '...' uploaded successfully.")
					console.log('Upload successful:', result);

					// Show success immediately
					buttonText.textContent = 'Voltooid!';
					processButton.classList.remove('button-loading');

					// Reset after success
					setTimeout(() => {
						userUploadedFiles = [];
						updateUserFileList();
						updateUserProcessButton();
						// Optionally, display the success message from the backend in your UI
						alert(result); // For quick testing, you can use alert
					}, 2000);

				} else {
					console.error('Upload failed:', response.status, response.statusText);
					const errorText = await response.text();
					alert('Upload failed: ' + errorText); // Display error from backend
					// Reset button state on failure
					buttonText.textContent = 'Bestanden Verwerken';
					processButton.classList.remove('button-loading');
				}
			} catch (error) {
				console.error('Error during upload:', error);
				if (error.name === 'AbortError') {
					alert('Upload timed out. The file might be too large or the server is busy. Please try again.');
				} else {
					alert('An error occurred during upload: ' + error.message);
				}
				// Reset button state on error
				buttonText.textContent = 'Bestanden Verwerken';
				processButton.classList.remove('button-loading');
			}
		}

		// --- IMPORTANT: This is the updated processAdminFiles function ---
		async function processAdminFiles() {
			const processButton = document.getElementById('admin-process-button');
			const buttonText = processButton.querySelector('.admin-button-text');

			// Show loading state
			processButton.classList.add('button-loading');
			buttonText.textContent = 'Verwerken...';

			const formData = new FormData();
			adminUploadedFiles.forEach(file => {
				formData.append('files', file); // 'files' must match the name attribute in your old form input
			});

			try {
				// Create a timeout for the request (5 minutes)
				const controller = new AbortController();
				const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minutes

				const response = await fetch('/file_upload', {
					method: 'POST',
					body: formData,
					signal: controller.signal,
					// When using FormData, Content-Type header is automatically set by the browser
					// to multipart/form-data with the correct boundary. Do not set it manually.
				});

				clearTimeout(timeoutId);

				if (response.ok) {
					const result = await response.text(); // Assuming your backend returns text (e.g., \"File '...' uploaded successfully.\")
					console.log('Upload successful:', result);

					// Show success immediately
					buttonText.textContent = 'Voltooid!';
					processButton.classList.remove('button-loading');

					// Reset after success
					setTimeout(() => {
						adminUploadedFiles = [];
						updateAdminFileList();
						updateAdminProcessButton();
						// Optionally, display the success message from the backend in your UI
						alert(result); // For quick testing, you can use alert
					}, 2000);

				} else {
					console.error('Upload failed:', response.status, response.statusText);
					const errorText = await response.text();
					alert('Upload failed: ' + errorText); // Display error from backend
					// Reset button state on failure
					buttonText.textContent = 'Bestanden Verwerken';
					processButton.classList.remove('button-loading');
				}
			} catch (error) {
				console.error('Error during upload:', error);
				if (error.name === 'AbortError') {
					alert('Upload timed out. The file might be too large or the server is busy. Please try again.');
				} else {
					alert('An error occurred during upload: ' + error.message);
				}
				// Reset button state on error
				buttonText.textContent = 'Bestanden Verwerken';
				processButton.classList.remove('button-loading');
			}
		}

        // Interactive animations for integration cards
        document.addEventListener('mousemove', (e) => {
            const cards = document.querySelectorAll('.integration-item');
            const mouseX = e.clientX;
            const mouseY = e.clientY;
            
            cards.forEach(card => {
                const rect = card.getBoundingClientRect();
                const cardX = rect.left + rect.width / 2;
                const cardY = rect.top + rect.height / 2;
                
                const angleX = (mouseY - cardY) / 30;
                const angleY = (cardX - mouseX) / 30;
                
                card.style.transform = `perspective(1000px) rotateX(${angleX}deg) rotateY(${angleY}deg) translateY(-4px)`;
            });
        });

        document.addEventListener('mouseleave', () => {
            document.querySelectorAll('.integration-item').forEach(card => {
                card.style.transform = '';
            });
        });

        // Add extra visual effects on hover
        document.querySelectorAll('.integration-item').forEach(item => {
            item.addEventListener('mouseenter', function() {
                this.style.boxShadow = '0 20px 60px rgba(59, 130, 246, 0.4)';
            });
            
            item.addEventListener('mouseleave', function() {
                this.style.boxShadow = '';
            });
        });

        // Staggered entrance animation for integration items
        window.addEventListener('load', function() {
            const integrationItems = document.querySelectorAll('.integration-item');
            integrationItems.forEach((item, index) => {
                item.style.setProperty('--index', index);
            });
        });

        // Check for pending tab switches
        checkPendingTabSwitch();
        
        // Also check after DOM is ready
        document.addEventListener('DOMContentLoaded', function() {
            console.log('DOM loaded, checking for pending tab switches...');
            checkPendingTabSwitch();
        });
        
        // Debug function to manually check debug section
        window.debugCheckDebugSection = function() {
            console.log('Manual debug check:');
            console.log('window.userApps:', window.userApps);
            console.log('adminDebugSection:', document.getElementById('admin-debug-section'));
            console.log('Current tab:', window.currentActiveTab);
            
            if (window.userApps && window.userApps.length > 0) {
                console.log('Debug should be visible, forcing initialization...');
                initializeDebugSection();
                updateDebugSectionVisibility('globaal');
            } else {
                console.log('Debug should NOT be visible (no userApps)');
            }
        };
        
        // Auto-run debug check after a longer delay
        setTimeout(() => {
            console.log('Auto-running debug check after 2 seconds...');
            window.debugCheckDebugSection();
        }, 2000);
    </script>
</body>