from imports import *
from abc import ABC, abstractmethod
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone
import asyncio
from concurrent.futures import ThreadPoolExecutor

from ..utils.config import UserQuotaConfig, ScheduledRequestsConfig
from ..utils.helpers import MemoryTracker, ThreadSafeCounter
from ..utils.exceptions import ResourceExhaustionError

class BaseScheduledRequestManager(ABC):
    """Abstract base class for all scheduled request managers"""
    
    def __init__(self, user_guid: str, quota_config: UserQuotaConfig):
        self.user_guid = user_guid
        self.quota_config = quota_config
        self._initialized = False
        self._shutdown = False
        
        # Resource tracking
        self._memory_tracker = MemoryTracker()
        self._active_tasks_counter = ThreadSafeCounter()
        self._daily_tasks_counter = ThreadSafeCounter()
        self._last_reset_date = datetime.now(timezone.utc).date()
        
        # Thread pool for user-specific operations
        self._thread_pool: Optional[ThreadPoolExecutor] = None
        self._setup_lock = asyncio.Lock()
        
        # Metrics tracking
        self._metrics = {
            'tasks_created': 0,
            'tasks_completed': 0,
            'tasks_failed': 0,
            'tasks_cancelled': 0,
            'total_execution_time': 0.0,
            'last_activity': datetime.now(timezone.utc).timestamp()
        }
        self._metrics_lock = asyncio.Lock()
    
    async def setup(self):
        """Initialize the manager - must be called before use"""
        async with self._setup_lock:
            if self._initialized or self._shutdown:
                return
                
            try:
                # Create user-specific thread pool
                self._thread_pool = ThreadPoolExecutor(
                    max_workers=self.quota_config.thread_pool_size,
                    thread_name_prefix=f"scheduled-{self.user_guid[:8]}"
                )
                
                # Perform manager-specific initialization
                await self._initialize()
                
                self._initialized = True
                LogFire.log("INIT", f"Manager initialized for user {self.user_guid}")
                
            except Exception as e:
                LogFire.log("ERROR", f"Failed to initialize manager for user {self.user_guid}: {str(e)}")
                raise ResourceExhaustionError("manager_initialization", str(e))
    
    @abstractmethod
    async def _initialize(self):
        """Manager-specific initialization logic"""
        pass
    
    async def shutdown(self):
        """Gracefully shutdown the manager"""
        if self._shutdown:
            return
            
        self._shutdown = True
        LogFire.log("INIT", f"Shutting down manager for user {self.user_guid}")
        
        try:
            # Perform manager-specific cleanup
            await self._cleanup()
            
            # Shutdown thread pool
            if self._thread_pool:
                self._thread_pool.shutdown(wait=True)
                self._thread_pool = None
            
            # Clear tracking data
            self._memory_tracker.cleanup_user(self.user_guid)
            
            LogFire.log("INIT", f"Manager shutdown complete for user {self.user_guid}")
            
        except Exception as e:
            LogFire.log("ERROR", f"Error during manager shutdown for user {self.user_guid}: {str(e)}")
    
    @abstractmethod
    async def _cleanup(self):
        """Manager-specific cleanup logic"""
        pass
    
    def is_initialized(self) -> bool:
        """Check if manager is initialized"""
        return self._initialized and not self._shutdown
    
    def is_shutdown(self) -> bool:
        """Check if manager is shutdown"""
        return self._shutdown
    
    async def check_quota_limits(self, operation_type: str) -> bool:
        """Check if operation would exceed quota limits"""
        # Reset daily counter if needed
        await self._reset_daily_counter_if_needed()
        
        current_tasks = self._active_tasks_counter.get_value()
        daily_tasks = self._daily_tasks_counter.get_value()
        memory_usage = self._memory_tracker.get_user_memory(self.user_guid)
        
        # Check limits
        if current_tasks >= self.quota_config.max_concurrent_tasks:
            LogFire.log("WARNING", f"User {self.user_guid} exceeded concurrent task limit: {current_tasks}/{self.quota_config.max_concurrent_tasks}")
            return False
            
        if daily_tasks >= self.quota_config.daily_task_limit:
            LogFire.log("WARNING", f"User {self.user_guid} exceeded daily task limit: {daily_tasks}/{self.quota_config.daily_task_limit}")
            return False
            
        if memory_usage >= self.quota_config.memory_limit_mb:
            LogFire.log("WARNING", f"User {self.user_guid} exceeded memory limit: {memory_usage}/{self.quota_config.memory_limit_mb}MB")
            return False
        
        return True
    
    async def track_task_start(self):
        """Track the start of a new task"""
        await self._reset_daily_counter_if_needed()
        
        self._active_tasks_counter.increment()
        self._daily_tasks_counter.increment()
        
        async with self._metrics_lock:
            self._metrics['tasks_created'] += 1
            self._metrics['last_activity'] = datetime.now(timezone.utc).timestamp()
    
    async def track_task_completion(self, execution_time_seconds: float, success: bool):
        """Track task completion"""
        self._active_tasks_counter.decrement()
        
        async with self._metrics_lock:
            if success:
                self._metrics['tasks_completed'] += 1
            else:
                self._metrics['tasks_failed'] += 1
            
            self._metrics['total_execution_time'] += execution_time_seconds
            self._metrics['last_activity'] = datetime.now(timezone.utc).timestamp()
    
    async def track_task_cancellation(self):
        """Track task cancellation"""
        self._active_tasks_counter.decrement()
        
        async with self._metrics_lock:
            self._metrics['tasks_cancelled'] += 1
            self._metrics['last_activity'] = datetime.now(timezone.utc).timestamp()
    
    async def get_metrics(self) -> Dict[str, Any]:
        """Get current metrics for this manager"""
        async with self._metrics_lock:
            system_memory = self._memory_tracker.get_system_memory_info()
            
            return {
                'user_guid': self.user_guid,
                'active_tasks': self._active_tasks_counter.get_value(),
                'daily_tasks': self._daily_tasks_counter.get_value(),
                'memory_usage_mb': self._memory_tracker.get_user_memory(self.user_guid),
                'quota_config': self.quota_config.dict(),
                'metrics': self._metrics.copy(),
                'system_memory': system_memory,
                'thread_pool_active': self._thread_pool is not None and not self._thread_pool._shutdown,
                'initialized': self._initialized,
                'shutdown': self._shutdown
            }
    
    async def get_health_status(self) -> Dict[str, Any]:
        """Get health status of this manager"""
        try:
            metrics = await self.get_metrics()
            
            # Determine health status
            is_healthy = (
                self.is_initialized() and 
                not self.is_shutdown() and
                metrics['active_tasks'] <= self.quota_config.max_concurrent_tasks and
                metrics['memory_usage_mb'] <= self.quota_config.memory_limit_mb
            )
            
            # Calculate load percentage
            load_percentage = min(100, (metrics['active_tasks'] / self.quota_config.max_concurrent_tasks) * 100)
            
            return {
                'user_guid': self.user_guid,
                'status': 'healthy' if is_healthy else 'unhealthy',
                'load_percentage': load_percentage,
                'last_activity': metrics['metrics']['last_activity'],
                'uptime_seconds': datetime.now(timezone.utc).timestamp() - metrics['metrics'].get('created_at', datetime.now(timezone.utc).timestamp()),
                'issues': self._get_health_issues(metrics)
            }
            
        except Exception as e:
            return {
                'user_guid': self.user_guid,
                'status': 'error',
                'error': str(e),
                'issues': ['Health check failed']
            }
    
    def _get_health_issues(self, metrics: Dict[str, Any]) -> List[str]:
        """Identify health issues from metrics"""
        issues = []
        
        if not self._initialized:
            issues.append("Manager not initialized")
        
        if self._shutdown:
            issues.append("Manager is shutdown")
        
        if metrics['active_tasks'] >= self.quota_config.max_concurrent_tasks:
            issues.append("At concurrent task limit")
        
        if metrics['memory_usage_mb'] >= self.quota_config.memory_limit_mb * 0.9:
            issues.append("High memory usage")
        
        if self._thread_pool and self._thread_pool._shutdown:
            issues.append("Thread pool is shutdown")
        
        return issues
    
    async def _reset_daily_counter_if_needed(self):
        """Reset daily counter if date has changed"""
        current_date = datetime.now(timezone.utc).date()
        if current_date != self._last_reset_date:
            self._daily_tasks_counter.reset()
            self._last_reset_date = current_date
            LogFire.log("TASK", f"Daily task counter reset for user {self.user_guid}")
    
    def get_thread_pool(self) -> Optional[ThreadPoolExecutor]:
        """Get the thread pool for this manager"""
        return self._thread_pool if self.is_initialized() else None