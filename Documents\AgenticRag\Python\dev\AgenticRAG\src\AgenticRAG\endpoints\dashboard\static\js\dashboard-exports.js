// Dashboard Exports JavaScript - Consolidated Function Exports Module
// Contains: All consolidated function exports and main initialization

// Wait for all modules to load before exporting functions
function waitForModulesAndExport() {
    // Check if all required objects/functions are available
    const requiredItems = [
        'TemplateLoader',
        'BrowserCompat', 
        'safeFetch',
        'LiveLogViewer',
        'NotificationSystem',
        'PollingManager',
        'initializeLiveLogViewer',
        'showTab',
        'loadUserList'
    ];
    
    let allLoaded = true;
    for (const item of requiredItems) {
        if (typeof window[item] === 'undefined' && typeof eval(`typeof ${item}`) === 'undefined') {
            allLoaded = false;
            console.log(`[DEBUG] Waiting for ${item} to load...`);
            break;
        }
    }
    
    if (!allLoaded) {
        // Try again in 50ms
        setTimeout(waitForModulesAndExport, 50);
        return;
    }
    
    console.log('[DEBUG] All modules loaded, exporting functions...');
    exportAllFunctions();
}

// Export all functions to global scope
function exportAllFunctions() {
    // ================================
    // CONSOLIDATED FUNCTION EXPORTS
    // ================================
    // All function exports consolidated here to ensure consistent availability

    // Core navigation and UI functions (with defensive checks)
    if (typeof showTab === 'function') window.showTab = showTab;
    if (typeof filterUsers === 'function') window.filterUsers = filterUsers;
    if (typeof loadUserList === 'function') window.loadUserList = loadUserList;
    console.log('[DEBUG] dashboard-exports: Checking selectUser availability - type:', typeof selectUser);
    if (typeof selectUser === 'function') {
        window.selectUser = selectUser;
        console.log('[DEBUG] dashboard-exports: selectUser exported successfully');
    } else {
        console.log('[WARNING] dashboard-exports: selectUser not available for export');
    }
    if (typeof loadUserRequests === 'function') window.loadUserRequests = loadUserRequests;
    if (typeof loadUserChatHistory === 'function') window.loadUserChatHistory = loadUserChatHistory;
    if (typeof loadChatHistory === 'function') window.loadChatHistory = loadChatHistory;
    if (typeof loadSystemOverview === 'function') window.loadSystemOverview = loadSystemOverview;
    if (typeof toggleAutoRefresh === 'function') window.toggleAutoRefresh = toggleAutoRefresh;
    if (typeof navigateToChatSession === 'function') window.navigateToChatSession = navigateToChatSession;
    if (typeof highlightChatSession === 'function') window.highlightChatSession = highlightChatSession;
    if (typeof showCallTrace === 'function') window.showCallTrace = showCallTrace;
    if (typeof showDebugMessages === 'function') window.showDebugMessages = showDebugMessages;
    if (typeof toggleRequestSession === 'function') window.toggleRequestSession = toggleRequestSession;
    if (typeof renderRequestDetails === 'function') window.renderRequestDetails = renderRequestDetails;
    if (typeof startRequestPolling === 'function') window.startRequestPolling = startRequestPolling;
    if (typeof stopRequestPolling === 'function') window.stopRequestPolling = stopRequestPolling;

    // Template loader functions  
    if (typeof TemplateLoader !== 'undefined') window.TemplateLoader = TemplateLoader;
    if (typeof TemplateLoader !== 'undefined' && TemplateLoader.clearCache) {
        window.clearTemplateCache = () => TemplateLoader.clearCache();
    }

    // Browser compatibility functions
    if (typeof BrowserCompat !== 'undefined') window.BrowserCompat = BrowserCompat;
    if (typeof safeFetch === 'function') window.safeFetch = safeFetch;
    if (typeof handleSessionTimeout === 'function') window.handleSessionTimeout = handleSessionTimeout;
    if (typeof restoreDashboardState === 'function') window.restoreDashboardState = restoreDashboardState;
    if (typeof escapeHtml === 'function') window.escapeHtml = escapeHtml;
    if (typeof initializeUserPermissions === 'function') window.initializeUserPermissions = initializeUserPermissions;
    if (typeof initializeDashboard === 'function') window.initializeDashboard = initializeDashboard;

    // Live log viewer functions (with defensive checks)
    if (typeof initializeLiveLogViewer === 'function') window.initializeLiveLogViewer = initializeLiveLogViewer;
    if (typeof copyLogContent === 'function') window.copyLogContent = copyLogContent;
    if (typeof filterLogContent === 'function') window.filterLogContent = filterLogContent;
    if (typeof forceRefreshLogs === 'function') window.forceRefreshLogs = forceRefreshLogs;
    if (typeof applyLogFilters === 'function') window.applyLogFilters = applyLogFilters;
    if (typeof switchLogSource === 'function') window.switchLogSource = switchLogSource;
    if (typeof LiveLogViewer !== 'undefined') window.LiveLogViewer = LiveLogViewer;

    // Notification system functions (with defensive checks)
    if (typeof showNotification === 'function') window.showNotification = showNotification;
    if (typeof removeNotification === 'function') window.removeNotification = removeNotification;
    if (typeof clearAllNotifications === 'function') window.clearAllNotifications = clearAllNotifications;
    if (typeof NotificationSystem !== 'undefined') window.NotificationSystem = NotificationSystem;

    // Test_real server functions (with defensive checks)
    if (typeof checkAndRedirectTestReal === 'function') window.checkAndRedirectTestReal = checkAndRedirectTestReal;
    if (typeof checkAndApplyHeaderStyling === 'function') window.checkAndApplyHeaderStyling = checkAndApplyHeaderStyling;

    // Raw mode functions (with defensive checks)
    if (typeof updateRawModeButton === 'function') window.updateRawModeButton = updateRawModeButton;
    if (typeof toggleRawMode === 'function') window.toggleRawMode = toggleRawMode;
    if (typeof jumpToLatestLogs === 'function') window.jumpToLatestLogs = jumpToLatestLogs;

    // Chat history functions (with defensive checks)
    if (typeof renderSessionMessages === 'function') window.renderSessionMessages = renderSessionMessages;
    if (typeof renderCallTrace === 'function') window.renderCallTrace = renderCallTrace;
    if (typeof renderDebugMessages === 'function') window.renderDebugMessages = renderDebugMessages;
    if (typeof toggleChatSession === 'function') window.toggleChatSession = toggleChatSession;
    if (typeof toggleCallTrace === 'function') window.toggleCallTrace = toggleCallTrace;
    if (typeof toggleDebugMessages === 'function') window.toggleDebugMessages = toggleDebugMessages;
    if (typeof HeightManager !== 'undefined') window.HeightManager = HeightManager;
    if (typeof startChatHistoryPolling === 'function') window.startChatHistoryPolling = startChatHistoryPolling;
    if (typeof stopChatHistoryPolling === 'function') window.stopChatHistoryPolling = stopChatHistoryPolling;

    // Manual load functions (with defensive checks)
    if (typeof loadUserRequestsManual === 'function') window.loadUserRequestsManual = loadUserRequestsManual;

    // Global instances
    window.pollingManager = window.pollingManager || new PollingManager();
    
    // Export auto-refresh variables for embedded code access
    if (typeof autoRefreshInterval !== 'undefined') window.autoRefreshInterval = autoRefreshInterval;
    if (typeof autoRefreshEnabled !== 'undefined') window.autoRefreshEnabled = autoRefreshEnabled;
    
    // Utility functions (already defined in dashboard-ui.js)
    // window.safeCall and window.safeDocumentWrite are already exported
    // window.debugSession is already exported

    // SIGNAL: Dashboard.js has fully loaded and is ready
    console.log('[DEBUG] dashboard-exports.js fully loaded - all functions exported to global scope');
    window.dashboardJsLoaded = true;

    // Log all available functions for debugging
    console.log('[DEBUG] Available functions after dashboard.js load:', Object.keys(window).filter(k => k.includes('Log') || k.includes('log') || k.includes('checkAnd') || k.includes('toggle')));

    // Signal to the HTML template coordination system that this script is ready
    if (typeof checkAllScriptsLoaded === 'function') {
        console.log('[DEBUG] dashboard.js signaling completion to script coordination system');
        checkAllScriptsLoaded();
    } else {
        console.log('[DEBUG] dashboard.js loaded but script coordination system not yet available');
    }
    
    // Initialize dashboard automatically once everything is exported
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeDashboard);
    } else {
        initializeDashboard();
    }
}

// Start the module loading check
console.log('[DEBUG] dashboard-exports.js starting module check...');
waitForModulesAndExport();