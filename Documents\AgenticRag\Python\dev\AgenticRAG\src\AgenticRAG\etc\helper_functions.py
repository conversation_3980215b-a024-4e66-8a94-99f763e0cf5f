from os import path, listdir, environ
from typing import Literal, Optional, TYPE_CHECKING

from globals import Globals

if TYPE_CHECKING:
    from userprofiles.ZairaUser import Zaira<PERSON>ser
    from userprofiles.ZairaChat import ZairaChat

def is_claude_environment():
    """Detect if the code is being run by <PERSON>"""
    # Check for Claude-specific environment variables
    claude_code = environ.get('CLAUDE_CODE')
    anthropic_user_id = environ.get('ANTHROPIC_USER_ID')
    
    # Simple check for Claude environment variables
    return bool(claude_code) or bool(anthropic_user_id)

def get_any_message_as_type():
    from langchain_core.messages import AIMessage, HumanMessage, ChatMessage, SystemMessage, FunctionMessage, ToolMessage, AIMessageChunk, HumanMessageChunk, ChatMessageChunk, SystemMessageChunk, FunctionMessageChunk, ToolMessageChunk, AnyMessage
    return (
        AIMessage,
        HumanMessage,
        ChatMessage,
        SystemMessage,
        FunctionMessage,
        ToolMessage,
        AIMessageChunk,
        HumanMessageChunk,
        ChatMessageChunk,
        SystemMessageChunk,
        FunctionMessageChunk,
        ToolMessageChunk,
    )

def folder_has_files(folder_path):
    # Check if the folder exists
    from managers.manager_logfire import LogFire
    if not path.isdir(folder_path):
        LogFire.log("ERROR", f"The folder {folder_path} does not exist.", severity="error")
        return False

    # List all entries in the directory
    entries = listdir(folder_path)
    
    # Check if any entry is a file (not a directory)
    for entry in entries:
        if path.isfile(path.join(folder_path, entry)):
            return True
    
    return False

def convert_key_value_to_string(**kwargs):
    return "\n\n".join(f"{key.replace('_', ' ')}: {value}" for key, value in kwargs.items())

def exception_triggered(e: Exception, affix: str = "", chat: Optional["ZairaChat"] = None):
    from managers.manager_logfire import LogFire
    import traceback
    import sys
    trace = traceback.format_exc()
    LogFire.log("ERROR", f"Error '{e.stderr}' during execution: {e}", chat=chat, severity="error")
    LogFire.log("ERROR", f"Stack trace: {trace}", chat=chat, severity="error")
    LogFire.log("ERROR", trace, affix, chat=chat, severity="error")
    if Globals.is_debug() == False:
        #output_task = SupervisorManager.get_task(f"{str(state.sections['OriginalSource'].get_description()).lower()}_out")
        #state.messages.append(HumanMessage(response))
        #await output_task.llm_call(state)
        # Don't actually exit during testing
        if 'pytest' not in sys.modules:
            exit()
    else:
        # Only break if not in test environment
        if 'pytest' not in sys.modules:
            breakpoint()
 
def handle_asyncio_task_result_errors(task):
    try:
        task.result()
    except Exception as e:
        import sys
        if 'pytest' not in sys.modules:
            breakpoint()
        from managers.manager_logfire import LogFire
        LogFire.log("ERROR", f"Caught exception: {e}", severity="error")

def call_cmd_debug(path, program, command):
    # Can NOT be enabled in production!!
    if Globals.is_docker() == True:
        return
    
    from subprocess import run as subprocess_run
    from subprocess import CalledProcessError

    try:
        result = subprocess_run(
            [program] + command.split(),
            capture_output=True,
            text=True,
            check=True,
            cwd=path
        )
        return result.stdout
    except CalledProcessError as e:
        from managers.manager_logfire import LogFire
        #LogFire.log("ERROR", f"Call_Network_Docker Error: {e.stderr}", severity="error")
        exception_triggered(e)

def call_network_docker(container, command):
    from subprocess import run as subprocess_run
    from subprocess import CalledProcessError

    network = get_value_from_env("ZAIRA_NETWORK_NAME", None)
    if Globals.is_docker():
        try:
            result = subprocess_run(
                ["docker", "exec", network + "-" + container, container] + command.split(),
                capture_output=True,
                text=True,
                check=True
            )
            return result.stdout
        except CalledProcessError as e:
            from managers.manager_logfire import LogFire
            #LogFire.log("ERROR", f"Call_Network_Docker Error: {e.stderr}", severity="error")
            exception_triggered(e)
    else:
        return call_cmd_debug("src/meltano/askzaira", container, command)
        if Globals.is_docker():
            LogFire.log("ERROR", "Call_Network_Docker Error: No ZAIRA_NETWORK_NAME found", severity="error")

def get_value_from_env(key: str, default_value: str | list[str] = "", can_be_list=False) -> str | list[str]:
    from os import getenv

    # Load the value from env or fall back to default
    raw_value = getenv(key)
    if raw_value is None:
        if isinstance(default_value, list):
            return default_value
        raw_value = default_value

    if can_be_list:
        # Auto-detect list
        if isinstance(raw_value, str) and "," in raw_value:
            return [item.strip() for item in raw_value.split(",") if item.strip()]
    
    return raw_value

def save_to_env(items: dict):
    def save_to_env_internal(env_path):
        try:
            from os import getcwd
            from os import path as os_path
            from managers.manager_logfire import LogFire
            # Determine the actual env file path based on environment
            actual_env_path = env_path
            if not Globals.is_docker():
                # For local development, use the relative path to meltano .env file
                if env_path == "/meltano/project/.env":
                    actual_env_path = getcwd() + "/src/meltano/askzaira/.env"
                elif env_path == "/app/.env":
                    actual_env_path = getcwd() + "/.env"  # Local .env file in AgenticRAG directory
            lines = []
            if os_path.exists(actual_env_path):
                with open(actual_env_path, "r") as f:
                    lines = f.readlines()

            if len(items) > 0:
                for key, value in items.items():
                    LogFire.log("DEBUG", f"Setting {key}={value}", severity="debug")

                    # Update or add the env variable
                    updated = False
                    for i, line in enumerate(lines):
                        if line.startswith(f"{key}="):
                            lines[i] = f"{key}={value}\n"
                            updated = True
                            break

                    if not updated:
                        lines.append(f"{key}={value}\n")
            # Write the updated lines back
            with open(actual_env_path, "w") as f:
                f.writelines(lines)
                LogFire.log("EVENT", f"Successfully saved {' '.join(items.keys())} credentials to {actual_env_path}")
                LogFire.log("RETRIEVE", ".env file modified with keys: " + ' '.join(items.keys()), ".env file modified with values: " + ' '.join(items.keys()), chat=None)
        except Exception as e:
            LogFire.log("ERROR", f"Error saving to env file: {e}", severity="error")
            ret_val = False
    
    if not Globals.is_docker():
        save_to_env_internal("/meltano/project/.env")
    save_to_env_internal("/app/.env")

def create_html_out(page_name: str, content: str) -> str:
    from os import getcwd
    from os import path as os_path
    if Globals.is_docker():
        ui_folder = "/app/ui/"
    else:
        # Check for the ui folder in multiple possible locations
        current_dir = getcwd()
        possible_paths = [
            os_path.join(current_dir, "ui"),                          # If already in src/AgenticRAG
            os_path.join(current_dir, "src", "AgenticRAG", "ui"),     # If in project root (AgenticRAG)
            os_path.join(current_dir, "..", "..", "ui"),              # Alternative structure
        ]
        
        ui_folder = None
        for path in possible_paths:
            if os_path.exists(path) and os_path.isdir(path):
                ui_folder = path
                break
        
        # Fallback to original logic if no valid path found
        if ui_folder is None:
            ui_folder = os_path.join(current_dir, "src", "AgenticRAG", "ui")

    with open(os_path.join(ui_folder, page_name + "_head.txt"), "r", encoding="utf-8") as f:
        HEAD_HTML = f.read()

    with open(os_path.join(ui_folder, page_name + "_header.txt"), "r", encoding="utf-8") as f:
        HEADER_HTML = f.read()

    with open(os_path.join(ui_folder, page_name + "_footer.txt"), "r", encoding="utf-8") as f:
        FOOTER_HTML = f.read()

    # Load whitelabel CSS if it exists and if {whitelabel_css} placeholder is found
    whitelabel_css = ""
    if "{whitelabel_css}" in HEAD_HTML:
        try:
            with open(os_path.join(ui_folder, "whitelabel.txt"), "r", encoding="utf-8") as f:
                whitelabel_css = f.read()
        except FileNotFoundError:
            pass  # whitelabel.txt is optional
        HEAD_HTML = HEAD_HTML.replace("{whitelabel_css}", whitelabel_css)

    # Dashboard.js inline loading DISABLED to prevent conflicts with external module loading
    # External module loading is handled by dashboard_header.txt 
    if "{dashboard_js_content}" in FOOTER_HTML:
        # Simply remove the placeholder - no inline loading
        FOOTER_HTML = FOOTER_HTML.replace("{dashboard_js_content}", 
            "// Dashboard modules loaded externally via dashboard_header.txt")

    # Inject favicon link into HEAD section
    if "</head>" in HEAD_HTML:
        HEAD_HTML = HEAD_HTML.replace("</head>", '    <link rel="icon" type="image/x-icon" href="/favicon.ico">\n</head>')
    
    html_content = f"""<!DOCTYPE html>
            <html lang="nl">
            {HEAD_HTML}
            {HEADER_HTML}
            {content}
            {FOOTER_HTML}
            </html>"""
    return html_content

def get_password(username: str) -> str:
    from hashlib import md5
    
    # Debug logging for password generation
    try:
        from managers.manager_logfire import LogFire
        LogFire.log("DEBUG", f"Password Gen Debug - Input: {username}", severity="debug")
        
        hash_input = "!askzaira#" + username + "-askzaira="
        LogFire.log("DEBUG", f"Password Gen Debug - Hash input string: {hash_input}", severity="debug")
        
        hash_result = md5(hash_input.encode('utf-8')).hexdigest()
        LogFire.log("DEBUG", f"Password Gen Debug - Hash result: {hash_result}", severity="debug")
        
        return hash_result
    except ImportError:
        # Fallback if LogFire not available (shouldn't happen but safety first)
        return md5(("!askzaira#" + username + "-askzaira=").encode('utf-8')).hexdigest()
