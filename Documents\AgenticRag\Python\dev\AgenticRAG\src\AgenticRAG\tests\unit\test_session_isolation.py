from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from uuid import uuid4, UUID
from userprofiles.ZairaUser import <PERSON>air<PERSON><PERSON><PERSON>, PERMISSION_LEVELS
from userprofiles.ZairaChat import ZairaChat, ChatSessionType
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
from endpoints.mybot_generic import MyBot_Generic
from tests.unit.test_logging_capture import with_unit_test_logging

class TestSessionIsolation:
    """Test session isolation functionality for scheduled requests"""
    
    @with_unit_test_logging
    def test_scheduled_request_creates_isolated_session(self):
        """Test that scheduled requests create isolated chat sessions"""
        # Create test user and mock bot
        user = <PERSON>airaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        # Mock bot for testing
        from unittest.mock import Mock
        mock_bot = Mock(spec=MyBot_Generic)
        mock_bot.name = "test_bot"
        
        # Create a scheduled request (with scheduled_guid provided)
        scheduled_guid = uuid4()
        scheduled_request = LongRunningZairaRequest(
            user=user,
            complete_message="Test scheduled request",
            calling_bot=mock_bot,
            original_message=None,
            scheduled_guid=scheduled_guid
        )
        
        # Verify that an isolated session was created
        assert scheduled_request.chat_session_guid != user.session_guid  # Should be different from active session
        assert scheduled_request.chat_session_guid in user.chat_history
        
        # Verify session properties
        scheduled_chat = user.chat_history[scheduled_request.chat_session_guid]
        assert scheduled_chat.session_type == ChatSessionType.SCHEDULED
        assert scheduled_chat.metadata['scheduled_guid'] == str(scheduled_guid)
        assert scheduled_chat.metadata['created_for'] == 'scheduled_request_isolation'
    
    @with_unit_test_logging
    def test_user_request_uses_active_session(self):
        """Test that user-initiated requests use the user's active session"""
        # Create test user and mock bot
        user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        # Mock bot for testing
        from unittest.mock import Mock
        mock_bot = Mock(spec=MyBot_Generic)
        mock_bot.name = "test_bot"
        
        # Store original active session GUID
        original_session_guid = user.session_guid
        
        # Create a user request (no scheduled_guid provided)
        user_request = LongRunningZairaRequest(
            user=user,
            complete_message="Test user request",
            calling_bot=mock_bot,
            original_message=None,
            scheduled_guid=None
        )
        
        # Verify that it uses the user's active session
        assert user_request.chat_session_guid == original_session_guid
        assert user_request.chat_session_guid == user.session_guid
    
    @with_unit_test_logging
    def test_different_scheduled_requests_get_different_sessions(self):
        """Test that different scheduled requests get different isolated sessions"""
        # Create test user and mock bot
        user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        # Mock bot for testing
        from unittest.mock import Mock
        mock_bot = Mock(spec=MyBot_Generic)
        mock_bot.name = "test_bot"
        
        # Test different scheduled request GUIDs
        scheduled_guid_1 = uuid4()
        scheduled_guid_2 = uuid4()
        
        # Create two different scheduled requests
        scheduled_request_1 = LongRunningZairaRequest(
            user=user,
            complete_message="First scheduled request",
            calling_bot=mock_bot,
            original_message=None,
            scheduled_guid=scheduled_guid_1
        )
        
        scheduled_request_2 = LongRunningZairaRequest(
            user=user,
            complete_message="Second scheduled request", 
            calling_bot=mock_bot,
            original_message=None,
            scheduled_guid=scheduled_guid_2
        )
        
        # Should be different sessions
        assert scheduled_request_1.chat_session_guid != scheduled_request_2.chat_session_guid
        
        # Both sessions should exist
        assert scheduled_request_1.chat_session_guid in user.chat_history
        assert scheduled_request_2.chat_session_guid in user.chat_history
        
        # Both should be scheduled type
        chat_1 = user.chat_history[scheduled_request_1.chat_session_guid]
        chat_2 = user.chat_history[scheduled_request_2.chat_session_guid]
        assert chat_1.session_type == ChatSessionType.SCHEDULED
        assert chat_2.session_type == ChatSessionType.SCHEDULED
        
        # Each should be linked to its respective scheduled request
        assert chat_1.metadata['scheduled_guid'] == str(scheduled_guid_1)
        assert chat_2.metadata['scheduled_guid'] == str(scheduled_guid_2)
    
    @with_unit_test_logging
    def test_isolated_sessions_dont_affect_active_session(self):
        """Test that isolated sessions don't affect the user's active session"""
        # Create test user
        user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        # Store original active session
        original_session_guid = user.session_guid
        
        # Create scheduled request which should create isolated session
        from unittest.mock import Mock
        mock_bot = Mock(spec=MyBot_Generic)
        mock_bot.name = "test_bot"
        
        scheduled_guid = uuid4()
        scheduled_request = LongRunningZairaRequest(
            user=user,
            complete_message="Test scheduled request",
            calling_bot=mock_bot,
            original_message=None,
            scheduled_guid=scheduled_guid
        )
        
        # Active session should remain unchanged
        assert user.session_guid == original_session_guid
        assert user.session_guid != scheduled_request.chat_session_guid
        
        # Active session should still be STANDARD type
        active_session = user.chat_history[user.session_guid]
        assert active_session.session_type == ChatSessionType.STANDARD
        
        # Isolated session should be SCHEDULED type
        isolated_session = user.chat_history[scheduled_request.chat_session_guid]
        assert isolated_session.session_type == ChatSessionType.SCHEDULED