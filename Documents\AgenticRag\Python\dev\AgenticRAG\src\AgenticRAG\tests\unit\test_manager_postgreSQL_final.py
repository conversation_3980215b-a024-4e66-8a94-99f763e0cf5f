"""
Comprehensive unit tests for PostgreSQL Manager with 100% code coverage
Cleaned and organized following CLAUDE.md standards
"""
import pytest
import asyncio
import signal
import os
from unittest.mock import AsyncMock, MagicMock, patch, call
from asyncpg import CannotConnectNowError
from socket import gaier<PERSON>r
from threading import Lock

# Add the path to imports
import sys
from os import path as os_path
sys.path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

from imports import *

class TestPostgreSQLManager:
    """Comprehensive tests for PostgreSQL Manager with 100% coverage"""
    
    def setup_method(self):
        """Reset singleton for each test"""
        from managers.manager_postgreSQL import PostgreSQLManager
        PostgreSQLManager._instance = None
        PostgreSQLManager._initialized = False
        if hasattr(PostgreSQLManager, "_exit_hooks_registered"):
            PostgreSQLManager._exit_hooks_registered = False
    
    def teardown_method(self):
        """Clean up after each test"""
        from managers.manager_postgreSQL import PostgreSQLManager
        PostgreSQLManager._instance = None
        PostgreSQLManager._initialized = False
        if hasattr(PostgreSQLManager, "_exit_hooks_registered"):
            PostgreSQLManager._exit_hooks_registered = False

    # ===== CORE FUNCTIONALITY =====
    
    def test_singleton_pattern(self):
        """Test singleton pattern implementation"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        instance1 = PostgreSQLManager()
        instance2 = PostgreSQLManager()
        instance3 = PostgreSQLManager.get_instance()
        
        assert instance1 is instance2 is instance3
        
        # Verify instance attributes are properly initialized
        assert hasattr(instance1, '_connections')
        assert hasattr(instance1, '_pools')
        assert hasattr(instance1, '_connection_modes')
        assert hasattr(instance1, '_lock')
        assert hasattr(instance1, '_async_lock')
        assert isinstance(instance1._connections, dict)
        assert isinstance(instance1._pools, dict)
        assert isinstance(instance1._connection_modes, dict)
        assert isinstance(instance1._lock, type(Lock()))
        assert isinstance(instance1._async_lock, type(asyncio.Lock()))
    
    @pytest.mark.asyncio
    async def test_setup_initialization(self):
        """Test setup method with initialization logic"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch.object(PostgreSQLManager, '_register_exit_hooks') as mock_hooks:
            # First call should initialize
            await PostgreSQLManager.setup()
            mock_hooks.assert_called_once()
            assert PostgreSQLManager._initialized is True
            
            # Second call should not reinitialize
            await PostgreSQLManager.setup()
            mock_hooks.assert_called_once()  # Still only called once
    
    def test_environment_configuration(self):
        """Test environment variable configuration and Docker detection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Test default values
        manager = PostgreSQLManager.get_instance()
        assert manager.user == "userzairaask"
        assert manager.password == "wordzairap4ss"
        assert manager.port == 5432
        
        # Test with environment variables
        with patch.dict(os.environ, {'POSTGRES_HOST': 'test-host', 'POSTGRES_PORT': '5433'}):
            PostgreSQLManager._instance = None
            new_manager = PostgreSQLManager.get_instance()
            assert new_manager.host == 'test-host'
            assert new_manager.port == 5433
        
        # Test Docker environment detection
        with patch('imports.Globals.is_docker') as mock_is_docker:
            mock_is_docker.return_value = True
            PostgreSQLManager._instance = None
            docker_manager = PostgreSQLManager.get_instance()
            assert docker_manager.host == "postgres"

    # ===== DATABASE OPERATIONS =====
    
    @pytest.mark.asyncio
    async def test_create_database_scenarios(self):
        """Test database creation with various scenarios"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = AsyncMock()
            mock_connect.return_value = mock_conn
            
            # Test successful creation
            mock_conn.fetch.return_value = []
            await PostgreSQLManager.create_database("testdb")
            mock_conn.execute.assert_called_with("CREATE DATABASE testdb;")
            
            # Test database already exists
            mock_conn.fetch.return_value = [{'datname': 'testdb'}]
            mock_conn.execute.reset_mock()
            await PostgreSQLManager.create_database("testdb")
            mock_conn.execute.assert_not_called()
            
            # Test error handling
            mock_connect.side_effect = Exception("Connection failed")
            with pytest.raises(Exception, match="Connection failed"):
                await PostgreSQLManager.create_database("testdb")
    
    @pytest.mark.asyncio
    async def test_delete_database_scenarios(self):
        """Test database deletion with various scenarios"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = AsyncMock()
            mock_connect.return_value = mock_conn
            
            # Test successful deletion
            mock_conn.fetch.side_effect = [
                [{'datname': 'testdb'}],  # Database exists
                []  # No active connections
            ]
            await PostgreSQLManager.delete_database("testdb")
            mock_conn.execute.assert_called_with("DROP DATABASE testdb;")
            
            # Test database doesn't exist
            mock_conn.fetch.side_effect = [[]]
            mock_conn.execute.reset_mock()
            await PostgreSQLManager.delete_database("testdb")
            mock_conn.execute.assert_not_called()
            
            # Test with active connections
            mock_conn.fetch.side_effect = [
                [{'datname': 'testdb'}],
                [{'pid': 123}]  # Active connections
            ]
            mock_conn.execute.reset_mock()
            await PostgreSQLManager.delete_database("testdb")
            mock_conn.execute.assert_not_called()
            
            # Test error handling (should not raise)
            mock_connect.side_effect = Exception("Connection failed")
            await PostgreSQLManager.delete_database("testdb")  # Should not raise

    # ===== CONNECTION MANAGEMENT =====
    
    @pytest.mark.asyncio
    async def test_connect_to_database_modes(self):
        """Test connection creation with pool and direct modes"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Test pool creation
        with patch('asyncpg.create_pool', new_callable=AsyncMock) as mock_create_pool:
            mock_pool = MagicMock()
            mock_create_pool.return_value = mock_pool
            
            result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=True, min_size=5, max_size=20)
            assert result is mock_pool
            
            call_args = mock_create_pool.call_args[1]
            assert call_args['database'] == "vectordb"
            assert call_args['user'] == "userzairaask"
            assert call_args['min_size'] == 5
            assert call_args['max_size'] == 20
        
        # Test direct connection
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            
            result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=False)
            assert result is mock_conn
            
            call_args = mock_connect.call_args[1]
            assert call_args['database'] == "vectordb"
            assert call_args['user'] == "userzairaask"
        
        # Test existing connection reuse
        mock_existing_conn = MagicMock()
        PostgreSQLManager._safe_set_connection("vectordb", mock_existing_conn, False)
        
        result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=False)
        assert result is mock_existing_conn
    
    @pytest.mark.asyncio
    async def test_connect_retry_logic(self):
        """Test connection retry logic with exponential backoff"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            with patch('asyncio.sleep', new_callable=AsyncMock) as mock_sleep:
                mock_conn = MagicMock()
                
                # Test retry with different error types
                for error_type in [CannotConnectNowError, ConnectionRefusedError, gaierror, OSError]:
                    mock_connect.side_effect = [error_type("Connection failed"), mock_conn]
                    
                    result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=False, retries=3, delay=2)
                    assert result is mock_conn
                
                # Test exponential backoff
                mock_connect.side_effect = [
                    CannotConnectNowError("Failed"),
                    CannotConnectNowError("Failed"),
                    mock_conn
                ]
                
                await PostgreSQLManager.connect_to_database("vectordb", use_pool=False, retries=3, delay=2)
                expected_calls = [call(2), call(4)]  # 2 * 2^0, 2 * 2^1
                mock_sleep.assert_has_calls(expected_calls)
                
                # Test max retries exceeded
                mock_connect.side_effect = CannotConnectNowError("Always fails")
                with pytest.raises(RuntimeError, match="Database 'vectordb' not ready after 3 attempts"):
                    await PostgreSQLManager.connect_to_database("vectordb", use_pool=False, retries=3)

    # ===== QUERY EXECUTION =====
    
    @pytest.mark.asyncio
    async def test_execute_query_comprehensive(self):
        """Test query execution with all scenarios"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Test with pool
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = [{"id": 1}]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test", [1, 'param'])
        assert result == [{"id": 1}]
        mock_conn.fetch.assert_called_with("SELECT * FROM test", 1, 'param')
        
        # Test with direct connection
        mock_direct_conn = MagicMock()
        mock_direct_conn.fetch.return_value = [{"id": 2}]
        PostgreSQLManager._safe_set_connection("vectordb", mock_direct_conn, False)
        
        result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
        assert result == [{"id": 2}]
        
        # Test pool recreation scenarios
        mock_closed_pool = MagicMock()
        mock_closed_pool._closed = True
        PostgreSQLManager._safe_set_connection("vectordb", mock_closed_pool, True)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_new_pool = MagicMock()
            mock_new_pool._closed = False
            mock_new_conn = MagicMock()
            mock_new_pool.acquire.return_value.__aenter__.return_value = mock_new_conn
            mock_new_conn.fetch.return_value = [{"id": 3}]
            mock_connect.return_value = mock_new_pool
            
            result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
            assert result == [{"id": 3}]
            mock_connect.assert_called_with("vectordb", use_pool=True, min_size=1, max_size=5)
        
        # Test health check failure and recovery
        mock_unhealthy_pool = MagicMock()
        mock_unhealthy_pool._closed = False
        mock_unhealthy_conn = MagicMock()
        mock_unhealthy_pool.acquire.return_value.__aenter__.return_value = mock_unhealthy_conn
        mock_unhealthy_conn.fetch.side_effect = Exception("Health check failed")
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_unhealthy_pool, True)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_healthy_pool = MagicMock()
            mock_healthy_conn = MagicMock()
            mock_healthy_pool.acquire.return_value.__aenter__.return_value = mock_healthy_conn
            mock_healthy_conn.fetch.return_value = [{"id": 4}]
            mock_connect.return_value = mock_healthy_pool
            
            result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
            assert result == [{"id": 4}]
        
        # Test fallback to direct connection
        mock_failing_pool = MagicMock()
        mock_failing_pool._closed = False
        mock_failing_conn = MagicMock()
        mock_failing_pool.acquire.return_value.__aenter__.return_value = mock_failing_conn
        mock_failing_conn.fetch.side_effect = Exception("Pool operation failed")
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_failing_pool, True)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            with patch.object(PostgreSQLManager, 'close_connection', new_callable=AsyncMock) as mock_close:
                mock_fallback_conn = MagicMock()
                mock_fallback_conn.fetch.return_value = [{"id": 5}]
                mock_connect.return_value = mock_fallback_conn
                
                result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
                assert result == [{"id": 5}]
                mock_connect.assert_called_with("vectordb", use_pool=False)
                mock_close.assert_called_with("vectordb")
        
        # Test connection creation when none exists
        PostgreSQLManager._safe_set_connection("vectordb", None, False)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            with patch.object(PostgreSQLManager, 'close_connection', new_callable=AsyncMock) as mock_close:
                mock_new_conn = MagicMock()
                mock_new_conn.fetch.return_value = [{"id": 6}]
                mock_connect.return_value = mock_new_conn
                
                result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
                assert result == [{"id": 6}]
                mock_connect.assert_called_with("vectordb")
                mock_close.assert_called_with("vectordb")
        
        # Test various error scenarios
        test_cases = [
            ("Pool not found", True, None, "No pool found"),
            ("Connection not found", False, None, "No connection found"),
            ("Pool creation failed", True, None, "Cannot create pool"),
            ("Connection creation failed", False, None, "No connection found")
        ]
        
        for description, use_pool, connection, expected_error in test_cases:
            PostgreSQLManager._safe_set_connection("vectordb", connection, use_pool)
            
            if "creation failed" in description:
                with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
                    mock_connect.return_value = None
                    with pytest.raises(Exception, match=expected_error):
                        await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
            else:
                with pytest.raises(Exception, match=expected_error):
                    await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
    
    @pytest.mark.asyncio
    async def test_execute_non_query_scenarios(self):
        """Test non-query execution scenarios"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Test with pool
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        await PostgreSQLManager.execute_non_query("vectordb", "INSERT INTO test VALUES ($1, $2)", [1, 'test'])
        mock_conn.execute.assert_called_with("INSERT INTO test VALUES ($1, $2)", 1, 'test')
        
        # Test with direct connection
        mock_direct_conn = MagicMock()
        PostgreSQLManager._safe_set_connection("vectordb", mock_direct_conn, False)
        
        await PostgreSQLManager.execute_non_query("vectordb", "INSERT INTO test VALUES (1)")
        mock_direct_conn.execute.assert_called_with("INSERT INTO test VALUES (1)")
        
        # Test error scenarios
        PostgreSQLManager._safe_set_connection("vectordb", None, True)
        with pytest.raises(Exception, match="No pool found"):
            await PostgreSQLManager.execute_non_query("vectordb", "INSERT INTO test VALUES (1)")
        
        PostgreSQLManager._safe_set_connection("vectordb", None, False)
        with pytest.raises(Exception, match="No connection found"):
            await PostgreSQLManager.execute_non_query("vectordb", "INSERT INTO test VALUES (1)")

    # ===== TABLE OPERATIONS =====
    
    @pytest.mark.asyncio
    async def test_get_table_names_comprehensive(self):
        """Test table name retrieval with all scenarios"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Test with pool
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = [{"table_name": "test_table"}]
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        result = await PostgreSQLManager.get_table_names("vectordb")
        assert result == ["test_table"]
        
        # Test with direct connection and custom schema
        mock_direct_conn = MagicMock()
        mock_direct_conn.fetch.return_value = [{"table_name": "custom_table"}]
        PostgreSQLManager._safe_set_connection("vectordb", mock_direct_conn, False)
        
        result = await PostgreSQLManager.get_table_names("vectordb", "custom_schema")
        assert result == ["custom_table"]
        expected_query = "SELECT table_name FROM information_schema.tables WHERE table_schema = 'custom_schema';"
        mock_direct_conn.fetch.assert_called_with(expected_query)
        
        # Test error scenarios
        test_cases = [
            (True, None, "No pool found"),
            (False, None, "No connection found"),
            (False, "exception", "Database error")
        ]
        
        for use_pool, connection, expected_result in test_cases:
            if connection == "exception":
                mock_error_conn = MagicMock()
                mock_error_conn.fetch.side_effect = Exception("Database error")
                PostgreSQLManager._safe_set_connection("vectordb", mock_error_conn, use_pool)
            else:
                PostgreSQLManager._safe_set_connection("vectordb", connection, use_pool)
            
            result = await PostgreSQLManager.get_table_names("vectordb")
            assert result == []

    # ===== TRANSACTION MANAGEMENT =====
    
    @pytest.mark.asyncio
    async def test_transaction_operations(self):
        """Test transaction start, commit, and rollback"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Test with pool
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_pool.acquire.return_value = mock_conn
        mock_conn.transaction.return_value = mock_tx
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        # Test start transaction
        conn, tx = await PostgreSQLManager.start_transaction("vectordb")
        assert conn is mock_conn
        assert tx is mock_tx
        mock_tx.start.assert_called_once()
        
        # Test commit transaction
        await PostgreSQLManager.commit_transaction(mock_conn, mock_tx, "vectordb")
        mock_tx.commit.assert_called_once()
        mock_pool.release.assert_called_with(mock_conn)
        
        # Test rollback transaction
        await PostgreSQLManager.rollback_transaction(mock_conn, mock_tx, "vectordb")
        mock_tx.rollback.assert_called_once()
        
        # Test with direct connection
        mock_direct_conn = MagicMock()
        mock_direct_tx = MagicMock()
        mock_direct_conn.transaction.return_value = mock_direct_tx
        PostgreSQLManager._safe_set_connection("vectordb", mock_direct_conn, False)
        
        conn, tx = await PostgreSQLManager.start_transaction("vectordb")
        assert conn is mock_direct_conn
        assert tx is mock_direct_tx
        
        # Test commit/rollback without dbname (no pool release)
        await PostgreSQLManager.commit_transaction(mock_direct_conn, mock_direct_tx)
        await PostgreSQLManager.rollback_transaction(mock_direct_conn, mock_direct_tx)
        
        # Test error scenarios
        PostgreSQLManager._safe_set_connection("vectordb", None, True)
        with pytest.raises(Exception, match="No pool found"):
            await PostgreSQLManager.start_transaction("vectordb")
        
        PostgreSQLManager._safe_set_connection("vectordb", None, False)
        with pytest.raises(Exception, match="No connection found"):
            await PostgreSQLManager.start_transaction("vectordb")
    
    @pytest.mark.asyncio
    async def test_execute_many_operations(self):
        """Test bulk execution operations"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        params = [(1, "test1"), (2, "test2")]
        
        # Test with pool
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        await PostgreSQLManager.execute_many("vectordb", "INSERT INTO test VALUES ($1, $2)", params)
        mock_conn.executemany.assert_called_with("INSERT INTO test VALUES ($1, $2)", params)
        
        # Test with direct connection
        mock_direct_conn = MagicMock()
        PostgreSQLManager._safe_set_connection("vectordb", mock_direct_conn, False)
        
        await PostgreSQLManager.execute_many("vectordb", "INSERT INTO test VALUES ($1, $2)", params)
        mock_direct_conn.executemany.assert_called_with("INSERT INTO test VALUES ($1, $2)", params)
        
        # Test error scenarios
        PostgreSQLManager._safe_set_connection("vectordb", None, True)
        with pytest.raises(Exception, match="No pool found"):
            await PostgreSQLManager.execute_many("vectordb", "INSERT INTO test VALUES ($1, $2)", params)
        
        PostgreSQLManager._safe_set_connection("vectordb", None, False)
        with pytest.raises(Exception, match="No connection found"):
            await PostgreSQLManager.execute_many("vectordb", "INSERT INTO test VALUES ($1, $2)", params)

    # ===== CONNECTION LIFECYCLE =====
    
    @pytest.mark.asyncio
    async def test_reconnect_operations(self):
        """Test reconnection with different modes"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Test reconnect with pool mode
        PostgreSQLManager._safe_set_connection("vectordb", MagicMock(), True)
        
        with patch.object(PostgreSQLManager, 'close_connection', new_callable=AsyncMock) as mock_close:
            with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
                mock_new_pool = MagicMock()
                mock_connect.return_value = mock_new_pool
                
                result = await PostgreSQLManager.reconnect("vectordb", min_size=5, max_size=15)
                assert result is mock_new_pool
                mock_close.assert_called_with("vectordb")
                mock_connect.assert_called_with("vectordb", use_pool=True, min_size=5, max_size=15)
        
        # Test reconnect with direct mode
        PostgreSQLManager._safe_set_connection("vectordb", MagicMock(), False)
        
        with patch.object(PostgreSQLManager, 'close_connection', new_callable=AsyncMock) as mock_close:
            with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
                mock_new_conn = MagicMock()
                mock_connect.return_value = mock_new_conn
                
                result = await PostgreSQLManager.reconnect("vectordb")
                assert result is mock_new_conn
                mock_close.assert_called_with("vectordb")
                mock_connect.assert_called_with("vectordb", use_pool=False, min_size=1, max_size=10)
    
    @pytest.mark.asyncio
    async def test_test_connection_operations(self):
        """Test connection testing functionality"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Test with pool
        PostgreSQLManager._safe_set_connection("vectordb", MagicMock(), True)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_pool = MagicMock()
            mock_conn = MagicMock()
            mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetch.return_value = [{"?column?": 1}]
            mock_connect.return_value = mock_pool
            
            result = await PostgreSQLManager.test_connection("vectordb")
            assert result is True
            mock_connect.assert_called_with("vectordb", use_pool=True)
        
        # Test with direct connection
        PostgreSQLManager._safe_set_connection("vectordb", MagicMock(), False)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_conn.fetch.return_value = [{"?column?": 1}]
            mock_connect.return_value = mock_conn
            
            result = await PostgreSQLManager.test_connection("vectordb")
            assert result is True
            mock_connect.assert_called_with("vectordb")
        
        # Test connection failure
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_connect.side_effect = Exception("Connection failed")
            
            result = await PostgreSQLManager.test_connection("vectordb")
            assert result is False
    
    @pytest.mark.asyncio
    async def test_close_connection_operations(self):
        """Test connection closing with various scenarios"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Test closing pool
        mock_pool = MagicMock()
        mock_pool._closed = False
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        await PostgreSQLManager.close_connection("vectordb")
        mock_pool.close.assert_called_once()
        
        # Test closing direct connection
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = False
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        await PostgreSQLManager.close_connection("vectordb")
        mock_conn.close.assert_called_once()
        
        # Test already closed connections
        mock_closed_pool = MagicMock()
        mock_closed_pool._closed = True
        PostgreSQLManager._safe_set_connection("vectordb", mock_closed_pool, True)
        
        await PostgreSQLManager.close_connection("vectordb")
        mock_closed_pool.close.assert_not_called()
        
        mock_closed_conn = MagicMock()
        mock_closed_conn.is_closed.return_value = True
        PostgreSQLManager._safe_set_connection("vectordb", mock_closed_conn, False)
        
        await PostgreSQLManager.close_connection("vectordb")
        mock_closed_conn.close.assert_not_called()
        
        # Test error handling
        mock_error_conn = MagicMock()
        mock_error_conn.is_closed.return_value = False
        mock_error_conn.close.side_effect = RuntimeError("Event loop closed")
        PostgreSQLManager._safe_set_connection("vectordb", mock_error_conn, False)
        
        await PostgreSQLManager.close_connection("vectordb")  # Should not raise
        mock_error_conn.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_close_all_connections(self):
        """Test closing all connections with error handling"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Set up mixed connections with various error scenarios
        mock_pool = MagicMock()
        mock_pool._closed = False
        mock_pool.close.side_effect = AttributeError("No attribute")
        
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = False
        mock_conn.close.side_effect = OSError("OS Error")
        
        manager = PostgreSQLManager.get_instance()
        manager._safe_set_connection("vectordb", mock_pool, True)
        manager._safe_set_connection("meltanodb", mock_conn, False)
        
        # Should handle all errors gracefully
        await manager.close_all_connections()
        
        # Verify attempts were made
        mock_pool.close.assert_called_once()
        mock_conn.close.assert_called_once()
        
        # Verify dictionaries were cleared
        assert len(manager._connections) == 0
        assert len(manager._pools) == 0
        assert len(manager._connection_modes) == 0

    # ===== THREAD SAFETY =====
    
    def test_thread_safety_operations(self):
        """Test thread-safe operations"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Test connection mode operations
        assert PostgreSQLManager._safe_get_connection_mode("nonexistent") is False
        
        PostgreSQLManager._safe_set_connection_mode("vectordb", True)
        assert PostgreSQLManager._safe_get_connection_mode("vectordb") is True
        assert PostgreSQLManager._using_pool("vectordb") is True
        
        # Test connection operations
        mock_conn = MagicMock()
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        assert PostgreSQLManager._safe_get_connection("vectordb") is mock_conn
        
        # Test connection removal
        removed_conn, use_pool = PostgreSQLManager._safe_remove_connection("vectordb")
        assert removed_conn is mock_conn
        assert use_pool is False
        
        # Test closed connection detection
        mock_closed_pool = MagicMock()
        mock_closed_pool._closed = True
        PostgreSQLManager._safe_set_connection("vectordb", mock_closed_pool, True)
        
        result = PostgreSQLManager._safe_get_connection("vectordb")
        assert result is None
        assert PostgreSQLManager._safe_get_connection_mode("vectordb") is False
        
        mock_closed_conn = MagicMock()
        mock_closed_conn.is_closed.return_value = True
        PostgreSQLManager._safe_set_connection("vectordb", mock_closed_conn, False)
        
        result = PostgreSQLManager._safe_get_connection("vectordb")
        assert result is None
        
        # Test connections without expected attributes
        mock_no_attr_pool = MagicMock()
        del mock_no_attr_pool._closed
        PostgreSQLManager._safe_set_connection("vectordb", mock_no_attr_pool, True)
        
        result = PostgreSQLManager._safe_get_connection("vectordb")
        assert result is mock_no_attr_pool
        
        mock_no_attr_conn = MagicMock()
        del mock_no_attr_conn.is_closed
        PostgreSQLManager._safe_set_connection("vectordb", mock_no_attr_conn, False)
        
        result = PostgreSQLManager._safe_get_connection("vectordb")
        assert result is mock_no_attr_conn

    # ===== EXIT HOOKS AND CLEANUP =====
    
    def test_exit_hooks_registration(self):
        """Test exit hooks registration and signal handling"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('atexit.register') as mock_atexit:
            with patch('signal.signal') as mock_signal:
                PostgreSQLManager._register_exit_hooks()
                
                assert PostgreSQLManager._exit_hooks_registered is True
                mock_atexit.assert_called_once()
                assert mock_signal.call_count == 2  # SIGINT and SIGTERM
                
                # Test duplicate registration prevention
                PostgreSQLManager._register_exit_hooks()
                mock_atexit.assert_called_once()  # Still only called once
        
        # Test signal handler
        with patch('signal.signal') as mock_signal:
            with patch('sys.exit') as mock_exit:
                PostgreSQLManager._exit_hooks_registered = False
                PostgreSQLManager._register_exit_hooks()
                
                signal_handler = mock_signal.call_args_list[0][0][1]
                signal_handler(signal.SIGINT, None)
                mock_exit.assert_called_with(0)
    
    def test_sync_cleanup_scenarios(self):
        """Test sync cleanup with various scenarios"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Test no event loop scenario
        with patch('asyncio.get_event_loop') as mock_get_loop:
            mock_get_loop.side_effect = RuntimeError("No event loop")
            
            with patch('asyncio.new_event_loop') as mock_new_loop:
                with patch('asyncio.set_event_loop') as mock_set_loop:
                    mock_loop = MagicMock()
                    mock_new_loop.return_value = mock_loop
                    
                    with patch.object(PostgreSQLManager, 'close_all_connections', new_callable=AsyncMock):
                        import atexit
                        with patch('atexit.register') as mock_atexit:
                            PostgreSQLManager._exit_hooks_registered = False
                            PostgreSQLManager._register_exit_hooks()
                            
                            cleanup_func = mock_atexit.call_args[0][0]
                            cleanup_func()
                            
                            mock_new_loop.assert_called_once()
                            mock_set_loop.assert_called_once_with(mock_loop)
                            mock_loop.run_until_complete.assert_called_once()
                            mock_loop.close.assert_called_once()
        
        # Test running event loop scenario
        with patch('asyncio.get_event_loop') as mock_get_loop:
            mock_loop = MagicMock()
            mock_loop.is_running.return_value = True
            mock_get_loop.return_value = mock_loop
            
            import atexit
            with patch('atexit.register') as mock_atexit:
                PostgreSQLManager._exit_hooks_registered = False
                PostgreSQLManager._register_exit_hooks()
                
                cleanup_func = mock_atexit.call_args[0][0]
                cleanup_func()
                
                mock_loop.run_until_complete.assert_not_called()
        
        # Test various exception scenarios
        exception_types = [RuntimeError, AttributeError, OSError]
        
        for exception_type in exception_types:
            with patch('asyncio.get_event_loop') as mock_get_loop:
                mock_get_loop.side_effect = RuntimeError("No event loop")
                
                with patch('asyncio.new_event_loop') as mock_new_loop:
                    mock_loop = MagicMock()
                    mock_loop.run_until_complete.side_effect = exception_type("Error")
                    mock_new_loop.return_value = mock_loop
                    
                    import atexit
                    with patch('atexit.register') as mock_atexit:
                        PostgreSQLManager._exit_hooks_registered = False
                        PostgreSQLManager._register_exit_hooks()
                        
                        cleanup_func = mock_atexit.call_args[0][0]
                        cleanup_func()  # Should handle exception gracefully

    # ===== ASYNC LOCK OPERATIONS =====
    
    @pytest.mark.asyncio
    async def test_async_lock_usage(self):
        """Test async lock usage in connection operations"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        
        # Test connect_to_database async lock
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            
            mock_async_lock = AsyncMock()
            mock_async_lock.__aenter__ = AsyncMock()
            mock_async_lock.__aexit__ = AsyncMock()
            manager._async_lock = mock_async_lock
            
            result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=False)
            
            assert result is mock_conn
            mock_async_lock.__aenter__.assert_called_once()
            mock_async_lock.__aexit__.assert_called_once()
        
        # Test close_connection async lock
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = False
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        mock_async_lock = AsyncMock()
        mock_async_lock.__aenter__ = AsyncMock()
        mock_async_lock.__aexit__ = AsyncMock()
        manager._async_lock = mock_async_lock
        
        await PostgreSQLManager.close_connection("vectordb")
        
        mock_async_lock.__aenter__.assert_called_once()
        mock_async_lock.__aexit__.assert_called_once()
        mock_conn.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_connection_async_compatibility(self):
        """Test async compatibility of get_connection method"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        # Test that async get_connection works
        result = await PostgreSQLManager.get_connection("vectordb")
        assert result is mock_conn
        
        # Test that sync get_connection also works
        result = PostgreSQLManager.get_connection("vectordb")
        assert result is mock_conn