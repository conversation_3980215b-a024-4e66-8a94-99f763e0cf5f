#!/usr/bin/env python3
"""
Test script to verify the startup hang fix.
This script tests the modified initialization order.
"""

import asyncio
import sys
import os
import time

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def log_with_timestamp(message: str):
    """Log message with precise timestamp"""
    timestamp = time.strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] TEST_FIX: {message}")
    sys.stdout.flush()

async def test_startup_fix():
    """Test the startup process with the fix"""
    log_with_timestamp("=== TESTING STARTUP FIX ===")
    
    try:
        # Basic setup
        log_with_timestamp("Setting up basic environment...")
        from imports import *
        await LogFire.setup()
        ZairaSettings.IsDebugMode = True
        Globals.set_debug(ZairaSettings.IsDebugMode)
        log_with_timestamp("✅ Basic environment setup complete")
        
        # Test the fixed late_init process
        log_with_timestamp("Testing fixed late_init process...")
        await test_fixed_late_init()
        log_with_timestamp("✅ Fixed late_init process completed successfully!")
        
        log_with_timestamp("=== STARTUP FIX TEST COMPLETED SUCCESSFULLY ===")
        
    except Exception as e:
        log_with_timestamp(f"❌ ERROR in startup fix test: {str(e)}")
        import traceback
        log_with_timestamp(f"❌ TRACEBACK: {traceback.format_exc()}")
        raise

async def test_fixed_late_init():
    """Test the fixed late_init process"""
    try:
        # Setup minimal required managers first
        log_with_timestamp("Setting up minimal managers...")
        await setup_minimal_managers_for_test()
        log_with_timestamp("✅ Minimal managers setup complete")
        
        # Test supervisor creation in the new order
        log_with_timestamp("Testing supervisor creation in new order...")
        
        # Step 1: Create output supervisor first
        log_with_timestamp("Step 1: Creating top output supervisor...")
        from tasks.task_top_output_supervisor import create_top_output_supervisor
        output_supervisor = await asyncio.wait_for(create_top_output_supervisor(), timeout=30.0)
        log_with_timestamp("✅ Step 1: Top output supervisor created successfully")
        
        # Step 2: Create top level supervisor (should now find existing output supervisor)
        log_with_timestamp("Step 2: Creating top level supervisor...")
        from tasks.task_top_level_supervisor import create_top_level_supervisor
        top_supervisor = await asyncio.wait_for(create_top_level_supervisor(), timeout=30.0)
        log_with_timestamp("✅ Step 2: Top level supervisor created successfully")
        
        log_with_timestamp("✅ Both supervisors created without hanging!")
        
    except asyncio.TimeoutError:
        log_with_timestamp("❌ TIMEOUT: Supervisor creation still hanging after fix")
        raise
    except Exception as e:
        log_with_timestamp(f"❌ ERROR in fixed late_init test: {str(e)}")
        raise

async def setup_minimal_managers_for_test():
    """Setup minimal managers needed for supervisor testing"""
    try:
        from managers.manager_supervisors import SupervisorManager
        from managers.manager_users import ZairaUserManager
        from managers.manager_prompts import PromptManager
        from managers.manager_postgreSQL import PostgreSQLManager
        from managers.manager_qdrant import QDrantManager
        from managers.manager_retrieval import RetrievalManager
        
        # Database setup
        log_with_timestamp("Setting up databases...")
        await PostgreSQLManager.create_database("vectordb")
        await PostgreSQLManager.connect_to_database("vectordb", use_pool=True, min_size=2, max_size=10)
        
        # Vector database
        log_with_timestamp("Setting up QDrant...")
        await QDrantManager.setup()
        
        # Retrieval manager
        log_with_timestamp("Setting up RetrievalManager...")
        await RetrievalManager.setup()
        
        # Supervisor manager
        log_with_timestamp("Setting up SupervisorManager...")
        await SupervisorManager.setup()
        
        # Prompt manager
        log_with_timestamp("Setting up PromptManager...")
        await PromptManager.loadDefaultPrompts()
        
        # Create test user
        log_with_timestamp("Creating test user...")
        from userprofiles.permission_levels import PERMISSION_LEVELS
        test_user = await ZairaUserManager.add_user("TestUser", PERMISSION_LEVELS.ADMIN, 
                                                   ZairaUserManager.create_guid(), 
                                                   ZairaUserManager.create_guid())
        
        # Set up a minimal index
        log_with_timestamp("Setting up minimal index...")
        from pathlib import Path
        DATA_DIR = Path("assets/documents")
        PERSIST_DIR = Path("data")
        
        from etc.parsers import create_parsers
        parsers = create_parsers()
        
        from etc.setup import loadEmbedding
        index = await loadEmbedding(DATA_DIR=DATA_DIR, PERSIST_DIR=PERSIST_DIR, parsers=parsers)
        Globals.set_index(index)
        
        log_with_timestamp("✅ Minimal managers setup complete")
        
    except Exception as e:
        log_with_timestamp(f"❌ ERROR in minimal manager setup: {str(e)}")
        raise

async def test_individual_supervisor_creation():
    """Test creating supervisors individually to ensure they work"""
    log_with_timestamp("=== TESTING INDIVIDUAL SUPERVISOR CREATION ===")
    
    try:
        # Setup minimal environment
        await setup_minimal_managers_for_test()
        
        # Test 1: Create output supervisor
        log_with_timestamp("Test 1: Creating output supervisor individually...")
        from tasks.task_top_output_supervisor import create_top_output_supervisor
        output_supervisor = await asyncio.wait_for(create_top_output_supervisor(), timeout=15.0)
        log_with_timestamp("✅ Test 1: Output supervisor created successfully")
        
        # Test 2: Create top level supervisor
        log_with_timestamp("Test 2: Creating top level supervisor individually...")
        from tasks.task_top_level_supervisor import create_top_level_supervisor
        top_supervisor = await asyncio.wait_for(create_top_level_supervisor(), timeout=15.0)
        log_with_timestamp("✅ Test 2: Top level supervisor created successfully")
        
        log_with_timestamp("✅ Individual supervisor creation test passed!")
        
    except asyncio.TimeoutError:
        log_with_timestamp("❌ Individual supervisor creation test timed out")
        raise
    except Exception as e:
        log_with_timestamp(f"❌ Individual supervisor creation test failed: {str(e)}")
        raise

if __name__ == "__main__":
    log_with_timestamp("Starting startup fix test...")
    try:
        # Test the fix
        asyncio.run(test_startup_fix())
        
        # Also test individual creation
        log_with_timestamp("\nRunning individual supervisor creation test...")
        asyncio.run(test_individual_supervisor_creation())
        
        log_with_timestamp("All tests completed successfully!")
        
    except KeyboardInterrupt:
        log_with_timestamp("Test interrupted by user")
    except Exception as e:
        log_with_timestamp(f"Test failed: {str(e)}")
        sys.exit(1)
