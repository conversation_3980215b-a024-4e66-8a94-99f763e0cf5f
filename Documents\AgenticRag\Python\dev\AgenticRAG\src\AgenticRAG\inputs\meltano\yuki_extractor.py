#!/usr/bin/env python3
"""Singer tap for extracting payment data from <PERSON><PERSON>."""

from __future__ import annotations

import sys
from typing import Any, Dict, Iterable, List, Optional
from datetime import datetime
import requests
from zeep import Client
from zeep.transports import Transport
from zeep.wsse.username import UsernameToken

from singer_sdk import Tap, Stream
from singer_sdk import typing as th
from singer_sdk.authenticators import SimpleAuthenticator
from singer_sdk.exceptions import FatalAPIError, RetriableAPIError


class YukiStream(Stream):
    """Base stream class for Yuki API."""
    
    def __init__(self, tap: Tap):
        super().__init__(tap)
        self._client = None
        
    @property
    def client(self):
        """Get or create SOAP client."""
        if self._client is None:
            # Setup transport with authentication
            session = requests.Session()
            transport = Transport(session=session)
            
            # Create SOAP client for AccountingInfo service
            wsdl_url = "https://api.yukiworks.be/ws/AccountingInfo.asmx?WSDL"
            self._client = Client(wsdl_url, transport=transport)
            
            # Add WS-Security token if username/password provided
            if self.config.get("api_key"):
                # For Yuki, the API key is used differently - typically in the method calls
                pass
                
        return self._client
    
    def get_records(self, context: Optional[dict]) -> Iterable[dict]:
        """Return a generator of row-type dictionary objects."""
        raise NotImplementedError("Streams must implement get_records")


class PaymentsStream(YukiStream):
    """Stream for extracting payment data from Yuki."""
    
    name = "payments"
    primary_keys = ["payment_id"]
    replication_key = "modified_date"
    
    schema = th.PropertiesList(
        th.Property("payment_id", th.StringType, description="Unique payment identifier"),
        th.Property("invoice_id", th.StringType, description="Related invoice ID"),
        th.Property("customer_id", th.StringType, description="Customer identifier"),
        th.Property("payment_date", th.DateTimeType, description="Date of payment"),
        th.Property("amount", th.NumberType, description="Payment amount"),
        th.Property("currency", th.StringType, description="Payment currency"),
        th.Property("payment_method", th.StringType, description="Method of payment"),
        th.Property("status", th.StringType, description="Payment status"),
        th.Property("reference", th.StringType, description="Payment reference"),
        th.Property("description", th.StringType, description="Payment description"),
        th.Property("created_date", th.DateTimeType, description="Record creation date"),
        th.Property("modified_date", th.DateTimeType, description="Last modification date"),
    ).to_dict()
    
    def get_records(self, context: Optional[dict]) -> Iterable[dict]:
        """Fetch payment records from Yuki API."""
        try:
            # Authenticate with Yuki API
            session_id = self._authenticate()
            
            # Get domains/administrations
            domains = self._get_domains(session_id)
            
            for domain in domains:
                # Fetch payments for each domain
                payments = self._get_payments_for_domain(session_id, domain['domain_id'])
                
                for payment in payments:
                    # Transform the payment data to match our schema
                    yield self._transform_payment_record(payment)
                    
        except Exception as e:
            self.logger.error(f"Error fetching payments: {str(e)}")
            raise RetriableAPIError(f"Failed to fetch payments: {str(e)}")
    
    def _authenticate(self) -> str:
        """Authenticate with Yuki API and return session ID."""
        try:
            # Call authentication method
            response = self.client.service.Authenticate(
                accessKey=self.config["api_key"],
                administrationID=self.config.get("administration_id")
            )
            
            if response and hasattr(response, 'SessionID'):
                return response.SessionID
            else:
                raise FatalAPIError("Authentication failed - no session ID returned")
                
        except Exception as e:
            self.logger.error(f"Authentication error: {str(e)}")
            raise FatalAPIError(f"Failed to authenticate with Yuki API: {str(e)}")
    
    def _get_domains(self, session_id: str) -> List[Dict[str, Any]]:
        """Get list of domains/administrations."""
        try:
            response = self.client.service.Domains(sessionID=session_id)
            
            domains = []
            if response and hasattr(response, 'Domain'):
                for domain in response.Domain:
                    domains.append({
                        'domain_id': domain.DomainID,
                        'name': domain.Name
                    })
            
            return domains
            
        except Exception as e:
            self.logger.error(f"Error fetching domains: {str(e)}")
            return []
    
    def _get_payments_for_domain(self, session_id: str, domain_id: str) -> List[Dict[str, Any]]:
        """Fetch payments for a specific domain."""
        try:
            # Note: The exact method name may vary - this is based on common patterns
            # You may need to adjust based on actual Yuki API documentation
            
            # Try to get transaction/payment data
            # This might be through ProcessedInvoices, Transactions, or similar endpoint
            response = self.client.service.ProcessedInvoices(
                sessionID=session_id,
                domainID=domain_id,
                # Add date range parameters if needed
                startDate=self.config.get("start_date"),
                endDate=self.config.get("end_date")
            )
            
            payments = []
            if response and hasattr(response, 'Invoice'):
                for invoice in response.Invoice:
                    # Extract payment information from invoices
                    if hasattr(invoice, 'Payments') and invoice.Payments:
                        for payment in invoice.Payments.Payment:
                            payments.append({
                                'payment_id': payment.PaymentID,
                                'invoice_id': invoice.InvoiceID,
                                'customer_id': invoice.CustomerID,
                                'payment_date': payment.PaymentDate,
                                'amount': float(payment.Amount),
                                'currency': payment.Currency or 'EUR',
                                'payment_method': payment.PaymentMethod,
                                'status': payment.Status,
                                'reference': payment.Reference,
                                'description': payment.Description
                            })
            
            return payments
            
        except Exception as e:
            self.logger.error(f"Error fetching payments for domain {domain_id}: {str(e)}")
            return []
    
    def _transform_payment_record(self, payment: Dict[str, Any]) -> Dict[str, Any]:
        """Transform payment record to match the stream schema."""
        # Ensure dates are properly formatted
        payment_date = payment.get('payment_date')
        if payment_date and isinstance(payment_date, str):
            payment_date = datetime.fromisoformat(payment_date.replace('Z', '+00:00'))
        
        return {
            'payment_id': payment.get('payment_id'),
            'invoice_id': payment.get('invoice_id'),
            'customer_id': payment.get('customer_id'),
            'payment_date': payment_date.isoformat() if payment_date else None,
            'amount': payment.get('amount'),
            'currency': payment.get('currency', 'EUR'),
            'payment_method': payment.get('payment_method'),
            'status': payment.get('status', 'completed'),
            'reference': payment.get('reference'),
            'description': payment.get('description'),
            'created_date': datetime.now().isoformat(),
            'modified_date': datetime.now().isoformat()
        }


class TapYuki(Tap):
    """Singer tap for Yuki."""
    
    name = "tap-yuki"
    
    config_jsonschema = th.PropertiesList(
        th.Property(
            "api_key",
            th.StringType,
            required=True,
            description="The Yuki API key (WebserviceAccessKey)",
            secret=True
        ),
        th.Property(
            "administration_id",
            th.StringType,
            required=False,
            description="The administration ID for Yuki"
        ),
        th.Property(
            "start_date",
            th.DateTimeType,
            description="Start date for data extraction"
        ),
        th.Property(
            "end_date",
            th.DateTimeType,
            description="End date for data extraction"
        ),
        th.Property(
            "api_url",
            th.StringType,
            default="https://api.yukiworks.be/ws/AccountingInfo.asmx",
            description="The base URL for the Yuki API"
        ),
    ).to_dict()
    
    def discover_streams(self) -> List[Stream]:
        """Return a list of discovered streams."""
        return [
            PaymentsStream(self),
        ]


if __name__ == "__main__":
    TapYuki.cli()