from imports import *

from langchain_core.tools import tool
import logging
from typing import Optional, Any
from datetime import datetime, timedelta
import json
from langchain_core.tools import BaseTool
from pydantic import BaseModel, Field

from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTask_Create_agent, SupervisorTaskState, SupervisorTask_Base
from managers.manager_users import ZairaUserManager
from tasks.data import AgendaProcessingData

class AgendaPlannerTool(BaseTool):
    """Tool for planning and generating agenda content"""
    name: str = "agenda_planner_tool"
    description: str = "Plans and generates agenda content, schedules, and event details"
    
    def _run(self, request: str, start_date: str = None, end_date: str = None, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, request: str, start_date: str = None, end_date: str = None, state: SupervisorTaskState = None) -> str:
        """Generate agenda content and planning information"""
        try:
            # Handle case where state might be passed as dict
            if isinstance(state, dict):
                from managers.manager_supervisors import SupervisorTaskState
                state = SupervisorTaskState(**state)
            # Import calendar tools from output task
            from tasks.outputs.output_tasks.task_out_agenda import get_calendar_tools
            
            # Use available calendar tools for planning
            calendar_tools = await get_calendar_tools()
            
            # Generate agenda content based on request
            agenda_content = await self._generate_agenda_content(request, start_date, end_date, state)
            
            # Parse dates and create structured agenda data
            from datetime import datetime, timedelta
            
            # Default to next hour if no start_date provided
            start_dt = datetime.now() + timedelta(hours=1)
            end_dt = start_dt + timedelta(hours=1)
            
            if start_date:
                try:
                    start_dt = datetime.fromisoformat(start_date.replace("Z", "+00:00"))
                except:
                    # If parsing fails, use default
                    pass
            
            if end_date:
                try:
                    end_dt = datetime.fromisoformat(end_date.replace("Z", "+00:00"))
                except:
                    # If parsing fails, use start + 1 hour
                    end_dt = start_dt + timedelta(hours=1)
            else:
                # Default end time is 1 hour after start
                end_dt = start_dt + timedelta(hours=1)
            
            # Extract summary from agenda content (first line or fallback)
            summary = request[:50] + "..." if len(request) > 50 else request
            
            # Create structured agenda data
            agenda_data = AgendaProcessingData(
                summary=summary,
                description=agenda_content,
                start_datetime=start_dt,
                end_datetime=end_dt,
                content_generated=True,
                schedule_planned=True,
                processing_metadata={
                    "request": request,
                    "tools_available": len(calendar_tools) > 0,
                    "generated_at": datetime.now().isoformat()
                }
            )
            
            user = await ZairaUserManager.get_instance().find_user(state.user_guid)
            # Get user's current chat session for logging
            chat_session = user.get_current_chat_session() if user else None
            
            # Add structured data to processing output
            user.my_requests[state.scheduled_guid].add_processing_output(agenda_data)
            LogFire.log("DEBUG", f"[AgendaPlannerTool] Added AgendaProcessingData to processing_data. Current demands: {user.my_requests[state.scheduled_guid].output_demands}", chat=chat_session)
            LogFire.log("DEBUG", f"[AgendaPlannerTool] After adding: {user.my_requests[state.scheduled_guid].output_demands}", chat=chat_session)
            
            return agenda_data.model_dump_json(indent=2)
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "agenda_planner_tool", state.user_guid if state else None)
            return json.dumps({
                "error": f"Failed to generate agenda: {str(e)}",
                "agenda_content": "Error occurred during agenda planning"
            })
    
    async def _generate_agenda_content(self, request: str, start_date: str = None, end_date: str = None, state: Optional[Any] = None) -> str:
        """Generate agenda content using LLM"""
        try:
            from langchain_openai import ChatOpenAI
            from langchain.prompts import ChatPromptTemplate
            
            # Create LLM instance
            llm = ChatOpenAI(temperature=0.7, model="gpt-3.5-turbo")
            
            # Create prompt for agenda generation
            prompt_template = ChatPromptTemplate.from_template("""
            You are an expert agenda planner and scheduler. Generate detailed agenda content based on the user's request.
            
            User Request: {request}
            Start Date: {start_date}
            End Date: {end_date}
            
            Generate a comprehensive agenda that includes:
            1. Event title/summary
            2. Detailed description
            3. Suggested time slots
            4. Location recommendations (if applicable)
            5. Agenda items or topics to cover
            6. Estimated duration
            
            Format the response as clear, actionable agenda content that can be used to create calendar events.
            Focus on being practical and detailed while maintaining clarity.
            """)
            
            # Create chain and invoke
            chain = prompt_template | llm
            
            result = await chain.ainvoke({
                "request": request,
                "start_date": start_date or "Not specified",
                "end_date": end_date or "Not specified"
            })
            
            return result.content if hasattr(result, 'content') else str(result)
            
        except Exception as e:
            logging.error(f"Error in agenda content generation: {e}")
            return f"Error generating agenda content: {str(e)}"


class AgendaGeneratorTool(BaseTool):
    """Tool for generating specific calendar event details"""
    name: str = "agenda_generator_tool"
    description: str = "Generates specific calendar event details including times, locations, and descriptions"
    
    def _run(self, event_type: str, duration: str = "1 hour", preferences: str = "", state: Optional[Any] = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, event_type: str, duration: str = "1 hour", preferences: str = "", state: Optional[Any] = None) -> str:
        """Generate specific event details"""
        try:
            # Handle case where state might be passed as dict
            if isinstance(state, dict):
                from managers.manager_supervisors import SupervisorTaskState
                state = SupervisorTaskState(**state)
            event_details = await self._generate_event_details(event_type, duration, preferences, state)
            
            # Event details are returned directly, main agenda data is handled by AgendaPlannerTool
            
            return json.dumps(event_details)
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "agenda_generator_tool", state.user_guid if state else None)
            return json.dumps({
                "error": f"Failed to generate event details: {str(e)}"
            })
    
    async def _generate_event_details(self, event_type: str, duration: str, preferences: str, state: Optional[Any] = None) -> dict:
        """Generate detailed event information"""
        try:
            from langchain_openai import ChatOpenAI
            from langchain.prompts import ChatPromptTemplate
            
            # Create LLM instance
            llm = ChatOpenAI(temperature=0.5, model="gpt-3.5-turbo")
            
            # Create prompt for event details generation
            prompt_template = ChatPromptTemplate.from_template("""
            Generate detailed calendar event information for the following:
            
            Event Type: {event_type}
            Duration: {duration}
            User Preferences: {preferences}
            
            Provide a JSON-like structure with the following fields:
            - summary: Clear, concise event title
            - description: Detailed description of the event
            - location: Suggested location (if applicable)
            - agenda_items: List of key topics or activities
            - preparation_notes: What attendees should prepare
            - expected_outcomes: What should be accomplished
            
            Be specific and actionable in your suggestions.
            """)
            
            # Create chain and invoke
            chain = prompt_template | llm
            
            result = await chain.ainvoke({
                "event_type": event_type,
                "duration": duration,
                "preferences": preferences or "No specific preferences"
            })
            
            content = result.content if hasattr(result, 'content') else str(result)
            
            # Try to parse as JSON, fallback to structured text
            try:
                import json
                return json.loads(content)
            except:
                return {
                    "summary": f"{event_type} - {duration}",
                    "description": content,
                    "generated_content": content
                }
            
        except Exception as e:
            logging.error(f"Error generating event details: {e}")
            return {
                "summary": f"{event_type}",
                "description": f"Error generating details: {str(e)}",
                "error": str(e)
            }


@tool
async def agenda_planning_tool(request: str, timeframe: str = "this week", preferences: str = "", state: Optional[Any] = None) -> str:
    """
    Plan agenda items and schedules based on user requests
    
    Args:
        request: Description of what needs to be planned
        timeframe: When the agenda is for (e.g., "this week", "next month")
        preferences: Any specific preferences or constraints
        state: Supervisor state for context
    
    Returns:
        Detailed agenda planning content
    """
    try:
        planner = AgendaPlannerTool()
        result = await planner._arun(request, timeframe, preferences, state)
        return result
    except Exception as e:
        logging.error(f"Error in agenda_planning_tool: {e}")
        return json.dumps({
            "error": f"Agenda planning failed: {str(e)}"
        })


@tool 
async def event_generation_tool(event_type: str, duration: str = "1 hour", requirements: str = "", state: Optional[Any] = None) -> str:
    """
    Generate specific event details for calendar creation
    
    Args:
        event_type: Type of event to generate (meeting, workshop, etc.)
        duration: How long the event should be
        requirements: Any specific requirements or preferences
        state: Supervisor state for context
    
    Returns:
        JSON with detailed event information
    """
    try:
        generator = AgendaGeneratorTool()
        result = await generator._arun(event_type, duration, requirements, state)
        return result
    except Exception as e:
        logging.error(f"Error in event_generation_tool: {e}")
        return json.dumps({
            "error": f"Event generation failed: {str(e)}"
        })


async def create_supervisor_agenda_planner() -> SupervisorSupervisor:
    """Create the agenda planner supervisor with planning-focused tools"""
    class TaskCreator:
        agenda_task: SupervisorTask_Create_agent = None
        
        async def create_tasks(self):
            # Import output tools for integration
            from tasks.outputs.output_tasks.task_out_agenda import get_calendar_tools
            
            # Get calendar tools for comprehensive agenda management
            calendar_tools = await get_calendar_tools()
            
            # Add planning tools
            planning_tools = [
                AgendaPlannerTool(),
                AgendaGeneratorTool()
            ]
            
            # Combine planning and output tools
            all_tools = planning_tools + calendar_tools
            
            # Register the agenda task with planning and output capabilities
            self.agenda_task = SupervisorManager.register_task(SupervisorTask_Create_agent(
                name="agenda_expert", 
                tools=all_tools, 
                prompt="""You are an agenda and calendar expert with comprehensive planning and execution capabilities.

                You have access to two types of tools:
                
                PLANNING TOOLS:
                - AgendaPlannerTool: Plan and generate agenda content and schedules
                - AgendaGeneratorTool: Generate specific event details and requirements
                
                CALENDAR TOOLS (Google Calendar Toolkit):
                - CalendarCreateEvent: Create new calendar events (with automatic user confirmation)
                - CalendarSearchEvents: Search for existing events
                - CalendarUpdateEvent: Update existing events
                - GetCalendarsInfo: Get calendar information
                - CalendarMoveEvent: Move events between calendars
                - CalendarDeleteEvent: Delete events
                - GetCurrentDatetime: Get current date and time
                - CalculatorTool: Perform calculations
                
                WORKFLOW:
                1. Use planning tools to understand requirements and generate agenda content
                2. Use calendar tools to implement the planned agenda items
                3. Always show users a preview before creating events
                4. Confirm all details with users before finalizing
                
                IMPORTANT: When creating events with CalendarCreateEvent, the system will automatically show a preview 
                to the user and request confirmation before actually creating the event.
                
                Be thorough in planning and precise in execution. Always consider user preferences and constraints.
                """,
            ))

        async def create_supervisor(self) -> SupervisorSupervisor:
            return SupervisorManager.register_supervisor(SupervisorSupervisor(
                name="agenda_supervisor",
                prompt="""You are an agenda and calendar supervisor that manages comprehensive agenda planning and calendar operations.
                
                Always delegate to the agenda_expert for:
                - Planning agenda items and schedules
                - Generating event details and requirements  
                - Creating, updating, or managing calendar events
                - Searching and organizing calendar information
                
                Focus on understanding user needs and ensuring all agenda-related tasks are handled efficiently.
                """
            )) \
            .add_task(self.agenda_task, priority=1) \
            .compile()

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()