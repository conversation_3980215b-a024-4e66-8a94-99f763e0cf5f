# Email and Agenda Integration Tests

This folder contains integration tests for email and agenda functionality workflows.

## Test Files

### New Integration Tests
- `test_email_agenda_workflow_integration.py` - Cross-workflow coordination between email and agenda
- `test_email_sending_pipeline_integration.py` - Complete email generation to sending pipeline  
- `test_imap_email_processing_integration.py` - IMAP email retrieval and processing workflows
- `test_agenda_calendar_integration.py` - Agenda planning and calendar integration
- `test_real_email_agenda_coordination.py` - REAL execution test for email-agenda coordination

### Existing Email Integration Tests (Moved)
- `test_email_writing_call_trace.py` - Email writing workflow trace analysis
- `test_imap_email_send_receive.py` - IMAP send/receive integration
- `test_real_email_sending_execution.py` - REAL email sending execution test

## Test Categories

### Workflow Integration Tests
Test end-to-end workflows combining multiple components:

1. **Email-Agenda Coordination** (`test_email_agenda_workflow_integration.py`)
   - Basic coordination between email generator and agenda planner
   - Agenda event triggering email reminders
   - Cross-tool data sharing through state
   - Error handling coordination
   - State preservation across tools

2. **Email Pipeline** (`test_email_sending_pipeline_integration.py`)
   - Complete generation to sending pipeline
   - User rejection and editing workflows
   - Sending failure handling
   - Approval cancellation
   - Direct sending workflows
   - State consistency throughout pipeline

3. **IMAP Processing** (`test_imap_email_processing_integration.py`)
   - SSL and non-SSL IMAP connections
   - Login fallback mechanisms
   - Selective email saving workflows
   - Empty mailbox handling
   - Callback response state management

4. **Calendar Integration** (`test_agenda_calendar_integration.py`)
   - Supervisor creation and integration
   - OAuth2 system integration
   - Calendar tools creation patterns
   - Event preview formatting
   - Error handling integration

### REAL Execution Tests
Tests that use actual system components:

5. **Real Email-Agenda Coordination** (`test_real_email_agenda_coordination.py`)
   - Multiple workflow types (agenda_email, meeting_invitation, agenda_summary)
   - Performance analysis across workflows
   - Component validation in real execution
   - Response quality analysis
   - Comprehensive execution documentation

## Running Tests

Run all email and agenda integration tests:
```bash
../../.venv/Scripts/pytest.exe tests/integration/email_agenda/ -v
```

Run specific test categories:
```bash
# Workflow integration tests
../../.venv/Scripts/pytest.exe tests/integration/email_agenda/test_email_agenda_workflow_integration.py -v
../../.venv/Scripts/pytest.exe tests/integration/email_agenda/test_email_sending_pipeline_integration.py -v

# REAL execution tests (longer running)
../../.venv/Scripts/pytest.exe tests/integration/email_agenda/test_real_email_agenda_coordination.py -v
../../.venv/Scripts/pytest.exe tests/integration/email_agenda/test_real_email_sending_execution.py -v
```

## Test Architecture

### Integration Test Patterns
- Cross-component interaction testing
- State management across multiple tools
- Error propagation and handling
- User interaction workflows
- OAuth and external service integration

### Real Execution Patterns
- Actual system component usage
- Performance monitoring
- Component trace analysis
- Response quality validation
- Timeout and exception handling

### Mock Strategies
- Selective mocking (only external dependencies)
- State object simulation
- Human-in-the-loop interaction mocking
- OAuth token simulation
- SMTP/IMAP service mocking

## Performance Considerations

### Real Execution Tests
- May take 60-90 seconds per test
- Require actual LLM and service calls
- Include timeout handling (90s default)
- Cache results for efficiency

### Mock Integration Tests
- Fast execution (1-5 seconds per test)
- Focus on workflow logic
- Comprehensive state testing
- Error scenario coverage

## Dependencies

Integration tests require:
- All unit test dependencies
- System initialization components
- Manager setup and teardown
- Real service configuration (for REAL tests)
- Extended timeout handling