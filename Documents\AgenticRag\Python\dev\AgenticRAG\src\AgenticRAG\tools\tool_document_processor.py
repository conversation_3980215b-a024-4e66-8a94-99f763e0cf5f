from imports import *
from langchain_core.tools import BaseTool
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field
from pathlib import Path

class DocumentProcessingInput(BaseModel):
    """Input for document processing tool."""
    file_path: str = Field(..., description="Path to the document file to process")
    extract_multimodal: bool = Field(default=True, description="Extract images and tables from document")
    store_in_vector_db: bool = Field(default=True, description="Store processed content in vector database")
    generate_summaries: bool = Field(default=True, description="Generate AI summaries for images and tables")
    doc_id: Optional[str] = Field(default=None, description="Custom document ID (auto-generated if not provided)")

class DocumentProcessorTool(BaseTool):
    """Comprehensive document processing tool with multimodal capabilities."""
    
    name: str = "document_processor"
    description: str = """
    Process documents with advanced multimodal extraction and storage capabilities.
    
    This tool can:
    - Extract text, images, and tables from PDF, DOCX, PPTX files
    - Generate AI-powered summaries for images and tables using vision models
    - Store all content in vector database for retrieval
    - Preserve document structure and context
    - Handle both embedded and standalone images
    - Convert tables to markdown format
    - Create searchable summaries for all content types
    
    Use this tool when you need to:
    - Process documents with complex layouts
    - Extract and understand images within documents
    - Analyze tables and structured data
    - Make multimodal content searchable and retrievable
    """
    
    args_schema: type = DocumentProcessingInput
    
    def _run(self, file_path: str, extract_multimodal: bool = True, store_in_vector_db: bool = True, 
             generate_summaries: bool = True, doc_id: Optional[str] = None) -> str:
        raise NotImplementedError("Use async version")
    
    async def _arun(self, file_path: str, extract_multimodal: bool = True, store_in_vector_db: bool = True, 
                   generate_summaries: bool = True, doc_id: Optional[str] = None) -> str:
        """Process a document with multimodal extraction and storage."""
        try:
            # Validate file path
            if not Path(file_path).exists():
                return f"Error: File not found at {file_path}"
            
            # Generate document ID if not provided
            if doc_id is None:
                import uuid
                doc_id = str(uuid.uuid4())
            
            # Initialize managers
            from managers.manager_multimodal import MultimodalManager
            from managers.manager_qdrant import QDrantManager
            from managers.manager_retrieval import RetrievalManager
            
            await MultimodalManager.setup()
            await QDrantManager.setup()
            await RetrievalManager.setup()
            
            results = {
                "doc_id": doc_id,
                "file_path": file_path,
                "status": "processing",
                "multimodal_extracted": False,
                "stored_in_vector_db": False,
                "summary": {}
            }
            
            # Step 1: Extract multimodal content
            if extract_multimodal:
                print(f"Extracting multimodal content from {file_path}...")
                multimodal_data = await MultimodalManager.extract_multimodal_elements(file_path, doc_id)
                
                if "error" in multimodal_data:
                    return f"Error extracting multimodal content: {multimodal_data['error']}"
                
                results["multimodal_extracted"] = True
                results["multimodal_data"] = multimodal_data
                
                # Summary of extracted content
                results["summary"] = {
                    "text_elements": len(multimodal_data.get("text_elements", [])),
                    "images": len(multimodal_data.get("images", [])),
                    "tables": len(multimodal_data.get("tables", [])),
                    "total_elements": len(multimodal_data.get("all_elements", []))
                }
                
                print(f"Extracted: {results['summary']['text_elements']} text elements, "
                      f"{results['summary']['images']} images, "
                      f"{results['summary']['tables']} tables")
            
            # Step 2: Process and store using multi-vector retriever
            if store_in_vector_db:
                print("Processing content for multi-vector storage...")
                
                # Initialize multi-vector retriever
                from managers.manager_multivector_retriever import MultiVectorRetriever
                await MultiVectorRetriever.setup()
                multivector_retriever = MultiVectorRetriever.get_instance()
                
                # Prepare metadata
                file_stats = Path(file_path).stat()
                
                if extract_multimodal and "multimodal_data" in results:
                    # Store using multi-vector retriever with document linking
                    storage_results = await multivector_retriever.add_multimodal_document(
                        multimodal_data=multimodal_data,
                        file_path=file_path,
                        file_name=Path(file_path).name,
                        parent_doc_id=doc_id
                    )
                    
                    # Update results with storage information
                    results["stored_in_vector_db"] = True
                    results["summary"]["text_chunks_stored"] = len(storage_results.get("text_ids", []))
                    results["summary"]["image_summaries_stored"] = len(storage_results.get("image_ids", []))
                    results["summary"]["table_summaries_stored"] = len(storage_results.get("table_ids", []))
                    results["summary"]["storage_method"] = "multi_vector_retriever"
                    
                    total_stored = (len(storage_results.get("text_ids", [])) + 
                                  len(storage_results.get("image_ids", [])) + 
                                  len(storage_results.get("table_ids", [])))
                    
                    print(f"Stored {total_stored} documents in multi-vector retriever:")
                    print(f"  - Text elements: {len(storage_results.get('text_ids', []))}")
                    print(f"  - Image summaries: {len(storage_results.get('image_ids', []))}")
                    print(f"  - Table summaries: {len(storage_results.get('table_ids', []))}")
                
                else:
                    # Fallback: simple text extraction and storage
                    text_content = await self._extract_simple_text(file_path)
                    text_chunks = self._chunk_text(text_content)
                    
                    # Store text chunks with summaries (use content as summary for simple text)
                    text_ids = await multivector_retriever.add_texts(
                        texts=text_chunks,
                        text_summaries=text_chunks,  # Use same content as summary
                        file_path=file_path,
                        file_name=Path(file_path).name,
                        parent_doc_id=doc_id
                    )
                    
                    results["stored_in_vector_db"] = True
                    results["summary"]["text_chunks_stored"] = len(text_ids)
                    results["summary"]["storage_method"] = "multi_vector_retriever_simple"
                    
                    print(f"Stored {len(text_ids)} text chunks in multi-vector retriever")
            
            # Step 3: Generate detailed summary
            results["status"] = "completed"
            return self._format_processing_results(results)
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "document_processor", None)
            return f"Error processing document: {str(e)}"
    
    async def _enhance_text_with_summaries(self, text: str, multimodal_data: Dict, generate_summaries: bool) -> str:
        """Enhance text content with relevant image and table summaries."""
        if not generate_summaries:
            return text
        
        try:
            enhanced_parts = [text]
            
            # Add relevant image summaries
            for image_data in multimodal_data.get("images", []):
                if image_data.get("summary"):
                    enhanced_parts.append(f"[Image: {image_data['summary']}]")
            
            # Add relevant table summaries
            for table_data in multimodal_data.get("tables", []):
                if table_data.get("summary"):
                    enhanced_parts.append(f"[Table: {table_data['summary']}]")
            
            return "\n\n".join(enhanced_parts)
            
        except Exception as e:
            print(f"Error enhancing text with summaries: {str(e)}")
            return text
    
    async def _extract_simple_text(self, file_path: str) -> str:
        """Extract simple text content from document (fallback method)."""
        try:
            from unstructured.partition.auto import partition
            
            elements = partition(filename=file_path)
            text_content = "\n".join([str(element) for element in elements])
            return text_content
            
        except Exception as e:
            print(f"Error extracting simple text: {str(e)}")
            return ""
    
    def _chunk_text(self, text: str, chunk_size: int = 4000, overlap: int = 200) -> List[str]:
        """Chunk text content into manageable pieces."""
        if not text:
            return []
        
        chunks = []
        start = 0
        
        while start < len(text):
            end = start + chunk_size
            
            # Try to break at sentence or paragraph boundaries
            if end < len(text):
                # Look for sentence endings
                sentence_end = text.rfind('.', start, end)
                if sentence_end > start:
                    end = sentence_end + 1
                else:
                    # Look for paragraph breaks
                    para_end = text.rfind('\n', start, end)
                    if para_end > start:
                        end = para_end + 1
            
            chunk = text[start:end].strip()
            if chunk:
                chunks.append(chunk)
            
            start = end - overlap
        
        return chunks
    
    def _format_processing_results(self, results: Dict) -> str:
        """Format processing results for output."""
        output = []
        
        output.append(f"Document Processing Results")
        output.append(f"========================")
        output.append(f"Document ID: {results['doc_id']}")
        output.append(f"File: {results['file_path']}")
        output.append(f"Status: {results['status'].upper()}")
        output.append("")
        
        if results.get("multimodal_extracted"):
            summary = results.get("summary", {})
            output.append(f"Multimodal Content Extracted:")
            output.append(f"  - Text Elements: {summary.get('text_elements', 0)}")
            output.append(f"  - Images: {summary.get('images', 0)}")
            output.append(f"  - Tables: {summary.get('tables', 0)}")
            output.append(f"  - Total Elements: {summary.get('total_elements', 0)}")
            output.append("")
        
        if results.get("stored_in_vector_db"):
            storage_method = results['summary'].get('storage_method', 'unknown')
            output.append(f"Multi-Vector Storage:")
            output.append(f"  - Storage Method: {storage_method}")
            output.append(f"  - Text Elements: {results['summary'].get('text_chunks_stored', 0)}")
            
            if results.get("multimodal_extracted"):
                output.append(f"  - Image Summaries: {results['summary'].get('image_summaries_stored', 0)}")
                output.append(f"  - Table Summaries: {results['summary'].get('table_summaries_stored', 0)}")
            
            output.append(f"  - Searchable: Yes (summaries)")
            output.append(f"  - Parent Documents: Yes (linked original content)")
            output.append("")
        
        # Add usage instructions
        output.append(f"Usage Instructions:")
        output.append(f"  - Use 'multivector_retrieval' tool to search this document")
        output.append(f"  - This searches summaries and returns original content")
        output.append(f"  - Filter by file name: {Path(results['file_path']).name}")
        output.append(f"  - Available content types: text, image, table")
        
        if results.get("multimodal_extracted") and results['summary'].get('images', 0) > 0:
            output.append(f"  - Use 'parent_document_retriever' tool to get specific documents by ID")
            output.append(f"  - Use 'multimodal_asset_retrieval' tool to access image assets")
        
        return "\n".join(output)

# Create tool instance
document_processor_tool = DocumentProcessorTool()