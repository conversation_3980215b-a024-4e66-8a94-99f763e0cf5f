"""
Global test environment detector for cross-thread test detection.

This module provides global test state management that persists across threads
and event loops, allowing background tasks and scheduled tasks to detect when 
they're running in a test environment.
"""

import os
import threading
import time
from pathlib import Path
from typing import Optional
from datetime import datetime, timedelta

# Global state variables with thread-safe access
_test_state_lock = threading.RLock()
_test_environment_active = False
_test_real_active = False
_current_test_log_path: Optional[Path] = None
_test_start_time: Optional[float] = None
_test_timeout: float = 600.0  # 10 minutes default timeout

def set_test_environment_active(test_name: str, log_path: Optional[Path] = None, timeout: float = 600.0):
    """
    Mark test environment as active globally across all threads
    
    Args:
        test_name: Name of the test being executed
        log_path: Path to the test log file for cross-thread writing
        timeout: How long to keep test environment active (seconds)
    """
    global _test_environment_active, _test_real_active, _current_test_log_path, _test_start_time, _test_timeout
    
    with _test_state_lock:
        _test_environment_active = True
        _test_real_active = 'test_real' in test_name.lower()
        _current_test_log_path = log_path
        _test_start_time = time.time()
        _test_timeout = timeout
        
        # Set environment variables for cross-process detection
        os.environ['TEST_ENVIRONMENT_ACTIVE'] = '1'
        if _test_real_active:
            os.environ['TEST_REAL_ACTIVE'] = '1'
            if log_path:
                os.environ['TEST_REAL_LOG_PATH'] = str(log_path)
        
        print(f"[TEST_ENV] Global test environment activated: {test_name}")
        if log_path:
            print(f"[TEST_ENV] Test log path set: {log_path}")

def clear_test_environment():
    """Clear test environment state and cleanup"""
    global _test_environment_active, _test_real_active, _current_test_log_path, _test_start_time
    
    with _test_state_lock:
        _test_environment_active = False
        _test_real_active = False
        _current_test_log_path = None
        _test_start_time = None
        
        # Clear environment variables
        os.environ.pop('TEST_ENVIRONMENT_ACTIVE', None)
        os.environ.pop('TEST_REAL_ACTIVE', None)
        os.environ.pop('TEST_REAL_LOG_PATH', None)
        
        print("[TEST_ENV] Global test environment cleared")

def is_test_environment_active() -> bool:
    """Check if any test environment is currently active"""
    with _test_state_lock:
        # Check global state first
        if _test_environment_active:
            # Verify timeout hasn't been exceeded
            if _test_start_time and time.time() - _test_start_time > _test_timeout:
                print(f"[TEST_ENV] Test environment timeout exceeded ({_test_timeout}s), clearing state")
                clear_test_environment()
                return False
            return True
        
        # Fallback: check environment variables (set by parent process/thread)
        return os.environ.get('TEST_ENVIRONMENT_ACTIVE') == '1'

def is_test_real_active() -> bool:
    """Check if test_real environment is currently active"""
    with _test_state_lock:
        if _test_real_active:
            # Verify timeout hasn't been exceeded
            if _test_start_time and time.time() - _test_start_time > _test_timeout:
                clear_test_environment()
                return False
            return True
        
        # Fallback: check environment variables
        return os.environ.get('TEST_REAL_ACTIVE') == '1'

def get_current_test_log_path() -> Optional[Path]:
    """Get the current test log file path if available"""
    with _test_state_lock:
        # Check global state first
        if _current_test_log_path:
            return _current_test_log_path
        
        # Fallback: check environment variables
        env_path = os.environ.get('TEST_REAL_LOG_PATH')
        if env_path:
            return Path(env_path)
        
        return None

def extend_test_timeout(additional_seconds: float = 300.0):
    """Extend the test timeout (useful for background tasks)"""
    global _test_timeout, _test_start_time
    
    with _test_state_lock:
        if _test_start_time:
            _test_timeout += additional_seconds
            print(f"[TEST_ENV] Test timeout extended by {additional_seconds}s (total: {_test_timeout}s)")

def write_to_test_log(message: str, add_timestamp: bool = True):
    """
    Thread-safe writing to the current test log file
    
    Args:
        message: Message to write to the test log
        add_timestamp: Whether to add a timestamp prefix
    """
    log_path = get_current_test_log_path()
    if not log_path:
        return False
    
    try:
        with _test_state_lock:
            if log_path.exists():
                with open(log_path, 'a', encoding='utf-8') as f:
                    if add_timestamp:
                        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                        f.write(f"[{timestamp}] {message}\n")
                    else:
                        f.write(f"{message}\n")
                    f.flush()
                return True
    except Exception as e:
        print(f"[TEST_ENV] Failed to write to test log: {e}")
        return False
    
    return False

def get_test_environment_info() -> dict:
    """Get current test environment information for debugging"""
    with _test_state_lock:
        return {
            'test_environment_active': _test_environment_active,
            'test_real_active': _test_real_active,
            'current_log_path': str(_current_test_log_path) if _current_test_log_path else None,
            'test_start_time': _test_start_time,
            'test_timeout': _test_timeout,
            'time_remaining': _test_timeout - (time.time() - _test_start_time) if _test_start_time else None,
            'env_test_active': os.environ.get('TEST_ENVIRONMENT_ACTIVE'),
            'env_test_real_active': os.environ.get('TEST_REAL_ACTIVE'),
            'env_log_path': os.environ.get('TEST_REAL_LOG_PATH')
        }

# Context manager for temporary test environment activation
class TestEnvironmentContext:
    """Context manager for setting test environment state"""
    
    def __init__(self, test_name: str, log_path: Optional[Path] = None, timeout: float = 600.0):
        self.test_name = test_name
        self.log_path = log_path
        self.timeout = timeout
        self.was_active_before = False
    
    def __enter__(self):
        self.was_active_before = is_test_environment_active()
        set_test_environment_active(self.test_name, self.log_path, self.timeout)
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if not self.was_active_before:
            clear_test_environment()