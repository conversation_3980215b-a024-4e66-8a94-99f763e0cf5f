"""
Profile Integration Example

This example demonstrates how to integrate the Profile dashboard into the existing
AskZaira application structure. The profile endpoint provides a comprehensive user
profile management interface with the requested fields.

To integrate into the main application, add this to your main application setup.
"""

from imports import *
from aiohttp import web
from endpoints.profile_endpoint import setup_profile_routes
from managers.manager_logfire import LogFire

async def setup_profile_integration(app: web.Application):
    """
    Integration example for adding profile functionality to the main application
    
    Add this to your main application setup where you configure other endpoints.
    """
    try:
        # Setup profile endpoint routes
        await setup_profile_routes(app)
        
        LogFire.log("INIT", "Profile dashboard integrated successfully")
        
        # Optional: Add profile link to main dashboard navigation
        # This would be added to your main dashboard's navigation menu
        profile_nav_item = {
            "id": "profile",
            "title": "Profile", 
            "url": "/profile",
            "icon": "user-circle",
            "description": "Manage your personal profile and AI preferences"
        }
        
        # Example of how you might add this to existing navigation
        # (This would be integrated into your actual navigation system)
        LogFire.log("DEBUG", f"Profile navigation item available: {profile_nav_item}")
        
    except Exception as e:
        from etc.helper_functions import exception_triggered
        exception_triggered(e, "Profile integration setup", None)
        raise

async def demo_profile_usage():
    """
    Demonstrates how the profile system works with existing user management
    """
    try:
        # Example of creating a user with profile data
        from userprofiles.ZairaUser import ZairaUser, PERMISSION_LEVELS
        from uuid import uuid4
        
        demo_user = ZairaUser(
            username="profile_demo",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        # Set profile information (new fields)
        demo_user.first_name = "Jane"
        demo_user.last_name = "Doe"
        demo_user.job_title = "AI Research Engineer"
        demo_user.company = "AskZaira Technologies"
        demo_user.personal_prompt = """
        I prefer:
        - Technical explanations with code examples
        - Step-by-step breakdowns for complex processes
        - Dutch language for casual conversations
        - Concise summaries for reports
        """
        demo_user.preferred_language = "en"
        demo_user.timezone = "Europe/Amsterdam"
        demo_user.email = "<EMAIL>"
        
        # Backward compatibility - existing fields still work
        demo_user.real_name = f"{demo_user.first_name} {demo_user.last_name}"
        
        LogFire.log("USER", f"Demo user created with profile data: {demo_user.first_name} {demo_user.last_name}")
        LogFire.log("DEBUG", f"User personal prompt: {demo_user.personal_prompt[:50]}...")
        
        return demo_user
        
    except Exception as e:
        from etc.helper_functions import exception_triggered
        exception_triggered(e, "Demo profile usage", None)
        raise

def profile_features_summary():
    """
    Summary of implemented profile dashboard features
    """
    features = {
        "personal_information": {
            "first_name": "User's first name",
            "last_name": "User's last name", 
            "email": "Email address with validation"
        },
        "professional_information": {
            "job_title": "Current job title or role",
            "company": "Company or organization name"
        },
        "ai_assistant_settings": {
            "personal_prompt": "Custom AI prompt for personalized responses",
            "preferred_language": "Language preference (en, nl, fr, de, es)",
            "timezone": "Timezone setting for scheduled tasks"
        },
        "dashboard_features": {
            "user_avatar": "Displays user initials or profile image",
            "statistics": "Shows total requests, sessions, permission level",
            "form_validation": "Client-side and server-side validation",
            "responsive_design": "Mobile-friendly glassmorphism interface",
            "auto_save": "Form data persistence and error handling"
        },
        "backend_integration": {
            "zaira_user_model": "Extended with new profile fields",
            "profile_endpoint": "RESTful API for profile management", 
            "user_manager": "Integration with existing user management",
            "logging": "Comprehensive LogFire logging integration"
        }
    }
    
    LogFire.log("INFO", "Profile dashboard features summary", severity="info")
    for category, items in features.items():
        LogFire.log("DEBUG", f"Category {category}: {len(items)} features", severity="debug")
    
    return features

# Usage example for main application integration:
"""
# Add this to your main app setup (e.g., in main.py or app.py):

from examples.profile_integration_example import setup_profile_integration

async def init_app():
    app = web.Application()
    
    # Your existing route setups...
    # await setup_dashboard_routes(app)
    # await setup_api_routes(app)
    
    # Add profile integration
    await setup_profile_integration(app)
    
    return app

# The profile will then be available at:
# GET  /profile                 - Profile dashboard page
# POST /profile/update          - Update profile information  
# GET  /profile/api/stats       - Get profile statistics
"""

if __name__ == "__main__":
    # Demo/testing code
    import asyncio
    
    async def run_demo():
        LogFire.log("INIT", "Running profile integration demo")
        
        # Show features
        features = profile_features_summary()
        
        # Create demo user
        user = await demo_profile_usage()
        
        LogFire.log("INFO", "Profile integration demo completed successfully")
    
    asyncio.run(run_demo())