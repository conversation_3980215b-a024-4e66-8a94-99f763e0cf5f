from imports import *
import json
import uuid
from typing import Dict, List, Optional, Any, Union
from datetime import datetime
from pathlib import Path
from dataclasses import dataclass, asdict
from pydantic import BaseModel, Field

@dataclass
class DocumentMetadata:
    """Metadata for stored documents."""
    doc_id: str
    content_type: str  # 'text', 'image', 'table', 'original'
    element_index: int
    file_path: str
    file_name: str
    created_at: datetime
    summary: Optional[str] = None
    asset_path: Optional[str] = None
    parent_doc_id: Optional[str] = None
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for JSON serialization."""
        data = asdict(self)
        data['created_at'] = self.created_at.isoformat()
        return data
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'DocumentMetadata':
        """Create from dictionary."""
        if isinstance(data['created_at'], str):
            data['created_at'] = datetime.fromisoformat(data['created_at'])
        return cls(**data)

class DocumentStoreManager:
    """
    Document store manager that handles parent-child relationships
    between summaries and original documents, similar to ChromaDB's
    MultiVectorRetriever but using PostgreSQL for persistence.
    """
    
    _instance: Optional['DocumentStoreManager'] = None
    _initialized: bool = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_instance(cls) -> "DocumentStoreManager":
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return
        
        # Initialize database tables
        await instance._create_tables()
        instance._initialized = True
    
    async def _create_tables(self):
        """Create necessary database tables for document storage."""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        db_manager = PostgreSQLManager.get_instance()
        
        # Create document store table
        create_document_store_sql = """
        CREATE TABLE IF NOT EXISTS document_store (
            doc_id VARCHAR(255) PRIMARY KEY,
            content_type VARCHAR(50) NOT NULL,
            element_index INTEGER NOT NULL,
            file_path TEXT NOT NULL,
            file_name VARCHAR(255) NOT NULL,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
            summary TEXT,
            asset_path TEXT,
            parent_doc_id VARCHAR(255),
            original_content TEXT NOT NULL,
            metadata_json TEXT
        );
        """
        
        # Create indexes for better performance
        create_indexes_sql = [
            "CREATE INDEX IF NOT EXISTS idx_document_store_content_type ON document_store(content_type);",
            "CREATE INDEX IF NOT EXISTS idx_document_store_parent_doc_id ON document_store(parent_doc_id);",
            "CREATE INDEX IF NOT EXISTS idx_document_store_file_name ON document_store(file_name);",
            "CREATE INDEX IF NOT EXISTS idx_document_store_created_at ON document_store(created_at);"
        ]
        
        try:
            async with db_manager.get_connection("vectordb") as conn:
                await conn.execute(create_document_store_sql)
                for index_sql in create_indexes_sql:
                    await conn.execute(index_sql)
        except Exception as e:
            print(f"Error creating document store tables: {str(e)}")
    
    async def store_document(self, doc_id: str, content: str, metadata: DocumentMetadata) -> bool:
        """
        Store a document with its metadata.
        
        Args:
            doc_id: Unique identifier for the document
            content: The actual content of the document
            metadata: Document metadata
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            from managers.manager_postgreSQL import PostgreSQLManager
            
            db_manager = PostgreSQLManager.get_instance()
            
            insert_sql = """
            INSERT INTO document_store (
                doc_id, content_type, element_index, file_path, file_name,
                created_at, summary, asset_path, parent_doc_id, original_content, metadata_json
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            ON CONFLICT (doc_id) DO UPDATE SET
                content_type = EXCLUDED.content_type,
                element_index = EXCLUDED.element_index,
                file_path = EXCLUDED.file_path,
                file_name = EXCLUDED.file_name,
                created_at = EXCLUDED.created_at,
                summary = EXCLUDED.summary,
                asset_path = EXCLUDED.asset_path,
                parent_doc_id = EXCLUDED.parent_doc_id,
                original_content = EXCLUDED.original_content,
                metadata_json = EXCLUDED.metadata_json
            """
            
            async with db_manager.get_connection("vectordb") as conn:
                await conn.execute(
                    insert_sql,
                    doc_id,
                    metadata.content_type,
                    metadata.element_index,
                    metadata.file_path,
                    metadata.file_name,
                    metadata.created_at,
                    metadata.summary,
                    metadata.asset_path,
                    metadata.parent_doc_id,
                    content,
                    json.dumps(metadata.to_dict())
                )
            
            return True
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "document_store_manager", None)
            return False
    
    async def store_multiple_documents(self, documents: List[tuple]) -> bool:
        """
        Store multiple documents in batch.
        
        Args:
            documents: List of (doc_id, content, metadata) tuples
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            from managers.manager_postgreSQL import PostgreSQLManager
            
            db_manager = PostgreSQLManager.get_instance()
            
            insert_sql = """
            INSERT INTO document_store (
                doc_id, content_type, element_index, file_path, file_name,
                created_at, summary, asset_path, parent_doc_id, original_content, metadata_json
            ) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11)
            ON CONFLICT (doc_id) DO UPDATE SET
                content_type = EXCLUDED.content_type,
                element_index = EXCLUDED.element_index,
                file_path = EXCLUDED.file_path,
                file_name = EXCLUDED.file_name,
                created_at = EXCLUDED.created_at,
                summary = EXCLUDED.summary,
                asset_path = EXCLUDED.asset_path,
                parent_doc_id = EXCLUDED.parent_doc_id,
                original_content = EXCLUDED.original_content,
                metadata_json = EXCLUDED.metadata_json
            """
            
            # Prepare batch data
            batch_data = []
            for doc_id, content, metadata in documents:
                batch_data.append((
                    doc_id,
                    metadata.content_type,
                    metadata.element_index,
                    metadata.file_path,
                    metadata.file_name,
                    metadata.created_at,
                    metadata.summary,
                    metadata.asset_path,
                    metadata.parent_doc_id,
                    content,
                    json.dumps(metadata.to_dict())
                ))
            
            async with db_manager.get_connection("vectordb") as conn:
                await conn.executemany(insert_sql, batch_data)
            
            return True
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "document_store_batch", None)
            return False
    
    async def retrieve_document(self, doc_id: str) -> Optional[tuple]:
        """
        Retrieve a document by its ID.
        
        Args:
            doc_id: Document identifier
            
        Returns:
            tuple: (content, metadata) or None if not found
        """
        try:
            from managers.manager_postgreSQL import PostgreSQLManager
            
            db_manager = PostgreSQLManager.get_instance()
            
            select_sql = """
            SELECT original_content, content_type, element_index, file_path, file_name,
                   created_at, summary, asset_path, parent_doc_id, metadata_json
            FROM document_store 
            WHERE doc_id = $1
            """
            
            async with db_manager.get_connection("vectordb") as conn:
                row = await conn.fetchrow(select_sql, doc_id)
                
                if row:
                    metadata = DocumentMetadata(
                        doc_id=doc_id,
                        content_type=row['content_type'],
                        element_index=row['element_index'],
                        file_path=row['file_path'],
                        file_name=row['file_name'],
                        created_at=row['created_at'],
                        summary=row['summary'],
                        asset_path=row['asset_path'],
                        parent_doc_id=row['parent_doc_id']
                    )
                    return (row['original_content'], metadata)
                
                return None
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "document_store_retrieve", None)
            return None
    
    async def retrieve_multiple_documents(self, doc_ids: List[str]) -> Dict[str, tuple]:
        """
        Retrieve multiple documents by their IDs.
        
        Args:
            doc_ids: List of document identifiers
            
        Returns:
            Dict: {doc_id: (content, metadata)} mapping
        """
        try:
            from managers.manager_postgreSQL import PostgreSQLManager
            
            db_manager = PostgreSQLManager.get_instance()
            
            # Create placeholders for the IN clause
            placeholders = ', '.join(f'${i+1}' for i in range(len(doc_ids)))
            select_sql = f"""
            SELECT doc_id, original_content, content_type, element_index, file_path, file_name,
                   created_at, summary, asset_path, parent_doc_id, metadata_json
            FROM document_store 
            WHERE doc_id IN ({placeholders})
            """
            
            async with db_manager.get_connection("vectordb") as conn:
                rows = await conn.fetch(select_sql, *doc_ids)
                
                results = {}
                for row in rows:
                    metadata = DocumentMetadata(
                        doc_id=row['doc_id'],
                        content_type=row['content_type'],
                        element_index=row['element_index'],
                        file_path=row['file_path'],
                        file_name=row['file_name'],
                        created_at=row['created_at'],
                        summary=row['summary'],
                        asset_path=row['asset_path'],
                        parent_doc_id=row['parent_doc_id']
                    )
                    results[row['doc_id']] = (row['original_content'], metadata)
                
                return results
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "document_store_retrieve_multiple", None)
            return {}
    
    async def retrieve_documents_by_parent(self, parent_doc_id: str) -> List[tuple]:
        """
        Retrieve all documents with a specific parent document ID.
        
        Args:
            parent_doc_id: Parent document identifier
            
        Returns:
            List: List of (doc_id, content, metadata) tuples
        """
        try:
            from managers.manager_postgreSQL import PostgreSQLManager
            
            db_manager = PostgreSQLManager.get_instance()
            
            select_sql = """
            SELECT doc_id, original_content, content_type, element_index, file_path, file_name,
                   created_at, summary, asset_path, parent_doc_id, metadata_json
            FROM document_store 
            WHERE parent_doc_id = $1
            ORDER BY element_index
            """
            
            async with db_manager.get_connection("vectordb") as conn:
                rows = await conn.fetch(select_sql, parent_doc_id)
                
                results = []
                for row in rows:
                    metadata = DocumentMetadata(
                        doc_id=row['doc_id'],
                        content_type=row['content_type'],
                        element_index=row['element_index'],
                        file_path=row['file_path'],
                        file_name=row['file_name'],
                        created_at=row['created_at'],
                        summary=row['summary'],
                        asset_path=row['asset_path'],
                        parent_doc_id=row['parent_doc_id']
                    )
                    results.append((row['doc_id'], row['original_content'], metadata))
                
                return results
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "document_store_retrieve_by_parent", None)
            return []
    
    async def retrieve_documents_by_content_type(self, content_type: str, 
                                               file_name: Optional[str] = None) -> List[tuple]:
        """
        Retrieve documents by content type and optionally by file name.
        
        Args:
            content_type: Type of content ('text', 'image', 'table', 'original')
            file_name: Optional file name filter
            
        Returns:
            List: List of (doc_id, content, metadata) tuples
        """
        try:
            from managers.manager_postgreSQL import PostgreSQLManager
            
            db_manager = PostgreSQLManager.get_instance()
            
            if file_name:
                select_sql = """
                SELECT doc_id, original_content, content_type, element_index, file_path, file_name,
                       created_at, summary, asset_path, parent_doc_id, metadata_json
                FROM document_store 
                WHERE content_type = $1 AND file_name = $2
                ORDER BY created_at DESC, element_index
                """
                params = (content_type, file_name)
            else:
                select_sql = """
                SELECT doc_id, original_content, content_type, element_index, file_path, file_name,
                       created_at, summary, asset_path, parent_doc_id, metadata_json
                FROM document_store 
                WHERE content_type = $1
                ORDER BY created_at DESC, element_index
                """
                params = (content_type,)
            
            async with db_manager.get_connection("vectordb") as conn:
                rows = await conn.fetch(select_sql, *params)
                
                results = []
                for row in rows:
                    metadata = DocumentMetadata(
                        doc_id=row['doc_id'],
                        content_type=row['content_type'],
                        element_index=row['element_index'],
                        file_path=row['file_path'],
                        file_name=row['file_name'],
                        created_at=row['created_at'],
                        summary=row['summary'],
                        asset_path=row['asset_path'],
                        parent_doc_id=row['parent_doc_id']
                    )
                    results.append((row['doc_id'], row['original_content'], metadata))
                
                return results
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "document_store_retrieve_by_type", None)
            return []
    
    async def delete_document(self, doc_id: str) -> bool:
        """
        Delete a document by its ID.
        
        Args:
            doc_id: Document identifier
            
        Returns:
            bool: True if successful, False otherwise
        """
        try:
            from managers.manager_postgreSQL import PostgreSQLManager
            
            db_manager = PostgreSQLManager.get_instance()
            
            delete_sql = "DELETE FROM document_store WHERE doc_id = $1"
            
            async with db_manager.get_connection("vectordb") as conn:
                result = await conn.execute(delete_sql, doc_id)
                return result == "DELETE 1"
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "document_store_delete", None)
            return False
    
    async def delete_documents_by_parent(self, parent_doc_id: str) -> int:
        """
        Delete all documents with a specific parent document ID.
        
        Args:
            parent_doc_id: Parent document identifier
            
        Returns:
            int: Number of deleted documents
        """
        try:
            from managers.manager_postgreSQL import PostgreSQLManager
            
            db_manager = PostgreSQLManager.get_instance()
            
            delete_sql = "DELETE FROM document_store WHERE parent_doc_id = $1"
            
            async with db_manager.get_connection("vectordb") as conn:
                result = await conn.execute(delete_sql, parent_doc_id)
                # Extract number from result like "DELETE 5"
                return int(result.split()[-1]) if result.startswith("DELETE") else 0
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "document_store_delete_by_parent", None)
            return 0
    
    async def get_document_count(self) -> int:
        """Get total number of documents in the store."""
        try:
            from managers.manager_postgreSQL import PostgreSQLManager
            
            db_manager = PostgreSQLManager.get_instance()
            
            count_sql = "SELECT COUNT(*) FROM document_store"
            
            async with db_manager.get_connection("vectordb") as conn:
                row = await conn.fetchrow(count_sql)
                return row[0] if row else 0
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "document_store_count", None)
            return 0
    
    async def get_document_stats(self) -> Dict[str, Any]:
        """Get statistics about stored documents."""
        try:
            from managers.manager_postgreSQL import PostgreSQLManager
            
            db_manager = PostgreSQLManager.get_instance()
            
            stats_sql = """
            SELECT 
                COUNT(*) as total_documents,
                COUNT(DISTINCT parent_doc_id) as unique_parents,
                COUNT(DISTINCT file_name) as unique_files,
                content_type,
                COUNT(*) as count_by_type
            FROM document_store 
            GROUP BY content_type
            """
            
            async with db_manager.get_connection("vectordb") as conn:
                rows = await conn.fetch(stats_sql)
                
                stats = {
                    "total_documents": 0,
                    "unique_parents": 0,
                    "unique_files": 0,
                    "content_type_counts": {}
                }
                
                for row in rows:
                    if stats["total_documents"] == 0:
                        stats["total_documents"] = row["total_documents"]
                        stats["unique_parents"] = row["unique_parents"]
                        stats["unique_files"] = row["unique_files"]
                    
                    stats["content_type_counts"][row["content_type"]] = row["count_by_type"]
                
                return stats
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "document_store_stats", None)
            return {"error": str(e)}