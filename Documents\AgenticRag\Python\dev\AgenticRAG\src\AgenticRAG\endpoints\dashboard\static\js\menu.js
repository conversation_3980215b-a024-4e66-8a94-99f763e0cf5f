// Enhanced Dashboard Menu JavaScript
(function() {
    let menuOpen = false;
    let currentPage = 'overview';
    const menu = document.getElementById('enhancedRightMenu');
    const toggleBtn = document.getElementById('enhancedMenuToggle');
    const particlesContainer = document.getElementById('menuParticlesInside');
    
    // Initialize particles for menu
    function initializeMenuParticles() {
        const particleCount = 25;
        
        for (let i = 0; i < particleCount; i++) {
            const particle = document.createElement('div');
            particle.className = 'menu-particle';
            
            const size = Math.random() * 4 + 2;
            const x = Math.random() * 100;
            const y = Math.random() * 100;
            const duration = Math.random() * 3 + 4;
            const delay = Math.random() * 2;
            
            particle.style.width = size + 'px';
            particle.style.height = size + 'px';
            particle.style.left = x + '%';
            particle.style.top = y + '%';
            particle.style.animationDuration = duration + 's';
            particle.style.animationDelay = delay + 's';
            
            particlesContainer.appendChild(particle);
        }
    }
    
    function toggleMenu() {
        menuOpen = !menuOpen;
        
        if (menuOpen) {
            menu.classList.add('expanded');
            toggleBtn.textContent = 'X';
            toggleBtn.classList.add('active');
        } else {
            menu.classList.remove('expanded');
            toggleBtn.textContent = 'Menu';
            toggleBtn.classList.remove('active');
        }
    }
    
    
    
    // Handle menu item clicks with JavaScript navigation
    function handleMenuItemClick(e) {
        const clickedItem = e.currentTarget;
        const page = clickedItem.getAttribute('data-page');
        const url = clickedItem.getAttribute('data-url');
        const password = clickedItem.getAttribute('data-password');
        
        // Handle password protection
        if (password) {
            const userPassword = prompt('Enter password for Direct Chat:');
            if (userPassword !== password) {
                showNotification('Incorrect password', 'error');
                return;
            }
        }
        
        // Handle external URLs (like connectors)
        if (url) {
            window.location.href = url;
            return;
        }
        
        // Update active state with smooth transition
        const menuItems = document.querySelectorAll('.menu-item');
        menuItems.forEach(item => {
            item.classList.remove('active');
        });
        
        clickedItem.classList.add('active');
        currentPage = page;
        
        // Handle navigation - this will be handled by the dashboard system
        console.log(`Navigating to page: ${page}`);
        
        // Auto-close menu after selection (optional)
        setTimeout(() => {
            if (menuOpen) {
                toggleMenu();
            }
        }, 500);
    }
    
    // Initialize menu
    function initializeMenu() {
        if (toggleBtn) {
            toggleBtn.addEventListener('click', toggleMenu);
        }
        
        // Add click handlers to menu items
        document.querySelectorAll('.menu-item').forEach(item => {
            item.addEventListener('click', handleMenuItemClick);
        });
        
        // Close menu when clicking outside
        document.addEventListener('click', function(e) {
            if (menuOpen && menu && !menu.contains(e.target) && e.target !== toggleBtn) {
                toggleMenu();
            }
        });
        
        // Initialize particles
        if (particlesContainer) {
            initializeMenuParticles();
        }
        
        // Initialize system health monitoring
        initializeSystemHealth();
    }
    
    // System Health Monitoring for Header
    function initializeSystemHealth() {
        updateSystemHealthStatus();
        // Update every 30 seconds
        setInterval(updateSystemHealthStatus, 30000);
    }
    
    async function updateSystemHealthStatus() {
        const statusElement = document.getElementById('systemHealthStatus');
        if (!statusElement) return;
        
        try {
            const response = await fetch('/dashboard/api/system-health');
            const health = await response.json();
            
            if (health.error) {
                statusElement.textContent = 'System Status: Error';
                statusElement.style.color = '#ef4444';
                return;
            }
            
            // Format system health status for header display
            const dbStatus = health.database === 'healthy' ? 'DB-OK' : 'DB-ERR';
            const vectorStatus = health.vector_db === 'healthy' ? 'VDB-OK' : 'VDB-ERR';
            const activeTasks = health.active_tasks || 0;
            const systemLoad = health.system_load || 'Normal';
            const uptime = health.uptime || '0h 0m';
            
            // Create compact status display
            const statusText = `${dbStatus} | ${vectorStatus} | ${activeTasks} tasks | ${systemLoad} | Up: ${uptime}`;
            statusElement.textContent = statusText;
            
            // Color code based on overall system health
            if (health.database === 'healthy' && health.vector_db === 'healthy' && systemLoad === 'Normal') {
                statusElement.style.color = '#10b981'; // Green for healthy
            } else if (health.database === 'Error' || health.vector_db === 'Error') {
                statusElement.style.color = '#ef4444'; // Red for errors
            } else {
                statusElement.style.color = '#f59e0b'; // Yellow for warnings
            }
            
        } catch (error) {
            statusElement.textContent = 'System Status: Connection Error';
            statusElement.style.color = '#ef4444';
            console.error('Failed to update system health status:', error);
        }
    }
    
    // Notification system (if not already available)
    function showNotification(message, type = 'info') {
        // Simple notification system
        const notification = document.createElement('div');
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            padding: 1rem 1.5rem;
            background: ${type === 'error' ? '#dc2626' : '#3b82f6'};
            color: white;
            border-radius: 8px;
            z-index: 10002;
            font-weight: 500;
            opacity: 0;
            transform: translateX(100%);
            transition: all 0.3s ease;
        `;
        notification.textContent = message;
        
        document.body.appendChild(notification);
        
        // Animate in
        setTimeout(() => {
            notification.style.opacity = '1';
            notification.style.transform = 'translateX(0)';
        }, 10);
        
        // Remove after 3 seconds
        setTimeout(() => {
            notification.style.opacity = '0';
            notification.style.transform = 'translateX(100%)';
            setTimeout(() => {
                document.body.removeChild(notification);
            }, 300);
        }, 3000);
    }
    
    // Initialize everything when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', initializeMenu);
    } else {
        initializeMenu();
    }
    
    // Expose functions globally if needed
    window.dashboardMenu = {
        toggleMenu,
        showNotification
    };
})();