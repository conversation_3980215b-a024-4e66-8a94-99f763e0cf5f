#!/usr/bin/env python3
"""
Test script for the Excel Analyzer task.
This script allows you to test your task with custom prompts.
"""

import asyncio
import sys
import os
from pathlib import Path

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_excel_analyzer_task():
    """Test the Excel Analyzer task with various prompts"""
    print("🧪 Testing Excel Analyzer Task")
    print("=" * 50)
    
    try:
        # Setup basic environment
        print("Setting up environment...")
        from imports import *
        await LogFire.setup()
        ZairaSettings.IsDebugMode = True
        Globals.set_debug(ZairaSettings.IsDebugMode)
        
        # Setup minimal required managers
        await setup_minimal_environment()
        
        # Import your task
        from tasks.processing.task_excel_analyzer import create_task_sql_excel_analyzer
        
        # Create the task
        print("Creating Excel Analyzer task...")
        excel_task = await create_task_sql_excel_analyzer()
        print("✅ Task created successfully")
        
        # Test prompts
        test_prompts = [
            "Analyze the sales data in the Excel file and show me the top 5 products by revenue",
            "Create a summary report of the quarterly financial data",
            "Find all customers with orders over $1000 in the last month",
            "Generate a pivot table showing sales by region and product category",
            "What are the trends in our monthly sales data?"
        ]
        
        # Test each prompt
        for i, prompt in enumerate(test_prompts, 1):
            print(f"\n🔍 Test {i}: Testing prompt")
            print(f"Prompt: {prompt}")
            print("-" * 40)
            
            try:
                # Create test state
                state = await create_test_state(prompt)
                
                # Run the task
                print("Running task...")
                result = await asyncio.wait_for(excel_task.llm_call(state), timeout=30.0)
                
                print("✅ Task completed successfully")
                print(f"Result: {result}")
                
            except asyncio.TimeoutError:
                print("❌ Task timed out after 30 seconds")
            except Exception as e:
                print(f"❌ Task failed: {str(e)}")
                import traceback
                print(f"Traceback: {traceback.format_exc()}")
            
            print("-" * 40)
        
        print("\n🎉 Excel Analyzer task testing completed!")
        
    except Exception as e:
        print(f"❌ Setup failed: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

async def setup_minimal_environment():
    """Setup minimal environment for task testing"""
    try:
        # Setup required managers
        from managers.manager_supervisors import SupervisorManager
        from managers.manager_users import ZairaUserManager
        from managers.manager_prompts import PromptManager
        from managers.manager_postgreSQL import PostgreSQLManager
        
        print("Setting up SupervisorManager...")
        await SupervisorManager.setup()
        
        print("Setting up PromptManager...")
        await PromptManager.loadDefaultPrompts()
        
        print("Setting up database connection...")
        await PostgreSQLManager.create_database("vectordb")
        await PostgreSQLManager.connect_to_database("vectordb", use_pool=True, min_size=1, max_size=5)
        
        # Create test user
        print("Creating test user...")
        from userprofiles.permission_levels import PERMISSION_LEVELS
        test_user = await ZairaUserManager.add_user(
            "TestUser", 
            PERMISSION_LEVELS.ADMIN, 
            ZairaUserManager.create_guid(), 
            ZairaUserManager.create_guid()
        )
        
        print("✅ Minimal environment setup complete")
        return test_user
        
    except Exception as e:
        print(f"❌ Environment setup failed: {str(e)}")
        raise

async def create_test_state(prompt: str):
    """Create a test SupervisorTaskState for testing"""
    from managers.manager_supervisors import SupervisorTaskState
    from langchain_core.messages import HumanMessage
    from managers.manager_users import ZairaUserManager
    
    # Get test user
    users = await ZairaUserManager.get_all_users()
    test_user = users[0] if users else None
    user_guid = str(test_user.GUID) if test_user else ""
    
    # Create state
    state = SupervisorTaskState(
        original_input=prompt,
        user_guid=user_guid,
        messages=[HumanMessage(content=prompt)],
        call_trace=["test_excel_analyzer: start"]
    )
    
    return state

async def test_with_custom_prompt():
    """Interactive testing with custom prompts"""
    print("🎯 Interactive Excel Analyzer Testing")
    print("=" * 50)
    
    try:
        # Setup
        from imports import *
        await LogFire.setup()
        ZairaSettings.IsDebugMode = True
        Globals.set_debug(ZairaSettings.IsDebugMode)
        
        await setup_minimal_environment()
        
        # Import and create task
        from tasks.processing.task_excel_analyzer import create_task_sql_excel_analyzer
        excel_task = await create_task_sql_excel_analyzer()
        
        print("✅ Excel Analyzer task ready for testing")
        print("\nEnter your prompts (type 'quit' to exit):")
        
        while True:
            try:
                # Get user input
                prompt = input("\n📝 Enter prompt: ").strip()
                
                if prompt.lower() in ['quit', 'exit', 'q']:
                    break
                
                if not prompt:
                    continue
                
                print(f"Testing prompt: {prompt}")
                
                # Create state and run task
                state = await create_test_state(prompt)
                result = await asyncio.wait_for(excel_task.llm_call(state), timeout=30.0)
                
                print("✅ Result:")
                print(result)
                
            except asyncio.TimeoutError:
                print("❌ Task timed out")
            except KeyboardInterrupt:
                break
            except Exception as e:
                print(f"❌ Error: {str(e)}")
        
        print("\n👋 Testing session ended")
        
    except Exception as e:
        print(f"❌ Interactive testing failed: {str(e)}")

async def test_task_compilation():
    """Test if the task compiles correctly"""
    print("🔧 Testing Task Compilation")
    print("=" * 30)
    
    try:
        # Setup
        from imports import *
        await LogFire.setup()
        ZairaSettings.IsDebugMode = True
        Globals.set_debug(ZairaSettings.IsDebugMode)
        
        from managers.manager_supervisors import SupervisorManager
        await SupervisorManager.setup()
        
        # Import and create task
        from tasks.processing.task_excel_analyzer import create_task_sql_excel_analyzer
        
        print("Creating task...")
        excel_task = await create_task_sql_excel_analyzer()
        print("✅ Task created")
        
        print("Compiling task...")
        if excel_task.compiled_langgraph is None:
            excel_task.compile_default()
        print("✅ Task compiled successfully")
        
        print("Task details:")
        print(f"  Name: {excel_task.name}")
        print(f"  Prompt ID: {getattr(excel_task, 'prompt_id', 'N/A')}")
        print(f"  Tools: {len(excel_task.get_tools())} tools")
        print(f"  Compiled: {excel_task.compiled_langgraph is not None}")
        
    except Exception as e:
        print(f"❌ Compilation test failed: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    print("Excel Analyzer Task Testing")
    print("Choose testing mode:")
    print("1. Predefined test prompts")
    print("2. Interactive custom prompts") 
    print("3. Compilation test only")
    
    try:
        choice = input("Enter choice (1-3): ").strip()
        
        if choice == "1":
            asyncio.run(test_excel_analyzer_task())
        elif choice == "2":
            asyncio.run(test_with_custom_prompt())
        elif choice == "3":
            asyncio.run(test_task_compilation())
        else:
            print("Invalid choice. Running predefined tests...")
            asyncio.run(test_excel_analyzer_task())
            
    except KeyboardInterrupt:
        print("\n👋 Testing interrupted by user")
    except Exception as e:
        print(f"❌ Testing failed: {str(e)}")
        sys.exit(1)
