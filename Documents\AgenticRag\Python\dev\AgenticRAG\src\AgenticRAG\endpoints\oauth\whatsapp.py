from imports import *

from aiohttp import web

from endpoints.oauth._verifier_ import OAuth2App

class OAuth2Whatsapp(OAuth2App):
    def setup(self, myname):
        super().setup(myname)
        self.create_input("comm", [
            "str:WhatsApp Phone Number",
            "str:Backup WhatsApp Phone Number",
            "str:Backup WhatsApp Phone Number",
        ]) \
        .set_meltano({
            "Recipient_WhatsApp_Phone_Number": "str1",
            "Backup_WhatsApp_Phone_Number_1": "str2",
            "Backup_WhatsApp_Phone_Number_2": "str3"
        })

    async def on_success_return(self, request: web.Request) -> str:
        # Wordt getoond zodra de koppeling gelukt is
        ret_html = await super().on_success_return(request)
        #ret_html += "WhatsApp configuratie opgeslagen! De bot is nu klaar voor gebruik."
        return ret_html

    async def on_success_execute(self, request: web.Request) -> str:
        # Mits de return != "", wordt getoond zodra on_success_execute klaar is
        ret_html = await super().on_success_execute(request)
        
        # Send automatic greeting message to new WhatsApp recipient
        try:
            # Import required modules
            from endpoints.whatsapp_endpoint import MyWhatsappBot
            from endpoints.oauth._verifier_ import OAuth2Verifier
            from managers.manager_logfire import LogFire
            
            # Get the WhatsApp configuration to find the recipient number
            whatsapp_config = await OAuth2Verifier.get_full_token("whatsapp")
            if whatsapp_config and whatsapp_config.get("str1"):
                recipient_number = whatsapp_config["str1"]
                
                # Send automatic greeting message
                greeting_message = "Hi my name is Zaira, how can I assist you today?"
                success = await MyWhatsappBot.send_a_whatsapp_message(greeting_message, recipient_number)
                
                if success:
                    LogFire.log("EVENT", f"Automatic greeting sent to WhatsApp number: {recipient_number}")
                    ret_html += f"<br>Automatic greeting message sent to {recipient_number}!"
                else:
                    LogFire.log("ERROR", f"Failed to send automatic greeting to WhatsApp number: {recipient_number}", severity="warning")
                    ret_html += f"<br>Failed to send automatic greeting message to {recipient_number}."
            else:
                LogFire.log("DEBUG", "No WhatsApp recipient number found in configuration")
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "whatsapp_auto_greeting", None)
            ret_html += "<br>Error occurred while sending automatic greeting message."
            
        return ret_html
    
    async def on_success_execute_fail(self, request: web.Request) -> str:
        # Mits success_execute, wordt getoond als on_success_execute faalt
        ret_html = await super().on_success_execute_fail(request)
        #ret_html += "Er is een fout opgetreden bij het configureren van WhatsApp."
        return ret_html