from imports import *

# https://langchain-ai.github.io/langgraph/tutorials/workflows/#agent
from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTaskState
from managers.manager_users import ZairaUserManager

class SupervisorTask_HTTP(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        input_message = state.messages[-1].content if len(state.messages) > 1 else state.messages[0].content
        user = await ZairaUserManager.find_user(state.user_guid)
        if self.calling_bot.name == "HTTP":
            await self.send_response(f"<html><head><title>{user.username}'s response</title></head><body>{input_message}</body></html>")
            LogFire.log("OUTPUT", "HTTP:", input_message)

async def create_out_task_http() -> SupervisorTask_Base:
    return SupervisorManager.register_task(SupervisorTask_HTTP(name="http_out", prompt_id="Output_Sender_HTTP"))
