from imports import *

from uuid import uuid4, UUID
import asyncio
from pathlib import Path
from typing import List, Dict, Any, Optional

from pydantic import BaseModel, Field, ConfigDict
from unstructured.partition.auto import partition
from langchain_core.messages import HumanMessage, AnyMessage
    
from userprofiles.ZairaMessage import ZairaMessage
from userprofiles.ZairaChat import ZairaChat
from managers.manager_multimodal import MultimodalManager

from enum import Enum
class PERMISSION_LEVELS(Enum):
    NONE = -1
    GUEST = 0
    USER = 1
    ADMIN = 2

class ZairaUser(BaseModel):
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
        from endpoints.mybot_generic import MyBot_Generic, ReplyContext

    username: str = ""
    rank: PERMISSION_LEVELS = PERMISSION_LEVELS.NONE
    real_name: str = ""
    email: str = ""
    is_system_user: bool = False  # Flag to identify system users
    
    # Profile fields for dashboard
    first_name: str = ""
    last_name: str = ""  
    job_title: str = ""
    company: str = ""
    personal_prompt: str = ""
    preferred_language: str = "en"
    timezone: str = "UTC"
    
    # Account settings fields
    zaira_voice: str = "default"  # Voice selection for Zaira
    enable_followup_questions: bool = True  # Enable AI follow-up questions
    chat_history_interval: str = "never"  # Options: never, daily, weekly, monthly
    company_domain: str = ""  # Company domain (for SYSTEM user)
    two_factor_enabled: bool = False  # 2-factor authentication setting
    
    # System information fields
    tokens_used: int = 0  # Total tokens consumed
    allow_document_generation: bool = True  # Permission to generate documents
    gb_remaining: float = 100.0  # Storage remaining in GB
    data_location: str = "Local Server"  # Where data is stored
    active_triggers: int = 0  # Number of active triggers
    disabled_triggers: int = 0  # Number of disabled triggers
    total_connectors: int = 0  # Total number of connectors
    total_automations: int = 0  # Total number of automations
    organization_id: str = ""  # Organization identifier
    privacy_level: str = "Standard"  # Privacy information level
    
    # Subscription information fields
    plan_type: str = "ZZP"  # Plan type: ZZP, MKB, ZairaPlus
    payment_method: str = "Credit Card ****1234"  # Current payment method
    next_billing_date: str = "2024-02-15"  # Next billing date (YYYY-MM-DD)
    monthly_cost: float = 29.99  # Monthly subscription cost
    employee_count: int = 1  # Number of employees/users
    subscription_status: str = "Active"  # Active, Cancelled, Expired, Pending

    # Internal
    user_guid: UUID = Field(None, exclude=True)
    DeviceGUID: UUID = Field(None, exclude=True)
    session_guid: UUID = Field(None, exclude=True)
    my_requests: Dict[UUID, "LongRunningZairaRequest"] = Field(default_factory=dict, exclude=True)
    asyncio_Task: asyncio.Task = Field(None, exclude=True)
    chat_history: dict[UUID, ZairaChat] = {}
    
    model_config = ConfigDict(arbitrary_types_allowed=True)

    def __init__(self, username: str, rank: PERMISSION_LEVELS, guid: UUID, device_guid: UUID):
        super().__init__()
        self.username = username
        self.rank = rank
        self.user_guid = guid
        self.DeviceGUID = device_guid
        self.session_guid = uuid4()
        self.chat_history[self.session_guid] = ZairaChat(
            session_guid=self.session_guid,
            user_guid=str(self.user_guid),
            conversation_guid=self.session_guid,
            title="Main Chat Session"
        )
        self.my_requests = {}
        self.is_system_user = False
        LogFire.log("INIT", f"ZairaUser created: {self.username}")
        
        # Set user reference in default chat session for logging
        if self.session_guid in self.chat_history:
            self.chat_history[self.session_guid].set_user(self)
            default_chat = self.chat_history[self.session_guid]
            LogFire.log("USER", f"User created with GUID {self.user_guid}", f". Username: {self.username}, rank: {self.rank}", chat=default_chat)
        else:
            LogFire.log("USER", f"User created with GUID {self.user_guid}", f". Username: {self.username}, rank: {self.rank}")  # fallback
    
    @property
    def user_guid_str(self) -> str:
        """Get user GUID as string for compatibility"""
        return str(self.user_guid)
    
    def get_available_vector_stores(self):
        match self.rank:
            case PERMISSION_LEVELS.NONE:
                return None
            case PERMISSION_LEVELS.GUEST:
                return None
            case PERMISSION_LEVELS.USER:
                return ["user_vectors"]  # Example return value
            case PERMISSION_LEVELS.ADMIN:
                return ["user_vectors", "admin_vectors"]  # Example return value
            case _:
                return None  # Default case
    
    # Request Management Methods
    def add_request(self, request: "LongRunningZairaRequest") -> None:
        """Add a new request to the user's request list"""
        self.my_requests[request.scheduled_guid] = request
        LogFire.log("EVENT", f"Added request {request.scheduled_guid} to user {self.username}. Total requests: {len(self.my_requests)}")
    
    def remove_request(self, scheduled_guid: UUID) -> bool:
        """Remove a request by its GUID. Returns True if request was found and removed."""
        if scheduled_guid in self.my_requests:
            removed_request = self.my_requests.pop(scheduled_guid)
            LogFire.log("EVENT", f"Removed request {scheduled_guid} from user {self.username}. Remaining requests: {len(self.my_requests)}")
            return True
        return False
    
    def get_request_by_guid(self, scheduled_guid: UUID) -> Optional["LongRunningZairaRequest"]:
        """Get a request by its GUID"""
        return self.my_requests.get(scheduled_guid)
    
    def has_active_requests(self) -> bool:
        """Check if user has any active requests"""
        return len(self.my_requests) > 0
    
    def get_active_hitl_request(self) -> Optional["LongRunningZairaRequest"]:
        """Get the first request that is waiting for human-in-the-loop interaction"""
        for request in self.my_requests.values():
            if hasattr(request, 'human_in_the_loop_callback') and request.human_in_the_loop_callback is not None:
                return request
        return None

    async def on_message(self, complete_message: str, calling_bot: "MyBot_Generic", attachments: list = [], original_message = None, reply_context: Optional["ReplyContext"] = None, run_in_background: bool = False) -> "LongRunningZairaRequest":
        # Enhanced multimodal processing for attachments
        attachment_context = ""
        
        if len(attachments) > 0:
            attachment_context = await self._process_attachments_multimodal(attachments)
            
            # Add processed attachment context to the message
            if attachment_context:
                complete_message += f"\n\nAttachment Analysis:\n{attachment_context}"
        
        # Process reply context and identify target chat session
        target_session_guid = self.session_guid  # Default to current session
        
        if reply_context and hasattr(reply_context, 'is_reply') and reply_context.is_reply:
            # Search for matching chat session if we have replied message content
            if hasattr(reply_context, 'replied_message_content') and reply_context.replied_message_content:
                for session_guid, chat_session in self.chat_history.items():
                    if chat_session.find_message_by_content(reply_context.replied_message_content):
                        target_session_guid = session_guid
                        break
            # If no matching chat found, stay with current session (self.session_guid)
        
        # Check if this is a human-in-the-loop response for any active request
        hitl_request = self.get_active_hitl_request()
        is_hitl_response = hitl_request is not None
        
        # Only add to chat history if it's not a human-in-the-loop response and message is not empty
        if not is_hitl_response and complete_message and complete_message.strip():
            message = ZairaMessage.create_user_message(
                complete_message, 
                self.session_guid, 
                self.session_guid
            )
            self.add_to_chat_history(message, target_session_guid)
            
        # Add reply context to the message for AI understanding
        if reply_context and hasattr(reply_context, 'format_reply_context_for_ai'):
            complete_message += reply_context.format_reply_context_for_ai()
        
        # Route message to appropriate request
        target_request = None
        
        # Check for human-in-the-loop request
        if not target_request and is_hitl_response:
            target_request = hitl_request
            LogFire.log("DEBUG", f"Routing to HITL request {target_request.scheduled_guid}", severity="debug")
        
        # If we have a target request, route to it; otherwise start a new request
        if target_request:
            await target_request.on_message(complete_message=complete_message, calling_bot=calling_bot, original_message=original_message, reply_context=reply_context)
            return target_request
        else:
            # No specific request target, start a new request
            target_request = await self.start_request(complete_message=complete_message, calling_bot=calling_bot, original_message=original_message, reply_context=reply_context, run_in_background=run_in_background)
            return target_request
    
    async def _process_attachments_multimodal(self, attachments: List[str]) -> str:
        """
        Process attachments with multimodal AI capabilities.
        Detects images and processes them using vision models.
        """
        try:
            processed_content = []
            
            for attachment_path in attachments:
                if await self._is_image_file(attachment_path):
                    # Process image with multimodal AI
                    image_analysis = await self._process_image_attachment(attachment_path)
                    processed_content.append(image_analysis)
                else:
                    # Process non-image files with existing unstructured approach
                    try:
                        document_content = partition(filename=attachment_path)
                        if document_content:
                            processed_content.append(f"**Document: {Path(attachment_path).name}**\n{document_content}")
                    except Exception as e:
                        from etc.helper_functions import exception_triggered
                        # Get current chat session for logging
                        chat_session = self.get_current_chat_session()
                        exception_triggered(e, f"Processing non-image attachment: {attachment_path}", chat_session)
                        processed_content.append(f"**File: {Path(attachment_path).name}** - Could not process content")
            
            return "\n\n".join(processed_content)
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            # Get current chat session for logging
            chat_session = self.get_current_chat_session()
            exception_triggered(e, "Processing attachments with multimodal AI", chat_session)
            return "Error processing attachments"
    
    async def _is_image_file(self, file_path: str) -> bool:
        """
        Check if a file is an image based on its extension.
        """
        try:
            image_extensions = {".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".webp"}
            file_extension = Path(file_path).suffix.lower()
            return file_extension in image_extensions
        except Exception:
            return False
    
    async def _process_image_attachment(self, image_path: str) -> str:
        """
        Process an image attachment using the MultimodalManager.
        """
        try:
            # Generate a unique document ID for this image
            doc_guid = str(uuid4())
            
            # Use MultimodalManager to generate image summary
            image_summary = await MultimodalManager.generate_image_summary(
                image_path=image_path,
                context=f"Image sent by user {self.username} in Discord chat"
            )
            
            # Format the response
            filename = Path(image_path).name
            return f"**Image: {filename}**\n{image_summary}"
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            # Get current chat session for logging
            chat_session = self.get_current_chat_session()
            exception_triggered(e, f"Processing image attachment: {image_path}", chat_session)
            return f"**Image: {Path(image_path).name}** - Could not analyze image content"

    async def start_request(self, complete_message: str, calling_bot: "MyBot_Generic", original_message = None, reply_context: Optional["ReplyContext"] = None, scheduled_guid: Optional[UUID] = None, run_in_background: bool = False) -> "LongRunningZairaRequest":
        new_request = LongRunningZairaRequest(user=self, complete_message=complete_message, calling_bot=calling_bot, original_message=original_message, scheduled_guid=scheduled_guid, run_in_background=run_in_background)
        
        # Add request to the user's request list
        self.add_request(new_request)
        
        # Execute request based on background mode
        try:
            if run_in_background:
                # Use background execution pattern extracted from ScheduledZairaRequest
                await new_request.run_request_in_background()
                # For background execution, we don't wait for completion
                # The background tasks handle execution and monitoring
                chat_session = self.get_current_chat_session()
                LogFire.log("TASK", f"Background request started for user {self.username}", chat=chat_session)
            else:
                # Use existing async execution (default behavior)
                # Always use non-blocking approach to prevent main loop freezing
                # Previous debug mode blocking was causing scheduled requests to freeze the system
                        #  if Globals.is_debug() == True and calling_bot.parent_instance == None:
                        #      await new_request.run_request()
                        #  else:
                self.asyncio_Task = asyncio.create_task(new_request.run_request())
                self.asyncio_Task.add_done_callback(etc.helper_functions.handle_asyncio_task_result_errors)
                # Tell the system that we'd like to get the result back through the output supervisor
                await new_request.await_status_complete(wait_on_complete=False)
            return new_request
        except Exception as e:
            etc.helper_functions.exception_triggered(e)
            await calling_bot.send_reply("Zaira heeft haar best gedaan maar is tot de conclusie gekomen dat ze hulp nodig heeft. Indien nodig stuur dan een <NAME_EMAIL>", self, original_message, False)
            # Remove the failed request from the list
            self.remove_request(new_request.scheduled_guid)
            return new_request

    def get_chat_history(self, typing="LangChain", session_guid: UUID = None) -> list:
        if not session_guid:
            session_guid = self.session_guid
        history = []
        if session_guid in self.chat_history:
            chat_session = self.chat_history[session_guid]
            if chat_session.message_count > 0:
                if typing.lower() == "langchain":
                    history = chat_session.to_langchain_messages()
        return history
    
    def add_to_chat_history(self, content: ZairaMessage, session_guid: UUID = None):
        # Get or create chat session
        if not session_guid:
            session_guid = self.session_guid
        if session_guid not in self.chat_history:
            from userprofiles.ZairaChat import ZairaChat
            from datetime import datetime
            timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")
            self.chat_history[session_guid] = ZairaChat(
                session_guid=session_guid,
                user_guid=str(self.user_guid),
                conversation_guid=session_guid,
                title=f"Auto-Created Session - {timestamp}"
            )
            self.receive_debug_message("Incorrectly created a ZairaChat through add_to_chat_history.", session_guid)
        self.chat_history[session_guid].add_message(content)
    
    def receive_debug_message(self, content: str, session_guid: UUID = None, call_trace: Optional[list[str]] = None):
        if Globals.is_debug_values():
            # Use provided session_guid or fall back to user's active session
            target_session = session_guid if session_guid else self.session_guid
            debug_message = ZairaMessage.create_debug_message(
                content=content,
                conversation_id=target_session, 
                session_id=target_session,
                call_trace=call_trace
            )
            self.add_to_chat_history(debug_message, target_session)
    
    def receive_logging_message(self, content: str, session_guid: UUID = None, call_trace: Optional[list[str]] = None):
        if Globals.is_debug_values():
            # Use provided session_guid or fall back to user's active session
            target_session = session_guid if session_guid else self.session_guid
            debug_message = ZairaMessage.create_logging_message(
                content=content,
                conversation_id=target_session, 
                session_id=target_session,
                call_trace=call_trace
            )
            self.add_to_chat_history(debug_message, target_session)

    async def get_supported_image_formats(self) -> List[str]:
        """
        Get list of supported image formats for multimodal processing.
        """
        return [".png", ".jpg", ".jpeg", ".gif", ".bmp", ".tiff", ".webp"]
    
    async def is_multimodal_enabled(self) -> bool:
        """
        Check if multimodal processing is enabled for this user.
        """
        try:
            # Check user permissions and system configuration
            if self.rank == PERMISSION_LEVELS.NONE or self.rank == PERMISSION_LEVELS.GUEST:
                return False
            
            # Ensure MultimodalManager is set up
            await MultimodalManager.setup()
            return True
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            # Get current chat session for logging
            chat_session = self.get_current_chat_session()
            exception_triggered(e, "Checking multimodal capabilities", chat_session)
            return False
    
    def get_current_chat_session(self) -> Optional[ZairaChat]:
        """Get the current chat session."""
        return self.chat_history.get(self.session_guid)
    
    def create_new_chat_session(self) -> ZairaChat:
        """Create a new chat session and set it as current."""
        from datetime import datetime
        new_session_guid = uuid4()
        timestamp = datetime.now().strftime("%Y-%m-%d %H:%M")
        new_chat = ZairaChat(
            session_guid=new_session_guid,
            user_guid=str(self.user_guid),
            conversation_guid=new_session_guid,
            title=f"Chat Session - {timestamp}"
        )
        self.chat_history[new_session_guid] = new_chat
        self.session_guid = new_session_guid
        return new_chat
    
    def switch_chat_session(self, session_guid: UUID) -> bool:
        """Switch to a different chat session."""
        if session_guid in self.chat_history:
            self.session_guid = session_guid
            return True
        return False
    

# Import after class definition to avoid circular import during class definition
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest

# Model rebuild handled in userprofiles/__init__.py after all classes are defined
