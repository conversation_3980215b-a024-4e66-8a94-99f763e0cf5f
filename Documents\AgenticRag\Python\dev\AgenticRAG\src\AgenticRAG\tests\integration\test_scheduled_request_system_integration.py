from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from uuid import uuid4
import asyncio
from datetime import datetime, timezone, timedelta

from managers.scheduled_requests.integration_adapter import ScheduledRequestIntegrationAdapter
from managers.scheduled_requests.core.factory import ScheduledRequestManagerFactory
from managers.scheduled_requests.core.user_manager import UserScheduledRequestManager
from managers.scheduled_requests.core.persistence import ScheduledRequestPersistenceManager
from managers.scheduled_requests.security.validator import ScheduledRequestSecurityValidator
from managers.scheduled_requests.resources.rate_limiter import SystemRateLimiter
from managers.scheduled_requests.resources.quota_manager import SystemQuotaManager
from managers.scheduled_requests.utils.config import UserQuotaConfig, RateLimitConfig
from managers.scheduled_requests.utils.exceptions import UserQuotaExceededError, UserRateLimitExceededError
from userprofiles.ZairaUser import <PERSON>airaUser, PERMISSION_LEVELS
from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest, ScheduleType

@pytest.mark.integration
class TestScheduledRequestSystemIntegration:
    """Integration tests for the complete scheduled request system"""
    
    def setup_method(self):
        """Set up test fixtures"""
        # Reset all singletons
        ScheduledRequestIntegrationAdapter._instance = None
        ScheduledRequestIntegrationAdapter._initialized = False
        ScheduledRequestManagerFactory._instance = None
        ScheduledRequestManagerFactory._initialized = False
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        self.test_user_guid = uuid4()
        self.test_user_guid_2 = uuid4()
        self.test_scheduled_guid = uuid4()
        
        # Create test users
        self.test_user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=self.test_user_guid,
            device_guid=uuid4()
        )
        
        self.test_admin_user = ZairaUser(
            username="admin_user",
            rank=PERMISSION_LEVELS.ADMIN,
            guid=self.test_user_guid_2,
            device_guid=uuid4()
        )
        
        # Create test scheduled request
        self.test_scheduled_request = Mock(spec=ScheduledZairaRequest)
        self.test_scheduled_request.user = self.test_user
        self.test_scheduled_request.scheduled_guid = self.test_scheduled_guid
        self.test_scheduled_request.schedule_prompt = "Test schedule prompt"
        self.test_scheduled_request.target_prompt = "Test target prompt"
        self.test_scheduled_request.delay_seconds = 300.0
        self.test_scheduled_request.start_delay_seconds = 60.0
        self.test_scheduled_request.schedule_type = Mock()
        self.test_scheduled_request.schedule_type.value = "once"
        self.test_scheduled_request.next_execution = datetime.now(timezone.utc) + timedelta(hours=1)
        self.test_scheduled_request.is_active = True
        self.test_scheduled_request.run_on_startup = False
        self.test_scheduled_request.calling_bot = Mock()
        self.test_scheduled_request.calling_bot.name = "TestBot"
        self.test_scheduled_request.original_physical_message = None
        self.test_scheduled_request.get_schedule_info.return_value = {"type": "test"}
        self.test_scheduled_request.get_request_status.return_value = {"status": "pending"}
        self.test_scheduled_request.cancel_schedule = Mock()
        
    def teardown_method(self):
        """Clean up after each test"""
        # Reset all singletons
        ScheduledRequestIntegrationAdapter._instance = None
        ScheduledRequestIntegrationAdapter._initialized = False
        ScheduledRequestManagerFactory._instance = None
        ScheduledRequestManagerFactory._initialized = False
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
    
    @pytest.mark.asyncio
    async def test_complete_system_initialization(self):
        """Test complete system initialization"""
        with patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.setup') as mock_persistence_setup, \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_quota_config, \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_rate_limit_config') as mock_rate_config:
            
            # Mock configurations
            mock_quota_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5, daily_task_limit=50, memory_limit_mb=100,
                thread_pool_size=3, max_task_duration_hours=24, max_recurring_tasks=10
            )
            mock_rate_config.return_value = RateLimitConfig(
                requests_per_minute=10, requests_per_hour=100, burst_limit=5
            )
            
            # Initialize the complete system
            adapter = await get_integration_adapter()
            
            assert adapter is not None
            assert isinstance(adapter, ScheduledRequestIntegrationAdapter)
            mock_persistence_setup.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_end_to_end_request_creation_and_cancellation(self):
        """Test complete end-to-end request creation and cancellation flow"""
        with patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.setup'), \
             patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.save_task', return_value=True), \
             patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.cancel_task', return_value=True), \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_quota_config, \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_rate_limit_config') as mock_rate_config:
            
            # Mock configurations
            mock_quota_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5, daily_task_limit=50, memory_limit_mb=100,
                thread_pool_size=3, max_task_duration_hours=24, max_recurring_tasks=10
            )
            mock_rate_config.return_value = RateLimitConfig(
                requests_per_minute=10, requests_per_hour=100, burst_limit=5
            )
            
            # Get system adapter
            adapter = await get_integration_adapter()
            
            # Create scheduled request
            success = await adapter.create_scheduled_request(self.test_scheduled_request)
            assert success is True
            
            # Verify user manager was created
            factory = ScheduledRequestManagerFactory.get_instance()
            assert factory.get_active_manager_count() == 1
            
            # Get user manager and verify request was added
            user_manager = await factory.get_user_manager(str(self.test_user_guid), self.test_user.rank)
            assert user_manager.get_active_request_count() == 1
            
            # Cancel the request
            success = await adapter.cancel_scheduled_request(
                str(self.test_scheduled_guid), 
                str(self.test_user_guid), 
                "Integration test cancellation"
            )
            assert success is True
            
            # Verify request was removed from user manager
            assert user_manager.get_active_request_count() == 0
    
    @pytest.mark.asyncio
    async def test_security_validation_integration(self):
        """Test security validation integration with request creation"""
        with patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.setup'), \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_quota_config, \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_rate_limit_config') as mock_rate_config:
            
            # Mock configurations
            mock_quota_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5, daily_task_limit=50, memory_limit_mb=100,
                thread_pool_size=3, max_task_duration_hours=24, max_recurring_tasks=10
            )
            mock_rate_config.return_value = RateLimitConfig(
                requests_per_minute=10, requests_per_hour=100, burst_limit=5
            )
            
            # Create malicious request
            malicious_request = Mock(spec=ScheduledZairaRequest)
            malicious_request.user = self.test_user
            malicious_request.scheduled_guid = uuid4()
            malicious_request.schedule_prompt = "<script>alert('xss')</script>"  # Malicious content
            malicious_request.target_prompt = "Test target"
            malicious_request.delay_seconds = 300.0
            malicious_request.start_delay_seconds = 60.0
            malicious_request.schedule_type = Mock()
            malicious_request.schedule_type.value = "once"
            malicious_request.next_execution = datetime.now(timezone.utc) + timedelta(hours=1)
            malicious_request.is_active = True
            malicious_request.run_on_startup = False
            malicious_request.calling_bot = Mock()
            malicious_request.calling_bot.name = "TestBot"
            malicious_request.get_schedule_info.return_value = {"type": "test"}
            malicious_request.get_request_status.return_value = {"status": "pending"}
            
            # Get system adapter
            adapter = await get_integration_adapter()
            
            # Attempt to create malicious request - should be rejected by security validation
            success = await adapter.create_scheduled_request(malicious_request)
            assert success is False
    
    @pytest.mark.asyncio
    async def test_quota_enforcement_integration(self):
        """Test quota enforcement integration across the system"""
        with patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.setup'), \
             patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.save_task', return_value=True), \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_quota_config, \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_rate_limit_config') as mock_rate_config:
            
            # Mock very restrictive quota config
            mock_quota_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=1,  # Very low limit
                daily_task_limit=2,      # Very low limit
                memory_limit_mb=50,      # Low limit
                thread_pool_size=1,
                max_task_duration_hours=24,
                max_recurring_tasks=1
            )
            mock_rate_config.return_value = RateLimitConfig(
                requests_per_minute=10, requests_per_hour=100, burst_limit=5
            )
            
            # Get system adapter
            adapter = await get_integration_adapter()
            
            # Create first request (should succeed)
            success = await adapter.create_scheduled_request(self.test_scheduled_request)
            assert success is True
            
            # Create second request (should succeed, within daily limit)
            second_request = Mock(spec=ScheduledZairaRequest)
            second_request.user = self.test_user
            second_request.scheduled_guid = uuid4()
            second_request.schedule_prompt = "Second test prompt"
            second_request.target_prompt = "Second test target"
            second_request.delay_seconds = 300.0
            second_request.start_delay_seconds = 60.0
            second_request.schedule_type = Mock()
            second_request.schedule_type.value = "once"
            second_request.next_execution = datetime.now(timezone.utc) + timedelta(hours=2)
            second_request.is_active = True
            second_request.run_on_startup = False
            second_request.calling_bot = Mock()
            second_request.calling_bot.name = "TestBot"
            second_request.get_schedule_info.return_value = {"type": "test"}
            second_request.get_request_status.return_value = {"status": "pending"}
            
            success = await adapter.create_scheduled_request(second_request)
            assert success is True
            
            # Attempt third request (should fail due to concurrent limit)
            third_request = Mock(spec=ScheduledZairaRequest)
            third_request.user = self.test_user
            third_request.scheduled_guid = uuid4()
            third_request.schedule_prompt = "Third test prompt"
            third_request.target_prompt = "Third test target"
            third_request.delay_seconds = 300.0
            third_request.start_delay_seconds = 60.0
            third_request.schedule_type = Mock()
            third_request.schedule_type.value = "once"
            third_request.next_execution = datetime.now(timezone.utc) + timedelta(hours=3)
            third_request.is_active = True
            third_request.run_on_startup = False
            third_request.calling_bot = Mock()
            third_request.calling_bot.name = "TestBot"
            third_request.get_schedule_info.return_value = {"type": "test"}
            third_request.get_request_status.return_value = {"status": "pending"}
            
            success = await adapter.create_scheduled_request(third_request)
            assert success is False  # Should fail due to quota limits
    
    @pytest.mark.asyncio
    async def test_rate_limiting_integration(self):
        """Test rate limiting integration across the system"""
        with patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.setup'), \
             patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.save_task', return_value=True), \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_quota_config, \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_rate_limit_config') as mock_rate_config:
            
            # Mock configurations
            mock_quota_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=10, daily_task_limit=100, memory_limit_mb=500,
                thread_pool_size=5, max_task_duration_hours=24, max_recurring_tasks=20
            )
            # Very restrictive rate limit
            mock_rate_config.return_value = RateLimitConfig(
                requests_per_minute=2,  # Very low
                requests_per_hour=10,
                burst_limit=1  # Very low
            )
            
            # Get system adapter
            adapter = await get_integration_adapter()
            
            # Create first request (should succeed)
            success = await adapter.create_scheduled_request(self.test_scheduled_request)
            assert success is True
            
            # Create second request immediately (should fail due to burst limit)
            second_request = Mock(spec=ScheduledZairaRequest)
            second_request.user = self.test_user
            second_request.scheduled_guid = uuid4()
            second_request.schedule_prompt = "Second test prompt"
            second_request.target_prompt = "Second test target"
            second_request.delay_seconds = 300.0
            second_request.start_delay_seconds = 60.0
            second_request.schedule_type = Mock()
            second_request.schedule_type.value = "once"
            second_request.next_execution = datetime.now(timezone.utc) + timedelta(hours=2)
            second_request.is_active = True
            second_request.run_on_startup = False
            second_request.calling_bot = Mock()
            second_request.calling_bot.name = "TestBot"
            second_request.get_schedule_info.return_value = {"type": "test"}
            second_request.get_request_status.return_value = {"status": "pending"}
            
            success = await adapter.create_scheduled_request(second_request)
            assert success is False  # Should fail due to rate limiting
    
    @pytest.mark.asyncio
    async def test_multi_user_isolation(self):
        """Test that users are properly isolated from each other"""
        with patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.setup'), \
             patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.save_task', return_value=True), \
             patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.get_active_requests') as mock_get_requests, \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_quota_config, \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_rate_limit_config') as mock_rate_config:
            
            # Mock configurations
            mock_quota_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5, daily_task_limit=50, memory_limit_mb=100,
                thread_pool_size=3, max_task_duration_hours=24, max_recurring_tasks=10
            )
            mock_rate_config.return_value = RateLimitConfig(
                requests_per_minute=10, requests_per_hour=100, burst_limit=5
            )
            
            # Mock get_active_requests to return user-specific results
            def mock_get_requests_side_effect(user_guid=None):
                if user_guid == str(self.test_user_guid):
                    return [{'scheduled_guid': str(self.test_scheduled_guid), 'user_guid': str(self.test_user_guid)}]
                elif user_guid == str(self.test_user_guid_2):
                    return [{'scheduled_guid': str(uuid4()), 'user_guid': str(self.test_user_guid_2)}]
                return []
            
            mock_get_requests.side_effect = mock_get_requests_side_effect
            
            # Get system adapter
            adapter = await get_integration_adapter()
            
            # Create request for first user
            success = await adapter.create_scheduled_request(self.test_scheduled_request)
            assert success is True
            
            # Create request for second user
            admin_request = Mock(spec=ScheduledZairaRequest)
            admin_request.user = self.test_admin_user
            admin_request.scheduled_guid = uuid4()
            admin_request.schedule_prompt = "Admin test prompt"
            admin_request.target_prompt = "Admin test target"
            admin_request.delay_seconds = 300.0
            admin_request.start_delay_seconds = 60.0
            admin_request.schedule_type = Mock()
            admin_request.schedule_type.value = "once"
            admin_request.next_execution = datetime.now(timezone.utc) + timedelta(hours=1)
            admin_request.is_active = True
            admin_request.run_on_startup = False
            admin_request.calling_bot = Mock()
            admin_request.calling_bot.name = "AdminBot"
            admin_request.get_schedule_info.return_value = {"type": "admin"}
            admin_request.get_request_status.return_value = {"status": "pending"}
            
            success = await adapter.create_scheduled_request(admin_request)
            assert success is True
            
            # Verify separate user managers were created
            factory = ScheduledRequestManagerFactory.get_instance()
            assert factory.get_active_manager_count() == 2
            
            # Get user-specific requests
            user1_requests = await adapter.get_user_requests(str(self.test_user_guid))
            user2_requests = await adapter.get_user_requests(str(self.test_user_guid_2))
            
            # Verify isolation - each user only sees their own requests
            assert len(user1_requests) == 1
            assert user1_requests[0]['user_guid'] == str(self.test_user_guid)
            
            assert len(user2_requests) == 1
            assert user2_requests[0]['user_guid'] == str(self.test_user_guid_2)
    
    @pytest.mark.asyncio
    async def test_system_cleanup_and_shutdown(self):
        """Test system cleanup and shutdown procedures"""
        with patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.setup'), \
             patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.save_task', return_value=True), \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_quota_config, \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_rate_limit_config') as mock_rate_config:
            
            # Mock configurations
            mock_quota_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5, daily_task_limit=50, memory_limit_mb=100,
                thread_pool_size=3, max_task_duration_hours=24, max_recurring_tasks=10
            )
            mock_rate_config.return_value = RateLimitConfig(
                requests_per_minute=10, requests_per_hour=100, burst_limit=5
            )
            
            # Get system adapter and create some requests
            adapter = await get_integration_adapter()
            
            # Create multiple requests
            await adapter.create_scheduled_request(self.test_scheduled_request)
            
            # Verify managers were created
            factory = ScheduledRequestManagerFactory.get_instance()
            assert factory.get_active_manager_count() > 0
            
            # Shutdown the system
            await adapter.shutdown()
            
            # Verify all managers were cleaned up
            assert factory.get_active_manager_count() == 0
    
    @pytest.mark.asyncio
    async def test_error_recovery_and_resilience(self):
        """Test system error recovery and resilience"""
        with patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.setup'), \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_quota_config, \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_rate_limit_config') as mock_rate_config:
            
            # Mock configurations
            mock_quota_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5, daily_task_limit=50, memory_limit_mb=100,
                thread_pool_size=3, max_task_duration_hours=24, max_recurring_tasks=10
            )
            mock_rate_config.return_value = RateLimitConfig(
                requests_per_minute=10, requests_per_hour=100, burst_limit=5
            )
            
            # Get system adapter
            adapter = await get_integration_adapter()
            
            # Test persistence failure recovery
            with patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.save_task', return_value=False):
                success = await adapter.create_scheduled_request(self.test_scheduled_request)
                assert success is False  # Should handle gracefully
            
            # Test that system continues to work after failure
            with patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.save_task', return_value=True):
                success = await adapter.create_scheduled_request(self.test_scheduled_request)
                assert success is True  # Should recover and work normally
    
    @pytest.mark.asyncio
    async def test_concurrent_request_handling(self):
        """Test concurrent request handling across the system"""
        with patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.setup'), \
             patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.save_task', return_value=True), \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_quota_config, \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_rate_limit_config') as mock_rate_config:
            
            # Mock configurations with high limits for concurrent testing
            mock_quota_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=20, daily_task_limit=100, memory_limit_mb=500,
                thread_pool_size=10, max_task_duration_hours=24, max_recurring_tasks=50
            )
            mock_rate_config.return_value = RateLimitConfig(
                requests_per_minute=50, requests_per_hour=1000, burst_limit=20
            )
            
            # Get system adapter
            adapter = await get_integration_adapter()
            
            # Create multiple concurrent requests
            async def create_request(request_id: int):
                request = Mock(spec=ScheduledZairaRequest)
                request.user = self.test_user
                request.scheduled_guid = uuid4()
                request.schedule_prompt = f"Concurrent test prompt {request_id}"
                request.target_prompt = f"Concurrent test target {request_id}"
                request.delay_seconds = 300.0
                request.start_delay_seconds = 60.0
                request.schedule_type = Mock()
                request.schedule_type.value = "once"
                request.next_execution = datetime.now(timezone.utc) + timedelta(hours=1)
                request.is_active = True
                request.run_on_startup = False
                request.calling_bot = Mock()
                request.calling_bot.name = "TestBot"
                request.get_schedule_info.return_value = {"type": "test", "id": request_id}
                request.get_request_status.return_value = {"status": "pending"}
                
                return await adapter.create_scheduled_request(request)
            
            # Create 10 concurrent requests
            tasks = [asyncio.create_task(create_request(i)) for i in range(10)]
            results = await asyncio.gather(*tasks)
            
            # All requests should succeed
            assert all(results)
            
            # Verify user manager was created and has the expected number of requests
            factory = ScheduledRequestManagerFactory.get_instance()
            user_manager = await factory.get_user_manager(str(self.test_user_guid), self.test_user.rank)
            assert user_manager.get_active_request_count() == 10
    
    @pytest.mark.asyncio
    async def test_system_metrics_and_monitoring(self):
        """Test system metrics and monitoring integration"""
        with patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.setup'), \
             patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager.save_task', return_value=True), \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_quota_config, \
             patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_rate_limit_config') as mock_rate_config:
            
            # Mock configurations
            mock_quota_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5, daily_task_limit=50, memory_limit_mb=100,
                thread_pool_size=3, max_task_duration_hours=24, max_recurring_tasks=10
            )
            mock_rate_config.return_value = RateLimitConfig(
                requests_per_minute=10, requests_per_hour=100, burst_limit=5
            )
            
            # Get system adapter
            adapter = await get_integration_adapter()
            
            # Create some requests to generate metrics
            await adapter.create_scheduled_request(self.test_scheduled_request)
            
            # Get system overview
            overview = await adapter.get_system_overview()
            
            assert 'active_managers' in overview
            assert 'total_active_requests' in overview
            assert 'system_health' in overview
            
            assert overview['active_managers'] >= 1
            assert overview['total_active_requests'] >= 1
            
            # Get user-specific metrics
            user_metrics = await adapter.get_user_metrics(str(self.test_user_guid))
            
            assert 'quota_usage' in user_metrics
            assert 'rate_limit_status' in user_metrics
            assert 'active_requests_count' in user_metrics
            
            assert user_metrics['active_requests_count'] >= 1

# Helper function to get integration adapter (similar to the one in integration_adapter.py)
async def get_integration_adapter() -> ScheduledRequestIntegrationAdapter:
    """Get or create the integration adapter"""
    return await ScheduledRequestIntegrationAdapter.get_instance()