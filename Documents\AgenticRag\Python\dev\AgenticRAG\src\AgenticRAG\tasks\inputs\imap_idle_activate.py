from imports import *

from os import getcwd, path as os_path, makedirs
from managers.manager_supervisors import Super<PERSON><PERSON>ana<PERSON>, SupervisorTask_Base, SupervisorTaskState
from endpoints.oauth._verifier_ import OAuth2Verifier
from endpoints.mybot_generic import MyBot_Generic
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import ZairaUser
from datetime import datetime
import imaplib
import email
import time
import socket
import asyncio
from typing import Optional, Dict
from langgraph.types import Command

class SupervisorTask_IMAPIdleActivate(SupervisorTask_Base):
    """
    IMAP email monitoring task for a SINGLE USER SESSION.
    Runs continuously with IMAP IDLE for exactly 30 minutes, then terminates.
    Processes emails immediately upon arrival during the 30-minute session.
    External code is responsible for creating new tasks every 30 minutes.
    """

    async def llm_call(self, state: SupervisorTaskState):
        """
        Start a 30-minute IMAP IDLE monitoring session for this user.
        Task will automatically terminate after 30 minutes.
        """
        # Get the correct chat session from state (isolated session for scheduled requests)
        chat_session = await SupervisorTask_Base.get_chat_session_from_state(state)
        user = await ZairaUserManager.find_user(state.user_guid)
        
        LogFire.log("IMAP IDLE", f"Task called with state: user_guid={state.user_guid}", chat=chat_session)
        
        # Initialize call trace for this task
        if not hasattr(state, 'call_trace') or state.call_trace is None:
            state.call_trace = []
        
        # Add initial call trace entry
        state.call_trace.append(f"{self.name}: start_imap_session")
        
        LogFire.log("IMAP IDLE", f"Found user: {user is not None}", chat=chat_session)
        
        # Check if user was found
        if not user:
            LogFire.log("ERROR", f"User not found for IMAP IDLE task: {state.user_guid}", chat=chat_session)
            state.call_trace.append(f"{self.name}: user_not_found")
            return f"User not found: {state.user_guid}"
        
        LogFire.log("IMAP IDLE", f"Starting task for user: {state.user_guid}", chat=chat_session)
        state.call_trace.append(f"{self.name}: user_validated")
        
        # DIRECT FIX: Update any running execution task's status with call traces
        try:
            # Find the current LongRunningZairaRequest for this scheduled_guid
            if hasattr(state, 'scheduled_guid') and state.scheduled_guid:
                user_obj = await ZairaUserManager.find_user(state.user_guid)
                if user_obj and hasattr(user_obj, 'my_requests'):
                    # Find the specific request that matches our scheduled_guid
                    target_request = None
                    for request_guid, request in user_obj.my_requests.items():
                        if hasattr(request, 'scheduled_guid') and str(request.scheduled_guid) == str(state.scheduled_guid):
                            target_request = request
                            break
                    
                    if target_request:
                        # Update request attributes with current call traces
                        target_request.call_trace = list(state.call_trace)  # Copy the list
                        target_request.call_trace_updated_at = time.time()
        except Exception as e:
            pass  # Non-critical
        
        # This task handles only one user for one 30-minute session
        result = await self._run_30_minute_session(user, state)
        
        # Add completion to call trace
        state.call_trace.append(f"{self.name}: session_completed")
        
        # Return result - the base class wrapper will handle call_trace and Command
        return result
    
    async def _run_30_minute_session(self, user, state: SupervisorTaskState) -> str:
        """
        Run a single 30-minute IMAP IDLE monitoring session.
        Task terminates automatically after 30 minutes.
        """
        session_start_time = time.time()
        timeout_seconds = 30 * 60  # Exactly 30 minutes
        user_guid = user.user_guid
        # Use the correct session from state (isolated for scheduled requests)
        chat_session = await SupervisorTask_Base.get_chat_session_from_state(state)
        
        try:
            LogFire.log("IMAP IDLE", f"Starting 30-minute session for user {user_guid}", chat=chat_session)
            state.call_trace.append(f"{self.name}: session_starting")
            
            # Get IMAP configuration
            config = await self._get_imap_config()
            if not config:
                state.call_trace.append(f"{self.name}: config_failed")
                return "Failed to get IMAP configuration - session terminated"
            
            state.call_trace.append(f"{self.name}: config_loaded")
            
            mail_connection = None
            emails_processed = 0
            
            # Main monitoring loop - runs for exactly 30 minutes
            while True:
                # Check if 30 minutes have elapsed
                elapsed_time = time.time() - session_start_time
                if elapsed_time >= timeout_seconds:
                    LogFire.log("IMAP IDLE", f"30-minute session completed for user {user_guid}. Processed {emails_processed} emails.", chat=chat_session)
                    break
                
                try:
                    # Establish connection if needed
                    if mail_connection is None:
                        state.call_trace.append(f"{self.name}: connecting")
                        mail_connection = await self._establish_imap_connection(config)
                        if mail_connection is None:
                            LogFire.log("ERROR", "Failed to establish IMAP connection - session terminated", chat=chat_session)
                            state.call_trace.append(f"{self.name}: connection_failed")
                            break
                        state.call_trace.append(f"{self.name}: connected")
                    
                    # Calculate remaining time for this IDLE session
                    remaining_time = timeout_seconds - elapsed_time
                    idle_duration = min(29 * 60, remaining_time - 60)  # Leave 1 minute buffer
                    
                    if idle_duration <= 0:
                        LogFire.log("IMAP IDLE", f"Insufficient time remaining - terminating session for user {user_guid}", chat=chat_session)
                        break
                    
                    # Start IMAP IDLE
                    LogFire.log("IMAP IDLE", f"Starting IDLE mode (duration: {idle_duration/60:.1f} minutes)", chat=chat_session)
                    state.call_trace.append(f"{self.name}: starting_idle_mode")
                    
                    # Go straight to IDLE mode
                    try:
                        LogFire.log("IMAP IDLE", "Attempting IDLE mode", chat=chat_session)
                        
                        # Try to start IDLE mode directly
                        idle_result = await self._attempt_idle_mode(mail_connection, user_guid, idle_duration)
                        state.call_trace.append(f"{self.name}: idle_mode_attempted")
                        
                        # Handle success (int) vs failure (tuple with timestamp)
                        if isinstance(idle_result, tuple):
                            # IDLE failed with timeout - idle_result is (processed_count, timeout_timestamp)
                            processed_count, timeout_timestamp = idle_result
                            emails_processed += processed_count
                            
                            LogFire.log("IMAP IDLE", "IDLE failed, reconnecting for gap check")
                            
                            # Close the timed-out connection
                            try:
                                mail_connection.close()
                                mail_connection.logout()
                            except:
                                pass
                            
                            # Re-establish connection for polling
                            mail_connection = await self._establish_imap_connection(config)
                            if mail_connection:
                                LogFire.log("IMAP GAP", "Reconnected successfully, performing one-time check for missed emails")
                                # Perform one-time check for emails that arrived during the timeout gap
                                processed_count = await self._gap_targeted_email_check(mail_connection, user_guid, timeout_timestamp, idle_duration)
                                emails_processed += processed_count
                            else:
                                LogFire.log("ERROR", "Failed to reconnect for polling fallback", chat=chat_session)
                                break
                        elif idle_result:
                            # IDLE succeeded - idle_result is processed count
                            emails_processed += idle_result
                    
                        # Skip the rest of the loop since we've handled the monitoring
                        continue
                        
                    except Exception as e:
                        LogFire.log("ERROR", f"IDLE setup failed: {str(e)}", chat=chat_session)
                        # Fall back to polling on any error
                        try:
                            processed_count = await self._periodic_email_check(mail_connection, user_guid, idle_duration)
                            emails_processed += processed_count
                        except Exception as e2:
                            LogFire.log("ERROR", f"Polling fallback also failed: {str(e2)}", chat=chat_session)
                        continue
                    
                    LogFire.log("IMAP SUCCESS", "IDLE started successfully, monitoring for emails...", chat=chat_session)
                    
                    # Monitor for new emails during IDLE
                    idle_start_time = time.time()
                    last_call_trace_update = 0
                    
                    while (time.time() - idle_start_time < idle_duration and 
                           time.time() - session_start_time < timeout_seconds):
                        
                        try:
                            
                            # Check for server response (new email notification)
                            mail_connection.sock.settimeout(2.0)  # 2 second timeout for better responsiveness
                            
                            # Read response from server
                            line = mail_connection.readline()
                            if line:
                                line_str = line.decode('utf-8', errors='ignore').strip()
                                
                                # Check for EXISTS notification (new email)
                                if 'EXISTS' in line_str:
                                    LogFire.log("IMAP EMAIL DETECTED", f"New email for user {user_guid}: {line_str}", chat=chat_session)
                                    
                                    # Exit IDLE mode
                                    mail_connection.send(b'DONE\r\n')
                                    resp = mail_connection.response('IDLE')
                                    
                                    # Process new emails immediately
                                    processed_count = await self._process_new_emails(mail_connection, user_guid)
                                    emails_processed += processed_count
                                    
                                    LogFire.log("IMAP PROCESSED", f"{processed_count} emails processed for user {user_guid}", chat=chat_session)
                                    
                                    # Break to restart IDLE
                                    break
                                
                                # Check for other status messages (no logging needed for maintenance operations)
                                    
                        except socket.timeout:
                            # Normal timeout - continue waiting
                            continue
                        except Exception as e:
                            LogFire.log("ERROR", f"Error during IDLE wait: {str(e)}", chat=chat_session)
                            break
                    
                    # Exit IDLE mode if still active
                    try:
                        mail_connection.send(b'DONE\r\n')
                        mail_connection.response('IDLE')
                    except:
                        pass
                    
                except Exception as e:
                    # Check if this is a normal session timeout
                    if time.time() - session_start_time >= timeout_seconds - 30:
                        LogFire.log("IMAP IDLE", f"Session completed normally after timeout for user {user_guid}", chat=chat_session)
                        break
                    
                    # Handle actual errors
                    error_str = str(e).lower()
                    if "timed out" in error_str or "timeout" in error_str:
                        LogFire.log("IMAP TIMEOUT", f"Connection timed out for user {user_guid}, re-establishing IDLE connection...", chat=chat_session)
                        
                        # Record timeout time for catch-up email check
                        timeout_occurred_time = time.time()
                        
                        # Close the timed-out connection
                        if mail_connection:
                            try:
                                mail_connection.close()
                                mail_connection.logout()
                            except:
                                pass
                            mail_connection = None
                        
                        # Re-establish connection immediately for catch-up check
                        mail_connection = await self._establish_imap_connection(config)
                        if mail_connection:
                            try:
                                LogFire.log("IMAP CATCH-UP", f"Checking for emails that arrived during timeout...", chat=chat_session)
                                
                                # Check for any emails that may have arrived during timeout
                                catch_up_processed = await self._catch_up_email_check(mail_connection, user_guid, timeout_occurred_time)
                                if catch_up_processed > 0:
                                    emails_processed += catch_up_processed
                                    LogFire.log("IMAP CATCH-UP", f"Found and processed {catch_up_processed} emails from timeout period for user {user_guid}!", chat=chat_session)
                                else:
                                    LogFire.log("IMAP CATCH-UP", "No emails found during timeout period", chat=chat_session)
                            except Exception as catch_up_error:
                                LogFire.log("IMAP CATCH-UP WARNING", f"Catch-up check failed: {catch_up_error}", chat=chat_session)
                        
                        # Continue the loop to re-establish IDLE monitoring
                        continue
                    
                    LogFire.log("ERROR", f"IMAP session error for user {user_guid}: {str(e)}", chat=chat_session)
                    
                    # Close problematic connection for non-timeout errors
                    if mail_connection:
                        try:
                            mail_connection.close()
                            mail_connection.logout()
                        except:
                            pass
                        mail_connection = None
                    
                    # For non-timeout errors, try once more then give up
                    if time.time() - session_start_time < timeout_seconds - 60:
                        await asyncio.sleep(30)  # 30 second delay
                    else:
                        break
            
            # Cleanup connection
            if mail_connection:
                try:
                    mail_connection.close()
                    mail_connection.logout()
                except:
                    pass
            
            final_elapsed = time.time() - session_start_time
            LogFire.log("IMAP IDLE", f"Session ended for user {user_guid} after {final_elapsed/60:.1f} minutes. Total emails processed: {emails_processed}", chat=chat_session)
            
            # Add final call trace entry with session summary
            state.call_trace.append(f"{self.name}: session_ended_{emails_processed}_emails")
            
            return f"IMAP IDLE session completed - ran for {final_elapsed/60:.1f} minutes, processed {emails_processed} emails"
            
        except Exception as e:
            LogFire.log("ERROR", f"Fatal error in IMAP session for user {user_guid}: {str(e)}", chat=chat_session)
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "_run_30_minute_session", user_guid)
            return f"IMAP IDLE session failed: {str(e)}"
    
    async def _get_imap_config(self) -> Optional[Dict]:
        """Get IMAP configuration from OAuth tokens"""
        try:
            server = await OAuth2Verifier.get_token("imap", "access_token")
            email_account = await OAuth2Verifier.get_token("imap", "refresh_token") 
            port = await OAuth2Verifier.get_token("imap", "expires_in")
            password = await OAuth2Verifier.get_token("imap", "token_type")
            
            if not all([server, email_account, port, password]):
                LogFire.log("ERROR", "Incomplete IMAP configuration", chat=None)
                return None
            
            config = {
                'server': server,
                'port': int(port),
                'email': email_account,
                'password': password,
                'use_ssl': int(port) == 993
            }
            
            LogFire.log("IMAP CONFIG", f"Server: {server}:{port}, Email: {email_account}, SSL: {config['use_ssl']}", chat=None)
            
            return config
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get OAuth config: {str(e)}", chat=None)
            return None
    
    def _sync_establish_imap_connection(self, config: Dict):
        """Synchronous IMAP connection establishment (runs in thread)"""
        try:
            if config['use_ssl']:
                mail = imaplib.IMAP4_SSL(config['server'], config['port'])
            else:
                mail = imaplib.IMAP4(config['server'], config['port'])
            
            # Login
            try:
                mail.login(config['email'], config['password'])
            except Exception:
                # Some providers don't allow extension in username
                username = config['email'].rsplit(".", 1)[0]
                mail.login(username, config['password'])
            
            # Select INBOX
            mail.select("INBOX")
            
            LogFire.log("IMAP CONNECTION", "Successfully established IMAP connection", chat=None)
            return mail
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to establish IMAP connection: {str(e)}", chat=None)
            return None
    
    async def _establish_imap_connection(self, config: Dict):
        """Establish IMAP connection in thread to prevent blocking main loop"""
        import asyncio
        from concurrent.futures import ThreadPoolExecutor
        
        # Run the synchronous IMAP operations in a thread executor
        loop = asyncio.get_event_loop()
        try:
            with ThreadPoolExecutor(max_workers=1) as executor:
                # Add timeout to prevent hanging indefinitely
                mail = await asyncio.wait_for(
                    loop.run_in_executor(executor, self._sync_establish_imap_connection, config),
                    timeout=30.0  # 30 second timeout
                )
                return mail
        except asyncio.TimeoutError:
            LogFire.log("ERROR", "IMAP connection timed out after 30 seconds", chat=None)
            return None
        except Exception as e:
            LogFire.log("ERROR", f"Failed to establish IMAP connection: {str(e)}")
            return None
    
    async def _extract_email_body_content(self, email_message) -> str:
        """Extract body content from email message object"""
        try:
            if email_message.is_multipart():
                # Handle multipart messages (HTML + text, attachments, etc.)
                body_content = ""
                for part in email_message.walk():
                    if part.get_content_type() == "text/plain":
                        body_content = part.get_payload(decode=True).decode('utf-8', errors='ignore')
                        break
                    elif part.get_content_type() == "text/html" and not body_content:
                        body_content = part.get_payload(decode=True).decode('utf-8', errors='ignore')
            else:
                # Simple single-part message
                body_content = email_message.get_payload(decode=True).decode('utf-8', errors='ignore')
                
            return body_content if body_content else ""
        except Exception as e:
            LogFire.log("ERROR", f"Failed to extract email body content: {str(e)}")
            return ""

    async def _process_new_emails(self, mail_connection, user_guid: str) -> int:
        """Process new emails and return count of processed emails"""
        processed_count = 0
        
        try:
            # Re-select INBOX after exiting IDLE mode
            select_result = mail_connection.select("INBOX")
            
            # Search for unseen emails
            LogFire.log("IMAP PROCESS", "Searching for unseen emails...")
            
            status, message_ids = mail_connection.search(None, 'UNSEEN')
            LogFire.log("IMAP PROCESS", f"Search result: status={status}, ids={message_ids}")
            
            if status == 'OK' and message_ids[0]:
                email_ids = message_ids[0].split()
                LogFire.log("IMAP PROCESS", f"Found {len(email_ids)} unseen emails: {email_ids}")
                
                for email_id in email_ids:
                    try:
                        # Fetch email
                        status, msg_data = mail_connection.fetch(email_id, '(RFC822)')
                        if status == 'OK':
                            raw_email = msg_data[0][1]
                            
                            # Parse email for logging and content extraction
                            email_message = email.message_from_bytes(raw_email)
                            subject = email_message.get('Subject', 'No Subject')
                            body_content = await self._extract_email_body_content(email_message)
                            
                            # Save email immediately
                            await self._process_email(raw_email, body_content, subject, user_guid)
                            processed_count += 1
                            
                            LogFire.log("IMAP PROCESS", 
                                      f"Processed email immediately: {subject} for user {user_guid}")
                            
                    except Exception as e:
                        LogFire.log("ERROR", f"Failed to process email {email_id}: {str(e)}")
                        
        except Exception as e:
            LogFire.log("ERROR", f"Failed to process new emails: {str(e)}")
        
        return processed_count
    
    async def _process_email(self, raw_email: bytes, body_content: str, subject: str, user_guid: str, state: SupervisorTaskState = None):
        """Save email, convert to vector store, and broadcast to all connected communication channels"""
        try:
            # Add call trace for email processing
            if state and hasattr(state, 'call_trace'):
                state.call_trace.append(f"{self.name}: processing_email")
            # Set up output path
            if Globals.is_docker():
                path = '/meltano/output'
            else:
                path = os_path.join(getcwd(), 'src', 'meltano', 'output')
            
            makedirs(path, exist_ok=True)
            
            # Clean subject for filename
            clean_subject = "".join(c for c in subject if c.isalnum() or c in (' ', '-', '_')).rstrip()[:50]
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = os_path.join(path, f"immediate_email_{timestamp}_{clean_subject}.eml")
            
            # Save email
            # TODO: Add check if boolean is enabled on OAuth screen
            with open(filename, "wb") as f:
                f.write(raw_email)
            
            # Convert to vector store
            user = await ZairaUserManager.find_user(user_guid)
            if user:
                #await MeltanoManager.ConvertFilesToVectorStore(path, user)
                LogFire.log("IMAP PROCESS", f"Converted email to vector store: {filename}")
                
                # Immediate broadcast notification to all connected communication channels
                try:
                    # Create a more informative broadcast message
                    broadcast_message = f"Email received: Title: {subject}, Content: {body_content[:200]}{'...' if len(body_content) > 200 else ''}"
                    await MyBot_Generic.send_broadcast_ALL(user, broadcast_message)
                    await self._process_email_with_supervisor(body_content, subject, user, state)
                except Exception as e:
                    LogFire.log("ERROR", f"Failed to send immediate broadcast: {str(e)}")
                
                # # Process email content through supervisor system for intelligent reaction
                # # Create this as a background task to avoid blocking email processing
                # from asyncio import create_task
                # from etc.helper_functions import handle_asyncio_task_result_errors
                
                # supervisor_task = create_task(self._process_email_with_supervisor(body_content, subject, user, state))
                # handle_asyncio_task_result_errors(supervisor_task)
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to save email: {str(e)}")
    
    async def _process_email_with_supervisor(self, body_content: str, subject: str, user: ZairaUser, state: SupervisorTaskState = None):
        """Process email content through supervisor system and implement human-in-the-loop workflow"""
        # from managers.manager_supervisors import SupervisorManager
        # from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
        #request = await user.start_request(f"I have received the following mail and want to send a reply to it: {body_content}. The mail subject should be RE:{subject}", None)
        #session_guid = request._create_isolated_session_for_scheduled_request()
        await user.on_message(f"I have received the following mail and want to send a reply to it: {body_content}. The mail subject should be RE:{subject}", None, [], None, None, True)
        #SupervisorManager.get_task("email_generator_task")
        return
        
    async def _periodic_email_check(self, mail_connection, user_guid: str, duration_seconds: float):
        """Fallback method for servers that don't support IDLE - polls every 30 seconds"""
        from datetime import datetime, timezone
        
        LogFire.log("IMAP POLL", f"Using polling fallback for {duration_seconds/60:.1f} minutes")
        
        start_time = time.time()
        session_start_datetime = datetime.now(timezone.utc)
        last_processed_datetime = session_start_datetime  # Track when last email was processed
        emails_processed = 0
        
        # Get baseline message count at session start
        try:
            # Validate connection before getting baseline
            if not await self._validate_connection(mail_connection):
                LogFire.log("WARNING", "Connection invalid when getting baseline count")
                baseline_count = 0
            else:
                mail_connection.select("INBOX")
                status, data = mail_connection.search(None, 'ALL')
                if status == 'OK' and data[0]:
                    baseline_count = len(data[0].split())
                else:
                    baseline_count = 0
                
                LogFire.log("IMAP POLL", f"Baseline message count: {baseline_count}")
        except Exception as e:
            LogFire.log("ERROR", f"Could not get baseline count: {str(e)}")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "_periodic_email_check_baseline", user_guid)
            baseline_count = 0
        
        while (time.time() - start_time) < duration_seconds:
            try:
                # Check for emails that arrived after last processed time
                processed_count = await self._process_emails_since_start(mail_connection, user_guid, last_processed_datetime, baseline_count)
                if processed_count > 0:
                    emails_processed += processed_count
                    last_processed_datetime = datetime.now(timezone.utc)  # Update last processed time
                    LogFire.log("IMAP POLL", f"Found {processed_count} new emails since last check")
                
                # Wait 30 seconds before next check (or until duration ends)
                remaining_time = duration_seconds - (time.time() - start_time)
                sleep_time = min(30, remaining_time)
                
                if sleep_time > 0:
                    await asyncio.sleep(sleep_time)
                
            except Exception as e:
                # Handle specific timeout errors
                if "cannot read from timed out object" in str(e) or "timed out" in str(e).lower():
                    LogFire.log("IMAP POLL WARNING", "Connection timed out - will retry with fresh connection")
                    
                    # Validate connection before continuing
                    if not await self._validate_connection(mail_connection):
                        LogFire.log("WARNING", "Connection validation failed after timeout - ending periodic check")
                        return emails_processed  # Let caller handle reconnection
                        
                    # Connection is still valid, continue with next polling cycle
                    continue
                else:
                    LogFire.log("IMAP POLL ERROR", f"{str(e)}")
                    await asyncio.sleep(30)  # Wait before retry
        
        LogFire.log("IMAP POLL COMPLETE", f"Processed {emails_processed} emails total")
        return emails_processed
    
    async def _process_emails_since_start(self, mail_connection, user_guid: str, session_start_datetime, baseline_count: int) -> int:
        """Process only emails that arrived after the session started"""
        processed_count = 0
        
        try:
            # Validate connection first
            if not await self._validate_connection(mail_connection):
                LogFire.log("WARNING", "Connection invalid in _process_emails_since_start")
                return 0
            
            # Re-select INBOX to ensure clean state
            mail_connection.select("INBOX")
            
            # Get current total message count
            status, data = mail_connection.search(None, 'ALL')
            if status == 'OK' and data[0]:
                current_count = len(data[0].split())
                current_message_ids = data[0].split()
            else:
                current_count = 0
                current_message_ids = []
            
            LogFire.log("IMAP POLL", f"Current message count: {current_count}, baseline: {baseline_count}")
            
            # If no new messages since baseline, nothing to process
            if current_count <= baseline_count:
                return 0
            
            # Get the new message IDs (messages after baseline)
            new_message_ids = current_message_ids[baseline_count:]
            
            LogFire.log("IMAP POLL", f"Found {len(new_message_ids)} new messages: {new_message_ids}")
            
            # Process each new message
            for email_id in new_message_ids:
                try:
                    # Fetch email
                    status, msg_data = mail_connection.fetch(email_id, '(RFC822)')
                    if status == 'OK':
                        raw_email = msg_data[0][1]
                        
                        # Parse email for logging and content extraction
                        email_message = email.message_from_bytes(raw_email)
                        subject = email_message.get('Subject', 'No Subject')
                        body_content = await self._extract_email_body_content(email_message)
                        
                        # Save email immediately
                        await self._process_email(raw_email, body_content, subject, user_guid)
                        processed_count += 1
                        
                        LogFire.log("IMAP POLL", f"Processed: {subject} for user {user_guid}")
                        
                except Exception as e:
                    LogFire.log("IMAP POLL ERROR", f"Failed to process {email_id}: {str(e)}")
                    
        except Exception as e:
            # Handle timeout errors gracefully
            if "cannot read from timed out object" in str(e) or "timed out" in str(e).lower():
                LogFire.log("IMAP POLL WARNING", "Connection timed out - will retry")
                return 0  # Return 0 processed, continue polling cycle
            else:
                LogFire.log("IMAP POLL ERROR", f"Failed to check emails since start: {str(e)}")
                from etc.helper_functions import exception_triggered
                exception_triggered(e, "_process_emails_since_start", user_guid)
        
        return processed_count
    
    async def _process_specific_email(self, mail_connection, user_guid: str, message_number: int) -> int:
        """Process a specific email by its message number (the newly arrived one)"""
        try:
            # Re-select INBOX after exiting IDLE mode
            select_result = mail_connection.select("INBOX")
            
            LogFire.log("IMAP PROCESS", f"Processing specific email #{message_number}")
            
            # Fetch the specific email by its sequence number
            status, msg_data = mail_connection.fetch(str(message_number), '(RFC822)')
            
            if status == 'OK' and msg_data and msg_data[0]:
                raw_email = msg_data[0][1]
                
                # Parse email for logging and content extraction
                email_message = email.message_from_bytes(raw_email)
                subject = email_message.get('Subject', 'No Subject')
                body_content = await self._extract_email_body_content(email_message)
                
                LogFire.log("IMAP PROCESS", f"Fetched email #{message_number}: {subject}")
                
                # Save email immediately
                await self._process_email(raw_email, body_content, subject, user_guid)
                
                LogFire.log("IMAP PROCESS", f"Successfully processed email #{message_number}: {subject}")
                
                return 1  # Successfully processed 1 email
            else:
                LogFire.log("IMAP PROCESS ERROR", f"Failed to fetch email #{message_number}: status={status}")
                return 0
                
        except Exception as e:
            LogFire.log("IMAP PROCESS ERROR", f"Failed to process email #{message_number}: {str(e)}")
            return 0
    
    async def _attempt_idle_mode(self, mail_connection, user_guid: str, duration_seconds: float):
        """Attempt to use IDLE mode for real-time email notifications with 5-minute cycling
        
        Args:
            mail_connection: Established IMAP connection
            user_guid: User identifier
            duration_seconds: How long to monitor in IDLE mode
            
        Returns:
            Number of emails processed during IDLE period
        """
        start_time = time.time()
        total_emails_processed = 0
        
        while (time.time() - start_time) < duration_seconds:
            remaining_time = duration_seconds - (time.time() - start_time)
            # Use 5-minute cycles or remaining time, whichever is shorter
            cycle_duration = min(300, remaining_time)  # 5 minutes max per cycle
            
            if cycle_duration <= 0:
                break
                
            # Run IDLE in a separate function with fresh variables
            try:
                emails_in_cycle = await self._run_idle_cycle(mail_connection, user_guid, cycle_duration)
                total_emails_processed += emails_in_cycle
                
                # Check if connection is still valid after cycle
                if not await self._validate_connection(mail_connection):
                    LogFire.log("WARNING", "Connection invalid after IDLE cycle, need reconnection")
                    # Return a tuple with processed count and timeout timestamp
                    return (total_emails_processed, time.time())  # Let caller handle reconnection
                    
            except Exception as e:
                LogFire.log("ERROR", f"IDLE cycle error: {str(e)}")
                return (total_emails_processed, time.time())  # Let caller handle reconnection
                
            # Brief pause between cycles to ensure clean state
            if (time.time() - start_time) < duration_seconds:
                await asyncio.sleep(1)
        
        LogFire.log("IMAP IDLE", f"Monitoring completed after {(time.time() - start_time)/60:.1f} minutes, processed {total_emails_processed} emails")
        return total_emails_processed
    
    async def _run_idle_cycle(self, mail_connection, user_guid: str, cycle_duration: float) -> int:
        """Run a single IDLE cycle with fresh variables and proper cleanup
        
        Args:
            mail_connection: Established IMAP connection
            user_guid: User identifier  
            cycle_duration: Duration for this IDLE cycle in seconds
            
        Returns:
            Number of emails processed during this cycle
        """
        emails_processed = 0
        original_timeout = None
        
        try:
            # Check if server supports IDLE
            capabilities_str = str(mail_connection.capabilities)
            if 'IDLE' not in capabilities_str:
                LogFire.log("WARNING", "IMAP server does not support IDLE command")
                return 0
            
            LogFire.log("IMAP IDLE", f"Starting IDLE cycle for {cycle_duration} seconds")
            
            # Store original timeout and set for this cycle
            original_timeout = getattr(mail_connection.sock, 'gettimeout', lambda: None)()
            
            # Generate tag and send IDLE command
            tag = mail_connection._new_tag()
            idle_command = f'{tag} IDLE\r\n'
            
            # Send the command
            mail_connection.send(idle_command.encode('ascii'))
            
            # Read continuation response
            response = mail_connection.readline()
            response_str = response.decode('utf-8', errors='ignore').strip()
            
            if not response_str.startswith('+'):
                LogFire.log("ERROR", f"IDLE rejected: {response_str}")
                return 0
            
            # Set timeout for this cycle
            mail_connection.sock.settimeout(cycle_duration)
            
            # Monitor for emails during this cycle
            cycle_start = time.time()
            
            while (time.time() - cycle_start) < cycle_duration:
                try:
                    # Check for server responses
                    response = mail_connection.readline()
                    if response:
                        response_str = response.decode('utf-8', errors='ignore').strip()
                        
                        # Check for EXISTS (new email)
                        if 'EXISTS' in response_str:
                            LogFire.log("IMAP EMAIL DETECTED", f"New email: {response_str}")
                            
                            # Extract the new message count from EXISTS response
                            try:
                                parts = response_str.split()
                                new_total_count = int(parts[1])
                            except (IndexError, ValueError):
                                LogFire.log("ERROR", f"Could not parse EXISTS: {response_str}")
                                continue
                            
                            # Exit IDLE to process emails
                            mail_connection.send(b'DONE\r\n')
                            
                            # Read all remaining responses to clear the buffer
                            try:
                                while True:
                                    response = mail_connection.readline()
                                    if not response:
                                        break
                                    response_str = response.decode('utf-8', errors='ignore').strip()
                                    
                                    # Look for the OK response that ends IDLE
                                    if 'OK' in response_str and 'IDLE' in response_str:
                                        break
                            except socket.timeout:
                                pass
                            
                            # Process only the specific new email
                            processed_count = await self._process_specific_email(mail_connection, user_guid, new_total_count)
                            emails_processed += processed_count
                            
                            # Restart IDLE for continued monitoring within this cycle
                            mail_connection.send(idle_command.encode('ascii'))
                            response = mail_connection.readline()
                            restart_response = response.decode('utf-8', errors='ignore').strip()
                            
                            if not restart_response.startswith('+'):
                                LogFire.log("ERROR", f"Failed to restart IDLE: {restart_response}")
                                break
                        
                
                except socket.timeout:
                    # Cycle timeout reached - this is normal
                    break
                except Exception as e:
                    LogFire.log("ERROR", f"IDLE monitoring error: {str(e)}")
                    raise  # Re-raise to let caller handle
            
            return emails_processed
            
        except Exception as e:
            LogFire.log("ERROR", f"IDLE cycle error: {str(e)}")
            raise
        finally:
            # Always clean up IDLE state
            try:
                # Exit IDLE if still active
                mail_connection.send(b'DONE\r\n')
                # Wait for response with short timeout
                mail_connection.sock.settimeout(5.0)
                mail_connection.response('IDLE')
            except:
                pass
            
            # Restore original timeout
            if original_timeout is not None:
                try:
                    mail_connection.sock.settimeout(original_timeout)
                except:
                    pass
    
    async def _validate_connection(self, mail_connection) -> bool:
        """Validate that the IMAP connection is still active
        
        Args:
            mail_connection: IMAP connection to validate
            
        Returns:
            bool: True if connection is valid, False otherwise
        """
        try:
            # Try a simple NOOP command to check connection
            mail_connection.noop()
            return True
        except Exception as e:
            # Don't log timeout errors as warnings - they are expected after 5-minute cycles
            if "timed out" in str(e).lower() or "cannot read from timed out object" in str(e):
                LogFire.log("IMAP IDLE", f"Connection timed out (expected after IDLE cycle)")
                return False
            else:
                LogFire.log("WARNING", f"Connection validation failed: {str(e)}")
                return False
    
    async def _gap_targeted_email_check(self, mail_connection, user_guid: str, timeout_timestamp: float, remaining_duration: float) -> int:
        """Check for emails that arrived during the gap between IDLE timeout and reconnection
        
        Args:
            mail_connection: Fresh IMAP connection
            user_guid: User identifier
            timeout_timestamp: When the IDLE timeout occurred
            remaining_duration: Remaining time to continue monitoring
            
        Returns:
            Number of emails processed during gap period + continued polling
        """
        from datetime import datetime, timezone
        import time
        
        start_time = time.time()
        emails_processed = 0
        
        try:
            # Convert timeout timestamp to datetime for IMAP search
            timeout_datetime = datetime.fromtimestamp(timeout_timestamp, tz=timezone.utc)
            
            LogFire.log("IMAP GAP", f"Checking for emails since IDLE timeout at {timeout_datetime}")
            
            # Search for emails received since the timeout occurred
            search_date = timeout_datetime.strftime('%d-%b-%Y')
            
            # Select inbox
            mail_connection.select('INBOX')
            
            # Search for messages since timeout date
            status, messages = mail_connection.search(None, f'(SINCE {search_date})')
            
            if status == 'OK' and messages[0]:
                message_nums = messages[0].split()
                LogFire.log("IMAP GAP", f"Found {len(message_nums)} potential messages to check from {search_date}")
                
                for num in message_nums[-50:]:  # Check last 50 messages to avoid overload
                    try:
                        # Fetch message date to verify it's actually in our gap
                        status, msg_data = mail_connection.fetch(num, '(INTERNALDATE)')
                        if status == 'OK' and msg_data:
                            # Parse the INTERNALDATE
                            date_str = msg_data[0].decode('utf-8')
                            
                            # Extract date from FETCH response
                            import re
                            date_match = re.search(r'INTERNALDATE "([^"]+)"', date_str)
                            if date_match:
                                from email.utils import parsedate_to_datetime
                                try:
                                    msg_datetime = parsedate_to_datetime(date_match.group(1))
                                    msg_timestamp = msg_datetime.timestamp()
                                    
                                    # Check if message is within our gap period (allow small buffer for precision)
                                    if msg_timestamp >= (timeout_timestamp - 1):  # 1 second buffer
                                        LogFire.log("IMAP GAP", f"Message {num} is within gap period, processing...")
                                        
                                        # Process this email as it arrived during the gap
                                        status, msg_data = mail_connection.fetch(num, '(RFC822)')
                                        if status == 'OK' and msg_data and msg_data[0]:
                                            raw_email = msg_data[0][1]
                                            
                                            # Parse email for logging and content extraction
                                            import email
                                            email_message = email.message_from_bytes(raw_email)
                                            subject = email_message.get('Subject', 'No Subject')
                                            body_content = await self._extract_email_body_content(email_message)
                                            
                                            # Process the email
                                            await self._process_email(raw_email, body_content, subject, user_guid)
                                            emails_processed += 1
                                            
                                            LogFire.log("IMAP GAP", f"Processed gap email: {subject}")
                                        
                                except Exception as date_parse_error:
                                    LogFire.log("WARNING", f"Could not parse message date for {num}: {date_parse_error}")
                            else:
                                LogFire.log("WARNING", f"Could not extract INTERNALDATE from {num}: {date_str}")
                                    
                    except Exception as e:
                        LogFire.log("WARNING", f"Failed to check message {num}: {str(e)}")
            
            # Do not continue with polling - return control to IMAP IDLE
            LogFire.log("IMAP GAP", f"Gap-targeted check completed. Processed {emails_processed} emails during gap period")
            
        except Exception as e:
            LogFire.log("ERROR", f"Gap-targeted email check failed: {str(e)}")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "_gap_targeted_email_check", user_guid)
        
        return emails_processed
    
    async def _catch_up_email_check(self, mail_connection, user_guid: str, timeout_start_time: float) -> int:
        """
        Check for emails that arrived during IMAP timeout period.
        Returns the number of emails processed.
        """
        try:
            from datetime import datetime, timedelta
            import email
            from email.header import decode_header
            
            # Calculate timeout duration (when the timeout started)
            timeout_datetime = datetime.fromtimestamp(timeout_start_time)
            
            LogFire.log("IMAP CATCH-UP", f"Searching for emails since {timeout_datetime}")
            
            # Search for emails received during timeout period
            # IMAP SEARCH command: look for messages received since the timeout started
            search_date = timeout_datetime.strftime('%d-%b-%Y')
            
            # Select inbox
            mail_connection.select('INBOX')
            
            # Search for messages since timeout started
            # Note: IMAP search is date-only, so we get all emails from that day and filter by time
            status, messages = mail_connection.search(None, f'(SINCE {search_date})')
            
            if status != 'OK' or not messages[0]:
                LogFire.log("IMAP CATCH-UP", f"No messages found since {search_date}")
                return 0
            
            message_nums = messages[0].split()
            processed_count = 0
            
            LogFire.log("IMAP CATCH-UP", f"Found {len(message_nums)} potential messages to check")
            
            for num in message_nums:
                try:
                    # Fetch message headers to check received time
                    status, msg_data = mail_connection.fetch(num, '(RFC822.HEADER)')
                    if status != 'OK' or not msg_data:
                        continue
                    
                    # Parse email headers
                    email_msg = email.message_from_bytes(msg_data[0][1])
                    received_header = email_msg.get('Received')
                    
                    # If we can't get receive time, process it to be safe
                    should_process = True
                    
                    if received_header:
                        try:
                            # Extract timestamp from Received header (simplified)
                            # This is a basic implementation - more robust parsing could be added
                            import re
                            timestamp_match = re.search(r'\d{1,2}\s+\w{3}\s+\d{4}\s+\d{2}:\d{2}:\d{2}', received_header)
                            if timestamp_match:
                                from datetime import datetime
                                try:
                                    # Simple timestamp parsing - could be enhanced
                                    received_time = datetime.strptime(timestamp_match.group(), '%d %b %Y %H:%M:%S')
                                    if received_time < timeout_datetime:
                                        should_process = False  # Email is too old
                                except:
                                    # If parsing fails, process the email to be safe
                                    should_process = True
                        except:
                            # If header parsing fails, process the email to be safe
                            should_process = True
                    
                    if should_process:
                        # Fetch full message
                        status, full_msg_data = mail_connection.fetch(num, '(RFC822)')
                        if status == 'OK' and full_msg_data:
                            raw_email = full_msg_data[0][1]
                            
                            # Parse subject for logging and content extraction
                            email_msg = email.message_from_bytes(raw_email)
                            subject = email_msg.get('Subject', 'No Subject')
                            body_content = await self._extract_email_body_content(email_msg)
                            
                            # Process the email using existing email processing logic
                            await self._process_email(raw_email, body_content, subject, user_guid)
                            processed_count += 1
                            
                            LogFire.log("IMAP CATCH-UP", f"Processed email {processed_count}/{len(message_nums)}")
                
                except Exception as e:
                    LogFire.log("WARNING", f"Error processing catch-up email: {str(e)}")
                    continue
            
            LogFire.log("IMAP CATCH-UP", f"Completed: processed {processed_count} emails")
            return processed_count
            
        except Exception as e:
            LogFire.log("IMAP CATCH-UP ERROR", f"{str(e)}")
            return 0

async def create_task_imap_idle_activate() -> SupervisorTask_Base:
    """Create and register the IMAP 30-minute session task"""
    return SupervisorManager.register_task(
        SupervisorTask_IMAPIdleActivate(
            name="imap_idle_activate", 
            prompt="Start IMAP IDLE email monitoring session. This task monitors email accounts for new messages using IMAP IDLE protocol. Execute when user requests to start email monitoring or IMAP IDLE functionality."
        )
    )

# Convenience function for external use
async def start_30_minute_imap_session(user_guid: str) -> str:
    """Start a 30-minute IMAP IDLE session for a user"""
    from managers.manager_supervisors import SupervisorTaskState
    
    task = SupervisorTask_IMAPIdleActivate()
    state = SupervisorTaskState()
    state.user_guid = user_guid
    
    return await task.llm_call(state)