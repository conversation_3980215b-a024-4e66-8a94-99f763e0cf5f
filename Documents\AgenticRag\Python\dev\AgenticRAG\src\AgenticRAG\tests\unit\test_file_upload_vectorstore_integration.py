from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, patch, MagicMock, mock_open
from aiohttp import web
from aiohttp.test_utils import AioHTTPTestCase, unittest_run_loop
from tests.unit.test_logging_capture import with_unit_test_logging

class TestFileUploadVectorStoreIntegration:
    """Test the complete file upload to vector store pipeline"""
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_handle_file_upload_complete_success(self):
        """Test successful file upload and vector store conversion"""
        from endpoints.api_endpoint import APIEndpoint
        
        # Create APIEndpoint instance
        api = APIEndpoint()
        
        # Mock request with file upload
        mock_request = MagicMock()
        mock_multipart_reader = MagicMock()
        mock_field = MagicMock()
        
        # Configure multipart field
        mock_field.name = 'files'
        mock_field.filename = 'test_document.pdf'
        mock_field.read_chunk = AsyncMock(side_effect=[b'test content', b''])
        
        # Configure multipart reader
        mock_multipart_reader.next = AsyncMock(side_effect=[mock_field, None])
        mock_request.multipart = AsyncMock(return_value=mock_multipart_reader)
        
        with patch('os.makedirs') as mock_makedirs, \
             patch('builtins.open', mock_open()) as mock_file, \
             patch.object(api, '_ensure_managers_initialized', return_value=True) as mock_init, \
             patch.object(api, '_validate_upload_dependencies', return_value=True) as mock_validate, \
             patch.object(api, '_process_files_to_vectorstore', return_value=True) as mock_process, \
             patch('globals.Globals.is_docker', return_value=False), \
             patch('os.getcwd', return_value='/test'):
            
            response = await api.handle_file_upload(mock_request)
            
            # Verify response
            assert response.status == 200
            assert "succesvol verwerkt" in response.text
            
            # Verify all steps were called
            mock_init.assert_called_once()
            mock_validate.assert_called_once()
            mock_process.assert_called_once()
            
            # Verify file was written
            mock_file.assert_called_once()
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_handle_file_upload_manager_initialization_failure(self):
        """Test file upload when manager initialization fails"""
        from endpoints.api_endpoint import APIEndpoint
        
        api = APIEndpoint()
        
        # Mock request with file upload
        mock_request = MagicMock()
        mock_multipart_reader = MagicMock()
        mock_field = MagicMock()
        
        mock_field.name = 'files'
        mock_field.filename = 'test.pdf'
        mock_field.read_chunk = AsyncMock(side_effect=[b'content', b''])
        
        mock_multipart_reader.next = AsyncMock(side_effect=[mock_field, None])
        mock_request.multipart = AsyncMock(return_value=mock_multipart_reader)
        
        with patch('os.makedirs'), \
             patch('builtins.open', mock_open()), \
             patch.object(api, '_ensure_managers_initialized', return_value=False) as mock_init, \
             patch('globals.Globals.is_docker', return_value=False), \
             patch('os.getcwd', return_value='/test'):
            
            response = await api.handle_file_upload(mock_request)
            
            # Verify failure response
            assert response.status == 500
            assert "System initialization failed" in response.text
            
            # Verify initialization was attempted
            mock_init.assert_called_once()
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_ensure_managers_initialized_success(self):
        """Test successful manager initialization"""
        from endpoints.api_endpoint import APIEndpoint
        
        api = APIEndpoint()
        
        # Mock all managers and their setup methods
        mock_index = MagicMock()
        mock_qdrant_client = MagicMock()
        
        with patch('globals.Globals.get_index', return_value=mock_index), \
             patch('managers.manager_qdrant.QDrantManager.setup') as mock_qdrant_setup, \
             patch('managers.manager_qdrant.QDrantManager.GetClient', return_value=mock_qdrant_client), \
             patch('managers.manager_multimodal.MultimodalManager.setup') as mock_multimodal_setup, \
             patch('managers.manager_retrieval.RetrievalManager.setup') as mock_retrieval_setup:
            
            result = await api._ensure_managers_initialized()
            
            # Verify all managers were initialized
            assert result is True
            mock_qdrant_setup.assert_called_once()
            mock_multimodal_setup.assert_called_once()
            mock_retrieval_setup.assert_called_once()
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_ensure_managers_initialized_index_failure(self):
        """Test manager initialization when global Index fails"""
        from endpoints.api_endpoint import APIEndpoint
        
        api = APIEndpoint()
        
        with patch('globals.Globals.get_index', return_value=None):
            result = await api._ensure_managers_initialized()
            
            assert result is False
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_validate_upload_dependencies_success(self):
        """Test successful upload dependency validation"""
        from endpoints.api_endpoint import APIEndpoint
        
        api = APIEndpoint()
        files = ['test.pdf', 'document.txt']
        upload_path = '/test/path'
        
        # Mock file system and embedding service
        mock_embedding = [0.1, 0.2, 0.3]
        
        with patch('os.path.exists', return_value=True), \
             patch('os.path.getsize', return_value=1024), \
             patch('managers.manager_retrieval.RetrievalManager.get_embeddings_dense', return_value=mock_embedding):
            
            result = await api._validate_upload_dependencies(files, upload_path)
            
            assert result is True
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_validate_upload_dependencies_file_too_large(self):
        """Test validation failure for oversized files"""
        from endpoints.api_endpoint import APIEndpoint
        
        api = APIEndpoint()
        files = ['huge_file.pdf']
        upload_path = '/test/path'
        
        # Mock large file size (60MB > 50MB limit)
        large_size = 60 * 1024 * 1024
        
        with patch('os.path.exists', return_value=True), \
             patch('os.path.getsize', return_value=large_size):
            
            result = await api._validate_upload_dependencies(files, upload_path)
            
            assert result is False
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_validate_upload_dependencies_unsupported_file_type(self):
        """Test validation failure for unsupported file types"""
        from endpoints.api_endpoint import APIEndpoint
        
        api = APIEndpoint()
        files = ['virus.exe']  # Unsupported type
        upload_path = '/test/path'
        
        with patch('os.path.exists', return_value=True), \
             patch('os.path.getsize', return_value=1024):
            
            result = await api._validate_upload_dependencies(files, upload_path)
            
            assert result is False
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_validate_upload_dependencies_embedding_service_failure(self):
        """Test validation failure when embedding service is not available"""
        from endpoints.api_endpoint import APIEndpoint
        
        api = APIEndpoint()
        files = ['test.pdf']
        upload_path = '/test/path'
        
        with patch('os.path.exists', return_value=True), \
             patch('os.path.getsize', return_value=1024), \
             patch('managers.manager_retrieval.RetrievalManager.get_embeddings_dense', return_value=None):
            
            result = await api._validate_upload_dependencies(files, upload_path)
            
            assert result is False
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_process_files_to_vectorstore_success(self):
        """Test successful file processing to vector store"""
        from endpoints.api_endpoint import APIEndpoint
        
        api = APIEndpoint()
        upload_path = '/test/path'
        files = ['test.pdf']
        
        with patch('managers.manager_meltano.MeltanoManager.ConvertFilesToVectorStore') as mock_convert, \
             patch('os.path.exists', return_value=False):  # Files removed after processing
            
            result = await api._process_files_to_vectorstore(upload_path, files)
            
            assert result is True
            mock_convert.assert_called_once_with(upload_path, None)
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_process_files_to_vectorstore_files_not_processed(self):
        """Test when files are not processed (still exist after conversion attempt)"""
        from endpoints.api_endpoint import APIEndpoint
        
        api = APIEndpoint()
        upload_path = '/test/path'
        files = ['test.pdf']
        
        with patch('managers.manager_meltano.MeltanoManager.ConvertFilesToVectorStore'), \
             patch('os.path.exists', return_value=True), \
             patch('os.remove') as mock_remove:
            
            result = await api._process_files_to_vectorstore(upload_path, files)
            
            assert result is False
            # Verify cleanup was attempted
            mock_remove.assert_called_once()
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_process_files_to_vectorstore_conversion_exception(self):
        """Test handling of conversion exceptions"""
        from endpoints.api_endpoint import APIEndpoint
        
        api = APIEndpoint()
        upload_path = '/test/path'
        files = ['test.pdf']
        
        with patch('managers.manager_meltano.MeltanoManager.ConvertFilesToVectorStore', 
                  side_effect=Exception("Conversion failed")):
            
            result = await api._process_files_to_vectorstore(upload_path, files)
            
            assert result is False
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_handle_file_upload_no_files_field(self):
        """Test file upload with incorrect field name"""
        from endpoints.api_endpoint import APIEndpoint
        
        api = APIEndpoint()
        
        # Mock request with wrong field name
        mock_request = MagicMock()
        mock_multipart_reader = MagicMock()
        mock_field = MagicMock()
        
        mock_field.name = 'wrong_field'
        mock_multipart_reader.next = AsyncMock(return_value=mock_field)
        mock_request.multipart = AsyncMock(return_value=mock_multipart_reader)
        
        response = await api.handle_file_upload(mock_request)
        
        assert response.status == 400
        assert 'Expected a "files" field' in response.text
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_handle_file_upload_no_filename(self):
        """Test file upload with no filename provided"""
        from endpoints.api_endpoint import APIEndpoint
        
        api = APIEndpoint()
        
        # Mock request with no filename
        mock_request = MagicMock()
        mock_multipart_reader = MagicMock()
        mock_field = MagicMock()
        
        mock_field.name = 'files'
        mock_field.filename = None
        mock_multipart_reader.next = AsyncMock(return_value=mock_field)
        mock_request.multipart = AsyncMock(return_value=mock_multipart_reader)
        
        response = await api.handle_file_upload(mock_request)
        
        assert response.status == 400
        assert 'No files uploaded' in response.text

class TestFileUploadVectorStoreEnd2End:
    """End-to-end integration tests for file upload pipeline"""
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_complete_pipeline_integration(self):
        """Test complete file upload to vector store pipeline with real components"""
        from endpoints.api_endpoint import APIEndpoint
        
        # This test would require actual system components to be running
        # For now, we'll test the pipeline structure with minimal mocking
        
        api = APIEndpoint()
        
        # Mock the external dependencies but test the internal logic flow
        with patch('managers.manager_qdrant.QDrantManager.setup') as mock_qdrant, \
             patch('managers.manager_multimodal.MultimodalManager.setup') as mock_multimodal, \
             patch('managers.manager_retrieval.RetrievalManager.setup') as mock_retrieval, \
             patch('globals.Globals.get_index') as mock_get_index, \
             patch('managers.manager_qdrant.QDrantManager.GetClient') as mock_get_client, \
             patch('managers.manager_retrieval.RetrievalManager.get_embeddings_dense') as mock_embeddings:
            
            # Configure mocks for success case
            mock_get_index.return_value = MagicMock()
            mock_get_client.return_value = MagicMock()
            mock_embeddings.return_value = [0.1, 0.2, 0.3]
            
            # Test manager initialization
            result = await api._ensure_managers_initialized()
            assert result is True
            
            # Verify all required setups were called
            mock_qdrant.assert_called_once()
            mock_multimodal.assert_called_once() 
            mock_retrieval.assert_called_once()
            mock_get_index.assert_called_once()
            mock_get_client.assert_called_once()