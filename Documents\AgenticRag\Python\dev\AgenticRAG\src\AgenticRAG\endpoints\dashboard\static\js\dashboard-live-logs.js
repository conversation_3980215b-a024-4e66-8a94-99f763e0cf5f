// Dashboard Live Logs JavaScript Functions - Live Logs Module
// Contains: LiveLogViewer class, NotificationSystem, PollingManager, and test_real server functions

// LiveLogViewer Class - Manages real-time log display with filtering
class LiveLogViewer {
    constructor() {
        this.isInitialized = false;
        this.isPaused = false;
        this.lastPosition = 0;
        this.updateInterval = 5000; // Fixed 5 seconds
        this.intervalId = null;
        this.filteredContent = '';
        this.allContent = '';
        this.previousDisplayContent = '';  // Track previous content for change detection
        this.lineCount = 0;
        this.retryCount = 0;
        this.maxRetries = 3;
        this.logSource = 'debug'; // Default to debug trace logs
        this.rawMode = false; // Default to normal filtering mode
    }

    async initialize() {
        if (this.isInitialized) return;
        
        try {
            // Check if log viewer elements exist
            const logContent = document.getElementById('logContent');
            const statusIcon = document.getElementById('logStatusIcon');
            const statusText = document.getElementById('logStatusText');
            
            if (!logContent || !statusIcon || !statusText) {
                console.log('[WARNING] Log viewer elements not found, attempting to create fallback elements');
                
                // Try to create minimal fallback elements if they don't exist
                if (!logContent) {
                    const fallbackContent = document.createElement('div');
                    fallbackContent.id = 'logContent';
                    fallbackContent.style.cssText = `
                        background: #1f2937;
                        color: #e5e7eb;
                        font-family: 'Courier New', monospace;
                        font-size: 13px;
                        line-height: 1.4;
                        padding: 1rem;
                        border-radius: 8px;
                        max-height: 400px;
                        overflow-y: auto;
                        white-space: pre-wrap;
                        word-break: break-all;
                    `;
                    fallbackContent.innerHTML = '<div style="color: #94a3b8; text-align: center;">[FALLBACK] Live Log Viewer - waiting for log data...</div>';
                    
                    // Try to find a suitable container
                    const chatContainer = document.querySelector('#chat') || document.querySelector('.tab-content.active') || document.body;
                    chatContainer.appendChild(fallbackContent);
                    console.log('[INFO] Created fallback logContent element');
                }
                
                if (!statusIcon) {
                    const fallbackIcon = document.createElement('span');
                    fallbackIcon.id = 'logStatusIcon';
                    fallbackIcon.style.cssText = 'width: 8px; height: 8px; border-radius: 50%; background: #6b7280; display: inline-block; margin-right: 8px;';
                    document.body.appendChild(fallbackIcon);
                    console.log('[INFO] Created fallback logStatusIcon element');
                }
                
                if (!statusText) {
                    const fallbackText = document.createElement('span');
                    fallbackText.id = 'logStatusText';
                    fallbackText.textContent = 'Initializing...';
                    fallbackText.style.cssText = 'font-size: 12px; color: #9ca3af;';
                    document.body.appendChild(fallbackText);
                    console.log('[INFO] Created fallback logStatusText element');
                }
                
                // Re-check if elements now exist
                const logContentCheck = document.getElementById('logContent');
                const statusIconCheck = document.getElementById('logStatusIcon');
                const statusTextCheck = document.getElementById('logStatusText');
                
                if (!logContentCheck || !statusIconCheck || !statusTextCheck) {
                    console.log('[ERROR] Could not create fallback elements, skipping Live Log Viewer initialization');
                    return;
                }
                
                console.log('[INFO] Fallback elements created successfully, proceeding with initialization');
            }
            
            this.updateStatus('connecting', 'Connecting...');
            
            // Get initial log status and debug mode
            const statusResponse = await safeFetch('/dashboard/api/log-status');
            const statusData = await statusResponse.json();
            
            // Check for test environment and show test_real button
            await this.checkTestEnvironmentAndShowButton();
            
            if (statusData.exists) {
                // Load initial log content
                await this.loadInitialContent();
                this.startPolling();
                this.updateStatus('connected', 'Connected');
                this.isInitialized = true;
                
                // Initialize filter status
                setTimeout(() => {
                    const excludeFrontendDebug = document.getElementById('excludeFrontendDebug')?.checked || false;
                    const excludeScrubbedAuth = document.getElementById('excludeScrubbedAuth')?.checked || false;
                    const eventCodesFilter = document.getElementById('eventCodesFilter')?.value || '';
                    updateFilterStatus(excludeFrontendDebug, excludeScrubbedAuth, eventCodesFilter);
                }, 100);
                
                console.log('Live log viewer initialized successfully');
            } else {
                this.updateStatus('error', 'Log file not found');
                logContent.innerHTML = '<div style="color: #fbbf24; text-align: center; margin-top: 2rem;">[WARNING] Log file not available</div>';
            }
            
        } catch (error) {
            console.error('Failed to initialize live log viewer:', error);
            this.updateStatus('error', 'Connection failed');
            const logContent = document.getElementById('logContent');
            if (logContent) {
                logContent.innerHTML = '<div style="color: #ef4444; text-align: center; margin-top: 2rem;">[ERROR] Failed to connect to log stream</div>';
            }
        }
    }
    
    async checkTestEnvironmentAndShowButton() {
        const testRealBtn = document.getElementById('testRealServerBtn');
        if (testRealBtn) {
            // Always show the button first
            testRealBtn.style.display = 'inline-block';
            console.log('Test environment button enabled');
        }
        
        try {
            // Try to check test environment for header styling
            const testResponse = await safeFetch('/dashboard/api/check-test-server?port=40999');
            
            if (!testResponse.ok) {
                // Handle authentication or other API errors gracefully
                console.log('API not available for environment check, using fallback logic');
                return;
            }
            
            const testData = await testResponse.json();
            
            // Apply red header styling if in test environment
            if (testData.test_environment === true) {
                this.applyTestEnvironmentStyling();
                console.log('Test environment detected, red header applied');
            }
        } catch (error) {
            // Prevent error from bubbling up to global handlers
            console.log('Test environment check failed, using fallback:', error);
            // Button already shown above, so we're good
        }
    }
    
    applyTestEnvironmentStyling() {
        const header = document.querySelector('.header');
        if (header) {
            header.classList.add('test-server-mode');
        }
    }
    
    async loadInitialContent() {
        try {
            // Load last 100 lines initially with filters
            const apiEndpoint = this.getApiEndpoint();
            const params = this.buildApiParams(0, 100);
            console.log(`[DEBUG] loadInitialContent: Making request to ${apiEndpoint} with params:`, params);
            const response = await safeFetch(`${apiEndpoint}?${params}`);
            
            console.log('[DEBUG] loadInitialContent: Response status:', response.status);
            console.log('[DEBUG] loadInitialContent: Response headers:', [...response.headers.entries()]);
            
            if (!response.ok) {
                // Handle authentication errors specifically
                if (response.status === 401) {
                    console.error('[ERROR] loadInitialContent: Authentication required (401)');
                    const errorText = await response.text();
                    console.error('[ERROR] loadInitialContent: Error response:', errorText);
                    throw new Error(`Authentication required: ${errorText}`);
                } else if (response.status === 403) {
                    console.error('[ERROR] loadInitialContent: Insufficient permissions (403)');
                    const errorText = await response.text();
                    console.error('[ERROR] loadInitialContent: Error response:', errorText);
                    throw new Error(`Access denied - SYSTEM user required: ${errorText}`);
                } else {
                    console.error('[ERROR] loadInitialContent: HTTP error:', response.status);
                    const errorText = await response.text();
                    console.error('[ERROR] loadInitialContent: Error response:', errorText);
                    throw new Error(`HTTP ${response.status}: ${errorText}`);
                }
            }
            
            const data = await response.json();
            console.log('[DEBUG] loadInitialContent: Response data:', data);
            console.log('[DEBUG] loadInitialContent: Position reset flag:', data.position_reset);
            
            if (data.content) {
                // Filter garbage detection messages from initial content
                this.allContent = this.filterGarbageDetectionMessages(data.content);
                this.lastPosition = data.new_position;
                
                // Log any position reset during initial load
                if (data.position_reset) {
                    console.log('[DEBUG] loadInitialContent: Server reset position during initial load');
                }
                
                const contentLength = this.allContent.length;
                const lineCount = this.allContent.split('\n').length;
                
                // Show loading feedback for large files
                if (contentLength > 50000) {
                    const logContent = document.getElementById('logContent');
                    if (logContent) {
                        logContent.innerHTML = '<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">[PROCESSING] Processing full debug_trace.log file (' + Math.round(contentLength/1024) + ' KB, ' + lineCount + ' lines)...</div>';
                    }
                    
                    // Use setTimeout to allow UI to update before processing
                    setTimeout(() => {
                        this.updateDisplay();
                        this.updateStats(data);
                    }, 100);
                } else {
                    this.updateDisplay();
                    this.updateStats(data);
                }
                
                console.log('[DEBUG] loadInitialContent: Successfully loaded', contentLength, 'characters (' + lineCount + ' lines)');
                
                // Debug: Show first 200 characters of loaded content
                const preview = this.allContent.substring(0, 200).replace(/\n/g, '\\n');
                console.log('[DEBUG] Content preview (first 200 chars):', preview);
            } else {
                console.log('[DEBUG] loadInitialContent: No content in response');
            }
            
        } catch (error) {
            console.error('[ERROR] loadInitialContent: Failed to load initial log content:', error);
            
            // Update UI to show the specific error
            const logContent = document.getElementById('logContent');
            if (logContent) {
                if (error.message.includes('Authentication required')) {
                    logContent.innerHTML = '<div style="color: #f59e0b; text-align: center; margin-top: 2rem;">[AUTH] Authentication required. Please log in as SYSTEM user.</div>';
                } else if (error.message.includes('Access denied')) {
                    logContent.innerHTML = '<div style="color: #f59e0b; text-align: center; margin-top: 2rem;">[DENIED] Access denied. SYSTEM user permissions required.</div>';
                } else {
                    logContent.innerHTML = `<div style="color: #ef4444; text-align: center; margin-top: 2rem;">[FAILED] Connection failed: ${error.message}</div>`;
                }
            }
            
            throw error;
        }
    }
    
    startPolling() {
        if (this.intervalId) {
            clearInterval(this.intervalId);
        }
        
        // Register with PollingManager instead of direct setInterval
        if (window.pollingManager) {
            this.intervalId = window.pollingManager.register('liveLogViewer', () => {
                this.pollForUpdates();
            }, this.updateInterval);
        } else {
            // Fallback to direct setInterval if PollingManager not available
            this.intervalId = setInterval(() => {
                this.pollForUpdates();
            }, this.updateInterval);
        }
    }
    
    async pollForUpdates() {
        try {
            const apiEndpoint = this.getApiEndpoint();
            const params = this.buildApiParams(this.lastPosition, 50);
            console.log(`[DEBUG] pollForUpdates: Polling ${apiEndpoint}, position: ${this.lastPosition}`);
            const response = await safeFetch(`${apiEndpoint}?${params}`);
            
            if (!response.ok) {
                console.error('[ERROR] pollForUpdates: HTTP error:', response.status);
                
                if (response.status === 401 || response.status === 403) {
                    // Authentication/permission error - stop polling and show error
                    const errorText = await response.text();
                    console.error('[ERROR] pollForUpdates: Auth error:', errorText);
                    this.updateStatus('error', 'Authentication failed');
                    this.stopPolling();
                    
                    const logContent = document.getElementById('logContent');
                    if (logContent) {
                        logContent.innerHTML = `<div style="color: #f59e0b; text-align: center; margin-top: 2rem;">[SESSION] Session expired or insufficient permissions. Please refresh and log in again.</div>`;
                    }
                    return;
                }
                
                throw new Error(`HTTP ${response.status}`);
            }
            
            const data = await response.json();
            console.log('[DEBUG] pollForUpdates: Response data length:', data.content ? data.content.length : 0);
            console.log('[DEBUG] pollForUpdates: Position reset flag:', data.position_reset);
            
            // CRITICAL FIX: Handle position reset from server
            if (data.position_reset) {
                console.log('[DEBUG] pollForUpdates: Server requested position reset - clearing cached content');
                this.allContent = ''; // Clear all cached content
                this.previousDisplayContent = ''; // CRITICAL: Reset content tracking for change detection
                this.lastPosition = 0; // Reset position
                
                // DON'T show reset notification - it interferes with scroll position detection
                console.log('[DEBUG] pollForUpdates: Position reset handled, ready for content update');
            }
            
            if (data.content && data.content.trim()) {
                // Filter out external garbage detection messages before processing
                const filteredContent = this.filterGarbageDetectionMessages(data.content);
                
                if (filteredContent && filteredContent.trim()) {
                    // Handle position reset vs incremental update
                    if (data.position_reset) {
                        // Full content replacement
                        this.allContent = filteredContent;
                        console.log('[DEBUG] pollForUpdates: Replaced all content due to position reset, new length:', this.allContent.length);
                    } else {
                        // Incremental content addition
                        this.allContent += filteredContent;
                        console.log('[DEBUG] pollForUpdates: Added incremental content, total length:', this.allContent.length);
                    }
                    
                    this.lastPosition = data.new_position;
                    this.updateDisplay();
                    this.updateStats(data);
                    this.retryCount = 0; // Reset retry count on success
                } else {
                    console.log('[DEBUG] pollForUpdates: All content was filtered out (garbage detection messages)');
                    // Update position but don't add content
                    this.lastPosition = data.new_position;
                }
            }
            
            // Update connection status
            this.updateStatus('connected', 'Connected');
            
        } catch (error) {
            console.error('[ERROR] pollForUpdates: Failed to poll log updates:', error);
            this.retryCount++;
            
            if (this.retryCount >= this.maxRetries) {
                console.error('[ERROR] pollForUpdates: Max retries reached, stopping polling');
                this.updateStatus('error', 'Connection lost');
                this.stopPolling();
            } else {
                console.warn('[WARN] pollForUpdates: Retrying...', this.retryCount, '/', this.maxRetries);
                this.updateStatus('warning', `Retrying... (${this.retryCount}/${this.maxRetries})`);
            }
        }
    }
    
    /**
     * Filter out external garbage detection messages that interfere with log display
     * These are generated by external monitoring systems and not part of the application logs
     */
    filterGarbageDetectionMessages(content) {
        if (!content) return content;
        
        const garbagePatterns = [
            /\[WARNING\] Garbage content detected.*$/gm,
            /\[DEBUG\] Garbage patterns found:.*$/gm,
            /\[DEBUG\] First \d+ chars:.*$/gm,
            /\[DEBUG\] Reading from:.*garbage_detected.*$/gm,
            /\[DEBUG\] Before seek.*$/gm,
            /\[DEBUG\] After seek.*$/gm,
            /\[DEBUG\] After read.*$/gm,
            /\[DEBUG\] Initial load from.*$/gm,
            /\[ERROR\] File content doesn't start as expected!.*$/gm,
            /\[DEBUG\] First \d+ raw bytes:.*$/gm,
            /\[DEBUG\] Re-reading first \d+ chars from absolute position.*$/gm,
            /\[DEBUG\] Re-read complete file:.*$/gm,
            /\[DEBUG\] Filter options:.*$/gm,
            /\[DEBUG\] No filters active.*$/gm
        ];
        
        let filtered = content;
        let originalLength = content.length;
        
        // Apply all garbage detection filters
        for (const pattern of garbagePatterns) {
            filtered = filtered.replace(pattern, '');
        }
        
        // Clean up multiple consecutive newlines left by filtering
        filtered = filtered.replace(/\n{3,}/g, '\n\n');
        
        if (originalLength !== filtered.length) {
            console.log(`[DEBUG] filterGarbageDetectionMessages: Filtered ${originalLength - filtered.length} characters of garbage content`);
        }
        
        return filtered;
    }
    
    updateDisplay() {
        const logContent = document.getElementById('logContent');
        const searchInput = document.getElementById('logSearchInput');
        if (!logContent) return;
        
        // Apply search filter if active
        const searchTerm = searchInput?.value.toLowerCase() || '';
        if (searchTerm) {
            const lines = this.allContent.split('\n');
            const filteredLines = lines.filter(line => 
                line.toLowerCase().includes(searchTerm)
            );
            this.filteredContent = filteredLines.join('\n');
        } else {
            this.filteredContent = this.allContent;
        }
        
        // Limit display to last 1000 lines for performance
        const lines = this.filteredContent.split('\n');
        if (lines.length > 1000) {
            this.filteredContent = lines.slice(-1000).join('\n');
        }
        
        // Use non-destructive content update to preserve scroll position
        const content = this.filteredContent.trim();
        
        if (content) {
            // Find or create the content div (preserves scroll position)
            let contentDiv = logContent.querySelector('.log-content-div');
            if (!contentDiv) {
                // Only create once on first run
                contentDiv = document.createElement('div');
                contentDiv.className = 'log-content-div';
                contentDiv.style.whiteSpace = 'pre-wrap';
                contentDiv.style.wordBreak = 'break-all';
                contentDiv.style.fontFamily = 'monospace';
                contentDiv.style.fontSize = '13px';
                contentDiv.style.lineHeight = '1.4';
                logContent.appendChild(contentDiv);
            }
            
            // CRITICAL: Check auto-scroll conditions BEFORE updating content
            const shouldAutoScroll = this.shouldAutoScrollBeforeUpdate(logContent, content);
            
            // Update content without destroying container (preserves scroll naturally)
            contentDiv.textContent = content;
            console.log('[DEBUG] updateDisplay: Updated content non-destructively, length:', content.length);
            
            // Apply auto-scroll decision made BEFORE content update
            this.applyAutoScrollAfterUpdate(logContent, shouldAutoScroll);
            
        } else {
            // Handle empty content case - PRESERVE SCROLL POSITION
            let contentDiv = logContent.querySelector('.log-content-div');
            if (!contentDiv) {
                contentDiv = document.createElement('div');
                contentDiv.className = 'log-content-div';
                contentDiv.style.whiteSpace = 'pre-wrap';
                contentDiv.style.wordBreak = 'break-all';
                contentDiv.style.fontFamily = 'monospace';
                contentDiv.style.fontSize = '13px';
                contentDiv.style.lineHeight = '1.4';
                logContent.appendChild(contentDiv);
            }
            contentDiv.innerHTML = '<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">No matching log entries</div>';
        }
        
        // Update line count
        this.lineCount = lines.length;
    }
    
    shouldAutoScrollBeforeUpdate(logContent, currentContent) {
        if (!logContent) {
            console.log('[DEBUG] shouldAutoScrollBeforeUpdate: No logContent element - returning false');
            return false;
        }
        
        // STEP 1: Check if auto-scroll checkbox is enabled
        const autoScrollCheckbox = document.getElementById('logAutoScroll');
        if (!autoScrollCheckbox) {
            console.log('[DEBUG] shouldAutoScrollBeforeUpdate: Auto-scroll checkbox not found - returning false');
            return false;
        }
        
        const isCheckboxChecked = autoScrollCheckbox.checked;
        console.log('[DEBUG] shouldAutoScrollBeforeUpdate: Checkbox checked =', isCheckboxChecked);
        
        if (!isCheckboxChecked) {
            console.log('[DEBUG] shouldAutoScrollBeforeUpdate: AUTO-SCROLL DISABLED - returning false (preserve position)');
            return false;
        }
        
        // STEP 2: Check if content actually changed
        const contentChanged = currentContent !== this.previousDisplayContent;
        if (!contentChanged) {
            console.log('[DEBUG] shouldAutoScrollBeforeUpdate: Content unchanged - returning false (no scroll needed)');
            return false;
        }
        
        console.log('[DEBUG] shouldAutoScrollBeforeUpdate: Content changed, auto-scroll enabled - checking scroll position');
        
        // STEP 3: Check if user is at bottom BEFORE content update
        const scrollTop = logContent.scrollTop;
        const clientHeight = logContent.clientHeight;
        const scrollHeight = logContent.scrollHeight;
        
        // Calculate max possible scroll position
        const maxScrollTop = scrollHeight - clientHeight;
        
        // User is "at bottom" if they're within 20px of the maximum scroll position
        const isAtBottom = (maxScrollTop - scrollTop) <= 20;
        
        console.log('[DEBUG] shouldAutoScrollBeforeUpdate: Scroll analysis:', {
            scrollTop: scrollTop,
            clientHeight: clientHeight, 
            scrollHeight: scrollHeight,
            maxScrollTop: maxScrollTop,
            distanceFromMax: (maxScrollTop - scrollTop),
            isAtBottom: isAtBottom,
            threshold: '20px'
        });
        
        if (isAtBottom) {
            console.log('[DEBUG] shouldAutoScrollBeforeUpdate: User IS at bottom - returning TRUE (will auto-scroll)');
            return true;
        } else {
            console.log('[DEBUG] shouldAutoScrollBeforeUpdate: User NOT at bottom - returning FALSE (preserve position)');
            return false;
        }
    }
    
    applyAutoScrollAfterUpdate(logContent, shouldAutoScroll) {
        // Update content tracking for next comparison (always do this)
        this.previousDisplayContent = this.filteredContent.trim();
        
        if (!logContent) {
            console.log('[DEBUG] applyAutoScrollAfterUpdate: No logContent element');
            return;
        }
        
        console.log('[DEBUG] applyAutoScrollAfterUpdate: Decision =', shouldAutoScroll);
        
        if (shouldAutoScroll === true) {
            console.log('[DEBUG] applyAutoScrollAfterUpdate: EXECUTING AUTO-SCROLL to bottom');
            
            // Use setTimeout to ensure DOM has been updated after content change
            setTimeout(() => {
                const beforeScroll = logContent.scrollTop;
                const scrollHeight = logContent.scrollHeight;
                const clientHeight = logContent.clientHeight;
                
                // Scroll to bottom: scrollTop = scrollHeight - clientHeight
                logContent.scrollTop = scrollHeight;
                
                const afterScroll = logContent.scrollTop;
                
                console.log('[DEBUG] applyAutoScrollAfterUpdate: Scroll executed:', {
                    beforeScroll: beforeScroll,
                    scrollHeight: scrollHeight,
                    clientHeight: clientHeight,
                    targetScrollTop: scrollHeight,
                    actualScrollTop: afterScroll,
                    success: (afterScroll > beforeScroll)
                });
            }, 50);  // Increased delay to ensure content rendering is complete
        } else {
            console.log('[DEBUG] applyAutoScrollAfterUpdate: NOT auto-scrolling - position naturally preserved');
        }
    }
    
    updateStats(data) {
        const lineCountElement = document.getElementById('logLineCount');
        const sizeInfoElement = document.getElementById('logSizeInfo');
        const lastUpdateElement = document.getElementById('logLastUpdate');
        const logSourceInfo = document.getElementById('logSourceInfo');
        const logFilteredCount = document.getElementById('logFilteredCount');
        
        if (lineCountElement) {
            lineCountElement.textContent = this.lineCount.toLocaleString();
        }
        
        if (sizeInfoElement && data.file_size) {
            const sizeKB = Math.round(data.file_size / 1024);
            sizeInfoElement.textContent = `${sizeKB} KB`;
        }
        
        if (lastUpdateElement) {
            const now = new Date();
            lastUpdateElement.textContent = now.toLocaleTimeString();
        }
        
        // Update source information
        if (logSourceInfo) {
            const sourceNames = {
                'debug': 'Debug Trace',
                'console': 'Console Output', 
                'system': 'System Events'
            };
            logSourceInfo.textContent = sourceNames[this.logSource] || 'Debug Trace';
        }
        
        // Update filtered count
        if (logFilteredCount) {
            const totalLines = this.allContent.split('\n').length;
            const filteredLines = this.filteredContent.split('\n').length;
            logFilteredCount.textContent = filteredLines.toLocaleString();
            
            // Show percentage if filtered
            if (totalLines > 0 && filteredLines !== totalLines) {
                const percentage = Math.round((filteredLines / totalLines) * 100);
                logFilteredCount.textContent = `${filteredLines.toLocaleString()} (${percentage}%)`;
                logFilteredCount.style.color = '#f59e0b'; // Orange for filtered
            } else {
                logFilteredCount.style.color = '#94a3b8'; // Normal gray
            }
        }
    }
    
    updateStatus(type, message) {
        const statusIcon = document.getElementById('logStatusIcon');
        const statusText = document.getElementById('logStatusText');
        
        if (!statusIcon || !statusText) return;
        
        statusText.textContent = message;
        
        switch (type) {
            case 'connected':
                statusIcon.style.background = '#10b981';
                break;
            case 'connecting':
                statusIcon.style.background = '#f59e0b';
                break;
            case 'warning':
                statusIcon.style.background = '#f59e0b';
                break;
            case 'error':
                statusIcon.style.background = '#ef4444';
                break;
            default:
                statusIcon.style.background = '#6b7280';
        }
    }
    
    toggle() {
        this.isPaused = !this.isPaused;
        const pauseBtn = document.getElementById('logPauseBtn');
        
        if (this.isPaused) {
            if (pauseBtn) pauseBtn.innerHTML = '[RESUME] Resume';
            this.updateStatus('warning', 'Paused');
        } else {
            if (pauseBtn) pauseBtn.innerHTML = '[PAUSE] Pause';
            this.updateStatus('connected', 'Connected');
        }
    }
    
    clear() {
        this.allContent = '';
        this.filteredContent = '';
        this.lineCount = 0;
        const logContent = document.getElementById('logContent');
        if (logContent) {
            logContent.innerHTML = '<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">Log display cleared</div>';
        }
        this.updateStats({ file_size: 0 });
    }
    
    copy() {
        if (!this.filteredContent.trim()) {
            alert('No log content to copy');
            return;
        }
        
        navigator.clipboard.writeText(this.filteredContent).then(() => {
            // Visual feedback
            const copyBtn = document.querySelector('button[onclick="copyLogContent()"]');
            if (copyBtn) {
                const originalText = copyBtn.innerHTML;
                copyBtn.innerHTML = '[COPIED] Copied';
                setTimeout(() => {
                    copyBtn.innerHTML = originalText;
                }, 2000);
            }
        }).catch(err => {
            console.error('Failed to copy log content:', err);
            alert('Failed to copy log content to clipboard');
        });
    }
    
    
    filter(searchTerm) {
        // Re-apply display with current search term
        this.updateDisplay();
    }
    
    getApiEndpoint() {
        // UNIFIED: Always use the same live-logs endpoint with filter_type parameter
        return '/dashboard/api/live-logs';
    }
    
    buildApiParams(lastPosition, maxLines) {
        // UNIFIED: Always use live-logs API parameters with filter_type
        let params = '';
        
        // Standard live logs API parameters
        params += `last_position=${lastPosition}`;
        params += `&max_lines=${maxLines}`;
        params += `&format=raw`;
        
        // Add filter_type parameter for different view modes
        params += `&filter_type=${this.logSource}`;
        
        // Add additional filtering parameters
        const filterParams = this.buildFilterParams();
        params += filterParams;
        
        return params;
    }
    
    buildFilterParams() {
        // Build URL parameters for server-side filtering (only for live-logs endpoint)
        let params = '';
        
        // If raw mode is enabled, bypass all filtering
        if (this.rawMode) {
            console.log('[DEBUG] Raw mode active - bypassing all server-side filtering');
            return '';
        }
        
        // Get current filter options (either from this.filterOptions or from UI)
        const filterOptions = this.filterOptions || {};
        
        // Read from UI if filter options not set
        const excludeFrontendDebug = filterOptions.exclude_frontend_debug !== undefined ? 
            filterOptions.exclude_frontend_debug : 
            (document.getElementById('excludeFrontendDebug')?.checked || false);
            
        const excludeScrubbedAuth = filterOptions.exclude_scrubbed !== undefined ?
            filterOptions.exclude_scrubbed :
            (document.getElementById('excludeScrubbedAuth')?.checked || false);
            
        const eventCodes = filterOptions.event_codes !== undefined ?
            filterOptions.event_codes :
            (document.getElementById('eventCodesFilter')?.value || '');
            
        const searchTerm = filterOptions.search_term !== undefined ?
            filterOptions.search_term :
            (document.getElementById('logSearchInput')?.value || '');
        
        // Apply special filtering for system logs
        if (this.logSource === 'system') {
            // Force specific event codes for system logs
            params += '&event_codes=INIT,USER,METRICS,EVENT';
            params += '&exclude_frontend_debug=true';
            params += '&exclude_scrubbed=true';
        } else {
            // Normal filtering for debug logs
            if (excludeFrontendDebug) params += '&exclude_frontend_debug=true';
            if (excludeScrubbedAuth) params += '&exclude_scrubbed=true';
            if (eventCodes.trim()) params += `&event_codes=${encodeURIComponent(eventCodes)}`;
        }
        
        if (searchTerm.trim()) params += `&search_term=${encodeURIComponent(searchTerm)}`;
        
        return params;
    }
    
    async fetchLogs() {
        // Force reload with current filters
        if (this.isPaused) {
            this.isPaused = false;
            const pauseBtn = document.getElementById('logPauseBtn');
            if (pauseBtn) {
                pauseBtn.textContent = '[PAUSE] Pause';
                pauseBtn.title = 'Pause real-time updates';
            }
        }
        
        await this.pollForUpdates();
        
        // Restart polling if not already running
        if (!this.intervalId && this.isInitialized) {
            this.startPolling();
        }
    }
    
    stopPolling() {
        if (this.intervalId) {
            // Unregister from PollingManager or clear direct interval
            if (window.pollingManager) {
                window.pollingManager.unregister('liveLogViewer');
            } else {
                clearInterval(this.intervalId);
            }
            this.intervalId = null;
        }
    }
    
    destroy() {
        this.stopPolling();
        this.isInitialized = false;
    }
}

// Global instance
let liveLogViewer = new LiveLogViewer();

// Global functions for template button handlers
function initializeLiveLogViewer() {
    try {
        console.log('[DEBUG] Initializing live log viewer...');
        liveLogViewer.initialize();
        
        // Initialize Raw Mode button with error handling
        setTimeout(() => {
            try {
                updateRawModeButton();
                console.log('[DEBUG] Raw mode button initialized successfully');
            } catch (error) {
                console.error('[ERROR] Failed to initialize raw mode button:', error);
            }
        }, 100);
        
        console.log('[DEBUG] Live log viewer initialization completed');
    } catch (error) {
        console.error('[ERROR] Failed to initialize live log viewer:', error);
        // Create a fallback message for users
        const logContainer = document.getElementById('logContent');
        if (logContainer) {
            logContainer.innerHTML = '<div style="color: #ef4444; padding: 1rem;">Live log viewer failed to initialize. Please refresh the page.</div>';
        }
    }
}


function copyLogContent() {
    liveLogViewer.copy();
}

// Force refresh functionality - clear all cache and reload fresh content
function forceRefreshLogs() {
    if (typeof liveLogViewer !== 'undefined') {
        console.log('[DEBUG] Force refresh requested - clearing all cached state');
        
        // Clear all cached content and reset position
        liveLogViewer.allContent = '';
        liveLogViewer.filteredContent = '';
        liveLogViewer.lastPosition = 0;
        liveLogViewer.retryCount = 0;
        
        // Show refreshing message
        const logContent = document.getElementById('logContent');
        if (logContent) {
            logContent.innerHTML = '<div style="color: #f59e0b; text-align: center; margin-top: 2rem;">[REFRESH] Force refreshing - clearing cache and reloading all content...</div>';
        }
        
        // Force a full reload
        setTimeout(async () => {
            try {
                await liveLogViewer.loadInitialContent();
                console.log('[DEBUG] Force refresh completed');
            } catch (error) {
                console.error('[ERROR] Force refresh failed:', error);
                if (logContent) {
                    logContent.innerHTML = `<div style="color: #ef4444; text-align: center; margin-top: 2rem;">[FAILED] Force refresh failed: ${error.message}</div>`;
                }
            }
        }, 100);
    } else {
        console.error('[ERROR] forceRefreshLogs: liveLogViewer not available');
    }
}

// Raw Mode functionality - shows exact debug_trace.log content without any filtering
let rawModeEnabled = false;

function toggleRawMode() {
    rawModeEnabled = !rawModeEnabled;
    updateRawModeButton();
    
    if (rawModeEnabled) {
        console.log('[DEBUG] Raw mode ENABLED - showing unfiltered debug_trace.log tail content');
        // Force refresh with raw mode starting from current tail
        liveLogViewer.lastPosition = 0;  // This triggers tail reading logic
        liveLogViewer.rawMode = true;
        liveLogViewer.allContent = '';   // Clear existing content
        
        // Show loading message
        const logContent = document.getElementById('logContent');
        if (logContent) {
            logContent.innerHTML = '<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">[LOADING] Loading raw debug_trace.log content...</div>';
        }
        
        liveLogViewer.fetchLogs();
    } else {
        console.log('[DEBUG] Raw mode DISABLED - using normal filtering from current tail');
        // Reset to normal mode but also start from current tail
        liveLogViewer.lastPosition = 0;  // This triggers tail reading logic
        liveLogViewer.rawMode = false;
        liveLogViewer.allContent = '';   // Clear existing content
        
        // Show loading message
        const logContent = document.getElementById('logContent');
        if (logContent) {
            logContent.innerHTML = '<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">[LOADING] Loading filtered log content...</div>';
        }
        
        liveLogViewer.fetchLogs();
    }
}

function updateRawModeButton() {
    try {
        const button = document.getElementById('rawModeToggle');
        if (button) {
            if (rawModeEnabled) {
                button.style.background = '#059669'; // Darker green when active
                button.innerHTML = '[ON] Raw Mode';
                button.title = 'Raw mode ON - showing exact debug_trace.log content (click to disable)';
            } else {
                button.style.background = '#10b981'; // Normal green when inactive
                button.innerHTML = '[OFF] Raw Mode';
                button.title = 'Raw mode OFF - applying filters (click to show exact debug_trace.log content)';
            }
        } else {
            console.debug('[DEBUG] Raw mode button element not found');
        }
    } catch (error) {
        console.error('[ERROR] updateRawModeButton failed:', error);
    }
}

function jumpToLatestLogs() {
    console.log('[DEBUG] Jump to Latest clicked - resetting to current tail');
    // Reset position to force reading from current end of file
    liveLogViewer.lastPosition = 0;
    liveLogViewer.allContent = '';
    
    // Clear current display 
    const logContent = document.getElementById('logContent');
    if (logContent) {
        logContent.innerHTML = '<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">[JUMPING] Jumping to latest logs...</div>';
    }
    
    // Force immediate refresh from tail
    liveLogViewer.fetchLogs();
}


function filterLogContent() {
    const searchInput = document.getElementById('logSearchInput');
    if (searchInput) {
        liveLogViewer.filter(searchInput.value);
    }
}

// Apply server-side log filtering
function applyLogFilters() {
    if (!liveLogViewer) {
        console.warn('Live log viewer not initialized');
        return;
    }
    
    const excludeFrontendDebug = document.getElementById('excludeFrontendDebug')?.checked || false;
    const excludeScrubbedAuth = document.getElementById('excludeScrubbedAuth')?.checked || false;
    const eventCodesFilter = document.getElementById('eventCodesFilter')?.value || '';
    const searchTerm = document.getElementById('logSearchInput')?.value || '';
    
    // Update filter status display
    updateFilterStatus(excludeFrontendDebug, excludeScrubbedAuth, eventCodesFilter);
    
    // Reset position to reload with filters
    liveLogViewer.lastPosition = 0;
    liveLogViewer.filterOptions = {
        exclude_frontend_debug: excludeFrontendDebug,
        exclude_scrubbed: excludeScrubbedAuth,
        event_codes: eventCodesFilter,
        search_term: searchTerm
    };
    
    // Clear current content and reload with filters
    const logContent = document.getElementById('logContent');
    if (logContent) {
        logContent.innerHTML = '<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">[FILTERING] Applying filters...</div>';
    }
    
    // Trigger immediate update with filters
    liveLogViewer.fetchLogs();
}

function updateFilterStatus(excludeFrontendDebug, excludeScrubbedAuth, eventCodesFilter) {
    const filterStatus = document.getElementById('filterStatus');
    if (!filterStatus) return;
    
    const activeFilters = [];
    if (excludeFrontendDebug) activeFilters.push('Hide FRONTEND_DEBUG');
    if (excludeScrubbedAuth) activeFilters.push('Hide scrubbed auth');
    if (eventCodesFilter.trim()) activeFilters.push(`Event codes: ${eventCodesFilter}`);
    
    if (activeFilters.length === 0) {
        filterStatus.textContent = 'Status: All filters disabled';
        filterStatus.style.color = '#64748b';
    } else {
        filterStatus.textContent = `Active: ${activeFilters.join(', ')}`;
        filterStatus.style.color = '#10b981';
    }
}

// Switch between log sources (debug, console, system)
function switchLogSource(source) {
    if (!liveLogViewer) {
        console.warn('Live log viewer not initialized');
        return;
    }
    
    console.log(`[DEBUG] Switching to log source: ${source}`);
    
    // Update tab appearance
    const tabs = document.querySelectorAll('.log-source-tab');
    tabs.forEach(tab => {
        tab.style.background = '#374151';
        tab.style.color = '#94a3b8';
        tab.classList.remove('active');
    });
    
    const activeTab = document.getElementById(`logSource${source.charAt(0).toUpperCase() + source.slice(1)}`);
    if (activeTab) {
        activeTab.style.background = '#3b82f6';
        activeTab.style.color = 'white';
        activeTab.classList.add('active');
    }
    
    // Update the live log viewer source
    liveLogViewer.logSource = source;
    liveLogViewer.lastPosition = 0;  // Reset position for new source
    
    // Clear current content and show loading
    const logContent = document.getElementById('logContent');
    if (logContent) {
        logContent.innerHTML = `<div style="color: #94a3b8; text-align: center; margin-top: 2rem;">[LOADING] Loading ${source} logs...</div>`;
    }
    
    // Update status to show source change
    liveLogViewer.updateStatus('connecting', `Switching to ${source} logs...`);
    
    // Update source info in stats immediately
    const logSourceInfo = document.getElementById('logSourceInfo');
    if (logSourceInfo) {
        const sourceNames = {
            'debug': 'Debug Trace',
            'console': 'Console Output',
            'system': 'System Events'
        };
        logSourceInfo.textContent = sourceNames[source] || 'Debug Trace';
    }
    
    // Trigger reload with new source
    liveLogViewer.fetchLogs();
}

// Generic Notification System
class NotificationSystem {
    constructor() {
        this.notifications = [];
        this.container = null;
        this.initialized = false;
    }
    
    initialize() {
        if (this.initialized) return;
        
        // Create notification container
        this.container = document.createElement('div');
        this.container.id = 'notificationContainer';
        this.container.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 10000;
            max-width: 400px;
            pointer-events: none;
        `;
        document.body.appendChild(this.container);
        this.initialized = true;
    }
    
    show(message, type = 'info', duration = 5000) {
        this.initialize();
        
        const notification = document.createElement('div');
        const notificationId = 'notification_' + Date.now() + Math.random();
        notification.id = notificationId;
        
        // Set styles based on type
        const typeStyles = {
            info: {
                background: 'rgba(59, 130, 246, 0.9)',
                border: '1px solid rgba(59, 130, 246, 0.3)',
                color: '#ffffff',
                icon: '[INFO]'
            },
            success: {
                background: 'rgba(16, 185, 129, 0.9)', 
                border: '1px solid rgba(16, 185, 129, 0.3)',
                color: '#ffffff',
                icon: '[OK]'
            },
            warning: {
                background: 'rgba(245, 158, 11, 0.9)',
                border: '1px solid rgba(245, 158, 11, 0.3)', 
                color: '#ffffff',
                icon: '[WARN]'
            },
            error: {
                background: 'rgba(239, 68, 68, 0.9)',
                border: '1px solid rgba(239, 68, 68, 0.3)',
                color: '#ffffff', 
                icon: '[ERROR]'
            }
        };
        
        const style = typeStyles[type] || typeStyles.info;
        
        notification.style.cssText = `
            background: ${style.background};
            border: ${style.border};
            color: ${style.color};
            padding: 12px 16px;
            margin-bottom: 10px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
            backdrop-filter: blur(10px);
            font-size: 14px;
            font-weight: 500;
            line-height: 1.4;
            pointer-events: auto;
            cursor: pointer;
            transition: all 0.3s ease;
            transform: translateX(100%);
            opacity: 0;
            animation: slideIn 0.3s ease forwards;
        `;
        
        notification.innerHTML = `
            <div style="display: flex; align-items: flex-start; gap: 8px;">
                <span style="font-size: 16px; flex-shrink: 0;">${style.icon}</span>
                <div style="flex: 1;">
                    <div style="font-weight: 600; margin-bottom: 2px;">${type.charAt(0).toUpperCase() + type.slice(1)}</div>
                    <div>${message}</div>
                </div>
                <button onclick="removeNotification('${notificationId}')" 
                        style="background: none; border: none; color: inherit; font-size: 18px; cursor: pointer; padding: 0; margin-left: 8px; opacity: 0.7; transition: opacity 0.2s;"
                        onmouseover="this.style.opacity='1'" 
                        onmouseout="this.style.opacity='0.7'">[X]</button>
            </div>
        `;
        
        // Click to dismiss
        notification.addEventListener('click', () => {
            this.remove(notificationId);
        });
        
        // Add slide-in animation
        const style_element = document.createElement('style');
        if (!document.getElementById('notification-animations')) {
            style_element.id = 'notification-animations';
            style_element.textContent = `
                @keyframes slideIn {
                    from {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                    to {
                        transform: translateX(0);
                        opacity: 1;
                    }
                }
                @keyframes slideOut {
                    from {
                        transform: translateX(0);
                        opacity: 1;
                    }
                    to {
                        transform: translateX(100%);
                        opacity: 0;
                    }
                }
            `;
            document.head.appendChild(style_element);
        }
        
        this.container.appendChild(notification);
        this.notifications.push(notificationId);
        
        // Auto-remove after duration (if duration > 0)
        if (duration > 0) {
            setTimeout(() => {
                this.remove(notificationId);
            }, duration);
        }
        
        return notificationId;
    }
    
    remove(notificationId) {
        const notification = document.getElementById(notificationId);
        if (notification) {
            notification.style.animation = 'slideOut 0.3s ease forwards';
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
                this.notifications = this.notifications.filter(id => id !== notificationId);
            }, 300);
        }
    }
    
    clear() {
        this.notifications.forEach(id => this.remove(id));
    }
}

// Global notification system instance
const notificationSystem = new NotificationSystem();

// Global notification functions
function showNotification(message, type = 'info', duration = 5000) {
    return notificationSystem.show(message, type, duration);
}

function removeNotification(notificationId) {
    notificationSystem.remove(notificationId);
}

function clearAllNotifications() {
    notificationSystem.clear();
}

// Test_Real Server Detection and Redirection
async function checkAndRedirectTestReal() {
    try {
        let targetPort;
        let isTestEnv = false;
        
        // Try to determine environment via API first
        try {
            const envResponse = await safeFetch('/dashboard/api/check-test-server?port=40999');
            
            if (envResponse.ok) {
                const envData = await envResponse.json();
                isTestEnv = envData.test_environment === true;
            } else {
                console.log('API authentication failed, using fallback port detection');
                // Fallback: determine environment by current port
                isTestEnv = window.location.port === '40999';
            }
        } catch (apiError) {
            console.log('API check failed, using fallback port detection:', apiError);
            // Fallback: determine environment by current port
            isTestEnv = window.location.port === '40999';
        }
        
        // If in test environment: button targets :41000 (production)
        // If NOT in test environment: button targets :40999 (test)
        if (isTestEnv) {
            targetPort = '41000';  // Test env → go to production
        } else {
            targetPort = '40999';  // Production → go to test
        }
        
        console.log(`Checking test_real server availability on port ${targetPort}`);
        showNotification(`Checking test_real server on port ${targetPort}...`, 'info', 2000);
        
        // Check if target server is available
        try {
            const response = await safeFetch(`/dashboard/api/check-test-server?port=${targetPort}`);
            
            if (!response.ok) {
                // API error - use direct connection test as fallback
                throw new Error(`API returned ${response.status}`);
            }
            
            const data = await response.json();
            
            if (data.available) {
                const targetUrl = `http://localhost:${targetPort}/`;
                showNotification(`Redirecting to test_real server on port ${targetPort}`, 'success', 2000);
                
                // Redirect after a short delay for user feedback
                setTimeout(() => {
                    window.location.href = targetUrl;
                }, 1000);
            } else {
                // Server not available - show appropriate error
                showNotification('there is no test_real_ running currently', 'error', 10000);
                console.log('Test_real server not available:', data);
            }
        } catch (apiError) {
            console.log('API check failed, attempting direct connection test:', apiError);
            
            // Fallback: try direct connection to target port
            try {
                const testUrl = `http://localhost:${targetPort}/`;
                const directResponse = await fetch(testUrl, { 
                    method: 'HEAD', 
                    mode: 'no-cors',
                    timeout: 3000 
                });
                
                // If we get here without error, server is likely running
                showNotification(`Redirecting to test_real server on port ${targetPort}`, 'success', 2000);
                setTimeout(() => {
                    window.location.href = testUrl;
                }, 1000);
                
            } catch (directError) {
                // Server definitely not available
                showNotification('there is no test_real_ running currently', 'error', 10000);
                console.log('Direct connection test failed - server not available:', directError);
            }
        }
        
    } catch (error) {
        // This should not happen due to comprehensive error handling above
        console.error('Unexpected error in checkAndRedirectTestReal:', error);
        showNotification('there is no test_real_ running currently', 'error', 10000);
    }
}

// Header styling for localhost:40999 detection
function checkAndApplyHeaderStyling() {
    // Check if we're on localhost:40999
    if (window.location.hostname === 'localhost' && window.location.port === '40999') {
        const header = document.querySelector('.header');
        if (header) {
            header.classList.add('test-server-mode');
            console.log('Applied red header styling for localhost:40999');
        }
    }
}

// Initialize header styling on page load
document.addEventListener('DOMContentLoaded', function() {
    checkAndApplyHeaderStyling();
});

// Also check when the page becomes visible (in case of navigation)
document.addEventListener('visibilitychange', function() {
    if (!document.hidden) {
        checkAndApplyHeaderStyling();
    }
});

// Centralized Polling Manager - CRITICAL FIX for "stop server pollution when page is hidden"
class PollingManager {
    constructor() {
        this.pollers = new Map(); // Map of poller_id -> {intervalId, callback, interval, isActive}
        this.isPageVisible = !document.hidden;
        this.setupVisibilityListener();
        
        console.log('[DEBUG] PollingManager initialized, page visible:', this.isPageVisible);
    }
    
    setupVisibilityListener() {
        // Page Visibility API support
        document.addEventListener('visibilitychange', () => {
            const wasVisible = this.isPageVisible;
            this.isPageVisible = !document.hidden;
            
            console.log('[DEBUG] Page visibility changed:', wasVisible, '->', this.isPageVisible);
            
            if (this.isPageVisible && !wasVisible) {
                // Page became visible - resume all polling and do immediate refresh
                console.log('[INFO] Page became visible - resuming polling and refreshing data');
                this.resumeAllPolling();
                this.performImmediateRefresh();
            } else if (!this.isPageVisible && wasVisible) {
                // Page became hidden - pause all polling to prevent server load
                console.log('[INFO] Page became hidden - pausing all polling to prevent server load');
                this.pauseAllPolling();
            }
        });
    }
    
    register(pollerId, callback, interval) {
        console.log(`[DEBUG] PollingManager: Registering poller '${pollerId}' with ${interval}ms interval`);
        
        // Stop any existing poller with same ID
        this.unregister(pollerId);
        
        const poller = {
            callback: callback,
            interval: interval,
            intervalId: null,
            isActive: false
        };
        
        this.pollers.set(pollerId, poller);
        
        // Start immediately if page is visible
        if (this.isPageVisible) {
            this.start(pollerId);
        } else {
            console.log(`[DEBUG] PollingManager: Page hidden - not starting poller '${pollerId}'`);
        }
        
        return pollerId;
    }
    
    unregister(pollerId) {
        if (this.pollers.has(pollerId)) {
            this.stop(pollerId);
            this.pollers.delete(pollerId);
            console.log(`[DEBUG] PollingManager: Unregistered poller '${pollerId}'`);
        }
    }
    
    start(pollerId) {
        const poller = this.pollers.get(pollerId);
        if (!poller) return false;
        
        if (poller.intervalId) {
            clearInterval(poller.intervalId);
        }
        
        // Only start if page is visible
        if (!this.isPageVisible) {
            console.log(`[DEBUG] PollingManager: Not starting '${pollerId}' - page is hidden`);
            return false;
        }
        
        poller.intervalId = setInterval(poller.callback, poller.interval);
        poller.isActive = true;
        
        console.log(`[DEBUG] PollingManager: Started poller '${pollerId}' (${poller.interval}ms)`);
        return true;
    }
    
    stop(pollerId) {
        const poller = this.pollers.get(pollerId);
        if (!poller) return false;
        
        if (poller.intervalId) {
            clearInterval(poller.intervalId);
            poller.intervalId = null;
        }
        
        poller.isActive = false;
        console.log(`[DEBUG] PollingManager: Stopped poller '${pollerId}'`);
        return true;
    }
    
    pauseAllPolling() {
        console.log('[INFO] PollingManager: Pausing all polling (page hidden)');
        let pausedCount = 0;
        
        for (const [pollerId, poller] of this.pollers) {
            if (poller.isActive) {
                this.stop(pollerId);
                pausedCount++;
            }
        }
        
        console.log(`[INFO] PollingManager: Paused ${pausedCount} pollers`);
        
        // Update UI to show paused state
        this.updateVisibilityStatus('hidden');
    }
    
    resumeAllPolling() {
        console.log('[INFO] PollingManager: Resuming all polling (page visible)');
        let resumedCount = 0;
        
        for (const [pollerId] of this.pollers) {
            if (this.start(pollerId)) {
                resumedCount++;
            }
        }
        
        console.log(`[INFO] PollingManager: Resumed ${resumedCount} pollers`);
        
        // Update UI to show active state
        this.updateVisibilityStatus('visible');
    }
    
    performImmediateRefresh() {
        console.log('[DEBUG] PollingManager: Performing immediate refresh for all pollers');
        
        // Execute all poller callbacks immediately to refresh data
        for (const [pollerId, poller] of this.pollers) {
            try {
                console.log(`[DEBUG] PollingManager: Immediate refresh for '${pollerId}'`);
                poller.callback();
            } catch (error) {
                console.error(`[ERROR] PollingManager: Immediate refresh failed for '${pollerId}':`, error);
            }
        }
    }
    
    updateVisibilityStatus(state) {
        // Update live log viewer status
        if (window.liveLogViewer && typeof liveLogViewer.updateStatus === 'function') {
            if (state === 'hidden') {
                liveLogViewer.updateStatus('warning', 'Paused (page hidden)');
            } else {
                liveLogViewer.updateStatus('connected', 'Connected');
            }
        }
        
        // Show notification to user
        if (typeof showNotification === 'function') {
            if (state === 'hidden') {
                showNotification('Polling paused - page is not visible', 'info', 3000);
            } else {
                showNotification('Polling resumed - refreshing data', 'success', 2000);
            }
        }
    }
    
    getStatus() {
        const activeCount = Array.from(this.pollers.values()).filter(p => p.isActive).length;
        return {
            isPageVisible: this.isPageVisible,
            totalPollers: this.pollers.size,
            activePollers: activeCount,
            pollers: Array.from(this.pollers.keys())
        };
    }
}

// Global error handler to catch and log JavaScript errors
window.addEventListener('error', function(e) {
    // Suppress specific document.write() errors that don't affect functionality
    if (e.message && (
        e.message.includes('Failed to execute \'write\' on \'Document\'') ||
        e.message.includes('Unexpected token \'<\'') ||
        e.message.includes('document.write')
    )) {
        console.log('Suppressed harmless document.write error:', e.message);
        return true; // Prevent showing error notification to user
    }
    
    console.error('Global error:', e.error);
    console.error('Error message:', e.message);
    console.error('Source:', e.filename + ':' + e.lineno + ':' + e.colno);
    
    // Send error info to debug trace if available
    if (typeof debugTrace === 'function') {
        debugTrace(`JavaScript Error: ${e.message} at ${e.filename}:${e.lineno}`);
    }
    
    return false; // Don't prevent default error handling
});

// Global unhandled promise rejection handler
window.addEventListener('unhandledrejection', function(e) {
    console.error('Unhandled promise rejection:', e.reason);
    
    // Send error info to debug trace if available
    if (typeof debugTrace === 'function') {
        debugTrace(`Unhandled Promise Rejection: ${e.reason}`);
    }
    
    return false; // Don't prevent default error handling
});

console.log('[DEBUG] dashboard-live-logs.js module loaded');