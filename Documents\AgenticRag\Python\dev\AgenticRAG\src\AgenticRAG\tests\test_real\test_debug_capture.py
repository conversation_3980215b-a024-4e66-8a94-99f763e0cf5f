"""
Test debug output capture module for test_real framework.

This module captures stdout/stderr to test-specific log files, similar to how
production handles debug_trace.txt but with test-appropriate file paths.

The captured logs are stored in: tests/test_real/logs/<test_function_name>_<timestamp>.log

This allows complete visibility into test execution, including all system output,
making it easier to debug test failures and understand system behavior during testing.
"""
import sys
import os
import functools
from datetime import datetime
from typing import TextIO, Optional
from pathlib import Path
import inspect

# Global flag to prevent main debug capture interference
_test_capture_active = False


class TestTeeOutput:
    """Tee class that writes to both console and test log file simultaneously"""
    
    def __init__(self, original_stream: TextIO, log_file_path: str):
        self.original_stream = original_stream
        self.log_file_path = log_file_path
        self.log_file = None
        self._open_log_file()
    
    def _open_log_file(self):
        """Open or reopen the log file"""
        try:
            if self.log_file and not self.log_file.closed:
                self.log_file.close()
            self.log_file = open(self.log_file_path, 'a', encoding='utf-8')
        except Exception as e:
            print(f"Warning: Could not open test log file: {e}")
            self.log_file = None
    
    def write(self, text: str):
        """Write to both original stream and log file"""
        # Write to original stream (console)
        if self.original_stream:
            self.original_stream.write(text)
            self.original_stream.flush()
        
        # Write to log file with timestamp
        if self.log_file and not self.log_file.closed:
            try:
                # Add timestamp to each line (similar to production debug_trace.txt)
                if text.strip():  # Only add timestamp to non-empty lines
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    # Split by lines and add timestamp to each
                    lines = text.splitlines(keepends=True)
                    for line in lines:
                        if line.strip():  # Only process non-empty lines
                            self.log_file.write(f"[{timestamp}] {line}")
                        else:
                            self.log_file.write(line)
                else:
                    self.log_file.write(text)
                self.log_file.flush()
            except Exception as e:
                # If writing to log file fails, continue with console output
                pass
    
    def flush(self):
        """Flush both streams"""
        if self.original_stream:
            self.original_stream.flush()
        if self.log_file and not self.log_file.closed:
            try:
                self.log_file.flush()
            except Exception:
                pass
    
    def close(self):
        """Close the log file"""
        if self.log_file and not self.log_file.closed:
            try:
                self.log_file.close()
            except Exception:
                pass
    
    def __getattr__(self, name):
        """Delegate other attributes to original stream"""
        return getattr(self.original_stream, name)


# Global variables to track capture state
_original_stdout = None
_original_stderr = None
_tee_stdout = None
_tee_stderr = None
_log_file_path = None


def setup_test_debug_capture(test_function_name: Optional[str] = None):
    """
    Setup stdout/stderr capture to test-specific log file.
    
    Args:
        test_function_name: Name of the test function. If not provided, 
                           will try to detect from call stack.
    """
    global _original_stdout, _original_stderr, _tee_stdout, _tee_stderr, _log_file_path, _test_capture_active
    
    # Set environment flags to prevent global debug capture interference
    os.environ['PYTEST_RUNNING'] = '1'
    os.environ['TEST_CAPTURE_ACTIVE'] = '1'  # Prevent circular import in debug_stdout_capture
    if test_function_name:
        os.environ['TEST_NAME'] = test_function_name
    
    # If already capturing, clean up first
    if _original_stdout is not None:
        cleanup_test_debug_capture()
    
    # Store original streams
    _original_stdout = sys.stdout
    _original_stderr = sys.stderr
    
    # Determine test function name if not provided
    if not test_function_name:
        # Try to get from call stack
        frame = inspect.currentframe()
        while frame:
            func_name = frame.f_code.co_name
            if func_name.startswith('test_'):
                test_function_name = func_name
                break
            frame = frame.f_back
        
        if not test_function_name:
            test_function_name = "unknown_test"
    
    # Create logs directory
    logs_dir = Path(f"tests/test_real/logs/{test_function_name}")
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate log filename with timestamp
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_filename = f"{timestamp}.log"
    _log_file_path = logs_dir / log_filename
    
    # Initialize log file with header
    try:
        with open(_log_file_path, 'w') as f:
            f.write(f"=== TEST DEBUG TRACE STARTED ===\n")
            f.write(f"Test Function: {test_function_name}\n")
            f.write(f"Started: {datetime.now()}\n")
            f.write(f"{'='*80}\n\n")
    except Exception as e:
        print(f"Warning: Could not initialize test trace file: {e}")
        return
    
    # Set flag to prevent main debug capture interference
    _test_capture_active = True
    
    # ENHANCED: Register with global test environment detector
    try:
        sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))
        from etc.test_environment_detector import set_test_environment_active, extend_test_timeout
        
        # Set global test state with extended timeout for background tasks
        test_timeout = 900.0  # 15 minutes to allow for background task execution
        set_test_environment_active(test_function_name, _log_file_path, test_timeout)
        
        print(f"[TEST_ENV] Global test environment registered: {test_function_name}")
    except Exception as e:
        print(f"[TEST_ENV] Warning: Could not register with global test environment: {e}")
    
    # Replace stdout and stderr with Tee objects
    _tee_stdout = TestTeeOutput(_original_stdout, str(_log_file_path))
    _tee_stderr = TestTeeOutput(_original_stderr, str(_log_file_path))
    sys.stdout = _tee_stdout
    sys.stderr = _tee_stderr
    
    print(f"Test debug trace capture activated - all console output will be logged to {_log_file_path}")


def cleanup_test_debug_capture():
    """Restore original stdout/stderr and close log file"""
    global _original_stdout, _original_stderr, _tee_stdout, _tee_stderr, _log_file_path, _test_capture_active
    
    if _original_stdout is not None:
        # Wait a moment for any background tasks to complete their logging
        import time
        time.sleep(2.0)
        
        # Write footer to log file with test summary
        if _tee_stdout and _tee_stdout.log_file and not _tee_stdout.log_file.closed:
            try:
                _tee_stdout.log_file.write(f"\n{'='*80}\n")
                
                # Add test summary before completion timestamp
                try:
                    # Import BaseRealTest to use the collect_test_summary method
                    sys_path_backup = None
                    try:
                        # Temporarily add path for BaseRealTest import
                        import sys
                        import os
                        sys_path_backup = sys.path.copy()
                        base_path = os.path.join(os.path.dirname(__file__), '../../')
                        if base_path not in sys.path:
                            sys.path.insert(0, base_path)
                        
                        from tests.test_real.base_real_test import BaseRealTest
                        summary_lines = BaseRealTest.collect_test_summary(_log_file_path)
                        
                        # Write summary to log file
                        for summary_line in summary_lines:
                            _tee_stdout.log_file.write(f"{summary_line}\n")
                        _tee_stdout.log_file.write(f"\n")
                        
                    finally:
                        # Restore original sys.path
                        if sys_path_backup is not None:
                            sys.path[:] = sys_path_backup
                            
                except ImportError:
                    _tee_stdout.log_file.write("TEST SUMMARY\n")
                    _tee_stdout.log_file.write("============\n") 
                    _tee_stdout.log_file.write("Summary collection not available\n\n")
                except Exception as e:
                    _tee_stdout.log_file.write("TEST SUMMARY\n")
                    _tee_stdout.log_file.write("============\n")
                    _tee_stdout.log_file.write(f"Error collecting summary: {str(e)[:100]}\n\n")
                
                _tee_stdout.log_file.write(f"=== TEST DEBUG TRACE COMPLETED ===\n")
                _tee_stdout.log_file.write(f"Completed: {datetime.now()}\n")
            except:
                pass
        
        # ENHANCED: Clear global test environment state
        try:
            sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../..'))
            from etc.test_environment_detector import clear_test_environment
            
            # Don't clear immediately - let background tasks have some time to complete
            print("[TEST_ENV] Allowing background tasks 5 seconds to complete logging...")
            time.sleep(5.0)
            
            clear_test_environment()
            print("[TEST_ENV] Global test environment cleared")
        except Exception as e:
            print(f"[TEST_ENV] Warning: Could not clear global test environment: {e}")
        
        # Close tee outputs
        if _tee_stdout:
            _tee_stdout.close()
        if _tee_stderr:
            _tee_stderr.close()
        
        # Restore original streams
        sys.stdout = _original_stdout
        sys.stderr = _original_stderr
        
        # Print summary to original stdout
        if _log_file_path:
            _original_stdout.write(f"Test debug trace capture deactivated - log saved to {_log_file_path}\n")
        
        # Reset globals
        _original_stdout = None
        _original_stderr = None
        _tee_stdout = None
        _tee_stderr = None
        _log_file_path = None
        _test_capture_active = False
        
        # Clean environment flags
        os.environ.pop('PYTEST_RUNNING', None)
        os.environ.pop('TEST_CAPTURE_ACTIVE', None)
        os.environ.pop('TEST_NAME', None)


def get_current_test_log_path() -> Optional[Path]:
    """Get the current test log file path if capture is active"""
    return _log_file_path


def is_test_capture_active() -> bool:
    """Check if test debug capture is currently active"""
    global _test_capture_active
    return _test_capture_active


def with_test_logging(func):
    """Decorator to automatically enable test logging for async test methods"""
    @functools.wraps(func)
    async def wrapper(self, *args, **kwargs):
        # Start logging with the test function name
        setup_test_debug_capture(func.__name__)
        try:
            # Run the actual test
            result = await func(self, *args, **kwargs)
            return result
        except Exception as e:
            # Log any exceptions
            import traceback
            print(f"\n[ERROR] Test failed with exception: {e}")
            traceback.print_exc()
            raise
        finally:
            # Always stop logging
            cleanup_test_debug_capture()
    return wrapper