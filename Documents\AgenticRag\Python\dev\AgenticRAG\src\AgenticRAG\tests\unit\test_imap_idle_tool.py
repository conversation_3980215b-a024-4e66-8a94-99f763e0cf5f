from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from tasks.inputs.imap_idle_activate import SupervisorTask_IMAPIdleActivate, create_task_imap_idle_activate, start_30_minute_imap_session
from managers.manager_supervisors import SupervisorTaskState
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import Zaira<PERSON>ser
from endpoints.mybot_generic import MyBot_Generic

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
from pydantic import ValidationError
import json


class TestSupervisorTaskIMAPIdleActivate:
    """Test SupervisorTask_IMAPIdleActivate functionality"""
    
    def test_valid_instantiation(self):
        """Test successful creation"""
        task = SupervisorTask_IMAPIdleActivate(name="test_task")
        assert task is not None
        assert hasattr(task, 'llm_call')

    def test_class_attributes(self):
        """Test class has expected attributes"""
        task = SupervisorTask_IMAPIdleActivate(name="test_task")
        assert hasattr(task, '__class__')
        assert task.__class__.__name__ == 'SupervisorTask_IMAPIdleActivate'

    @pytest.mark.asyncio
    async def test_create_task_function(self):
        """Test create_task_imap_idle_activate function"""
        with patch('tasks.inputs.imap_idle_activate.SupervisorManager') as mock_manager:
            mock_manager.register_task.return_value = Mock()
            result = await create_task_imap_idle_activate()
            mock_manager.register_task.assert_called_once()

    @pytest.mark.asyncio 
    async def test_start_30_minute_session(self):
        """Test start_30_minute_imap_session function"""
        test_guid = "test-user-guid"
        
        # Patch both the task class and the SupervisorTaskState import
        with patch('tasks.inputs.imap_idle_activate.SupervisorTask_IMAPIdleActivate') as mock_task_class:
            # Mock the task instance
            mock_task = Mock()
            mock_task.llm_call = AsyncMock(return_value="Session started")
            mock_task_class.return_value = mock_task
            
            # Patch the SupervisorTaskState import inside the function
            with patch('managers.manager_supervisors.SupervisorTaskState') as mock_state_class:
                mock_state = Mock()
                mock_state_class.return_value = mock_state
                
                result = await start_30_minute_imap_session(test_guid)
                
                # Verify task was created
                mock_task_class.assert_called_once()
                
                # Verify state was created and user_guid was set
                mock_state_class.assert_called_once()
                assert mock_state.user_guid == test_guid
                
                # Verify llm_call was called with the state
                mock_task.llm_call.assert_called_once_with(mock_state)
                
                assert result == "Session started"


class TestIMAPIdleIntegration:
    """Test IMAP Idle integration aspects"""
    
    def test_can_import_module(self):
        """Test that module can be imported"""
        from tasks.inputs.imap_idle_activate import SupervisorTask_IMAPIdleActivate
        assert SupervisorTask_IMAPIdleActivate is not None
        
    def test_integration_with_supervisor_state(self):
        """Test integration with supervisor task state"""
        state = SupervisorTaskState(messages=[], user_guid="test-guid")
        assert state.user_guid == "test-guid"
        
    def test_integration_with_user_manager(self):
        """Test integration with user manager"""
        manager = ZairaUserManager.get_instance()
        assert manager is not None
        
    def test_integration_with_zaira_user(self):
        """Test integration with ZairaUser"""
        # Test that we can import and access the ZairaUser class
        # Avoid actual instantiation due to complex dependencies
        from userprofiles.ZairaUser import PERMISSION_LEVELS
        assert ZairaUser is not None
        assert PERMISSION_LEVELS.USER is not None
        assert hasattr(ZairaUser, '__init__')
        
    def test_integration_with_mybot_generic(self):
        """Test integration with MyBot_Generic"""
        # MyBot_Generic requires parent_instance and name parameters
        # We'll test that the class exists and can be imported
        assert MyBot_Generic is not None
        assert hasattr(MyBot_Generic, '__init__')

    @pytest.mark.asyncio
    async def test_llm_call_with_mock_user(self):
        """Test llm_call with mocked user"""
        task = SupervisorTask_IMAPIdleActivate(name="test_task")
        state = SupervisorTaskState(messages=[], user_guid="test-guid")
        
        with patch('tasks.inputs.imap_idle_activate.ZairaUserManager') as mock_manager:
            mock_manager.find_user = AsyncMock(return_value=None)
            
            result = await task.llm_call(state)
            
            assert "User not found" in result
            mock_manager.find_user.assert_called_once_with("test-guid")