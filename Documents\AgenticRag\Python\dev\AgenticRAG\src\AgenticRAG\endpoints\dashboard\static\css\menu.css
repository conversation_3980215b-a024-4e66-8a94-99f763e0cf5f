/* 🗑️ DEAD CODE: This CSS file is not loaded by the UI file system - menu styles are inline in dashboard_header.txt */
/* Menu-specific CSS variables */
:root {
    --brand-primary: #0A84FF;
    --brand-secondary: #14272C;
    --menu-background: rgba(20, 39, 44, 0.95);
    --menu-hover: rgba(59, 130, 246, 0.1);
    --menu-active: rgba(59, 130, 246, 0.2);
    --menu-border: rgba(59, 130, 246, 0.3);
    --dashboard-glass: rgba(255, 255, 255, 0.1);
    --dashboard-glass-border: rgba(255, 255, 255, 0.2);
}

/* Particles for menu */
.menu-particles {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    pointer-events: none;
    z-index: 1;
}

.menu-particle {
    position: absolute;
    background: rgba(96, 165, 250, 0.6);
    border-radius: 50%;
    animation: float 6s ease-in-out infinite;
}

@keyframes float {
    0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.4; }
    50% { transform: translateY(-20px) rotate(180deg); opacity: 0.8; }
}

/* Right side menu styling with enhanced details */
.enhanced-right-menu {
    position: fixed;
    top: 0;
    right: -320px;
    height: 100vh;
    width: 80px;
    background: var(--menu-background);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border-left: 1px solid var(--menu-border);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 9998;
    box-shadow: -10px 0 30px rgba(0, 0, 0, 0.3);
    overflow: hidden;
}

.enhanced-right-menu.expanded {
    right: 0;
    width: 320px;
}

.menu-content {
    position: relative;
    height: 100%;
    overflow-y: auto;
    z-index: 2;
}

.menu-header {
    padding: 2rem 1.5rem 1rem 1.5rem;
    text-align: center;
    border-bottom: 1px solid var(--dashboard-glass-border);
    margin-bottom: 1rem;
}

.menu-title {
    font-size: 1.5rem;
    font-weight: 700;
    color: #ffffff;
    background: linear-gradient(135deg, #60a5fa, #a78bfa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-shadow: 0 0 30px rgba(96, 165, 250, 0.5);
    margin-bottom: 0.5rem;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.1s;
}

.menu-subtitle {
    font-size: 0.85rem;
    color: #94a3b8;
    opacity: 0;
    transform: translateY(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) 0.2s;
    letter-spacing: 0.05em;
    text-transform: uppercase;
    font-weight: 500;
}

.enhanced-right-menu.expanded .menu-title {
    opacity: 1;
    transform: translateY(0);
}

.enhanced-right-menu.expanded .menu-subtitle {
    opacity: 0.8;
    transform: translateY(0);
}

.menu-items {
    padding: 0 0.5rem;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.menu-item {
    display: flex;
    align-items: center;
    padding: 1rem;
    color: #94a3b8;
    text-decoration: none;
    border-radius: 12px;
    background: transparent;
    border: 1px solid transparent;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
    cursor: pointer;
    min-height: 48px;
}

.menu-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(96, 165, 250, 0.1), transparent);
    transition: left 0.6s ease-out;
}

.menu-item:hover::before {
    left: 100%;
}

.menu-item:hover {
    background: var(--menu-hover);
    border-color: var(--menu-border);
    color: #ffffff;
    transform: translateX(-3px) scale(1.02);
    box-shadow: 0 8px 25px rgba(59, 130, 246, 0.15), 0 0 0 1px rgba(59, 130, 246, 0.1);
}

.menu-item.active {
    background: var(--menu-active);
    color: var(--brand-primary);
    border-color: var(--menu-border);
    box-shadow: 0 4px 20px rgba(59, 130, 246, 0.2);
}

.menu-item-icon {
    width: 24px;
    height: 24px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1rem;
    opacity: 0.9;
    flex-shrink: 0;
    margin-right: 1rem;
    transition: all 0.3s ease;
}

.menu-item:hover .menu-item-icon {
    opacity: 1;
    transform: scale(1.1);
}

.menu-item-text {
    font-weight: 500;
    font-size: 0.95rem;
    letter-spacing: 0.025em;
    white-space: nowrap;
    opacity: 0;
    transform: translateX(-10px);
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.enhanced-right-menu.expanded .menu-item-text {
    opacity: 1;
    transform: translateX(0);
}

/* Staggered animation for menu items */
.enhanced-right-menu.expanded .menu-item:nth-child(1) .menu-item-text { transition-delay: 0.1s; }
.enhanced-right-menu.expanded .menu-item:nth-child(2) .menu-item-text { transition-delay: 0.15s; }
.enhanced-right-menu.expanded .menu-item:nth-child(3) .menu-item-text { transition-delay: 0.2s; }
.enhanced-right-menu.expanded .menu-item:nth-child(4) .menu-item-text { transition-delay: 0.25s; }
.enhanced-right-menu.expanded .menu-item:nth-child(5) .menu-item-text { transition-delay: 0.3s; }
.enhanced-right-menu.expanded .menu-item:nth-child(6) .menu-item-text { transition-delay: 0.35s; }
.enhanced-right-menu.expanded .menu-item:nth-child(7) .menu-item-text { transition-delay: 0.4s; }
.enhanced-right-menu.expanded .menu-item:nth-child(8) .menu-item-text { transition-delay: 0.45s; }

/* Enhanced toggle button */
.enhanced-menu-toggle {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 60px;
    height: 60px;
    background: var(--menu-background);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid var(--menu-border);
    border-radius: 16px;
    color: white;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    z-index: 10000;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    display: flex;
    align-items: center;
    justify-content: center;
}

.enhanced-menu-toggle:hover {
    background: rgba(59, 130, 246, 0.8);
    border-color: rgba(59, 130, 246, 0.6);
    transform: translateY(-2px);
    box-shadow: 0 12px 35px rgba(59, 130, 246, 0.25);
}

.enhanced-menu-toggle.active {
    background: rgba(239, 68, 68, 0.8);
    border-color: rgba(239, 68, 68, 0.6);
}

/* Role-based menu item visibility */
.menu-item.system-user-only {
    display: none; /* Hidden by default */
}

/* Show system-user-only items when user has permissions */
.user-is-system .menu-item.system-user-only {
    display: block;
}