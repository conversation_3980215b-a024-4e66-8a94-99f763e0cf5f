#!/usr/bin/env python3
"""
Quick console test for Excel Analyzer task.
Fast testing without full system setup.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def quick_test():
    """Quick test of Excel Analyzer task"""
    print("⚡ Quick Excel Analyzer Test")
    print("=" * 30)
    
    try:
        # Minimal setup
        print("Minimal setup...")
        from imports import *
        ZairaSettings.IsDebugMode = True
        Globals.set_debug(ZairaSettings.IsDebugMode)
        
        # Just test task creation and basic functionality
        print("Testing task creation...")
        
        # Import your task
        from tasks.processing.task_excel_analyzer import create_task_sql_excel_analyzer
        
        # Create task
        excel_task = await create_task_sql_excel_analyzer()
        print(f"✅ Task created: {excel_task.name}")
        
        # Test compilation
        print("Testing compilation...")
        if excel_task.compiled_langgraph is None:
            excel_task.compile_default()
        print("✅ Task compiled successfully")
        
        # Test basic properties
        print(f"Task name: {excel_task.name}")
        print(f"Task type: {type(excel_task).__name__}")
        print(f"Has tools: {len(excel_task.get_tools())} tools")
        
        # List tools if any
        tools = excel_task.get_tools()
        if tools:
            print("Tools:")
            for tool in tools:
                print(f"  - {tool.name}: {tool.description}")
        
        print("\n✅ Quick test completed successfully!")
        
    except Exception as e:
        print(f"❌ Quick test failed: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

async def test_with_mock_state():
    """Test with a mock state (no full system setup)"""
    print("🎭 Mock State Test")
    print("=" * 20)
    
    try:
        # Minimal setup
        from imports import *
        ZairaSettings.IsDebugMode = True
        Globals.set_debug(ZairaSettings.IsDebugMode)
        
        # Create task
        from tasks.processing.task_excel_analyzer import create_task_sql_excel_analyzer
        excel_task = await create_task_sql_excel_analyzer()
        
        # Create mock state
        from managers.manager_supervisors import SupervisorTaskState
        from langchain_core.messages import HumanMessage
        
        mock_state = SupervisorTaskState(
            original_input="Analyze Excel data for sales trends",
            user_guid="test-user-guid",
            messages=[HumanMessage(content="Analyze Excel data for sales trends")],
            call_trace=["mock_test: start"]
        )
        
        print("Created mock state")
        print(f"Input: {mock_state.original_input}")
        
        # Try to call the task (this might fail due to missing dependencies)
        print("Attempting to call task with mock state...")
        try:
            result = await asyncio.wait_for(excel_task.llm_call(mock_state), timeout=10.0)
            print(f"✅ Task result: {result}")
        except Exception as e:
            print(f"⚠️  Task call failed (expected): {str(e)}")
            print("This is normal if the task requires database connections or other services")
        
    except Exception as e:
        print(f"❌ Mock state test failed: {str(e)}")

def interactive_prompt_test():
    """Interactive prompt testing"""
    print("💬 Interactive Prompt Test")
    print("=" * 30)
    print("Enter prompts to see how they would be processed")
    print("(This tests prompt formatting, not actual execution)")
    print("Type 'quit' to exit\n")
    
    while True:
        try:
            prompt = input("📝 Enter prompt: ").strip()
            
            if prompt.lower() in ['quit', 'exit', 'q']:
                break
            
            if not prompt:
                continue
            
            # Analyze the prompt
            print(f"Analyzing prompt: '{prompt}'")
            
            # Check for Excel-related keywords
            excel_keywords = ['excel', 'spreadsheet', 'xlsx', 'csv', 'data', 'analyze', 'report', 'query', 'sql']
            found_keywords = [kw for kw in excel_keywords if kw.lower() in prompt.lower()]
            
            if found_keywords:
                print(f"✅ Excel-related keywords found: {', '.join(found_keywords)}")
                print("This prompt would likely route to Excel Analyzer task")
            else:
                print("⚠️  No Excel-related keywords found")
                print("This prompt might not route to Excel Analyzer task")
            
            # Suggest improvements
            if 'excel' not in prompt.lower() and 'data' not in prompt.lower():
                print("💡 Suggestion: Add 'Excel' or 'data' to make routing more likely")
            
            print("-" * 40)
            
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"Error: {e}")
    
    print("\n👋 Interactive test ended")

if __name__ == "__main__":
    print("Quick Excel Analyzer Testing")
    print("Choose test type:")
    print("1. Quick task creation test")
    print("2. Mock state test") 
    print("3. Interactive prompt analysis")
    print("4. All tests")
    
    try:
        choice = input("Enter choice (1-4): ").strip()
        
        if choice == "1":
            asyncio.run(quick_test())
        elif choice == "2":
            asyncio.run(test_with_mock_state())
        elif choice == "3":
            interactive_prompt_test()
        elif choice == "4":
            asyncio.run(quick_test())
            print("\n" + "="*40 + "\n")
            asyncio.run(test_with_mock_state())
            print("\n" + "="*40 + "\n")
            interactive_prompt_test()
        else:
            print("Invalid choice. Running quick test...")
            asyncio.run(quick_test())
            
    except KeyboardInterrupt:
        print("\n👋 Testing interrupted")
    except Exception as e:
        print(f"❌ Testing failed: {str(e)}")
        sys.exit(1)
