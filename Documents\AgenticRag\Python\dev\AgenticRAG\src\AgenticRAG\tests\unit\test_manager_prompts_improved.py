"""
Improved unit tests for Prompt Manager - targeting 90% coverage
Following CLAUDE.md guidance for comprehensive test coverage
"""
import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from imports import *

@pytest.mark.unit
class TestPromptManagerImproved:
    """Improved tests for prompt manager functionality"""
    
    def setup_method(self):
        """Reset singletons and setup test data"""
        # Reset singleton instances
        from managers.manager_prompts import PromptManager
        PromptManager._instance = None
        PromptManager._initialized = False
    
    def teardown_method(self):
        """Clean up after each test"""
        from managers.manager_prompts import PromptManager
        PromptManager._instance = None
        PromptManager._initialized = False

    # ===== CORE FUNCTIONALITY TESTS =====
    
    def test_singleton_pattern(self):
        """Test that PromptManager follows singleton pattern"""
        from managers.manager_prompts import PromptManager
        
        instance1 = PromptManager()
        instance2 = PromptManager()
        instance3 = PromptManager.get_instance()
        
        assert instance1 is instance2
        assert instance2 is instance3
        assert isinstance(instance1, PromptManager)
    
    def test_initial_state(self):
        """Test initial state of PromptManager"""
        from managers.manager_prompts import PromptManager
        
        assert PromptManager._instance is None
        assert PromptManager._initialized is False
        
        # Test that get_instance creates new instance
        instance = PromptManager.get_instance()
        assert PromptManager._instance is not None
        assert isinstance(instance, PromptManager)
    
    def test_token_values_definition(self):
        """Test that token values are properly defined"""
        from managers.manager_prompts import PromptManager
        
        assert hasattr(PromptManager, 'token_string_values')
        assert hasattr(PromptManager, 'token_int_values')
        assert isinstance(PromptManager.token_string_values, list)
        assert isinstance(PromptManager.token_int_values, list)
        assert len(PromptManager.token_string_values) > 0
        assert len(PromptManager.token_int_values) > 0
        
        # Verify specific expected values
        assert "access_token" in PromptManager.token_string_values
        assert "refresh_token" in PromptManager.token_string_values
        assert "expires_in" in PromptManager.token_int_values
        assert "refresh_token_expires_in" in PromptManager.token_int_values
    
    def test_prompts_dictionary_definition(self):
        """Test that prompts dictionary is properly defined"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        assert hasattr(instance, 'prompts')
        assert isinstance(instance.prompts, dict)
        assert len(instance.prompts) > 0
        
        # Verify some key prompts exist
        expected_prompts = [
            "AskZaira_Prompt",
            "Quick_RAG_Search", 
            "Quick_LLM_Search",
            "Global_Supervisor_Prompt",
            "Output_Supervisor_Prompt"
        ]
        
        for prompt_key in expected_prompts:
            assert prompt_key in instance.prompts
            assert isinstance(instance.prompts[prompt_key], str)
            assert len(instance.prompts[prompt_key]) > 0
    
    @pytest.mark.asyncio
    async def test_setup_first_time(self):
        """Test manager setup first time"""
        from managers.manager_prompts import PromptManager
        
        # Set initial state
        PromptManager._initialized = False
        
        with patch.object(PromptManager, 'setDefaultPrompts', new_callable=AsyncMock) as mock_set_defaults:
            await PromptManager.setup()
            
            mock_set_defaults.assert_called_once()
            assert PromptManager._initialized is True
    
    @pytest.mark.asyncio
    async def test_setup_already_initialized(self):
        """Test manager setup when already initialized"""
        from managers.manager_prompts import PromptManager
        
        # Set initial state
        PromptManager._initialized = True
        
        with patch.object(PromptManager, 'setDefaultPrompts', new_callable=AsyncMock) as mock_set_defaults:
            await PromptManager.setup()
            
            mock_set_defaults.assert_not_called()
    
    # ===== PROMPT SETTING TESTS =====
    
    @pytest.mark.asyncio
    async def test_setDefaultPrompts_success(self):
        """Test successful default prompts setting"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        with patch.object(instance, 'setDefaultPrompts_MemoryOnly', new_callable=AsyncMock) as mock_memory_only:
            await instance.setDefaultPrompts()
            
            mock_memory_only.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_setDefaultPrompts_MemoryOnly_success(self):
        """Test successful memory-only prompts setting"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Mock the database interactions
        with patch('managers.manager_meltano.MeltanoManager.get_instance') as mock_meltano:
            mock_meltano_instance = MagicMock()
            mock_meltano.return_value = mock_meltano_instance
            
            mock_meltano_instance.update_prompt.return_value = None
            
            await instance.setDefaultPrompts_MemoryOnly()
            
            # Verify that update_prompt was called for prompts
            assert mock_meltano_instance.update_prompt.call_count > 0
    
    @pytest.mark.asyncio
    async def test_setDefaultPrompts_MemoryOnly_error_handling(self):
        """Test memory-only prompts setting with error handling"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Mock the database interactions to raise exception
        with patch('managers.manager_meltano.MeltanoManager.get_instance') as mock_meltano:
            mock_meltano_instance = MagicMock()
            mock_meltano.return_value = mock_meltano_instance
            
            mock_meltano_instance.update_prompt.side_effect = Exception("Database error")
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                await instance.setDefaultPrompts_MemoryOnly()
                
                # Should handle errors gracefully
                mock_exception.assert_called()
    
    @pytest.mark.asyncio
    async def test_loadDefaultPrompts_success(self):
        """Test successful default prompts loading"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Mock the database interactions
        with patch('managers.manager_meltano.MeltanoManager.get_instance') as mock_meltano:
            mock_meltano_instance = MagicMock()
            mock_meltano.return_value = mock_meltano_instance
            
            # Mock return value for get_prompt
            mock_meltano_instance.get_prompt.return_value = "Test prompt value"
            
            await instance.loadDefaultPrompts()
            
            # Verify that get_prompt was called for prompts
            assert mock_meltano_instance.get_prompt.call_count > 0
    
    @pytest.mark.asyncio
    async def test_loadDefaultPrompts_partial_loading(self):
        """Test default prompts loading with some prompts missing"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Mock the database interactions
        with patch('managers.manager_meltano.MeltanoManager.get_instance') as mock_meltano:
            mock_meltano_instance = MagicMock()
            mock_meltano.return_value = mock_meltano_instance
            
            # Mock return value for get_prompt - some found, some not
            def mock_get_prompt(prompt_key):
                if prompt_key == "AskZaira_Prompt":
                    return "Loaded prompt value"
                else:
                    return None
            
            mock_meltano_instance.get_prompt.side_effect = mock_get_prompt
            
            await instance.loadDefaultPrompts()
            
            # Should load available prompts and keep defaults for missing ones
            assert mock_meltano_instance.get_prompt.call_count > 0
    
    @pytest.mark.asyncio
    async def test_loadDefaultPrompts_error_handling(self):
        """Test default prompts loading with error handling"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Mock the database interactions to raise exception
        with patch('managers.manager_meltano.MeltanoManager.get_instance') as mock_meltano:
            mock_meltano_instance = MagicMock()
            mock_meltano.return_value = mock_meltano_instance
            
            mock_meltano_instance.get_prompt.side_effect = Exception("Database error")
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                await instance.loadDefaultPrompts()
                
                # Should handle errors gracefully
                mock_exception.assert_called()
    
    # ===== PROMPT RETRIEVAL TESTS =====
    
    def test_get_prompt_identifier_and_key_success(self):
        """Test successful prompt identifier and key retrieval"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Test with existing prompt
        result = instance.get_prompt_identifier_and_key("AskZaira_Prompt")
        
        assert result is not None
        assert isinstance(result, tuple)
        assert len(result) == 2
        identifier, key = result
        assert identifier is not None
        assert key is not None
    
    def test_get_prompt_identifier_and_key_not_found(self):
        """Test prompt identifier and key retrieval for non-existent prompt"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Test with non-existent prompt
        result = instance.get_prompt_identifier_and_key("NonExistentPrompt")
        
        assert result is None
    
    def test_get_prompt_identifier_and_key_empty_string(self):
        """Test prompt identifier and key retrieval for empty string"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Test with empty string
        result = instance.get_prompt_identifier_and_key("")
        
        assert result is None
    
    def test_get_prompt_identifier_and_key_none(self):
        """Test prompt identifier and key retrieval for None"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Test with None
        result = instance.get_prompt_identifier_and_key(None)
        
        assert result is None
    
    def test_get_prompt_success(self):
        """Test successful prompt retrieval"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Test with existing prompt
        result = instance.get_prompt("AskZaira_Prompt")
        
        assert result is not None
        assert isinstance(result, str)
        assert len(result) > 0
        # Should match the value in the prompts dictionary
        assert result == instance.prompts["AskZaira_Prompt"]
    
    def test_get_prompt_not_found(self):
        """Test prompt retrieval for non-existent prompt"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Test with non-existent prompt
        result = instance.get_prompt("NonExistentPrompt")
        
        assert result is None
    
    def test_get_prompt_empty_string(self):
        """Test prompt retrieval for empty string"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Test with empty string
        result = instance.get_prompt("")
        
        assert result is None
    
    def test_get_prompt_none(self):
        """Test prompt retrieval for None"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Test with None
        result = instance.get_prompt(None)
        
        assert result is None
    
    def test_get_prompt_case_sensitivity(self):
        """Test that prompt retrieval is case-sensitive"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Test with different case
        result = instance.get_prompt("askzaira_prompt")  # lowercase
        
        assert result is None  # Should not find it due to case sensitivity
    
    # ===== PROMPT CONTENT VALIDATION TESTS =====
    
    def test_prompt_content_validation(self):
        """Test that all prompts have valid content"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        for prompt_key, prompt_value in instance.prompts.items():
            # Each prompt should be a non-empty string
            assert isinstance(prompt_value, str), f"Prompt {prompt_key} is not a string"
            assert len(prompt_value.strip()) > 0, f"Prompt {prompt_key} is empty"
            
            # Prompt should not contain obvious placeholder text
            assert "TODO" not in prompt_value.upper(), f"Prompt {prompt_key} contains TODO"
            assert "FIXME" not in prompt_value.upper(), f"Prompt {prompt_key} contains FIXME"
    
    def test_critical_prompts_exist(self):
        """Test that critical prompts exist and have reasonable content"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        critical_prompts = [
            "AskZaira_Prompt",
            "Global_Supervisor_Prompt",
            "Output_Supervisor_Prompt",
            "Quick_RAG_Search",
            "Quick_LLM_Search"
        ]
        
        for prompt_key in critical_prompts:
            assert prompt_key in instance.prompts, f"Critical prompt {prompt_key} is missing"
            prompt_value = instance.prompts[prompt_key]
            assert len(prompt_value.strip()) > 20, f"Critical prompt {prompt_key} is too short"
    
    def test_prompt_formatting_consistency(self):
        """Test that prompts have consistent formatting"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        for prompt_key, prompt_value in instance.prompts.items():
            # Check for excessive whitespace
            assert prompt_value == prompt_value.strip(), f"Prompt {prompt_key} has leading/trailing whitespace"
            
            # Check for reasonable length (not too short or extremely long)
            assert len(prompt_value) > 5, f"Prompt {prompt_key} is too short"
            assert len(prompt_value) < 10000, f"Prompt {prompt_key} is unreasonably long"
    
    # ===== INTEGRATION TESTS =====
    
    @pytest.mark.asyncio
    async def test_full_initialization_workflow(self):
        """Test complete initialization workflow"""
        from managers.manager_prompts import PromptManager
        
        # Start with clean state
        PromptManager._instance = None
        PromptManager._initialized = False
        
        # Mock Meltano manager
        with patch('managers.manager_meltano.MeltanoManager.get_instance') as mock_meltano:
            mock_meltano_instance = MagicMock()
            mock_meltano.return_value = mock_meltano_instance
            mock_meltano_instance.update_prompt.return_value = None
            
            # Perform setup
            await PromptManager.setup()
            
            # Get instance and verify
            instance = PromptManager.get_instance()
            assert instance is not None
            assert PromptManager._initialized is True
            
            # Verify prompts are accessible
            prompt = instance.get_prompt("AskZaira_Prompt")
            assert prompt is not None
            assert isinstance(prompt, str)
    
    @pytest.mark.asyncio
    async def test_prompt_update_and_reload_workflow(self):
        """Test updating prompts and reloading"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Mock Meltano manager
        with patch('managers.manager_meltano.MeltanoManager.get_instance') as mock_meltano:
            mock_meltano_instance = MagicMock()
            mock_meltano.return_value = mock_meltano_instance
            
            # Mock updating prompts
            mock_meltano_instance.update_prompt.return_value = None
            await instance.setDefaultPrompts_MemoryOnly()
            
            # Mock loading prompts
            mock_meltano_instance.get_prompt.return_value = "Updated prompt value"
            await instance.loadDefaultPrompts()
            
            # Verify interactions
            assert mock_meltano_instance.update_prompt.call_count > 0
            assert mock_meltano_instance.get_prompt.call_count > 0
    
    # ===== ERROR HANDLING TESTS =====
    
    @pytest.mark.asyncio
    async def test_meltano_manager_unavailable(self):
        """Test behavior when Meltano manager is unavailable"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Mock Meltano manager to raise exception
        with patch('managers.manager_meltano.MeltanoManager.get_instance') as mock_meltano:
            mock_meltano.side_effect = Exception("Meltano manager unavailable")
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                await instance.setDefaultPrompts_MemoryOnly()
                
                # Should handle error gracefully
                mock_exception.assert_called()
    
    @pytest.mark.asyncio
    async def test_database_connection_error(self):
        """Test behavior with database connection errors"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Mock Meltano manager with connection error
        with patch('managers.manager_meltano.MeltanoManager.get_instance') as mock_meltano:
            mock_meltano_instance = MagicMock()
            mock_meltano.return_value = mock_meltano_instance
            
            mock_meltano_instance.update_prompt.side_effect = ConnectionError("Database connection failed")
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                await instance.setDefaultPrompts_MemoryOnly()
                
                # Should handle error gracefully
                mock_exception.assert_called()
    
    # ===== PERFORMANCE TESTS =====
    
    def test_prompt_retrieval_performance(self):
        """Test that prompt retrieval is fast"""
        from managers.manager_prompts import PromptManager
        import time
        
        instance = PromptManager.get_instance()
        
        # Test retrieval of multiple prompts
        start_time = time.time()
        for _ in range(100):
            result = instance.get_prompt("AskZaira_Prompt")
            assert result is not None
        end_time = time.time()
        
        # Should be very fast (well under 1 second for 100 retrievals)
        assert (end_time - start_time) < 1.0
    
    def test_prompt_identifier_retrieval_performance(self):
        """Test that prompt identifier retrieval is fast"""
        from managers.manager_prompts import PromptManager
        import time
        
        instance = PromptManager.get_instance()
        
        # Test retrieval of multiple prompt identifiers
        start_time = time.time()
        for _ in range(100):
            result = instance.get_prompt_identifier_and_key("AskZaira_Prompt")
            assert result is not None
        end_time = time.time()
        
        # Should be very fast (well under 1 second for 100 retrievals)
        assert (end_time - start_time) < 1.0
    
    # ===== EDGE CASE TESTS =====
    
    def test_prompt_with_special_characters(self):
        """Test handling of prompts with special characters"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        # Test prompts that might have special characters
        for prompt_key, prompt_value in instance.prompts.items():
            # Should handle unicode characters gracefully
            try:
                encoded = prompt_value.encode('utf-8')
                decoded = encoded.decode('utf-8')
                assert decoded == prompt_value
            except UnicodeError:
                pytest.fail(f"Prompt {prompt_key} contains invalid unicode")
    
    def test_prompt_keys_format(self):
        """Test that prompt keys follow expected format"""
        from managers.manager_prompts import PromptManager
        
        instance = PromptManager.get_instance()
        
        for prompt_key in instance.prompts.keys():
            # Prompt keys should be strings
            assert isinstance(prompt_key, str)
            # Should not be empty
            assert len(prompt_key) > 0
            # Should not contain spaces (use underscores instead)
            assert ' ' not in prompt_key
            # Should not start with underscore (reserved for internal use)
            assert not prompt_key.startswith('_')
    
    def test_concurrent_access(self):
        """Test that concurrent access works correctly"""
        from managers.manager_prompts import PromptManager
        import threading
        
        instance = PromptManager.get_instance()
        results = []
        
        def access_prompt():
            result = instance.get_prompt("AskZaira_Prompt")
            results.append(result)
        
        # Create multiple threads accessing prompts
        threads = []
        for _ in range(10):
            thread = threading.Thread(target=access_prompt)
            threads.append(thread)
            thread.start()
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        # All should have gotten the same result
        assert len(results) == 10
        assert all(result == results[0] for result in results)
        assert all(result is not None for result in results)