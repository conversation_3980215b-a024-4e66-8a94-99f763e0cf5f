from imports import *
from typing import Dict, List, Optional, Any
import asyncio
from datetime import datetime, timezone

from .integration_adapter import ScheduledRequestIntegrationAdapter
from .monitoring.metrics_collector import MetricsCollector
from .utils.helpers import validate_guid, format_duration

if TYPE_CHECKING:
    from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest

class ScheduledRequestAdminInterface:
    """Administrative interface for managing the scheduled requests system"""
    
    def __init__(self):
        self._adapter: Optional[ScheduledRequestIntegrationAdapter] = None
    
    async def initialize(self):
        """Initialize the admin interface"""
        try:
            self._adapter = ScheduledRequestIntegrationAdapter.get_instance()
            if not self._adapter.is_initialized():
                await ScheduledRequestIntegrationAdapter.setup()
            LogFire.log("INIT", "Admin interface ready")
        except Exception as e:
            LogFire.log("ERROR", f"Failed to initialize admin interface: {str(e)}")
            raise
    
    async def get_system_overview(self) -> Dict[str, Any]:
        """Get comprehensive system overview"""
        try:
            if not self._adapter:
                await self.initialize()
            
            health_status = await self._adapter.get_system_health()
            
            # Get additional system info
            factory = self._adapter._factory
            quota_manager = self._adapter._quota_manager
            rate_limiter = self._adapter._rate_limiter
            metrics_collector = self._adapter._metrics_collector
            
            factory_metrics = await factory.get_factory_metrics()
            quota_overview = await quota_manager.get_system_quota_overview()
            rate_metrics = await rate_limiter.get_system_rate_metrics()
            current_metrics = metrics_collector.get_current_metrics()
            
            return {
                'system_health': health_status,
                'factory_metrics': factory_metrics,
                'quota_overview': quota_overview,
                'rate_metrics': rate_metrics,
                'performance_metrics': current_metrics,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get system overview: {str(e)}")
            return {'error': str(e)}
    
    async def get_user_details(self, user_guid: str) -> Dict[str, Any]:
        """Get detailed information for specific user"""
        try:
            if not validate_guid(user_guid):
                return {'error': 'Invalid user GUID format'}
            
            if not self._adapter:
                await self.initialize()
            
            # Get user from user manager
            from managers.manager_users import ZairaUserManager
            user_manager = ZairaUserManager.get_instance()
            user = await user_manager.find_user(user_guid)
            
            if not user:
                return {'error': 'User not found'}
            
            # Get user's quota status
            quota_status = await self._adapter.get_user_quota_status(user)
            
            # Get user's scheduled requests
            user_requests = await self._adapter.get_user_scheduled_requests(user, include_completed=True)
            
            # Get user metrics
            metrics_collector = self._adapter._metrics_collector
            user_metrics = metrics_collector.get_user_metrics_summary(user_guid)
            
            return {
                'user_info': {
                    'user_guid': user_guid,
                    'username': user.username,
                    'rank': user.rank.value if hasattr(user, 'rank') else 'USER',
                    'device_guid': str(user.DeviceGUID)
                },
                'quota_status': quota_status,
                'active_requests': len([r for r in user_requests if r.get('status') == 'active']),
                'total_requests': len(user_requests),
                'requests': user_requests[:10],  # Show first 10
                'metrics': user_metrics,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get user details for {user_guid}: {str(e)}")
            return {'error': str(e)}
    
    async def get_request_object(self, scheduled_guid: str, user_guid: str) -> Optional['ScheduledZairaRequest']:
        """Get the full ScheduledZairaRequest object for a specific scheduled request"""
        try:
            if not validate_guid(scheduled_guid) or not validate_guid(user_guid):
                return None
            
            if not self._adapter:
                await self.initialize()
            
            # Get user manager
            factory = self._adapter._factory
            user_manager = await factory.get_user_manager(user_guid)
            
            # Get full request object from user manager
            request_object = await user_manager.get_request_object(scheduled_guid)
            
            return request_object
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get request object for {scheduled_guid}: {str(e)}")
            return None
    
    # get_request_status method removed - use get_request_object to access attributes directly
    
    async def get_request_details(self, scheduled_guid: str) -> Optional[Dict[str, Any]]:
        """Get detailed information for a specific scheduled request"""
        try:
            if not validate_guid(scheduled_guid):
                return None
            
            if not self._adapter:
                await self.initialize()
            
            # Get request from persistence layer
            persistence = self._adapter._persistence
            request_data = await persistence.load_request(scheduled_guid)
            
            if request_data:
                # Enrich with current status by checking if request is active
                # Need to check user-specific manager for active status
                user_guid = request_data.get('user_guid')
                is_active = False
                
                if user_guid:
                    try:
                        factory = self._adapter._factory
                        user_manager = await factory.get_user_manager(user_guid)
                        # Check if this request is in the user's active requests
                        is_active = scheduled_guid in user_manager._active_requests
                    except Exception as e:
                        LogFire.log("DEBUG", f"Could not check active status for {scheduled_guid}: {str(e)}")
                        is_active = False
                
                if is_active:
                    request_data['status'] = 'active'  # For cancel button display
                    request_data['current_status'] = 'active'
                    request_data['is_running'] = True
                else:
                    request_data['status'] = request_data.get('status', 'inactive')
                    request_data['current_status'] = request_data.get('status', 'inactive')
                    request_data['is_running'] = False
                
                # Remove request_status if it exists in persisted data
                # Status should be accessed through request object attributes directly
                if 'request_status' in request_data:
                    del request_data['request_status']
                
                return request_data
            
            return None
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get request details for {scheduled_guid}: {str(e)}")
            return None
    
    async def manage_user_quotas(self, user_guid: str, action: str, **kwargs) -> Dict[str, Any]:
        """Manage user quotas (admin operation)"""
        try:
            if not validate_guid(user_guid):
                return {'error': 'Invalid user GUID format'}
            
            if not self._adapter:
                await self.initialize()
            
            # Get user
            from managers.manager_users import ZairaUserManager
            user_manager = ZairaUserManager.get_instance()
            user = await user_manager.find_user(user_guid)
            
            if not user:
                return {'error': 'User not found'}
            
            quota_manager = self._adapter._quota_manager
            user_quota_manager = await quota_manager.get_user_quota_manager(user_guid, user.rank)
            
            if action == 'reset_daily':
                await user_quota_manager.reset_daily_quota(force=True)
                LogFire.log("ADMIN", f"Reset daily quota for user {user_guid}")
                return {'success': True, 'message': 'Daily quota reset'}
                
            elif action == 'update_config' and 'config' in kwargs:
                from managers.scheduled_requests.utils.config import UserQuotaConfig
                new_config = UserQuotaConfig(**kwargs['config'])
                await user_quota_manager.update_quota_config(new_config)
                LogFire.log("ADMIN", f"Updated quota config for user {user_guid}")
                return {'success': True, 'message': 'Quota configuration updated'}
                
            elif action == 'get_forecast':
                hours = kwargs.get('hours', 24)
                forecast = await user_quota_manager.get_quota_forecast(hours)
                return {'success': True, 'forecast': forecast}
                
            else:
                return {'error': f'Unknown action: {action}'}
                
        except Exception as e:
            LogFire.log("ERROR", f"Failed to manage quotas for user {user_guid}: {str(e)}")
            return {'error': str(e)}
    
    async def manage_rate_limits(self, user_guid: Optional[str], action: str, **kwargs) -> Dict[str, Any]:
        """Manage rate limits"""
        try:
            if not self._adapter:
                await self.initialize()
            
            rate_limiter = self._adapter._rate_limiter
            
            if action == 'reset_user' and user_guid:
                if not validate_guid(user_guid):
                    return {'error': 'Invalid user GUID format'}
                
                success = await rate_limiter.reset_user_limits(user_guid)
                if success:
                    LogFire.log("ADMIN", f"Reset rate limits for user {user_guid}")
                    return {'success': True, 'message': f'Rate limits reset for user {user_guid}'}
                else:
                    return {'error': 'User rate limiter not found'}
                    
            elif action == 'emergency_limits':
                severity = kwargs.get('severity', 'medium')
                await rate_limiter.apply_emergency_limits(severity)
                LogFire.log("ADMIN", f"Applied emergency rate limits: {severity}")
                return {'success': True, 'message': f'Emergency limits applied: {severity}'}
                
            elif action == 'restore_normal':
                await rate_limiter.restore_normal_limits()
                LogFire.log("ADMIN", "Restored normal rate limits")
                return {'success': True, 'message': 'Normal rate limits restored'}
                
            elif action == 'get_metrics':
                metrics = await rate_limiter.get_system_rate_metrics()
                return {'success': True, 'metrics': metrics}
                
            else:
                return {'error': f'Unknown action: {action}'}
                
        except Exception as e:
            LogFire.log("ERROR", f"Failed to manage rate limits: {str(e)}")
            return {'error': str(e)}
    
    async def system_maintenance(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Perform system maintenance operations"""
        try:
            if not self._adapter:
                await self.initialize()
            
            if operation == 'force_cleanup':
                await self._adapter.force_cleanup()
                LogFire.log("ADMIN", "Force cleanup completed")
                return {'success': True, 'message': 'Force cleanup completed'}
                
            elif operation == 'health_check':
                health = await self._adapter.get_system_health()
                return {'success': True, 'health': health}
                
            elif operation == 'cleanup_old_data':
                days = kwargs.get('days', 30)
                persistence = self._adapter._persistence
                cleaned_count = await persistence.cleanup_old_records(days)
                LogFire.log("ADMIN", f"Cleaned up {cleaned_count} old records")
                return {'success': True, 'message': f'Cleaned up {cleaned_count} old records'}
                
            elif operation == 'export_metrics':
                metrics_collector = self._adapter._metrics_collector
                export_data = metrics_collector.export_metrics_for_external_monitoring()
                return {'success': True, 'metrics': export_data}
                
            else:
                return {'error': f'Unknown operation: {operation}'}
                
        except Exception as e:
            LogFire.log("ERROR", f"System maintenance failed: {str(e)}")
            return {'error': str(e)}
    
    async def system_health_operations(self, operation: str, **kwargs) -> Dict[str, Any]:
        """Handle system health monitoring operations"""
        try:
            if not self._adapter:
                await self.initialize()
            
            metrics_collector = self._adapter._metrics_collector
            
            if operation == 'health_check':
                health = await metrics_collector.get_system_health_status()
                return {'success': True, 'health': health}
                
            elif operation == 'metrics':
                metrics = metrics_collector.get_current_metrics()
                return {'success': True, 'metrics': metrics}
                
            elif operation == 'performance':
                performance = metrics_collector.get_system_performance_summary(
                    time_range_hours=kwargs.get('time_range_hours', 1)
                )
                return {'success': True, 'performance': performance}
                
            elif operation == 'violations':
                violations = metrics_collector.get_trending_violations(
                    time_range_hours=kwargs.get('time_range_hours', 24)
                )
                return {'success': True, 'violations': violations}
                
            elif operation == 'export':
                export_data = metrics_collector.export_metrics_for_external_monitoring()
                return {'success': True, 'export_data': export_data}
                
            else:
                return {'error': f'Unknown health operation: {operation}'}
                
        except Exception as e:
            LogFire.log("ERROR", f"Health operation failed: {str(e)}")
            return {'error': str(e)}
    
    async def get_security_report(self, time_range_hours: int = 24) -> Dict[str, Any]:
        """Generate security report"""
        try:
            if not self._adapter:
                await self.initialize()
            
            metrics_collector = self._adapter._metrics_collector
            security_validator = self._adapter._security_validator
            
            # Get violation trends
            violations = metrics_collector.get_trending_violations(time_range_hours)
            
            # Get security metrics
            security_metrics = security_validator.get_security_metrics()
            
            # Get current blocked IPs (if available)
            blocked_ips = security_metrics.get('active_ip_blocks', 0)
            
            return {
                'time_range_hours': time_range_hours,
                'security_summary': {
                    'total_violations': sum(violations['security_trends'].values()),
                    'quota_violations': sum(violations['quota_trends'].values()),
                    'rate_limit_violations': sum(violations['rate_limit_trends'].values()),
                    'blocked_ips': blocked_ips,
                    'top_violating_users': violations['top_violating_users'][:5]
                },
                'trends': violations,
                'security_metrics': security_metrics,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to generate security report: {str(e)}")
            return {'error': str(e)}
    
    async def get_performance_report(self, time_range_hours: int = 1) -> Dict[str, Any]:
        """Generate performance report"""
        try:
            if not self._adapter:
                await self.initialize()
            
            metrics_collector = self._adapter._metrics_collector
            
            # Get performance summary
            performance = metrics_collector.get_system_performance_summary(time_range_hours)
            
            # Get current metrics
            current = metrics_collector.get_current_metrics()
            
            return {
                'time_range_hours': time_range_hours,
                'performance_summary': performance,
                'current_metrics': current,
                'system_load': current['health_indicators']['system_load'],
                'recommendations': self._generate_performance_recommendations(performance, current),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to generate performance report: {str(e)}")
            return {'error': str(e)}
    
    def _generate_performance_recommendations(self, performance: Dict[str, Any], 
                                           current: Dict[str, Any]) -> List[str]:
        """Generate performance recommendations based on metrics"""
        recommendations = []
        
        # Check error rate
        error_rate = performance.get('error_rate', 0)
        if error_rate > 0.1:  # 10% error rate
            recommendations.append(f"High error rate detected: {error_rate:.1%}. Investigate failing requests.")
        
        # Check response time
        avg_response_time = performance.get('average_response_time', 0)
        if avg_response_time > 30:  # 30 seconds
            recommendations.append(f"Slow response time: {format_duration(avg_response_time)}. Consider optimization.")
        
        # Check system load
        system_load = current['health_indicators'].get('system_load', 0)
        if system_load > 80:
            recommendations.append(f"High system load: {system_load:.1f}%. Consider scaling.")
        
        # Check request rate
        requests_per_hour = performance.get('requests_per_hour', 0)
        if requests_per_hour > 500:
            recommendations.append("High request volume. Monitor for capacity planning.")
        
        if not recommendations:
            recommendations.append("System performance is within normal parameters.")
        
        return recommendations
    
    async def emergency_shutdown(self, reason: str = "Emergency shutdown requested") -> Dict[str, Any]:
        """Emergency system shutdown"""
        try:
            LogFire.log("CRITICAL", f"Emergency shutdown initiated: {reason}")
            
            if self._adapter:
                await self._adapter.shutdown()
            
            return {
                'success': True,
                'message': 'Emergency shutdown completed',
                'reason': reason,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            LogFire.log("ERROR", f"Emergency shutdown failed: {str(e)}")
            return {'error': str(e)}

# Global admin interface instance
_admin_interface = ScheduledRequestAdminInterface()

async def get_admin_interface() -> ScheduledRequestAdminInterface:
    """Get the global admin interface instance"""
    if not _admin_interface._adapter:
        await _admin_interface.initialize()
    return _admin_interface