=== DEBUG TRACE STARTED at 2025-08-16 17:58:45.758000 ===
[2025-08-16 17:58:46.730] Traceback (most recent call last):
[2025-08-16 17:58:46.731]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\quick_startup_test.py", line 103, in <module>
    [2025-08-16 17:58:46.731] log_with_timestamp("Starting quick startup test...")
[2025-08-16 17:58:46.731]   File "c:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\quick_startup_test.py", line 18, in log_with_timestamp
    [2025-08-16 17:58:46.731] timestamp = time.strftime("%H:%M:%S.%f")[:-3]
                [2025-08-16 17:58:46.744] ^[2025-08-16 17:58:46.744] ^[2025-08-16 17:58:46.744] ^[2025-08-16 17:58:46.744] ^[2025-08-16 17:58:46.744] ^[2025-08-16 17:58:46.744] ^[2025-08-16 17:58:46.744] ^[2025-08-16 17:58:46.744] ^[2025-08-16 17:58:46.744] ^[2025-08-16 17:58:46.744] ^[2025-08-16 17:58:46.744] ^[2025-08-16 17:58:46.744] ^[2025-08-16 17:58:46.744] ^[2025-08-16 17:58:46.744] ^[2025-08-16 17:58:46.745] ^[2025-08-16 17:58:46.745] ^[2025-08-16 17:58:46.745] ^[2025-08-16 17:58:46.745] ^[2025-08-16 17:58:46.745] ^[2025-08-16 17:58:46.745] ^[2025-08-16 17:58:46.745] ^[2025-08-16 17:58:46.745] ^[2025-08-16 17:58:46.745] ^[2025-08-16 17:58:46.745] ^[2025-08-16 17:58:46.745] ^[2025-08-16 17:58:46.745] ^[2025-08-16 17:58:46.745] ^[2025-08-16 17:58:46.746] ^
[2025-08-16 17:58:46.746] ValueError[2025-08-16 17:58:46.746] : [2025-08-16 17:58:46.746] Invalid format string
