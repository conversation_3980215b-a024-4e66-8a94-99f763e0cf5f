"""
Unit tests for the IMAP task
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, Mock, mock_open
from imports import *
import sys
from os import path as os_path

# Add the parent directory to the sys.path
sys.path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

@pytest.mark.unit
@pytest.mark.asyncio
class TestIMAPTask:
    """Test the IMAP task functionality"""
    
    async def test_create_task_imap_receiver(self):
        """Test create_task_imap_receiver function"""
        from tasks.inputs.task_imap import create_task_imap_receiver
        
        with patch('tasks.inputs.task_imap.SupervisorManager') as mock_sm:
            mock_task = MagicMock()
            mock_sm.register_task.return_value = mock_task
            
            result = await create_task_imap_receiver()
            
            assert result is not None
            mock_sm.register_task.assert_called_once()
            
            # Check that the task was created with correct parameters
            call_args = mock_sm.register_task.call_args[0][0]
            assert call_args.name == "imap_retrieval_task"
            assert call_args.prompt_id == "Task_IMAP"
    
    async def test_supervisor_task_imap_initialization(self):
        """Test SupervisorTask_IMAP initialization"""
        from tasks.inputs.task_imap import SupervisorTask_IMAP
        
        task = SupervisorTask_IMAP(name="test_imap", prompt_id="test_prompt")
        
        assert task.name == "test_imap"
        assert task.prompt_id == "test_prompt"
        assert task.callback_response == False
        assert hasattr(task, 'llm_call')
    
    async def test_supervisor_task_imap_llm_call_ssl_connection(self):
        """Test SupervisorTask_IMAP llm_call with SSL connection"""
        from tasks.inputs.task_imap import SupervisorTask_IMAP
        
        task = SupervisorTask_IMAP(name="test_imap", prompt_id="test_prompt")
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.scheduled_guid = "test-scheduled-guid"
        
        # Mock user and task
        mock_user = MagicMock()
        mock_user_task = MagicMock()
        mock_user_task.request_human_in_the_loop = AsyncMock()
        mock_user.my_requests = {mock_state.scheduled_guid: mock_user_task}
        
        with patch('tasks.inputs.task_imap.OAuth2Verifier') as mock_oauth:
            # Mock OAuth token responses
            async def mock_get_token(identifier, token_type):
                if identifier == "imap" and token_type == "access_token":
                    return "imap.gmail.com"
                elif identifier == "imap" and token_type == "refresh_token":
                    return "<EMAIL>"
                elif identifier == "imap" and token_type == "expires_in":
                    return 993  # SSL port
                elif identifier == "imap" and token_type == "token_type":
                    return "test-password"
                return None
            
            mock_oauth.get_token = AsyncMock(side_effect=mock_get_token)
            
            with patch('tasks.inputs.task_imap.Globals') as mock_globals:
                mock_globals.is_docker.return_value = False
                
                with patch('os.getcwd', return_value="/test/path"):
                    with patch('imaplib.IMAP4_SSL') as mock_imap_ssl:
                        mock_mail = MagicMock()
                        mock_imap_ssl.return_value = mock_mail
                        mock_mail.login = MagicMock()
                        mock_mail.select = MagicMock()
                        mock_mail.search.return_value = ("OK", [b"1 2 3"])
                        mock_mail.fetch.return_value = ("OK", [(None, b"fake email content")])
                        mock_mail.logout = MagicMock()
                        
                        with patch('tasks.inputs.task_imap.ZairaUserManager') as mock_zum:
                            mock_zum.find_user = AsyncMock(return_value=mock_user)
                            
                            with patch('tasks.inputs.task_imap.MeltanoManager') as mock_meltano:
                                mock_meltano.ConvertFilesToVectorStore = AsyncMock()
                                
                                with patch('builtins.open', mock_open()) as mock_file:
                                    with patch('tasks.inputs.task_imap.os_path.join', return_value="/test/path/email_0001.eml"):
                                        # Mock user approval callback
                                        async def mock_user_approval(msg, callback, wait):
                                            await callback(mock_user_task, "ja")  # User approves all
                                        
                                        mock_user_task.request_human_in_the_loop = AsyncMock(side_effect=mock_user_approval)
                                        
                                        result = await task.llm_call(mock_state)
                                        
                                        # Verify SSL connection was used
                                        mock_imap_ssl.assert_called_once_with("imap.gmail.com", 993)
                                        
                                        # Verify login was attempted
                                        mock_mail.login.assert_called()
                                        
                                        # Verify emails were fetched
                                        assert mock_mail.fetch.call_count == 3  # 3 emails
                                        
                                        # Verify files were written
                                        assert mock_file.call_count == 3
                                        
                                        # Verify logout
                                        mock_mail.logout.assert_called_once()
                                        
                                        # Verify vector store conversion
                                        mock_meltano.ConvertFilesToVectorStore.assert_called_once()
    
    async def test_supervisor_task_imap_llm_call_non_ssl_connection(self):
        """Test SupervisorTask_IMAP llm_call with non-SSL connection"""
        from tasks.inputs.task_imap import SupervisorTask_IMAP
        
        task = SupervisorTask_IMAP(name="test_imap", prompt_id="test_prompt")
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.scheduled_guid = "test-scheduled-guid"
        
        # Mock user and task
        mock_user = MagicMock()
        mock_user_task = MagicMock()
        mock_user.my_requests = {mock_state.scheduled_guid: mock_user_task}
        
        with patch('tasks.inputs.task_imap.OAuth2Verifier') as mock_oauth:
            # Mock OAuth token responses for non-SSL
            async def mock_get_token(identifier, token_type):
                if identifier == "imap" and token_type == "access_token":
                    return "imap.example.com"
                elif identifier == "imap" and token_type == "refresh_token":
                    return "<EMAIL>"
                elif identifier == "imap" and token_type == "expires_in":
                    return 143  # Non-SSL port
                elif identifier == "imap" and token_type == "token_type":
                    return "test-password"
                return None
            
            mock_oauth.get_token = AsyncMock(side_effect=mock_get_token)
            
            with patch('tasks.inputs.task_imap.Globals') as mock_globals:
                mock_globals.is_docker.return_value = True  # Docker path
                
                with patch('imaplib.IMAP4') as mock_imap:
                    mock_mail = MagicMock()
                    mock_imap.return_value = mock_mail
                    mock_mail.login = MagicMock()
                    mock_mail.select = MagicMock()
                    mock_mail.search.return_value = ("OK", [b"1"])
                    mock_mail.fetch.return_value = ("OK", [(None, b"fake email content")])
                    mock_mail.logout = MagicMock()
                    
                    with patch('tasks.inputs.task_imap.ZairaUserManager') as mock_zum:
                        mock_zum.find_user = AsyncMock(return_value=mock_user)
                        
                        with patch('tasks.inputs.task_imap.MeltanoManager') as mock_meltano:
                            mock_meltano.ConvertFilesToVectorStore = AsyncMock()
                            
                            with patch('builtins.open', mock_open()) as mock_file:
                                with patch('tasks.inputs.task_imap.os_path.join', return_value="/meltano/output/email_0001.eml"):
                                    # Mock user approval callback
                                    async def mock_user_approval(msg, callback, wait):
                                        await callback(mock_user_task, "ja")  # User approves all
                                    
                                    mock_user_task.request_human_in_the_loop = AsyncMock(side_effect=mock_user_approval)
                                    
                                    result = await task.llm_call(mock_state)
                                    
                                    # Verify non-SSL connection was used
                                    mock_imap.assert_called_once_with("imap.example.com", 143)
                                    
                                    # Verify login and processing
                                    mock_mail.login.assert_called()
                                    mock_mail.fetch.assert_called_once()  # 1 email
                                    mock_file.assert_called_once()
    
    async def test_supervisor_task_imap_llm_call_login_fallback(self):
        """Test SupervisorTask_IMAP llm_call with login fallback"""
        from tasks.inputs.task_imap import SupervisorTask_IMAP
        
        task = SupervisorTask_IMAP(name="test_imap", prompt_id="test_prompt")
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.scheduled_guid = "test-scheduled-guid"
        
        # Mock user and task
        mock_user = MagicMock()
        mock_user_task = MagicMock()
        mock_user.my_requests = {mock_state.scheduled_guid: mock_user_task}
        
        with patch('tasks.inputs.task_imap.OAuth2Verifier') as mock_oauth:
            # Mock OAuth token responses
            async def mock_get_token(identifier, token_type):
                if identifier == "imap" and token_type == "access_token":
                    return "imap.gmail.com"
                elif identifier == "imap" and token_type == "refresh_token":
                    return "<EMAIL>"
                elif identifier == "imap" and token_type == "expires_in":
                    return 993
                elif identifier == "imap" and token_type == "token_type":
                    return "test-password"
                return None
            
            mock_oauth.get_token = AsyncMock(side_effect=mock_get_token)
            
            with patch('tasks.inputs.task_imap.Globals') as mock_globals:
                mock_globals.is_docker.return_value = False
                
                with patch('os.getcwd', return_value="/test/path"):
                    with patch('imaplib.IMAP4_SSL') as mock_imap_ssl:
                        mock_mail = MagicMock()
                        mock_imap_ssl.return_value = mock_mail
                        
                        # Mock first login to fail, second to succeed
                        mock_mail.login.side_effect = [Exception("Login failed"), None]
                        mock_mail.select = MagicMock()
                        mock_mail.search.return_value = ("OK", [b"1"])
                        mock_mail.fetch.return_value = ("OK", [(None, b"fake email content")])
                        mock_mail.logout = MagicMock()
                        
                        with patch('tasks.inputs.task_imap.ZairaUserManager') as mock_zum:
                            mock_zum.find_user = AsyncMock(return_value=mock_user)
                            
                            with patch('tasks.inputs.task_imap.MeltanoManager') as mock_meltano:
                                mock_meltano.ConvertFilesToVectorStore = AsyncMock()
                                
                                with patch('builtins.open', mock_open()):
                                    with patch('tasks.inputs.task_imap.os_path.join', return_value="/test/path/email_0001.eml"):
                                        # Mock user approval callback
                                        async def mock_user_approval(msg, callback, wait):
                                            await callback(mock_user_task, "ja")
                                        
                                        mock_user_task.request_human_in_the_loop = AsyncMock(side_effect=mock_user_approval)
                                        
                                        result = await task.llm_call(mock_state)
                                        
                                        # Verify login was attempted twice (fallback was used)
                                        assert mock_mail.login.call_count == 2
                                        
                                        # First call with full email
                                        mock_mail.login.assert_any_call("<EMAIL>", "test-password")
                                        
                                        # Second call with truncated email (fallback)
                                        mock_mail.login.assert_any_call("test@gmail", "test-password")
    
    async def test_supervisor_task_imap_llm_call_selective_saving(self):
        """Test SupervisorTask_IMAP llm_call with selective email saving"""
        from tasks.inputs.task_imap import SupervisorTask_IMAP
        
        task = SupervisorTask_IMAP(name="test_imap", prompt_id="test_prompt")
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.scheduled_guid = "test-scheduled-guid"
        
        # Mock user and task
        mock_user = MagicMock()
        mock_user_task = MagicMock()
        mock_user.my_requests = {mock_state.scheduled_guid: mock_user_task}
        
        with patch('tasks.inputs.task_imap.OAuth2Verifier') as mock_oauth:
            # Mock OAuth token responses
            async def mock_get_token(identifier, token_type):
                if identifier == "imap" and token_type == "access_token":
                    return "imap.gmail.com"
                elif identifier == "imap" and token_type == "refresh_token":
                    return "<EMAIL>"
                elif identifier == "imap" and token_type == "expires_in":
                    return 993
                elif identifier == "imap" and token_type == "token_type":
                    return "test-password"
                return None
            
            mock_oauth.get_token = AsyncMock(side_effect=mock_get_token)
            
            with patch('tasks.inputs.task_imap.Globals') as mock_globals:
                mock_globals.is_docker.return_value = False
                
                with patch('os.getcwd', return_value="/test/path"):
                    with patch('imaplib.IMAP4_SSL') as mock_imap_ssl:
                        mock_mail = MagicMock()
                        mock_imap_ssl.return_value = mock_mail
                        mock_mail.login = MagicMock()
                        mock_mail.select = MagicMock()
                        mock_mail.search.return_value = ("OK", [b"1 2"])  # 2 emails
                        mock_mail.fetch.return_value = ("OK", [(None, b"fake email content")])
                        mock_mail.logout = MagicMock()
                        
                        with patch('tasks.inputs.task_imap.ZairaUserManager') as mock_zum:
                            mock_zum.find_user = AsyncMock(return_value=mock_user)
                            
                            with patch('tasks.inputs.task_imap.MeltanoManager') as mock_meltano:
                                mock_meltano.ConvertFilesToVectorStore = AsyncMock()
                                
                                with patch('builtins.open', mock_open()) as mock_file:
                                    with patch('tasks.inputs.task_imap.os_path.join', side_effect=["/test/path/email_0001.eml", "/test/path/email_0002.eml"]):
                                        # Mock selective approval: "no" for all, then "yes" for first email, "no" for second
                                        call_count = 0
                                        async def mock_selective_approval(msg, callback, wait):
                                            nonlocal call_count
                                            call_count += 1
                                            if call_count == 1:  # First question: save all?
                                                await callback(mock_user_task, "nee")  # No to all
                                            elif call_count == 2:  # Second question: save email 1?
                                                await callback(mock_user_task, "ja")   # Yes to email 1
                                            elif call_count == 3:  # Third question: save email 2?
                                                await callback(mock_user_task, "nee")  # No to email 2
                                        
                                        mock_user_task.request_human_in_the_loop = AsyncMock(side_effect=mock_selective_approval)
                                        
                                        result = await task.llm_call(mock_state)
                                        
                                        # Verify 3 human-in-the-loop calls were made
                                        assert mock_user_task.request_human_in_the_loop.call_count == 3
                                        
                                        # Verify only 1 file was written (first email)
                                        assert mock_file.call_count == 1
    
    async def test_supervisor_task_imap_llm_call_no_emails(self):
        """Test SupervisorTask_IMAP llm_call when no emails found"""
        from tasks.inputs.task_imap import SupervisorTask_IMAP
        
        task = SupervisorTask_IMAP(name="test_imap", prompt_id="test_prompt")
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.scheduled_guid = "test-scheduled-guid"
        
        # Mock user and task
        mock_user = MagicMock()
        mock_user_task = MagicMock()
        mock_user.my_requests = {mock_state.scheduled_guid: mock_user_task}
        
        with patch('tasks.inputs.task_imap.OAuth2Verifier') as mock_oauth:
            # Mock OAuth token responses
            async def mock_get_token(identifier, token_type):
                if identifier == "imap" and token_type == "access_token":
                    return "imap.gmail.com"
                elif identifier == "imap" and token_type == "refresh_token":
                    return "<EMAIL>"
                elif identifier == "imap" and token_type == "expires_in":
                    return 993
                elif identifier == "imap" and token_type == "token_type":
                    return "test-password"
                return None
            
            mock_oauth.get_token = AsyncMock(side_effect=mock_get_token)
            
            with patch('tasks.inputs.task_imap.Globals') as mock_globals:
                mock_globals.is_docker.return_value = False
                
                with patch('os.getcwd', return_value="/test/path"):
                    with patch('imaplib.IMAP4_SSL') as mock_imap_ssl:
                        mock_mail = MagicMock()
                        mock_imap_ssl.return_value = mock_mail
                        mock_mail.login = MagicMock()
                        mock_mail.select = MagicMock()
                        mock_mail.search.return_value = ("OK", [b""])  # No emails
                        mock_mail.logout = MagicMock()
                        
                        with patch('tasks.inputs.task_imap.ZairaUserManager') as mock_zum:
                            mock_zum.find_user = AsyncMock(return_value=mock_user)
                            
                            with patch('tasks.inputs.task_imap.MeltanoManager') as mock_meltano:
                                mock_meltano.ConvertFilesToVectorStore = AsyncMock()
                                
                                # Mock user approval callback
                                async def mock_user_approval(msg, callback, wait):
                                    await callback(mock_user_task, "ja")
                                
                                mock_user_task.request_human_in_the_loop = AsyncMock(side_effect=mock_user_approval)
                                
                                result = await task.llm_call(mock_state)
                                
                                # Verify no fetch calls were made (no emails)
                                mock_mail.fetch.assert_not_called()
                                
                                # Verify only one human-in-the-loop call (for "save all")
                                assert mock_user_task.request_human_in_the_loop.call_count == 1
                                
                                # Verify vector store conversion was still called
                                mock_meltano.ConvertFilesToVectorStore.assert_called_once()
    
    async def test_supervisor_task_imap_callback_response_attribute(self):
        """Test that callback_response attribute works correctly"""
        from tasks.inputs.task_imap import SupervisorTask_IMAP
        
        task = SupervisorTask_IMAP(name="test_imap", prompt_id="test_prompt")
        
        # Initial state
        assert task.callback_response == False
        
        # Test setting to True
        task.callback_response = True
        assert task.callback_response == True
        
        # Test setting back to False
        task.callback_response = False
        assert task.callback_response == False