from imports import *

import json
import onnxruntime
import numpy as np
from llama_index.embeddings.huggingface_optimum import OptimumEmbedding
from transformers import AutoTokenizer
from langchain_text_splitters import RecursiveCharacterTextSplitter
from typing import List, Union

class RetrievalManager:
    _instance = None
    _initialized = False

    session: onnxruntime.InferenceSession = None
    tokenizer: AutoTokenizer = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return

        # Load the ONNX model (adjust the path as necessary)
        onnx_model_path = BASE_DIR() / "bge_onnx" / "model.onnx"
        instance.session = onnxruntime.InferenceSession(onnx_model_path)
        # Load the tokenizer (adjust model name if different)
        instance.tokenizer = AutoTokenizer.from_pretrained("BAAI/bge-small-en")

        instance._initialized = True

    # Convert JSON to a list of elements (simplified for Markdown)
    @classmethod
    async def json_to_dict(cls, data, parent_key=""):
        elements = []
        if isinstance(data, str):
            elements.extend(await cls.json_to_dict(json.loads(data)))
        if isinstance(data, dict):
            for key, value in data.items():
                new_key = f"{parent_key}.{key}" if parent_key else key
                if isinstance(value, (dict, list)):
                    elements.extend(await cls.json_to_dict(value, new_key))
                else:
                    elements.append({"id": new_key, "text": value})
        elif isinstance(data, list):
            for i, item in enumerate(data):
                new_key = f"{parent_key}[{i}]"
                if isinstance(item, (dict, list)):
                    elements.extend(await cls.json_to_dict(item, new_key))
                else:
                    elements.append({"id": new_key, "text": item})
        return elements

    @classmethod
    async def unstructured_to_list(cls, elements):
        ret_val = []
        for element in elements:
            ret_val.append({
                "type": type(element).__name__,
                "id": getattr(element, "id", None),
                "text": str(element),
                "metadata": element.metadata.to_dict() if hasattr(element.metadata, "to_dict") else str(element.metadata),
            })
        return ret_val

    @classmethod
    def safe_get(cls, obj, key, default=None):
        try:
            if isinstance(obj, dict):
                return obj.get(key, default)
            return getattr(obj, key, default)
        except Exception:
            return default

    @classmethod
    async def list_to_dict(cls, mylist):
        ret_val = {}
        for item in mylist:
            ret_val[cls.safe_get(item,'id')] = cls.safe_get(item,'text')
        return ret_val

    @classmethod
    async def elements_to_markdown(cls, data, prefix=""):
        markdown_content = prefix
        def iteration(ret_val, my_data):
            for element in my_data:
                ret_val += f"**{element}**: {cls.safe_get(my_data, element)}\n\n"
            return ret_val
        if isinstance(data, list):
            for item in data:
                markdown_content = iteration(ret_val=markdown_content,my_data=item)
        else:
            markdown_content = iteration(ret_val=markdown_content,my_data=data)
        return markdown_content

    @classmethod
    async def get_embeddings_dense(cls, text):
        instance = cls.get_instance()
        
        # Add initialization check
        if not instance._initialized or instance.session is None or instance.tokenizer is None:
            await cls.setup()

        """
        Generate embeddings using the ONNX model with tokenized inputs.
        Compatible with OpenAI's input expectations.
        """
        # Handle single string or list of strings (OpenAI accepts both)
        if isinstance(text, str):
            texts = [text]
        elif isinstance(text, list):
            texts = text
        else:
            raise ValueError("Input must be a string or list of strings")

        # Tokenize the input (assuming apiendpoint.tokenizer is available)
        inputs = instance.tokenizer(
            texts,
            padding=True,
            truncation=True,
            return_tensors="np",  # Return NumPy arrays for ONNX
            max_length=EMBEDDING_SIZE  # Matches common OpenAI defaults, adjust if needed
        )

        # Prepare input feed for ONNX, casting to int64
        input_feed = {
            "input_ids": inputs["input_ids"].astype(np.int64),
            "attention_mask": inputs["attention_mask"].astype(np.int64),
            "token_type_ids": inputs.get("token_type_ids", np.zeros_like(inputs["input_ids"])).astype(np.int64)
        }

        # Run the ONNX model (assuming apiendpoint.session is your ONNX session)
        output1 = instance.session.run(None, input_feed)

        # Alternative embedding generation (assuming Settings.embed_model exists)
        # If this is redundant, remove it and use output1[0] directly
        embeddings = [ZairaSettings.OllamaSettings().embed_model.get_text_embedding(t) for t in texts]

        # Convert embeddings to list if they are NumPy arrays (OpenAI expects JSON-serializable output)
        embeddings = [e.tolist() if isinstance(e, np.ndarray) else e for e in embeddings]

        return embeddings[0]

    @classmethod
    async def get_embeddings_sparse(cls, text):
        return list(ZairaSettings.sparse_embed_model.embed(text))[0]

    @classmethod
    async def chunk_text(cls, data: Union[str, List[str]], chunk_size: int = CHUNK_SIZE, chunk_overlap: int = CHUNK_OVERLAP) -> List[str]:
        """
        Split text into chunks using LangChain's RecursiveCharacterTextSplitter.
        """
        # Validate parameters - overlap cannot be larger than chunk size
        if chunk_overlap >= chunk_size:
            chunk_overlap = max(0, chunk_size // 4)  # Set overlap to 25% of chunk size
            print(f"Warning: chunk_overlap was larger than chunk_size. Adjusted overlap to {chunk_overlap}")

        # Initialize the RecursiveCharacterTextSplitter
        text_splitter = RecursiveCharacterTextSplitter(
            chunk_size=chunk_size,
            chunk_overlap=chunk_overlap,
            length_function=len,
            is_separator_regex=False,
        )

        # Handle different input types
        if isinstance(data, str):
            # Single string input
            chunks = text_splitter.split_text(data)
        elif isinstance(data, list):
            # List of strings input - concatenate and split
            combined_text = "\n\n".join(str(item) for item in data)
            chunks = text_splitter.split_text(combined_text)
        else:
            # Convert other types to string
            text_data = str(data)
            chunks = text_splitter.split_text(text_data)

        return chunks
    
    @classmethod
    async def chunk_multimodal_elements(cls, multimodal_data: dict, chunk_size: int = CHUNK_SIZE, 
                                      chunk_overlap: int = CHUNK_OVERLAP) -> List[str]:
        """
        Create chunks from multimodal document elements while preserving context.
        
        Args:
            multimodal_data: Output from MultimodalManager.extract_multimodal_elements()
            chunk_size: Maximum size of each chunk
            chunk_overlap: Overlap between chunks
            
        Returns:
            List of text chunks with preserved multimodal context
        """
        # Combine all text elements in order, preserving multimodal elements as markers
        combined_elements = []
        
        # Process all elements in order
        for element_data in multimodal_data.get("all_elements", []):
            element_type = element_data.get("type", "")
            element_text = element_data.get("text", "")
            
            if element_type == "Image":
                # Add image placeholder with summary
                image_summary = element_data.get("summary", "Image content")
                combined_elements.append(f"\n[IMAGE: {image_summary}]\n")
                
            elif element_type == "Table":
                # Add table with summary and structure
                table_summary = element_data.get("summary", "Table content")
                table_markdown = element_data.get("markdown", "")
                combined_elements.append(f"\n[TABLE: {table_summary}]\n{table_markdown}\n")
                
            elif element_type == "Figure":
                # Add figure placeholder
                combined_elements.append(f"\n[FIGURE: {element_text}]\n")
                
            elif element_type == "FigureCaption":
                # Add caption
                combined_elements.append(f"\n[CAPTION: {element_text}]\n")
                
            else:
                # Regular text elements (Title, NarrativeText, etc.)
                combined_elements.append(element_text)
        
        # Join all elements
        combined_text = "\n".join(combined_elements)
        
        # Use context-aware chunking that respects multimodal boundaries
        chunks = await cls._chunk_with_multimodal_awareness(
            combined_text, chunk_size, chunk_overlap
        )
        
        return chunks
    
    @classmethod
    async def _chunk_with_multimodal_awareness(cls, text: str, chunk_size: int, chunk_overlap: int) -> List[str]:
        """
        Chunk text while preserving multimodal element boundaries.
        """
        # Define multimodal markers
        multimodal_markers = ["[IMAGE:", "[TABLE:", "[FIGURE:", "[CAPTION:"]
        
        # Split text into paragraphs/sections
        paragraphs = text.split('\n\n')
        
        chunks = []
        current_chunk = ""
        
        for paragraph in paragraphs:
            # Check if this paragraph contains multimodal content
            has_multimodal = any(marker in paragraph for marker in multimodal_markers)
            
            # Check if adding this paragraph would exceed chunk size
            potential_chunk = current_chunk + "\n\n" + paragraph if current_chunk else paragraph
            
            if len(potential_chunk) <= chunk_size:
                # Add paragraph to current chunk
                current_chunk = potential_chunk
            else:
                # Current chunk is full
                if current_chunk:
                    chunks.append(current_chunk.strip())
                
                # Handle large paragraphs
                if len(paragraph) > chunk_size:
                    if has_multimodal:
                        # For multimodal content, try to keep it intact but split if absolutely necessary
                        sub_chunks = await cls._split_large_multimodal_paragraph(paragraph, chunk_size)
                        chunks.extend(sub_chunks)
                        current_chunk = ""
                    else:
                        # For regular text, use normal splitting
                        from langchain_text_splitters import RecursiveCharacterTextSplitter
                        text_splitter = RecursiveCharacterTextSplitter(
                            chunk_size=chunk_size,
                            chunk_overlap=chunk_overlap,
                            length_function=len,
                            is_separator_regex=False,
                        )
                        sub_chunks = text_splitter.split_text(paragraph)
                        chunks.extend(sub_chunks[:-1])  # Add all but last
                        current_chunk = sub_chunks[-1] if sub_chunks else ""
                else:
                    # Start new chunk with this paragraph
                    current_chunk = paragraph
        
        # Add final chunk if exists
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        # Apply overlap between chunks
        if chunk_overlap > 0 and len(chunks) > 1:
            chunks = await cls._apply_overlap_to_chunks(chunks, chunk_overlap)
        
        return [chunk for chunk in chunks if chunk.strip()]
    
    @classmethod
    async def _split_large_multimodal_paragraph(cls, paragraph: str, max_size: int) -> List[str]:
        """
        Split large paragraphs containing multimodal content while preserving context.
        """
        # Try to split at sentence boundaries first
        sentences = paragraph.split('. ')
        
        chunks = []
        current_chunk = ""
        
        for i, sentence in enumerate(sentences):
            # Add period back except for last sentence
            if i < len(sentences) - 1:
                sentence += ". "
            
            potential_chunk = current_chunk + sentence
            
            if len(potential_chunk) <= max_size:
                current_chunk = potential_chunk
            else:
                if current_chunk:
                    chunks.append(current_chunk.strip())
                current_chunk = sentence
        
        if current_chunk:
            chunks.append(current_chunk.strip())
        
        return chunks
    
    @classmethod
    async def _apply_overlap_to_chunks(cls, chunks: List[str], overlap_size: int) -> List[str]:
        """
        Apply overlap between chunks while preserving multimodal boundaries.
        """
        if len(chunks) <= 1:
            return chunks
        
        overlapped_chunks = [chunks[0]]  # First chunk remains unchanged
        
        for i in range(1, len(chunks)):
            prev_chunk = chunks[i-1]
            current_chunk = chunks[i]
            
            # Get overlap from previous chunk
            overlap_text = prev_chunk[-overlap_size:] if len(prev_chunk) > overlap_size else prev_chunk
            
            # Try to find a good break point in the overlap (sentence/paragraph boundary)
            overlap_sentences = overlap_text.split('. ')
            if len(overlap_sentences) > 1:
                # Use last complete sentence as overlap
                overlap_text = '. '.join(overlap_sentences[-2:])
            
            # Combine overlap with current chunk
            combined_chunk = overlap_text + "\n\n" + current_chunk
            overlapped_chunks.append(combined_chunk)
        
        return overlapped_chunks
