from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from endpoints.teams_endpoint import MyTeamsBot, on_turn_error
from aiohttp import web
from uuid import uuid4

class TestMyTeamsBot:
    """Test suite for MyTeamsBot"""
    
    def setup_method(self):
        """Setup test fixtures"""
        # Reset singleton instance
        MyTeamsBot._instance = None
        self.bot = MyTeamsBot()
        
    def test_singleton_pattern(self):
        """Test that MyTeamsBot follows singleton pattern"""
        bot1 = MyTeamsBot()
        bot2 = MyTeamsBot()
        bot3 = MyTeamsBot.get_instance()
        
        assert bot1 is bot2
        assert bot2 is bot3
        assert bot1._instance is not None
        
    def test_initialization(self):
        """Test MyTeamsBot initialization"""
        bot = MyTeamsBot()
        
        assert hasattr(bot, 'initialized')
        assert bot.initialized == True
        assert bot.adapter is None
        assert bot.aio_app is None
        assert bot.asyncio_Task is None
        assert bot.bot_generic is None
        
    @pytest.mark.asyncio
    async def test_setup_debug_mode(self):
        """Test setup method in debug mode"""
        with patch('endpoints.teams_endpoint.Globals.is_debug', return_value=True):
            await MyTeamsBot.setup()
            
            # Should not create adapter in debug mode
            instance = MyTeamsBot.get_instance()
            assert instance.adapter is None
            assert instance.aio_app is None
            
    @pytest.mark.asyncio
    async def test_setup_production_mode(self):
        """Test setup method in production mode"""
        with patch('endpoints.teams_endpoint.Globals.is_debug', return_value=False), \
             patch('botbuilder.core.BotFrameworkAdapter') as mock_adapter, \
             patch('aiohttp.web.Application') as mock_app, \
             patch('endpoints.mybot_generic.MyBot_Generic') as mock_bot_generic:
            
            mock_adapter_instance = MagicMock()
            mock_adapter.return_value = mock_adapter_instance
            mock_app_instance = MagicMock()
            mock_app.return_value = mock_app_instance
            mock_bot_generic.return_value = MagicMock()
            
            await MyTeamsBot.setup()
            
            instance = MyTeamsBot.get_instance()
            assert instance.adapter == mock_adapter_instance
            assert instance.aio_app == mock_app_instance
            assert instance.bot_generic is not None
            
            # Verify routes were added
            mock_app_instance.add_routes.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_teams_auth_endpoint(self):
        """Test teams_auth endpoint"""
        request = MagicMock()
        request.query = {'code': 'test_code'}
        
        with patch('endpoints.oauth._verifier_.OAuth2Verifier.get_instance') as mock_verifier:
            mock_verifier_instance = MagicMock()
            mock_verifier.return_value = mock_verifier_instance
            mock_verifier_instance.teams_auth = AsyncMock(return_value='auth_result')
            
            response = await self.bot.teams_auth(request)
            
            assert isinstance(response, web.Response)
            assert response.text == 'auth_result'
            mock_verifier_instance.teams_auth.assert_called_once_with('test_code')
            
    @pytest.mark.asyncio
    async def test_teams_logout_endpoint(self):
        """Test teams_logout endpoint"""
        request = MagicMock()
        
        with patch('endpoints.oauth._verifier_.OAuth2Verifier.get_instance') as mock_verifier:
            mock_verifier_instance = MagicMock()
            mock_verifier.return_value = mock_verifier_instance
            mock_verifier_instance.teams_logout = AsyncMock(return_value='logout_result')
            
            response = await self.bot.teams_logout(request)
            
            assert isinstance(response, web.Response)
            assert response.text == 'logout_result'
            mock_verifier_instance.teams_logout.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_teams_messages_endpoint(self):
        """Test teams_messages endpoint"""
        request = MagicMock()
        request.headers = {'Authorization': 'Bearer test_token'}
        
        mock_activity = MagicMock()
        mock_activity.type = 'message'
        
        with patch.object(self.bot, 'adapter') as mock_adapter:
            mock_adapter.process_activity = AsyncMock()
            
            response = await self.bot.teams_messages(request)
            
            assert isinstance(response, web.Response)
            assert response.status == 200
            mock_adapter.process_activity.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_teams_messages_endpoint_exception(self):
        """Test teams_messages endpoint with exception"""
        request = MagicMock()
        request.headers = {'Authorization': 'Bearer test_token'}
        
        with patch.object(self.bot, 'adapter') as mock_adapter:
            mock_adapter.process_activity = AsyncMock(side_effect=Exception("Processing error"))
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                response = await self.bot.teams_messages(request)
                
                assert isinstance(response, web.Response)
                assert response.status == 500
                mock_exception.assert_called_once()
                
    @pytest.mark.asyncio
    async def test_on_message_activity(self):
        """Test on_message_activity method"""
        mock_turn_context = MagicMock()
        mock_turn_context.activity = MagicMock()
        mock_turn_context.activity.type = 'message'
        mock_turn_context.activity.text = 'Hello bot'
        mock_turn_context.activity.from_property = MagicMock()
        mock_turn_context.activity.from_property.id = 'user123'
        mock_turn_context.activity.from_property.name = 'Test User'
        mock_turn_context.activity.channel_id = 'msteams'
        mock_turn_context.activity.conversation = MagicMock()
        mock_turn_context.activity.conversation.conversation_type = 'personal'
        
        mock_user = MagicMock()
        mock_user.username = 'Test User'
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user), \
             patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            
            mock_bot_generic.on_message = AsyncMock()
            
            await self.bot.on_message_activity(mock_turn_context)
            
            mock_bot_generic.on_message.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_on_message_activity_new_user(self):
        """Test on_message_activity with new user creation"""
        mock_turn_context = MagicMock()
        mock_turn_context.activity = MagicMock()
        mock_turn_context.activity.type = 'message'
        mock_turn_context.activity.text = 'Hello bot'
        mock_turn_context.activity.from_property = MagicMock()
        mock_turn_context.activity.from_property.id = 'user123'
        mock_turn_context.activity.from_property.name = 'New User'
        mock_turn_context.activity.channel_id = 'msteams'
        mock_turn_context.activity.conversation = MagicMock()
        mock_turn_context.activity.conversation.conversation_type = 'personal'
        
        mock_user = MagicMock()
        mock_user.username = 'New User'
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=None), \
             patch('managers.manager_users.ZairaUserManager.create_temp_user', return_value=mock_user), \
             patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            
            mock_bot_generic.on_message = AsyncMock()
            
            await self.bot.on_message_activity(mock_turn_context)
            
            mock_bot_generic.on_message.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_on_members_added_activity(self):
        """Test on_members_added_activity method"""
        mock_turn_context = MagicMock()
        mock_turn_context.activity = MagicMock()
        mock_turn_context.activity.members_added = [
            MagicMock(id='user123', name='New Member')
        ]
        
        with patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            mock_bot_generic.on_member_join = AsyncMock()
            
            await self.bot.on_members_added_activity(mock_turn_context)
            
            mock_bot_generic.on_member_join.assert_called_once_with('New Member', mock_turn_context)
            
    @pytest.mark.asyncio
    async def test_on_members_added_activity_multiple_members(self):
        """Test on_members_added_activity with multiple members"""
        mock_turn_context = MagicMock()
        mock_turn_context.activity = MagicMock()
        mock_turn_context.activity.members_added = [
            MagicMock(id='user123', name='Member 1'),
            MagicMock(id='user456', name='Member 2')
        ]
        
        with patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            mock_bot_generic.on_member_join = AsyncMock()
            
            await self.bot.on_members_added_activity(mock_turn_context)
            
            assert mock_bot_generic.on_member_join.call_count == 2
            
    @pytest.mark.asyncio
    async def test_send_teams_broadcast_not_implemented(self):
        """Test send_teams_broadcast method (not implemented)"""
        # This method is not implemented, should not raise exception
        try:
            await MyTeamsBot.send_teams_broadcast("Test message")
        except NotImplementedError:
            pass  # Expected
        except Exception as e:
            pytest.fail(f"Unexpected exception: {e}")
            
    @pytest.mark.asyncio
    async def test_send_a_teams_message(self):
        """Test send_a_teams_message method"""
        mock_turn_context = MagicMock()
        mock_turn_context.send_activity = AsyncMock()
        
        with patch('botbuilder.core.MessageFactory') as mock_message_factory:
            mock_activity = MagicMock()
            mock_message_factory.text.return_value = mock_activity
            
            await MyTeamsBot.send_a_teams_message(mock_turn_context, "Test message")
            
            mock_message_factory.text.assert_called_once_with("Test message")
            mock_turn_context.send_activity.assert_called_once_with(mock_activity)
            
class TestOnTurnError:
    """Test the on_turn_error function"""
    
    @pytest.mark.asyncio
    async def test_on_turn_error_function(self):
        """Test on_turn_error function"""
        mock_context = MagicMock()
        mock_context.send_activity = AsyncMock()
        mock_error = Exception("Test error")
        
        with patch('botbuilder.core.MessageFactory') as mock_message_factory, \
             patch('etc.helper_functions.exception_triggered') as mock_exception:
            
            mock_activity = MagicMock()
            mock_message_factory.text.return_value = mock_activity
            
            await on_turn_error(mock_context, mock_error)
            
            mock_exception.assert_called_once_with(mock_error, "teams_endpoint.on_turn_error", None)
            mock_message_factory.text.assert_called_once_with("The bot encountered an error or bug.")
            mock_context.send_activity.assert_called_once_with(mock_activity)
            
    @pytest.mark.asyncio
    async def test_on_turn_error_send_activity_exception(self):
        """Test on_turn_error when send_activity also fails"""
        mock_context = MagicMock()
        mock_context.send_activity = AsyncMock(side_effect=Exception("Send error"))
        mock_error = Exception("Test error")
        
        with patch('botbuilder.core.MessageFactory') as mock_message_factory, \
             patch('etc.helper_functions.exception_triggered') as mock_exception:
            
            mock_activity = MagicMock()
            mock_message_factory.text.return_value = mock_activity
            
            await on_turn_error(mock_context, mock_error)
            
            # Should call exception_triggered twice - once for original error, once for send error
            assert mock_exception.call_count == 2
            
class TestMyTeamsBotEdgeCases:
    """Test edge cases for MyTeamsBot"""
    
    def setup_method(self):
        """Setup test fixtures"""
        MyTeamsBot._instance = None
        self.bot = MyTeamsBot()
        
    @pytest.mark.asyncio
    async def test_exception_handling_in_setup(self):
        """Test exception handling in setup method"""
        with patch('endpoints.teams_endpoint.Globals.is_debug', return_value=False), \
             patch('botbuilder.core.BotFrameworkAdapter', side_effect=Exception("Adapter error")), \
             patch('etc.helper_functions.exception_triggered') as mock_exception:
            
            await MyTeamsBot.setup()
            
            mock_exception.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_on_message_activity_exception_handling(self):
        """Test exception handling in on_message_activity"""
        mock_turn_context = MagicMock()
        mock_turn_context.activity = MagicMock()
        mock_turn_context.activity.type = 'message'
        mock_turn_context.activity.text = 'Hello bot'
        mock_turn_context.activity.from_property = MagicMock()
        mock_turn_context.activity.from_property.id = 'user123'
        mock_turn_context.activity.from_property.name = 'Test User'
        mock_turn_context.activity.channel_id = 'msteams'
        mock_turn_context.activity.conversation = MagicMock()
        mock_turn_context.activity.conversation.conversation_type = 'personal'
        
        with patch('managers.manager_users.ZairaUserManager.find_user', side_effect=Exception("User error")), \
             patch('etc.helper_functions.exception_triggered') as mock_exception:
            
            await self.bot.on_message_activity(mock_turn_context)
            
            mock_exception.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_channel_type_detection(self):
        """Test channel type detection in on_message_activity"""
        # Test personal conversation
        mock_turn_context = MagicMock()
        mock_turn_context.activity = MagicMock()
        mock_turn_context.activity.type = 'message'
        mock_turn_context.activity.text = 'Hello bot'
        mock_turn_context.activity.from_property = MagicMock()
        mock_turn_context.activity.from_property.id = 'user123'
        mock_turn_context.activity.from_property.name = 'Test User'
        mock_turn_context.activity.channel_id = 'msteams'
        mock_turn_context.activity.conversation = MagicMock()
        mock_turn_context.activity.conversation.conversation_type = 'personal'
        
        mock_user = MagicMock()
        mock_user.username = 'Test User'
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user), \
             patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            
            mock_bot_generic.on_message = AsyncMock()
            
            await self.bot.on_message_activity(mock_turn_context)
            
            args, kwargs = mock_bot_generic.on_message.call_args
            assert args[0] == "private"  # channel_type
            
        # Test group conversation
        mock_turn_context.activity.conversation.conversation_type = 'group'
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user), \
             patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            
            mock_bot_generic.on_message = AsyncMock()
            
            await self.bot.on_message_activity(mock_turn_context)
            
            args, kwargs = mock_bot_generic.on_message.call_args
            assert args[0] == "public"  # channel_type
            
    @pytest.mark.asyncio
    async def test_empty_message_handling(self):
        """Test handling of empty messages"""
        mock_turn_context = MagicMock()
        mock_turn_context.activity = MagicMock()
        mock_turn_context.activity.type = 'message'
        mock_turn_context.activity.text = ''
        mock_turn_context.activity.from_property = MagicMock()
        mock_turn_context.activity.from_property.id = 'user123'
        mock_turn_context.activity.from_property.name = 'Test User'
        mock_turn_context.activity.channel_id = 'msteams'
        mock_turn_context.activity.conversation = MagicMock()
        mock_turn_context.activity.conversation.conversation_type = 'personal'
        
        mock_user = MagicMock()
        mock_user.username = 'Test User'
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user), \
             patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            
            mock_bot_generic.on_message = AsyncMock()
            
            await self.bot.on_message_activity(mock_turn_context)
            
            mock_bot_generic.on_message.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_missing_activity_properties(self):
        """Test handling of missing activity properties"""
        mock_turn_context = MagicMock()
        mock_turn_context.activity = MagicMock()
        mock_turn_context.activity.type = 'message'
        mock_turn_context.activity.text = 'Hello'
        mock_turn_context.activity.from_property = None  # Missing from_property
        mock_turn_context.activity.channel_id = 'msteams'
        mock_turn_context.activity.conversation = MagicMock()
        mock_turn_context.activity.conversation.conversation_type = 'personal'
        
        with patch('etc.helper_functions.exception_triggered') as mock_exception:
            await self.bot.on_message_activity(mock_turn_context)
            
            # Should handle missing properties gracefully
            mock_exception.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_concurrent_message_processing(self):
        """Test concurrent message processing"""
        mock_turn_context = MagicMock()
        mock_turn_context.activity = MagicMock()
        mock_turn_context.activity.type = 'message'
        mock_turn_context.activity.text = 'Hello bot'
        mock_turn_context.activity.from_property = MagicMock()
        mock_turn_context.activity.from_property.id = 'user123'
        mock_turn_context.activity.from_property.name = 'Test User'
        mock_turn_context.activity.channel_id = 'msteams'
        mock_turn_context.activity.conversation = MagicMock()
        mock_turn_context.activity.conversation.conversation_type = 'personal'
        
        mock_user = MagicMock()
        mock_user.username = 'Test User'
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user), \
             patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            
            mock_bot_generic.on_message = AsyncMock()
            
            # Process multiple messages concurrently
            await asyncio.gather(
                self.bot.on_message_activity(mock_turn_context),
                self.bot.on_message_activity(mock_turn_context),
                self.bot.on_message_activity(mock_turn_context)
            )
            
            # Should handle all messages
            assert mock_bot_generic.on_message.call_count == 3
