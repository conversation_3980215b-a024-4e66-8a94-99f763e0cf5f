#!/usr/bin/env python3
"""
Real Email System Integration Tests - PRODUCTION SYSTEM ONLY

This module tests the complete email system using the IDENTICAL production
system configuration. NO test environment isolation - this connects directly
to the production database, IMAP, SMTP, and all other services.

Test requirements:
- IMAP idle scheduled request exists for SYSTEM user
- SMTP email <NAME_EMAIL>
- Verification that email_generator_tool executes a second time when IMAP processes the received email
- Detection of the second email generator execution indicates the email round-trip is working
"""

from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

import pytest
import asyncio
import os
import time
import json
from typing import Dict, Any, List
from uuid import uuid4
from datetime import datetime, timedelta
import functools
from pathlib import Path

# Test debug capture
from tests.test_real.test_debug_capture import setup_test_debug_capture, cleanup_test_debug_capture

# Import LogFire for logging
from managers.manager_logfire import LogFire


def with_test_logging(func):
    """Decorator to automatically enable test logging for async test methods and server cleanup"""
    @functools.wraps(func)
    async def wrapper(self, *args, **kwargs):
        # Start logging with the test function name
        setup_test_debug_capture(func.__name__)
        try:
            # Run the actual test
            result = await func(self, *args, **kwargs)
            return result
        except Exception as e:
            # Log the exception but ALWAYS re-raise it
            import traceback
            LogFire.log("DEBUG", f"\n[ERROR] Test failed with exception: {e}")
            LogFire.log("DEBUG", f"Traceback:\n{traceback.format_exc()}")
            raise  # CRITICAL: Always re-raise to ensure pytest sees the failure
        finally:
            # Cleanup servers if this is a BaseRealTest instance
            if hasattr(self, 'cleanup_servers'):
                try:
                    LogFire.log("DEBUG", f"[{getattr(self, 'test_name', 'Unknown')}] Performing server cleanup...")
                    await self.cleanup_servers()
                except Exception as e:
                    LogFire.log("ERROR", f"Server cleanup failed: {e}")
            
            # Cleanup LogFire resources and background workers
            try:
                await LogFire.cleanup_all_resources()
            except Exception as e:
                print(f"LogFire cleanup failed: {e}")
            
            # Always stop logging
            cleanup_test_debug_capture()
    return wrapper



def skip_if_no_smtp():
    """Skip test if SMTP is not configured"""
    return lambda func: func


def requires_openai_api():
    """Skip test if OpenAI API key is not configured"""
    # Import config.py to ensure API key is loaded
    try:
        import config
        api_key_configured = os.environ.get('OPENAI_API_KEY')
    except ImportError:
        api_key_configured = os.environ.get('OPENAI_API_KEY')
    
    return pytest.mark.skipif(
        not api_key_configured,
        reason="OpenAI API key not configured - ensure config.py is loaded"
    )


from tests.test_real.base_real_test import BaseRealTest

class TestRealEmail(BaseRealTest):
    """
    Test suite for real email system functionality using PRODUCTION system.
    
    This class uses the actual production mainFunc() via BaseRealTest to ensure
    100% identical behavior to production - no more "test_real isn't real" issues!
    """

    @with_test_logging
    @requires_openai_api()
    @skip_if_no_smtp()
    @pytest.mark.asyncio
    async def test_real_email(self):
        """Test real email functionality: IMAP idle scheduled request, SMTP sending, and email_generator_tool second execution detection"""
        LogFire.log("DEBUG", f"[{self.test_name}] ===== STARTING TEST_REAL_EMAIL =====")
        LogFire.log("DEBUG", f"[{self.test_name}] Test will verify: IMAP idle -> Email sending -> Second email generator execution detection")
        
        try:
            # Initialize production system
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: Initializing production system...")
            setup_success = await self.setup_real_system()
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: Setup result = {setup_success}")
            
            if not setup_success:
                LogFire.log("DEBUG", f"[{self.test_name}] ERROR: Production system setup failed!")
                pytest.fail("Failed to initialize real production system")
            
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: [OK] Production system initialized successfully")
        
            # Get user and bot
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: Getting production test user and bot...")
            user = self.production_test_user
            bot = self.production_testing_bot
            user.email = "<EMAIL>"
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: [OK] User: {user.username}, Bot: {getattr(bot, 'name', 'Testing')}, Email: {user.email}")
        
            # Import modules needed for test
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: Importing required managers...")
            from managers.manager_system_user import SystemUserManager
            from managers.scheduled_requests import ScheduledRequestPersistenceManager
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: [OK] Managers imported successfully")
            
            # Test configuration
            test_email_subject = f"Test Email {str(uuid4())[:8]}"
            test_start_time = datetime.now()
            LogFire.log("DEBUG", f"[{self.test_name}] PHASE 0: [OK] Test config: Subject='{test_email_subject}', Start={test_start_time}")
        
            # Step 1: Verify IMAP IDLE scheduled request exists
            LogFire.log("DEBUG", f"[{self.test_name}] ===== STEP 1: IMAP IDLE VERIFICATION =====")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: Getting SystemUserManager instance...")
            system_user_manager = SystemUserManager.get_instance()
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: [OK] SystemUserManager obtained")
            
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: Getting SYSTEM user...")
            system_user = await system_user_manager.get_system_user()
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: System user result: {system_user is not None}")
            
            if not system_user:
                LogFire.log("DEBUG", f"[{self.test_name}] ERROR: SYSTEM user not found!")
                pytest.fail("SYSTEM user not found")
            
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: [OK] SYSTEM user found: {system_user.username}")
            
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: Getting ScheduledRequestPersistenceManager...")
            persistence_manager = ScheduledRequestPersistenceManager.get_instance()
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: [OK] PersistenceManager obtained")
            
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: Querying active requests for SYSTEM user GUID: {system_user_manager.SYSTEM_USER_GUID}")
            existing_requests = await persistence_manager.get_active_requests(str(system_user_manager.SYSTEM_USER_GUID))
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: Found {len(existing_requests)} active requests")
            
            # Log all existing requests for debugging
            for i, request in enumerate(existing_requests):
                target_prompt = request.get('target_prompt', 'No target_prompt')
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: Request {i+1}: target_prompt='{target_prompt}'")
            
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: Checking for IMAP-related requests...")
            imap_task_exists = any(
                task.get('target_prompt') and "imap" in task.get('target_prompt', '').lower()
                for task in existing_requests
            )
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: IMAP task exists: {imap_task_exists}")
            
            if not imap_task_exists:
                LogFire.log("DEBUG", f"[{self.test_name}] ERROR: No IMAP idle scheduled request found!")
                LogFire.log("DEBUG", f"[{self.test_name}] ERROR: Available requests: {[r.get('target_prompt', 'N/A') for r in existing_requests]}")
                pytest.fail("IMAP idle scheduled request must exist")
            
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1: [OK] IMAP idle request exists and is active")
        
            # Step 1.5: Wait for IMAP to fully activate
            LogFire.log("DEBUG", f"[{self.test_name}] ===== STEP 1.5: IMAP ACTIVATION WAIT =====")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1.5: About to wait 30 seconds for IMAP to fully activate...")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1.5: During this wait, background IMAP tasks should be running")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1.5: Look for 'imap_idle_activate' logs in the output")
            
            # Wait in smaller chunks with progress logging
            for second in range(30):
                await asyncio.sleep(1)
                if second % 5 == 0 or second == 29:  # Log every 5 seconds and at the end
                    LogFire.log("DEBUG", f"[{self.test_name}] STEP 1.5: IMAP wait progress: {second+1}/30 seconds elapsed")
            
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1.5: [OK] IMAP activation wait complete - IMAP should now be active")
        
            # Step 2: Clear broadcast history and send email
            LogFire.log("DEBUG", f"[{self.test_name}] ===== STEP 2: EMAIL SENDING =====")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: Clearing broadcast history...")
            
            # Clear any existing broadcast history before sending email
            bot.clear_broadcast_history()
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: [OK] Broadcast history cleared")
            
            query = f"send a test <NAME_EMAIL> with subject '{test_email_subject}'"
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: Preparing email query: '{query}'")
        
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: About to call user.on_message() for email sending...")
            task_result = None
            try:
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: Calling user.on_message()...")
                task_result = await user.on_message(
                    complete_message=query,
                    calling_bot=bot,
                    attachments=[],
                    original_message=None
                )
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: user.on_message() completed successfully")
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: Task result type: {type(task_result)}, Value: {task_result}")
            except Exception as e:
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: ERROR in user.on_message(): {e}")
                import traceback
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: Full traceback: {traceback.format_exc()}")
            
            email_sent = task_result is not None
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: Email sending result - Success: {email_sent}")
            
            if not email_sent:
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: WARNING: Email task was not created successfully")
            else:
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 2: [OK] Email task created successfully")
        
            # Step 3: Wait for second email generator execution (when IMAP processes the received email)
            LogFire.log("DEBUG", f"[{self.test_name}] ===== STEP 3: EMAIL GENERATOR EXECUTION DETECTION =====")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Setting up email generator execution monitoring...")
        
            # Create email generator execution assertion function using LogFire manager
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Creating email generator execution assertion function...")
            async def email_generator_execution_assertion():
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Checking for email_generator_tool executions since {test_start_time}...")
                
                # Use LogFire manager to search for email_generator_tool executions
                # Search for the tool invocation pattern that appears when LLM calls the tool
                search_result = await LogFire.search_log_entries(
                    search_pattern="%Adding state to tool email_generator_tool%",
                    start_time=test_start_time,
                    min_count=2
                )
                
                execution_count = search_result.get("execution_count", 0)
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Found {execution_count} email_generator_tool executions")
                
                if "error" in search_result:
                    LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Error from LogFire search: {search_result['error']}")
                    return {
                        "passed": False,
                        "message": f"Error searching log entries: {search_result['error']}",
                        "data": search_result
                    }
                
                # We need at least 2 executions: 1 for outbound email, 1 for processing received email
                success = search_result.get("meets_minimum", False)
                
                return {
                    "passed": success,
                    "message": f"Email generator executions: {execution_count} found (need >= 2)",
                    "data": {
                        "execution_count": execution_count,
                        "latest_execution": search_result.get("latest_execution"),
                        "required_count": 2
                    }
                }
        
            # Wait for second email generator execution (30-second intervals)
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Starting email generator execution polling (8 minutes timeout, 30-second intervals)...")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Looking for >= 2 executions: 1st for outbound email, 2nd for processing received email")
            execution_result = await self.wait_for_assertions(
                assertion_func=email_generator_execution_assertion,
                timeout=480,  # 8 minutes
                poll_interval=30,  # 30 seconds for dashboard monitoring
                assertion_name="email_generator_execution_detection"
            )
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Email generator execution polling completed")
        
            second_execution_detected = execution_result["success"]
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Second execution detection result: {second_execution_detected}")
            
            if second_execution_detected:
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: [OK] Second email generator execution detected after {execution_result['elapsed_time']:.1f}s ({execution_result['poll_count']} polls)")
                # Log details about the detected executions
                final_data = execution_result.get('final_data', {})
                execution_count = final_data.get('execution_count', 0)
                latest_execution = final_data.get('latest_execution')
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Total email generator executions: {execution_count}")
                if latest_execution:
                    LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Latest execution timestamp: {latest_execution}")
            else:
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: WARNING: Second email generator execution not detected within timeout")
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Final message: {execution_result['final_message']}")
                final_data = execution_result.get('final_data', {})
                execution_count = final_data.get('execution_count', 0)
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 3: Final execution count: {execution_count} (needed >= 2)")
        
            # Step 4: HITL Response and Second Email Generation
            LogFire.log("DEBUG", f"[{self.test_name}] ===== STEP 4: HITL RESPONSE =====")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: Setting up HITL response monitoring...")
            
            hitl_responded = False
            third_execution_detected = False
            
            if second_execution_detected:
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: Second execution confirmed - looking for HITL request...")
                
                # Create HITL response assertion function
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: Creating HITL response assertion function...")
                async def hitl_response_assertion():
                    LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: Checking for active HITL request...")
                    
                    # Check for HITL request
                    hitl_request = user.get_active_hitl_request()
                    if hitl_request:
                        LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: Found HITL request: {hitl_request.scheduled_guid}")
                        
                        # Check if it has callback
                        if hasattr(hitl_request, 'human_in_the_loop_callback') and hitl_request.human_in_the_loop_callback is not None:
                            LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: HITL callback exists - responding with approval...")
                            
                            try:
                                # Respond with approval using the testing bot
                                await user.on_message(
                                    complete_message="ja",  # Approve the email
                                    calling_bot=bot,
                                    attachments=[],
                                    original_message=None
                                )
                                LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: HITL approval response sent successfully")
                                return {
                                    "passed": True,
                                    "message": "HITL request found and approval response sent",
                                    "data": {"hitl_guid": hitl_request.scheduled_guid}
                                }
                            except Exception as e:
                                LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: Error responding to HITL: {e}")
                                return {
                                    "passed": False,
                                    "message": f"Error responding to HITL: {str(e)}",
                                    "data": {"error": str(e)}
                                }
                        else:
                            LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: HITL request found but no callback available")
                            return {
                                "passed": False,
                                "message": "HITL request found but no callback available",
                                "data": {"hitl_guid": hitl_request.scheduled_guid}
                            }
                    else:
                        LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: No active HITL request found yet")
                        return {
                            "passed": False,
                            "message": "HITL request not found yet",
                            "data": {}
                        }
                
                # Wait for HITL response opportunity (2 minutes timeout, 10 second intervals)
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: Starting HITL response polling (2 minutes timeout, 10-second intervals)...")
                hitl_result = await self.wait_for_assertions(
                    assertion_func=hitl_response_assertion,
                    timeout=120,  # 2 minutes to find and respond to HITL
                    poll_interval=10,  # 10 seconds for quick HITL detection
                    assertion_name="hitl_response"
                )
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: HITL response polling completed")
                
                hitl_responded = hitl_result["success"]
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: HITL response result: {hitl_responded}")
                
                if hitl_responded:
                    LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: [OK] HITL request found and approval sent after {hitl_result['elapsed_time']:.1f}s")
                    
                    # Now wait for third email generator execution (second email sending)
                    LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: Waiting for third email generator execution (second email sending)...")
                    
                    # Create third execution assertion function  
                    async def third_execution_assertion():
                        LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: Checking for >= 3 email_generator_tool executions...")
                        
                        search_result = await LogFire.search_log_entries(
                            search_pattern="%Adding state to tool email_generator_tool%",
                            start_time=test_start_time,
                            min_count=3
                        )
                        
                        execution_count = search_result.get("execution_count", 0)
                        LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: Found {execution_count} email_generator_tool executions")
                        
                        if "error" in search_result:
                            return {
                                "passed": False,
                                "message": f"Error searching log entries: {search_result['error']}",
                                "data": search_result
                            }
                        
                        success = search_result.get("meets_minimum", False)
                        
                        return {
                            "passed": success,
                            "message": f"Email generator executions: {execution_count} found (need >= 3)",
                            "data": {
                                "execution_count": execution_count,
                                "latest_execution": search_result.get("latest_execution"),
                                "required_count": 3
                            }
                        }
                    
                    # Wait for third execution (4 minutes timeout)
                    third_result = await self.wait_for_assertions(
                        assertion_func=third_execution_assertion,
                        timeout=240,  # 4 minutes for second email to be sent
                        poll_interval=15,  # 15 seconds for email sending monitoring
                        assertion_name="third_execution_detection"
                    )
                    
                    third_execution_detected = third_result["success"]
                    LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: Third execution detection result: {third_execution_detected}")
                    
                    if third_execution_detected:
                        LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: [OK] Second email sent successfully after {third_result['elapsed_time']:.1f}s")
                        third_data = third_result.get('final_data', {})
                        third_count = third_data.get('execution_count', 0)
                        LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: Total executions after second email: {third_count}")
                    else:
                        LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: WARNING: Third execution (second email) not detected within timeout")
                        LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: Third execution message: {third_result['final_message']}")
                        
                else:
                    LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: WARNING: HITL request not found or response failed")
                    LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: HITL result message: {hitl_result['final_message']}")
                    
            else:
                LogFire.log("DEBUG", f"[{self.test_name}] STEP 4: SKIPPED - Second execution not detected, cannot proceed with HITL response")
                hitl_responded = False
                third_execution_detected = False
        
            # Summary
            LogFire.log("DEBUG", f"[{self.test_name}] ===== FINAL TEST SUMMARY =====")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1 - IMAP exists: [OK]")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 1.5 - IMAP activation wait: [OK]")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 2 - Email sent: {'[OK]' if email_sent else '[ERROR]'}")
            LogFire.log("DEBUG", f"[{self.test_name}] STEP 3 - Second email generator execution: {'[OK]' if second_execution_detected else '[ERROR]'}")
            final_data = execution_result.get('final_data', {})
            final_execution_count = final_data.get('execution_count', 0)
            LogFire.log("DEBUG", f"[{self.test_name}] Final email generator execution count: {final_execution_count}")
        
            # Assertions
            LogFire.log("DEBUG", f"[{self.test_name}] ===== RUNNING FINAL ASSERTIONS =====")
            
            LogFire.log("DEBUG", f"[{self.test_name}] Assertion 1: IMAP task exists = {imap_task_exists}")
            assert imap_task_exists, "IMAP idle scheduled request must exist"
            
            LogFire.log("DEBUG", f"[{self.test_name}] Assertion 2: Email sent = {email_sent}")
            assert email_sent, "Email sending must succeed"
            
            LogFire.log("DEBUG", f"[{self.test_name}] Assertion 3: Second email generator execution detected = {second_execution_detected}")
            assert second_execution_detected, "Second email generator execution must be detected (indicates IMAP processed received email)"
            
            LogFire.log("DEBUG", f"[{self.test_name}] ===== TEST COMPLETED SUCCESSFULLY =====")
            
        except Exception as e:
            LogFire.log("DEBUG", f"[{self.test_name}] ===== CRITICAL ERROR IN TEST =====")
            LogFire.log("DEBUG", f"[{self.test_name}] Exception: {type(e).__name__}: {e}")
            import traceback
            LogFire.log("DEBUG", f"[{self.test_name}] Full traceback: {traceback.format_exc()}")
            LogFire.log("DEBUG", f"[{self.test_name}] Test failed due to exception - re-raising...")
            raise  # Re-raise to ensure pytest sees the failure
