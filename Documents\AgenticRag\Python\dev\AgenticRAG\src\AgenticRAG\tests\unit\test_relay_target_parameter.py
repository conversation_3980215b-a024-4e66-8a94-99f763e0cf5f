"""
Unit tests for relay_main.py target parameter functionality
Tests both new target parameter feature and backward compatibility
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import Mock, AsyncMock
import json
from aiohttp import web
from tests.unit.test_logging_capture import with_unit_test_logging


class TestRelayTargetParameter:
    """Test target parameter functionality in relay convert endpoints"""
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_convert_endpoint_with_target_parameter(self):
        """Test POST /convert endpoint with target parameter"""
        from endpoints.standalones.relay.relay_main import handle_convert_endpoint
        
        # Test data with target parameter
        test_data = {
            'message': 'hello world',
            'user_id': '123',
            'target': 'https://example.com'
        }
        
        # Create mock request
        mock_request = Mock()
        mock_request.match_info = {'tail': 'test_path'}
        mock_request.headers = {'content-type': 'application/json'}
        mock_request.query = {}
        mock_request.text = AsyncMock()
        mock_request.text.return_value = json.dumps(test_data)
        
        # Call handler
        response = await handle_convert_endpoint(mock_request)
        
        # Verify response
        assert isinstance(response, web.HTTPFound)
        location = response.location
        assert location.startswith('https://example.com/test_path')
        assert 'message=hello+world' in location
        assert 'user_id=123' in location
        assert 'target=' not in location  # Target should be excluded from query params
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_convert_endpoint_without_target_parameter(self):
        """Test POST /convert endpoint without target parameter (backward compatibility)"""
        from endpoints.standalones.relay.relay_main import handle_convert_endpoint
        
        # Test data without target parameter
        test_data = {
            'message': 'hello world',
            'user_id': '123'
        }
        
        # Create mock request with Referer header
        mock_request = Mock()
        mock_request.match_info = {'tail': 'test_path'}
        mock_request.headers = {'content-type': 'application/json', 'Referer': 'https://mysite.com/page'}
        mock_request.query = {}
        mock_request.text = AsyncMock()
        mock_request.text.return_value = json.dumps(test_data)
        
        # Call handler
        response = await handle_convert_endpoint(mock_request)
        
        # Verify response uses existing logic (Referer header)
        assert isinstance(response, web.HTTPFound)
        location = response.location
        assert location.startswith('https://mysite.com/test_path')  # Should use Referer
        assert 'message=hello+world' in location
        assert 'user_id=123' in location
    
    @with_unit_test_logging
    def test_convert_post_endpoint_url_construction_with_target(self):
        """Test URL construction logic for convertpost endpoint with target parameter"""
        # Test the URL construction logic directly without async mocking complexity
        query_params = {
            'message': 'test message',
            'data.field': 'nested_value',
            'target': 'https://api.example.com'
        }
        path = 'api/test'
        
        # Simulate the logic from handle_convert_post_endpoint
        target_base_url = None
        if 'target' in query_params:
            target_base_url = query_params.pop('target')
        
        # Build target URL
        if target_base_url:
            target_base_url = target_base_url.rstrip('/')
            target_url = f"{target_base_url}/{path}"
        else:
            # This would be the fallback logic
            from globals import ZAIRA_PYTHON_PORT
            target_url = f"http://host.docker.internal:{ZAIRA_PYTHON_PORT}/{path}"
        
        # Verify URL construction
        assert target_url == 'https://api.example.com/api/test'
        assert 'target' not in query_params  # Should be removed from query params
    
    @with_unit_test_logging
    def test_convert_post_endpoint_url_construction_without_target(self):
        """Test URL construction logic for convertpost endpoint without target parameter (backward compatibility)"""
        # Test the URL construction logic directly
        query_params = {
            'message': 'test message',
            'data.field': 'nested_value'
        }
        path = 'api/test'
        from globals import ZAIRA_PYTHON_PORT  # Use dynamic port value
        
        # Simulate the logic from handle_convert_post_endpoint
        target_base_url = None
        if 'target' in query_params:
            target_base_url = query_params.pop('target')
        
        # Build target URL
        if target_base_url:
            target_base_url = target_base_url.rstrip('/')
            target_url = f"{target_base_url}/{path}"
        else:
            # This is the existing fallback logic
            target_url = f"http://host.docker.internal:{ZAIRA_PYTHON_PORT}/{path}"
        
        # Verify URL construction uses existing logic
        assert target_url == f'http://host.docker.internal:{ZAIRA_PYTHON_PORT}/api/test'
        assert 'target' not in query_params  # Should not exist anyway
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_target_parameter_url_normalization(self):
        """Test that target URLs are properly normalized (trailing slash removal)"""
        from endpoints.standalones.relay.relay_main import handle_convert_endpoint
        
        # Test data with target parameter having trailing slash
        test_data = {
            'message': 'test',
            'target': 'https://example.com/'  # Note the trailing slash
        }
        
        # Create mock request
        mock_request = Mock()
        mock_request.match_info = {'tail': 'api/endpoint'}
        mock_request.headers = {'content-type': 'application/json'}
        mock_request.query = {}
        mock_request.text = AsyncMock()
        mock_request.text.return_value = json.dumps(test_data)
        
        # Call handler
        response = await handle_convert_endpoint(mock_request)
        
        # Verify response normalizes the URL correctly
        assert isinstance(response, web.HTTPFound)
        location = response.location
        assert location.startswith('https://example.com/api/endpoint')  # Should not have double slash
        assert '//' not in location.replace('https://', '')  # No double slashes after protocol
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_target_parameter_in_query_params_post_endpoint(self):
        """Test target parameter passed via query params in POST request"""
        from endpoints.standalones.relay.relay_main import handle_convert_endpoint
        
        # Test data without target parameter in body
        test_data = {
            'message': 'hello world',
            'user_id': '123'
        }
        
        # Create mock request with target in query params
        mock_request = Mock()
        mock_request.match_info = {'tail': 'test_path'}
        mock_request.headers = {'content-type': 'application/json'}
        mock_request.query = {'target': 'https://query.example.com'}  # Target in query params
        mock_request.text = AsyncMock()
        mock_request.text.return_value = json.dumps(test_data)
        
        # Call handler
        response = await handle_convert_endpoint(mock_request)
        
        # Verify response uses target from query params
        assert isinstance(response, web.HTTPFound)
        location = response.location
        assert location.startswith('https://query.example.com/test_path')
        assert 'message=hello+world' in location
        assert 'user_id=123' in location
        assert 'target=' not in location  # Target should be excluded from final query params