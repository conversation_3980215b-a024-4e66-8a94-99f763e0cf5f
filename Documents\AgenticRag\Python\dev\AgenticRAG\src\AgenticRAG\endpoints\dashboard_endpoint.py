"""
AskZaira Dashboard Endpoint - Comprehensive Multi-Agent AI Assistant Control Panel

## Overview
The AskZaira Dashboard is a sophisticated web-based control panel for managing and monitoring 
a multi-agent AI assistant system built on LangGraph supervisors with LlamaIndex RAG capabilities.
This endpoint provides a unified interface for system administration, user management, scheduled 
task coordination, and real-time monitoring of AI agent activities.

## Architecture
- **Multi-Agent System**: LangGraph supervisors with hierarchical routing and task coordination
- **RAG Pipeline**: LlamaIndex integration with hybrid search capabilities  
- **Communication Channels**: Multiple endpoints (Discord, Teams, WhatsApp, HTTP API)
- **Task Management**: Natural language scheduled task parsing and execution
- **Data Persistence**: PostgreSQL with connection pooling and Qdrant vector storage
- **Authentication**: OAuth 2.0 system with role-based access control

## Dashboard Features

### 1. System Overview
- **Real-time Status Monitoring**: Live system health, database connections, agent status
- **Performance Metrics**: Response times, task completion rates, error tracking
- **Resource Usage**: Memory, CPU, database query performance
- **Multi-Channel Activity**: Discord, Teams, WhatsApp message processing statistics

### 2. Scheduled Request Management
- **Tabbed Organization**: Requests grouped by status (Active, Completed, Failed, Other)
- **Natural Language Parsing**: Human-readable schedule creation ("every Monday at 9am")
- **Detailed Execution Traces**: Call trace and debug message viewing with expandable sections
- **Request Control**: Cancel, modify, reschedule active requests
- **Persistence**: Survive system restarts with database-backed state management

### 3. User Management (Zaira Page)
- **Multi-Tab Interface**: User List, Scheduled Requests, Chat History
- **GUID-Based Operations**: All user operations use secure GUID identifiers
- **Permission Levels**: Role-based access control with user rank management
- **Device Tracking**: Multi-device user session management

### 4. Chat History Analysis
- **Session-Based Organization**: Chat conversations grouped by session ID
- **Message Threading**: Chronological message display with role-based color coding
- **Search & Filter**: Find specific conversations or message content
- **Export Capabilities**: Download chat logs for analysis or compliance

### 5. Advanced Features
- **F5 State Persistence**: Page refresh maintains current view, selected data, and UI state
- **Auto-Refresh System**: Configurable 30-second updates with smart state preservation
- **Debug Access**: Password-protected debug functionality for system diagnostics
- **Responsive Design**: Glassmorphism UI with backdrop blur effects and smooth animations

## Technical Implementation

### Frontend Architecture
- **JavaScript Navigation**: Single-page application with dynamic content loading
- **State Management**: localStorage-based persistence for user preferences and current state
- **Component System**: Reusable TabManager class for consistent tabbed interfaces
- **API Integration**: RESTful endpoints for all dashboard operations

### Backend Integration
- **Admin Interface**: Comprehensive management API for scheduled requests
- **User Manager**: Singleton pattern for efficient user data operations
- **LogFire Integration**: Centralized logging with severity levels and user tracking
- **Error Handling**: Comprehensive exception management with user-friendly error display

### Data Flow
1. **Request Processing**: User interaction → JavaScript navigation → API call → Database query → Response rendering
2. **Real-time Updates**: Auto-refresh → API polling → State comparison → Selective DOM updates
3. **State Persistence**: User action → State capture → localStorage save → Page reload → State restoration

## API Endpoints

### Dashboard Operations
- `GET /cp` - Main dashboard interface
- `GET /dashboard/api/dashboard-overview` - System status and metrics
- `GET /dashboard/api/page-content` - Dynamic page content loading

### User Management  
- `GET /dashboard/api/users` - User list with filtering capabilities
- `GET /dashboard/api/user-requests` - User's scheduled requests
- `GET /dashboard/api/user-chat-history` - User's conversation history

### Request Management
- `POST /dashboard/api/cancel-scheduled-request` - Cancel active scheduled requests
- `GET /dashboard/api/request-details` - Detailed request information with traces

### System Monitoring
- `GET /dashboard/api/system-health` - Health check and performance metrics
- `GET /dashboard/api/agent-status` - Multi-agent system status

## Security Considerations
- **GUID-Only Operations**: No integer IDs exposed to prevent enumeration attacks
- **Role-Based Access**: Admin, user, and guest permission levels
- **Session Management**: Secure session handling with timeout controls
- **Input Validation**: Comprehensive Pydantic model validation
- **Debug Protection**: Password-protected debug features

## Development Patterns

### Adding New Dashboard Sections
1. Create tabbed interface using TabManager class
2. Implement API endpoint following existing patterns
3. Add state persistence support
4. Include auto-refresh compatibility
5. Follow glassmorphism UI design patterns

### Data Display Standards
- **Expandable Content**: Use 0.3s sliding animations for details
- **Color Coding**: Consistent status colors (success: green, error: red, active: blue)
- **Loading States**: Show spinner with descriptive text
- **Empty States**: Provide helpful messaging and suggested actions

### Error Handling
- **User-Friendly Messages**: Convert technical errors to understandable language
- **Graceful Degradation**: Maintain functionality when services are unavailable
- **Debug Information**: Detailed logging for development and troubleshooting
- **Recovery Actions**: Provide users with clear next steps

## Future Extensibility
The dashboard is designed for easy extension with new features:
- **Plugin Architecture**: Add new agent types without core modifications
- **Custom Metrics**: Extensible monitoring system for new data points
- **Integration Points**: RESTful API structure supports external tool integration
- **Theming System**: CSS variable-based styling for white-label customization

This dashboard serves as the central nervous system for the AskZaira multi-agent AI platform,
providing comprehensive oversight, control, and monitoring capabilities in an intuitive,
responsive web interface.
"""

from imports import *

import os
from os import path as os_path
import json
from datetime import datetime, timezone, timedelta
from aiohttp import web
from typing import Dict, List, Any, Optional

from managers.scheduled_requests.admin_interface import get_admin_interface
from managers.scheduled_requests.integration_adapter import get_integration_adapter
from managers.manager_users import ZairaUserManager
from endpoints.dashboard.template_loader import get_dashboard_template_loader
from etc.helper_functions import is_claude_environment, exception_triggered
from managers.manager_logfire import LogFire
# Template loader removed - using inline HTML for better control panel compatibility

# Authentication decorators
def require_authentication(handler):
    """Decorator to require authenticated session"""
    async def wrapper(self, request: web.Request):
        if not request.get('session', {}).get('authenticated', False):
            LogFire.log("USER", "Unauthenticated access attempt to dashboard", severity="warning")
            return web.json_response({'error': 'Authentication required'}, status=401)
        return await handler(self, request)
    return wrapper

def require_system_user(handler):
    """Decorator to require SYSTEM user permissions"""
    async def wrapper(self, request: web.Request):
        session = request.get('session', {})
        if not session.get('authenticated', False):
            return web.json_response({'error': 'Authentication required'}, status=401)
        if not session.get('is_system_user', False):
            LogFire.log("USER", f"Access denied for non-SYSTEM user: {session.get('username', 'unknown')}", severity="warning")
            return web.json_response({'error': 'Insufficient permissions - SYSTEM user required'}, status=403)
        return await handler(self, request)
    return wrapper

def get_user_from_session(request: web.Request) -> Optional[Dict[str, Any]]:
    """Helper to extract user info from session"""
    session = request.get('session', {})
    LogFire.log("DEBUG", f"Raw session data: {session}", severity="debug")
    
    if not session.get('authenticated', False):
        LogFire.log("DEBUG", "Session not authenticated", severity="debug")
        return None
    
    user_data = {
        'user_guid': session.get('user_guid'),
        'username': session.get('username'),
        'is_system_user': session.get('is_system_user', False),
        'rank': session.get('rank', 'NONE')
    }
    LogFire.log("DEBUG", f"Extracted user data: {user_data}", severity="debug")
    return user_data

class DashboardEndpoint:
    """
    Administrative dashboard for multi-agent AI assistant management.
    
    Provides web-based control panel for:
    - System monitoring and health checks
    - Scheduled request management with tabbed organization  
    - User management with GUID-based operations
    - Chat history analysis with session-based display
    - Real-time status updates with state persistence
    """
    
    def __init__(self):
        # Frontend debug logging state tracking to prevent redundant logging
        self._last_frontend_state = {}
        self._frontend_message_count = 0
        # DEBUG_WORKFLOW state tracking to prevent redundant logging
        self._last_workflow_states = {}  # Track last counts per request_guid
        pass
    
    def _check_session_timeout(self, request: web.Request) -> Optional[web.Response]:
        """
        Check for session timeout indicators in request
        
        Returns:
            Response object if session timeout detected, None otherwise
        """
        # Check for session timeout indicators in headers
        user_agent = request.headers.get('User-Agent', '').lower()
        referer = request.headers.get('Referer', '').lower()
        
        # Basic session timeout detection (can be enhanced with actual session management)
        # For now, check if request seems to come from an expired session context
        if 'expired' in referer or 'timeout' in referer or 'login' in referer:
            LogFire.log("ERROR", "Session timeout detected in dashboard request", chat=None)
            return web.json_response({
                'error': 'Session expired. Please refresh the page.',
                'error_type': 'session_timeout',
                'redirect_url': '/dashboard',
                'action_required': 'refresh'
            }, status=401)
        
        return None
    
    def _should_log_workflow_debug(self, request_guid: str, call_trace_count: int, debug_count: int, logging_count: int) -> bool:
        """
        Determine if workflow debug information should be logged to prevent redundancy.
        Only logs when counts actually change or for significant state changes.
        """
        if request_guid not in self._last_workflow_states:
            # First time seeing this request - always log
            self._last_workflow_states[request_guid] = {
                'call_trace': call_trace_count,
                'debug': debug_count,
                'logging': logging_count
            }
            return True
        
        last_state = self._last_workflow_states[request_guid]
        
        # Check if any counts have changed
        if (call_trace_count != last_state['call_trace'] or 
            debug_count != last_state['debug'] or 
            logging_count != last_state['logging']):
            
            # Update tracked state
            self._last_workflow_states[request_guid] = {
                'call_trace': call_trace_count,
                'debug': debug_count,
                'logging': logging_count
            }
            return True
        
        # Counts haven't changed - don't log redundant workflow debug
        return False
    
    async def _fetch_app_data(self, data_type: str, guid: str) -> Optional[Dict[str, Any]]:
        """
        Fetch data from the app using GUIDs
        
        Args:
            data_type: Type of data to fetch ('user', 'request', 'task', etc.)
            guid: The GUID to fetch data for
        
        Returns:
            Dict with the fetched data or None if not found
        """
        try:
            admin = await get_admin_interface()
            
            if data_type == 'user':
                user_manager = ZairaUserManager.get_instance()
                user = await user_manager.find_user(guid)
                if user:
                    return {
                        'user_guid': user.user_guid,
                        'username': user.username,
                        'email': user.email,
                        'rank': user.rank.value if hasattr(user, 'rank') else 'USER',
                        'device_guid': str(user.DeviceGUID)
                    }
            
            elif data_type == 'request':
                request_data = await admin.get_request_details(guid)
                return request_data
            
            elif data_type == 'task':
                # Fetch task-specific data
                adapter = await get_integration_adapter()
                persistence = adapter._persistence
                task_data = await persistence.load_request(guid)
                return task_data
            
            elif data_type == 'quota':
                # Fetch quota information for a user
                user_details = await admin.get_user_details(guid)
                return user_details.get('quota_status')
            
            elif data_type == 'metrics':
                # Fetch metrics for a user
                user_details = await admin.get_user_details(guid)
                return user_details.get('metrics')
            
            return None
            
        except Exception as e:
            # Check for session/authentication errors
            error_msg = str(e).lower()
            if any(indicator in error_msg for indicator in ['unauthorized', 'session', 'expired', 'authentication']):
                LogFire.log("ERROR", f"Session timeout detected while fetching {data_type} data: {str(e)}")
                return {'error_type': 'session_timeout', 'error': 'Session expired'}
            
            LogFire.log("ERROR", f"Failed to fetch {data_type} data for {guid}: {str(e)}")
            return None
    
    @require_authentication
    async def dashboard_home(self, request: web.Request) -> web.Response:
        """Main dashboard page with authentication and role-based access"""
        try:
            # Version validation - must exactly match "1.0"
            # Handle both JSON and form data
            version = ""
            try:
                LogFire.log("DEBUG", f"Dashboard request content-type: '{request.content_type}', method: {request.method}", severity="debug")
                
                if request.content_type and 'application/json' in request.content_type:
                    data = await request.json()
                    version = data.get("version", "")
                    LogFire.log("DEBUG", f"Dashboard received JSON data: {data}", severity="debug")
                else:
                    # This is likely form data
                    data = await request.post()
                    version = data.get("version", "").strip()
                    LogFire.log("DEBUG", f"Dashboard received form data keys: {list(data.keys()) if data else 'None'} (content-type: {request.content_type})", severity="debug")
                    LogFire.log("DEBUG", f"Dashboard version from form: '{version}' (type: {type(version)}, repr: {repr(version)})", severity="debug")
                    
            except Exception as e:
                LogFire.log("ERROR", f"Error parsing dashboard request data: {e}", severity="error")
                return web.Response(text="Invalid request format", status=400)
            
            LogFire.log("DEBUG", f"Dashboard final version check: '{version}' == '1.0' ? {version == '1.0'}", severity="debug")
            if version != "1.0":
                LogFire.log("ERROR", f"Invalid version provided to dashboard: '{version}' (expected '1.0')", severity="error")
                return web.Response(text="Invalid version", status=400)
            
            from etc.helper_functions import create_html_out
            
            # Get user info from session
            user_info = get_user_from_session(request)
            
            # Generate page content placeholder - Overview will be loaded via JavaScript
            try:
                template_loader = get_dashboard_template_loader()
                LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE ACTIVE: Loading welcome.html for dashboard main page", severity="info")
                content = template_loader.load_template('welcome.html')
                LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE SUCCESS: welcome.html loaded successfully", severity="info")
            except Exception:
                # Fallback if template fails
                content = "<p>Loading dashboard...</p>"
            
            # Add user information script to inject permissions
            user_script = f"""
            <script>
                // Set current user information for permission handling
                window.currentUser = {{
                    username: '{user_info.get('username', '') if user_info else ''}',
                    is_system_user: {str(user_info.get('is_system_user', False)).lower() if user_info else 'false'},
                    rank: '{user_info.get('rank', 'NONE') if user_info else 'NONE'}',
                    user_guid: '{user_info.get('user_guid', '') if user_info else ''}'
                }};
                console.log('User permissions loaded:', window.currentUser);
            </script>
            """
            
            # Combine content with user script
            enhanced_content = user_script + content
            
            # Use the new template system
            html = create_html_out("dashboard", enhanced_content)
            
            return web.Response(
                text=html,
                content_type='text/html',
                headers={'Cache-Control': 'no-cache'}
            )
            
        except Exception as e:
            LogFire.log("ERROR", f"Dashboard error: {str(e)}")
            return web.Response(
                text=f"<html><body><h1>Dashboard Error</h1><p>{str(e)}</p></body></html>",
                content_type='text/html',
                status=500
            )
    
    async def serve_static_css(self, request: web.Request) -> web.Response:
        """Serve CSS static files"""
        try:
            filename = request.match_info['filename']
            
            # Security check - only allow CSS files
            if not filename.endswith('.css'):
                raise web.HTTPNotFound()
            
            css_path = os_path.join(os_path.dirname(__file__), 'dashboard', 'static', 'css', filename)
            
            if not os_path.exists(css_path):
                raise web.HTTPNotFound()
            
            with open(css_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return web.Response(
                text=content,
                content_type='text/css',
                headers={'Cache-Control': 'public, max-age=86400'}  # Cache for 1 day
            )
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to serve CSS file {filename}: {str(e)}")
            raise web.HTTPNotFound()
    
    async def serve_static_js(self, request: web.Request) -> web.Response:
        """Serve JavaScript static files"""
        try:
            filename = request.match_info['filename']
            
            # Security check - only allow JS files
            if not filename.endswith('.js'):
                raise web.HTTPNotFound()
            
            js_path = os_path.join(os_path.dirname(__file__), 'dashboard', 'static', 'js', filename)
            
            if not os_path.exists(js_path):
                raise web.HTTPNotFound()
            
            with open(js_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return web.Response(
                text=content,
                content_type='application/javascript',
                headers={'Cache-Control': 'no-cache, no-store, must-revalidate'}  # Disable caching for development
            )
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to serve JS file {filename}: {str(e)}")
            raise web.HTTPNotFound()
    
    async def serve_template(self, request: web.Request) -> web.Response:
        """Serve HTML templates for JavaScript use"""
        try:
            template_name = request.match_info['template_name']
            
            # Validate template name to prevent directory traversal
            if '..' in template_name or '/' in template_name or '\\' in template_name:
                raise web.HTTPNotFound()
            
            # Ensure .html extension
            if not template_name.endswith('.html'):
                template_name += '.html'
            
            from endpoints.dashboard.template_loader import get_dashboard_template_loader
            template_loader = get_dashboard_template_loader()
            
            # Get query parameters for template context
            context = dict(request.query)
            
            # Render template with context if provided
            if context:
                LogFire.log("TEMPLATE_USAGE", f"📄 TEMPLATE ACTIVE: Rendering {template_name} with context", severity="info")
                content = template_loader.render_template(template_name, **context)
                LogFire.log("TEMPLATE_USAGE", f"📄 TEMPLATE SUCCESS: {template_name} rendered successfully", severity="info")
            else:
                LogFire.log("TEMPLATE_USAGE", f"📄 TEMPLATE ACTIVE: Loading {template_name} without context", severity="info")
                content = template_loader.load_template(template_name)
                LogFire.log("TEMPLATE_USAGE", f"📄 TEMPLATE SUCCESS: {template_name} loaded successfully", severity="info")
            
            return web.Response(
                text=content,
                content_type='text/html',
                headers={
                    'Cache-Control': 'public, max-age=300',  # 5 minute cache
                    'Access-Control-Allow-Origin': '*',  # Allow CORS for fetch
                }
            )
            
        except FileNotFoundError:
            raise web.HTTPNotFound()
        except Exception as e:
            LogFire.log("ERROR", f"Failed to serve template {template_name}: {str(e)}")
            raise web.HTTPInternalServerError()
    
    async def api_dashboard_overview(self, request: web.Request) -> web.Response:
        """API endpoint for dashboard overview data"""
        try:
            admin = await get_admin_interface()
            overview = await admin.get_system_overview()
            
            return web.json_response(overview)
            
        except Exception as e:
            # Check for rate limiting indicators
            error_msg = str(e).lower()
            if any(indicator in error_msg for indicator in ['rate limit', 'too many requests', 'quota exceeded', 'throttled']):
                LogFire.log("ERROR", f"Dashboard overview API rate limited: {str(e)}")
                return web.json_response(
                    {
                        'error': 'Rate limit exceeded. Please wait before refreshing.',
                        'error_type': 'rate_limit',
                        'retry_after': 30,
                        'fallback_data': {
                            'system_health': {'overall_status': 'rate_limited'},
                            'factory_metrics': {'active_managers': 0, 'total_requests': 0, 'total_active_tasks': 0},
                            'performance_metrics': {'timestamp': 'Rate limited'}
                        }
                    },
                    status=429
                )
            
            LogFire.log("ERROR", f"Dashboard overview API error: {str(e)}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )
    
    async def api_system_health(self, request: web.Request) -> web.Response:
        """API endpoint for system health data (for header display)"""
        try:
            admin = await get_admin_interface()
            overview = await admin.get_system_overview()
            
            # Extract key health metrics for header display
            system_health = overview.get('system_health', {})
            performance_metrics = overview.get('performance_metrics', {})
            
            # Create a concise status summary for header
            status = {
                'database': system_health.get('database_status', 'Unknown'),
                'vector_db': system_health.get('vector_database_status', 'Unknown'),
                'active_tasks': performance_metrics.get('active_tasks', 0),
                'total_requests': performance_metrics.get('total_requests', 0),
                'system_load': performance_metrics.get('system_load', 'Normal'),
                'uptime': system_health.get('uptime', '0h 0m'),
                'timestamp': overview.get('timestamp', '')
            }
            
            return web.json_response(status)
            
        except Exception as e:
            # Check for rate limiting indicators
            error_msg = str(e).lower()
            if any(indicator in error_msg for indicator in ['rate limit', 'too many requests', 'quota exceeded', 'throttled']):
                LogFire.log("ERROR", f"System health API rate limited: {str(e)}")
                return web.json_response({
                    'database': 'Rate Limited',
                    'vector_db': 'Rate Limited', 
                    'active_tasks': 0,
                    'total_requests': 0,
                    'system_load': 'Rate Limited',
                    'uptime': 'Rate Limited',
                    'error': 'Rate limit exceeded',
                    'error_type': 'rate_limit',
                    'retry_after': 30
                }, status=429)
            
            LogFire.log("ERROR", f"System health API error: {str(e)}")
            return web.json_response({
                'database': 'Error',
                'vector_db': 'Error', 
                'active_tasks': 0,
                'total_requests': 0,
                'system_load': 'Error',
                'uptime': 'Error',
                'error': str(e)
            }, status=500)
    
    async def api_user_management(self, request: web.Request) -> web.Response:
        """API endpoint for user management data"""
        try:
            # For now, return basic structure - the actual user management
            # functions will be handled by existing JavaScript functions
            return web.json_response({
                'status': 'ready',
                'message': 'User management interface ready'
            })
            
        except Exception as e:
            LogFire.log("ERROR", f"User management API error: {str(e)}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )
    
    async def api_page_content(self, request: web.Request) -> web.Response:
        """API endpoint for dynamic page content loading"""
        try:
            page = request.query.get('page', 'overview')
            # COMPREHENSIVE API LOGGING: Track all page routing requests
            LogFire.log("API_CALL", f"🌐 API ACTIVE: /dashboard/api/page-content called for page='{page}'", severity="info")
            LogFire.log("FUNCTION_CALL", f"api_page_content() called for page: {page}")
            LogFire.log("RETRIEVE", f"Loading page content for: {page}", chat=None)
            
            if page == 'profile':
                return await self._get_profile_content(request)
            elif page == 'overview':
                return await self._get_overview_content(request)
            elif page == 'zaira':
                return await self._get_zaira_content(request)
            elif page == 'account':
                return await self._get_account_content(request)
            elif page == 'system':
                return await self._get_system_content(request)
            elif page == 'subscription':
                return await self._get_subscription_content(request)
            elif page == 'help':
                return await self._get_help_content(request)
            else:
                LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE ACTIVE: Loading empty_state.html for page not found", severity="info")
                template_loader = get_dashboard_template_loader()
                html_content = template_loader.render_template('empty_state.html', title='Page Not Found', message=f'The page "{page}" is not implemented yet.')
                LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE SUCCESS: empty_state.html rendered for page not found", severity="info")
                LogFire.log("API_CALL", f"🌐 API SUCCESS: page '{page}' content delivered (page not found)", severity="warning")
                return web.json_response({'html': html_content})
                
        except Exception as e:
            LogFire.log("API_CALL", f"🌐 API ERROR: /dashboard/api/page-content failed for page='{page}'", severity="error")
            LogFire.log("ERROR", f"Page content API error: {str(e)}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )
    
    async def _get_profile_content(self, request: web.Request) -> web.Response:
        """Generate profile page content"""
        # COMPREHENSIVE LOGGING: Track profile page requests
        LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_profile_content() ENTRY - generating profile page", severity="info")
        LogFire.log("FUNCTION_CALL", "_get_profile_content() called")
        try:
            # Get current user (simplified - in real implementation would come from session/auth)
            user_guid = request.query.get('user_guid', 'system-user')
            
            from managers.manager_users import ZairaUserManager
            user_manager = ZairaUserManager.get_instance()
            user = await user_manager.find_user(user_guid)
            
            if not user:
                # Use SYSTEM user as fallback
                from managers.manager_system_user import SystemUserManager
                system_manager = SystemUserManager.get_instance()
                user = await system_manager.get_system_user()
                
                if not user:
                    # If SYSTEM user doesn't exist, create it
                    await SystemUserManager.setup()
                    user = await system_manager.get_system_user()
                
                # Set profile information for SYSTEM user
                if user:
                    user.first_name = "System"
                    user.last_name = "Administrator"
                    user.email = "<EMAIL>"
                    user.job_title = "System Administrator"
                    user.company = "AskZaira System"
                    user.personal_prompt = "I manage system operations and provide administrative oversight."
            
            # Generate user initials for avatar
            initials = self._get_user_initials(user)
            display_name = f"{user.first_name} {user.last_name}".strip() or user.username or "User"
            
            # Build profile page HTML with inline styles and functionality
            profile_html = f"""
            <div class="profile-container">
                <div class="profile-header">
                    <div class="profile-avatar">
                        <span class="avatar-initials">{initials}</span>
                    </div>
                    <h1 class="profile-name">{display_name}</h1>
                    <p class="profile-subtitle">{user.job_title or 'No title set'}</p>
                    <div class="status-badge {'active' if user.has_active_requests() else 'inactive'}">
                        {'Active' if user.has_active_requests() else 'Inactive'}
                    </div>
                </div>
                
                <div class="profile-stats">
                    <div class="stat-card">
                        <div class="stat-value">{len(user.my_requests)}</div>
                        <div class="stat-label">Total Requests</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">{len(user.chat_history)}</div>
                        <div class="stat-label">Chat Sessions</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">{user.rank.name}</div>
                        <div class="stat-label">Permission Level</div>
                    </div>
                    <div class="stat-card">
                        <div class="stat-value">Just now</div>
                        <div class="stat-label">Last Activity</div>
                    </div>
                </div>
                
                <div class="profile-form card">
                    <h3 class="card-title">Personal Information</h3>
                    <form id="profileForm" onsubmit="updateProfile(event)">
                        <div class="form-row">
                            <div class="form-group">
                                <label class="form-label">First Name</label>
                                <input type="text" id="first_name" value="{user.first_name}" class="form-input" required>
                            </div>
                            <div class="form-group">
                                <label class="form-label">Last Name</label>
                                <input type="text" id="last_name" value="{user.last_name}" class="form-input" required>
                            </div>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Email Address</label>
                            <input type="email" id="email" value="{user.email}" class="form-input" required>
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Job Title</label>
                            <input type="text" id="job_title" value="{user.job_title}" class="form-input" placeholder="Enter your job title">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Company</label>
                            <input type="text" id="company" value="{user.company}" class="form-input" placeholder="Enter your company">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Personal AI Prompt</label>
                            <textarea id="personal_prompt" class="form-input" rows="4" 
                                placeholder="Enter instructions for the AI assistant to customize responses to your preferences...">{user.personal_prompt}</textarea>
                            <small style="color: var(--dashboard-text-secondary); margin-top: 8px; display: block; font-size: 0.8rem;">
                                This prompt helps the AI understand your communication style and preferences.
                            </small>
                        </div>
                        
                        <div class="form-actions">
                            <button type="button" class="btn btn-secondary" onclick="resetProfileForm()">Reset</button>
                            <button type="submit" class="btn btn-primary">Save Profile</button>
                        </div>
                    </form>
                </div>
            </div>
            """
            
            LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_profile_content() SUCCESS - profile page generated successfully", severity="info")
            return web.json_response({'html': profile_html})
            
        except Exception as e:
            LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_profile_content() ERROR - profile generation failed", severity="error")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "Profile content generation", None)
            try:
                template_loader = get_dashboard_template_loader()
                LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE ACTIVE: Loading error.html for profile error", severity="warning")
                error_html = template_loader.render_template('error.html', error_message='Failed to load profile content. Please try again.')
                LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE SUCCESS: error.html rendered for profile error", severity="warning")
            except:
                error_html = 'Failed to load profile content. Please try again.'
            return web.json_response({'html': error_html})
    
    async def _get_overview_content(self, request: web.Request) -> web.Response:
        """Signal to use existing overview implementation"""
        # COMPREHENSIVE LOGGING: Track overview page requests
        LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_overview_content() ENTRY - redirecting to overview page", severity="info")
        LogFire.log("FUNCTION_CALL", "_get_overview_content() called")
        LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_overview_content() SUCCESS - redirect signal sent", severity="info")
        return web.json_response({
            'redirect': 'overview'  # Signal to use existing overview implementation
        })
    
    async def _get_zaira_content(self, request: web.Request) -> web.Response:
        """Signal to use existing zaira implementation"""
        # COMPREHENSIVE LOGGING: Track zaira page requests
        LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_zaira_content() ENTRY - redirecting to zaira page", severity="info")
        LogFire.log("FUNCTION_CALL", "_get_zaira_content() called")
        LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_zaira_content() SUCCESS - redirect signal sent", severity="info")
        return web.json_response({
            'redirect': 'zaira'  # Signal to use existing zaira implementation
        })
    
    async def _get_account_content(self, request: web.Request) -> web.Response:
        """Generate account page content"""
        # COMPREHENSIVE LOGGING: Track account page requests
        LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_account_content() ENTRY - generating account page", severity="info")
        LogFire.log("FUNCTION_CALL", "_get_account_content() called")
        try:
            # Get current user (use SYSTEM user as fallback)
            user_guid = request.query.get('user_guid', 'system-user')
            
            from managers.manager_users import ZairaUserManager
            user_manager = ZairaUserManager.get_instance()
            user = await user_manager.find_user(user_guid)
            
            if not user:
                # Use SYSTEM user as fallback
                from managers.manager_system_user import SystemUserManager
                system_manager = SystemUserManager.get_instance()
                user = await system_manager.get_system_user()
                
                if not user:
                    # If SYSTEM user doesn't exist, create it
                    await SystemUserManager.setup()
                    user = await system_manager.get_system_user()
                
                # Set account settings for SYSTEM user
                if user:
                    user.first_name = "System"
                    user.last_name = "Administrator"
                    user.email = "<EMAIL>"
                    user.company = "AskZaira System"
                    user.company_domain = "askzaira.nl"
                    user.zaira_voice = "professional"
                    user.enable_followup_questions = True
                    user.chat_history_interval = "weekly"
                    user.two_factor_enabled = False
            
            # Generate user initials for avatar
            initials = self._get_user_initials(user)
            display_name = f"{user.first_name} {user.last_name}".strip() or user.username or "User"
            is_system = user.is_system_user if hasattr(user, 'is_system_user') else (user_guid == 'system-user' or user.username == 'SYSTEM')
            
            # Language options
            lang_options = {
                'en': 'English',
                'nl': 'Dutch',
                'fr': 'French', 
                'de': 'German',
                'es': 'Spanish'
            }
            
            # Voice options
            voice_options = {
                'default': 'Default Voice',
                'professional': 'Professional',
                'friendly': 'Friendly',
                'concise': 'Concise',
                'detailed': 'Detailed'
            }
            
            # Chat history interval options
            interval_options = {
                'never': 'Never Clear',
                'daily': 'Clear Daily', 
                'weekly': 'Clear Weekly',
                'monthly': 'Clear Monthly'
            }
            
            # Prepare template context
            from endpoints.dashboard.template_loader import get_dashboard_template_loader
            template_loader = get_dashboard_template_loader()
            
            # Build language options HTML
            language_options = ''.join([
                f'<option value="{code}" {"selected" if user.preferred_language == code else ""}>{name}</option>'
                for code, name in lang_options.items()
            ])
            
            # Build voice options HTML
            voice_options_html = ''.join([
                f'<option value="{code}" {"selected" if user.zaira_voice == code else ""}>{name}</option>'
                for code, name in voice_options.items()
            ])
            
            # Build interval options HTML
            interval_options_html = ''.join([
                f'<option value="{code}" {"selected" if user.chat_history_interval == code else ""}>{name}</option>'
                for code, name in interval_options.items()
            ])
            
            # Build company section HTML (conditional)
            company_section = ''
            if is_system:
                company_section = f"""
                <div class="settings-card card">
                    <h3 class="card-title">Company Information</h3>
                    <form id="companyForm" onsubmit="updateCompanyInfo(event)">
                        <div class="form-group">
                            <label class="form-label">Company Name</label>
                            <input type="text" id="company_name" value="{user.company}" class="form-input" placeholder="Enter company name">
                        </div>
                        
                        <div class="form-group">
                            <label class="form-label">Company Domain</label>
                            <input type="text" id="company_domain" value="{user.company_domain}" class="form-input" placeholder="example.com">
                            <small class="form-help">Primary domain for system operations and email communications.</small>
                        </div>
                        
                        <div class="form-actions">
                            <button type="submit" class="btn btn-primary">Update Company Info</button>
                        </div>
                    </form>
                </div>
                """
            else:
                company_section = '<!-- Company section hidden for non-system users -->'
            
            # Prepare template context
            context = {
                'initials': initials,
                'display_name': display_name,
                'status_badge_class': 'system' if is_system else 'active',
                'status_badge_text': 'System Account' if is_system else 'User Account',
                'language_options': language_options,
                'voice_options': voice_options_html,
                'followup_checked': 'checked' if user.enable_followup_questions else '',
                'company_section': company_section,
                'interval_options': interval_options_html,
                'two_factor_checked': 'checked' if user.two_factor_enabled else ''
            }
            
            # Render template
            LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE ACTIVE: Loading account.html for account page", severity="info")
            account_html = template_loader.render_template('account.html', **context)
            LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE SUCCESS: account.html rendered successfully", severity="info")
            
            LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_account_content() SUCCESS - account page generated successfully", severity="info")
            return web.json_response({'html': account_html})
            
        except Exception as e:
            LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_account_content() ERROR - account generation failed", severity="error")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "Account content generation", None)
            try:
                template_loader = get_dashboard_template_loader()
                LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE ACTIVE: Loading error.html for account error", severity="warning")
                error_html = template_loader.render_template('error.html', error_message='Failed to load account content. Please try again.')
                LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE SUCCESS: error.html rendered for account error", severity="warning")
            except:
                error_html = 'Failed to load account content. Please try again.'
            return web.json_response({'html': error_html})
    
    async def _get_system_content(self, request: web.Request) -> web.Response:
        """Generate system information page content"""
        # COMPREHENSIVE LOGGING: Track system page requests
        LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_system_content() ENTRY - generating system page", severity="info")
        LogFire.log("FUNCTION_CALL", "_get_system_content() called")
        try:
            # Get current user (use SYSTEM user as fallback for system info)
            user_guid = request.query.get('user_guid', 'system-user')
            
            from managers.manager_users import ZairaUserManager
            user_manager = ZairaUserManager.get_instance()
            user = await user_manager.find_user(user_guid)
            
            if not user:
                # Use SYSTEM user as fallback
                from managers.manager_system_user import SystemUserManager
                system_manager = SystemUserManager.get_instance()
                user = await system_manager.get_system_user()
                
                if not user:
                    # If SYSTEM user doesn't exist, create it
                    await SystemUserManager.setup()
                    user = await system_manager.get_system_user()
                
                # Set system information for SYSTEM user
                if user:
                    user.tokens_used = 125_000  # Example usage
                    user.allow_document_generation = True
                    user.gb_remaining = 85.7
                    user.data_location = "Local Server - Docker Container"
                    user.active_triggers = 12
                    user.disabled_triggers = 3
                    user.total_connectors = 8
                    user.total_automations = 15
                    user.organization_id = "askzaira-system-001"
                    user.privacy_level = "Enterprise"
            
            # Generate system initials/icon
            initials = self._get_user_initials(user)
            display_name = f"{user.first_name} {user.last_name}".strip() or user.username or "System"
            
            # Calculate system metrics
            total_triggers = user.active_triggers + user.disabled_triggers
            storage_used = 100.0 - user.gb_remaining
            storage_percentage = (storage_used / 100.0) * 100
            
            # Privacy level options
            privacy_options = {
                'basic': 'Basic',
                'standard': 'Standard',
                'enterprise': 'Enterprise',
                'maximum': 'Maximum Security'
            }
            
            # Data location options
            location_options = {
                'local': 'Local Server',
                'cloud': 'Cloud Storage',
                'hybrid': 'Hybrid Storage',
                'distributed': 'Distributed Network'
            }
            
            # Prepare template context
            from endpoints.dashboard.template_loader import get_dashboard_template_loader
            template_loader = get_dashboard_template_loader()
            
            # Build location options HTML
            location_options_html = ''.join([
                f'<option value="{code}" {"selected" if user.data_location.lower().startswith(code) else ""}>{name}</option>'
                for code, name in location_options.items()
            ])
            
            # Build privacy options HTML
            privacy_options_html = ''.join([
                f'<option value="{code}" {"selected" if user.privacy_level.lower() == code else ""}>{name}</option>'
                for code, name in privacy_options.items()
            ])
            
            # Prepare template context
            context = {
                'initials': initials,
                'tokens_used': f"{user.tokens_used:,}",
                'storage_used': f"{storage_used:.1f}",
                'gb_remaining': f"{user.gb_remaining:.1f}",
                'storage_percentage': f"{storage_percentage:.1f}",
                'document_generation_checked': 'checked' if user.allow_document_generation else '',
                'location_options': location_options_html,
                'current_data_location': user.data_location,
                'organization_id': user.organization_id,
                'privacy_options': privacy_options_html,
                'active_triggers': user.active_triggers,
                'disabled_triggers': user.disabled_triggers,
                'total_connectors': user.total_connectors,
                'total_automations': user.total_automations
            }
            
            # Render template
            LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE ACTIVE: Loading system.html for system page", severity="info")
            system_html = template_loader.render_template('system.html', **context)
            LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE SUCCESS: system.html rendered successfully", severity="info")
            
            LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_system_content() SUCCESS - system page generated successfully", severity="info")
            return web.json_response({'html': system_html})
            
        except Exception as e:
            LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_system_content() ERROR - system generation failed", severity="error")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "System content generation", None)
            try:
                template_loader = get_dashboard_template_loader()
                LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE ACTIVE: Loading error.html for system error", severity="warning")
                error_html = template_loader.render_template('error.html', error_message='Failed to load system information. Please try again.')
                LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE SUCCESS: error.html rendered for system error", severity="warning")
            except:
                error_html = 'Failed to load system information. Please try again.'
            return web.json_response({'html': error_html})
    
    async def _get_subscription_content(self, request: web.Request) -> web.Response:
        """Generate subscription page content"""
        # COMPREHENSIVE LOGGING: Track subscription page requests
        LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_subscription_content() ENTRY - generating subscription page", severity="info")
        LogFire.log("FUNCTION_CALL", "_get_subscription_content() called")
        try:
            # Get current user (use SYSTEM user as fallback)
            user_guid = request.query.get('user_guid', 'system-user')
            
            from managers.manager_users import ZairaUserManager
            user_manager = ZairaUserManager.get_instance()
            user = await user_manager.find_user(user_guid)
            
            if not user:
                # Use SYSTEM user as fallback
                from managers.manager_system_user import SystemUserManager
                system_manager = SystemUserManager.get_instance()
                user = await system_manager.get_system_user()
                
                if not user:
                    # If SYSTEM user doesn't exist, create it
                    await SystemUserManager.setup()
                    user = await system_manager.get_system_user()
                
                # Set subscription information for SYSTEM user
                if user:
                    user.plan_type = "ZairaPlus"
                    user.payment_method = "Credit Card ****4567"
                    user.next_billing_date = "2024-02-28"
                    user.monthly_cost = 99.99
                    user.employee_count = 5
                    user.subscription_status = "Active"
            
            # Generate user initials for avatar
            initials = self._get_user_initials(user)
            display_name = f"{user.first_name} {user.last_name}".strip() or user.username or "User"
            
            # Plan type configurations
            plan_configs = {
                'ZZP': {'name': 'ZZP (Freelancer)', 'price': 29.99, 'max_employees': 1, 'features': ['Basic AI Features', '10GB Storage', 'Email Support']},
                'MKB': {'name': 'MKB (Small Business)', 'price': 69.99, 'max_employees': 10, 'features': ['Advanced AI Features', '50GB Storage', 'Priority Support', 'Team Management']},
                'ZairaPlus': {'name': 'ZairaPlus (Enterprise)', 'price': 199.99, 'max_employees': 100, 'features': ['Full AI Features', 'Unlimited Storage', '24/7 Support', 'Custom Integrations', 'Advanced Analytics']}
            }
            
            current_plan = plan_configs.get(user.plan_type, plan_configs['ZZP'])
            
            # Sample invoice data
            invoices = [
                {'date': '2024-01-15', 'amount': user.monthly_cost, 'status': 'Paid', 'invoice_id': 'INV-2024-001'},
                {'date': '2023-12-15', 'amount': user.monthly_cost, 'status': 'Paid', 'invoice_id': 'INV-2023-012'},
                {'date': '2023-11-15', 'amount': user.monthly_cost, 'status': 'Paid', 'invoice_id': 'INV-2023-011'},
                {'date': '2023-10-15', 'amount': user.monthly_cost, 'status': 'Paid', 'invoice_id': 'INV-2023-010'}
            ]
            
            # Prepare template context
            from endpoints.dashboard.template_loader import get_dashboard_template_loader
            template_loader = get_dashboard_template_loader()
            
            # Build plan features HTML
            plan_features_html = ''.join([
                f'<div class="feature-item">{feature}</div>'
                for feature in current_plan['features']
            ])
            
            # Build invoices HTML
            invoices_html = ''.join([f'''
                <div class="invoice-item">
                    <div class="invoice-date">{invoice['date']}</div>
                    <div class="invoice-amount">€{invoice['amount']:.2f}</div>
                    <div class="invoice-status {'paid' if invoice['status'] == 'Paid' else 'unpaid'}">{invoice['status']}</div>
                    <div class="invoice-actions">
                        <button type="button" class="btn btn-link" onclick="downloadInvoice('{invoice['invoice_id']}')">Download</button>
                    </div>
                </div>
                ''' for invoice in invoices])
            
            # Prepare template context
            context = {
                'initials': initials,
                'display_name': display_name,
                'current_plan_name': current_plan['name'],
                'subscription_status_class': 'active' if user.subscription_status == 'Active' else 'inactive',
                'subscription_status': user.subscription_status,
                'monthly_cost': f"{user.monthly_cost:.2f}",
                'plan_features': plan_features_html,
                'payment_method': user.payment_method,
                'next_billing_date': user.next_billing_date,
                'employee_count': user.employee_count,
                'max_employees': current_plan['max_employees'],
                'invoices_html': invoices_html
            }
            
            # Render template
            LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE ACTIVE: Loading subscription.html for subscription page", severity="info")
            subscription_html = template_loader.render_template('subscription.html', **context)
            LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE SUCCESS: subscription.html rendered successfully", severity="info")
            
            LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_subscription_content() SUCCESS - subscription page generated successfully", severity="info")
            return web.json_response({'html': subscription_html})
            
        except Exception as e:
            LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_subscription_content() ERROR - subscription generation failed", severity="error")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "Subscription content generation", None)
            try:
                template_loader = get_dashboard_template_loader()
                LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE ACTIVE: Loading error.html for subscription error", severity="warning")
                error_html = template_loader.render_template('error.html', error_message='Failed to load subscription information. Please try again.')
                LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE SUCCESS: error.html rendered for subscription error", severity="warning")
            except:
                error_html = 'Failed to load subscription information. Please try again.'
            return web.json_response({'html': error_html})
    
    async def _get_help_content(self, request: web.Request) -> web.Response:
        """Generate help page content"""
        # COMPREHENSIVE LOGGING: Track help page requests
        LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_help_content() ENTRY - generating help page", severity="info")
        LogFire.log("FUNCTION_CALL", "_get_help_content() called")
        try:
            template_loader = get_dashboard_template_loader()
            LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE ACTIVE: Loading help.html for help page", severity="info")
            help_html = template_loader.load_template('help.html')
            LogFire.log("TEMPLATE_USAGE", "📄 TEMPLATE SUCCESS: help.html loaded successfully", severity="info")
            LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_help_content() SUCCESS - help page generated successfully", severity="info")
            return web.json_response({'html': help_html})
        except Exception as e:
            LogFire.log("FUNCTION_CALL", "[ACTIVE] _get_help_content() ERROR - help generation failed", severity="error")
            LogFire.log("ERROR", f"Failed to load help template: {str(e)}")
            return web.json_response({
                'html': 'Failed to load help content. Please try again.'
            })
    
    def _get_user_initials(self, user) -> str:
        """Get user initials for avatar display"""
        try:
            if user.first_name and user.last_name:
                return f"{user.first_name[0]}{user.last_name[0]}".upper()
            elif user.real_name:
                parts = user.real_name.split()
                if len(parts) >= 2:
                    return f"{parts[0][0]}{parts[-1][0]}".upper()
                elif len(parts) == 1:
                    return parts[0][:2].upper()
            elif user.username:
                return user.username[:2].upper()
            else:
                return "U"
        except Exception:
            return "U"
    
    async def api_user_details(self, request: web.Request) -> web.Response:
        """API endpoint for user details"""
        try:
            user_guid = request.query.get('user_guid')
            if not user_guid:
                return web.json_response(
                    {'error': 'user_guid parameter required'},
                    status=400
                )
            
            admin = await get_admin_interface()
            user_details = await admin.get_user_details(user_guid)
            
            return web.json_response(user_details)
            
        except Exception as e:
            LogFire.log("ERROR", f"User details API error: {str(e)}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )
    
    async def api_user_requests(self, request: web.Request) -> web.Response:
        """API endpoint for user's scheduled requests"""
        try:
            user_guid = request.query.get('user_guid')
            if not user_guid:
                LogFire.log("ERROR", "api_user_requests: Missing user_guid parameter", chat=None)
                return web.json_response(
                    {'error': 'user_guid parameter required'},
                    status=400
                )
            
            # Validate GUID format
            import re
            guid_pattern = r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$'
            if not re.match(guid_pattern, user_guid):
                LogFire.log("ERROR", f"api_user_requests: Invalid GUID format: {user_guid}", chat=None)
                return web.json_response(
                    {'error': 'Invalid user_guid format'},
                    status=400
                )
            
            LogFire.log("DEBUG", f"api_user_requests: Looking up user with GUID: {user_guid}", chat=None)
            
            # Get user
            user_manager = ZairaUserManager.get_instance()
            user = await user_manager.find_user(user_guid)
            
            if not user:
                # Enhanced debug logging for user not found
                available_users_count = len(user_manager.users) if hasattr(user_manager, 'users') else 0
                LogFire.log("ERROR", f"api_user_requests: User not found for GUID {user_guid}. Available users: {available_users_count}", chat=None)
                
                # Check if GUID exists in user manager's users dictionary
                if hasattr(user_manager, 'users') and user_guid in user_manager.users:
                    LogFire.log("DEBUG", f"api_user_requests: User found in users dict but find_user failed", chat=None)
                
                return web.json_response(
                    {
                        'error': 'User not found', 
                        'user_guid': user_guid,
                        'available_users': available_users_count
                    },
                    status=404
                )
            
            # Get user's scheduled requests from the scheduled request system
            requests = []
            
            # Use the scheduled request system only
            try:
                # Get scheduled requests from the scheduled request system
                adapter = await get_integration_adapter()
                
                # Get scheduled request GUIDs from the scheduled request system only
                # Note: user.my_requests contains immediate tasks, not scheduled ones
                scheduled_request_guids = await adapter.get_user_scheduled_request_guids(user)
                
                # Fetch full data for each request using GUIDs
                for idx, request_guid in enumerate(scheduled_request_guids):
                    
                    # Get request object from scheduled request system
                    admin = await get_admin_interface()
                    scheduled_request_object = await admin.get_request_object(request_guid, user_guid)
                    
                    # Get data from the ScheduledRequest object if available
                    if scheduled_request_object:
                        # Get additional data from the object
                        full_request = {
                            'scheduled_guid': str(scheduled_request_object.scheduled_guid),
                            'target_prompt': scheduled_request_object.target_prompt,
                            'schedule_prompt': scheduled_request_object.schedule_prompt,
                            'schedule_type': scheduled_request_object.schedule_type.value,
                            'status': 'active',  # For cancel button display
                            'current_status': 'active',
                            'is_running': True,
                            'schedule_info': scheduled_request_object.get_schedule_info(),
                            'chat_session_guid': str(scheduled_request_object.chat_session_guid) if hasattr(scheduled_request_object, 'chat_session_guid') else None,
                            'description': scheduled_request_object.target_prompt or 'No description available',
                            'created_at': scheduled_request_object.created_at.isoformat() if hasattr(scheduled_request_object, 'created_at') else None,
                            'next_run': None  # Will be calculated from schedule_info
                        }
                    else:
                        # Fallback to persistence layer data if object not available
                        full_request = await admin.get_request_details(request_guid)
                        
                        # If none of the methods worked, skip this request
                        if not full_request:
                            continue
                    
                    # Extract call_trace and debug_messages (containing both debug+logging) directly from ZairaChat
                    call_trace = []
                    debug_messages = []
                    
                    # Get call_trace from the scheduled request object
                    if scheduled_request_object:
                        if hasattr(scheduled_request_object, 'call_trace') and scheduled_request_object.call_trace:
                            call_trace = scheduled_request_object.call_trace if isinstance(scheduled_request_object.call_trace, list) else [scheduled_request_object.call_trace]
                        
                        # Extract ALL debug and logging messages directly from the ZairaChat session (same as Chat History)
                        if hasattr(scheduled_request_object, 'chat_session_guid') and scheduled_request_object.chat_session_guid:
                            try:
                                chat_session_guid = scheduled_request_object.chat_session_guid
                                if user.chat_history and chat_session_guid in user.chat_history:
                                    chat_session = user.chat_history[chat_session_guid]
                                    if hasattr(chat_session, 'messages') and chat_session.messages:
                                        # Extract debug and logging messages (same pattern as Chat History uses)
                                        for message in chat_session.messages:
                                            message_role = getattr(message, 'role', None)
                                            role_str = str(message_role).lower() if message_role else ''
                                            
                                            # Use exact same pattern as Chat History: debug OR logging messages
                                            if 'debug' in role_str or 'logging' in role_str:
                                                content = message.content if hasattr(message, 'content') else ''
                                                debug_messages.append({
                                                    'session_id': str(chat_session_guid),
                                                    'timestamp': message.timestamp.isoformat() if hasattr(message, 'timestamp') else None,
                                                    'message_type': getattr(message, 'message_type', 'unknown'),
                                                    'content': content,
                                                    'role': role_str,
                                                    'tokens_used': getattr(message, 'tokens_used', 0),
                                                    'content_length': len(content)
                                                })
                                        
                                        # Conditional logging for workflow debug to prevent redundancy
                            except Exception as e:
                                pass  # Skip if can't extract debug messages
                    
                    # Use the full_request data (already verified to exist above)
                    final_request_data = full_request
                    
                    # Add call_trace and debug_messages (containing both debug+logging) to the final request data
                    final_request_data['call_trace'] = call_trace
                    final_request_data['debug_messages'] = debug_messages
                    
                    
                    requests.append(final_request_data)
            except Exception as e:
                LogFire.log("ERROR", f"Failed to load scheduled requests for user {user_guid}: {str(e)}")
                # Don't fail completely - return empty list with error info
                requests = []
            
            # Note: All scheduled requests now use the unified scheduled request system
            
            return web.json_response({
                'user_guid': user_guid,
                'requests': requests,
                'count': len(requests)
            })
            
        except Exception as e:
            LogFire.log("ERROR", f"User requests API error: {str(e)}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )
    
    async def api_security_report(self, request: web.Request) -> web.Response:
        """API endpoint for security report"""
        try:
            hours = int(request.query.get('hours', 24))
            
            admin = await get_admin_interface()
            report = await admin.get_security_report(hours)
            
            return web.json_response(report)
            
        except Exception as e:
            LogFire.log("ERROR", f"Security report API error: {str(e)}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )
    
    async def api_performance_report(self, request: web.Request) -> web.Response:
        """API endpoint for performance report"""
        try:
            hours = int(request.query.get('hours', 1))
            
            admin = await get_admin_interface()
            report = await admin.get_performance_report(hours)
            
            return web.json_response(report)
            
        except Exception as e:
            LogFire.log("ERROR", f"Performance report API error: {str(e)}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )
    
    async def api_cancel_request(self, request: web.Request) -> web.Response:
        """API endpoint to cancel a scheduled request - accepts only GUIDs"""
        try:
            data = await request.json()
            user_guid = data.get('user_guid')
            scheduled_guid = data.get('scheduled_guid')
            
            # Only accept GUIDs, fetch reason from app data
            if not user_guid or not scheduled_guid:
                return web.json_response(
                    {'error': 'user_guid and scheduled_guid required'},
                    status=400
                )
            
            # Get user from app using GUID
            user_manager = ZairaUserManager.get_instance()
            user = await user_manager.find_user(user_guid)
            
            if not user:
                return web.json_response(
                    {'error': 'User not found'},
                    status=404
                )
            
            # Get reason from app data or use default
            admin = await get_admin_interface()
            app_data = await admin.get_request_details(scheduled_guid)
            reason = app_data.get('cancellation_reason', 'Admin cancellation') if app_data else 'Admin cancellation'
            
            # Cancel request
            adapter = await get_integration_adapter()
            success = await adapter.cancel_scheduled_request(scheduled_guid, user, reason)
            
            if success:
                LogFire.log("ADMIN", f"Admin cancelled request {scheduled_guid} for user {user_guid}", chat=None)
                return web.json_response({
                    'success': True,
                    'message': f'Request {scheduled_guid} cancelled successfully'
                })
            else:
                return web.json_response(
                    {'error': 'Failed to cancel request'},
                    status=500
                )
                
        except Exception as e:
            LogFire.log("ERROR", f"Cancel request API error: {str(e)}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )
    
    async def api_send_message(self, request: web.Request) -> web.Response:
        """API endpoint to send a message to a user through the AI system"""
        try:
            # Removed manual debug_trace.log write - use LogFire.log() instead
            
            LogFire.log("INIT", "Processing send message request", chat=None)
            
            data = await request.json()
            user_guid = data.get('user_guid')
            message = data.get('message')
            
            # Removed manual debug_trace.log write - use LogFire.log() instead
            
            if not user_guid or not message:
                LogFire.log("ERROR", "Missing required parameters for send message", chat=None)
                return web.json_response(
                    {'error': 'user_guid and message required'},
                    status=400
                )
            
            LogFire.log("DEBUG", f"Send message request: user_guid={user_guid}, message_length={len(message)}", chat=None)
                
            # Get user from app using GUID
            from managers.manager_users import ZairaUserManager
            user_manager = ZairaUserManager.get_instance()
            
            # First try direct lookup (same as user list API)
            user = user_manager.users.get(user_guid)
            # Removed manual debug_trace.log write - use LogFire.log() instead
            LogFire.log("DEBUG", f"Direct user lookup for GUID {user_guid}: {'Found' if user else 'Not found'}", chat=None)
            
            # If not found, try find_user method (handles SYSTEM user)
            if not user:
                user = await user_manager.find_user(user_guid)
                # Removed manual debug_trace.log write - use LogFire.log() instead
                LogFire.log("DEBUG", f"find_user lookup for GUID {user_guid}: {'Found' if user else 'Not found'}", chat=None)
            
            # Debug: List all available users for troubleshooting
            if not user:
                available_guids = list(user_manager.users.keys())
                # Removed manual debug_trace.log write - use LogFire.log() instead
                LogFire.log("DEBUG", f"Available user GUIDs: {available_guids[:5]}...", chat=None)  # Show first 5
                LogFire.log("ERROR", f"User not found for GUID: {user_guid}", chat=None)
                return web.json_response(
                    {'error': f'User not found. Available users: {len(available_guids)}'},
                    status=404
                )
            
            LogFire.log("DEBUG", f"Found user: {user.username}", chat=None)
            
            # Send message through the user's on_message method
            try:
                # Create a mock bot instance for the API call
                from endpoints.mybot_generic import MyBot_Generic
                
                # Get or create a generic bot instance for dashboard messages
                # MyBot_Generic requires parent_instance and name parameters
                mock_bot = MyBot_Generic(parent_instance=None, name="Dashboard_API")
                
                LogFire.log("DEBUG", f"Sending message to user {user.username}: {message[:100]}...", chat=None)
                
                # Process the message through the user's standard message handler
                request_task = await user.on_message(message, mock_bot, attachments=[], original_message=None)
                
                LogFire.log("DEBUG", f"Message processing started for user {user.username}, task created: {request_task.scheduled_guid if request_task else 'None'}", chat=None)
                
                # Removed manual debug_trace.log write - use LogFire.log() instead
                
                return web.json_response({
                    'success': True,
                    'user_guid': user_guid,
                    'task_guid': str(request_task.scheduled_guid) if request_task else None,
                    'status': 'Message processing initiated'
                })
                
            except Exception as processing_error:
                LogFire.log("ERROR", f"Message processing error: {str(processing_error)}", chat=None)
                return web.json_response(
                    {'error': f'Message processing failed: {str(processing_error)}'},
                    status=500
                )
                
        except Exception as e:
            LogFire.log("ERROR", f"Send message API error: {str(e)}")
            exception_triggered(e, "api_send_message", None)
            return web.json_response(
                {'error': str(e)},
                status=500
            )
    
    async def api_debug_trace(self, request: web.Request) -> web.Response:
        """API endpoint for frontend debug tracing with state-change detection"""
        try:
            data = await request.json()
            message = data.get('message', 'Unknown debug message')
            
            # State-change detection to reduce redundant logging
            should_log = self._should_log_frontend_debug(message)
            
            if should_log:
                # Removed manual debug_trace.log write - use LogFire.log() instead
                # Use conditional logging to reduce frequency
                LogFire.log_conditionally("DEBUG", f"Frontend debug: {message}", 
                                         condition=should_log, chat=None, severity="debug")
            
            return web.json_response({'success': True})
            
        except Exception as e:
            LogFire.log("ERROR", f"Debug trace API error: {str(e)}")
            exception_triggered(e, "api_debug_trace", None)
            return web.json_response({'error': str(e)}, status=500)
    
    @require_system_user
    async def api_live_logs(self, request: web.Request) -> web.Response:
        """API endpoint for real-time log viewing - SYSTEM users only"""
        try:
            # Get query parameters
            last_position = int(request.query.get('last_position', 0))
            max_lines = int(request.query.get('max_lines', 100))
            format_type = request.query.get('format', 'raw')  # raw or formatted
            filter_type = request.query.get('filter_type', 'debug')  # debug, console, or system
            
            # Get filtering parameters - defaults match UI checkbox states (unchecked = include everything)
            exclude_frontend_debug = request.query.get('exclude_frontend_debug', 'false').lower() == 'true'
            exclude_scrubbed = request.query.get('exclude_scrubbed', 'false').lower() == 'true'
            event_codes_param = request.query.get('event_codes', '')
            event_codes = [code.strip() for code in event_codes_param.split(',') if code.strip()] if event_codes_param else []
            search_term = request.query.get('search_term', None)
            
            filter_options = {
                'exclude_frontend_debug': exclude_frontend_debug,
                'exclude_scrubbed': exclude_scrubbed, 
                'event_codes': event_codes,
                'search_term': search_term
            }
            
            #LogFire.log("DEBUG", f"Live logs request: position={last_position}, max_lines={max_lines}, filter_type={filter_type}, filters={filter_options}", chat=None)
            
            # Debug: Show which file we're reading
            #log_path = await LogFire.get_debug_trace_path()
            #LogFire.log("DEBUG", f"Reading debug trace from: {log_path}", chat=None)
            
            # Use LogFire manager to get log content with unified filtering
            log_data = await LogFire.get_log_tail(last_position, max_lines, filter_options, filter_type)
            
            # Add additional metadata
            log_data['request_timestamp'] = datetime.now(timezone.utc).isoformat()
            log_data['format'] = format_type
            
            return web.json_response(log_data)
            
        except ValueError as e:
            LogFire.log("ERROR", f"Invalid parameters in live logs API: {str(e)}")
            return web.json_response({
                'error': 'Invalid parameters',
                'details': str(e)
            }, status=400)
        except Exception as e:
            LogFire.log("ERROR", f"Live logs API error: {str(e)}")
            exception_triggered(e, "api_live_logs", None)
            return web.json_response({
                'error': str(e),
                'content': '',
                'new_position': last_position,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }, status=500)
    
    @require_system_user
    async def api_log_status(self, request: web.Request) -> web.Response:
        """API endpoint for log file status information"""
        try:
            # CRITICAL DEBUG: Check working directory context in API handler
            from os import getcwd, chdir
            from os.path import exists, abspath
            from pathlib import Path
            
            api_handler_cwd = getcwd()
            LogFire.log("DEBUG", f"[API_LOG_STATUS] API handler current working directory: {api_handler_cwd}")
            
            # Check if we're in the correct working directory (where dev.env exists)
            dev_env_path = api_handler_cwd + "/dev.env"
            dev_env_exists_here = exists(dev_env_path)
            LogFire.log("DEBUG", f"[API_LOG_STATUS] dev.env exists in current directory: {dev_env_exists_here}")
            
            # If not in correct directory, temporarily switch to correct directory
            original_cwd = api_handler_cwd
            if not dev_env_exists_here:
                # Find the correct directory (same logic as web server startup)
                script_dir = Path(__file__).parent.parent.absolute()  # src/AgenticRAG/
                potential_dev_env = script_dir / "dev.env"
                
                if potential_dev_env.exists():
                    LogFire.log("DEBUG", f"[API_LOG_STATUS] Switching API handler to correct directory: {script_dir}")
                    chdir(script_dir)
                else:
                    LogFire.log("DEBUG", f"[API_LOG_STATUS] Cannot find correct directory, staying in: {api_handler_cwd}")
            else:
                LogFire.log("DEBUG", f"[API_LOG_STATUS] API handler working directory is correct: {api_handler_cwd}")
            
            # Get log file path and basic info
            log_path = await LogFire.get_debug_trace_path()
            log_path_absolute = abspath(log_path)
            
            LogFire.log("DEBUG", f"[API_LOG_STATUS] Resolved log file path (relative): {log_path}")
            LogFire.log("DEBUG", f"[API_LOG_STATUS] Resolved log file path (absolute): {log_path_absolute}")
            
            file_exists = exists(log_path)
            LogFire.log("DEBUG", f"[API_LOG_STATUS] File exists check: {file_exists}")
            
            status_info = {
                'log_path': log_path,
                'log_path_absolute': log_path_absolute,
                'exists': file_exists,
                'api_handler_cwd': api_handler_cwd,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            # Add file stats if file exists
            if status_info['exists']:
                try:
                    file_stats = os.stat(log_path)
                    status_info.update({
                        'file_size': file_stats.st_size,
                        'modified_time': datetime.fromtimestamp(file_stats.st_mtime, timezone.utc).isoformat(),
                        'readable': os.access(log_path, os.R_OK)
                    })
                    LogFire.log("DEBUG", f"[API_LOG_STATUS] File stats - Size: {file_stats.st_size}, Readable: {os.access(log_path, os.R_OK)}")
                except Exception as stat_error:
                    status_info['stat_error'] = str(stat_error)
                    LogFire.log("DEBUG", f"[API_LOG_STATUS] File stat error: {stat_error}")
            else:
                LogFire.log("DEBUG", f"[API_LOG_STATUS] File does not exist at path: {log_path_absolute}")
            
            # Restore original working directory if we changed it
            current_cwd = getcwd()
            if current_cwd != original_cwd:
                LogFire.log("DEBUG", f"[API_LOG_STATUS] Restoring original working directory: {original_cwd}")
                chdir(original_cwd)
            
            LogFire.log("DEBUG", f"[API_LOG_STATUS] Returning status - exists: {status_info['exists']}")
            return web.json_response(status_info)
            
        except Exception as e:
            LogFire.log("ERROR", f"Log status API error: {str(e)}")
            exception_triggered(e, "api_log_status", None)
            return web.json_response({
                'error': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }, status=500)
    
    @require_authentication
    async def api_check_test_server(self, request: web.Request) -> web.Response:
        """API endpoint to check test_real server availability - authenticated users only"""
        try:
            # Get query parameters
            port = int(request.query.get('port', 40999))
            
            # Only allow checking specific ports for security
            if port not in [40999, 41000]:
                return web.json_response({
                    'error': 'Invalid port. Only 40999 and 41000 are allowed.',
                    'available': False,
                    'port': port
                }, status=400)
            
            LogFire.log("DEBUG", f"Checking test_real server availability on port {port}", chat=None)
            
            # Check if test environment is active and return environment info
            from globals import is_test_environment
            test_env = is_test_environment()
            
            # Always allow the check but return environment info
            LogFire.log("DEBUG", f"Test environment check: {test_env}", chat=None)
            
            # Check server availability using aiohttp ClientSession
            import aiohttp
            import asyncio
            
            timeout = aiohttp.ClientTimeout(total=3.0, connect=2.0)
            
            try:
                async with aiohttp.ClientSession(timeout=timeout) as session:
                    # Try to connect to the server's root endpoint
                    url = f"http://localhost:{port}/"
                    async with session.get(url) as response:
                        # If we get any response (including 404, 302, etc.), server is running
                        available = response.status < 500
                        
                        return web.json_response({
                            'available': available,
                            'port': port,
                            'status_code': response.status,
                            'test_environment': test_env,
                            'url': url,
                            'timestamp': datetime.now(timezone.utc).isoformat()
                        })
                        
            except (aiohttp.ClientError, asyncio.TimeoutError) as conn_error:
                # Server is not available
                return web.json_response({
                    'available': False,
                    'port': port,
                    'test_environment': test_env,
                    'error': str(conn_error),
                    'timestamp': datetime.now(timezone.utc).isoformat()
                })
            
        except ValueError as e:
            LogFire.log("ERROR", f"Invalid port parameter in test server check: {str(e)}")
            return web.json_response({
                'error': 'Invalid port parameter',
                'available': False,
                'details': str(e)
            }, status=400)
        except Exception as e:
            LogFire.log("ERROR", f"Test server check API error: {str(e)}")
            exception_triggered(e, "api_check_test_server", None)
            return web.json_response({
                'error': str(e),
                'available': False,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }, status=500)
    
    async def api_user_list(self, request: web.Request) -> web.Response:
        """API endpoint to get list of all users"""
        # Check for session timeout first
        session_check = self._check_session_timeout(request)
        if session_check:
            return session_check
        
        try:
            from managers.manager_users import ZairaUserManager
            user_manager = ZairaUserManager.get_instance()
            
            # Get all users from the user manager
            all_users = []
            user_count = len(user_manager.users)
            
            # Memory exhaustion protection - limit user list size
            MAX_USERS = 1000  # Configurable limit
            if user_count > MAX_USERS:
                LogFire.log("ERROR", f"User list too large ({user_count} users), truncating to {MAX_USERS}", chat=None)
                # Take first MAX_USERS users and add warning
                limited_users = list(user_manager.users.items())[:MAX_USERS]
                
                for user_guid, user in limited_users:
                    all_users.append({
                        'user_guid': user_guid,
                        'username': user.username,
                        'rank': user.rank.value if hasattr(user.rank, 'value') else str(user.rank),
                        'device_guid': str(user.DeviceGUID),
                        'created_at': user.created_at.isoformat() if hasattr(user, 'created_at') and user.created_at else None
                    })
                
                # Sort by username for easy lookup
                all_users.sort(key=lambda x: x['username'].lower())
                
                return web.json_response({
                    'users': all_users,
                    'total_count': len(all_users),
                    'actual_total': user_count,
                    'is_truncated': True,
                    'truncated_message': f'Showing first {MAX_USERS} of {user_count} users due to memory limits',
                    'timestamp': datetime.now(timezone.utc).isoformat()
                })
            
            # Normal processing for smaller user lists
            for user_guid, user in user_manager.users.items():
                all_users.append({
                    'user_guid': user_guid,
                    'username': user.username,
                    'rank': user.rank.value if hasattr(user.rank, 'value') else str(user.rank),
                    'device_guid': str(user.DeviceGUID),
                    'created_at': user.created_at.isoformat() if hasattr(user, 'created_at') and user.created_at else None
                })
            
            # Sort by username for easy lookup
            all_users.sort(key=lambda x: x['username'].lower())
            
            return web.json_response({
                'users': all_users,
                'total_count': len(all_users),
                'timestamp': datetime.now(timezone.utc).isoformat()
            })
            
        except Exception as e:
            # Check for session/authentication errors first
            error_msg = str(e).lower()
            if any(indicator in error_msg for indicator in ['unauthorized', 'session', 'expired', 'authentication']):
                LogFire.log("ERROR", f"User list API session timeout: {str(e)}")
                return web.json_response({
                    'error': 'Session expired. Please refresh the page.',
                    'error_type': 'session_timeout',
                    'redirect_url': '/dashboard',
                    'action_required': 'refresh'
                }, status=401)
            
            # Check for rate limiting indicators
            if any(indicator in error_msg for indicator in ['rate limit', 'too many requests', 'quota exceeded', 'throttled']):
                LogFire.log("ERROR", f"User list API rate limited: {str(e)}")
                return web.json_response({
                    'error': 'Rate limit exceeded. Please wait before refreshing user list.',
                    'error_type': 'rate_limit',
                    'retry_after': 30,
                    'users': [],  # Empty fallback
                    'total_count': 0,
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }, status=429)
            
            LogFire.log("ERROR", f"User list API error: {str(e)}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )
    
    async def api_user_chat_history(self, request: web.Request) -> web.Response:
        """API endpoint to get chat history for a user"""
        try:
            user_guid = request.query.get('user_guid')
            if not user_guid:
                return web.json_response(
                    {'error': 'user_guid parameter required'},
                    status=400
                )
            
            from managers.manager_users import ZairaUserManager
            user_manager = ZairaUserManager.get_instance()
            user = await user_manager.find_user(user_guid)
            
            if not user:
                return web.json_response(
                    {'error': 'User not found'},
                    status=404
                )
            
            # Get chat history from user's chat_history attribute, organized by sessions
            chat_sessions = {}
            total_sessions = 0
            total_messages = 0
            
            # Memory exhaustion protection
            MAX_SESSIONS = 20  # Limit number of sessions to process
            MAX_MESSAGES_PER_SESSION = 100  # Limit messages per session
            MAX_TOTAL_MESSAGES = 1000  # Overall message limit
            
            if hasattr(user, 'chat_history') and user.chat_history:
                total_sessions = len(user.chat_history)
                session_items = list(user.chat_history.items())
                
                # Sort sessions by activity (most recent first) and limit
                if total_sessions > MAX_SESSIONS:
                    LogFire.log("ERROR", f"Too many chat sessions ({total_sessions}), limiting to {MAX_SESSIONS}", chat=None)
                    # Take most recent sessions
                    session_items = session_items[-MAX_SESSIONS:]
                
                sessions_processed = 0
                for session_id, chat_session in session_items:
                    if total_messages >= MAX_TOTAL_MESSAGES:
                        LogFire.log("ERROR", f"Message limit reached ({MAX_TOTAL_MESSAGES}), stopping session processing", chat=None)
                        break
                    
                    # Handle both old format (list of messages) and new format (ZairaChat objects)
                    messages = []
                    session_title = None
                    if hasattr(chat_session, 'messages'):  # New ZairaChat format
                        session_messages = chat_session.messages
                        # Extract title from ZairaChat object
                        session_title = getattr(chat_session, 'title', None)
                        # Limit messages per session
                        messages = session_messages[-min(MAX_MESSAGES_PER_SESSION, len(session_messages)):]
                    elif isinstance(chat_session, list):  # Old format - list of messages
                        messages = chat_session[-min(MAX_MESSAGES_PER_SESSION, len(chat_session)):]
                    
                    # Check if adding these messages would exceed total limit
                    if total_messages + len(messages) > MAX_TOTAL_MESSAGES:
                        remaining_slots = MAX_TOTAL_MESSAGES - total_messages
                        messages = messages[-remaining_slots:] if remaining_slots > 0 else []
                    
                    total_messages += len(messages)
                    sessions_processed += 1
                    
                    # Process messages to group debug messages with following assistant messages
                    processed_messages = []
                    debug_buffer = []
                    
                    for i, message in enumerate(messages):
                        # Get full content without truncation
                        content = message.content if hasattr(message, 'content') else ''
                        display_content = content
                        
                        message_role = getattr(message, 'role', None)
                        role_str = str(message_role).lower() if message_role else 'user'
                        
                        if 'debug' in role_str or 'logging' in role_str:
                            # This is a debug message - add to buffer
                            debug_buffer.append({
                                'session_id': str(session_id),
                                'timestamp': message.timestamp.isoformat() if hasattr(message, 'timestamp') else None,
                                'message_type': getattr(message, 'message_type', 'unknown'),
                                'content': display_content,
                                'role': role_str,
                                'tokens_used': getattr(message, 'tokens_used', 0),
                                'content_length': len(content)
                            })
                        else:
                            # Non-debug message
                            
                            # CRITICAL: If this is a user message and we have debug messages in buffer, 
                            # they should not be grouped across user message boundaries
                            if 'user' in role_str and debug_buffer:
                                # Add any buffered debug messages as standalone messages before this user message
                                processed_messages.extend(debug_buffer)
                                debug_buffer.clear()  # Clear buffer to prevent inappropriate grouping
                            
                            # Extract call_trace for assistant messages
                            call_trace = []
                            if hasattr(message, 'call_trace') and message.call_trace:
                                # Only include call_trace for assistant messages
                                if 'assistant' in role_str:
                                    call_trace = message.call_trace if isinstance(message.call_trace, list) else [message.call_trace]
                                    LogFire.log("DEBUG", f"Found call_trace for assistant message: {call_trace}", chat=None)
                            
                            message_data = {
                                'session_id': str(session_id),
                                'timestamp': message.timestamp.isoformat() if hasattr(message, 'timestamp') else None,
                                'message_type': getattr(message, 'message_type', 'unknown'),
                                'content': display_content,
                                'role': role_str,
                                'tokens_used': getattr(message, 'tokens_used', 0),
                                'content_length': len(content),
                                'call_trace': call_trace
                            }
                            
                            # If this is an assistant message and we have debug messages buffered, attach them
                            if 'assistant' in role_str and debug_buffer:
                                message_data['debug_messages'] = debug_buffer.copy()
                                debug_buffer.clear()  # Clear buffer after attaching
                            
                            processed_messages.append(message_data)
                    
                    # If there are leftover debug messages at the end, add them as standalone messages
                    processed_messages.extend(debug_buffer)
                    
                    # Sort messages by timestamp within session
                    processed_messages.sort(key=lambda x: x['timestamp'] or '', reverse=False)
                    
                    # Store session data
                    session_timestamp = processed_messages[-1]['timestamp'] if processed_messages else None
                    chat_sessions[str(session_id)] = {
                        'session_id': str(session_id),
                        'messages': processed_messages,
                        'message_count': len(processed_messages),
                        'last_activity': session_timestamp,
                        'title': session_title  # Include session title from ZairaChat object
                    }
            
            # Sort sessions by last activity, most recent first
            sorted_sessions = sorted(chat_sessions.values(), key=lambda x: x['last_activity'] or '', reverse=True)
            
            # Create sessions_list as array of session IDs (what frontend expects)
            sessions_list = [session['session_id'] for session in sorted_sessions]
            
            # Reorganize data structure for frontend compatibility
            frontend_chat_sessions = {}
            sessions_metadata = {}
            for session in sorted_sessions:
                session_id = session['session_id']
                frontend_chat_sessions[session_id] = session['messages']
                # Create metadata dictionary that frontend expects
                sessions_metadata[session_id] = {
                    'title': session.get('title'),
                    'last_activity': session.get('last_activity'),
                    'message_count': session.get('message_count', 0)
                }
            
            # Prepare response with memory limitation information
            response_data = {
                'user_info': {
                    'user_guid': user_guid,
                    'username': user.username,
                    'rank': user.rank.value if hasattr(user.rank, 'value') else str(user.rank)
                },
                'chat_sessions': frontend_chat_sessions,  # sessionId -> messages mapping
                'sessions_list': sessions_list,  # Array of session IDs for iteration
                'sessions_metadata': sessions_metadata,  # sessionId -> metadata mapping (includes titles)
                'total_sessions': len(chat_sessions),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
            # Add memory limitation warnings if applicable
            if hasattr(user, 'chat_history') and user.chat_history:
                actual_total_sessions = len(user.chat_history)
                if actual_total_sessions > MAX_SESSIONS or total_messages >= MAX_TOTAL_MESSAGES:
                    response_data['is_truncated'] = True
                    response_data['truncated_info'] = {
                        'actual_total_sessions': actual_total_sessions,
                        'sessions_shown': len(chat_sessions),
                        'total_messages_shown': total_messages,
                        'message_limit': MAX_TOTAL_MESSAGES,
                        'session_limit': MAX_SESSIONS,
                        'truncation_reason': 'Memory limit protection'
                    }
                    response_data['truncated_message'] = f'Showing {len(chat_sessions)} most recent sessions with {total_messages} messages (limited for performance)'
            
            return web.json_response(response_data)
            
        except Exception as e:
            LogFire.log("ERROR", f"Chat history API error: {str(e)}")
            return web.json_response(
                {'error': str(e)},
                status=500
            )
    
    def _generate_dashboard_with_menu(self, overview: Dict[str, Any]) -> str:
        """🗑️ DEAD CODE: Generate the original dashboard HTML with enhanced menu overlay"""
        LogFire.log("ERROR", "🚨 DEAD CODE ACCESSED: _generate_dashboard_with_menu() called - this method should not be reached", severity="error")
        
        # 🛑 IMMEDIATE RETURN - DO NOT EXECUTE FUNCTIONALITY
        return "<!-- 🗑️ DEAD CODE: This method is obsolete and should not be called -->"
        
        # Dead code below this point will never execute:
        # original_html = self._generate_dashboard_html_working(overview)
        # html_with_menu = self._add_menu_overlay(original_html)
        # return html_with_menu
    
    def _get_menu_html(self) -> str:
        """Get the right-side menu HTML from dashboard_header.txt"""
        try:
            from os import getcwd, path as os_path
            
            # Determine UI folder path
            current_dir = getcwd()
            possible_paths = [
                os_path.join(current_dir, "ui"),                          
                os_path.join(current_dir, "src", "AgenticRAG", "ui"),     
            ]
            
            ui_folder = None
            for path in possible_paths:
                if os_path.exists(path) and os_path.isdir(path):
                    ui_folder = path
                    break
            
            if ui_folder is None:
                return ""  # No menu if UI folder not found
            
            # Read the dashboard_header.txt file to extract menu HTML
            header_file = os_path.join(ui_folder, "dashboard_header.txt")
            if os_path.exists(header_file):
                with open(header_file, "r", encoding="utf-8") as f:
                    header_content = f.read()
                
                # Extract the menu part (everything before the main-container)
                # This includes the particles background and right-side menu
                if '<!-- Main Container -->' in header_content:
                    menu_part = header_content.split('<!-- Main Container -->')[0]
                    # Also extract the JavaScript for menu functionality
                    if '<script>' in header_content:
                        script_start = header_content.find('<script>')
                        script_end = header_content.find('</script>') + len('</script>')
                        menu_script = header_content[script_start:script_end]
                        return menu_part + menu_script
                    return menu_part
                else:
                    # Return the entire header if no main container marker found
                    return header_content
            
            return ""  # Empty menu if file not found
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to load menu HTML: {str(e)}")
            return ""  # Return empty string on error
    
    def _should_log_frontend_debug(self, message: str) -> bool:
        """
        Determine if frontend debug message should be logged based on state changes
        and frequency control to reduce FRONTEND_DEBUG cascading noise.
        """
        import hashlib
        
        # Parse common frontend debug patterns
        if "PROCESSING" in message and "role=" in message:
            # Extract the role and count from processing messages
            try:
                if "PROCESSING" in message and "pendingCount=" in message:
                    # Extract pendingCount from message like "PROCESSING 15: role="debug" type="unknown" isDebug=true isHuman=false pendingCount=15"
                    parts = message.split("pendingCount=")
                    if len(parts) > 1:
                        count_part = parts[1].split()[0]
                        current_count = int(count_part)
                        
                        # Only log every 5th processing message or when count changes significantly
                        if current_count % 5 == 0 or abs(current_count - self._frontend_message_count) > 10:
                            self._frontend_message_count = current_count
                            return True
                        return False
            except (ValueError, IndexError):
                pass
        
        elif "ACCUMULATED DEBUG" in message:
            # Only log accumulated debug when count changes significantly
            try:
                if "now" in message and "pending" in message:
                    # Extract count from "ACCUMULATED DEBUG: now 16 pending"
                    parts = message.split("now")
                    if len(parts) > 1:
                        count_part = parts[1].split("pending")[0].strip()
                        current_count = int(count_part)
                        
                        # Check if this is a significant change from last logged count
                        last_accumulated = self._last_frontend_state.get('last_accumulated_count', 0)
                        if abs(current_count - last_accumulated) >= 5:  # Only log every 5 message changes
                            self._last_frontend_state['last_accumulated_count'] = current_count
                            return True
                        return False
            except (ValueError, IndexError):
                pass
        
        elif "SORTED MESSAGES" in message:
            # Only log sorted messages periodically - hash-based deduplication
            message_hash = hashlib.md5(message.encode()).hexdigest()
            last_sorted_hash = self._last_frontend_state.get('last_sorted_hash')
            
            if message_hash != last_sorted_hash:
                self._last_frontend_state['last_sorted_hash'] = message_hash
                # Only log every 3rd unique sorted message to reduce volume
                sort_count = self._last_frontend_state.get('sort_count', 0) + 1
                self._last_frontend_state['sort_count'] = sort_count
                return sort_count % 3 == 0
            return False
        
        # For other message types, use general frequency control
        # Only log every 10th message to prevent spam
        general_count = self._last_frontend_state.get('general_count', 0) + 1
        self._last_frontend_state['general_count'] = general_count
        
        return general_count % 10 == 0
    
    def _add_menu_overlay(self, original_html: str) -> str:
        """🗑️ DEAD CODE: Add menu overlay - now handled by UI file system"""
        LogFire.log("ERROR", "🚨 DEAD CODE ACCESSED: _add_menu_overlay() called - replaced by UI file system", severity="error")
        
        # 🛑 IMMEDIATE RETURN - DO NOT EXECUTE FUNCTIONALITY  
        return "<!-- 🗑️ DEAD CODE: This method is obsolete and should not be called -->"
        
        # Dead code below this point will never execute:
        # return original_html
    
    def _generate_dashboard_html_old(self, overview: Dict[str, Any]) -> str:
        """🗑️ DEAD CODE: Fallback HTML generation - now uses template system as well"""
        LogFire.log("ERROR", "🚨 DEAD CODE ACCESSED: _generate_dashboard_html_old() called - should use UI file system instead", severity="error")
        
        # 🛑 IMMEDIATE RETURN - DO NOT EXECUTE FUNCTIONALITY
        return "<!-- 🗑️ DEAD CODE: This method is obsolete and should not be called -->"
        
        # Dead code below this point will never execute:
        # (All remaining method code has been removed as it's unreachable)


    def _generate_dashboard_html(self, overview: Dict[str, Any]) -> str:
        """🗑️ DEAD CODE: Generate the main dashboard HTML - uses template system by default"""
        LogFire.log("ERROR", "🚨 DEAD CODE ACCESSED: _generate_dashboard_html() called - should use create_html_out() instead", severity="error")
        
        # 🛑 IMMEDIATE RETURN - DO NOT EXECUTE FUNCTIONALITY
        return "<!-- 🗑️ DEAD CODE: This method is obsolete and should not be called -->"
        
        # Dead code below this point will never execute:
        # (All remaining method code has been removed as it's unreachable)
    
    def _generate_dashboard_html_template(self, overview: Dict[str, Any]) -> str:
        """🗑️ DEAD CODE: Generate dashboard HTML using external templates and stylesheets"""
        LogFire.log("ERROR", "🚨 DEAD CODE ACCESSED: _generate_dashboard_html_template() called - replaced by UI file system", severity="error")
        
        # 🛑 IMMEDIATE RETURN - DO NOT EXECUTE FUNCTIONALITY
        return "<!-- 🗑️ DEAD CODE: This method is obsolete and should not be called -->"
        
        # Dead code below this point will never execute:
        # (All remaining method code has been removed as it's unreachable)
    
    def _generate_dashboard_html_working(self, overview: Dict[str, Any]) -> str:
        """🗑️ DEAD CODE: Generate dashboard HTML using external templates and CSS/JS files"""
        LogFire.log("ERROR", "🚨 DEAD CODE ACCESSED: _generate_dashboard_html_working() called - replaced by UI file system", severity="error")
        
        # 🛑 IMMEDIATE RETURN - DO NOT EXECUTE FUNCTIONALITY
        return "<!-- 🗑️ DEAD CODE: This method is obsolete and should not be called -->"
        
        # Dead code below this point will never execute:
        # (All remaining method code has been removed as it's unreachable)
        #     factory_metrics = overview.get('factory_metrics', {})
        #     performance_metrics = overview.get('performance_metrics', {})
            
        #     overall_status = system_health.get('overall_status', 'unknown')
        #     active_managers = factory_metrics.get('active_managers', 0)
        #     total_requests = factory_metrics.get('total_requests_all_users', 0)
        #     total_active_tasks = factory_metrics.get('total_active_tasks', 0)
            
        #     # Color coding for status
        #     status_color = {
        #         'healthy': '#28a745',
        #         'moderate': '#ffc107', 
        #         'degraded': '#fd7e14',
        #         'critical': '#dc3545',
        #         'unknown': '#6c757d'
        #     }.get(overall_status, '#6c757d')
            
        #     # Load template using external template loader
        #     template_loader = get_dashboard_template_loader()
        #     # DEBUG: Log template variables to check for problematic content
        #     template_vars = {
        #         'overall_status': overall_status,
        #         'active_managers': active_managers,
        #         'total_requests': total_requests,
        #         'total_active_tasks': total_active_tasks,
        #         'status_color': status_color,
        #         'timestamp': performance_metrics.get('timestamp', 'Unknown')
        #     }
            
        #     LogFire.log("DEBUG", f"Dashboard template variables: {template_vars}", severity="debug")
            
        #     # Check for Unicode in template variables
        #     for key, value in template_vars.items():
        #         if isinstance(value, str) and any(ord(c) > 127 for c in value):
        #             LogFire.log("ERROR", f"Template variable '{key}' contains Unicode: {value}", severity="error")
            
        #     # Note: dashboard.html template was removed - fallback method will be used
        #     raise FileNotFoundError("Dashboard template removed")
            
        #     # DEBUG: Log HTML content length and potential parsing issues
        #     LogFire.log("DEBUG", f"Final dashboard HTML length: {len(final_html)} characters", severity="debug")
            
        #     # DEBUG: Check for potential problematic characters
        #     has_unicode = any(ord(c) > 127 for c in final_html)
        #     if has_unicode:
        #         LogFire.log("ERROR", "Dashboard HTML contains Unicode characters that may cause parsing errors", severity="error")
        #         # Log sample of problematic characters
        #         unicode_chars = [c for c in final_html if ord(c) > 127][:10]
        #         LogFire.log("ERROR", f"Sample Unicode characters found: {unicode_chars}", severity="error")
            
        #     # DEBUG: Check for unclosed tags or malformed HTML
        #     body_tags = final_html.count('<body>') - final_html.count('</body>')
        #     script_tags = final_html.count('<script>') + final_html.count('<script ') - final_html.count('</script>')
        #     LogFire.log("DEBUG", f"HTML validation - Body tags balance: {body_tags}, Script tags balance: {script_tags}", severity="debug")
            
        #     # CRITICAL: Sanitize HTML content to prevent document.write() parsing errors
        #     # Remove any remaining Unicode characters that could break JavaScript parsing
        #     import re
        #     sanitized_html = re.sub(r'[^\x00-\x7F]+', '', final_html)  # Remove non-ASCII characters
        #     sanitized_html = sanitized_html.replace('\u00a0', ' ')  # Replace non-breaking spaces
        #     sanitized_html = sanitized_html.replace('\r\n', '\n').replace('\r', '\n')  # Normalize line endings
            
        #     # Log if sanitization made changes
        #     if len(sanitized_html) != len(final_html):
        #         LogFire.log("DEBUG", f"HTML sanitized: removed {len(final_html) - len(sanitized_html)} problematic characters", severity="debug")
            
        #     return sanitized_html
            
        # except Exception as e:
        #     LogFire.log("ERROR", f"Failed to generate dashboard HTML using templates: {str(e)}")
        #     # Fallback to minimal HTML if templates fail
        #     return f"""
        #     <!DOCTYPE html>
        #     <html lang="en">
        #     <head>
        #         <meta charset="UTF-8">
        #         <meta name="viewport" content="width=device-width, initial-scale=1.0">
        #         <title>ZairaControl - Dashboard Error</title>
        #         <style>
        #             body {{ background: #000; color: #fff; font-family: Arial, sans-serif; padding: 2rem; }}
        #             .error {{ background: #dc3545; padding: 1rem; border-radius: 8px; }}
        #         </style>
        #     </head>
        #     <body>
        #         <div class="error">
        #             <h1>Dashboard Template Error</h1>
        #             <p>Failed to load dashboard template. Error: {str(e)}</p>
        #             <p>Please check that the template files exist in endpoints/dashboard/</p>
        #         </div>
        #     </body>
        #     </html>"""

    @classmethod
    async def setup(cls):
        """Setup ZairaControl endpoint routes"""
        try:
            # Removed manual debug_trace.log write - use LogFire.log() instead
            
            # Add routes to the main API endpoint
            from endpoints.api_endpoint import APIEndpoint
            zaira_control = get_dashboard_endpoint()
            
            APIEndpoint.get_instance().aio_app.add_routes([
                # Main dashboard page - NOW POST with authentication
                web.post('/dashboard', zaira_control.dashboard_home),
                
                # Static file serving for dashboard assets
                web.get('/dashboard/static/css/{filename}', zaira_control.serve_static_css),
                web.get('/dashboard/static/js/{filename}', zaira_control.serve_static_js),
                web.get('/dashboard/templates/{template_name}', zaira_control.serve_template),
                
                # New navigation API endpoints
                web.get('/dashboard/api/dashboard-overview', zaira_control.api_dashboard_overview),
                web.get('/dashboard/api/system-health', zaira_control.api_system_health),
                web.get('/dashboard/api/user-management', zaira_control.api_user_management),
                web.get('/dashboard/api/page-content', zaira_control.api_page_content),
                web.get('/dashboard/api/debug-session', zaira_control.api_debug_session),
                
                # Existing API endpoints
                web.get('/dashboard/api/user-details', zaira_control.api_user_details),
                web.get('/dashboard/api/user-requests', zaira_control.api_user_requests),
                web.get('/dashboard/api/user-list', zaira_control.api_user_list),
                web.get('/dashboard/api/user-chat-history', zaira_control.api_user_chat_history),
                web.get('/dashboard/api/security-report', zaira_control.api_security_report),
                web.get('/dashboard/api/performance-report', zaira_control.api_performance_report),
                web.post('/dashboard/api/cancel-request', zaira_control.api_cancel_request),
                web.post('/dashboard/api/send-message', zaira_control.api_send_message),
                web.post('/dashboard/api/debug-trace', zaira_control.api_debug_trace),
                
                # Live log viewing endpoints (UNIFIED)
                web.get('/dashboard/api/live-logs', zaira_control.api_live_logs),
                web.get('/dashboard/api/log-status', zaira_control.api_log_status),
                
                # Test_real server detection endpoint
                web.get('/dashboard/api/check-test-server', zaira_control.api_check_test_server),
                
                # Session debug endpoint (for troubleshooting)
                web.get('/dashboard/api/session-debug', zaira_control.api_session_debug)
            ])
            
            # Removed manual debug_trace.log write - use LogFire.log() instead
            
            LogFire.log("INIT", "ZairaControl endpoint routes registered successfully", chat=None)
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to setup ZairaControl endpoint routes: {str(e)}")
            raise
    
    @require_authentication
    async def api_debug_session(self, request: web.Request) -> web.Response:
        """Debug endpoint to inspect current user session data"""
        try:
            # Get raw session data
            session = request.get('session', {})
            
            # Get user info via helper
            user_info = get_user_from_session(request)
            
            # Get SYSTEM user info for comparison
            from managers.manager_system_user import SystemUserManager
            system_manager = SystemUserManager.get_instance()
            system_user = await system_manager.get_system_user()
            
            debug_data = {
                'timestamp': datetime.now().isoformat(),
                'raw_session': session,
                'extracted_user_info': user_info,
                'system_user_info': {
                    'username': system_user.username if system_user else None,
                    'is_system_user': system_user.is_system_user if system_user else None,
                    'rank': system_user.rank.name if system_user else None,
                    'user_guid': str(system_user.user_guid) if system_user else None,
                    'email': system_user.email if system_user else None
                } if system_user else 'SYSTEM user not found',
                'session_checks': {
                    'has_session_key': 'session' in request,
                    'session_authenticated': session.get('authenticated', False),
                    'is_system_user_flag': session.get('is_system_user', 'NOT_SET'),
                    'is_system_user_type': type(session.get('is_system_user', None)).__name__
                },
                'comparison': {
                    'session_username': session.get('username'),
                    'system_username': 'SYSTEM',
                    'username_match': session.get('username') == 'SYSTEM',
                    'session_guid': session.get('user_guid'),
                    'system_guid': str(SystemUserManager.SYSTEM_USER_GUID),
                    'guid_match': session.get('user_guid') == str(SystemUserManager.SYSTEM_USER_GUID)
                }
            }
            
            LogFire.log("DEBUG", f"Debug session data requested: {debug_data}", severity="debug")
            
            return web.json_response(debug_data, status=200)
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "api_debug_session", None)
            return web.json_response({'error': 'Debug session failed', 'details': str(e)}, status=500)
    
    async def api_session_debug(self, request: web.Request) -> web.Response:
        """Lightweight session debug endpoint that doesn't require authentication"""
        try:
            # Get raw session data without authentication requirements
            session = request.get('session', {})
            cookies = dict(request.cookies)
            
            # Check if session cookie exists
            from endpoints.api_endpoint import SESSION_COOKIE_NAME
            has_session_cookie = SESSION_COOKIE_NAME in cookies
            session_cookie_length = len(cookies.get(SESSION_COOKIE_NAME, '')) if has_session_cookie else 0
            
            debug_info = {
                'timestamp': datetime.now().isoformat(),
                'path': request.path_qs,
                'method': request.method,
                'session_status': {
                    'has_session_data': bool(session),
                    'session_keys': list(session.keys()) if session else [],
                    'authenticated': session.get('authenticated', False),
                    'is_system_user': session.get('is_system_user', False),
                    'username': session.get('username', 'none'),
                    'user_guid': session.get('user_guid', 'none')
                },
                'cookie_status': {
                    'has_session_cookie': has_session_cookie,
                    'session_cookie_name': SESSION_COOKIE_NAME,
                    'session_cookie_length': session_cookie_length,
                    'all_cookies': list(cookies.keys()),
                    'cookie_count': len(cookies)
                },
                'headers': {
                    'user_agent': request.headers.get('User-Agent', 'unknown'),
                    'host': request.headers.get('Host', 'unknown'),
                    'origin': request.headers.get('Origin', 'none'),
                    'referer': request.headers.get('Referer', 'none')
                }
            }
            
            # Log this for debugging
            LogFire.log("DEBUG", f"Session debug requested from {request.path_qs}: {debug_info}", severity="debug")
            
            return web.json_response(debug_info)
            
        except Exception as e:
            LogFire.log("ERROR", f"Session debug API error: {str(e)}")
            exception_triggered(e, "api_session_debug", None)
            return web.json_response({'error': str(e)}, status=500)

# Global instance
_dashboard_endpoint = DashboardEndpoint()

def get_dashboard_endpoint() -> DashboardEndpoint:
    """Get the global Dashboard endpoint instance"""
    return _dashboard_endpoint