from imports import *
import psutil
import threading
from datetime import datetime, timezone
from typing import Dict, Any, Optional, List
from uuid import UUID

class MemoryTracker:
    """Track memory usage for user managers"""
    
    def __init__(self):
        self._usage_by_user: Dict[str, float] = {}
        self._lock = threading.Lock()
        
    def track_user_memory(self, user_guid: str, memory_mb: float):
        """Track memory usage for specific user"""
        with self._lock:
            self._usage_by_user[user_guid] = memory_mb
    
    def get_user_memory(self, user_guid: str) -> float:
        """Get current memory usage for user in MB"""
        with self._lock:
            return self._usage_by_user.get(user_guid, 0.0)
    
    def get_total_memory(self) -> float:
        """Get total memory usage across all users in MB"""
        with self._lock:
            return sum(self._usage_by_user.values())
    
    def cleanup_user(self, user_guid: str):
        """Clean up memory tracking for user"""
        with self._lock:
            self._usage_by_user.pop(user_guid, None)
    
    def get_system_memory_info(self) -> Dict[str, float]:
        """Get system memory information"""
        memory = psutil.virtual_memory()
        return {
            'total_mb': memory.total / 1024 / 1024,
            'available_mb': memory.available / 1024 / 1024,
            'used_mb': memory.used / 1024 / 1024,
            'percent_used': memory.percent
        }

class ThreadSafeCounter:
    """Thread-safe counter for metrics"""
    
    def __init__(self, initial_value: int = 0):
        self._value = initial_value
        self._lock = threading.Lock()
    
    def increment(self, amount: int = 1) -> int:
        """Increment counter and return new value"""
        with self._lock:
            self._value += amount
            return self._value
    
    def decrement(self, amount: int = 1) -> int:
        """Decrement counter and return new value"""
        with self._lock:
            self._value = max(0, self._value - amount)
            return self._value
    
    def get_value(self) -> int:
        """Get current counter value"""
        with self._lock:
            return self._value
    
    def reset(self) -> int:
        """Reset counter to zero and return previous value"""
        with self._lock:
            old_value = self._value
            self._value = 0
            return old_value

class TimeWindow:
    """Time-based sliding window for rate limiting"""
    
    def __init__(self, window_size_seconds: int):
        self.window_size = window_size_seconds
        self._requests: List[float] = []
        self._lock = threading.Lock()
    
    def add_request(self, timestamp: Optional[float] = None) -> int:
        """Add request to window and return current count"""
        if timestamp is None:
            timestamp = datetime.now(timezone.utc).timestamp()
        
        with self._lock:
            # Add new request
            self._requests.append(timestamp)
            
            # Remove old requests outside window
            cutoff = timestamp - self.window_size
            self._requests = [req for req in self._requests if req > cutoff]
            
            return len(self._requests)
    
    def get_count(self) -> int:
        """Get current count in window"""
        current_time = datetime.now(timezone.utc).timestamp()
        cutoff = current_time - self.window_size
        
        with self._lock:
            self._requests = [req for req in self._requests if req > cutoff]
            return len(self._requests)
    
    def clear(self):
        """Clear all requests from window"""
        with self._lock:
            self._requests.clear()

def validate_guid(guid_str: str) -> bool:
    """Validate GUID format"""
    try:
        UUID(guid_str)
        return True
    except (ValueError, TypeError):
        return False

def sanitize_user_input(input_string: str, max_length: int = 1000) -> str:
    """Sanitize user input for security"""
    if not isinstance(input_string, str):
        return ""
    
    # Truncate to max length
    sanitized = input_string[:max_length]
    
    # Remove null bytes and control characters
    sanitized = ''.join(char for char in sanitized if ord(char) >= 32 or char in '\t\n\r')
    
    # Ensure ASCII only as per project standards
    try:
        sanitized.encode('ascii')
    except UnicodeEncodeError:
        # Convert non-ASCII characters to ASCII equivalents or remove them
        sanitized = sanitized.encode('ascii', errors='ignore').decode('ascii')
    
    return sanitized.strip()

def format_duration(seconds: float) -> str:
    """Format duration in human-readable format"""
    if seconds < 60:
        return f"{seconds:.1f}s"
    elif seconds < 3600:
        minutes = seconds / 60
        return f"{minutes:.1f}m"
    else:
        hours = seconds / 3600
        return f"{hours:.1f}h"

def safe_json_serialize(obj: Any) -> str:
    """Safely serialize object to JSON"""
    try:
        def json_serializer(obj):
            if isinstance(obj, datetime):
                return obj.isoformat()
            elif isinstance(obj, UUID):
                return str(obj)
            elif hasattr(obj, '__dict__'):
                return obj.__dict__
            else:
                return str(obj)
        
        from json import dumps as json_dumps
        return json_dumps(obj, default=json_serializer, ensure_ascii=True)
    except Exception as e:
        LogFire.log("WARNING", f"Failed to serialize object to JSON: {str(e)}")
        return "{}"

def calculate_backoff_delay(attempt: int, base_delay: float = 1.0, max_delay: float = 300.0) -> float:
    """Calculate exponential backoff delay"""
    delay = base_delay * (2 ** attempt)
    return min(delay, max_delay)

def get_user_friendly_error(error: Exception) -> str:
    """Convert technical error to user-friendly message"""
    from .exceptions import (
        UserQuotaExceededError, UserRateLimitExceededError, 
        UnauthorizedScheduledRequestError, ScheduledRequestNotFoundError
    )
    
    if isinstance(error, UserQuotaExceededError):
        return f"You have exceeded your {error.quota_type} limit. Please try again later or contact support."
    elif isinstance(error, UserRateLimitExceededError):
        return f"Too many requests. Please wait {format_duration(error.retry_after)} before trying again."
    elif isinstance(error, UnauthorizedScheduledRequestError):
        return "You don't have permission to access this scheduled task."
    elif isinstance(error, ScheduledRequestNotFoundError):
        return "The requested scheduled task was not found."
    else:
        return "An error occurred while processing your request. Please try again later."