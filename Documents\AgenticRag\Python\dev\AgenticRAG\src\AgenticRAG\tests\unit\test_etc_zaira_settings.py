from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import patch, MagicMock
from dataclasses import is_dataclass, fields

# Import the module we're testing
from etc.ZairaSettings import ZairaSettingsClass, ZairaSettings

class TestZairaSettings:
    """Test suite for ZairaSettings.py"""

    def test_zaira_settings_class_is_dataclass(self):
        """Test ZairaSettingsClass is a dataclass"""
        assert is_dataclass(ZairaSettingsClass)

    def test_zaira_settings_class_fields(self):
        """Test ZairaSettingsClass has expected fields"""
        field_names = [field.name for field in fields(ZairaSettingsClass)]
        
        expected_fields = ['IsDebugMode', 'llm', 'sparse_embed_model']
        for field in expected_fields:
            assert field in field_names

    def test_zaira_settings_class_field_types(self):
        """Test ZairaSettingsClass field types"""
        class_fields = fields(ZairaSettingsClass)
        field_dict = {field.name: field for field in class_fields}
        
        # Check IsDebugMode is bool
        assert field_dict['IsDebugMode'].type == bool
        
        # Check llm has correct type annotation
        assert 'BaseChatModel' in str(field_dict['llm'].type)
        
        # Check sparse_embed_model has correct type annotation
        assert 'SparseTextEmbedding' in str(field_dict['sparse_embed_model'].type)

    def test_zaira_settings_class_default_values(self):
        """Test ZairaSettingsClass default values"""
        class_fields = fields(ZairaSettingsClass)
        field_dict = {field.name: field for field in class_fields}
        
        # Check llm has default value of None
        assert field_dict['llm'].default is None
        
        # Check sparse_embed_model has default value of None
        assert field_dict['sparse_embed_model'].default is None

    def test_zaira_settings_class_instantiation(self):
        """Test ZairaSettingsClass can be instantiated"""
        settings = ZairaSettingsClass(IsDebugMode=True)
        
        assert settings.IsDebugMode is True
        assert settings.llm is None
        assert settings.sparse_embed_model is None

    def test_zaira_settings_class_instantiation_with_all_params(self):
        """Test ZairaSettingsClass can be instantiated with all parameters"""
        mock_llm = MagicMock()
        mock_sparse_embed = MagicMock()
        
        settings = ZairaSettingsClass(
            IsDebugMode=False,
            llm=mock_llm,
            sparse_embed_model=mock_sparse_embed
        )
        
        assert settings.IsDebugMode is False
        assert settings.llm is mock_llm
        assert settings.sparse_embed_model is mock_sparse_embed

    def test_zaira_settings_class_missing_required_field(self):
        """Test ZairaSettingsClass raises error when required field is missing"""
        with pytest.raises(TypeError):
            ZairaSettingsClass()  # Missing required IsDebugMode

    def test_zaira_settings_class_invalid_debug_mode_type(self):
        """Test ZairaSettingsClass with invalid IsDebugMode type"""
        # Note: dataclass doesn't enforce types at runtime by default
        # but we can test that it accepts the value
        settings = ZairaSettingsClass(IsDebugMode="true")  # String instead of bool
        assert settings.IsDebugMode == "true"

    @patch('llama_index.core.Settings')
    def test_ollama_settings_classmethod(self, mock_settings):
        """Test OllamaSettings classmethod"""
        mock_settings_instance = MagicMock()
        mock_settings.return_value = mock_settings_instance
        
        result = ZairaSettingsClass.OllamaSettings()
        
        assert result is mock_settings

    @patch('llama_index.core.Settings')
    def test_ollama_settings_import_success(self, mock_settings):
        """Test OllamaSettings imports Settings correctly"""
        ZairaSettingsClass.OllamaSettings()
        
        # The import should have been successful if no exception was raised
        assert True

    def test_zaira_settings_singleton_assignment(self):
        """Test ZairaSettings is assigned to ZairaSettingsClass"""
        assert ZairaSettings is ZairaSettingsClass

    def test_zaira_settings_singleton_behavior(self):
        """Test ZairaSettings behaves as expected singleton reference"""
        # Create instance using the singleton reference
        settings1 = ZairaSettings(IsDebugMode=True)
        settings2 = ZairaSettings(IsDebugMode=False)
        
        # They should be different instances (not a true singleton pattern)
        assert settings1 is not settings2
        assert settings1.IsDebugMode != settings2.IsDebugMode

    def test_zaira_settings_class_attribute_access(self):
        """Test ZairaSettingsClass attribute access"""
        settings = ZairaSettingsClass(IsDebugMode=True)
        
        # Test getting attributes
        assert settings.IsDebugMode is True
        assert settings.llm is None
        assert settings.sparse_embed_model is None
        
        # Test setting attributes
        mock_llm = MagicMock()
        settings.llm = mock_llm
        assert settings.llm is mock_llm

    def test_zaira_settings_class_attribute_modification(self):
        """Test ZairaSettingsClass attribute modification"""
        settings = ZairaSettingsClass(IsDebugMode=True)
        
        # Modify attributes
        settings.IsDebugMode = False
        mock_sparse_embed = MagicMock()
        settings.sparse_embed_model = mock_sparse_embed
        
        assert settings.IsDebugMode is False
        assert settings.sparse_embed_model is mock_sparse_embed

    def test_zaira_settings_class_equality(self):
        """Test ZairaSettingsClass equality comparison"""
        settings1 = ZairaSettingsClass(IsDebugMode=True)
        settings2 = ZairaSettingsClass(IsDebugMode=True)
        settings3 = ZairaSettingsClass(IsDebugMode=False)
        
        assert settings1 == settings2
        assert settings1 != settings3

    def test_zaira_settings_class_repr(self):
        """Test ZairaSettingsClass string representation"""
        settings = ZairaSettingsClass(IsDebugMode=True)
        repr_str = repr(settings)
        
        assert 'ZairaSettingsClass' in repr_str
        assert 'IsDebugMode=True' in repr_str
        assert 'llm=None' in repr_str
        assert 'sparse_embed_model=None' in repr_str

    def test_zaira_settings_class_with_mock_llm(self):
        """Test ZairaSettingsClass with mock LLM"""
        mock_llm = MagicMock()
        mock_llm.model_name = 'test-model'
        
        settings = ZairaSettingsClass(IsDebugMode=True, llm=mock_llm)
        
        assert settings.llm is mock_llm
        assert settings.llm.model_name == 'test-model'

    def test_zaira_settings_class_with_mock_sparse_embed_model(self):
        """Test ZairaSettingsClass with mock sparse embedding model"""
        mock_sparse_embed = MagicMock()
        mock_sparse_embed.model_name = 'test-sparse-model'
        
        settings = ZairaSettingsClass(IsDebugMode=True, sparse_embed_model=mock_sparse_embed)
        
        assert settings.sparse_embed_model is mock_sparse_embed
        assert settings.sparse_embed_model.model_name == 'test-sparse-model'

    def test_type_checking_imports(self):
        """Test that TYPE_CHECKING imports are handled correctly"""
        # This test ensures the TYPE_CHECKING block doesn't cause runtime errors
        from etc.ZairaSettings import ZairaSettingsClass
        
        # If we can import without error, the TYPE_CHECKING block is working
        assert ZairaSettingsClass is not None

    def test_zaira_settings_class_hashable(self):
        """Test ZairaSettingsClass instances are hashable (for use in sets/dicts)"""
        settings1 = ZairaSettingsClass(IsDebugMode=True)
        settings2 = ZairaSettingsClass(IsDebugMode=False)
        
        # Dataclasses are not hashable by default, so we expect TypeError
        with pytest.raises(TypeError):
            settings_set = {settings1, settings2}
        
        # Also should not be able to use as dict key
        with pytest.raises(TypeError):
            settings_dict = {settings1: 'debug', settings2: 'production'}

    def test_zaira_settings_class_frozen_behavior(self):
        """Test ZairaSettingsClass is not frozen by default"""
        settings = ZairaSettingsClass(IsDebugMode=True)
        
        # Should be able to modify attributes (not frozen)
        settings.IsDebugMode = False
        assert settings.IsDebugMode is False
        
        # Should be able to add new attributes (not frozen)
        settings.new_attribute = 'test'
        assert settings.new_attribute == 'test'

    @patch('llama_index.core.Settings')
    def test_ollama_settings_multiple_calls(self, mock_settings):
        """Test OllamaSettings can be called multiple times"""
        mock_settings_instance = MagicMock()
        mock_settings.return_value = mock_settings_instance
        
        result1 = ZairaSettingsClass.OllamaSettings()
        result2 = ZairaSettingsClass.OllamaSettings()
        
        assert result1 is mock_settings
        assert result2 is mock_settings
        assert result1 is result2

    def test_zaira_settings_class_field_annotations(self):
        """Test ZairaSettingsClass field annotations are preserved"""
        annotations = ZairaSettingsClass.__annotations__
        
        assert 'IsDebugMode' in annotations
        assert annotations['IsDebugMode'] == bool
        
        # Check that type annotations are strings for forward references
        assert isinstance(annotations['llm'], str)
        assert isinstance(annotations['sparse_embed_model'], str)