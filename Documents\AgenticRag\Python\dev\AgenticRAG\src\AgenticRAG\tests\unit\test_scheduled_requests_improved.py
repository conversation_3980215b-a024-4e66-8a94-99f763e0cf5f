"""
Improved unit tests for Scheduled Task Management - targeting 90% coverage
Following CLAUDE.md guidance for comprehensive test coverage
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock, call
from datetime import datetime, timezone, timedelta
from uuid import uuid4, UUID
from imports import *

@pytest.mark.unit 
class TestScheduledRequestsImproved:
    """Improved tests for scheduled task functionality"""
    
    def setup_method(self):
        """Reset singletons and setup test data"""
        # Reset singleton instances
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        ScheduledRequestPersistenceManager._active_requests = {}
        ScheduledRequestPersistenceManager._paused_requests = {}
        
        # Setup common test data
        self.test_guid = str(uuid4())
        self.test_user_guid = str(uuid4())
        self.base_task_data = {
            'scheduled_guid': self.test_guid,
            'user_guid': self.test_user_guid,
            'schedule_prompt': 'test task every 5 minutes',
            'target_prompt': 'Test target',
            'delay_seconds': 300.0,
            'start_delay_seconds': 0.0,
            'schedule_type': 'RECURRING',
            'next_execution': datetime.now(timezone.utc),
            'is_active': True,
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc),
            'calling_bot_name': 'test_bot',
            'task_data': {'key': 'value'},
            'cancellation_reason': None,
            'cancelled_at': None
        }
    
    def teardown_method(self):
        """Clean up after each test"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        ScheduledRequestPersistenceManager._active_requests = {}
        ScheduledRequestPersistenceManager._paused_requests = {}

    # ===== CORE MANAGER FUNCTIONALITY =====
    
    def test_persistence_manager_singleton_pattern(self):
        """Test that ScheduledRequestPersistenceManager follows singleton pattern"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance1 = ScheduledRequestPersistenceManager()
        instance2 = ScheduledRequestPersistenceManager()
        instance3 = ScheduledRequestPersistenceManager.get_instance()
        
        assert instance1 is instance2
        assert instance2 is instance3
        assert isinstance(instance1, ScheduledRequestPersistenceManager)
        assert hasattr(instance1, '_db_available')
        assert instance1._db_available is True

    def test_get_instance_creates_new_instance(self):
        """Test that get_instance creates new instance when none exists"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        # Ensure no instance exists
        assert ScheduledRequestPersistenceManager._instance is None
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        assert instance is not None
        assert isinstance(instance, ScheduledRequestPersistenceManager)
        assert ScheduledRequestPersistenceManager._instance is instance

    @pytest.mark.asyncio
    async def test_setup_first_time_initialization(self):
        """Test manager setup first time"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        # Set initial state
        ScheduledRequestPersistenceManager._initialized = False
        
        with patch.object(ScheduledRequestPersistenceManager, '_create_tables', new_callable=AsyncMock) as mock_create:
            await ScheduledRequestPersistenceManager.setup()
            
            mock_create.assert_called_once()
            assert ScheduledRequestPersistenceManager._initialized is True

    @pytest.mark.asyncio
    async def test_setup_already_initialized(self):
        """Test manager setup when already initialized"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        # Set initial state
        ScheduledRequestPersistenceManager._initialized = True
        
        with patch.object(ScheduledRequestPersistenceManager, '_create_tables', new_callable=AsyncMock) as mock_create:
            await ScheduledRequestPersistenceManager.setup()
            
            mock_create.assert_not_called()

    @pytest.mark.asyncio
    async def test_late_setup_success(self):
        """Test late setup task recovery"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(instance, '_recover_requests', new_callable=AsyncMock) as mock_recover:
            await ScheduledRequestPersistenceManager.late_setup()
            
            mock_recover.assert_called_once()

    @pytest.mark.asyncio
    async def test_late_setup_error_handling(self):
        """Test late setup error handling"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(instance, '_recover_requests', new_callable=AsyncMock) as mock_recover:
            mock_recover.side_effect = Exception("Recovery failed")
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                await ScheduledRequestPersistenceManager.late_setup()
                
                mock_exception.assert_called_once()

    # ===== DATABASE TABLE CREATION TESTS =====

    @pytest.mark.asyncio
    async def test_create_tables_success(self):
        """Test successful table creation"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = [{'column_name': 'scheduled_guid'}, {'column_name': 'user_guid'}]
            
            with patch.object(instance, '_execute_single_operation', new_callable=AsyncMock) as mock_single:
                await instance._create_tables()
                
                # Should try to check table structure
                mock_execute.assert_called()
                # Should create indexes
                mock_single.assert_called()

    @pytest.mark.asyncio
    async def test_create_tables_missing_columns(self):
        """Test table creation when columns are missing"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', new_callable=AsyncMock) as mock_execute:
            # Return only some columns, missing required ones
            mock_execute.return_value = [{'column_name': 'scheduled_guid'}]
            
            with patch.object(instance, '_execute_single_operation', new_callable=AsyncMock) as mock_single:
                await instance._create_tables()
                
                # Should drop and recreate table
                drop_calls = [call for call in mock_single.call_args_list if 'DROP TABLE' in str(call)]
                assert len(drop_calls) > 0

    @pytest.mark.asyncio
    async def test_create_tables_table_not_exists(self):
        """Test table creation when table doesn't exist"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.side_effect = Exception("Table doesn't exist")
            
            with patch.object(instance, '_execute_single_operation', new_callable=AsyncMock) as mock_single:
                await instance._create_tables()
                
                # Should create table and indexes
                create_calls = [call for call in mock_single.call_args_list if 'CREATE TABLE' in str(call)]
                assert len(create_calls) > 0

    @pytest.mark.asyncio
    async def test_create_tables_connection_error(self):
        """Test table creation with connection errors"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.side_effect = ConnectionRefusedError("Connection refused")
            
            await instance._create_tables()
            
            # Should set db_available to False
            assert instance._db_available is False

    @pytest.mark.asyncio
    async def test_create_tables_index_creation_error(self):
        """Test table creation with index creation errors"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = []  # Empty result (table doesn't exist)
            
            with patch.object(instance, '_execute_single_operation', new_callable=AsyncMock) as mock_single:
                # First call (CREATE TABLE) succeeds, index calls fail
                mock_single.side_effect = [None, Exception("column does not exist"), None, None]
                
                await instance._create_tables()
                
                # Should continue creating other indexes despite failures
                assert mock_single.call_count >= 2

    # ===== DATABASE OPERATION TESTS =====

    @pytest.mark.asyncio
    async def test_execute_single_operation_success(self):
        """Test successful single operation execution"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.get_connection') as mock_get_conn:
            mock_pool = MagicMock()
            mock_conn = MagicMock()
            mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.execute.return_value = None
            mock_get_conn.return_value = mock_pool
            
            await instance._execute_single_operation("CREATE TABLE test")
            
            mock_conn.execute.assert_called_once_with("CREATE TABLE test")

    @pytest.mark.asyncio
    async def test_execute_single_operation_with_params(self):
        """Test single operation execution with parameters"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.get_connection') as mock_get_conn:
            mock_pool = MagicMock()
            mock_conn = MagicMock()
            mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.execute.return_value = None
            mock_get_conn.return_value = mock_pool
            
            await instance._execute_single_operation("INSERT INTO test VALUES ($1, $2)", ['value1', 'value2'])
            
            mock_conn.execute.assert_called_once_with("INSERT INTO test VALUES ($1, $2)", 'value1', 'value2')

    @pytest.mark.asyncio
    async def test_execute_single_operation_no_pool(self):
        """Test single operation execution when no pool available"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.get_connection') as mock_get_conn:
            mock_get_conn.return_value = None
            
            with patch('managers.manager_postgreSQL.PostgreSQLManager.connect_to_database', new_callable=AsyncMock) as mock_connect:
                mock_conn = MagicMock()
                mock_conn.execute.return_value = None
                mock_conn.close.return_value = None
                mock_connect.return_value = mock_conn
                
                await instance._execute_single_operation("CREATE TABLE test")
                
                mock_connect.assert_called_once_with("vectordb")
                mock_conn.execute.assert_called_once_with("CREATE TABLE test")
                mock_conn.close.assert_called_once()

    @pytest.mark.asyncio
    async def test_execute_single_operation_connection_error(self):
        """Test single operation execution with connection error"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.get_connection') as mock_get_conn:
            mock_get_conn.side_effect = Exception("Connection failed")
            
            with pytest.raises(Exception):
                await instance._execute_single_operation("CREATE TABLE test")

    # ===== TASK PERSISTENCE TESTS =====

    @pytest.mark.asyncio
    async def test_save_task_success(self):
        """Test successful task saving"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(instance, '_execute_single_operation', new_callable=AsyncMock) as mock_execute:
            await instance.save_task(self.base_task_data)
            
            mock_execute.assert_called_once()
            # Verify the SQL contains INSERT or UPDATE
            call_args = mock_execute.call_args[0][0]
            assert 'INSERT' in call_args or 'UPDATE' in call_args

    @pytest.mark.asyncio
    async def test_save_task_db_unavailable(self):
        """Test task saving when database is unavailable"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        instance._db_available = False
        
        # Should not raise exception
        await instance.save_task(self.base_task_data)

    @pytest.mark.asyncio
    async def test_save_task_with_json_data(self):
        """Test task saving with JSON data"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        # Add complex JSON data
        task_data = self.base_task_data.copy()
        task_data['task_data'] = {'nested': {'key': 'value'}, 'list': [1, 2, 3]}
        
        with patch.object(instance, '_execute_single_operation', new_callable=AsyncMock) as mock_execute:
            await instance.save_task(task_data)
            
            mock_execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_save_task_error_handling(self):
        """Test task saving error handling"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(instance, '_execute_single_operation', new_callable=AsyncMock) as mock_execute:
            mock_execute.side_effect = Exception("Database error")
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                await instance.save_task(self.base_task_data)
                
                mock_exception.assert_called_once()

    @pytest.mark.asyncio
    async def test_load_task_success(self):
        """Test successful task loading"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = [self.base_task_data]
            
            result = await instance.load_task(self.test_guid)
            
            assert result == self.base_task_data
            mock_execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_load_task_not_found(self):
        """Test task loading when task not found"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = []
            
            result = await instance.load_task(self.test_guid)
            
            assert result is None

    @pytest.mark.asyncio
    async def test_load_task_db_unavailable(self):
        """Test task loading when database is unavailable"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        instance._db_available = False
        
        result = await instance.load_task(self.test_guid)
        
        assert result is None

    @pytest.mark.asyncio
    async def test_load_task_error_handling(self):
        """Test task loading error handling"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.side_effect = Exception("Database error")
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                result = await instance.load_task(self.test_guid)
                
                assert result is None
                mock_exception.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_active_requests_success(self):
        """Test getting active tasks"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = [self.base_task_data]
            
            result = await instance.get_active_requests(self.test_user_guid)
            
            assert result == [self.base_task_data]
            mock_execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_get_active_requests_empty(self):
        """Test getting active tasks when none exist"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = []
            
            result = await instance.get_active_requests(self.test_user_guid)
            
            assert result == []

    @pytest.mark.asyncio
    async def test_get_active_requests_db_unavailable(self):
        """Test getting active tasks when database is unavailable"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        instance._db_available = False
        
        result = await instance.get_active_requests(self.test_user_guid)
        
        assert result == []

    @pytest.mark.asyncio
    async def test_cancel_task_success(self):
        """Test successful task cancellation"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(instance, '_execute_single_operation', new_callable=AsyncMock) as mock_execute:
            await instance.cancel_task(self.test_guid, "Test cancellation")
            
            mock_execute.assert_called_once()
            # Verify the SQL contains UPDATE
            call_args = mock_execute.call_args[0][0]
            assert 'UPDATE' in call_args

    @pytest.mark.asyncio
    async def test_cancel_task_db_unavailable(self):
        """Test task cancellation when database is unavailable"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        instance._db_available = False
        
        # Should not raise exception
        await instance.cancel_task(self.test_guid, "Test cancellation")

    @pytest.mark.asyncio
    async def test_cancel_task_error_handling(self):
        """Test task cancellation error handling"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(instance, '_execute_single_operation', new_callable=AsyncMock) as mock_execute:
            mock_execute.side_effect = Exception("Database error")
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                await instance.cancel_task(self.test_guid, "Test cancellation")
                
                mock_exception.assert_called_once()

    # ===== TASK RECOVERY TESTS =====

    @pytest.mark.asyncio
    async def test_recover_requests_success(self):
        """Test successful task recovery"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = [self.base_task_data]
            
            with patch.object(instance, '_recreate_task_from_data', new_callable=AsyncMock) as mock_recreate:
                await instance._recover_requests()
                
                mock_execute.assert_called_once()
                mock_recreate.assert_called_once_with(self.base_task_data)

    @pytest.mark.asyncio
    async def test_recover_requests_no_requests(self):
        """Test task recovery when no tasks exist"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.return_value = []
            
            await instance._recover_requests()
            
            mock_execute.assert_called_once()

    @pytest.mark.asyncio
    async def test_recover_requests_db_unavailable(self):
        """Test task recovery when database is unavailable"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        instance._db_available = False
        
        # Should not raise exception
        await instance._recover_requests()

    @pytest.mark.asyncio
    async def test_recover_requests_error_handling(self):
        """Test task recovery error handling"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager.execute_query', new_callable=AsyncMock) as mock_execute:
            mock_execute.side_effect = Exception("Database error")
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                await instance._recover_requests()
                
                mock_exception.assert_called_once()

    @pytest.mark.asyncio
    async def test_recreate_task_from_data_success(self):
        """Test successful task recreation from data"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_users.ZairaUserManager.get_instance') as mock_user_manager:
            mock_user_manager.return_value.find_user.return_value = MagicMock()
            
            with patch('userprofiles.ScheduledZairaRequest.ScheduledZairaRequest') as mock_request:
                mock_task = MagicMock()
                mock_request.return_value = mock_task
                
                await instance._recreate_task_from_data(self.base_task_data)
                
                # Verify task was created and stored
                assert self.test_guid in instance._active_requests

    @pytest.mark.asyncio
    async def test_recreate_task_from_data_user_not_found(self):
        """Test task recreation when user not found"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_users.ZairaUserManager.get_instance') as mock_user_manager:
            mock_user_manager.return_value.find_user.return_value = None
            
            await instance._recreate_task_from_data(self.base_task_data)
            
            # Task should be paused
            assert self.test_guid in instance._paused_requests

    @pytest.mark.asyncio
    async def test_recreate_task_from_data_error_handling(self):
        """Test task recreation error handling"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_users.ZairaUserManager.get_instance') as mock_user_manager:
            mock_user_manager.side_effect = Exception("User manager error")
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                await instance._recreate_task_from_data(self.base_task_data)
                
                mock_exception.assert_called_once()

    # ===== USER TASK MANAGEMENT TESTS =====

    def test_get_paused_requests_count(self):
        """Test getting paused tasks count"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        # Add paused tasks
        instance._paused_requests[self.test_guid] = self.base_task_data
        instance._paused_requests[str(uuid4())] = self.base_task_data.copy()
        
        count = instance.get_paused_requests_count()
        
        assert count == 2

    def test_get_paused_requests_count_empty(self):
        """Test getting paused tasks count when empty"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        instance._paused_requests = {}
        
        count = instance.get_paused_requests_count()
        
        assert count == 0

    def test_get_paused_requests_info(self):
        """Test getting paused tasks info"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        # Add paused tasks
        instance._paused_requests[self.test_guid] = self.base_task_data
        
        info = instance.get_paused_requests_info()
        
        assert len(info) == 1
        assert info[0]['scheduled_guid'] == self.test_guid

    def test_get_paused_requests_info_empty(self):
        """Test getting paused tasks info when empty"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        instance._paused_requests = {}
        
        info = instance.get_paused_requests_info()
        
        assert info == []

    # ===== TASK CLEANUP TESTS =====

    @pytest.mark.asyncio
    async def test_cleanup_old_requests_success(self):
        """Test successful old task cleanup"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(instance, '_execute_single_operation', new_callable=AsyncMock) as mock_execute:
            await instance.cleanup_old_requests(days_old=30)
            
            mock_execute.assert_called_once()
            # Verify the SQL contains DELETE
            call_args = mock_execute.call_args[0][0]
            assert 'DELETE' in call_args

    @pytest.mark.asyncio
    async def test_cleanup_old_requests_db_unavailable(self):
        """Test old task cleanup when database is unavailable"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        instance._db_available = False
        
        # Should not raise exception
        await instance.cleanup_old_requests(days_old=30)

    @pytest.mark.asyncio
    async def test_cleanup_old_requests_error_handling(self):
        """Test old task cleanup error handling"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        with patch.object(instance, '_execute_single_operation', new_callable=AsyncMock) as mock_execute:
            mock_execute.side_effect = Exception("Database error")
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                await instance.cleanup_old_requests(days_old=30)
                
                mock_exception.assert_called_once()

    # ===== ACTIVE TASK MANAGEMENT TESTS =====

    def test_get_active_task_success(self):
        """Test getting active task"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        # Add active task
        mock_task = MagicMock()
        instance._active_requests[self.test_guid] = mock_task
        
        result = instance.get_active_task(self.test_guid)
        
        assert result == mock_task

    def test_get_active_task_not_found(self):
        """Test getting active task when not found"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        instance._active_requests = {}
        
        result = instance.get_active_task(self.test_guid)
        
        assert result is None

    def test_get_all_active_requests(self):
        """Test getting all active tasks"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        # Add active tasks
        mock_task1 = MagicMock()
        mock_task2 = MagicMock()
        instance._active_requests[self.test_guid] = mock_task1
        instance._active_requests[str(uuid4())] = mock_task2
        
        result = instance.get_all_active_requests()
        
        assert len(result) == 2
        assert mock_task1 in result
        assert mock_task2 in result

    def test_get_all_active_requests_empty(self):
        """Test getting all active tasks when empty"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance = ScheduledRequestPersistenceManager.get_instance()
        
        instance._active_requests = {}
        
        result = instance.get_all_active_requests()
        
        assert result == []

    # ===== UTILITY FUNCTION TESTS =====

    @pytest.mark.asyncio
    async def test_get_persistence_manager(self):
        """Test getting persistence manager"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        result = ScheduledRequestPersistenceManager.get_instance()
        
        assert result is not None
        assert isinstance(result, ScheduledRequestPersistenceManager)

    @pytest.mark.asyncio
    async def test_get_persistence_manager_singleton(self):
        """Test that persistence manager is singleton"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        result1 = ScheduledRequestPersistenceManager.get_instance()
        result2 = ScheduledRequestPersistenceManager.get_instance()
        
        assert result1 is result2