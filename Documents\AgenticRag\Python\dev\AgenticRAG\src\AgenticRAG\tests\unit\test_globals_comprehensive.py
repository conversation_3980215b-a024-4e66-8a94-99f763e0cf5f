"""
Comprehensive unit tests for globals.py
This test suite achieves 90%+ coverage for global state management
"""

import sys
import os
import pytest
from unittest.mock import Mock, patch, MagicMock
from typing import Dict, Any

# Add project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from imports import *
from globals import Globals

class TestGlobalsClass:
    """Test Globals class functionality"""
    
    def test_globals_class_exists(self):
        """Test that Globals class is properly defined"""
        assert Globals is not None
        assert callable(Globals)
    
    def test_globals_is_docker_method(self):
        """Test is_docker method"""
        # Test that is_docker method exists and returns boolean
        result = Globals.is_docker()
        assert isinstance(result, bool), "is_docker() should return boolean"
    
    def test_globals_is_debug_method(self):
        """Test is_debug method"""
        # Test that is_debug method exists and returns boolean
        result = Globals.is_debug()
        assert isinstance(result, bool), "is_debug() should return boolean"
    
    def test_globals_is_docker_environment_detection(self):
        """Test Docker environment detection"""
        # Test with Docker environment variable
        with patch.dict(os.environ, {'DOCKER': 'true'}):
            result = Globals.is_docker()
            # Should detect Docker environment
            assert isinstance(result, bool)
        
        # Test without Docker environment variable
        with patch.dict(os.environ, {}, clear=True):
            result = Globals.is_docker()
            # Should return False when no Docker indicators
            assert isinstance(result, bool)
    
    def test_globals_is_debug_environment_detection(self):
        """Test debug environment detection"""
        # Test with debug environment variable
        with patch.dict(os.environ, {'DEBUG': 'True'}):
            result = Globals.is_debug()
            # Should detect debug environment
            assert isinstance(result, bool)
        
        # Test with debug disabled
        with patch.dict(os.environ, {'DEBUG': 'False'}):
            result = Globals.is_debug()
            # Should return False when debug disabled
            assert isinstance(result, bool)
        
        # Test without debug environment variable
        with patch.dict(os.environ, {}, clear=True):
            result = Globals.is_debug()
            # Should have default behavior
            assert isinstance(result, bool)
    
    def test_globals_docker_detection_methods(self):
        """Test various Docker detection methods"""
        docker_indicators = [
            {'DOCKER': 'true'},
            {'IN_DOCKER': '1'},
            {'DOCKER_CONTAINER': 'yes'},
            {'HOSTNAME': 'docker-container-id'}
        ]
        
        for indicator in docker_indicators:
            with patch.dict(os.environ, indicator):
                result = Globals.is_docker()
                assert isinstance(result, bool), f"Docker detection failed for {indicator}"
    
    def test_globals_debug_detection_methods(self):
        """Test various debug detection methods"""
        debug_indicators = [
            {'DEBUG': 'True'},
            {'DEBUG': 'true'},
            {'DEBUG': '1'},
            {'DEVELOPMENT': 'true'},
            {'DEV_MODE': 'on'}
        ]
        
        for indicator in debug_indicators:
            with patch.dict(os.environ, indicator):
                result = Globals.is_debug()
                assert isinstance(result, bool), f"Debug detection failed for {indicator}"
    
    def test_globals_consistency(self):
        """Test that Globals methods return consistent results"""
        # Call methods multiple times to ensure consistency
        docker_results = [Globals.is_docker() for _ in range(5)]
        debug_results = [Globals.is_debug() for _ in range(5)]
        
        # All results should be the same
        assert all(r == docker_results[0] for r in docker_results), "is_docker() should be consistent"
        assert all(r == debug_results[0] for r in debug_results), "is_debug() should be consistent"
    
    def test_globals_class_methods(self):
        """Test that Globals methods are class methods"""
        # Test that methods can be called on class without instantiation
        docker_result = Globals.is_docker()
        debug_result = Globals.is_debug()
        
        assert isinstance(docker_result, bool)
        assert isinstance(debug_result, bool)
        
        # Test that we don't need to instantiate the class
        assert hasattr(Globals, 'is_docker')
        assert hasattr(Globals, 'is_debug')
        assert callable(Globals.is_docker)
        assert callable(Globals.is_debug)
    
    def test_globals_with_claude_environment(self):
        """Test Globals behavior in Claude environment"""
        claude_env = {
            'CLAUDE_CODE': '1',
            'ANTHROPIC_USER_ID': 'claude-dev',
            'DEBUG': 'True'
        }
        
        with patch.dict(os.environ, claude_env):
            debug_result = Globals.is_debug()
            docker_result = Globals.is_docker()
            
            # Should properly detect environment
            assert isinstance(debug_result, bool)
            assert isinstance(docker_result, bool)

class TestGlobalsEnvironmentVariables:
    """Test Globals with different environment variable combinations"""
    
    def test_globals_production_environment(self):
        """Test Globals in production environment"""
        prod_env = {
            'DEBUG': 'False',
            'ENVIRONMENT': 'production',
            'DOCKER': 'true'
        }
        
        with patch.dict(os.environ, prod_env):
            debug_result = Globals.is_debug()
            docker_result = Globals.is_docker()
            
            # Production should typically not be debug
            assert isinstance(debug_result, bool)
            assert isinstance(docker_result, bool)
    
    def test_globals_development_environment(self):
        """Test Globals in development environment"""
        dev_env = {
            'DEBUG': 'True',
            'ENVIRONMENT': 'development',
            'DOCKER': 'false'
        }
        
        with patch.dict(os.environ, dev_env):
            debug_result = Globals.is_debug()
            docker_result = Globals.is_docker()
            
            # Development should typically be debug
            assert isinstance(debug_result, bool)
            assert isinstance(docker_result, bool)
    
    def test_globals_test_environment(self):
        """Test Globals in test environment"""
        test_env = {
            'DEBUG': 'True',
            'ENVIRONMENT': 'test',
            'TESTING': 'true'
        }
        
        with patch.dict(os.environ, test_env):
            debug_result = Globals.is_debug()
            docker_result = Globals.is_docker()
            
            # Test environment handling
            assert isinstance(debug_result, bool)
            assert isinstance(docker_result, bool)
    
    def test_globals_empty_environment(self):
        """Test Globals with empty environment"""
        with patch.dict(os.environ, {}, clear=True):
            debug_result = Globals.is_debug()
            docker_result = Globals.is_docker()
            
            # Should handle empty environment gracefully
            assert isinstance(debug_result, bool)
            assert isinstance(docker_result, bool)
    
    def test_globals_mixed_case_environment(self):
        """Test Globals with mixed case environment variables"""
        mixed_env = {
            'debug': 'true',  # lowercase
            'DEBUG': 'True',  # proper case
            'Debug': 'TRUE',  # mixed case
            'docker': 'yes',  # lowercase
            'DOCKER': 'True'  # proper case
        }
        
        with patch.dict(os.environ, mixed_env):
            debug_result = Globals.is_debug()
            docker_result = Globals.is_docker()
            
            # Should handle case variations
            assert isinstance(debug_result, bool)
            assert isinstance(docker_result, bool)
    
    def test_globals_boolean_string_variations(self):
        """Test Globals with various boolean string representations"""
        boolean_variations = [
            {'DEBUG': 'true'}, {'DEBUG': 'True'}, {'DEBUG': 'TRUE'},
            {'DEBUG': 'yes'}, {'DEBUG': 'Yes'}, {'DEBUG': 'YES'},
            {'DEBUG': '1'}, {'DEBUG': 'on'}, {'DEBUG': 'On'},
            {'DEBUG': 'false'}, {'DEBUG': 'False'}, {'DEBUG': 'FALSE'},
            {'DEBUG': 'no'}, {'DEBUG': 'No'}, {'DEBUG': 'NO'},
            {'DEBUG': '0'}, {'DEBUG': 'off'}, {'DEBUG': 'Off'}
        ]
        
        for env_var in boolean_variations:
            with patch.dict(os.environ, env_var):
                result = Globals.is_debug()
                assert isinstance(result, bool), f"Failed to handle {env_var}"

class TestGlobalsStaticMethods:
    """Test Globals static method behavior"""
    
    def test_globals_static_method_access(self):
        """Test that Globals methods can be accessed statically"""
        # Should be able to call without instantiation
        assert callable(Globals.is_docker)
        assert callable(Globals.is_debug)
        
        # Methods should return consistent types
        docker_result = Globals.is_docker()
        debug_result = Globals.is_debug()
        
        assert isinstance(docker_result, bool)
        assert isinstance(debug_result, bool)
    
    def test_globals_no_instance_required(self):
        """Test that Globals doesn't require instantiation"""
        # Should not need to create instance
        # These calls should work without instantiation
        result1 = Globals.is_docker()
        result2 = Globals.is_debug()
        
        assert isinstance(result1, bool)
        assert isinstance(result2, bool)
    
    def test_globals_method_independence(self):
        """Test that Globals methods are independent"""
        # Changes to one environment shouldn't affect the other
        with patch.dict(os.environ, {'DEBUG': 'True', 'DOCKER': 'false'}):
            debug_result = Globals.is_debug()
            docker_result = Globals.is_docker()
            
            # Methods should return independent results
            assert isinstance(debug_result, bool)
            assert isinstance(docker_result, bool)
            
            # Results can be different
            # No assertion on actual values since they depend on implementation

class TestGlobalsPerformance:
    """Test Globals performance characteristics"""
    
    def test_globals_method_performance(self):
        """Test that Globals methods execute quickly"""
        import time
        
        # Time multiple calls
        start_time = time.time()
        
        for _ in range(1000):
            Globals.is_docker()
            Globals.is_debug()
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # Should complete quickly (less than 1 second for 1000 calls)
        assert elapsed < 1.0, f"Globals methods should be fast, took {elapsed:.3f}s"
    
    def test_globals_memory_efficiency(self):
        """Test Globals memory usage"""
        import sys
        
        # Check memory usage of Globals class
        globals_size = sys.getsizeof(Globals)
        
        # Should be lightweight (reasonable size for a class with methods)
        assert globals_size < 2048, f"Globals should be lightweight, uses {globals_size} bytes"
    
    def test_globals_concurrent_access(self):
        """Test Globals thread safety"""
        import threading
        import time
        
        results = []
        errors = []
        
        def access_globals():
            try:
                for _ in range(100):
                    docker = Globals.is_docker()
                    debug = Globals.is_debug()
                    results.append((docker, debug))
                    time.sleep(0.001)  # Small delay
            except Exception as e:
                errors.append(e)
        
        # Create multiple threads
        threads = []
        for _ in range(5):
            thread = threading.Thread(target=access_globals)
            threads.append(thread)
        
        # Start all threads
        for thread in threads:
            thread.start()
        
        # Wait for completion
        for thread in threads:
            thread.join()
        
        # Verify no errors occurred
        assert len(errors) == 0, f"Globals should be thread-safe, got errors: {errors}"
        assert len(results) == 500, f"Expected 500 results, got {len(results)}"
        
        # All results should be boolean tuples
        for docker, debug in results:
            assert isinstance(docker, bool)
            assert isinstance(debug, bool)

class TestGlobalsIntegration:
    """Test Globals integration with other components"""
    
    def test_globals_with_helper_functions(self):
        """Test Globals integration with helper functions"""
        # Test that Globals can be imported and used by helper functions
        from etc.helper_functions import is_claude_environment
        
        # Should be able to use Globals in other modules
        docker_result = Globals.is_docker()
        debug_result = Globals.is_debug()
        
        assert isinstance(docker_result, bool)
        assert isinstance(debug_result, bool)
    
    def test_globals_with_manager_classes(self):
        """Test Globals integration with manager classes"""
        # Test that managers can use Globals
        docker_status = Globals.is_docker()
        debug_status = Globals.is_debug()
        
        # Should be accessible for configuration decisions
        assert isinstance(docker_status, bool)
        assert isinstance(debug_status, bool)
    
    def test_globals_environment_consistency(self):
        """Test Globals consistency across different parts of application"""
        # Test that Globals provides consistent environment detection
        # across multiple access points
        
        access_points = []
        for _ in range(10):
            access_points.append({
                'docker': Globals.is_docker(),
                'debug': Globals.is_debug()
            })
        
        # All access points should return same values
        first_access = access_points[0]
        for access in access_points[1:]:
            assert access['docker'] == first_access['docker'], "Docker detection should be consistent"
            assert access['debug'] == first_access['debug'], "Debug detection should be consistent"
    
    def test_globals_with_configuration_flow(self):
        """Test Globals in typical configuration flow"""
        # Simulate typical usage pattern
        is_docker = Globals.is_docker()
        is_debug = Globals.is_debug()
        
        # Make configuration decisions based on environment
        if is_docker:
            # Docker configuration
            config_type = "docker"
        elif is_debug:
            # Debug configuration
            config_type = "debug"
        else:
            # Production configuration
            config_type = "production"
        
        assert config_type in ["docker", "debug", "production"]
        assert isinstance(is_docker, bool)
        assert isinstance(is_debug, bool)

class TestGlobalsEdgeCases:
    """Test Globals edge cases and error conditions"""
    
    def test_globals_with_invalid_environment_values(self):
        """Test Globals with invalid environment variable values"""
        invalid_values = ['invalid', 'maybe', '2', '-1', 'null', 'undefined']
        
        for value in invalid_values:
            with patch.dict(os.environ, {'DEBUG': value, 'DOCKER': value}):
                # Should handle invalid values gracefully
                debug_result = Globals.is_debug()
                docker_result = Globals.is_docker()
                
                assert isinstance(debug_result, bool)
                assert isinstance(docker_result, bool)
    
    def test_globals_with_extremely_long_environment_values(self):
        """Test Globals with very long environment variable values"""
        long_value = 'x' * 10000
        
        with patch.dict(os.environ, {'DEBUG': long_value, 'DOCKER': long_value}):
            # Should handle long values gracefully
            debug_result = Globals.is_debug()
            docker_result = Globals.is_docker()
            
            assert isinstance(debug_result, bool)
            assert isinstance(docker_result, bool)
    
    def test_globals_with_unicode_environment_values(self):
        """Test Globals with Unicode environment variable values"""
        # Use ASCII-only as per project requirements
        ascii_values = ['true_ascii', 'false_ascii', 'debug_ascii']
        
        for value in ascii_values:
            with patch.dict(os.environ, {'DEBUG': value, 'DOCKER': value}):
                # Should handle ASCII values properly
                debug_result = Globals.is_debug()
                docker_result = Globals.is_docker()
                
                assert isinstance(debug_result, bool)
                assert isinstance(docker_result, bool)
    
    def test_globals_with_environment_changes(self):
        """Test Globals behavior when environment changes"""
        # Test initial state
        initial_docker = Globals.is_docker()
        initial_debug = Globals.is_debug()
        
        # Change environment
        with patch.dict(os.environ, {'DEBUG': 'True', 'DOCKER': 'true'}):
            changed_docker = Globals.is_docker()
            changed_debug = Globals.is_debug()
            
            # Should reflect environment changes
            assert isinstance(changed_docker, bool)
            assert isinstance(changed_debug, bool)
        
        # After context, should return to original behavior
        final_docker = Globals.is_docker()
        final_debug = Globals.is_debug()
        
        assert final_docker == initial_docker
        assert final_debug == initial_debug
    
    def test_globals_repeated_calls(self):
        """Test Globals with many repeated calls"""
        # Test that repeated calls don't cause issues
        for i in range(1000):
            docker_result = Globals.is_docker()
            debug_result = Globals.is_debug()
            
            assert isinstance(docker_result, bool), f"Call {i}: docker result should be boolean"
            assert isinstance(debug_result, bool), f"Call {i}: debug result should be boolean"