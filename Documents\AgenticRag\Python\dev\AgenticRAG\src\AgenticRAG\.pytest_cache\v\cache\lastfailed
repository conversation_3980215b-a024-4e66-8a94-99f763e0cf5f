{"tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_multimodal_chunking_workflow": true, "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_end_to_end_storage_workflow": true, "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_multimodal_search_workflow": true, "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_meltano_multimodal_integration": true, "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_asset_management_workflow": true, "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_vision_model_integration": true, "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_table_summarization_workflow": true, "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_user_not_found_error": true, "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_start_action_success": true, "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_stop_action_success": true, "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_status_action_success": true, "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_restart_action_success": true, "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_invalid_action_error": true, "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_scheduler_exception_handling": true, "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_general_exception_handling": true, "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_supervisor_task_state_integration": true, "tests/integration/test_whatsapp_workflow.py::TestWhatsAppWorkflow::test_webhook_verification_workflow": true, "tests/unit/test_mybot_testing_coverage.py": true, "tests/unit/test_mybot_testing_extended.py": true, "tests/unit/test_mybot_testing_final.py": true, "tests/unit/test_agenda_oauth_scopes.py::TestAgendaOAuthScopes::test_agenda_sender_tool_uses_filtered_scopes": true, "tests/unit/test_excel_analyzer_integration.py::TestExcelAnalyzerIntegration::test_excel_analyzer_tool_creation": true, "tests/integration/test_call_trace_suite.py": true, "tests/unit/test_etc_helper_functions_comprehensive.py": true, "tests/unit/test_sql_excel_analyzer.py": true, "tests/unit/test_sql_excel_analyzer_output.py": true, "tests/test_real/batch_wrappers/test_batch_agenda.py::TestBatchAgenda": true, "tests/test_real/batch_wrappers/test_batch_bot.py::TestBatchBot": true, "tests/test_real/batch_wrappers/test_batch_data.py::TestBatchData": true, "tests/test_real/batch_wrappers/test_batch_email.py::TestBatchEmail": true, "tests/test_real/batch_wrappers/test_batch_execution_suite.py::TestBatchExecutionSuite": true, "tests/test_real/batch_wrappers/test_batch_imap_smtp_broadcast.py::TestBatchImapSmtpBroadcast": true, "tests/test_real/batch_wrappers/test_batch_smtp_with_bot.py::TestBatchSmtpWithBot": true, "tests/unit/test_profile_endpoint.py::AioHTTPTestCase": true, "tests/unit/test_profile_endpoint.py::TestProfileEndpoint": true, "tests/unit/test_profile_endpoint.py::TestProfileEndpointHelpers": true, "tests/unit/test_profile_endpoint.py::TestZairaUserProfileFields": true, "quick_excel_test.py": true, "test_excel_analyzer_manual.py": true, "test_excel_analyzer_task.py": true, "test_excel_integration.py": true, "test_startup_fix.py": true, "tests/unit/test_profile_endpoint.py": true, "tests/unit/test_excel_analyzer_integration.py::TestExcelAnalyzerIntegration::test_excel_analyzer_supervisor_creation": true, "tests/unit/test_excel_analyzer_integration.py::TestExcelAnalyzerIntegration::test_excel_analyzer_tool_with_sample_data": true, "tests/unit/test_excel_analyzer_integration.py::TestExcelAnalyzerIntegration::test_oauth_verifier_integration": true, "tests/integration/email_agenda/test_agenda_calendar_integration.py::TestAgendaCalendarIntegration::test_create_supervisor_agenda_planner_integration": true, "tests/integration/email_agenda/test_agenda_calendar_integration.py::TestAgendaCalendarIntegration::test_calendar_tools_oauth_integration": true, "tests/integration/email_agenda/test_agenda_calendar_integration.py::TestAgendaCalendarIntegration::test_calendar_imports_unavailable_fallback": true, "tests/integration/email_agenda/test_agenda_calendar_integration.py::TestAgendaCalendarIntegration::test_format_event_preview_comprehensive": true, "tests/integration/email_agenda/test_agenda_calendar_integration.py::TestAgendaCalendarIntegration::test_agenda_supervisor_workflow_integration": true, "tests/integration/email_agenda/test_agenda_calendar_integration.py::TestAgendaCalendarIntegration::test_agenda_calendar_error_handling_integration": true, "tests/integration/email_agenda/test_agenda_calendar_integration.py::TestAgendaCalendarIntegration::test_agenda_tool_execution_with_state_persistence": true, "tests/integration/email_agenda/test_agenda_calendar_integration.py::TestAgendaCalendarIntegration::test_calendar_tool_creation_patterns": true, "tests/integration/email_agenda/test_agenda_calendar_integration.py::TestAgendaCalendarIntegration": true, "tests/integration/test_chat_session_switching.py::TestChatSessionSwitching::test_complete_chat_session_switching_workflow": true, "tests/integration/test_chat_session_switching.py::TestChatSessionSwitching::test_chat_session_isolation": true, "tests/integration/test_chat_session_switching.py::TestChatSessionSwitching::test_chat_session_persistence_across_switches": true, "tests/integration/test_chat_session_switching.py::TestChatSessionSwitching::test_chat_session_edge_cases": true, "tests/integration/test_chat_session_switching.py::TestChatSessionIntegrationWithSupervisors::test_chat_session_with_supervisor_workflow": true, "tests/integration/test_chat_session_switching.py::TestChatSessionSwitching": true, "tests/integration/test_chat_session_switching.py::TestChatSessionIntegrationWithSupervisors": true, "tests/integration/test_email_output_routing.py::TestEmailOutputRouting::test_email_generator_adds_output_demands": true, "tests/integration/test_email_output_routing.py::TestEmailOutputRouting::test_email_output_routing_integration": true, "tests/integration/test_email_output_routing.py::TestEmailOutputRouting::test_email_routing_without_approval": true, "tests/integration/test_email_output_routing.py::TestEmailOutputRouting::test_output_demands_unique_entries": true, "tests/integration/test_email_output_routing.py::TestEmailOutputRouting": true, "tests/integration/test_end_to_end_system_integration.py::TestEndToEndSystemIntegration::test_complete_user_interaction_workflow": true, "tests/integration/test_end_to_end_system_integration.py::TestEndToEndSystemIntegration::test_multi_platform_communication_integration": true, "tests/integration/test_end_to_end_system_integration.py::TestEndToEndSystemIntegration::test_comprehensive_database_integration": true, "tests/integration/test_end_to_end_system_integration.py::TestEndToEndSystemIntegration": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateIntegration::test_task_initialization": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateIntegration::test_task_user_not_found": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateIntegration::test_task_30_minute_session_mock": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateWorkflow::test_create_task_imap_idle_activate": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateWorkflow::test_start_30_minute_imap_session": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateSystemIntegration::test_task_conforms_to_supervisor_interface": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateSystemIntegration::test_task_error_handling": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateSystemIntegration::test_task_integration_with_existing_patterns": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateSystemIntegration::test_supervisor_task_state_compatibility": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateCompatibility::test_compatibility_with_supervisor_manager": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateCompatibility::test_compatibility_with_existing_imap_infrastructure": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateCompatibility::test_integration_with_user_management": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateIntegration": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateWorkflow": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateSystemIntegration": true, "tests/integration/test_imap_idle_tool_integration.py::TestIMAPIdleActivateCompatibility": true, "tests/integration/test_message_processing_workflow.py::TestMessageProcessingWorkflow::test_discord_message_to_supervisor_workflow": true, "tests/integration/test_message_processing_workflow.py::TestMessageProcessingWorkflow::test_message_with_reply_context_workflow": true, "tests/integration/test_message_processing_workflow.py::TestMessageProcessingWorkflow::test_message_with_attachments_workflow": true, "tests/integration/test_message_processing_workflow.py::TestMessageProcessingWorkflow::test_user_creation_workflow": true, "tests/integration/test_message_processing_workflow.py::TestMessageProcessingWorkflow::test_private_message_workflow": true, "tests/integration/test_message_processing_workflow.py::TestMessageProcessingWorkflow::test_message_filtering_workflow": true, "tests/integration/test_message_processing_workflow.py::TestSupervisorWorkflow::test_supervisor_task_routing_workflow": true, "tests/integration/test_message_processing_workflow.py::TestSupervisorWorkflow::test_supervisor_error_handling_workflow": true, "tests/integration/test_message_processing_workflow.py::TestSupervisorWorkflow::test_supervisor_task_not_found_workflow": true, "tests/integration/test_message_processing_workflow.py::TestEndToEndWorkflow::test_complete_message_to_response_workflow": true, "tests/integration/test_message_processing_workflow.py::TestMessageProcessingWorkflow": true, "tests/integration/test_message_processing_workflow.py::TestSupervisorWorkflow": true, "tests/integration/test_message_processing_workflow.py::TestEndToEndWorkflow": true, "tests/integration/test_multi_user_session_workflow.py::TestMultiUserSessionWorkflow::test_multi_user_registration_workflow": true, "tests/integration/test_multi_user_session_workflow.py::TestMultiUserSessionWorkflow::test_multi_user_session_creation_workflow": true, "tests/integration/test_multi_user_session_workflow.py::TestMultiUserSessionWorkflow::test_concurrent_user_message_processing_workflow": true, "tests/integration/test_multi_user_session_workflow.py::TestMultiUserSessionWorkflow::test_user_session_isolation_workflow": true, "tests/integration/test_multi_user_session_workflow.py::TestMultiUserSessionWorkflow::test_user_permission_workflow": true, "tests/integration/test_multi_user_session_workflow.py::TestMultiUserSessionWorkflow::test_user_session_cleanup_workflow": true, "tests/integration/test_multi_user_session_workflow.py::TestMultiUserSessionWorkflow::test_user_load_balancing_workflow": true, "tests/integration/test_multi_user_session_workflow.py::TestMultiUserSessionWorkflow::test_user_state_consistency_workflow": true, "tests/integration/test_multi_user_session_workflow.py::TestUserSessionIntegration::test_user_session_with_chat_management_integration": true, "tests/integration/test_multi_user_session_workflow.py::TestUserSessionIntegration::test_user_session_with_supervisor_integration": true, "tests/integration/test_multi_user_session_workflow.py::TestMultiUserSessionWorkflow": true, "tests/integration/test_multi_user_session_workflow.py::TestUserSessionIntegration": true, "tests/integration/test_rag_retrieval_workflow.py::TestRAGRetrievalWorkflow::test_rag_search_workflow": true, "tests/integration/test_rag_retrieval_workflow.py::TestRAGRetrievalWorkflow::test_rag_embedding_workflow": true, "tests/integration/test_rag_retrieval_workflow.py::TestRAGRetrievalWorkflow::test_rag_context_retrieval_workflow": true, "tests/integration/test_rag_retrieval_workflow.py::TestRAGRetrievalWorkflow::test_rag_response_generation_workflow": true, "tests/integration/test_rag_retrieval_workflow.py::TestRAGRetrievalWorkflow::test_rag_quick_search_task_workflow": true, "tests/integration/test_rag_retrieval_workflow.py::TestRAGRetrievalWorkflow::test_rag_llm_search_task_workflow": true, "tests/integration/test_rag_retrieval_workflow.py::TestRAGRetrievalWorkflow::test_rag_retrieval_supervisor_workflow": true, "tests/integration/test_rag_retrieval_workflow.py::TestRAGRetrievalWorkflow::test_rag_end_to_end_workflow": true, "tests/integration/test_rag_retrieval_workflow.py::TestRAGRetrievalWorkflow::test_rag_error_handling_workflow": true, "tests/integration/test_rag_retrieval_workflow.py::TestRAGRetrievalWorkflow::test_rag_performance_workflow": true, "tests/integration/test_rag_retrieval_workflow.py::TestRAGDataManagement::test_rag_data_ingestion_workflow": true, "tests/integration/test_rag_retrieval_workflow.py::TestRAGDataManagement::test_rag_collection_management_workflow": true, "tests/integration/test_rag_retrieval_workflow.py::TestRAGRetrievalWorkflow": true, "tests/integration/test_rag_retrieval_workflow.py::TestRAGDataManagement": true, "tests/integration/test_scheduled_request_manager_integration.py::TestScheduledRequestManagerIntegration::test_create_task_workflow": true, "tests/integration/test_scheduled_request_manager_integration.py::TestScheduledRequestManagerIntegration::test_list_requests_workflow": true, "tests/integration/test_scheduled_request_manager_integration.py::TestScheduledRequestManagerIntegration::test_cancel_task_workflow": true, "tests/integration/test_scheduled_request_manager_integration.py::TestScheduledRequestManagerIntegration::test_info_request_workflow": true, "tests/integration/test_scheduled_request_manager_integration.py::TestScheduledRequestManagerIntegration::test_intelligent_routing_without_hardcoded_patterns": true, "tests/integration/test_scheduled_request_manager_integration.py::TestScheduledRequestManagerIntegration::test_state_preservation_across_requests": true, "tests/integration/test_scheduled_request_manager_integration.py::TestScheduledRequestManagerIntegration::test_error_handling_integration": true, "tests/integration/test_scheduled_request_manager_integration.py::TestScheduledRequestManagerIntegration::test_concurrent_request_handling": true, "tests/integration/test_scheduled_request_manager_integration.py::TestScheduledRequestManagerIntegration::test_manager_setup_integration": true, "tests/integration/test_scheduled_request_manager_integration.py::TestScheduledRequestManagerIntegration::test_persistence_manager_integration": true, "tests/integration/test_scheduled_request_manager_integration.py::TestScheduledRequestManagerIntegration::test_full_lifecycle_simulation": true, "tests/integration/test_scheduled_request_manager_integration.py::TestScheduledRequestManagerIntegration": true, "tests/integration/test_scheduled_request_system_integration.py::TestScheduledRequestSystemIntegration::test_complete_system_initialization": true, "tests/integration/test_scheduled_request_system_integration.py::TestScheduledRequestSystemIntegration::test_end_to_end_request_creation_and_cancellation": true, "tests/integration/test_scheduled_request_system_integration.py::TestScheduledRequestSystemIntegration::test_security_validation_integration": true, "tests/integration/test_scheduled_request_system_integration.py::TestScheduledRequestSystemIntegration::test_quota_enforcement_integration": true, "tests/integration/test_scheduled_request_system_integration.py::TestScheduledRequestSystemIntegration::test_rate_limiting_integration": true, "tests/integration/test_scheduled_request_system_integration.py::TestScheduledRequestSystemIntegration::test_multi_user_isolation": true, "tests/integration/test_scheduled_request_system_integration.py::TestScheduledRequestSystemIntegration::test_system_cleanup_and_shutdown": true, "tests/integration/test_scheduled_request_system_integration.py::TestScheduledRequestSystemIntegration::test_error_recovery_and_resilience": true, "tests/integration/test_scheduled_request_system_integration.py::TestScheduledRequestSystemIntegration::test_concurrent_request_handling": true, "tests/integration/test_scheduled_request_system_integration.py::TestScheduledRequestSystemIntegration::test_system_metrics_and_monitoring": true, "tests/integration/test_scheduled_request_system_integration.py::TestScheduledRequestSystemIntegration": true, "tests/integration/test_scheduled_request_workflow.py::TestScheduledRequestWorkflow::test_scheduled_request_creation_workflow": true, "tests/integration/test_scheduled_request_workflow.py::TestScheduledRequestWorkflow::test_scheduled_request_retrieval_workflow": true, "tests/integration/test_scheduled_request_workflow.py::TestScheduledRequestWorkflow::test_scheduled_request_execution_workflow": true, "tests/integration/test_scheduled_request_workflow.py::TestScheduledRequestWorkflow::test_scheduled_request_completion_workflow": true, "tests/integration/test_scheduled_request_workflow.py::TestScheduledRequestWorkflow::test_scheduled_request_error_handling_workflow": true, "tests/integration/test_scheduled_request_workflow.py::TestScheduledRequestWorkflow::test_scheduled_request_retry_workflow": true, "tests/integration/test_scheduled_request_workflow.py::TestScheduledRequestWorkflow::test_scheduled_request_cleanup_workflow": true, "tests/integration/test_scheduled_request_workflow.py::TestScheduledRequestWorkflow::test_scheduled_request_priority_workflow": true, "tests/integration/test_scheduled_request_workflow.py::TestScheduledRequestWorkflow::test_scheduled_request_concurrent_execution_workflow": true, "tests/integration/test_scheduled_request_workflow.py::TestScheduledRequestIntegration::test_scheduled_request_with_user_management_integration": true, "tests/integration/test_scheduled_request_workflow.py::TestScheduledRequestIntegration::test_scheduled_request_with_supervisor_integration": true, "tests/integration/test_scheduled_request_workflow.py::TestScheduledRequestWorkflow": true, "tests/integration/test_scheduled_request_workflow.py::TestScheduledRequestIntegration": true, "tests/integration/test_supervisor_integration_suite.py::TestSupervisorIntegrationSuite::test_supervisor_execution_flow_with_task_ordering": true, "tests/integration/test_supervisor_integration_suite.py::TestSupervisorIntegrationSuite::test_supervisor_task_coordination_with_failures": true, "tests/integration/test_supervisor_integration_suite.py::TestSupervisorIntegrationSuite::test_supervisor_task_conditional_execution": true, "tests/integration/test_supervisor_integration_suite.py::TestSupervisorIntegrationSuite::test_top_level_supervisor_task_routing": true, "tests/integration/test_supervisor_integration_suite.py::TestSupervisorIntegrationSuite::test_multi_agent_coordination_workflow": true, "tests/integration/test_supervisor_integration_suite.py::TestSupervisorIntegrationSuite::test_supervisor_chain_of_thought_workflow": true, "tests/integration/test_supervisor_integration_suite.py::TestSupervisorIntegrationSuite::test_supervisor_task_registration_and_discovery": true, "tests/integration/test_supervisor_integration_suite.py::TestSupervisorIntegrationSuite::test_supervisor_task_state_management": true, "tests/integration/test_supervisor_integration_suite.py::TestSupervisorIntegrationSuite::test_comprehensive_supervisor_integration_workflow": true, "tests/integration/test_supervisor_integration_suite.py::TestSupervisorIntegrationSuite": true, "tests/integration/test_whatsapp_workflow.py::TestWhatsAppWorkflow::test_complete_message_workflow": true, "tests/integration/test_whatsapp_workflow.py::TestWhatsAppWorkflow::test_new_user_registration_workflow": true, "tests/integration/test_whatsapp_workflow.py::TestWhatsAppWorkflow::test_message_response_workflow": true, "tests/integration/test_whatsapp_workflow.py::TestWhatsAppWorkflow::test_long_message_splitting_workflow": true, "tests/integration/test_whatsapp_workflow.py::TestWhatsAppWorkflow::test_error_handling_workflow": true, "tests/integration/test_whatsapp_workflow.py::TestWhatsAppWorkflow::test_concurrent_message_processing": true, "tests/integration/test_whatsapp_workflow.py::TestWhatsAppWorkflow::test_message_types_workflow": true, "tests/integration/test_whatsapp_workflow.py::TestWhatsAppWorkflow": true, "tests/test_real/test_bot_testing.py::TestBotTesting::test_bot_testing_responses": true, "tests/test_real/test_bot_testing.py::TestBotTesting::test_bot_fallback_responses": true, "tests/test_real/test_bot_testing.py::TestBotTesting::test_bot_name_behavior": true, "tests/test_real/test_bot_testing.py::TestBotTesting::test_bot_integration_with_user_message": true, "tests/test_real/test_real_agenda.py::TestRealAgenda::test_basic_agenda_planning": true, "tests/test_real/test_real_agenda.py::TestRealAgenda::test_comprehensive_agenda_generation": true, "tests/test_real/test_real_agenda.py::TestRealAgenda::test_calendar_scheduling_integration": true, "tests/test_real/test_real_agenda.py::TestRealAgenda::test_agenda_pipeline_verification": true, "tests/test_real/test_real_agenda.py::TestRealAgenda::test_meeting_types_handling": true, "tests/test_real/test_real_agenda.py::TestRealAgenda::test_calendar_integration_full_workflow": true, "tests/test_real/test_real_agenda.py::TestRealAgenda::test_agenda_content_optimization": true, "tests/test_real/test_real_agenda.py::TestRealAgenda::test_agenda_error_handling": true, "tests/test_real/test_real_bot.py::TestRealBot::test_automated_bot_responses": true, "tests/test_real/test_real_bot.py::TestRealBot::test_bot_fallback_responses": true, "tests/test_real/test_real_bot.py::TestRealBot::test_human_in_the_loop_automation": true, "tests/test_real/test_real_bot.py::TestRealBot::test_bot_name_behavior": true, "tests/test_real/test_real_bot.py::TestRealBot::test_bot_error_handling": true, "tests/test_real/test_real_bot.py::TestRealBot::test_bot_integration_with_workflows": true, "tests/test_real/test_real_bot.py::TestRealBot::test_bot_response_consistency": true, "tests/test_real/test_real_bot.py::TestRealBot::test_bot_multi_language_support": true, "tests/test_real/test_real_bot.py::TestRealBot::test_bot_performance_metrics": true, "tests/test_real/test_real_data.py::TestRealData::test_tell_me_about_your_data_basic": true, "tests/test_real/test_real_data.py::TestRealData::test_data_source_analysis": true, "tests/test_real/test_real_data.py::TestRealData::test_knowledge_base_exploration": true, "tests/test_real/test_real_data.py::TestRealData::test_data_pipeline_verification": true, "tests/test_real/test_real_data.py::TestRealData::test_data_quality_assessment": true, "tests/test_real/test_real_data.py::TestRealData::test_document_type_analysis": true, "tests/test_real/test_real_data.py::TestRealData::test_information_synthesis": true, "tests/test_real/test_real_data.py::TestRealData::test_data_retrieval_accuracy": true, "tests/test_real/test_real_data.py::TestRealData::test_data_analysis_error_handling": true, "tests/test_real/test_real_data.py::TestRealData::test_data_context_awareness": true, "tests/test_real/test_real_execution_suite.py::TestRealExecutionSuite::test_real_email_agenda_coordination_execution": true, "tests/test_real/test_real_execution_suite.py::TestRealExecutionSuite::test_real_email_agenda_components_validation": true, "tests/test_real/test_real_execution_suite.py::TestRealExecutionSuite::test_real_meeting_invitation_workflow": true, "tests/test_real/test_real_execution_suite.py::TestRealExecutionSuite::test_real_agenda_execution_call_trace": true, "tests/test_real/test_real_execution_suite.py::TestRealExecutionSuite::test_real_agenda_components_validation": true, "tests/test_real/test_real_execution_suite.py::TestRealExecutionSuite::test_real_detailed_planning_workflow": true, "tests/test_real/test_real_execution_suite.py::TestRealExecutionSuite::test_real_email_sending_execution": true, "tests/test_real/test_real_execution_suite.py::TestRealExecutionSuite::test_real_email_routing_validation": true, "tests/test_real/test_real_execution_suite.py::TestRealExecutionSuite::test_real_tell_me_about_data_execution": true, "tests/test_real/test_real_execution_suite.py::TestRealExecutionSuite::test_real_data_analysis_components_validation": true, "tests/test_real/test_real_execution_suite.py::TestRealExecutionSuite::test_real_execution_performance_analysis": true, "tests/test_real/test_real_execution_suite.py::TestRealExecutionSuite::test_real_response_quality_analysis": true, "tests/test_real/test_real_execution_suite.py::TestRealExecutionSuite::test_comprehensive_real_execution_suite": true, "tests/test_real/test_real_imap_smtp_broadcast.py::TestRealIMAPSMTPBroadcast::test_full_imap_smtp_broadcast_integration": true, "tests/test_real/test_real_imap_smtp_broadcast.py::TestRealIMAPSMTPBroadcast::test_imap_broadcast_without_smtp": true, "tests/test_real/test_real_smtp_with_bot.py::TestRealSMTPWithBot::test_smtp_email_with_automated_bot": true, "tests/test_real/test_real_smtp_with_bot.py::TestRealSMTPWithBot::test_verify_email_routing_pipeline": true, "tests/test_real/test_smtp_simple.py::TestSMTPSimple::test_smtp_email_sending_full_pipeline": true, "tests/test_real/base_real_test.py": true, "tests/test_real/batch_wrappers": true, "tests/test_real/bats": true, "tests/test_real/bge_onnx": true, "tests/test_real/helpers": true, "tests/test_real/logs": true, "tests/test_real/test_bot_testing.py": true, "tests/test_real/test_debug_capture.py": true, "tests/test_real/test_real_agenda.py": true, "tests/test_real/test_real_bot.py": true, "tests/test_real/test_real_data.py": true, "tests/test_real/test_real_email.py": true, "tests/test_real/test_real_execution_suite.py": true, "tests/test_real/test_real_imap_smtp_broadcast.py": true, "tests/test_real/test_real_smtp_with_bot.py": true, "tests/test_real/test_smtp_simple.py": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_singleton_pattern": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_initialization": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_setup": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_start_app": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_late_setup": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_home_endpoint": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_login_endpoint": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_validate_login_success": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_validate_login_failure": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_dashboard_endpoint": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_dashboard_unauthorized": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_ask_endpoint": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_ask_url_endpoint": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_ask_delayed_endpoint": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_ask_delayed_missing_params": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_slack_events_verification": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_slack_events_normal": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_embedding_openai_success": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_embedding_openai_invalid_input": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_embedding_onnx_success": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_embedding_onnx_missing_input": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_handle_file_upload_success": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_handle_file_upload_no_files": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_convert_sql_to_vectorstore": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_restart_endpoint_success": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_restart_endpoint_unauthorized": true, "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_load_content_dashboard": true, "tests/unit/test_api_endpoint.py::TestAPIEndpointMiddleware::test_logfire_middleware": true, "tests/unit/test_api_endpoint.py::TestAPIEndpointMiddleware::test_ip_check_middleware": true, "tests/unit/test_api_endpoint.py::TestAPIEndpointMiddleware::test_ip_check_middleware_no_cf_header": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordClient::test_on_ready": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordClient::test_on_message": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordClient::test_on_message_bot_user": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordClient::test_on_member_join": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordClient::test_on_member_leave": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordClient::test_on_guild_join": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_singleton_pattern": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_initialization": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_setup_debug_mode": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_setup_production_mode": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_setup_no_token": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_ready": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_member_join": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_member_join_wrong_guild": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_message_processing": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_message_with_attachments": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_message_with_reply": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_message_wrong_guild": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_message_no_exclamation": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_send_discord_broadcast": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_send_discord_broadcast_no_client": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_send_a_discord_message": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_run_discord_bot_internal_no_client": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_run_discord_bot_internal_with_client": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBotThreading::test_threading_setup": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBotThreading::test_exception_handling": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBotEdgeCases::test_user_creation_on_message": true, "tests/unit/test_discord_endpoint.py::TestMyDiscordBotEdgeCases::test_private_message_handling": true, "tests/unit/test_discord_reply_functionality.py::TestDiscordReplyFunctionality::test_reply_context_creation_with_reply": true, "tests/unit/test_discord_reply_functionality.py::TestDiscordReplyFunctionality::test_reply_context_creation_without_reply": true, "tests/unit/test_discord_reply_functionality.py::TestDiscordReplyFunctionality::test_mybot_generic_handles_reply_context": true, "tests/unit/test_discord_reply_functionality.py::TestDiscordReplyFunctionality::test_reply_context_message_formatting": true, "tests/unit/test_discord_reply_functionality.py::TestDiscordReplyFunctionality::test_no_reply_context_fallback": true, "tests/unit/test_discord_reply_functionality.py::TestDiscordReplyFunctionality::test_reply_context_class_methods": true, "tests/unit/test_endpoints_comprehensive.py::TestMyDiscordBot::test_discord_bot_singleton": true, "tests/unit/test_endpoints_comprehensive.py::TestMyDiscordBot::test_discord_bot_initialization": true, "tests/unit/test_endpoints_comprehensive.py::TestMyDiscordBot::test_discord_bot_setup": true, "tests/unit/test_endpoints_comprehensive.py::TestMyDiscordBot::test_discord_bot_setup_idempotent": true, "tests/unit/test_endpoints_comprehensive.py::TestMyDiscordBot::test_discord_bot_setup_with_string_guild_id": true, "tests/unit/test_endpoints_comprehensive.py::TestMyDiscordBot::test_discord_bot_setup_docker_environment": true, "tests/unit/test_endpoints_comprehensive.py::TestMyDiscordBot::test_discord_on_ready": true, "tests/unit/test_endpoints_comprehensive.py::TestMyDiscordBot::test_discord_on_message": true, "tests/unit/test_endpoints_comprehensive.py::TestMyDiscordBot::test_discord_on_message_from_bot": true, "tests/unit/test_endpoints_comprehensive.py::TestMyDiscordBot::test_discord_on_message_wrong_guild": true, "tests/unit/test_endpoints_comprehensive.py::TestMyDiscordBot::test_discord_on_member_join": true, "tests/unit/test_endpoints_comprehensive.py::TestMyDiscordBot::test_discord_on_member_leave": true, "tests/unit/test_endpoints_comprehensive.py::TestMyDiscordBot::test_discord_on_guild_join": true, "tests/unit/test_endpoints_comprehensive.py::TestMyDiscordBot::test_discord_client_events": true, "tests/unit/test_endpoints_comprehensive.py::TestAPIEndpoint::test_api_endpoint_singleton": true, "tests/unit/test_endpoints_comprehensive.py::TestAPIEndpoint::test_api_endpoint_initialization": true, "tests/unit/test_endpoints_comprehensive.py::TestAPIEndpoint::test_api_endpoint_setup": true, "tests/unit/test_endpoints_comprehensive.py::TestAPIEndpoint::test_api_endpoint_setup_idempotent": true, "tests/unit/test_endpoints_comprehensive.py::TestAPIEndpoint::test_api_endpoint_handle_request": true, "tests/unit/test_endpoints_comprehensive.py::TestAPIEndpoint::test_api_endpoint_handle_request_error": true, "tests/unit/test_endpoints_comprehensive.py::TestAPIEndpoint::test_api_endpoint_handle_invalid_json": true, "tests/unit/test_endpoints_comprehensive.py::TestMyWhatsappBot::test_whatsapp_endpoint_singleton": true, "tests/unit/test_endpoints_comprehensive.py::TestMyWhatsappBot::test_whatsapp_endpoint_initialization": true, "tests/unit/test_endpoints_comprehensive.py::TestMyWhatsappBot::test_whatsapp_endpoint_setup": true, "tests/unit/test_endpoints_comprehensive.py::TestMyWhatsappBot::test_whatsapp_webhook_verification": true, "tests/unit/test_endpoints_comprehensive.py::TestMyWhatsappBot::test_whatsapp_webhook_verification_failed": true, "tests/unit/test_endpoints_comprehensive.py::TestMyWhatsappBot::test_whatsapp_webhook_message_processing": true, "tests/unit/test_endpoints_comprehensive.py::TestSlackEndpoint::test_slack_endpoint_singleton": true, "tests/unit/test_endpoints_comprehensive.py::TestSlackEndpoint::test_slack_endpoint_initialization": true, "tests/unit/test_endpoints_comprehensive.py::TestSlackEndpoint::test_slack_endpoint_setup": true, "tests/unit/test_endpoints_comprehensive.py::TestSlackEndpoint::test_slack_endpoint_setup_idempotent": true, "tests/unit/test_endpoints_comprehensive.py::TestSlackEndpoint::test_slack_message_processing": true, "tests/unit/test_endpoints_comprehensive.py::TestSlackEndpoint::test_slack_message_processing_error": true, "tests/unit/test_endpoints_comprehensive.py::TestTeamsEndpoint::test_teams_endpoint_singleton": true, "tests/unit/test_endpoints_comprehensive.py::TestTeamsEndpoint::test_teams_endpoint_initialization": true, "tests/unit/test_endpoints_comprehensive.py::TestTeamsEndpoint::test_teams_endpoint_setup": true, "tests/unit/test_endpoints_comprehensive.py::TestTeamsEndpoint::test_teams_endpoint_setup_idempotent": true, "tests/unit/test_endpoints_comprehensive.py::TestTeamsEndpoint::test_teams_message_handling": true, "tests/unit/test_endpoints_comprehensive.py::TestTeamsEndpoint::test_teams_message_handling_error": true, "tests/unit/test_endpoints_comprehensive.py::TestMyBotGeneric::test_mybot_generic_initialization": true, "tests/unit/test_endpoints_comprehensive.py::TestMyBotGeneric::test_mybot_generic_channel_type_enum": true, "tests/unit/test_endpoints_comprehensive.py::TestMyBotGeneric::test_mybot_generic_reply_context": true, "tests/unit/test_endpoints_comprehensive.py::TestMyBotGeneric::test_mybot_generic_process_message": true, "tests/unit/test_endpoints_comprehensive.py::TestMyBotGeneric::test_mybot_generic_extract_message_data_not_implemented": true, "tests/unit/test_endpoints_comprehensive.py::TestMyBotGeneric::test_mybot_generic_create_reply_context_not_implemented": true, "tests/unit/test_endpoints_comprehensive.py::TestMyBotGeneric::test_mybot_generic_send_response_not_implemented": true, "tests/unit/test_endpoints_comprehensive.py::TestEndpointIntegration::test_endpoint_factory_pattern": true, "tests/unit/test_endpoints_comprehensive.py::TestEndpointIntegration::test_endpoint_port_configuration": true, "tests/unit/test_endpoints_comprehensive.py::TestEndpointIntegration::test_endpoint_initialization_state": true, "tests/unit/test_endpoints_comprehensive.py::TestEndpointIntegration::test_endpoint_error_handling": true, "tests/unit/test_endpoints_comprehensive.py::TestEndpointIntegration::test_endpoint_thread_safety": true, "tests/unit/test_endpoints_comprehensive.py::TestEndpointIntegration::test_endpoint_memory_management": true, "tests/unit/test_endpoints_comprehensive.py::TestEndpointIntegration::test_endpoint_configuration_consistency": true, "tests/unit/test_endpoints_comprehensive.py::TestEndpointIntegration::test_endpoint_inheritance_hierarchy": true, "tests/unit/test_endpoints_comprehensive.py::TestEndpointIntegration::test_endpoint_state_isolation": true, "tests/unit/test_endpoints_comprehensive.py::TestEndpointPerformance::test_endpoint_creation_performance": true, "tests/unit/test_endpoints_comprehensive.py::TestEndpointPerformance::test_endpoint_method_call_performance": true, "tests/unit/test_endpoints_comprehensive.py::TestEndpointPerformance::test_endpoint_concurrent_access_performance": true, "tests/unit/test_etc_setup.py::TestSetup::test_imports_successful": true, "tests/unit/test_etc_setup.py::TestSetup::test_module_has_expected_functions": true, "tests/unit/test_etc_setup.py::TestSetup::test_conditional_imports_claude_environment": true, "tests/unit/test_etc_setup.py::TestSetup::test_conditional_imports_non_claude_environment": true, "tests/unit/test_etc_setup.py::TestSetup::test_mock_bot_class_methods": true, "tests/unit/test_etc_setup.py::TestSetup::test_init_new_project": true, "tests/unit/test_etc_setup.py::TestSetup::test_init_existing_project": true, "tests/unit/test_etc_setup.py::TestSetup::test_late_init": true, "tests/unit/test_etc_setup.py::TestSetup::test_generate_embedding": true, "tests/unit/test_etc_setup.py::TestSetup::test_load_embedding": true, "tests/unit/test_etc_setup.py::TestSetup::test_optimum_embedding_creation": true, "tests/unit/test_etc_setup.py::TestSetup::test_logging_configuration": true, "tests/unit/test_etc_setup.py::TestSetup::test_constants_and_imports": true, "tests/unit/test_etc_setup.py::TestSetup::test_zaira_settings_configuration": true, "tests/unit/test_etc_setup.py::TestSetup::test_database_names_compliance": true, "tests/unit/test_etc_setup.py::TestSetup::test_async_function_definitions": true, "tests/unit/test_etc_setup.py::TestSetup::test_manager_setup_sequence": true, "tests/unit/test_etc_setup.py::TestSetup::test_error_handling_in_generate_embedding": true, "tests/unit/test_imap_idle_tool.py::TestSupervisorTaskIMAPIdleActivate::test_valid_instantiation": true, "tests/unit/test_imap_idle_tool.py::TestSupervisorTaskIMAPIdleActivate::test_class_attributes": true, "tests/unit/test_imap_idle_tool.py::TestSupervisorTaskIMAPIdleActivate::test_create_task_function": true, "tests/unit/test_imap_idle_tool.py::TestSupervisorTaskIMAPIdleActivate::test_start_30_minute_session": true, "tests/unit/test_imap_idle_tool.py::TestIMAPIdleIntegration::test_can_import_module": true, "tests/unit/test_imap_idle_tool.py::TestIMAPIdleIntegration::test_integration_with_supervisor_state": true, "tests/unit/test_imap_idle_tool.py::TestIMAPIdleIntegration::test_integration_with_user_manager": true, "tests/unit/test_imap_idle_tool.py::TestIMAPIdleIntegration::test_integration_with_zaira_user": true, "tests/unit/test_imap_idle_tool.py::TestIMAPIdleIntegration::test_integration_with_mybot_generic": true, "tests/unit/test_imap_idle_tool.py::TestIMAPIdleIntegration::test_llm_call_with_mock_user": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerSingleton::test_multimodal_manager_singleton_creation": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerSingleton::test_multimodal_manager_get_instance": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerSingleton::test_multimodal_manager_initialization_state": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerSingleton::test_multimodal_manager_setup": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerSingleton::test_multimodal_manager_setup_idempotent": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerSingleton::test_multimodal_manager_config": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerDocumentProcessing::test_extract_multimodal_elements_success": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerDocumentProcessing::test_extract_multimodal_elements_no_doc_id": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerDocumentProcessing::test_extract_multimodal_elements_partition_error": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerDocumentProcessing::test_extract_multimodal_elements_with_debug_output": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerImageProcessing::test_process_image_element_success": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerImageProcessing::test_process_image_element_no_image_data": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerImageProcessing::test_process_image_element_metadata_image_data": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerImageProcessing::test_process_image_element_error_handling": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerImageProcessing::test_save_image_asset_success": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerImageProcessing::test_save_image_asset_error": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerTableProcessing::test_process_table_element_success": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerTableProcessing::test_process_table_element_no_table_data": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerTableProcessing::test_process_table_element_error_handling": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerTableProcessing::test_table_to_markdown_structured_data": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerTableProcessing::test_table_to_markdown_no_structured_data": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerTableProcessing::test_table_to_markdown_error": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerTableProcessing::test_structured_table_to_markdown_normal": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerTableProcessing::test_structured_table_to_markdown_empty": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerTableProcessing::test_structured_table_to_markdown_uneven_rows": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerAIIntegration::test_generate_image_summary_success": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerAIIntegration::test_generate_image_summary_error": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerAIIntegration::test_generate_table_summary_success": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerAIIntegration::test_generate_table_summary_error": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerAIIntegration::test_encode_image_to_base64_success": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerAIIntegration::test_encode_image_to_base64_error": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerUtilities::test_get_surrounding_context_normal": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerUtilities::test_get_surrounding_context_edge_cases": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerUtilities::test_get_surrounding_context_error_handling": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerUtilities::test_extract_table_key_info_success": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerUtilities::test_extract_table_key_info_invalid_format": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerUtilities::test_extract_table_key_info_error": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerUtilities::test_infer_column_types_success": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerUtilities::test_infer_column_types_mixed_data": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerUtilities::test_infer_column_types_error": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerUtilities::test_is_numeric_valid_numbers": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerUtilities::test_is_numeric_invalid_values": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerUtilities::test_is_date_valid_dates": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerUtilities::test_is_date_invalid_values": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerAssetManagement::test_cleanup_assets_success": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerAssetManagement::test_cleanup_assets_directory_not_exists": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerAssetManagement::test_cleanup_assets_error": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerAssetManagement::test_get_asset_path_success": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerAssetManagement::test_get_asset_path_not_found": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerAssetManagement::test_get_asset_path_error": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerIntegration::test_full_document_processing_workflow": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerIntegration::test_multimodal_manager_with_rag_system": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerIntegration::test_error_recovery_in_complex_workflow": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerPerformance::test_large_document_processing_performance": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerPerformance::test_memory_efficiency": true, "tests/unit/test_manager_multimodal_comprehensive.py::TestMultimodalManagerPerformance::test_concurrent_processing": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerSingleton::test_qdrant_manager_singleton_creation": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerSingleton::test_qdrant_manager_get_instance": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerSingleton::test_qdrant_manager_initialization_state": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerSingleton::test_qdrant_manager_setup": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerSingleton::test_qdrant_manager_setup_idempotent": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerConnection::test_qdrant_manager_client_creation": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerConnection::test_qdrant_manager_client_configuration": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerConnection::test_qdrant_manager_connection_error_handling": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerCollectionOperations::test_create_collection": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerCollectionOperations::test_delete_collection": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerCollectionOperations::test_list_collections": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerCollectionOperations::test_collection_exists": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerCollectionOperations::test_collection_not_exists": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerVectorOperations::test_upsert_vectors": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerVectorOperations::test_search_vectors": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerVectorOperations::test_get_vectors": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerVectorOperations::test_delete_vectors": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerAdvancedOperations::test_search_with_filter": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerAdvancedOperations::test_batch_upsert": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerAdvancedOperations::test_update_collection_config": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerAdvancedOperations::test_get_collection_info": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerErrorHandling::test_search_error_handling": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerErrorHandling::test_upsert_error_handling": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerErrorHandling::test_collection_creation_error_handling": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerErrorHandling::test_connection_timeout_handling": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerPerformance::test_concurrent_searches": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerPerformance::test_large_batch_operations": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerPerformance::test_memory_efficient_operations": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerPerformance::test_operation_timing": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerIntegration::test_rag_pipeline_integration": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerIntegration::test_document_ingestion_workflow": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerIntegration::test_semantic_search_workflow": true, "tests/unit/test_manager_qdrant_comprehensive.py::TestQDrantManagerIntegration::test_multi_collection_management": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerSingleton::test_retrieval_manager_singleton_creation": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerSingleton::test_retrieval_manager_get_instance": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerSingleton::test_retrieval_manager_initialization_state": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerSingleton::test_retrieval_manager_setup": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerSingleton::test_retrieval_manager_setup_idempotent": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerDataProcessing::test_json_to_dict_string_input": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerDataProcessing::test_json_to_dict_dict_input": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerDataProcessing::test_json_to_dict_list_input": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerDataProcessing::test_json_to_dict_nested_structure": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerDataProcessing::test_unstructured_to_list": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerDataProcessing::test_safe_get_dict": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerDataProcessing::test_safe_get_object": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerDataProcessing::test_safe_get_exception_handling": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerDataProcessing::test_list_to_dict": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerDataProcessing::test_elements_to_markdown_list": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerDataProcessing::test_elements_to_markdown_dict": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerEmbeddings::test_get_embeddings_dense_string_input": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerEmbeddings::test_get_embeddings_dense_list_input": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerEmbeddings::test_get_embeddings_dense_invalid_input": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerEmbeddings::test_get_embeddings_dense_numpy_conversion": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerEmbeddings::test_get_embeddings_sparse": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerChunking::test_chunk_text_string_input": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerChunking::test_chunk_text_list_input": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerChunking::test_chunk_text_overlap_validation": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerChunking::test_chunk_text_custom_parameters": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerChunking::test_chunk_text_non_string_input": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerChunking::test_chunk_multimodal_elements": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerChunking::test_chunk_with_multimodal_awareness": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerChunking::test_split_large_multimodal_paragraph": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerChunking::test_apply_overlap_to_chunks": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerErrorHandling::test_setup_model_loading_error": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerErrorHandling::test_setup_tokenizer_loading_error": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerErrorHandling::test_json_to_dict_invalid_json": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerErrorHandling::test_embeddings_with_uninitialized_manager": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerPerformance::test_chunking_performance": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerPerformance::test_json_processing_performance": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerPerformance::test_memory_efficiency": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerIntegration::test_full_retrieval_pipeline": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerIntegration::test_multimodal_document_processing": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerIntegration::test_retrieval_with_rag_system": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerEdgeCases::test_empty_input_handling": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerEdgeCases::test_very_large_input_handling": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerEdgeCases::test_unicode_content_handling": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerEdgeCases::test_malformed_multimodal_data": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerEdgeCases::test_safe_get_with_none_input": true, "tests/unit/test_manager_retrieval_comprehensive.py::TestRetrievalManagerEdgeCases::test_chunking_with_extreme_parameters": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerSingleton::test_scheduled_request_manager_singleton_creation": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerSingleton::test_scheduled_request_manager_get_instance": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerSingleton::test_scheduled_request_manager_initialization_state": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerSingleton::test_scheduled_request_manager_setup": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerSingleton::test_scheduled_request_manager_setup_idempotent": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerDatabaseOperations::test_create_tables_success": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerDatabaseOperations::test_create_tables_existing_table": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerDatabaseOperations::test_create_tables_missing_columns": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerDatabaseOperations::test_create_tables_database_error": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerDatabaseOperations::test_execute_single_operation_success": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerDatabaseOperations::test_execute_single_operation_with_params": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerDatabaseOperations::test_execute_single_operation_no_pool": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerTaskOperations::test_save_task_success": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerTaskOperations::test_save_task_database_unavailable": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerTaskOperations::test_save_task_database_error": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerTaskOperations::test_load_task_success": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerTaskOperations::test_load_task_not_found": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerTaskOperations::test_load_task_database_error": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerTaskOperations::test_get_active_requests_all": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerTaskOperations::test_get_active_requests_by_user": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerTaskOperations::test_get_active_requests_database_unavailable": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerTaskOperations::test_cancel_task_success": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerTaskOperations::test_cancel_task_not_active": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerTaskOperations::test_cancel_task_database_error": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerRecovery::test_late_setup_success": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerRecovery::test_late_setup_recovery_error": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerRecovery::test_recover_requests_success": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerRecovery::test_recover_requests_overdue_task": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerRecovery::test_recover_requests_invalid_data": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerRecovery::test_recover_requests_recreation_failure": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerRecovery::test_recreate_task_from_data_success": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerRecovery::test_recreate_task_user_not_found": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerRecovery::test_recreate_task_user_manager_error": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerUserTasks::test_get_paused_requests_count": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerUserTasks::test_get_paused_requests_info": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerUtilities::test_cleanup_old_requests_success": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerUtilities::test_cleanup_old_requests_custom_days": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerUtilities::test_cleanup_old_requests_database_error": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerUtilities::test_get_active_task": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerUtilities::test_get_all_active_requests": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerErrorHandling::test_save_task_json_serialization_error": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerErrorHandling::test_recovery_with_general_error": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerIntegration::test_persistence_manager_singleton_access": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerIntegration::test_full_task_lifecycle": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerIntegration::test_persistence_manager_with_real_schedule_types": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerPerformance::test_bulk_task_operations_performance": true, "tests/unit/test_manager_scheduled_requests_comprehensive.py::TestScheduledRequestPersistenceManagerPerformance::test_memory_efficiency": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSection::test_default_constructor": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSection::test_from_values_with_string_description": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSection::test_from_values_with_list_description": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSection::test_get_description_with_string": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSection::test_get_description_with_list": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSection::test_str_representation_with_string": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSection::test_str_representation_with_list": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskState::test_state_creation_with_defaults": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskState::test_state_creation_with_values": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskState::test_state_sections_dict": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_base_task_initialization": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_base_task_repr": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_get_tools_default": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_get_prompt_with_id": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_get_prompt_with_prefix": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_get_prompt_without_id": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_llm_call_not_implemented": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_llm_call_wrapper_success": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_llm_call_wrapper_none_result": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_llm_call_wrapper_command_result": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_compile_default": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_call_task_with_query": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_single_agent_initialization": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_get_tools": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_llm_call_without_tools": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_llm_call_with_tools": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_llm_call_with_hitl_yes": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_llm_call_with_hitl_no": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_compile_default_without_hitl": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_compile_default_with_hitl": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskChainOfThought::test_cot_task_initialization": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskChainOfThought::test_get_cot_prompt_with_custom_suffix": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskChainOfThought::test_get_cot_prompt_with_default_suffix": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskChainOfThought::test_get_cot_prompt_with_prefix": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskChainOfThought::test_llm_call_with_reasoning_extraction": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskChainOfThought::test_llm_call_without_reasoning_content": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskChainOfThought::test_llm_call_no_content_attribute": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_supervisor_initialization": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_supervisor_repr": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_add_task_with_priority": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_add_task_without_priority": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_add_task_replaces_existing": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_add_task_maintains_order": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_remove_task_success": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_remove_task_not_found": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_update_task_priority": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_get_requests": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_get_requests_with_priorities": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_has_task": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_get_task_by_name": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_find_task_by_guid": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_get_task_priority": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_get_tools": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_llm_call_router_with_requests": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_llm_call_router_with_end_decision": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_llm_call_router_with_always_call_last_requests": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_compile_with_conditional_requests": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_compile_with_always_call_first_requests": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_compile_with_always_call_last_requests": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_compile_with_no_requests": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_call_supervisor": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_call_supervisor_with_state": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_singleton_pattern": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_setup": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_register_task": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_register_supervisor": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_get_supervisor": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_add_task_to_supervisor": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_add_task_to_nonexistent_supervisor": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_transfer_task": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_transfer_task_not_found": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_transfer_task_supervisor_not_found": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_find_task": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_get_task": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_get_all_requests": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskCreateAgent::test_create_agent_initialization": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskCreateAgent::test_create_agent_get_tools": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskCreateAgent::test_llm_call_success": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskCreateAgent::test_llm_call_with_conversation_history": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskCreateAgent::test_llm_call_error_handling": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskCreateAgent::test_llm_call_invalid_response_format": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskCreateAgent::test_compile_default": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_supervisor_initialization": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_supervisor_compile": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_router_with_first_call": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_router_with_always_call_first": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_router_with_always_call_last": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_router_with_reasoning_enabled": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_router_reasoning_error_fallback": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_router_invalid_task_validation": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_router_completed_task_validation": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestIntegration::test_full_supervisor_task_flow": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestIntegration::test_task_transfer_between_supervisors": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestIntegration::test_chain_of_thought_with_regular_requests": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestIntegration::test_model_validation_and_error_handling": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestIntegration::test_concurrent_task_management": true, "tests/unit/test_manager_supervisors_comprehensive.py::TestIntegration::test_memory_management": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_singleton_pattern": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_manager_initialization": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_add_user_success": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_add_user_duplicate_guid": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_add_user_persistence_failure": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_create_guid": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_remove_user_success": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_remove_user_not_found": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_find_user_success": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_find_user_not_found": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_get_user_by_username_success": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_get_user_by_username_not_found": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_get_user_multiple_users": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_update_user_success": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_update_user_not_found": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_concurrent_access": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_lock_usage": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerLogging::test_logger_initialization": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerLogging::test_logging_on_operations": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_empty_manager_operations": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_invalid_guid_formats": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_ensure_models_rebuilt": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_ensure_models_rebuilt_error": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_update_user_invalid_attribute": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_list_users": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_list_users_empty": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_find_users_by_rank": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_find_users_by_rank_empty": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_user_exists_true": true, "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_user_exists_false": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_multimodal_manager_singleton": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_multimodal_manager_setup": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_get_images_base64": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_is_numeric_validation": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_is_date_validation": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_structured_table_to_markdown": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_structured_table_to_markdown_empty": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_html_to_markdown_conversion": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_get_surrounding_context": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_extract_table_key_info": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_infer_column_types": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_document_processor_tool_validation": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_multimodal_retrieval_tool_error_handling": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_multimodal_search_input_validation": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_document_processing_input_validation": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_save_image_asset_functionality": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_save_base64_image_functionality": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_cleanup_assets": true, "tests/unit/test_multimodal_processing.py::TestMultimodalProcessing::test_get_asset_path": true, "tests/unit/test_multimodal_processing.py::TestMultimodalIntegration::test_end_to_end_processing_workflow": true, "tests/unit/test_multimodal_processing.py::TestMultimodalIntegration::test_multimodal_metadata_structure": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_mybot_generic_creation": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_mybot_generic_pydantic_config": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_ready": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_message": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_member_join_python_call": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_member_join_teams": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_member_join_discord": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_member_join_whatsapp": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_request_human_in_the_loop_python": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_request_human_in_the_loop_discord": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_request_human_in_the_loop_teams": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_request_human_in_the_loop_whatsapp": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_request_human_in_the_loop_halt_until_response": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_broadcast_python": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_broadcast_discord": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_broadcast_other_platforms": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_python": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_teams": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_discord_short_message": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_discord_long_message": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_whatsapp": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_whatsapp_long_message": true, "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_without_chat_history": true, "tests/unit/test_mybot_generic.py::TestMyBotGenericTextSplitting::test_text_splitting_short_text": true, "tests/unit/test_mybot_generic.py::TestMyBotGenericTextSplitting::test_text_splitting_sentence_boundaries": true, "tests/unit/test_mybot_generic.py::TestMyBotGenericTextSplitting::test_text_splitting_no_sentences": true, "tests/unit/test_mybot_generic.py::TestMyBotGenericTextSplitting::test_text_splitting_last_chunk_rebalancing": true, "tests/unit/test_mybot_generic.py::TestMyBotGenericEdgeCases::test_creation_with_kwargs": true, "tests/unit/test_mybot_generic.py::TestMyBotGenericEdgeCases::test_unknown_platform_handling": true, "tests/unit/test_mybot_generic.py::TestReplyContext::test_reply_context_create_no_reply": true, "tests/unit/test_mybot_generic.py::TestReplyContext::test_reply_context_create_reply": true, "tests/unit/test_mybot_generic.py::TestReplyContext::test_reply_context_format_reply_context_for_ai": true, "tests/unit/test_mybot_generic.py::TestReplyContext::test_reply_context_format_reply_context_for_ai_no_content": true, "tests/unit/test_mybot_generic.py::TestReplyContext::test_reply_context_format_reply_context_for_ai_no_author": true, "tests/unit/test_mybot_generic.py::TestReplyContext::test_reply_context_validation": true, "tests/unit/test_mybot_generic.py::TestChannelType::test_channel_type_constants": true, "tests/unit/test_mybot_testing.py::TestMyBotTesting::test_initialization": true, "tests/unit/test_mybot_testing.py::TestMyBotTesting::test_initialization_with_params": true, "tests/unit/test_mybot_testing.py::TestMyBotTesting::test_request_human_in_the_loop_testing_name": true, "tests/unit/test_mybot_testing.py::TestMyBotTesting::test_request_human_in_the_loop_other_name": true, "tests/unit/test_mybot_testing.py::TestMyBotTesting::test_request_human_in_the_loop_halt_until_response": true, "tests/unit/test_mybot_testing.py::TestMyBotTesting::test_generate_test_response_with_llm": true, "tests/unit/test_mybot_testing.py::TestMyBotTesting::test_generate_test_response_llm_exception": true, "tests/unit/test_mybot_testing.py::TestMyBotTesting::test_generate_test_response_no_llm": true, "tests/unit/test_mybot_testing.py::TestMyBotTesting::test_get_fallback_response_email_approval": true, "tests/unit/test_mybot_testing.py::TestMyBotTesting::test_get_fallback_response_email_address": true, "tests/unit/test_mybot_testing.py::TestMyBotTesting::test_get_fallback_response_sender_email": true, "tests/unit/test_mybot_testing.py::TestMyBotTesting::test_get_fallback_response_yes_no": true, "tests/unit/test_mybot_testing.py::TestMyBotTesting::test_get_fallback_response_default": true, "tests/unit/test_mybot_testing.py::TestMyBotTesting::test_get_fallback_response_case_insensitive": true, "tests/unit/test_mybot_testing.py::TestMyBotTesting::test_get_fallback_response_mixed_patterns": true, "tests/unit/test_mybot_testing.py::TestMyBotTestingEdgeCases::test_exception_handling_in_generate_response": true, "tests/unit/test_mybot_testing.py::TestMyBotTestingEdgeCases::test_empty_request_handling": true, "tests/unit/test_mybot_testing.py::TestMyBotTestingEdgeCases::test_long_request_handling": true, "tests/unit/test_mybot_testing.py::TestMyBotTestingEdgeCases::test_inheritance_from_mybot_generic": true, "tests/unit/test_mybot_testing.py::TestMyBotTestingEdgeCases::test_unicode_handling": true, "tests/unit/test_mybot_testing.py::TestMyBotTestingEdgeCases::test_multiple_pattern_matches": true, "tests/unit/test_output_sender_system_integration.py::TestOutputSenderSystemIntegration::test_system_task_creation": true, "tests/unit/test_output_sender_system_integration.py::TestOutputSenderSystemIntegration::test_output_sender_includes_system_task": true, "tests/unit/test_output_sender_system_integration.py::TestOutputSenderSystemIntegration::test_system_task_llm_call_executes_output_requests": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_singleton_pattern": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_manager_initialization": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_setup_success": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_setup_already_initialized": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_setup_failure": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_create_tables_and_indexes": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_create_tables_missing_columns": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_execute_single_operation_success": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_execute_single_operation_no_params": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_execute_single_operation_failure": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_save_task_success": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_save_task_db_unavailable": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_save_task_invalid_guid": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_save_task_database_error": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_get_active_requests_user_filtered": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_get_active_requests_invalid_user_guid": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_get_active_requests_system_wide": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_get_active_requests_security_hash_validation_failure": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_get_active_requests_db_unavailable": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_get_active_requests_database_error": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_cancel_task_success": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_cancel_task_invalid_guid": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_cancel_task_unauthorized": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_cancel_task_not_found": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_update_task_execution_stats_success": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_update_task_execution_stats_failure": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_update_task_execution_stats_database_error": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_get_user_quota_info_existing": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_get_user_quota_info_new_user": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_get_user_quota_info_invalid_guid": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_update_user_quota_tracking_task_created": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_update_user_quota_tracking_task_completed": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_initialize_user_quota_tracking": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_log_audit_event": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_log_audit_event_with_error": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_generate_security_hash": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_validate_security_hash_success": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_validate_security_hash_failure": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_validate_security_hash_no_hash": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_validate_security_hash_error": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_recreate_task_from_data": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_cleanup_old_records": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_cleanup_old_records_error": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_concurrent_setup_calls": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_save_task_with_timezone_aware_datetime": true, "tests/unit/test_persistence_manager.py::TestScheduledRequestPersistenceManager::test_save_task_with_none_next_execution": true, "tests/unit/test_quota_manager.py::TestQuotaUsage::test_quota_usage_initialization": true, "tests/unit/test_quota_manager.py::TestQuotaUsage::test_quota_usage_with_values": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_user_quota_manager_initialization": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_check_quota_availability_success": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_check_quota_availability_concurrent_limit_exceeded": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_check_quota_availability_daily_limit_exceeded": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_check_quota_availability_memory_limit_exceeded": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_check_quota_availability_recurring_limit_exceeded": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_reserve_quota_success": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_reserve_quota_recurring_task": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_reserve_quota_concurrent_limit_exceeded": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_reserve_quota_daily_limit_exceeded": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_reserve_quota_memory_limit_exceeded": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_reserve_quota_recurring_limit_exceeded": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_release_quota_standard_task": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_release_quota_recurring_task": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_get_quota_usage": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_get_quota_metrics": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_update_quota_config": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_reset_daily_quota": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_get_quota_forecast": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_calculate_quota_health": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_calculate_hours_until_limit": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_get_forecast_recommendation": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_should_reset_daily_quota": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_update_peak_usage": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_record_quota_violation": true, "tests/unit/test_quota_manager.py::TestUserQuotaManager::test_record_quota_violation_history_limit": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_system_quota_manager_initialization": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_system_manager_setup": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_system_manager_shutdown": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_get_user_quota_manager_new_user": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_get_user_quota_manager_existing_user": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_remove_user_quota_manager_success": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_remove_user_quota_manager_nonexistent": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_get_system_quota_overview": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_apply_emergency_quotas": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_calculate_system_health_healthy": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_calculate_system_health_moderate": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_calculate_system_health_degraded": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_calculate_system_health_critical": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_calculate_system_health_empty": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_get_system_resource_utilization_success": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_get_system_resource_utilization_error": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_system_monitoring_healthy": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_periodic_cleanup": true, "tests/unit/test_quota_manager.py::TestSystemQuotaManager::test_concurrent_manager_access": true, "tests/unit/test_rate_limiter.py::TestUserRateLimiter::test_user_rate_limiter_initialization": true, "tests/unit/test_rate_limiter.py::TestUserRateLimiter::test_check_rate_limit_success": true, "tests/unit/test_rate_limiter.py::TestUserRateLimiter::test_check_rate_limit_burst_exceeded": true, "tests/unit/test_rate_limiter.py::TestUserRateLimiter::test_check_rate_limit_minute_exceeded": true, "tests/unit/test_rate_limiter.py::TestUserRateLimiter::test_check_rate_limit_hour_exceeded": true, "tests/unit/test_rate_limiter.py::TestUserRateLimiter::test_get_current_usage": true, "tests/unit/test_rate_limiter.py::TestUserRateLimiter::test_get_metrics": true, "tests/unit/test_rate_limiter.py::TestUserRateLimiter::test_calculate_acceptance_rate": true, "tests/unit/test_rate_limiter.py::TestUserRateLimiter::test_reset_counters": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_system_rate_limiter_initialization": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_system_limiter_setup": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_system_limiter_shutdown": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_check_user_rate_limit_success": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_check_user_rate_limit_global_limits_exceeded": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_check_user_rate_limit_user_limit_exceeded": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_get_user_limiter_new_user": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_get_user_limiter_existing_user": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_check_global_limits_under_limit": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_check_global_limits_minute_exceeded": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_check_global_limits_hour_exceeded": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_get_user_rate_status_existing": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_get_user_rate_status_nonexistent": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_get_system_rate_metrics": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_calculate_system_load": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_get_top_users_by_usage": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_reset_user_limits_success": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_reset_user_limits_nonexistent": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_remove_user_limiter_success": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_remove_user_limiter_nonexistent": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_cleanup_inactive_limiters": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_get_limiter_count": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_apply_emergency_limits": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_restore_normal_limits": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_periodic_cleanup_runs": true, "tests/unit/test_rate_limiter.py::TestSystemRateLimiter::test_concurrent_user_limiter_access": true, "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_creation_with_llm_parsing": true, "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_recurring_schedule_tool": true, "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_daily_schedule_tool": true, "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_monthly_schedule_tool": true, "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_generic_schedule_tool": true, "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_llm_parsing_agent_creation": true, "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_extract_parsed_info_from_llm_result": true, "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_fallback_to_regex_parsing": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_singleton_pattern": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_factory_initialization": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_factory_setup": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_get_user_manager_new_user": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_get_user_manager_existing_user": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_get_user_manager_different_users": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_remove_user_manager_success": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_remove_user_manager_nonexistent": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_get_all_user_managers": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_get_all_user_managers_empty": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_get_active_manager_count": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_factory_shutdown": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_concurrent_access": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_manager_initialization_failure": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_cleanup_inactive_managers": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_get_factory_metrics": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_is_user_manager_active": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_user_lock_mechanism": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_multiple_quota_configurations": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_factory_properties": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_error_handling_in_setup": true, "tests/unit/test_scheduled_request_factory.py::TestScheduledRequestManagerFactory::test_graceful_shutdown_with_active_managers": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validator_initialization": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_user_authorization_success": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_user_authorization_invalid_guid": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_user_authorization_inactive_account": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_user_authorization_unknown_operation": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_request_content_success": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_request_content_malicious_schedule_prompt": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_request_content_malicious_target_prompt": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_request_content_invalid_timing": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_ip_access_success": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_ip_access_invalid_format": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_ip_access_blocked_ip": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_ip_access_rate_limit_exceeded": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_request_ownership_success": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_request_ownership_unauthorized": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_sanitize_request_data_success": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_sanitize_request_data_extreme_values": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_generate_audit_hash": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_user_account_status_valid": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_user_account_status_invalid": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_create_authorization_success": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_create_authorization_no_rank": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_read_authorization_with_specific_request": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_read_authorization_general_access": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_modify_authorization_success": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_modify_authorization_no_guid": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_prompt_content_success": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_prompt_content_malicious_patterns": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_prompt_content_too_long": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_prompt_content_non_ascii": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_prompt_content_invalid_input": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_timing_parameters_success": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_timing_parameters_invalid_delays": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_timing_parameters_future_execution": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_schedule_constraints_success": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_schedule_constraints_recurring_too_frequent": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_schedule_constraints_admin_frequent_allowed": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_ip_format_valid": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_validate_ip_format_invalid": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_is_ip_blocked_not_blocked": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_is_ip_blocked_currently_blocked": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_is_ip_blocked_expired_block": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_check_ip_rate_limit_under_limit": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_check_ip_rate_limit_over_limit": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_check_ip_rate_limit_old_attempts_cleaned": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_block_ip_temporarily": true, "tests/unit/test_security_validator.py::TestScheduledRequestSecurityValidator::test_get_security_metrics": true, "tests/unit/test_session_isolation.py::TestSessionIsolation::test_scheduled_request_creates_isolated_session": true, "tests/unit/test_session_isolation.py::TestSessionIsolation::test_user_request_uses_active_session": true, "tests/unit/test_session_isolation.py::TestSessionIsolation::test_different_scheduled_requests_get_different_sessions": true, "tests/unit/test_session_isolation.py::TestSessionIsolation::test_isolated_sessions_dont_affect_active_session": true, "tests/unit/test_session_isolation_integration.py::TestSessionIsolationIntegration::test_long_running_request_uses_correct_session_for_scheduled_request": true, "tests/unit/test_session_isolation_integration.py::TestSessionIsolationIntegration::test_long_running_request_uses_active_session_for_regular_request": true, "tests/unit/test_session_isolation_integration.py::TestSessionIsolationIntegration::test_multiple_scheduled_requests_get_isolated_sessions": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_basic_router_functionality": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_always_call_first_routing": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_always_call_last_routing": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_completed_tasks_exclusion": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_error_handling_invalid_task": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_empty_requests_list": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_router_wrapper_functionality": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_router_wrapper_string_return": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_router_wrapper_none_return": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_always_call_first_priority": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_always_call_first_sequence": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_reasoning_with_model_response": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_disabled_reasoning": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_task_exclusion_logic": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_model_error_handling": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_empty_reasoning_response": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_debug_logging": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorCriticalEdgeCases::test_concurrent_task_execution_safety": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorCriticalEdgeCases::test_state_mutation_isolation": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorCriticalEdgeCases::test_memory_leak_prevention": true, "tests/unit/test_supervisor_comprehensive.py::TestSupervisorCriticalEdgeCases::test_malformed_state_handling": true, "tests/unit/test_system_user_startup_tasks.py::TestSystemUserStartupTasks::test_execute_startup_requests_with_imap_configured": true, "tests/unit/test_system_user_startup_tasks.py::TestSystemUserStartupTasks::test_execute_startup_requests_without_imap_configured": true, "tests/unit/test_system_user_startup_tasks.py::TestSystemUserStartupTasks::test_execute_startup_task_by_name_success": true, "tests/unit/test_system_user_startup_tasks.py::TestSystemUserStartupTasks::test_execute_startup_task_by_name_not_found": true, "tests/unit/test_system_user_startup_tasks.py::TestSystemUserStartupTasks::test_execute_imap_idle_startup_success": true, "tests/unit/test_system_user_startup_tasks.py::TestSystemUserStartupTasks::test_execute_imap_idle_startup_task_not_found": true, "tests/unit/test_system_user_startup_tasks.py::TestSystemUserStartupTasks::test_execute_startup_requests_handles_exceptions": true, "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_new_chat_session_success": true, "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_new_chat_session_user_not_found": true, "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_new_chat_session_exception_handling": true, "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_change_chat_session_success": true, "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_change_chat_session_invalid_guid": true, "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_change_chat_session_not_found": true, "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_change_chat_session_user_not_found": true, "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_list_chat_sessions_success": true, "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_list_chat_sessions_empty": true, "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_list_chat_sessions_user_not_found": true, "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_list_chat_sessions_exception_handling": true, "tests/unit/test_task_chat_session.py::TestSupervisorTaskChangeSession::test_class_creation": true, "tests/unit/test_task_chat_session.py::TestSupervisorTaskChangeSession::test_llm_call_delegates_to_parent": true, "tests/unit/test_task_chat_session.py::TestCreateTaskFunction::test_create_task_manage_chat_sessions": true, "tests/unit/test_task_chat_session.py::TestUUIDHandling::test_uuid_string_conversion": true, "tests/unit/test_task_chat_session.py::TestUUIDHandling::test_invalid_uuid_string": true, "tests/unit/test_tasks_comprehensive.py::TestTopLevelSupervisor::test_create_top_level_supervisor_success": true, "tests/unit/test_tasks_comprehensive.py::TestTopLevelSupervisor::test_supervisor_task_registration": true, "tests/unit/test_tasks_comprehensive.py::TestTopLevelSupervisor::test_supervisor_compilation": true, "tests/unit/test_tasks_comprehensive.py::TestTopLevelSupervisor::test_task_priorities": true, "tests/unit/test_tasks_comprehensive.py::TestTopLevelSupervisor::test_supervisor_name_and_prompt": true, "tests/unit/test_tasks_comprehensive.py::TestTopOutputSupervisor::test_create_top_output_supervisor_success": true, "tests/unit/test_tasks_comprehensive.py::TestTopOutputSupervisor::test_output_supervisor_registration": true, "tests/unit/test_tasks_comprehensive.py::TestTopOutputSupervisor::test_output_supervisor_compilation": true, "tests/unit/test_tasks_comprehensive.py::TestQuickSearchTasks::test_create_task_quick_rag_search": true, "tests/unit/test_tasks_comprehensive.py::TestQuickSearchTasks::test_create_task_quick_llm_search": true, "tests/unit/test_tasks_comprehensive.py::TestQuickSearchTasks::test_create_task_quick_complexity_search": true, "tests/unit/test_tasks_comprehensive.py::TestQuickSearchTasks::test_search_task_configuration": true, "tests/unit/test_tasks_comprehensive.py::TestQuickSearchTasks::test_search_task_prompt_configuration": true, "tests/unit/test_tasks_comprehensive.py::TestLanguageDetectorTask::test_create_task_language_detector": true, "tests/unit/test_tasks_comprehensive.py::TestLanguageDetectorTask::test_language_detector_task_configuration": true, "tests/unit/test_tasks_comprehensive.py::TestLanguageDetectorTask::test_language_detector_prompt_configuration": true, "tests/unit/test_tasks_comprehensive.py::TestScheduledRequestManager::test_create_task_scheduled_request_manager": true, "tests/unit/test_tasks_comprehensive.py::TestScheduledRequestManager::test_scheduled_request_manager_configuration": true, "tests/unit/test_tasks_comprehensive.py::TestScheduledRequestManager::test_scheduled_request_manager_prompt_configuration": true, "tests/unit/test_tasks_comprehensive.py::TestChatSessionManager::test_create_task_manage_chat_sessions": true, "tests/unit/test_tasks_comprehensive.py::TestChatSessionManager::test_chat_session_manager_configuration": true, "tests/unit/test_tasks_comprehensive.py::TestChatSessionManager::test_chat_session_manager_prompt_configuration": true, "tests/unit/test_tasks_comprehensive.py::TestEmailGenerator::test_create_task_email_generator": true, "tests/unit/test_tasks_comprehensive.py::TestEmailGenerator::test_email_generator_configuration": true, "tests/unit/test_tasks_comprehensive.py::TestAgendaPlanner::test_create_supervisor_agenda_planner": true, "tests/unit/test_tasks_comprehensive.py::TestAgendaPlanner::test_agenda_planner_configuration": true, "tests/unit/test_tasks_comprehensive.py::TestRetrievalSupervisor::test_create_supervisor_retrieval": true, "tests/unit/test_tasks_comprehensive.py::TestRetrievalSupervisor::test_retrieval_supervisor_configuration": true, "tests/unit/test_tasks_comprehensive.py::TestTaskIntegration::test_task_execution_flow": true, "tests/unit/test_tasks_comprehensive.py::TestTaskIntegration::test_supervisor_task_coordination": true, "tests/unit/test_tasks_comprehensive.py::TestTaskIntegration::test_task_state_management": true, "tests/unit/test_tasks_comprehensive.py::TestTaskIntegration::test_error_handling_in_requests": true, "tests/unit/test_tasks_comprehensive.py::TestTaskIntegration::test_task_tool_integration": true, "tests/unit/test_tasks_comprehensive.py::TestTaskIntegration::test_task_priority_handling": true, "tests/unit/test_tasks_comprehensive.py::TestTaskIntegration::test_task_removal_and_replacement": true, "tests/unit/test_tasks_comprehensive.py::TestTaskIntegration::test_task_state_persistence": true, "tests/unit/test_tasks_comprehensive.py::TestTaskPerformance::test_task_creation_performance": true, "tests/unit/test_tasks_comprehensive.py::TestTaskPerformance::test_supervisor_task_management_performance": true, "tests/unit/test_tasks_comprehensive.py::TestTaskPerformance::test_task_state_serialization_performance": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_singleton_pattern": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_initialization": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_setup_debug_mode": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_setup_production_mode": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_teams_auth_endpoint": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_teams_logout_endpoint": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_teams_messages_endpoint": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_teams_messages_endpoint_exception": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_on_message_activity": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_on_message_activity_new_user": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_on_members_added_activity": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_on_members_added_activity_multiple_members": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_send_teams_broadcast_not_implemented": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_send_a_teams_message": true, "tests/unit/test_teams_endpoint.py::TestOnTurnError::test_on_turn_error_function": true, "tests/unit/test_teams_endpoint.py::TestOnTurnError::test_on_turn_error_send_activity_exception": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBotEdgeCases::test_exception_handling_in_setup": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBotEdgeCases::test_on_message_activity_exception_handling": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBotEdgeCases::test_channel_type_detection": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBotEdgeCases::test_empty_message_handling": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBotEdgeCases::test_missing_activity_properties": true, "tests/unit/test_teams_endpoint.py::TestMyTeamsBotEdgeCases::test_concurrent_message_processing": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_manager_initialization": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_manager_setup": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_quota_availability_check_success": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_quota_availability_check_concurrent_limit": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_quota_availability_check_memory_limit": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_create_scheduled_request_success": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_create_scheduled_request_unauthorized": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_create_scheduled_request_quota_exceeded": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_cancel_scheduled_request_success": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_cancel_nonexistent_request": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_get_user_requests": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_get_request_status_active": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_get_request_status_nonexistent": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_manager_shutdown": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_manager_properties": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_recurring_tasks_limit": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_memory_tracking": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_invalid_guid_handling": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_daily_quota_reset": true, "tests/unit/test_user_scheduled_request_manager.py::TestUserScheduledRequestManager::test_load_user_requests_error_handling": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserClass::test_zaira_user_creation_basic": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserClass::test_zaira_user_creation_with_all_fields": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserClass::test_zaira_user_default_values": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserClass::test_zaira_user_guid_validation": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserClass::test_zaira_user_username_validation": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserClass::test_zaira_user_permission_level_validation": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserClass::test_zaira_user_email_validation": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserClass::test_zaira_user_pydantic_model": true, "tests/unit/test_zaira_user_comprehensive.py::TestPermissionLevels::test_permission_levels_enum_exists": true, "tests/unit/test_zaira_user_comprehensive.py::TestPermissionLevels::test_permission_levels_values": true, "tests/unit/test_zaira_user_comprehensive.py::TestPermissionLevels::test_permission_levels_hierarchy": true, "tests/unit/test_zaira_user_comprehensive.py::TestPermissionLevels::test_permission_levels_comparison": true, "tests/unit/test_zaira_user_comprehensive.py::TestPermissionLevels::test_permission_levels_in_user_creation": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserValidation::test_zaira_user_ascii_validation": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserValidation::test_zaira_user_field_length_validation": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserValidation::test_zaira_user_email_format_validation": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserValidation::test_zaira_user_datetime_validation": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserValidation::test_zaira_user_boolean_validation": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserMethods::test_zaira_user_str_representation": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserMethods::test_zaira_user_repr_representation": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserMethods::test_zaira_user_equality": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserMethods::test_zaira_user_dict_export": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserMethods::test_zaira_user_json_export": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserMethods::test_zaira_user_field_access": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserEdgeCases::test_zaira_user_with_special_characters": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserEdgeCases::test_zaira_user_with_long_values": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserEdgeCases::test_zaira_user_with_minimal_data": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserEdgeCases::test_zaira_user_with_boundary_timestamps": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserPerformance::test_zaira_user_creation_performance": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserPerformance::test_zaira_user_serialization_performance": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserPerformance::test_zaira_user_memory_usage": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserIntegration::test_zaira_user_with_database_integration": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserIntegration::test_zaira_user_with_api_integration": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserIntegration::test_zaira_user_permission_checking": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserIntegration::test_zaira_user_with_session_management": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserIntegration::test_main_chat_session_has_title": true, "tests/unit/test_zaira_user_comprehensive.py::TestZairaUserIntegration::test_new_chat_session_has_title": true, "tests/unit/test_signature_functionality.py::TestZairaUserSignatureFields::test_zaira_user_signature_fields": true, "tests/unit/test_signature_functionality.py::TestZairaUserSignatureFields::test_zaira_user_custom_signature": true, "tests/unit/test_signature_functionality.py::TestSignatureIntegration::test_signature_manager_get_user_signature": true, "tests/unit/test_signature_functionality.py::TestSignatureIntegration::test_signature_manager_prepare_signature_for_email": true, "tests/unit/test_signature_functionality.py::TestSignatureManager::test_get_default_signature_hg_sports": true, "tests/unit/test_signature_functionality.py::TestSignatureManager::test_generate_html_signature_text_only": true, "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_create_task_email_generator": true, "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_tool_initialization": true, "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_tool_run_not_implemented": true, "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_tool_basic_functionality": true, "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_tool_exception_handling": true, "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_no_user_email": true, "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_missing_recipient": true, "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_user_rejection": true, "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_tool_instance": true, "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_missing_user_email_with_callback": true, "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_user_cancellation": true, "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_subject_generation_missing": true, "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_state_sections_missing": true, "tests/unit/test_tasks_comprehensive.py::TestTopLevelSupervisor": true, "tests/unit/test_tasks_comprehensive.py::TestTopOutputSupervisor": true, "tests/unit/test_tasks_comprehensive.py::TestQuickSearchTasks": true, "tests/unit/test_tasks_comprehensive.py::TestLanguageDetectorTask": true, "tests/unit/test_tasks_comprehensive.py::TestScheduledRequestManager": true, "tests/unit/test_tasks_comprehensive.py::TestChatSessionManager": true, "tests/unit/test_tasks_comprehensive.py::TestEmailGenerator": true, "tests/unit/test_tasks_comprehensive.py::TestAgendaPlanner": true, "tests/unit/test_tasks_comprehensive.py::TestRetrievalSupervisor": true, "tests/unit/test_tasks_comprehensive.py::TestTaskIntegration": true, "tests/unit/test_tasks_comprehensive.py::TestTaskPerformance": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_singleton_pattern": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_client_creation": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_on_ready": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_process_webhook_valid_message": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_process_webhook_empty_data": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_process_webhook_no_messages": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_process_webhook_multiple_messages": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_on_message_short_text": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_on_message_new_user": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_send_whatsapp_message_success": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_send_whatsapp_message_empty": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_send_whatsapp_message_no_recipient": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_send_whatsapp_message_too_long": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_send_whatsapp_message_api_error": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_send_whatsapp_message_connection_error": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_verify_success": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_verify_invalid_token": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_verify_missing_params": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_webhook_valid_json": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_webhook_invalid_content_type": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_webhook_invalid_json": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_webhook_exception": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_setup_oauth_configuration": true, "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_message_payload_structure": true, "tests/unit/test_supervisor_comprehensive.py": true, "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_handle_file_upload_complete_success": true, "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_handle_file_upload_manager_initialization_failure": true, "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_ensure_managers_initialized_success": true, "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_ensure_managers_initialized_index_failure": true, "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_validate_upload_dependencies_success": true, "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_validate_upload_dependencies_file_too_large": true, "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_validate_upload_dependencies_unsupported_file_type": true, "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_validate_upload_dependencies_embedding_service_failure": true, "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_process_files_to_vectorstore_success": true, "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_process_files_to_vectorstore_files_not_processed": true, "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_process_files_to_vectorstore_conversion_exception": true, "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_handle_file_upload_no_files_field": true, "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_handle_file_upload_no_filename": true, "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreEnd2End::test_complete_pipeline_integration": true}