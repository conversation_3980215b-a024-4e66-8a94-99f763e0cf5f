=== DEBUG TRACE STARTED at 2025-08-19 13:52:27.620745 ===
[2025-08-19 13:52:35.562] Traceback (most recent call last):
[2025-08-19 13:52:35.562]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\debug_hitl_test.py", line 15, in <module>
    [2025-08-19 13:52:35.563] from userprofiles.ZairaUser import ZairaUser, PERMISSION_LEVELS
[2025-08-19 13:52:35.563]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\__init__.py", line 4, in <module>
    [2025-08-19 13:52:35.563] from .ZairaUser import ZairaUser
[2025-08-19 13:52:35.563]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\ZairaUser.py", line 14, in <module>
    [2025-08-19 13:52:35.564] from managers.manager_multimodal import MultimodalManager
[2025-08-19 13:52:35.564]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\managers\manager_multimodal.py", line 10, in <module>
    [2025-08-19 13:52:35.565] from unstructured.partition.pdf import partition_pdf
[2025-08-19 13:52:35.565]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured\partition\pdf.py", line 19, in <module>
    [2025-08-19 13:52:35.565] from unstructured_inference.inference.layout import DocumentLayout
[2025-08-19 13:52:35.565]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured_inference\inference\layout.py", line 18, in <module>
    [2025-08-19 13:52:35.565] from unstructured_inference.models.base import get_model
[2025-08-19 13:52:35.565]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured_inference\models\base.py", line 7, in <module>
    [2025-08-19 13:52:35.566] from unstructured_inference.models.detectron2onnx import (
[2025-08-19 13:52:35.566]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured_inference\models\detectron2onnx.py", line 6, in <module>
    [2025-08-19 13:52:35.566] import onnxruntime
[2025-08-19 13:52:35.566]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\onnxruntime\__init__.py", line 61, in <module>
    [2025-08-19 13:52:35.566] raise import_capi_exception
[2025-08-19 13:52:35.566]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\onnxruntime\__init__.py", line 24, in <module>
    [2025-08-19 13:52:35.567] from onnxruntime.capi._pybind_state import (
[2025-08-19 13:52:35.567]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\onnxruntime\capi\_pybind_state.py", line 32, in <module>
    [2025-08-19 13:52:35.567] from .onnxruntime_pybind11_state import *  # noqa
    [2025-08-19 13:52:35.567] ^[2025-08-19 13:52:35.567] ^[2025-08-19 13:52:35.567] ^[2025-08-19 13:52:35.567] ^[2025-08-19 13:52:35.567] ^[2025-08-19 13:52:35.567] ^[2025-08-19 13:52:35.567] ^[2025-08-19 13:52:35.567] ^[2025-08-19 13:52:35.567] ^[2025-08-19 13:52:35.567] ^[2025-08-19 13:52:35.567] ^[2025-08-19 13:52:35.567] ^[2025-08-19 13:52:35.567] ^[2025-08-19 13:52:35.567] ^[2025-08-19 13:52:35.567] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.568] ^[2025-08-19 13:52:35.569] ^[2025-08-19 13:52:35.569] ^[2025-08-19 13:52:35.569] ^[2025-08-19 13:52:35.569] ^[2025-08-19 13:52:35.569] ^[2025-08-19 13:52:35.569] ^[2025-08-19 13:52:35.569] ^[2025-08-19 13:52:35.569] ^[2025-08-19 13:52:35.569] ^
[2025-08-19 13:52:35.569] ImportError[2025-08-19 13:52:35.569] : [2025-08-19 13:52:35.569] DLL load failed while importing onnxruntime_pybind11_state: A dynamic link library (DLL) initialization routine failed.
