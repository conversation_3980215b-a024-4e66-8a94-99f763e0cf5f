from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from uuid import uuid4, UUID
import asyncio
from datetime import datetime, timezone, timedelta

from managers.scheduled_requests.core.user_manager import UserScheduledRequestManager
from managers.scheduled_requests.core.base_manager import BaseScheduledRequestManager
from managers.scheduled_requests.utils.config import UserQuotaConfig
from managers.scheduled_requests.utils.exceptions import UserQuotaExceededError
from userprofiles.ZairaUser import <PERSON><PERSON><PERSON>User, PERMISSION_LEVELS
from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest, ScheduleType
from endpoints.mybot_generic import MyBot_Generic

class TestUserScheduledRequestManager:
    """Comprehensive test class for UserScheduledRequestManager"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.test_quota_config = UserQuotaConfig(
            max_concurrent_tasks=5,
            daily_task_limit=50,
            memory_limit_mb=100,
            thread_pool_size=3,
            max_task_duration_hours=24,
            max_recurring_tasks=10
        )
        
        # Create test user
        self.test_user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            GUID=UUID(self.test_user_guid),
            device_GUID=uuid4()
        )
        
        # Create test bot
        self.test_bot = Mock(spec=MyBot_Generic)
        self.test_bot.name = "test_bot"
    
    def teardown_method(self):
        """Clean up after each test"""
        pass
    
    def test_manager_initialization(self):
        """Test UserScheduledRequestManager initialization"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        
        assert manager.user_guid == self.test_user_guid
        assert manager.quota_config == self.test_quota_config
        assert isinstance(manager._active_requests, dict)
        assert isinstance(manager._paused_requests, dict)
        assert isinstance(manager._completed_requests, dict)
        assert len(manager._active_requests) == 0
        assert manager._recurring_tasks_count == 0
        assert not manager._initialized
    
    @pytest.mark.asyncio
    async def test_manager_setup(self):
        """Test manager setup process"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        
        with patch.object(manager, '_load_user_requests') as mock_load:
            mock_load.return_value = None
            
            await manager.setup()
            
            assert manager._initialized
            assert manager._thread_pool is not None
            assert manager._thread_pool._max_workers == self.test_quota_config.thread_pool_size
            mock_load.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_quota_availability_check_success(self):
        """Test quota availability check when within limits"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        await manager.setup()
        
        available, reason = await manager.check_quota_availability("create_task", 10.0)
        
        assert available is True
        assert reason is None
    
    @pytest.mark.asyncio
    async def test_quota_availability_check_concurrent_limit(self):
        """Test quota availability check when concurrent limit exceeded"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        await manager.setup()
        
        # Simulate max concurrent tasks reached
        manager._concurrent_tasks._value = self.test_quota_config.max_concurrent_tasks
        
        available, reason = await manager.check_quota_availability("create_task", 10.0)
        
        assert available is False
        assert "Concurrent tasks limit exceeded" in reason
    
    @pytest.mark.asyncio
    async def test_quota_availability_check_memory_limit(self):
        """Test quota availability check when memory limit would be exceeded"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        await manager.setup()
        
        # Set current memory usage near limit
        manager._memory_tracker.track_user_memory(self.test_user_guid, 95.0)
        
        available, reason = await manager.check_quota_availability("create_task", 10.0)
        
        assert available is False
        assert "Memory limit would be exceeded" in reason
    
    @pytest.mark.asyncio
    async def test_create_scheduled_request_success(self):
        """Test successful creation of scheduled request"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        await manager.setup()
        
        # Create mock scheduled request
        scheduled_request = Mock(spec=ScheduledZairaRequest)
        scheduled_request.user = self.test_user
        scheduled_request.scheduled_guid = uuid4()
        scheduled_request.schedule_type = Mock()
        scheduled_request.schedule_type.value = "once"
        
        with patch.object(manager, '_start_request_in_thread') as mock_start:
            success = await manager.create_scheduled_request(scheduled_request)
            
            assert success is True
            assert str(scheduled_request.scheduled_guid) in manager._active_requests
            mock_start.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_scheduled_request_unauthorized(self):
        """Test creation fails with wrong user"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        await manager.setup()
        
        # Create scheduled request for different user
        wrong_user = ZairaUser(
            username="wrong_user",
            rank=PERMISSION_LEVELS.USER,
            GUID=uuid4(),
            device_GUID=uuid4()
        )
        
        scheduled_request = Mock(spec=ScheduledZairaRequest)
        scheduled_request.user = wrong_user
        scheduled_request.scheduled_guid = uuid4()
        
        with pytest.raises(Exception):  # Should raise UnauthorizedScheduledRequestError
            await manager.create_scheduled_request(scheduled_request)
    
    @pytest.mark.asyncio
    async def test_create_scheduled_request_quota_exceeded(self):
        """Test creation fails when quota exceeded"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        await manager.setup()
        
        # Set concurrent tasks to limit
        manager._concurrent_tasks._value = self.test_quota_config.max_concurrent_tasks
        
        scheduled_request = Mock(spec=ScheduledZairaRequest)
        scheduled_request.user = self.test_user
        scheduled_request.scheduled_guid = uuid4()
        scheduled_request.schedule_type = Mock()
        scheduled_request.schedule_type.value = "once"
        
        with pytest.raises(UserQuotaExceededError):
            await manager.create_scheduled_request(scheduled_request)
    
    @pytest.mark.asyncio
    async def test_cancel_scheduled_request_success(self):
        """Test successful cancellation of scheduled request"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        await manager.setup()
        
        # Add a mock request
        scheduled_guid = str(uuid4())
        mock_request = Mock(spec=ScheduledZairaRequest)
        mock_request.schedule_type = Mock()
        mock_request.schedule_type.value = "once"
        mock_request.get_schedule_info.return_value = {"test": "data"}
        
        manager._active_requests[scheduled_guid] = mock_request
        
        success = await manager.cancel_scheduled_request(scheduled_guid, "Test cancellation")
        
        assert success is True
        assert scheduled_guid not in manager._active_requests
        assert scheduled_guid in manager._completed_requests
        mock_request.cancel_schedule.assert_called_once_with("Test cancellation")
    
    @pytest.mark.asyncio
    async def test_cancel_nonexistent_request(self):
        """Test cancellation of nonexistent request"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        await manager.setup()
        
        nonexistent_guid = str(uuid4())
        
        success = await manager.cancel_scheduled_request(nonexistent_guid, "Test")
        
        assert success is False
    
    @pytest.mark.asyncio
    async def test_get_user_requests(self):
        """Test getting user requests"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        await manager.setup()
        
        # Add mock active request
        active_guid = str(uuid4())
        mock_active = Mock(spec=ScheduledZairaRequest)
        mock_active.get_schedule_info.return_value = {"type": "active"}
        mock_active.get_request_status.return_value = {"status": "running"}
        manager._active_requests[active_guid] = mock_active
        
        # Add mock completed request
        completed_guid = str(uuid4())
        manager._completed_requests[completed_guid] = {
            'scheduled_guid': completed_guid,
            'status': 'completed'
        }
        
        # Test without completed requests
        requests = await manager.get_user_requests(include_completed=False)
        assert len(requests) == 1
        assert requests[0]['scheduled_guid'] == active_guid
        
        # Test with completed requests
        requests = await manager.get_user_requests(include_completed=True)
        assert len(requests) == 2
    
    @pytest.mark.asyncio
    async def test_get_request_status_active(self):
        """Test getting status of active request"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        await manager.setup()
        
        scheduled_guid = str(uuid4())
        mock_request = Mock(spec=ScheduledZairaRequest)
        mock_request.get_schedule_info.return_value = {"type": "test"}
        mock_request.get_request_status.return_value = {"status": "active"}
        
        manager._active_requests[scheduled_guid] = mock_request
        
        status = await manager.get_request_status(scheduled_guid)
        
        assert status is not None
        assert status['scheduled_guid'] == scheduled_guid
        assert status['status'] == 'active'
    
    @pytest.mark.asyncio
    async def test_get_request_status_nonexistent(self):
        """Test getting status of nonexistent request"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        await manager.setup()
        
        nonexistent_guid = str(uuid4())
        
        status = await manager.get_request_status(nonexistent_guid)
        
        assert status is None
    
    @pytest.mark.asyncio
    async def test_manager_shutdown(self):
        """Test manager shutdown process"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        await manager.setup()
        
        # Add mock request
        mock_request = Mock(spec=ScheduledZairaRequest)
        manager._active_requests[str(uuid4())] = mock_request
        
        await manager.shutdown()
        
        assert manager.is_shutdown()
        assert manager._thread_pool is None
        mock_request.cancel_schedule.assert_called()
    
    def test_manager_properties(self):
        """Test manager property methods"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        
        assert manager.get_active_request_count() == 0
        assert manager.get_recurring_task_count() == 0
        assert not manager.is_initialized()
        assert not manager.is_shutdown()
    
    @pytest.mark.asyncio
    async def test_recurring_tasks_limit(self):
        """Test recurring tasks limit enforcement"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        await manager.setup()
        
        # Set recurring tasks to limit
        manager._recurring_tasks_count = self.test_quota_config.max_recurring_tasks
        
        scheduled_request = Mock(spec=ScheduledZairaRequest)
        scheduled_request.user = self.test_user
        scheduled_request.scheduled_guid = uuid4()
        scheduled_request.schedule_type = Mock()
        scheduled_request.schedule_type.value = "recurring"
        
        with pytest.raises(UserQuotaExceededError):
            await manager.create_scheduled_request(scheduled_request)
    
    @pytest.mark.asyncio
    async def test_memory_tracking(self):
        """Test memory tracking functionality"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        await manager.setup()
        
        # Track memory usage
        manager._memory_tracker.track_user_memory(self.test_user_guid, 50.0)
        
        memory_usage = manager._memory_tracker.get_user_memory(self.test_user_guid)
        assert memory_usage == 50.0
        
        # Test quota check with memory
        available, reason = await manager.check_quota_availability("create_task", 60.0)
        assert available is False
        assert "Memory limit would be exceeded" in reason
    
    def test_invalid_guid_handling(self):
        """Test handling of invalid GUIDs"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        
        # Test with invalid GUID format
        with pytest.raises(Exception):
            asyncio.run(manager.get_request_status("invalid-guid"))
    
    @pytest.mark.asyncio 
    async def test_daily_quota_reset(self):
        """Test daily quota reset functionality"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        await manager.setup()
        
        # Set daily tasks
        manager._daily_tasks._value = 10
        
        # Simulate day change
        yesterday = datetime.now(timezone.utc).date() - timedelta(days=1)
        manager._last_reset_date = yesterday
        
        # Check quota availability should trigger reset
        available, reason = await manager.check_quota_availability("create_task")
        
        assert available is True
        assert manager._daily_tasks.get_value() == 0  # Should be reset
        assert manager._last_reset_date == datetime.now(timezone.utc).date()
    
    @pytest.mark.asyncio
    async def test_load_user_requests_error_handling(self):
        """Test error handling in load_user_requests"""
        manager = UserScheduledRequestManager(self.test_user_guid, self.test_quota_config)
        
        with patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager') as mock_persistence_class:
            mock_persistence = Mock()
            mock_persistence.get_active_requests.side_effect = Exception("Database error")
            mock_persistence_class.get_instance.return_value = mock_persistence
            
            # Should not raise exception, just log error
            await manager._initialize()
            
            assert manager._initialized  # Should still be initialized despite load error