from imports import *

from aiohttp import web

from endpoints.oauth._verifier_ import OAuth2App
from managers.manager_logfire import LogFire

class OAuth2Woocommerce(OAuth2App):
    def setup(self, myname):
        super().setup(myname)
        self.create_input("input", ["str:Consumer Key", "str:Consumer Secret", "str:WooCommerce URL", "str:Data verzamelen vanaf (YYYY-MM-DD)"]) \
                                    .set_meltano({
                                        "TAP_WOOCOMMERCE_CONSUMER_KEY": "access_token",
                                        "TAP_WOOCOMMERCE_CONSUMER_SECRET": "refresh_token",
                                        "TAP_WOOCOMMERCE_SITE_URL": "token_type",
                                        "TAP_WOOCOMMERCE_START_DATE": "str1"
                                    }) \
                                    .set_commands(["extract_woocommerce"])

    async def on_success_return(self, request: web.Request) -> str:
        # Wordt getoond zodra de koppeling gelukt is
        ret_html = await super().on_success_return(request)
        ret_html += "Woocommerce koppeling wordt gestart."
        return ret_html

    async def on_success_execute(self, request: web.Request) -> str:
        # Mits de return != "", wordt getoond zodra on_success_execute klaar is
        ret_html = await super().on_success_execute(request)
        # Extract WooCommerce data and store in vector database
        LogFire.log("INIT", "Starting WooCommerce data extraction")
        LogFire.log("DEBUG", "Starting WooCommerce data extraction", severity="debug")  # Console output for visibility
        try:
            from tasks.inputs.woocommerce_extractor import WooCommerceExtractor
            from endpoints.oauth._verifier_ import OAuth2Verifier

            # Get WooCommerce credentials from stored tokens
            consumer_key = await OAuth2Verifier.get_token("woocommerce", "access_token")
            consumer_secret = await OAuth2Verifier.get_token("woocommerce", "refresh_token")
            site_url = await OAuth2Verifier.get_token("woocommerce", "token_type")
            start_date = await OAuth2Verifier.get_token("woocommerce", "str1")

            from datetime import datetime
            start_date = datetime.strptime(start_date, "%Y-%m-%d")

            LogFire.log("DEBUG", "WooCommerce credentials check:")
            LogFire.log("DEBUG", f"- Consumer Key: {'Available' if consumer_key else 'Missing'}")
            LogFire.log("DEBUG", f"- Consumer Secret: {'Available' if consumer_secret else 'Missing'}")
            LogFire.log("DEBUG", f"- Site URL: {site_url if site_url else 'Missing'}")
            LogFire.log("DEBUG", f"- Start Date: {start_date.date().isoformat() if start_date else 'Missing'}")
            LogFire.log("DEBUG", "WooCommerce credentials check:", severity="debug")  # Console output for visibility
            LogFire.log("DEBUG", f"- Consumer Key: {'[YES]' if consumer_key else '[NO]'}", severity="debug")
            LogFire.log("DEBUG", f"- Consumer Secret: {'[YES]' if consumer_secret else '[NO]'}", severity="debug")
            LogFire.log("DEBUG", f"- Site URL: {site_url if site_url else '[NO]'}", severity="debug")
            LogFire.log("DEBUG", f"- Start Date: {start_date.date().isoformat() if start_date else '[NO]'}", severity="debug")

            if consumer_key and consumer_secret and site_url:
                # Initialize the WooCommerce extractor
                extractor = await WooCommerceExtractor.setup_async(
                    consumer_key=consumer_key,
                    consumer_secret=consumer_secret,
                    site_url=site_url,
                    start_date=start_date
                )

                # Extract and process WooCommerce data
                extraction_success = await extractor.extract_and_store_data()

                if extraction_success:
                    LogFire.log("EVENT", "WooCommerce data extraction completed successfully")
                    LogFire.log("DEBUG", " WooCommerce data extraction completed successfully", severity="debug")  # Console output for confirmation
                else:
                    LogFire.log("ERROR", "WooCommerce data extraction failed", severity="error")
                    LogFire.log("ERROR", "Check the logs above for detailed error information", severity="error")
                    LogFire.log("DEBUG", " WooCommerce data extraction failed", severity="debug")  # Console output for troubleshooting
                    LogFire.log("DEBUG", "Check the logs above for detailed error information", severity="debug")
                    # Don't set ret_val = False here as this might be due to Meltano setup issues
                    # The OAuth process itself was successful

                LogFire.log("DEBUG", "Finished WooCommerce data extraction", severity="debug")
            else:
                LogFire.log("ERROR", "Missing WooCommerce credentials", severity="error")
                LogFire.log("ERROR", "Please ensure all required fields are filled:", severity="error")
                LogFire.log("ERROR", "- Consumer Key, Consumer Secret, WooCommerce URL, Start Date", severity="error")
                ret_val = False
        except Exception as e:
            LogFire.log("ERROR", f" Error during WooCommerce extraction: {e}", severity="error")
            import traceback
            traceback.print_exc()
            # Don't set ret_val = False for extraction errors, only for OAuth/credential errors
            LogFire.log("DEBUG", "OAuth process completed, but extraction encountered issues", severity="debug")
        ret_html += ""
        return ret_html
    
    async def on_success_execute_fail(self, request: web.Request) -> str:
        # Mits success_execute, wordt getoond als on_success_execute faalt
        ret_html = await super().on_success_execute(request)
        ret_html += ""
        return ret_html
