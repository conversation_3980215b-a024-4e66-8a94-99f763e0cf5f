from imports import *

from aiohttp import web

from endpoints.oauth._verifier_ import OAuth2App, OAuth2Verifier
from managers.manager_supervisors import SupervisorTaskState

class OAuth2IMAP(OAuth2App):
    def setup(self, myname):
        super().setup(myname)
        self.create_input("input", ["str:Server Naam", "int:Netwerk port", "str:E-mail adres", "str:E-mail wachtwoord", "bool:Opslaan in Zaira geheugen?"])

    async def on_success_return(self, request: web.Request) -> str:
        # Wordt getoond zodra de koppeling gelukt is
        ret_html = await super().on_success_return(request)
        ret_html += "<PERSON><PERSON><PERSON> van mails wordt gestart."
        return ret_html

    async def on_success_execute(self, request: web.Request) -> str:
        # Mits de return != "", wordt getoond zodra on_success_execute klaar is
        SAVE_IN_VECTOR = await OAuth2Verifier.get_token("imap", "int1")

        ret_html = await super().on_success_execute(request)
        from managers.manager_supervisors import SupervisorManager
        if SAVE_IN_VECTOR:
            await SupervisorManager.get_task("imap_retrieval_task").call_task_with_query("Execute the task", None)
        #await SupervisorManager.get_task("imap_idle_30_minute_session").call_task_with_query("Execute the task", None)
        ret_html += "Mails zijn succesvol opgehaald."
        
        # Create SYSTEM user scheduled request for recurring IMAP IDLE monitoring
        await self._create_imap_idle_scheduled_request()
        ret_html += " Recurring IMAP monitoring scheduled every 30 minutes."
        
        return ret_html
    
    async def on_success_execute_fail(self, request: web.Request) -> str:
        # Mits success_execute, wordt getoond als on_success_execute faalt
        ret_html = await super().on_success_execute_fail(request)
        ret_html += ""
        return ret_html
    
    async def _create_imap_idle_scheduled_request(self):
        """Create a SYSTEM user scheduled request for IMAP IDLE activation every 30 minutes"""
        try:
            from managers.manager_system_user import SystemUserManager
            from managers.scheduled_requests import ScheduledRequestPersistenceManager
            from tasks.inputs.task_scheduled_request_manager import CreateScheduledRequestTool
            
            # Get SYSTEM user
            system_user_manager = SystemUserManager.get_instance()
            system_user = await system_user_manager.get_system_user()
            
            if not system_user:
                LogFire.log("ERROR", "SYSTEM user not found for creating IMAP IDLE request")
                return
            
            # Define the request parameters first
            schedule_prompt = "IMAP IDLE Email Monitoring - Recurring every 30 minutes"
            target_prompt = "Start IMAP IDLE email monitoring session"
            
            # Check if IMAP IDLE request already exists using the same target_prompt
            persistence_manager = ScheduledRequestPersistenceManager.get_instance()
            existing_requests = await persistence_manager.get_active_requests(str(system_user_manager.SYSTEM_USER_GUID))
            
            # Find and cancel any existing IMAP IDLE requests
            for request in existing_requests:
                if request.get('target_prompt') and request.get('target_prompt') == target_prompt:
                    scheduled_guid = request.get('scheduled_guid')
                    if scheduled_guid:
                        LogFire.log("USER", f"Found existing IMAP IDLE request {scheduled_guid}, cancelling it first")
                        try:
                            await persistence_manager.cancel_task(
                                scheduled_guid, 
                                str(system_user_manager.SYSTEM_USER_GUID),
                                "Replacing with fresh IMAP IDLE request"
                            )
                            LogFire.log("USER", f"Successfully cancelled existing IMAP IDLE request {scheduled_guid}")
                        except Exception as cancel_error:
                            LogFire.log("ERROR", f"Failed to cancel existing IMAP IDLE request {scheduled_guid}: {cancel_error}")
                            # Continue with creating new request even if cancellation fails
            
            # Use the proper CreateScheduledRequestTool to create and start the request
            create_tool = CreateScheduledRequestTool()
            temp_state = SupervisorTaskState()
            temp_state.user_guid = system_user_manager.SYSTEM_USER_GUID
            temp_state.scheduled_guid = -1
            
            # Create the request using the proper tool (this will save AND start the request)
            result = await create_tool._arun(
                schedule_prompt=schedule_prompt,
                target_prompt=target_prompt,
                start_delay_seconds=2,  # Start in 2 seconds
                delay_seconds=1800,  # 30 minutes = 1800 seconds
                schedule_type="recurring",
                run_on_startup=True,
                state=temp_state
            )
            
            LogFire.log("USER", f"Created IMAP IDLE recurring request: {result}")
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to create IMAP IDLE scheduled request: {e}")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "OAuth2IMAP._create_imap_idle_scheduled_request", None)
