from imports import *
from typing import Dict, List, Optional, Any, Tuple
from datetime import datetime, timezone, timedelta
import hashlib
import ipaddress
import re

from ..utils.exceptions import (
    SecurityValidationError, UnauthorizedScheduledRequestError,
    ScheduledRequestNotFoundError
)
from ..utils.helpers import validate_guid, sanitize_user_input
from ..utils.config import ScheduledRequestsConfig

if TYPE_CHECKING:
    from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest
    from userprofiles.ZairaUser import ZairaUser

class ScheduledRequestSecurityValidator:
    """Security validation layer for scheduled requests"""
    
    def __init__(self):
        # Security patterns and rules
        self._blocked_patterns = [
            r'<script[^>]*>.*?</script>',  # XSS prevention
            r'javascript:',
            r'data:.*base64',
            r'eval\s*\(',
            r'exec\s*\(',
            r'__import__',
            r'subprocess',
            r'os\.system',
        ]
        
        # Compile patterns for performance
        self._compiled_patterns = [re.compile(pattern, re.IGNORECASE | re.DOTALL) 
                                 for pattern in self._blocked_patterns]
        
        # Rate limiting tracking
        self._failed_attempts: Dict[str, List[float]] = {}
        self._blocked_ips: Dict[str, float] = {}  # IP -> unblock_time
    
    async def validate_user_authorization(self, user: 'ZairaUser', operation: str, 
                                        scheduled_guid: str = None) -> bool:
        """
        Validate user authorization for scheduled request operations
        
        Args:
            user: User attempting the operation
            operation: Type of operation (create, read, update, delete)
            scheduled_guid: Optional GUID of specific request
            
        Returns:
            bool: True if authorized
            
        Raises:
            SecurityValidationError: If validation fails
            UnauthorizedScheduledRequestError: If user lacks authorization
        """
        try:
            user_guid = str(user.user_guid)
            
            # Validate user GUID format
            if not validate_guid(user_guid):
                raise SecurityValidationError("user_guid_validation", user_guid, "Invalid user GUID format")
            
            # Check if user account is active/valid
            if not await self._validate_user_account_status(user):
                raise UnauthorizedScheduledRequestError(user_guid, scheduled_guid or "system")
            
            # Operation-specific validation
            if operation == "create":
                return await self._validate_create_authorization(user)
            elif operation == "read":
                return await self._validate_read_authorization(user, scheduled_guid)
            elif operation in ["update", "delete", "cancel"]:
                return await self._validate_modify_authorization(user, scheduled_guid)
            else:
                raise SecurityValidationError("unknown_operation", user_guid, f"Unknown operation: {operation}")
            
        except (SecurityValidationError, UnauthorizedScheduledRequestError):
            raise
        except Exception as e:
            LogFire.log("ERROR", f"Security validation error for user {user.user_guid}: {str(e)}")
            raise SecurityValidationError("validation_error", str(user.user_guid), str(e))
    
    async def validate_request_content(self, scheduled_request: 'ScheduledZairaRequest') -> bool:
        """
        Validate the content of a scheduled request for security issues
        
        Args:
            scheduled_request: Request to validate
            
        Returns:
            bool: True if content is safe
            
        Raises:
            SecurityValidationError: If content contains security issues
        """
        try:
            user_guid = str(scheduled_request.user.user_guid)
            
            # Validate prompt content
            if not self._validate_prompt_content(scheduled_request.schedule_prompt):
                raise SecurityValidationError(
                    "malicious_schedule_prompt", user_guid, 
                    "Schedule prompt contains potentially malicious content"
                )
            
            if not self._validate_prompt_content(scheduled_request.target_prompt):
                raise SecurityValidationError(
                    "malicious_target_prompt", user_guid,
                    "Target prompt contains potentially malicious content"
                )
            
            # Validate timing parameters
            if not self._validate_timing_parameters(scheduled_request):
                raise SecurityValidationError(
                    "invalid_timing", user_guid,
                    "Invalid timing parameters detected"
                )
            
            # Validate schedule type and constraints
            if not self._validate_schedule_constraints(scheduled_request):
                raise SecurityValidationError(
                    "invalid_schedule", user_guid,
                    "Schedule constraints violation"
                )
            
            return True
            
        except SecurityValidationError:
            raise
        except Exception as e:
            LogFire.log("ERROR", f"Content validation error: {str(e)}")
            raise SecurityValidationError("content_validation", str(scheduled_request.user.user_guid), str(e))
    
    async def validate_ip_access(self, ip_address: str, user_guid: str) -> bool:
        """
        Validate IP address access and rate limiting
        
        Args:
            ip_address: Client IP address
            user_guid: User GUID for logging
            
        Returns:
            bool: True if IP is allowed
            
        Raises:
            SecurityValidationError: If IP is blocked or rate limited
        """
        try:
            # Validate IP format
            if not self._validate_ip_format(ip_address):
                raise SecurityValidationError("invalid_ip", user_guid, f"Invalid IP format: {ip_address}")
            
            # Check if IP is currently blocked
            if await self._is_ip_blocked(ip_address):
                raise SecurityValidationError("ip_blocked", user_guid, f"IP {ip_address} is temporarily blocked")
            
            # Check rate limiting
            if not await self._check_ip_rate_limit(ip_address):
                await self._block_ip_temporarily(ip_address)
                raise SecurityValidationError("rate_limit_exceeded", user_guid, f"Rate limit exceeded for IP {ip_address}")
            
            return True
            
        except SecurityValidationError:
            raise
        except Exception as e:
            LogFire.log("ERROR", f"IP validation error for {ip_address}: {str(e)}")
            raise SecurityValidationError("ip_validation", user_guid, str(e))
    
    async def validate_request_ownership(self, user_guid: str, scheduled_guid: str) -> bool:
        """
        Validate that user owns the specified scheduled request
        
        Args:
            user_guid: User GUID
            scheduled_guid: Scheduled request GUID
            
        Returns:
            bool: True if user owns the request
            
        Raises:
            UnauthorizedScheduledRequestError: If user doesn't own the request
        """
        try:
            from ..core.persistence import ScheduledRequestPersistenceManager
            persistence = ScheduledRequestPersistenceManager.get_instance()
            
            # Get request details
            requests = await persistence.get_active_requests(user_guid)
            
            # Check if request exists and belongs to user
            for request in requests:
                if request.get('scheduled_guid') == scheduled_guid:
                    return True
            
            # Request not found or doesn't belong to user
            raise UnauthorizedScheduledRequestError(user_guid, scheduled_guid)
            
        except UnauthorizedScheduledRequestError:
            raise
        except Exception as e:
            LogFire.log("ERROR", f"Ownership validation error: {str(e)}")
            raise SecurityValidationError("ownership_validation", user_guid, str(e))
    
    async def sanitize_request_data(self, request_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Sanitize request data for safe storage and processing
        
        Args:
            request_data: Raw request data
            
        Returns:
            Dict: Sanitized request data
        """
        sanitized = {}
        
        try:
            # Sanitize string fields
            string_fields = ['schedule_prompt', 'target_prompt', 'cancellation_reason']
            for field in string_fields:
                if field in request_data:
                    sanitized[field] = sanitize_user_input(
                        str(request_data[field]), 
                        max_length=2000 if 'prompt' in field else 500
                    )
            
            # Sanitize numeric fields
            numeric_fields = ['delay_seconds', 'start_delay_seconds']
            for field in numeric_fields:
                if field in request_data:
                    try:
                        value = float(request_data[field])
                        # Enforce reasonable limits
                        sanitized[field] = max(0, min(value, 86400 * 7))  # Max 1 week
                    except (ValueError, TypeError):
                        sanitized[field] = 0.0
            
            # Sanitize boolean fields
            boolean_fields = ['is_active', 'run_on_startup']
            for field in boolean_fields:
                if field in request_data:
                    sanitized[field] = bool(request_data[field])
            
            # Sanitize GUID fields
            guid_fields = ['user_guid', 'scheduled_guid']
            for field in guid_fields:
                if field in request_data:
                    guid_value = str(request_data[field])
                    if validate_guid(guid_value):
                        sanitized[field] = guid_value
            
            # Pass through safe fields
            safe_fields = ['schedule_type', 'calling_bot_name', 'call_trace', 'status', 'created_at', 'schedule_info', 'source']
            for field in safe_fields:
                if field in request_data:
                    sanitized[field] = request_data[field]
            
            return sanitized
            
        except Exception as e:
            LogFire.log("ERROR", f"Data sanitization error: {str(e)}")
            return {}
    
    def generate_audit_hash(self, user_guid: str, operation: str, data: Dict[str, Any]) -> str:
        """Generate hash for audit trail integrity"""
        try:
            # Create deterministic hash from key audit data
            audit_string = f"{user_guid}:{operation}:{datetime.now(timezone.utc).date().isoformat()}"
            for key in sorted(data.keys()):
                audit_string += f":{key}:{str(data[key])[:100]}"  # Limit field length
            
            return hashlib.sha256(audit_string.encode('utf-8')).hexdigest()[:16]
            
        except Exception as e:
            LogFire.log("ERROR", f"Audit hash generation error: {str(e)}")
            return ""
    
    async def _validate_user_account_status(self, user: 'ZairaUser') -> bool:
        """Validate user account is active and valid"""
        try:
            # Check if user object is valid
            if not user or not hasattr(user, 'user_guid'):
                return False
            
            # Additional user validation could be added here
            # For now, assume all users with valid GUID are active
            return validate_guid(str(user.user_guid))
            
        except Exception:
            return False
    
    async def _validate_create_authorization(self, user: 'ZairaUser') -> bool:
        """Validate user can create scheduled requests"""
        try:
            # Check user permission level
            if hasattr(user, 'rank'):
                from userprofiles.ZairaUser import PERMISSION_LEVELS
                # All permission levels can create requests, but with different quotas
                return user.rank in [PERMISSION_LEVELS.ADMIN, PERMISSION_LEVELS.USER, PERMISSION_LEVELS.GUEST]
            
            return True  # Default allow if no rank specified
            
        except Exception:
            return False
    
    async def _validate_read_authorization(self, user: 'ZairaUser', scheduled_guid: str = None) -> bool:
        """Validate user can read scheduled requests"""
        try:
            if scheduled_guid:
                # For specific request, validate ownership
                return await self.validate_request_ownership(str(user.user_guid), scheduled_guid)
            else:
                # For general read access, all authenticated users allowed
                return True
                
        except Exception:
            return False
    
    async def _validate_modify_authorization(self, user: 'ZairaUser', scheduled_guid: str) -> bool:
        """Validate user can modify/delete scheduled requests"""
        try:
            if not scheduled_guid:
                return False
            
            # Must own the request to modify it
            return await self.validate_request_ownership(str(user.user_guid), scheduled_guid)
            
        except Exception:
            return False
    
    def _validate_prompt_content(self, prompt: str) -> bool:
        """Validate prompt content for security issues"""
        try:
            if not prompt or not isinstance(prompt, str):
                return False
            
            # Check for malicious patterns
            for pattern in self._compiled_patterns:
                if pattern.search(prompt):
                    LogFire.log("WARNING", f"Blocked malicious pattern in prompt: {pattern.pattern}")
                    return False
            
            # Check length
            if len(prompt) > 10000:  # Max 10KB prompt
                return False
            
            # Ensure ASCII-only as per project standards
            try:
                prompt.encode('ascii')
            except UnicodeEncodeError:
                return False
            
            return True
            
        except Exception:
            return False
    
    def _validate_timing_parameters(self, scheduled_request: 'ScheduledZairaRequest') -> bool:
        """Validate timing parameters are reasonable"""
        try:
            # Check delay values
            if scheduled_request.delay_seconds < 0 or scheduled_request.delay_seconds > 86400 * 7:  # Max 1 week
                return False
            
            if scheduled_request.start_delay_seconds < 0 or scheduled_request.start_delay_seconds > 86400 * 7:
                return False
            
            # Check next execution time
            if scheduled_request.next_execution:
                now = datetime.now(timezone.utc)
                next_exec = scheduled_request.next_execution
                if next_exec.tzinfo is None:
                    next_exec = next_exec.replace(tzinfo=timezone.utc)
                
                # Don't allow scheduling too far in the future (1 year max)
                if next_exec > now + timedelta(days=365):
                    return False
            
            return True
            
        except Exception:
            return False
    
    def _validate_schedule_constraints(self, scheduled_request: 'ScheduledZairaRequest') -> bool:
        """Validate schedule constraints based on user permission level"""
        try:
            # Get user permission level
            user_rank = getattr(scheduled_request.user, 'rank', None)
            if not user_rank:
                return True  # Default allow if no rank
            
            # Get quota config for user
            quota_config = ScheduledRequestsConfig.get_user_quota_config(user_rank)
            
            # Validate task duration doesn't exceed user limits
            max_duration_seconds = quota_config.max_task_duration_hours * 3600
            
            if scheduled_request.schedule_type.value == "recurring":
                # For recurring tasks, ensure delay isn't too short for user level
                min_recurring_delay = {
                    'ADMIN': 60,      # 1 minute
                    'USER': 300,      # 5 minutes  
                    'GUEST': 3600     # 1 hour
                }.get(user_rank.name, 3600)
                
                if scheduled_request.delay_seconds < min_recurring_delay:
                    return False
            
            return True
            
        except Exception:
            return False
    
    def _validate_ip_format(self, ip_address: str) -> bool:
        """Validate IP address format"""
        try:
            ipaddress.ip_address(ip_address)
            return True
        except (ValueError, TypeError):
            return False
    
    async def _is_ip_blocked(self, ip_address: str) -> bool:
        """Check if IP is currently blocked"""
        try:
            if ip_address in self._blocked_ips:
                unblock_time = self._blocked_ips[ip_address]
                current_time = datetime.now(timezone.utc).timestamp()
                
                if current_time < unblock_time:
                    return True
                else:
                    # Unblock expired blocks
                    del self._blocked_ips[ip_address]
                    
            return False
            
        except Exception:
            return False
    
    async def _check_ip_rate_limit(self, ip_address: str) -> bool:
        """Check IP rate limiting"""
        try:
            current_time = datetime.now(timezone.utc).timestamp()
            window_size = 300  # 5 minutes
            max_requests = 100  # Max requests per window
            
            # Initialize tracking for new IPs
            if ip_address not in self._failed_attempts:
                self._failed_attempts[ip_address] = []
            
            # Clean old attempts
            attempts = self._failed_attempts[ip_address]
            cutoff_time = current_time - window_size
            self._failed_attempts[ip_address] = [t for t in attempts if t > cutoff_time]
            
            # Check rate limit
            if len(self._failed_attempts[ip_address]) >= max_requests:
                return False
            
            # Add current attempt
            self._failed_attempts[ip_address].append(current_time)
            return True
            
        except Exception:
            return False
    
    async def _block_ip_temporarily(self, ip_address: str):
        """Temporarily block an IP address"""
        try:
            block_duration = ScheduledRequestsConfig.LOCKOUT_DURATION_MINUTES * 60
            unblock_time = datetime.now(timezone.utc).timestamp() + block_duration
            
            self._blocked_ips[ip_address] = unblock_time
            LogFire.log("WARNING", f"Temporarily blocked IP {ip_address} for {block_duration} seconds")
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to block IP {ip_address}: {str(e)}")
    
    def get_security_metrics(self) -> Dict[str, Any]:
        """Get security-related metrics"""
        current_time = datetime.now(timezone.utc).timestamp()
        
        # Count active blocks
        active_blocks = sum(1 for unblock_time in self._blocked_ips.values() 
                          if unblock_time > current_time)
        
        # Count recent failed attempts (last hour)
        recent_attempts = 0
        cutoff_time = current_time - 3600
        for attempts in self._failed_attempts.values():
            recent_attempts += sum(1 for attempt_time in attempts if attempt_time > cutoff_time)
        
        return {
            'active_ip_blocks': active_blocks,
            'total_blocked_ips': len(self._blocked_ips),
            'recent_failed_attempts': recent_attempts,
            'tracked_ips': len(self._failed_attempts),
            'security_patterns_count': len(self._compiled_patterns)
        }