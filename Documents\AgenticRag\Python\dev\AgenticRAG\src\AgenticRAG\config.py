# AIRBYTE CREDENTIALS
AIRBYTE_AIRTABLE = "**********************************************************************************"
# AIRBYTE CREDENTIALS

# DISCORD CREDENTIALS
DISCORD_TOKEN = "MTM3Njg0OTgxODQ4MzQyOTQxNg.G6bhbS.5OJmpLFKaunt-BXpqhYhJM-pg19PKdTwjbsHeI"
DISCORD_BOT_CLIENT_ID = 1376849818483429416
DISCORD_BOT_CLIENT_SECRET = "HtQk-LwT4D9j4Rx4TUOAEbcXyGEHIiKR"
# DISCORD CREDENTIALS

# SLACK CREDENTIALS
SLACK_BOT_CLIENT_ID = "8748468813782.8742054652855"
SLACK_BOT_CLIENT_SECRET = "a8bebc074a1be68fff746959ccf862f3"
SLACK_BOT_SIGNING_SECRET = "9db1a68a8a9974b90b6256bf6ba04e0c"
SLACK_BOT_APP_TOKEN = "xapp-1-A08MU1LK6R5-8839751515665-a66919191d801c63765cbb6dc82addaa911975818e414933f289ea13baaf49cf"
# SLACK CREDENTIALS

# GOOGLE CREDENTIALS
GOOGLE_BOT_CLIENT_ID = "454683121268-70jmk1n562aupnmpk9mhgnbqkpq7oghe.apps.googleusercontent.com"#"655020897145-0p34jdgsk55voeupn9r6ji8lg4ipeaga.apps.googleusercontent.com"
GOOGLE_BOT_CLIENT_SECRET = "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"#"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"
GOOGLE_ADS_BOT_CLIENT_ID = "655020897145-0p34jdgsk55voeupn9r6ji8lg4ipeaga.apps.googleusercontent.com"
GOOGLE_ADS_BOT_CLIENT_SECRET = "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"
# GOOGLE CREDENTIALS

# MICROSOFT TEAMS CREDENTIALS
MICROSOFT_AZURE_BOT_ID = "df6ac12f-9ad8-4148-b4e4-5fd1174d5f4c"
MICROSOFT_AZURE_BOT_SECRET = "****************************************"
# MICROSOFT TEAMS CREDENTIALS

# Set up API keys
from os import environ
# LANGSMITH CREDENTIALS
environ["LANGSMITH_TRACING"] = "true"
environ["LANGSMITH_ENDPOINT"] = "https://api.smith.langchain.com"
environ["LANGSMITH_API_KEY"] = "***************************************************"
environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
# LANGSMITH CREDENTIALS
environ["OPENAI_API_KEY"] = "********************************************************************************************************************************************************************"
environ["TAVILY_API_KEY"] = '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'




# WHATSAPP CREDENTIALS
# Meta Cloud API credentials
META_APP_ID = "****************"
META_APP_SECRET = "d11509dd3584737ec0cf4a9221bbc56b"
# WhatsApp Business Phone Number ID
WHATSAPP_PHONE_NUMBER_ID = "***************"
# WhatsApp Business Account ID
WHATSAPP_BUSINESS_ACCOUNT_ID = "****************"
# Verification token set in the Meta dashboard
WHATSAPP_VERIFY_TOKEN = "12346"
# Access token (this should be stored securely in a production environment)
WHATSAPP_ACCESS_TOKEN = "EAAaI3y6NHa8BPEcLALNGrOxZAcZAlm2O8DcSzBZCVLOZC8HzhWl0eqZA9BZCOZC1NOCKApjCV1UDN8FweWaerWNXuVtZBE6ubucIftAEUSWyGfJeT6XKnwBbbd7bb9gJW6gh7vY9hTZA2rGOvREliEwZBAH3ESXfaHWOLnsSspkMsJutFM2uMigFeZBjZAtm4doBVh6YSAZDZD"

WHATSAPP_RECIPIENT_WAID="+***********" # Your WhatsApp number with country code (e.g., +***********)


#KLANT GEBASEERDE PROMPTs

# HANDTEKENING_SIEP_HGSPORTS_MAIL = """Vriendelijke groet,

# Siep van de Reijt | H-G Sports | Toegankelijker sporten! | www.h-gsports.nl
# Mobiel: +*********** | E: <EMAIL>
# Prins Willemstraat 10 | 4791 JR | Klundert"""

# # Email signature configuration
# SIGNATURE_IMAGE_PATH = r"C:\Users\<USER>\Documents\AgenticRag\Python\Test files\KIxIv6vR0pWoxgxUkvrr03UWs0sm49uDqw.png"
# DEFAULT_SIGNATURE_TEXT = HANDTEKENING_SIEP_HGSPORTS_MAIL
# DEFAULT_SIGNATURE_IMAGE_PATH = SIGNATURE_IMAGE_PATH