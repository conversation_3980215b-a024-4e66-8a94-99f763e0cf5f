from imports import *
from typing import Dict, List, Optional, Any
from datetime import datetime, timezone, timedelta
import asyncio
import threading
from collections import defaultdict, deque

from ..utils.helpers import ThreadSafeCounter
from ..utils.config import ScheduledRequestsConfig

class MetricsCollector:
    """Centralized metrics collection for scheduled requests system"""
    
    def __init__(self):
        # Metrics storage with time-series data
        self._metrics_data: Dict[str, deque] = defaultdict(lambda: deque(maxlen=1000))
        self._metrics_lock = threading.Lock()
        
        # Real-time counters
        self._counters = {
            'requests_created': ThreadSafeCounter(),
            'requests_completed': ThreadSafeCounter(),
            'requests_failed': ThreadSafeCounter(),
            'requests_cancelled': ThreadSafeCounter(),
            'security_violations': ThreadSafeCounter(),
            'quota_violations': ThreadSafeCounter(),
            'rate_limit_violations': ThreadSafeCounter()
        }
        
        # Performance metrics
        self._performance_metrics = {
            'average_execution_time': 0.0,
            'total_execution_time': 0.0,
            'execution_count': 0,
            'peak_concurrent_tasks': 0,
            'peak_memory_usage': 0.0
        }
        self._performance_lock = threading.Lock()
        
        # System health indicators
        self._health_indicators = {
            'last_update': datetime.now(timezone.utc).timestamp(),
            'system_load': 0.0,
            'database_health': 'unknown',
            'memory_health': 'unknown',
            'thread_pool_health': 'unknown'
        }
        
        # Background collection task
        self._collection_task: Optional[asyncio.Task] = None
        self._shutdown = False
    
    async def setup(self):
        """Initialize metrics collection"""
        if not self._collection_task:
            self._collection_task = asyncio.create_task(self._periodic_collection())
    
    async def shutdown(self):
        """Shutdown metrics collection"""
        self._shutdown = True
        
        if self._collection_task:
            self._collection_task.cancel()
            try:
                await self._collection_task
            except asyncio.CancelledError:
                pass
        
        LogFire.log("INIT", "MetricsCollector shutdown complete")
    
    def record_request_created(self, user_guid: str, request_type: str):
        """Record a new request creation"""
        timestamp = datetime.now(timezone.utc).timestamp()
        
        self._counters['requests_created'].increment()
        
        with self._metrics_lock:
            self._metrics_data['requests_created'].append({
                'timestamp': timestamp,
                'user_guid': user_guid,
                'request_type': request_type
            })
    
    def record_request_completed(self, user_guid: str, execution_time: float, success: bool):
        """Record request completion"""
        timestamp = datetime.now(timezone.utc).timestamp()
        
        if success:
            self._counters['requests_completed'].increment()
        else:
            self._counters['requests_failed'].increment()
        
        # Update performance metrics
        with self._performance_lock:
            self._performance_metrics['execution_count'] += 1
            self._performance_metrics['total_execution_time'] += execution_time
            self._performance_metrics['average_execution_time'] = (
                self._performance_metrics['total_execution_time'] / 
                self._performance_metrics['execution_count']
            )
        
        with self._metrics_lock:
            self._metrics_data['requests_completed'].append({
                'timestamp': timestamp,
                'user_guid': user_guid,
                'execution_time': execution_time,
                'success': success
            })
    
    def record_request_cancelled(self, user_guid: str, reason: str):
        """Record request cancellation"""
        timestamp = datetime.now(timezone.utc).timestamp()
        
        self._counters['requests_cancelled'].increment()
        
        with self._metrics_lock:
            self._metrics_data['requests_cancelled'].append({
                'timestamp': timestamp,
                'user_guid': user_guid,
                'reason': reason
            })
    
    def record_security_violation(self, user_guid: str, violation_type: str, details: str):
        """Record security violation"""
        timestamp = datetime.now(timezone.utc).timestamp()
        
        self._counters['security_violations'].increment()
        
        with self._metrics_lock:
            self._metrics_data['security_violations'].append({
                'timestamp': timestamp,
                'user_guid': user_guid,
                'violation_type': violation_type,
                'details': details
            })
    
    def record_quota_violation(self, user_guid: str, quota_type: str, current_value: int, limit: int):
        """Record quota violation"""
        timestamp = datetime.now(timezone.utc).timestamp()
        
        self._counters['quota_violations'].increment()
        
        with self._metrics_lock:
            self._metrics_data['quota_violations'].append({
                'timestamp': timestamp,
                'user_guid': user_guid,
                'quota_type': quota_type,
                'current_value': current_value,
                'limit': limit
            })
    
    def record_rate_limit_violation(self, user_guid: str, rate_type: str, retry_after: float):
        """Record rate limit violation"""
        timestamp = datetime.now(timezone.utc).timestamp()
        
        self._counters['rate_limit_violations'].increment()
        
        with self._metrics_lock:
            self._metrics_data['rate_limit_violations'].append({
                'timestamp': timestamp,
                'user_guid': user_guid,
                'rate_type': rate_type,
                'retry_after': retry_after
            })
    
    def update_system_metrics(self, concurrent_tasks: int, memory_usage: float, system_load: float):
        """Update system-wide metrics"""
        with self._performance_lock:
            self._performance_metrics['peak_concurrent_tasks'] = max(
                self._performance_metrics['peak_concurrent_tasks'], concurrent_tasks
            )
            self._performance_metrics['peak_memory_usage'] = max(
                self._performance_metrics['peak_memory_usage'], memory_usage
            )
        
        self._health_indicators['system_load'] = system_load
        self._health_indicators['last_update'] = datetime.now(timezone.utc).timestamp()
    
    def update_health_indicators(self, database_health: str, memory_health: str, thread_pool_health: str):
        """Update system health indicators"""
        self._health_indicators.update({
            'database_health': database_health,
            'memory_health': memory_health,
            'thread_pool_health': thread_pool_health,
            'last_update': datetime.now(timezone.utc).timestamp()
        })
    
    def get_current_metrics(self) -> Dict[str, Any]:
        """Get current metrics snapshot"""
        with self._performance_lock:
            performance_metrics = self._performance_metrics.copy()
        
        return {
            'counters': {name: counter.get_value() for name, counter in self._counters.items()},
            'performance': performance_metrics,
            'health_indicators': self._health_indicators.copy(),
            'timestamp': datetime.now(timezone.utc).timestamp()
        }
    
    def get_time_series_data(self, metric_name: str, time_range_minutes: int = 60) -> List[Dict[str, Any]]:
        """Get time series data for specific metric"""
        cutoff_time = datetime.now(timezone.utc).timestamp() - (time_range_minutes * 60)
        
        with self._metrics_lock:
            if metric_name in self._metrics_data:
                return [
                    entry for entry in self._metrics_data[metric_name]
                    if entry['timestamp'] > cutoff_time
                ]
        
        return []
    
    def get_user_metrics_summary(self, user_guid: str, time_range_hours: int = 24) -> Dict[str, Any]:
        """Get metrics summary for specific user"""
        cutoff_time = datetime.now(timezone.utc).timestamp() - (time_range_hours * 3600)
        
        user_metrics = {
            'requests_created': 0,
            'requests_completed': 0,
            'requests_failed': 0,
            'requests_cancelled': 0,
            'security_violations': 0,
            'quota_violations': 0,
            'rate_limit_violations': 0,
            'average_execution_time': 0.0,
            'total_execution_time': 0.0
        }
        
        with self._metrics_lock:
            # Count user-specific events
            for metric_name, metric_data in self._metrics_data.items():
                for entry in metric_data:
                    if (entry.get('user_guid') == user_guid and 
                        entry['timestamp'] > cutoff_time):
                        
                        if metric_name in user_metrics:
                            user_metrics[metric_name] += 1
                        
                        # Track execution times
                        if metric_name == 'requests_completed' and 'execution_time' in entry:
                            user_metrics['total_execution_time'] += entry['execution_time']
        
        # Calculate average execution time
        if user_metrics['requests_completed'] > 0:
            user_metrics['average_execution_time'] = (
                user_metrics['total_execution_time'] / 
                user_metrics['requests_completed']
            )
        
        return user_metrics
    
    def get_system_performance_summary(self, time_range_hours: int = 1) -> Dict[str, Any]:
        """Get system performance summary"""
        cutoff_time = datetime.now(timezone.utc).timestamp() - (time_range_hours * 3600)
        
        # Count recent events
        recent_metrics = {
            'requests_per_hour': 0,
            'completion_rate': 0.0,
            'average_response_time': 0.0,
            'error_rate': 0.0,
            'top_users': [],
            'trending_metrics': {}
        }
        
        with self._metrics_lock:
            # Count recent requests
            recent_created = [
                entry for entry in self._metrics_data['requests_created']
                if entry['timestamp'] > cutoff_time
            ]
            recent_completed = [
                entry for entry in self._metrics_data['requests_completed']
                if entry['timestamp'] > cutoff_time
            ]
            recent_failed = [
                entry for entry in self._metrics_data['requests_failed']
                if entry['timestamp'] > cutoff_time
            ]
            
            recent_metrics['requests_per_hour'] = len(recent_created)
            
            # Calculate completion rate
            total_finished = len(recent_completed) + len(recent_failed)
            if total_finished > 0:
                recent_metrics['completion_rate'] = len(recent_completed) / total_finished
                recent_metrics['error_rate'] = len(recent_failed) / total_finished
            
            # Calculate average response time
            if recent_completed:
                total_time = sum(entry.get('execution_time', 0) for entry in recent_completed)
                recent_metrics['average_response_time'] = total_time / len(recent_completed)
            
            # Get top users by activity
            user_activity = defaultdict(int)
            for entry in recent_created:
                user_activity[entry.get('user_guid', 'unknown')] += 1
            
            recent_metrics['top_users'] = sorted(
                [{'user_guid': uid, 'requests': count} for uid, count in user_activity.items()],
                key=lambda x: x['requests'],
                reverse=True
            )[:10]
        
        return recent_metrics
    
    def get_trending_violations(self, time_range_hours: int = 24) -> Dict[str, Any]:
        """Get trending violation patterns"""
        cutoff_time = datetime.now(timezone.utc).timestamp() - (time_range_hours * 3600)
        
        violation_trends = {
            'security_trends': defaultdict(int),
            'quota_trends': defaultdict(int),
            'rate_limit_trends': defaultdict(int),
            'user_violation_counts': defaultdict(int),
            'hourly_distribution': defaultdict(int)
        }
        
        with self._metrics_lock:
            # Analyze security violations
            for entry in self._metrics_data['security_violations']:
                if entry['timestamp'] > cutoff_time:
                    violation_trends['security_trends'][entry.get('violation_type', 'unknown')] += 1
                    violation_trends['user_violation_counts'][entry.get('user_guid', 'unknown')] += 1
                    
                    # Hourly distribution
                    hour = datetime.fromtimestamp(entry['timestamp'], timezone.utc).hour
                    violation_trends['hourly_distribution'][hour] += 1
            
            # Analyze quota violations
            for entry in self._metrics_data['quota_violations']:
                if entry['timestamp'] > cutoff_time:
                    violation_trends['quota_trends'][entry.get('quota_type', 'unknown')] += 1
                    violation_trends['user_violation_counts'][entry.get('user_guid', 'unknown')] += 1
            
            # Analyze rate limit violations
            for entry in self._metrics_data['rate_limit_violations']:
                if entry['timestamp'] > cutoff_time:
                    violation_trends['rate_limit_trends'][entry.get('rate_type', 'unknown')] += 1
                    violation_trends['user_violation_counts'][entry.get('user_guid', 'unknown')] += 1
        
        # Convert to regular dicts and sort
        return {
            'security_trends': dict(violation_trends['security_trends']),
            'quota_trends': dict(violation_trends['quota_trends']),
            'rate_limit_trends': dict(violation_trends['rate_limit_trends']),
            'top_violating_users': sorted(
                [{'user_guid': uid, 'violations': count} 
                 for uid, count in violation_trends['user_violation_counts'].items()],
                key=lambda x: x['violations'],
                reverse=True
            )[:10],
            'hourly_distribution': dict(violation_trends['hourly_distribution'])
        }
    
    async def _periodic_collection(self):
        """Background metrics collection"""
        while not self._shutdown:
            try:
                await asyncio.sleep(60)  # Collect every minute
                
                # Collect system metrics from various sources
                await self._collect_system_metrics()
                
                # Clean old metrics data
                self._cleanup_old_metrics()
                
                # Log periodic summary (reduced frequency)
                if not hasattr(self, '_summary_log_counter'):
                    self._summary_log_counter = 0
                self._summary_log_counter += 1
                
                # Only log every 5 minutes instead of every minute
                if self._summary_log_counter >= 5:
                    current_metrics = self.get_current_metrics()
                    LogFire.log("METRICS", f"System Summary - Created: {current_metrics['counters']['requests_created']}, "
                              f"Completed: {current_metrics['counters']['requests_completed']}, "
                              f"Failed: {current_metrics['counters']['requests_failed']}, "
                              f"Avg Time: {current_metrics['performance']['average_execution_time']:.2f}s")
                    self._summary_log_counter = 0
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                LogFire.log("ERROR", f"Error in metrics collection: {str(e)}")
    
    async def _collect_system_metrics(self):
        """Collect system-wide metrics"""
        try:
            # Get system resource usage
            import psutil
            
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=0.1)
            
            # Update health indicators based on system state
            memory_health = "healthy" if memory.percent < 80 else "critical" if memory.percent > 95 else "warning"
            
            self.update_health_indicators(
                database_health="healthy",  # This could be enhanced with actual DB checks
                memory_health=memory_health,
                thread_pool_health="healthy"  # This could be enhanced with thread pool monitoring
            )
            
            # Update system metrics
            self.update_system_metrics(
                concurrent_tasks=0,  # This would be populated by the factory
                memory_usage=memory.used / 1024 / 1024,  # MB
                system_load=cpu_percent
            )
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to collect system metrics: {str(e)}")
    
    def _cleanup_old_metrics(self):
        """Clean up old metrics data to prevent memory bloat"""
        cutoff_time = datetime.now(timezone.utc).timestamp() - (ScheduledRequestsConfig.METRICS_RETENTION_DAYS * 86400)
        
        with self._metrics_lock:
            for metric_name, metric_data in self._metrics_data.items():
                # Remove old entries
                while metric_data and metric_data[0]['timestamp'] < cutoff_time:
                    metric_data.popleft()
    
    def export_metrics_for_external_monitoring(self) -> Dict[str, Any]:
        """Export metrics in format suitable for external monitoring systems"""
        current_metrics = self.get_current_metrics()
        performance_summary = self.get_system_performance_summary()
        
        # Format for Prometheus/Grafana style metrics
        return {
            'scheduled_requests_created_total': current_metrics['counters']['requests_created'],
            'scheduled_requests_completed_total': current_metrics['counters']['requests_completed'],
            'scheduled_requests_failed_total': current_metrics['counters']['requests_failed'],
            'scheduled_requests_cancelled_total': current_metrics['counters']['requests_cancelled'],
            'scheduled_requests_security_violations_total': current_metrics['counters']['security_violations'],
            'scheduled_requests_quota_violations_total': current_metrics['counters']['quota_violations'],
            'scheduled_requests_rate_limit_violations_total': current_metrics['counters']['rate_limit_violations'],
            'scheduled_requests_average_execution_time_seconds': current_metrics['performance']['average_execution_time'],
            'scheduled_requests_peak_concurrent_tasks': current_metrics['performance']['peak_concurrent_tasks'],
            'scheduled_requests_peak_memory_usage_mb': current_metrics['performance']['peak_memory_usage'],
            'scheduled_requests_completion_rate': performance_summary['completion_rate'],
            'scheduled_requests_error_rate': performance_summary['error_rate'],
            'scheduled_requests_per_hour': performance_summary['requests_per_hour'],
            'system_load_percent': current_metrics['health_indicators']['system_load'],
            'timestamp': datetime.now(timezone.utc).timestamp()
        }
    
    async def get_system_health_status(self) -> Dict[str, Any]:
        """Get comprehensive system health status"""
        try:
            from ..integration_adapter import ScheduledRequestIntegrationAdapter
            
            # Check if integration adapter is running
            adapter = ScheduledRequestIntegrationAdapter.get_instance()
            
            if not adapter.is_initialized():
                return {
                    'system_healthy': False,
                    'overall_status': 'critical',
                    'error': 'Integration adapter not initialized',
                    'timestamp': datetime.now(timezone.utc).isoformat()
                }
            
            # Get system health data
            health_status = await adapter.get_system_health()
            current_metrics = self.get_current_metrics()
            
            # Determine overall health based on multiple factors
            health_factors = {
                'integration_adapter': health_status.get('overall_status', 'unknown'),
                'database_health': current_metrics['health_indicators']['database_health'],
                'memory_health': current_metrics['health_indicators']['memory_health'],
                'thread_pool_health': current_metrics['health_indicators']['thread_pool_health'],
                'error_rate': self.get_system_performance_summary()['error_rate']
            }
            
            # Calculate overall health status
            critical_issues = sum(1 for status in health_factors.values() if status == 'critical')
            warning_issues = sum(1 for status in health_factors.values() if status == 'warning')
            
            if critical_issues > 0:
                overall_status = 'critical'
                system_healthy = False
            elif warning_issues > 1:
                overall_status = 'degraded'
                system_healthy = False
            elif warning_issues == 1:
                overall_status = 'moderate'
                system_healthy = True
            else:
                overall_status = 'healthy'
                system_healthy = True
            
            return {
                'system_healthy': system_healthy,
                'overall_status': overall_status,
                'health_factors': health_factors,
                'health_details': health_status,
                'performance_metrics': current_metrics['performance'],
                'active_violations': {
                    'security': current_metrics['counters']['security_violations'],
                    'quota': current_metrics['counters']['quota_violations'],
                    'rate_limit': current_metrics['counters']['rate_limit_violations']
                },
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            return {
                'system_healthy': False,
                'overall_status': 'critical',
                'error': str(e),
                'timestamp': datetime.now(timezone.utc).isoformat()
            }