from imports import *
from asyncpg import connect, create_pool, CannotConnectNowError
import asyncio
import atexit
import signal
from socket import gaier<PERSON>r
from threading import RLock, local
import os
from contextvars import ContextVar
from weakref import WeakKeyDictionary

class PostgreSQLManager:
    _instance = None
    _initialized = False
    _instance_lock = RLock()  # Class-level lock for singleton creation
    user="userzairaask"
    password="wordzairap4ss"
    # Check for WSL environment and use Windows host IP
    host:str = ""
    port=5432

    def __new__(cls):
        # Double-checked locking pattern for thread-safe singleton
        if cls._instance is None:
            with cls._instance_lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
                    # Event loop isolated storage
                    cls._instance._event_loop_data = WeakKeyDictionary()  # loop -> {connections, pools, modes}
                    cls._instance._operation_locks = {}  # Per-database operation locks
                    cls._instance._lock = RLock()  # Thread-safe access to shared dictionaries
                    cls._instance._async_locks = WeakKeyDictionary()  # Event loop -> {dbname: Lock}
                    cls._instance._thread_local = local()  # Thread-local storage
                    cls._instance.host = os.environ.get('POSTGRES_HOST', "postgres" if Globals.is_docker() else "localhost")
                    cls._instance.port = int(os.environ.get('POSTGRES_PORT', 5432))
        return cls._instance

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        with cls._instance_lock:
            if cls._initialized:
                return
            cls._initialized = True
            cls._register_exit_hooks()

    @classmethod
    def get_instance(cls):
        if cls._instance is None:
            cls()
        return cls._instance
    
    @classmethod
    def _get_event_loop_data(cls):
        """Get event loop specific data storage"""
        instance = cls.get_instance()
        try:
            loop = asyncio.get_running_loop()
        except RuntimeError:
            # No event loop, use thread-local storage
            if not hasattr(instance._thread_local, 'data'):
                instance._thread_local.data = {
                    'connections': {},
                    'pools': {},
                    'connection_modes': {}
                }
            return instance._thread_local.data
        
        with instance._lock:
            if loop not in instance._event_loop_data:
                instance._event_loop_data[loop] = {
                    'connections': {},
                    'pools': {},
                    'connection_modes': {}
                }
            return instance._event_loop_data[loop]
    
    @classmethod
    def _safe_get_connection_mode(cls, dbname):
        """Thread-safe getter for connection mode"""
        data = cls._get_event_loop_data()
        return data['connection_modes'].get(dbname, False)
    
    @classmethod
    def _safe_set_connection_mode(cls, dbname, use_pool):
        """Thread-safe setter for connection mode"""
        data = cls._get_event_loop_data()
        data['connection_modes'][dbname] = use_pool
    
    @classmethod
    def _safe_get_connection(cls, dbname):
        """Thread-safe getter for connection/pool with state validation"""
        data = cls._get_event_loop_data()
        if data['connection_modes'].get(dbname, False):
            pool = data['pools'].get(dbname)
            # Validate pool state
            if pool and hasattr(pool, '_closed') and pool._closed:
                # Pool is closed, remove it from our records
                data['pools'].pop(dbname, None)
                data['connection_modes'].pop(dbname, None)
                return None
            return pool
        else:
            conn = data['connections'].get(dbname)
            # Validate connection state
            if conn and hasattr(conn, 'is_closed') and conn.is_closed():
                # Connection is closed, remove it from our records
                data['connections'].pop(dbname, None)
                data['connection_modes'].pop(dbname, None)
                return None
            return conn
    
    @classmethod
    def _safe_set_connection(cls, dbname, connection, use_pool):
        """Thread-safe setter for connection/pool"""
        data = cls._get_event_loop_data()
        data['connection_modes'][dbname] = use_pool
        if use_pool:
            data['pools'][dbname] = connection
        else:
            data['connections'][dbname] = connection
    
    @classmethod
    def _safe_remove_connection(cls, dbname):
        """Thread-safe removal of connection/pool"""
        data = cls._get_event_loop_data()
        use_pool = data['connection_modes'].get(dbname, False)
        if use_pool:
            connection = data['pools'].pop(dbname, None)
        else:
            connection = data['connections'].pop(dbname, None)
        data['connection_modes'].pop(dbname, None)
        
        # Clean up async locks for current event loop only
        instance = cls.get_instance()
        try:
            loop = asyncio.get_running_loop()
            if loop in instance._async_locks and dbname in instance._async_locks[loop]:
                instance._async_locks[loop].pop(dbname, None)
        except RuntimeError:
            pass
        
        # Clean up thread locks
        instance._operation_locks.pop(dbname, None)
        return connection, use_pool

    @classmethod
    def _register_exit_hooks(cls):
        """
        Registers cleanup logic for interpreter exit and termination signals.
        """
        # Make sure we don't register multiple times
        if getattr(cls, "_exit_hooks_registered", False):
            return

        cls._exit_hooks_registered = True

        def sync_cleanup():
            try:
                # During shutdown, try to use existing loop first
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # Event loop is still running, can't cleanup synchronously
                        return
                except RuntimeError:
                    # No event loop, create a new one
                    pass
                
                # Create a new event loop for cleanup
                try:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(cls.close_all_connections())
                    loop.close()
                except (RuntimeError, AttributeError, OSError):
                    # Event loop or proactor issues during shutdown - ignore
                    pass
            except Exception:
                # During shutdown, any error should be ignored
                pass

        atexit.register(sync_cleanup)

        for sig in (signal.SIGINT, signal.SIGTERM):
            signal.signal(sig, lambda s, f: (sync_cleanup(), exit(0)))

    @classmethod
    async def create_database(cls, dbname):
        """
        Creates a new database with the specified name, only if it doesn't already exist.
        """
        try:
            # Connect to the PostgreSQL server (not specifying a database yet)
            instance = cls.get_instance()
            conn = await connect(user=instance.user, password=instance.password, host=instance.host, port=instance.port, database="postgres")

            # Check if the database already exists
            query = f"SELECT 1 FROM pg_database WHERE datname = '{dbname}';"
            existing_db = await conn.fetch(query)

            if existing_db:
                print(f"Database '{dbname}' already exists.")
            else:
                # If the database doesn't exist, create it
                query = f"CREATE DATABASE {dbname};"
                await conn.execute(query)
                print(f"Database '{dbname}' created successfully.")

            # Close the connection
            await conn.close()

        except Exception as e:
            print(f"Error creating database '{dbname}': {e}")
            # Re-raise for proper handling upstream
            raise

    @classmethod
    async def delete_database(cls, dbname):
        """
        Deletes a specified database if it exists and there are no active connections.
        """
        try:
            # Connect to the PostgreSQL server (not specifying a database yet)
            instance = cls.get_instance()
            conn = await connect(user=instance.user, password=instance.password, host=instance.host, port=instance.port,database="postgres")

            # Check if the database exists
            query = f"SELECT 1 FROM pg_database WHERE datname = '{dbname}';"
            existing_db = await conn.fetch(query)

            if not existing_db:
                print(f"Database '{dbname}' does not exist.")
                await conn.close()
                return

            # Check for active connections to the database
            query = f"""
                SELECT pid FROM pg_stat_activity WHERE datname = '{dbname}' AND pid <> pg_backend_pid();
            """
            active_connections = await conn.fetch(query)

            if active_connections:
                print(f"Cannot delete database '{dbname}' because there are active connections.")
                await conn.close()
                return

            # If no active connections, proceed to drop the database
            query = f"DROP DATABASE {dbname};"
            await conn.execute(query)
            print(f"Database '{dbname}' deleted successfully.")

            # Close the connection
            await conn.close()

        except Exception as e:
            print(f"Error deleting database '{dbname}': {e}")

    @classmethod
    async def _get_or_create_async_lock(cls, dbname):
        """Get or create per-database async lock for current event loop"""
        instance = cls.get_instance()
        
        try:
            # Get current event loop
            loop = asyncio.get_running_loop()
        except RuntimeError:
            # No event loop running, fallback to thread lock
            print(f"No event loop running for {dbname}, using thread lock")
            return cls._get_thread_lock_context(dbname)
        
        with instance._lock:
            # Get or create locks dictionary for this event loop
            if loop not in instance._async_locks:
                instance._async_locks[loop] = {}
            
            loop_locks = instance._async_locks[loop]
            
            # Get or create lock for this database in this event loop
            if dbname not in loop_locks:
                try:
                    loop_locks[dbname] = asyncio.Lock()
                except RuntimeError as e:
                    # If lock creation fails, fallback to thread lock
                    print(f"Failed to create async lock for {dbname}: {e}")
                    return cls._get_thread_lock_context(dbname)
            
            return loop_locks[dbname]
    
    @classmethod
    def _get_thread_lock_context(cls, dbname):
        """Get a thread lock that can be used in async context"""
        class ThreadLockContext:
            def __init__(self, lock):
                self.lock = lock
                
            async def __aenter__(self):
                self.lock.acquire()
                return self
                
            async def __aexit__(self, exc_type, exc_val, exc_tb):
                self.lock.release()
        
        thread_lock = cls._get_or_create_thread_lock(dbname)
        return ThreadLockContext(thread_lock)
    
    @classmethod
    def _get_or_create_thread_lock(cls, dbname):
        """Get or create per-database thread lock as fallback"""
        instance = cls.get_instance()
        with instance._lock:
            if dbname not in instance._operation_locks:
                instance._operation_locks[dbname] = RLock()
            return instance._operation_locks[dbname]
    
    @classmethod
    async def _safe_async_operation(cls, dbname, operation):
        """Execute operation with simplified error handling"""
        try:
            return await operation()
        except Exception as e:
            error_msg = str(e)
            if ("bound to a different event loop" in error_msg or 
                "attached to a different loop" in error_msg):
                # Clear corrupted connection and retry
                cls._safe_remove_connection(dbname)
                return await operation()
            else:
                raise

    @classmethod
    async def connect_to_database(cls, dbname, use_pool=False, min_size=1, max_size=10, retries=5, delay=1):
        # Check if we're in a thread without an event loop
        try:
            asyncio.get_running_loop()
        except RuntimeError:
            # No event loop, we need to create one for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            try:
                return await cls._connect_with_loop(dbname, use_pool, min_size, max_size, retries, delay)
            finally:
                # Don't close the loop as other operations might need it
                pass
        
        # Use safe async operation to handle event loop binding
        instance = cls.get_instance()
        
        async def _connect():
            # Connection logic moved inside the safe operation
            # Check if connection already exists (inside the lock)
            existing_connection = cls._safe_get_connection(dbname)
            existing_mode = cls._safe_get_connection_mode(dbname)
            
            # If connection exists and mode matches, return it
            if existing_connection and existing_mode == use_pool:
                return existing_connection

            for attempt in range(retries):
                try:
                    if use_pool:
                        pool = await create_pool(
                            database=dbname,
                            user=instance.user,
                            password=instance.password,
                            host=instance.host,
                            port=instance.port,
                            min_size=min_size,
                            max_size=max_size
                        )
                        cls._safe_set_connection(dbname, pool, True)
                        return pool
                    else:
                        conn = await connect(
                            database=dbname,
                            user=instance.user,
                            password=instance.password,
                            host=instance.host,
                            port=instance.port
                        )
                        cls._safe_set_connection(dbname, conn, False)
                        return conn

                except (CannotConnectNowError, ConnectionRefusedError, gaierror, OSError) as e:
                    if attempt < retries - 1:
                        await asyncio.sleep(delay * (2 ** attempt))  # exponential backoff
                    else:
                        raise RuntimeError(f"Database '{dbname}' not ready after {retries} attempts: {e}")
        
        return await cls._safe_async_operation(dbname, _connect)
    
    @classmethod
    async def _connect_with_loop(cls, dbname, use_pool=False, min_size=1, max_size=10, retries=5, delay=1):
        """Connection logic for when we need to create an event loop in a thread"""
        instance = cls.get_instance()
        
        # Check if connection already exists
        existing_connection = cls._safe_get_connection(dbname)
        existing_mode = cls._safe_get_connection_mode(dbname)
        
        # If connection exists and mode matches, return it
        if existing_connection and existing_mode == use_pool:
            return existing_connection

        for attempt in range(retries):
            try:
                if use_pool:
                    pool = await create_pool(
                        database=dbname,
                        user=instance.user,
                        password=instance.password,
                        host=instance.host,
                        port=instance.port,
                        min_size=min_size,
                        max_size=max_size
                    )
                    cls._safe_set_connection(dbname, pool, True)
                    return pool
                else:
                    conn = await connect(
                        database=dbname,
                        user=instance.user,
                        password=instance.password,
                        host=instance.host,
                        port=instance.port
                    )
                    cls._safe_set_connection(dbname, conn, False)
                    return conn

            except (CannotConnectNowError, ConnectionRefusedError, gaierror, OSError) as e:
                if attempt < retries - 1:
                    await asyncio.sleep(delay * (2 ** attempt))  # exponential backoff
                else:
                    raise RuntimeError(f"Database '{dbname}' not ready after {retries} attempts: {e}")

    @classmethod
    def _using_pool(cls, dbname):
        return cls._safe_get_connection_mode(dbname)

    @classmethod
    async def get_connection(cls, dbname):
        """Get connection with automatic creation if needed"""
        connection = cls._safe_get_connection(dbname)
        if connection is None:
            # Connection doesn't exist, create it using the same mechanism as execute_query
            # This avoids deadlocks by using consistent locking
            try:
                # Try to create a connection - this will use proper locking
                connection = await cls.connect_to_database(dbname, use_pool=True, min_size=1, max_size=5)
                return connection
            except Exception as e:
                # If pool creation fails, try without pool
                try:
                    connection = await cls.connect_to_database(dbname, use_pool=False)
                    return connection
                except Exception as e2:
                    print(f"Failed to create connection for {dbname}: {e2}")
                    return None
        return connection
            
    @classmethod
    async def get_table_names(cls, dbname, schema:str="public"):
        """
        Retrieves the list of table names in the specified database.
        """
        try:
            # Get per-database lock to prevent concurrent operations
            db_lock = await cls._get_or_create_async_lock(dbname)
            
            async with db_lock:
                # Check if we are using a pool or a direct connection
                if cls._using_pool(dbname):
                    pool = await cls.get_connection(dbname)
                    if pool is None or (hasattr(pool, '_closed') and pool._closed):
                        pool = await cls.connect_to_database(dbname, use_pool=True, min_size=1, max_size=5)
                        if pool is None:
                            raise Exception(f"Cannot create pool for database '{dbname}'.")
                    async with pool.acquire() as conn:
                        query = "SELECT table_name FROM information_schema.tables WHERE table_schema = $1;"
                        tables = await conn.fetch(query, schema)
                else:
                    conn = await cls.get_connection(dbname)
                    if conn is None or (hasattr(conn, 'is_closed') and conn.is_closed()):
                        conn = await cls.connect_to_database(dbname, use_pool=False)
                        if conn is None:
                            raise Exception(f"Cannot create connection for database '{dbname}'.")
                        try:
                            query = "SELECT table_name FROM information_schema.tables WHERE table_schema = $1;"
                            tables = await conn.fetch(query, schema)
                        finally:
                            await cls.close_connection(dbname)
                    else:
                        query = "SELECT table_name FROM information_schema.tables WHERE table_schema = $1;"
                        tables = await conn.fetch(query, schema)

                # Extract table names from the results and return them
                table_names = [table['table_name'] for table in tables]
                return table_names
        except Exception as e:
            print(f"Error retrieving table names for database '{dbname}': {e}")
            return []

    @classmethod
    async def execute_query(cls, dbname, query, params=None):
        params = params or []
        
        # Simplified approach: try to get connection, if fails, create it
        try:
            if cls._using_pool(dbname):
                pool = cls._safe_get_connection(dbname)
                if pool is None or (hasattr(pool, '_closed') and pool._closed):
                    # Pool not found or closed, create a new one
                    pool = await cls.connect_to_database(dbname, use_pool=True, min_size=1, max_size=5)
                    if pool is None:
                        raise Exception(f"Cannot create pool for database '{dbname}'.")
                
                async with pool.acquire() as conn:
                    return await conn.fetch(query, *params)
            else:
                conn = cls._safe_get_connection(dbname)
                if conn is None or (hasattr(conn, 'is_closed') and conn.is_closed()):
                    conn = await cls.connect_to_database(dbname, use_pool=False)
                    if conn is None:
                        raise Exception(f"Cannot create connection for database '{dbname}'.")
                    try:
                        return await conn.fetch(query, *params)
                    finally:
                        await cls.close_connection(dbname)
                else:
                    return await conn.fetch(query, *params)
        except Exception as e:
            # Handle specific connection errors
            error_msg = str(e)
            if ("attached to a different loop" in error_msg or 
                "bound to a different event loop" in error_msg or
                "another operation is in progress" in error_msg or
                "connection was closed" in error_msg):
                
                print(f"Connection error for {dbname}, recreating: {error_msg}")
                # Clear the corrupted connection
                cls._safe_remove_connection(dbname)
                # Retry once with a new connection
                return await cls.execute_query(dbname, query, params)
            else:
                raise
    
    @classmethod
    async def _safe_pool_operation(cls, dbname, operation):
        """Safely execute operation on pool with proper error handling"""
        try:
            return await operation()
        except Exception as e:
            error_msg = str(e)
            if ("attached to a different loop" in error_msg or 
                "bound to a different event loop" in error_msg or
                "another operation is in progress" in error_msg or
                "connection was closed" in error_msg):
                
                print(f"Pool operation failed for {dbname}, recreating: {error_msg}")
                
                # Force remove the corrupted pool from current event loop
                cls._safe_remove_connection(dbname)
                
                # Create a new pool in the current event loop
                use_pool = True
                pool = await cls.connect_to_database(dbname, use_pool=use_pool, min_size=1, max_size=5)
                if pool is None:
                    raise Exception(f"Cannot recreate pool for database '{dbname}' after corruption.")
                
                # Retry the operation with the new pool
                return await operation()
            else:
                raise

    @classmethod
    async def execute_non_query(cls, dbname, query, params=None):
        params = params or []
        # Get per-database lock to prevent concurrent operations
        db_lock = await cls._get_or_create_async_lock(dbname)
        
        async with db_lock:
            if cls._using_pool(dbname):
                pool = await cls.get_connection(dbname)
                if pool is None or (hasattr(pool, '_closed') and pool._closed):
                    pool = await cls.connect_to_database(dbname, use_pool=True, min_size=1, max_size=5)
                    if pool is None:
                        raise Exception(f"Cannot create pool for database '{dbname}'.")
                async with pool.acquire() as conn:
                    await conn.execute(query, *params)
            else:
                conn = await cls.get_connection(dbname)
                if conn is None or (hasattr(conn, 'is_closed') and conn.is_closed()):
                    conn = await cls.connect_to_database(dbname, use_pool=False)
                    if conn is None:
                        raise Exception(f"Cannot create connection for database '{dbname}'.")
                    try:
                        await conn.execute(query, *params)
                    finally:
                        await cls.close_connection(dbname)
                else:
                    await conn.execute(query, *params)

    @classmethod
    async def start_transaction(cls, dbname):
        # Get per-database lock for transaction management
        db_lock = await cls._get_or_create_async_lock(dbname)
        
        async with db_lock:
            if cls._using_pool(dbname):
                pool = await cls.get_connection(dbname)
                if pool is None or (hasattr(pool, '_closed') and pool._closed):
                    pool = await cls.connect_to_database(dbname, use_pool=True, min_size=1, max_size=5)
                    if pool is None:
                        raise Exception(f"Cannot create pool for database '{dbname}'.")
                conn = await pool.acquire()
                tx = conn.transaction()
                await tx.start()
                return conn, tx
            else:
                conn = await cls.get_connection(dbname)
                if conn is None or (hasattr(conn, 'is_closed') and conn.is_closed()):
                    conn = await cls.connect_to_database(dbname, use_pool=False)
                    if conn is None:
                        raise Exception(f"Cannot create connection for database '{dbname}'.")
                tx = conn.transaction()
                await tx.start()
                return conn, tx

    @classmethod
    async def commit_transaction(cls, conn, tx, dbname=None):
        await tx.commit()
        if dbname and cls._using_pool(dbname):
            pool = await cls.get_connection(dbname)
            await pool.release(conn)

    @classmethod
    async def rollback_transaction(cls, conn, tx, dbname=None):
        await tx.rollback()
        if dbname and cls._using_pool(dbname):
            pool = await cls.get_connection(dbname)
            await pool.release(conn)

    @classmethod
    async def execute_many(cls, dbname, query, list_of_params):
        # Get per-database lock to prevent concurrent operations
        db_lock = await cls._get_or_create_async_lock(dbname)
        
        async with db_lock:
            if cls._using_pool(dbname):
                pool = await cls.get_connection(dbname)
                if pool is None or (hasattr(pool, '_closed') and pool._closed):
                    pool = await cls.connect_to_database(dbname, use_pool=True, min_size=1, max_size=5)
                    if pool is None:
                        raise Exception(f"Cannot create pool for database '{dbname}'.")
                async with pool.acquire() as conn:
                    await conn.executemany(query, list_of_params)
            else:
                conn = await cls.get_connection(dbname)
                if conn is None or (hasattr(conn, 'is_closed') and conn.is_closed()):
                    conn = await cls.connect_to_database(dbname, use_pool=False)
                    if conn is None:
                        raise Exception(f"Cannot create connection for database '{dbname}'.")
                    try:
                        await conn.executemany(query, list_of_params)
                    finally:
                        await cls.close_connection(dbname)
                else:
                    await conn.executemany(query, list_of_params)

    @classmethod
    async def reconnect(cls, dbname, min_size=1, max_size=10):
        """Reconnect to database with existing connection mode"""
        # Get per-database lock for reconnection
        db_lock = await cls._get_or_create_async_lock(dbname)
        
        async with db_lock:
            use_pool = cls._safe_get_connection_mode(dbname)
            # Close existing connection
            connection, _ = cls._safe_remove_connection(dbname)
            if connection:
                try:
                    if use_pool and hasattr(connection, '_closed') and not connection._closed:
                        await connection.close()
                    elif not use_pool and hasattr(connection, 'is_closed') and not connection.is_closed():
                        await connection.close()
                except:
                    pass
            # Create new connection
            return await cls.connect_to_database(dbname, use_pool=use_pool, min_size=min_size, max_size=max_size)

    @classmethod
    async def close_connection(cls, dbname):
        # Use per-database async lock for safe connection closure
        db_lock = await cls._get_or_create_async_lock(dbname)
        async with db_lock:
            connection, use_pool = cls._safe_remove_connection(dbname)
            if connection:
                try:
                    if use_pool:
                        if hasattr(connection, '_closed') and not connection._closed:
                            await connection.close()
                    else:
                        if hasattr(connection, 'is_closed') and not connection.is_closed():
                            await connection.close()
                except (AttributeError, RuntimeError, OSError):
                    # Connection already closed or event loop issues
                    pass

    @classmethod
    async def close_all_connections(cls):
        instance = cls.get_instance()
        
        # Get current event loop data
        data = cls._get_event_loop_data()
        
        # Get connections and pools for current event loop
        connections = list(data['connections'].items())
        pools = list(data['pools'].items())
        
        # Close individual connections
        for dbname, conn in connections:
            try:
                if hasattr(conn, 'is_closed') and not conn.is_closed():
                    await conn.close()
            except (AttributeError, RuntimeError, OSError):
                # Connection already closed or event loop shut down
                pass
        
        # Close connection pools
        for dbname, pool in pools:
            try:
                if hasattr(pool, '_closed') and not pool._closed:
                    await pool.close()
            except (AttributeError, RuntimeError, OSError):
                # Pool already closed or event loop shut down
                pass
        
        # Clear current event loop data
        data['connections'].clear()
        data['pools'].clear()
        data['connection_modes'].clear()
        
        # Clear async locks for current event loop
        try:
            loop = asyncio.get_running_loop()
            if loop in instance._async_locks:
                instance._async_locks[loop].clear()
        except RuntimeError:
            pass
        
        # Clear thread locks (global)
        instance._operation_locks.clear()

    @classmethod
    async def test_connection(cls, dbname):
        try:
            # Get per-database lock for testing
            db_lock = await cls._get_or_create_async_lock(dbname)
            
            async with db_lock:
                if cls._using_pool(dbname):
                    pool = await cls.get_connection(dbname)
                    if pool is None or (hasattr(pool, '_closed') and pool._closed):
                        pool = await cls.connect_to_database(dbname, use_pool=True)
                    async with pool.acquire() as conn:
                        await conn.fetch("SELECT 1")
                else:
                    conn = await cls.get_connection(dbname)
                    if conn is None or (hasattr(conn, 'is_closed') and conn.is_closed()):
                        conn = await cls.connect_to_database(dbname)
                        try:
                            await conn.fetch("SELECT 1")
                        finally:
                            await cls.close_connection(dbname)
                    else:
                        await conn.fetch("SELECT 1")
                return True
        except Exception as e:
            print(f"Connection test failed for {dbname}: {e}")
            return False

