from imports import *
from typing import Dict, List, Optional, Any
from uuid import UUID
from datetime import datetime, timezone, timedelta
import asyncio
import threading
import weakref

from .base_manager import BaseScheduledRequestManager
from ..utils.config import UserQuotaConfig, ScheduledRequestsConfig
from ..utils.exceptions import (
    UserQuotaExceededError, UnauthorizedScheduledRequestError, 
    ScheduledRequestNotFoundError, ResourceExhaustionError
)
from ..utils.helpers import validate_guid, sanitize_user_input

if TYPE_CHECKING:
    from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest

class UserScheduledRequestManager(BaseScheduledRequestManager):
    """User-specific scheduled request manager with resource isolation"""
    
    def __init__(self, user_guid: str, quota_config: UserQuotaConfig):
        super().__init__(user_guid, quota_config)
        
        # User-specific request tracking
        self._active_requests: Dict[str, 'ScheduledZairaRequest'] = {}
        self._paused_requests: Dict[str, Dict[str, Any]] = {}
        self._completed_requests: Dict[str, Dict[str, Any]] = {}
        
        # Thread tracking with weak references to prevent memory leaks
        self._active_threads: Dict[str, threading.Thread] = {}
        self._active_loops: weakref.WeakValueDictionary = weakref.WeakValueDictionary()
        
        # User-specific locks
        self._requests_lock = asyncio.Lock()
        self._threads_lock = threading.Lock()
        
        # Recurring task tracking
        self._recurring_tasks_count = 0
        
        LogFire.log("INIT", f"UserScheduledRequestManager created for user {user_guid}")
    
    async def _initialize(self):
        """Initialize user-specific manager components"""
        try:
            # Load any existing requests for this user from persistence
            await self._load_user_requests()
            
            # Start background cleanup task
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
            
            LogFire.log("INIT", f"User manager initialized with {len(self._active_requests)} active requests")
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to initialize user manager for {self.user_guid}: {str(e)}")
            raise
    
    async def _cleanup(self):
        """Cleanup user-specific resources"""
        try:
            # Cancel cleanup task
            if hasattr(self, '_cleanup_task'):
                self._cleanup_task.cancel()
                try:
                    await self._cleanup_task
                except asyncio.CancelledError:
                    pass
            
            # Cancel all active requests
            await self._cancel_all_requests("Manager shutdown")
            
            # Clean up threads
            await self._cleanup_all_threads()
            
            LogFire.log("INIT", f"User manager cleanup complete for {self.user_guid}")
            
        except Exception as e:
            LogFire.log("ERROR", f"Error during user manager cleanup for {self.user_guid}: {str(e)}")
    
    async def create_scheduled_request(self, scheduled_request: 'ScheduledZairaRequest') -> bool:
        """
        Create a new scheduled request for this user
        
        Args:
            scheduled_request: The ScheduledZairaRequest to create
            
        Returns:
            bool: True if created successfully
            
        Raises:
            UserQuotaExceededError: If user exceeds quota limits
            ResourceExhaustionError: If system resources are exhausted
        """
        # Validate user ownership
        if str(scheduled_request.user.user_guid) != self.user_guid:
            raise UnauthorizedScheduledRequestError(self.user_guid, str(scheduled_request.scheduled_guid))
        
        # Check quota limits
        if not await self.check_quota_limits("create_request"):
            await self._raise_appropriate_quota_error()
        
        # Check recurring task limits
        if scheduled_request.schedule_type.value == "recurring":
            if self._recurring_tasks_count >= self.quota_config.max_recurring_tasks:
                raise UserQuotaExceededError(
                    self.user_guid, "max_recurring_tasks", 
                    self._recurring_tasks_count, self.quota_config.max_recurring_tasks
                )
        
        async with self._requests_lock:
            scheduled_guid = str(scheduled_request.scheduled_guid)
            
            # Ensure not duplicate - this is expected during recovery/initialization
            if scheduled_guid in self._active_requests:
                existing_request = self._active_requests[scheduled_guid]
                LogFire.log("DEBUG", f"Scheduled request {scheduled_guid} already exists for user {self.user_guid}. " +
                           f"Existing: {existing_request.target_prompt[:50]}..., " +
                           f"New: {scheduled_request.target_prompt[:50]}...")
                return False
            
            try:
                # Track the request
                self._active_requests[scheduled_guid] = scheduled_request
                
                # Add to user's my_requests dictionary for supervisor access
                scheduled_request.user.add_request(scheduled_request)
                
                if scheduled_request.schedule_type.value == "recurring":
                    self._recurring_tasks_count += 1
                
                # Track task start
                await self.track_task_start()
                
                # Start the request in its own thread
                self._start_request_in_thread(scheduled_request, scheduled_guid)
                
                LogFire.log("REQUEST", f"Created scheduled request {scheduled_guid} for user {self.user_guid}")
                return True
                
            except Exception as e:
                # Cleanup on failure
                self._active_requests.pop(scheduled_guid, None)
                # Remove from user's my_requests dictionary if it was added
                try:
                    scheduled_request.user.remove_request(scheduled_request.scheduled_guid)
                except:
                    pass  # May not have been added yet
                if scheduled_request.schedule_type.value == "recurring":
                    self._recurring_tasks_count = max(0, self._recurring_tasks_count - 1)
                
                LogFire.log("ERROR", f"Failed to create scheduled request {scheduled_guid}: {str(e)}")
                raise ResourceExhaustionError("thread_creation", str(e))
    
    async def cancel_scheduled_request(self, scheduled_guid: str, reason: str = "User requested cancellation") -> bool:
        """
        Cancel a scheduled request
        
        Args:
            scheduled_guid: GUID of request to cancel
            reason: Reason for cancellation
            
        Returns:
            bool: True if cancelled successfully
        """
        if not validate_guid(scheduled_guid):
            return False
        
        async with self._requests_lock:
            if scheduled_guid not in self._active_requests:
                LogFire.log("WARNING", f"Attempted to cancel non-existent request {scheduled_guid} for user {self.user_guid}")
                return False
            
            try:
                request = self._active_requests[scheduled_guid]
                
                # Cancel the request
                request.cancel_schedule(reason)
                
                # Track recurring task count
                if request.schedule_type.value == "recurring":
                    self._recurring_tasks_count = max(0, self._recurring_tasks_count - 1)
                
                # Move to completed requests
                self._completed_requests[scheduled_guid] = {
                    'scheduled_guid': scheduled_guid,
                    'user_guid': self.user_guid,
                    'status': 'cancelled',
                    'reason': reason,
                    'cancelled_at': datetime.now(timezone.utc).isoformat(),
                    'schedule_info': request.get_schedule_info()
                }
                
                # Remove from active requests
                del self._active_requests[scheduled_guid]
                
                # Remove from user's my_requests dictionary
                request.user.remove_request(request.scheduled_guid)
                
                # Track cancellation
                await self.track_task_cancellation()
                
                LogFire.log("REQUEST", f"Cancelled scheduled request {scheduled_guid}: {reason}")
                return True
                
            except Exception as e:
                LogFire.log("ERROR", f"Failed to cancel request {scheduled_guid}: {str(e)}")
                return False
    
    async def get_user_request_guids(self) -> List[str]:
        """
        Get only the GUIDs of all scheduled requests for this user
        
        Returns:
            List of request GUIDs
        """
        async with self._requests_lock:
            guids = []
            
            # Add active request GUIDs
            active_guids = list(self._active_requests.keys())
            paused_guids = list(self._paused_requests.keys())
            completed_guids = list(self._completed_requests.keys())
            
            LogFire.log("DEBUG", f"User {self.user_guid} request collections: active={len(active_guids)} {active_guids}, paused={len(paused_guids)} {paused_guids}, completed={len(completed_guids)} {completed_guids}")
            
            guids.extend(active_guids)
            guids.extend(paused_guids)
            guids.extend(completed_guids)
            
            LogFire.log("DEBUG", f"User {self.user_guid} returning {len(guids)} total request GUIDs: {guids}")
            
            return guids
    
    async def get_request_object(self, scheduled_guid: str) -> Optional['ScheduledZairaRequest']:
        """
        Get the full ScheduledZairaRequest object
        
        Args:
            scheduled_guid: The GUID of the request
            
        Returns:
            ScheduledZairaRequest object or None if not found
        """
        async with self._requests_lock:
            if scheduled_guid in self._active_requests:
                return self._active_requests[scheduled_guid]
            return None
    
    # get_request_status method removed - access request object attributes directly
    
    async def get_user_requests(self, include_completed: bool = False) -> List[Dict[str, Any]]:
        """
        Get all scheduled requests for this user
        
        Args:
            include_completed: Whether to include completed/cancelled requests
            
        Returns:
            List of request information dictionaries
        """
        async with self._requests_lock:
            requests = []
            
            # Add active requests
            for scheduled_guid, request in self._active_requests.items():
                try:
                    
                    requests.append({
                        'scheduled_guid': scheduled_guid,
                        'status': 'active',
                        'schedule_info': request.get_schedule_info()
                        # task_status removed - should be fetched separately using GUID
                    })
                except Exception as e:
                    LogFire.log("WARNING", f"Failed to get info for request {scheduled_guid}: {str(e)}")
            
            # Add paused requests
            for scheduled_guid, request_data in self._paused_requests.items():
                requests.append({
                    'scheduled_guid': scheduled_guid,
                    'status': 'paused',
                    'schedule_info': request_data
                })
            
            # Add completed requests if requested
            if include_completed:
                for scheduled_guid, request_data in self._completed_requests.items():
                    requests.append(request_data)
            
            return requests
    
    # get_request_status method removed - access request object attributes directly
    
    def _start_request_in_thread(self, scheduled_request: 'ScheduledZairaRequest', scheduled_guid: str):
        """Start a scheduled request in its own thread"""
        def run_request_in_thread():
            """Run the scheduled request in a separate thread with its own event loop"""
            try:
                # Create new event loop for this thread
                loop = asyncio.new_event_loop()
                asyncio.set_event_loop(loop)
                
                # Store loop reference with weak reference
                self._active_loops[scheduled_guid] = loop
                
                try:
                    # Reduced verbosity: Only log thread starts on debug level
                    if ZairaSettings.IsDebugMode:
                        LogFire.log("REQUEST", f"Thread started for scheduled request {scheduled_guid}")
                    
                    # Run the request
                    start_time = datetime.now(timezone.utc)
                    loop.run_until_complete(scheduled_request.run_request())
                    end_time = datetime.now(timezone.utc)
                    
                    # Track completion
                    execution_time = (end_time - start_time).total_seconds()
                    try:
                        asyncio.run_coroutine_threadsafe(
                            self.track_task_completion(execution_time, True), 
                            loop
                        )
                    except Exception as e:
                        LogFire.log("ERROR", f"Failed to submit task completion tracking: {str(e)}")
                    
                    # Wait for any remaining background tasks
                    pending_tasks = asyncio.all_tasks(loop)
                    if pending_tasks:
                        try:
                            loop.run_until_complete(
                                asyncio.wait_for(
                                    asyncio.gather(*pending_tasks, return_exceptions=True),
                                    timeout=300  # 5 minutes max
                                )
                            )
                        except asyncio.TimeoutError:
                            LogFire.log("WARNING", f"Background tasks in {scheduled_guid} timed out")
                    
                    LogFire.log("REQUEST", f"Thread completed for scheduled request {scheduled_guid}")
                    
                except asyncio.CancelledError:
                    LogFire.log("REQUEST", f"Request {scheduled_guid} was cancelled")
                except Exception as e:
                    LogFire.log("ERROR", f"Thread execution failed for request {scheduled_guid}: {str(e)}")
                    # Track failure
                    try:
                        asyncio.run_coroutine_threadsafe(
                            self.track_task_completion(0, False), 
                            loop
                        )
                    except Exception as e:
                        LogFire.log("ERROR", f"Failed to submit task completion tracking: {str(e)}")
                finally:
                    # Cleanup tasks in the loop
                    pending_tasks = asyncio.all_tasks(loop)
                    for task in pending_tasks:
                        task.cancel()
                    
                    if pending_tasks:
                        loop.run_until_complete(asyncio.gather(*pending_tasks, return_exceptions=True))
                    
                    # Close the loop
                    loop.close()
                    
                    # Remove from tracking
                    with self._threads_lock:
                        self._active_threads.pop(scheduled_guid, None)
                    
                    if scheduled_guid in self._active_loops:
                        del self._active_loops[scheduled_guid]
                        
            except Exception as e:
                LogFire.log("ERROR", f"Failed to create thread for request {scheduled_guid}: {str(e)}")
                with self._threads_lock:
                    self._active_threads.pop(scheduled_guid, None)
        
        # Start the thread
        thread = threading.Thread(target=run_request_in_thread, daemon=False)
        thread.start()
        
        # Track the thread
        with self._threads_lock:
            self._active_threads[scheduled_guid] = thread
        
        LogFire.log("REQUEST", f"Started thread for scheduled request {scheduled_guid}")
    
    async def _load_user_requests(self):
        """Load existing requests for this user from persistence"""
        try:
            from .persistence import ScheduledRequestPersistenceManager
            persistence = ScheduledRequestPersistenceManager.get_instance()
            
            # Load only requests for this specific user
            active_requests = await persistence.get_active_requests(self.user_guid)
            
            for request_data in active_requests:
                try:
                    # Create fresh task from persisted data
                    from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest, ScheduleType
                    from managers.manager_users import ZairaUserManager
                    from endpoints.mybot_generic import MyBot_Generic
                    from uuid import UUID
                    
                    # Get user
                    user = await ZairaUserManager.find_user(request_data['user_guid'])
                    if not user:
                        LogFire.log("WARNING", f"User {request_data['user_guid']} not found for task recovery")
                        continue
                    
                    # Create bot instance
                    bot = MyBot_Generic(None, request_data.get('calling_bot_name', 'recovered_task'))
                    
                    # Create NEW task instance (this will go through normal creation flow)
                    new_request = ScheduledZairaRequest(
                        user=user,
                        calling_bot=bot,
                        original_message=None,
                        schedule_prompt=request_data['schedule_prompt'],
                        target_prompt=request_data['target_prompt'],
                        start_delay_seconds=request_data.get('start_delay_seconds', 0.0),
                        delay_seconds=request_data['delay_seconds'],
                        schedule_type=ScheduleType(request_data['schedule_type']),
                        run_on_startup=request_data.get('run_on_startup', False)
                    )
                    
                    # Override with persisted GUID and execution time to maintain continuity
                    object.__setattr__(new_request, 'scheduled_guid', UUID(request_data['scheduled_guid']))
                    object.__setattr__(new_request, 'next_execution', request_data['next_execution'])
                    
                    # This will be handled by the normal create_scheduled_request flow
                    success = await self.create_scheduled_request(new_request)
                    if success:
                        LogFire.log("REQUEST", f"Successfully recovered task {request_data['scheduled_guid']}")
                    else:
                        LogFire.log("WARNING", f"Failed to create recovered task {request_data['scheduled_guid']} - may already exist")
                        
                except Exception as e:
                    LogFire.log("ERROR", f"Failed to recover request {request_data.get('scheduled_guid', 'unknown')}: {str(e)}")
            
            LogFire.log("REQUEST", f"Loaded {len(self._active_requests)} requests for user {self.user_guid}")
            
        except Exception as e:
            LogFire.log("WARNING", f"Failed to load user requests for {self.user_guid}: {str(e)}")
    
    async def _periodic_cleanup(self):
        """Periodic cleanup of completed threads and expired data"""
        while not self.is_shutdown():
            try:
                await asyncio.sleep(ScheduledRequestsConfig.MANAGER_CLEANUP_INTERVAL_MINUTES * 60)
                
                # Clean up completed threads
                self._cleanup_completed_threads()
                
                # Clean up old completed requests
                await self._cleanup_old_completed_requests()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                LogFire.log("ERROR", f"Error in periodic cleanup for user {self.user_guid}: {str(e)}")
    
    def _cleanup_completed_threads(self):
        """Clean up threads that have completed"""
        with self._threads_lock:
            completed_threads = []
            for scheduled_guid, thread in list(self._active_threads.items()):
                if not thread.is_alive():
                    completed_threads.append(scheduled_guid)
            
            for scheduled_guid in completed_threads:
                del self._active_threads[scheduled_guid]
                LogFire.log("REQUEST", f"Cleaned up completed thread for {scheduled_guid}")
    
    async def _cleanup_old_completed_requests(self):
        """Clean up old completed requests"""
        cutoff_time = datetime.now(timezone.utc) - timedelta(hours=24)
        cutoff_timestamp = cutoff_time.isoformat()
        
        async with self._requests_lock:
            to_remove = []
            for scheduled_guid, request_data in self._completed_requests.items():
                completed_at = request_data.get('cancelled_at') or request_data.get('completed_at')
                if completed_at and completed_at < cutoff_timestamp:
                    to_remove.append(scheduled_guid)
            
            for scheduled_guid in to_remove:
                del self._completed_requests[scheduled_guid]
            
            if to_remove:
                LogFire.log("REQUEST", f"Cleaned up {len(to_remove)} old completed requests for user {self.user_guid}")
    
    async def _cancel_all_requests(self, reason: str):
        """Cancel all active requests"""
        async with self._requests_lock:
            for scheduled_guid, request in list(self._active_requests.items()):
                try:
                    request.cancel_schedule(reason)
                except Exception as e:
                    LogFire.log("ERROR", f"Error cancelling request {scheduled_guid}: {str(e)}")
    
    async def _cleanup_all_threads(self):
        """Clean up all active threads"""
        # Cancel all tasks in tracked event loops
        for scheduled_guid, loop in list(self._active_loops.items()):
            try:
                if loop and not loop.is_closed():
                    asyncio.run_coroutine_threadsafe(
                        self._cancel_all_tasks_in_loop(loop), loop
                    )
            except Exception as e:
                LogFire.log("ERROR", f"Error cancelling tasks in loop for {scheduled_guid}: {str(e)}")
        
        # Wait for threads to complete
        with self._threads_lock:
            active_threads = list(self._active_threads.values())
        
        for thread in active_threads:
            try:
                thread.join(timeout=10)  # Wait max 10 seconds per thread
            except Exception as e:
                LogFire.log("ERROR", f"Error joining thread: {str(e)}")
        
        # Clear tracking
        with self._threads_lock:
            self._active_threads.clear()
    
    async def _cancel_all_tasks_in_loop(self, loop):
        """Cancel all tasks in a specific event loop"""
        tasks = asyncio.all_tasks(loop)
        for task in tasks:
            task.cancel()
        
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)
    
    async def _raise_appropriate_quota_error(self):
        """Raise appropriate quota error based on what limit was exceeded"""
        current_tasks = self._active_tasks_counter.get_value()
        daily_tasks = self._daily_tasks_counter.get_value()
        memory_usage = self._memory_tracker.get_user_memory(self.user_guid)
        
        if current_tasks >= self.quota_config.max_concurrent_tasks:
            raise UserQuotaExceededError(
                self.user_guid, "max_concurrent_tasks", 
                current_tasks, self.quota_config.max_concurrent_tasks
            )
        elif daily_tasks >= self.quota_config.daily_task_limit:
            raise UserQuotaExceededError(
                self.user_guid, "daily_task_limit", 
                daily_tasks, self.quota_config.daily_task_limit
            )
        elif memory_usage >= self.quota_config.memory_limit_mb:
            raise UserQuotaExceededError(
                self.user_guid, "memory_limit_mb", 
                int(memory_usage), self.quota_config.memory_limit_mb
            )
        else:
            raise ResourceExhaustionError("quota_check", "Unknown quota limit exceeded")
    
    def get_active_request_count(self) -> int:
        """Get count of active requests"""
        return len(self._active_requests)
    
    def get_recurring_task_count(self) -> int:
        """Get count of recurring tasks"""
        return self._recurring_tasks_count