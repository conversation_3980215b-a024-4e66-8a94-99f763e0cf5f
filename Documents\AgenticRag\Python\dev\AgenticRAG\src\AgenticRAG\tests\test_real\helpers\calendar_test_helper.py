#!/usr/bin/env python3
"""
Calendar Test Helper for Real System Integration Tests

This module provides calendar-specific functionality for test_real files,
including calendar/agenda setup, calendar supervisors, and calendar-specific utilities.

Usage:
    from tests.test_real.helpers.calendar_test_helper import CalendarTestHelper
    
    class TestCalendarFeature(BaseRealTest):
        async def test_schedule_meeting(self):
            setup_success = await self.setup_real_system()
            assert setup_success
            
            # Use calendar helper for category-specific setup
            calendar_helper = CalendarTestHelper(self)
            await calendar_helper.setup_calendar_components()
            
            user = await self.create_test_user()
            bot = self.create_test_bot()
            
            result = await self.execute_and_monitor_request(
                user=user,
                query="schedule meeting with team for tomorrow at 2pm",
                bot=bot,
                timeout=60
            )
            
            self.assert_request_success(result, expected_outputs=["calendar"])
"""

from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../../'))

from imports import *
from managers.manager_logfire import LogFire
import os
import pytest
from typing import List, Tuple, Dict, Any


class CalendarTestHelper:
    """
    Helper class for calendar-specific test functionality.
    
    This class encapsulates all calendar-related setup, configuration,
    and utility methods that were previously in BaseRealTest.
    """
    
    def __init__(self, base_test_instance):
        """
        Initialize the calendar test helper.
        
        Args:
            base_test_instance: Instance of BaseRealTest that this helper supports
        """
        self.base_test = base_test_instance
        self.test_name = getattr(base_test_instance, 'test_name', 'CalendarTestHelper')
    
    async def setup_calendar_components(self):
        """
        Set up calendar-specific components including agenda planners.
        
        This method creates the agenda planner supervisor that is
        required for calendar testing functionality.
        
        Returns:
            bool: True if setup successful, False otherwise
        """
        try:
            from tasks.processing.task_agenda_planner import create_supervisor_agenda_planner
            
            await create_supervisor_agenda_planner()
            LogFire.log("DEBUG", f"[{self.test_name}] Calendar supervisors created successfully")
            return True
        except Exception as e:
            LogFire.log("DEBUG", f"[{self.test_name}] Warning: Could not create calendar supervisors: {e}")
            return False
    
    def get_calendar_test_documents(self) -> List[Tuple[str, str]]:
        """
        Get calendar-specific test documents for document stores.
        
        Returns:
            List[Tuple[str, str]]: List of (filename, content) tuples for calendar testing
        """
        return [
            ("meeting_templates.txt", "Standard meeting templates and formats"),
            ("calendar_policies.txt", "Calendar policies and scheduling guidelines"),
            ("availability_info.txt", "Availability and scheduling information")
        ]
    
    def get_calendar_pipeline_steps(self) -> List[str]:
        """
        Get expected pipeline steps for calendar operations.
        
        Returns:
            List[str]: List of expected pipeline step names
        """
        return [
            "agenda_planner",
            "calendar_integration",
            "scheduling"
        ]
    
    async def create_calendar_test_user(self, 
                                      username: str = None,
                                      email: str = "<EMAIL>"):
        """
        Create a test user specifically configured for calendar testing.
        
        Args:
            username: Username for the test user (default: auto-generated)
            email: Email address for the user (default: "<EMAIL>")
            
        Returns:
            ZairaUser: The created test user configured for calendar testing
        """
        if not username:
            username = f"calendar_user_{len(self.base_test.test_users) + 1}"
        
        return await self.base_test.create_test_user(
            username=username,
            email=email
        )
    
    def assert_calendar_pipeline_verification(self, result: Dict[str, Any]):
        """
        Assert that calendar-specific pipeline steps were executed.
        
        Args:
            result: Result dictionary from execute_and_monitor_request()
            
        Returns:
            List[str]: Verified calendar pipeline steps that were found
        """
        expected_steps = self.get_calendar_pipeline_steps()
        return self.base_test.assert_pipeline_verification(result, expected_steps)
    
    def get_calendar_test_examples(self) -> Dict[str, str]:
        """
        Get common calendar test query examples.
        
        Returns:
            Dict[str, str]: Dictionary mapping test scenario names to query strings
        """
        return {
            "simple_meeting": "schedule meeting with team for tomorrow at 2pm",
            "recurring_meeting": "schedule weekly team meeting every Monday at 10am",
            "check_availability": "check my availability for next week",
            "cancel_meeting": "cancel the meeting scheduled for tomorrow",
            "reschedule_meeting": "reschedule tomorrow's meeting to next Friday"
        }
    
    def get_calendar_oauth_structure(self) -> Dict[str, str]:
        """
        Get the Google Calendar OAuth token structure for reference.
        
        Returns:
            Dict[str, str]: Calendar OAuth token structure mapping
        """
        return {
            "access_token": "google_calendar_access_token",
            "refresh_token": "google_calendar_refresh_token", 
            "token_type": "Bearer",
            "expires_in": "3600"
        }


def skip_if_no_calendar():
    """Skip test if calendar integration is not configured"""
    # Check if calendar credentials are available
    calendar_configured = os.environ.get('GOOGLE_CALENDAR_CREDENTIALS')
    
    return pytest.mark.skipif(
        not calendar_configured,
        reason="Calendar not configured - set GOOGLE_CALENDAR_CREDENTIALS"
    )


def skip_if_no_google_api():
    """Skip test if Google API credentials are not configured"""
    # Check if Google API credentials are available
    google_api_configured = os.environ.get('GOOGLE_API_CREDENTIALS')
    
    return pytest.mark.skipif(
        not google_api_configured,
        reason="Google API not configured - set GOOGLE_API_CREDENTIALS"
    )


# Export public API
__all__ = [
    'CalendarTestHelper',
    'skip_if_no_calendar',
    'skip_if_no_google_api'
]