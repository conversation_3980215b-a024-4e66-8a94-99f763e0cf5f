"""
Integration test for split agenda functionality
This test validates that the agenda planner and agenda output work together correctly after the split
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch


@pytest.mark.integration
@pytest.mark.asyncio
class TestAgendaSplitIntegration:
    """Integration tests for split agenda functionality"""
    
    async def test_agenda_planner_and_output_coordination(self):
        """Test that agenda planner (generation) and agenda output (API) work together"""
        # Test the planning side
        from tasks.processing.task_agenda_planner import AgendaPlannerTool, AgendaGeneratorTool
        
        planner_tool = AgendaPlannerTool()
        generator_tool = AgendaGeneratorTool()
        
        # Create mock state for cross-tool coordination
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.sections = {}
        
        # Mock the output task import in planning task
        with patch('tasks.outputs.output_requests.task_out_agenda.get_calendar_tools', return_value=[]):
            # Mock LLM generation
            with patch('langchain_openai.ChatOpenAI'):
                with patch('langchain.prompts.ChatPromptTemplate') as mock_template:
                    mock_chain = MagicMock()
                    mock_chain.ainvoke = AsyncMock(return_value=MagicMock(content="Generated agenda content"))
                    mock_prompt = MagicMock()
                    mock_prompt.__or__ = MagicMock(return_value=mock_chain)
                    mock_template.from_template.return_value = mock_prompt
                    
                    # Test planning workflow
                    planner_result = await planner_tool._arun(
                        request="Plan team meeting",
                        start_date="2024-01-15",
                        end_date="2024-01-15",
                        state=mock_state
                    )
                    
                    # Verify planning completed and stored data in state
                    assert mock_state.sections['generated_agenda'] == "Generated agenda content"
                    
                    # Parse planner result
                    import json
                    planner_data = json.loads(planner_result)
                    assert planner_data['agenda_content'] == "Generated agenda content"
                    assert planner_data['request'] == "Plan team meeting"
        
        # Test the output side using data from planning
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderTool
        
        sender_tool = AgendaSenderTool()
        
        # Mock successful calendar event creation
        mock_calendar_result = {
            "success": True,
            "event_id": "test_event_123",
            "message": "Event created successfully"
        }
        
        with patch.object(sender_tool, '_create_calendar_event', return_value=mock_calendar_result):
            # Mock user manager
            with patch('tasks.outputs.output_requests.task_out_agenda.ZairaUserManager') as mock_zum:
                mock_user = MagicMock()
                mock_zum.get_instance.return_value.find_user = AsyncMock(return_value=mock_user)
                
                # Use generated content from planning stage
                event_details = {
                    "summary": "Team Meeting", 
                    "start": "2024-01-15T14:00:00",
                    "end": "2024-01-15T15:00:00",
                    "description": mock_state.sections['generated_agenda']  # Use planned content
                }
                
                # Set approval in state
                mock_state.sections['agenda_approved'] = True
                
                # Test output workflow
                sender_result = await sender_tool._arun(event_details, state=mock_state)
                
                # Verify output completed and updated state
                assert "Calendar event created successfully" in sender_result
                assert "test_event_123" in sender_result
                assert mock_state.sections['agenda_sent'] == True
                assert mock_state.sections['agenda_event_id'] == "test_event_123"
        
        # Verify complete workflow coordination
        assert mock_state.sections['generated_agenda'] == "Generated agenda content"  # From planning
        assert mock_state.sections['agenda_sent'] == True  # From output
        assert mock_state.sections['agenda_event_id'] == "test_event_123"  # From output
    
    async def test_planning_and_output_imports_separation(self):
        """Test that planning and output tasks have separate imports"""
        # Verify planning task imports
        try:
            from tasks.processing.task_agenda_planner import AgendaPlannerTool, AgendaGeneratorTool
            planning_imports_work = True
        except ImportError:
            planning_imports_work = False
        
        # Verify output task imports
        try:
            from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderTool, get_calendar_tools, format_event_preview
            output_imports_work = True
        except ImportError:
            output_imports_work = False
        
        assert planning_imports_work, "Planning task imports should work"
        assert output_imports_work, "Output task imports should work"
        
        # Verify cross-imports work (planning can import from output)
        from tasks.processing.task_agenda_planner import AgendaPlannerTool
        
        planner = AgendaPlannerTool()
        
        # Mock the cross-import and verify it's called
        with patch('tasks.outputs.output_requests.task_out_agenda.get_calendar_tools') as mock_get_tools:
            mock_get_tools.return_value = []
            
            mock_state = MagicMock()
            mock_state.sections = {}
            
            with patch.object(planner, '_generate_agenda_content', return_value="test content"):
                await planner._arun("test request", state=mock_state)
                
                # Verify cross-import was called
                mock_get_tools.assert_called_once()
    
    async def test_output_task_independence(self):
        """Test that output task can work independently of planning task"""
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderDirectTool, format_event_preview
        
        # Test format_event_preview works independently
        event_data = {
            'summary': 'Independent Test',
            'start': {'dateTime': '2024-01-15T14:00:00'},
            'end': {'dateTime': '2024-01-15T15:00:00'},
            'location': 'Test Room'
        }
        
        preview = format_event_preview(event_data)
        assert 'Independent Test' in preview
        assert 'Test Room' in preview
        
        # Test direct sender tool works independently
        direct_tool = AgendaSenderDirectTool()
        
        mock_result = {
            "success": True,
            "event_id": "direct_event_456",
            "message": "Direct event created"
        }
        
        with patch('tasks.outputs.output_requests.task_out_agenda.AgendaSenderTool') as mock_sender_class:
            mock_sender_instance = MagicMock()
            mock_sender_instance._create_calendar_event = AsyncMock(return_value=mock_result)
            mock_sender_class.return_value = mock_sender_instance
            
            mock_state = MagicMock()
            mock_state.sections = {}
            
            result = await direct_tool._arun(event_data, state=mock_state)
            
            assert "Calendar event created directly" in result
            assert "direct_event_456" in result
            assert mock_state.sections['agenda_sent'] == True
    
    async def test_supervisor_creation_with_split_functionality(self):
        """Test that supervisor creation works with split functionality"""
        from tasks.processing.task_agenda_planner import create_supervisor_agenda_planner
        
        # Mock all dependencies
        with patch('tasks.outputs.output_requests.task_out_agenda.get_calendar_tools') as mock_get_tools:
            mock_calendar_tools = [MagicMock(name="calendar_tool_1"), MagicMock(name="calendar_tool_2")]
            mock_get_tools.return_value = mock_calendar_tools
            
            with patch('tasks.processing.task_agenda_planner.SupervisorManager') as mock_sm:
                # Mock supervisor manager components
                mock_task = MagicMock()
                mock_task.name = "agenda_expert"
                
                mock_supervisor = MagicMock()
                mock_supervisor.add_task.return_value = mock_supervisor
                mock_supervisor.compile.return_value = mock_supervisor
                
                mock_sm.register_task.return_value = mock_task
                mock_sm.register_supervisor.return_value = mock_supervisor
                
                # Create supervisor
                result = await create_supervisor_agenda_planner()
                
                # Verify supervisor creation workflow
                assert result == mock_supervisor
                
                # Verify calendar tools from output task were obtained
                mock_get_tools.assert_called_once()
                
                # Verify task was registered with both planning and calendar tools
                task_call_args = mock_sm.register_task.call_args[0][0]
                assert task_call_args.name == "agenda_expert"
                # Should include planning tools + calendar tools
                assert len(task_call_args.tools) >= 2  # At least planning tools + calendar tools
                
                # Verify supervisor was configured
                mock_sm.register_supervisor.assert_called_once()
                mock_supervisor.add_task.assert_called_once_with(mock_task, priority=1)
                mock_supervisor.compile.assert_called_once()
    
    async def test_workflow_state_management_across_split(self):
        """Test that state is properly managed across the planning/output split"""
        from tasks.processing.task_agenda_planner import AgendaPlannerTool
        from tasks.outputs.output_tasks.task_out_agenda import AgendaSenderTool
        
        # Initialize shared state
        shared_state = MagicMock()
        shared_state.user_guid = "workflow-test-user"
        shared_state.sections = {}
        
        # Step 1: Planning phase
        planner = AgendaPlannerTool()
        
        with patch('tasks.outputs.output_requests.task_out_agenda.get_calendar_tools', return_value=[]):
            with patch.object(planner, '_generate_agenda_content', return_value="Planned meeting agenda"):
                planning_result = await planner._arun(
                    request="Weekly team sync",
                    start_date="2024-01-20",
                    end_date="2024-01-20",
                    state=shared_state
                )
                
                # Verify planning updated state
                assert shared_state.sections['generated_agenda'] == "Planned meeting agenda"
                assert shared_state.sections['agenda_start_date'] == "2024-01-20"
        
        # Step 2: Output phase using planned data
        sender = AgendaSenderTool()
        
        # Create event using planned data
        event_from_planning = {
            "summary": "Weekly team sync",
            "description": shared_state.sections['generated_agenda'],
            "start": f"{shared_state.sections['agenda_start_date']}T10:00:00",
            "end": f"{shared_state.sections['agenda_start_date']}T11:00:00"
        }
        
        # Mock successful output
        mock_output_result = {
            "success": True,
            "event_id": "workflow_event_789",
            "message": "Workflow event created"
        }
        
        with patch.object(sender, '_create_calendar_event', return_value=mock_output_result):
            with patch('tasks.outputs.output_requests.task_out_agenda.ZairaUserManager') as mock_zum:
                mock_user = MagicMock()
                mock_zum.get_instance.return_value.find_user = AsyncMock(return_value=mock_user)
                
                # Set approval
                shared_state.sections['agenda_approved'] = True
                
                output_result = await sender._arun(event_from_planning, state=shared_state)
                
                # Verify output updated state
                assert shared_state.sections['agenda_sent'] == True
                assert shared_state.sections['agenda_event_id'] == "workflow_event_789"
        
        # Step 3: Verify complete workflow state
        final_state_keys = set(shared_state.sections.keys())
        expected_keys = {
            'generated_agenda',      # From planning
            'agenda_start_date',     # From planning
            'agenda_approved',       # Set during workflow
            'agenda_sent',          # From output
            'agenda_event_id'       # From output
        }
        
        assert expected_keys.issubset(final_state_keys), f"Missing state keys. Expected: {expected_keys}, Got: {final_state_keys}"
        
        # Verify data flow integrity
        assert "Planned meeting agenda" in shared_state.sections['generated_agenda']
        assert shared_state.sections['agenda_sent'] == True
        assert "workflow_event_789" in shared_state.sections['agenda_event_id']