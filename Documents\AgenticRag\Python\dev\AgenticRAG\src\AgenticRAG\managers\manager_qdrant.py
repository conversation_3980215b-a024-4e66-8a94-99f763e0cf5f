from imports import *

from qdrant_client import Qdrant<PERSON>lient, AsyncQdrantClient, models

from managers.manager_retrieval import RetrievalManager

class QDrantManager:
    _instance = None
    _initialized = False

    _client: QdrantClient = None
    _aclient: AsyncQdrantClient = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return

        instance.CreateQDrantClient()
        instance.CreateQDrantAsyncClient()
        instance._initialized = True

    @classmethod
    def GetClient(cls) -> QdrantClient:
        instance = cls.get_instance()
        if instance._client is None:
            instance.CreateQDrantClient()
        return instance._client

    @classmethod
    def GetAsyncClient(cls) -> AsyncQdrantClient:
        instance = cls.get_instance()
        if instance._aclient is None:
            instance.CreateQDrantAsyncClient()
        return instance._aclient

    def CreateQDrantClient(self) -> QdrantClient:
        host = "qdrant" if Globals.is_docker() else "localhost"
        self._client = QdrantClient(
            # you can use :memory: mode for fast and light-weight experiments,
            # it does not require to have Qdrant deployed anywhere
            # but requires qdrant-client >= 1.1.1
            # location=":memory:"
            # otherwise set Qdrant instance address with:
            # url="http://<host>:<port>"
            # otherwise set Qdrant instance with host and port:
            url=f"http://{host}:6333",
            #host="localhost",
            #port=6333
            # set API KEY for Qdrant Cloud
            # api_key="<qdrant-api-key>",
            timeout=300,
        )
        return self._client

    def CreateQDrantAsyncClient(self) -> AsyncQdrantClient:
        host = "qdrant" if Globals.is_docker() else "localhost"
        self._aclient = AsyncQdrantClient(
            # you can use :memory: mode for fast and light-weight experiments,
            # it does not require to have Qdrant deployed anywhere
            # but requires qdrant-client >= 1.1.1
            # location=":memory:"
            # otherwise set Qdrant instance address with:
            # url="http://<host>:<port>"
            # otherwise set Qdrant instance with host and port:
            url=f"http://{host}:6333",
            #host="localhost",
            #port=6333
            # set API KEY for Qdrant Cloud
            # api_key="<qdrant-api-key>",
            timeout=300,
        )
        return self._aclient

    @classmethod
    async def upsert(cls, id: str, data_as_str: str, metadata: dict = None, insert_node = True):
        """
        Upsert a document into Qdrant with rich metadata support.

        Args:
            id: Unique identifier for the document
            text_dense: Dense vector embedding
            text_sparse: Sparse vector embedding (BM25)
            data_as_str: The text content of the document
            metadata: Dictionary containing document metadata (file_path, file_name, file_type, etc.)
            collection_name: Name of the Qdrant collection

        The metadata typically includes:
        - file_path: Full path to the source file
        - file_name: Just the filename
        - file_type: MIME type of the file (e.g., 'text/plain', 'application/pdf')
        - file_size: Size in bytes
        - creation_date: When the file was created
        - last_modified_date: When the file was last modified
        
        Multimodal metadata (optional):
        - has_images: Boolean indicating if document contains images
        - has_tables: Boolean indicating if document contains tables
        - image_count: Number of images in the document
        - table_count: Number of tables in the document
        - image_summaries: List of image descriptions
        - table_summaries: List of table summaries
        - asset_paths: List of paths to saved assets
        - multimodal_content: Structured multimodal content data
        """
        text_dense = await RetrievalManager.get_embeddings_dense(data_as_str)
        text_sparse = await RetrievalManager.get_embeddings_sparse(data_as_str)
        sparse_vector = models.SparseVector(
            indices=text_sparse.indices,
            values=text_sparse.values
        )
        from llama_index.core.schema import TextNode
        node = TextNode(
            id_=id,
            text=data_as_str,
            metadata=metadata or {},
            embedding=text_dense,
            embedding_sparse=sparse_vector,
            text_template="{content}",      
                   
        )
        if insert_node == True:
            Globals.get_index().insert_nodes([node])
        else:
            return node

    @classmethod
    async def upsert_multiple(cls, ids: list[str], data_as_strs: list[str], metadatas: list[dict]):
        nodes = []
        counter = 0
        for id in ids:
            node = await cls.upsert(id, data_as_strs[counter], metadata=metadatas[counter], insert_node=False)
            nodes.append(node)
            counter += 1
        Globals.get_index().insert_nodes(nodes)
    
    @classmethod
    async def upsert_multimodal(cls, doc_id: str, text_chunks: list[str], multimodal_data: dict, base_metadata: dict = None):
        """
        Upsert a document with multimodal content into Qdrant.
        
        Args:
            doc_id: Base document ID
            text_chunks: List of text chunks from the document
            multimodal_data: Multimodal content data from MultimodalManager
            base_metadata: Base metadata for the document
        """
        nodes = []
        base_metadata = base_metadata or {}
        
        # Add multimodal metadata to base metadata
        multimodal_metadata = {
            "has_images": len(multimodal_data.get("images", [])) > 0,
            "has_tables": len(multimodal_data.get("tables", [])) > 0,
            "image_count": len(multimodal_data.get("images", [])),
            "table_count": len(multimodal_data.get("tables", [])),
            "figure_count": len(multimodal_data.get("figures", [])),
            "caption_count": len(multimodal_data.get("captions", [])),
            "multimodal_doc_id": doc_id,
        }
        
        # Extract summaries for metadata
        image_summaries = [img.get("summary", "") for img in multimodal_data.get("images", [])]
        table_summaries = [tbl.get("summary", "") for tbl in multimodal_data.get("tables", [])]
        asset_paths = [img.get("asset_path", "") for img in multimodal_data.get("images", []) if img.get("asset_path")]
        
        # Add summaries to metadata
        if image_summaries:
            multimodal_metadata["image_summaries"] = image_summaries
        if table_summaries:
            multimodal_metadata["table_summaries"] = table_summaries
        if asset_paths:
            multimodal_metadata["asset_paths"] = asset_paths
        
        # Combine base metadata with multimodal metadata
        combined_metadata = {**base_metadata, **multimodal_metadata}
        
        # Create nodes for text chunks
        for i, chunk in enumerate(text_chunks):
            # Generate a new UUID for each chunk to ensure valid Qdrant point ID
            from uuid import uuid5, NAMESPACE_DNS
            chunk_id = str(uuid5(NAMESPACE_DNS, f"{doc_id}_chunk_{i}"))
            chunk_metadata = combined_metadata.copy()
            chunk_metadata["chunk_index"] = i
            chunk_metadata["chunk_id"] = chunk_id
            
            # Enhanced text content including multimodal summaries
            enhanced_text = await cls._enhance_text_with_multimodal_context(chunk, multimodal_data, i)
            
            node = await cls.upsert(chunk_id, enhanced_text, metadata=chunk_metadata, insert_node=False)
            nodes.append(node)
        
        # Create separate nodes for image summaries (for better retrieval)
        for i, image_data in enumerate(multimodal_data.get("images", [])):
            if image_data.get("summary"):
                # Generate a valid UUID for Qdrant
                from uuid import uuid5, NAMESPACE_DNS
                image_id = str(uuid5(NAMESPACE_DNS, f"{doc_id}_image_{i}"))
                image_metadata = combined_metadata.copy()
                image_metadata.update({
                    "content_type": "image_summary",
                    "image_index": i,
                    "element_id": image_data.get("id"),
                    "asset_path": image_data.get("asset_path", ""),
                    "has_asset": image_data.get("has_asset", False)
                })
                
                node = await cls.upsert(image_id, image_data["summary"], metadata=image_metadata, insert_node=False)
                nodes.append(node)
        
        # Create separate nodes for table summaries (for better retrieval)
        for i, table_data in enumerate(multimodal_data.get("tables", [])):
            if table_data.get("summary"):
                # Generate a valid UUID for Qdrant
                from uuid import uuid5, NAMESPACE_DNS
                table_id = str(uuid5(NAMESPACE_DNS, f"{doc_id}_table_{i}"))
                table_metadata = combined_metadata.copy()
                table_metadata.update({
                    "content_type": "table_summary",
                    "table_index": i,
                    "element_id": table_data.get("id"),
                    "has_structure": table_data.get("has_structure", False),
                    "num_columns": table_data.get("key_info", {}).get("num_columns", 0),
                    "num_rows": table_data.get("key_info", {}).get("num_rows", 0)
                })
                
                # Combine table summary with markdown for better searchability
                table_content = f"{table_data['summary']}\n\nTable Content:\n{table_data.get('markdown', '')}"
                
                node = await cls.upsert(table_id, table_content, metadata=table_metadata, insert_node=False)
                nodes.append(node)
        
        # Insert all nodes at once
        Globals.get_index().insert_nodes(nodes)
        
        return len(nodes)
    
    @classmethod
    async def upsert_with_multivector(cls, doc_id: str, content: str, summary: str, 
                                     metadata: dict = None, content_type: str = "text") -> str:
        """
        Upsert a document with multi-vector retriever support.
        Stores the summary in vector database and links to original document.
        
        Args:
            doc_id: Document identifier
            content: Original document content
            summary: Summary for vector storage
            metadata: Document metadata
            content_type: Type of content (text, table, image)
            
        Returns:
            str: Document ID
        """
        try:
            # Store summary in vector database with multi-vector metadata
            vector_metadata = metadata.copy() if metadata else {}
            vector_metadata.update({
                "doc_id": doc_id,
                "content_type": f"{content_type}_summary",
                "retriever_type": "multi_vector",
                "has_parent_document": True,
                "original_content_type": content_type
            })
            
            # Store summary in vector database
            await cls.upsert(doc_id, summary, metadata=vector_metadata)
            
            return doc_id
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "qdrant_upsert_multivector", None)
            return ""
    
    @classmethod
    async def _enhance_text_with_multimodal_context(cls, text_chunk: str, multimodal_data: dict, chunk_index: int) -> str:
        """
        Enhance text chunks with relevant multimodal context for better retrieval.
        """
        enhanced_text = text_chunk
        
        # Add image context if images are present
        if multimodal_data.get("images"):
            image_summaries = [img.get("summary", "") for img in multimodal_data["images"]]
            if image_summaries:
                enhanced_text += f"\n\nDocument contains images: {'; '.join(image_summaries[:3])}"  # Limit to first 3
        
        # Add table context if tables are present
        if multimodal_data.get("tables"):
            table_summaries = [tbl.get("summary", "") for tbl in multimodal_data["tables"]]
            if table_summaries:
                enhanced_text += f"\n\nDocument contains tables: {'; '.join(table_summaries[:3])}"  # Limit to first 3
        
        return enhanced_text
    
    @classmethod
    async def search_multimodal(cls, query: str, has_images: bool = None, has_tables: bool = None, 
                               content_type: str = None, limit: int = 10):
        """
        Search for multimodal content with specific filters.
        
        Args:
            query: Search query
            has_images: Filter for documents with images
            has_tables: Filter for documents with tables
            content_type: Filter by content type ("image_summary", "table_summary", or None for all)
            limit: Maximum number of results
        """
        from llama_index.core.vector_stores import MetadataFilters, MetadataFilter, FilterOperator
        
        filters = []
        
        if has_images is not None:
            filters.append(MetadataFilter(key="has_images", value=str(has_images), operator=FilterOperator.EQ))
        
        if has_tables is not None:
            filters.append(MetadataFilter(key="has_tables", value=str(has_tables), operator=FilterOperator.EQ))
        
        if content_type is not None:
            filters.append(MetadataFilter(key="content_type", value=content_type, operator=FilterOperator.EQ))
        
        metadata_filters = MetadataFilters(filters=filters) if filters else None
        
        query_engine = Globals.get_index().as_query_engine(
            filters=metadata_filters,
            similarity_top_k=limit,
            response_mode="tree_summarize"
        )
        
        return query_engine.query(query)
    
    @classmethod
    async def get_document_assets(cls, doc_id: str):
        """
        Get all assets associated with a document.
        """
        from llama_index.core.vector_stores import MetadataFilters, MetadataFilter, FilterOperator
        
        filters = [
            MetadataFilter(key="multimodal_doc_id", value=doc_id, operator=FilterOperator.EQ)
        ]
        
        metadata_filters = MetadataFilters(filters=filters)
        
        # Use retriever to get nodes with metadata
        retriever = Globals.get_index().as_retriever(
            filters=metadata_filters,
            similarity_top_k=100  # Get all chunks for this document
        )
        
        # Dummy query to get all nodes for this document
        nodes = retriever.retrieve("document content")
        
        # Extract asset information
        assets = {
            "images": [],
            "tables": [],
            "chunks": []
        }
        
        for node in nodes:
            metadata = node.metadata
            if metadata.get("content_type") == "image_summary":
                assets["images"].append({
                    "id": metadata.get("element_id"),
                    "summary": node.text,
                    "asset_path": metadata.get("asset_path"),
                    "has_asset": metadata.get("has_asset", False)
                })
            elif metadata.get("content_type") == "table_summary":
                assets["tables"].append({
                    "id": metadata.get("element_id"),
                    "summary": node.text,
                    "num_columns": metadata.get("num_columns", 0),
                    "num_rows": metadata.get("num_rows", 0),
                    "has_structure": metadata.get("has_structure", False)
                })
            else:
                assets["chunks"].append({
                    "id": node.id_,
                    "text": node.text,
                    "chunk_index": metadata.get("chunk_index", 0)
                })
        
        return assets
