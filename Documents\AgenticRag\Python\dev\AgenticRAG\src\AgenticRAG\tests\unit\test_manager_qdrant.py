"""
Unit tests for QDrantManager
"""
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from imports import *

@pytest.mark.unit
class TestQDrantManager:
    """Test the QDrantManager functionality"""
    
    def test_singleton_pattern(self):
        """Test that QDrantManager follows singleton pattern"""
        from managers.manager_qdrant import QDrantManager
        
        instance1 = QDrantManager()
        instance2 = QDrantManager()
        instance3 = QDrantManager.get_instance()
        
        assert instance1 is instance2
        assert instance2 is instance3
    
    @pytest.mark.asyncio
    async def test_setup(self):
        """Test QDrantManager setup method"""
        from managers.manager_qdrant import QDrantManager
        
        # Reset initialization for test
        QDrantManager._initialized = False
        
        with patch.object(QDrantManager, 'CreateQDrantClient'):
            with patch.object(QDrantManager, 'CreateQDrantAsyncClient'):
                await QDrantManager.setup()
                
                instance = QDrantManager.get_instance()
                assert instance._initialized is True
                
                # Call setup again - should not reinitialize
                await QDrantManager.setup()
                assert instance._initialized is True
    
    def test_get_client_docker_environment(self):
        """Test GetClient method in Docker environment"""
        from managers.manager_qdrant import QDrantManager
        
        with patch('globals.Globals.is_docker', return_value=True):
            with patch('managers.manager_qdrant.QdrantClient') as mock_client:
                mock_qdrant_instance = MagicMock()
                mock_client.return_value = mock_qdrant_instance
                
                instance = QDrantManager.get_instance()
                instance._client = None  # Reset client
                
                client = QDrantManager.GetClient()
                
                # Verify client was created with Docker URL
                mock_client.assert_called_once_with(url="http://qdrant:6333", timeout=300)
                assert client == mock_qdrant_instance
    
    def test_get_client_local_environment(self):
        """Test GetClient method in local environment"""
        from managers.manager_qdrant import QDrantManager
        
        with patch('globals.Globals.is_docker', return_value=False):
            with patch('managers.manager_qdrant.QdrantClient') as mock_client:
                mock_qdrant_instance = MagicMock()
                mock_client.return_value = mock_qdrant_instance
                
                instance = QDrantManager.get_instance()
                instance._client = None  # Reset client
                
                client = QDrantManager.GetClient()
                
                # Verify client was created with localhost URL
                mock_client.assert_called_once_with(url="http://localhost:6333", timeout=300)
                assert client == mock_qdrant_instance
    
    def test_get_client_cached(self):
        """Test GetClient returns cached client"""
        from managers.manager_qdrant import QDrantManager
        
        instance = QDrantManager.get_instance()
        mock_client = MagicMock()
        instance._client = mock_client
        
        client = QDrantManager.GetClient()
        
        assert client == mock_client
    
    def test_get_async_client_docker_environment(self):
        """Test GetAsyncClient method in Docker environment"""
        from managers.manager_qdrant import QDrantManager
        
        with patch('globals.Globals.is_docker', return_value=True):
            with patch('managers.manager_qdrant.AsyncQdrantClient') as mock_client:
                mock_qdrant_instance = MagicMock()
                mock_client.return_value = mock_qdrant_instance
                
                instance = QDrantManager.get_instance()
                instance._aclient = None  # Reset client
                
                client = QDrantManager.GetAsyncClient()
                
                # Verify client was created with Docker URL
                mock_client.assert_called_once_with(url="http://qdrant:6333", timeout=300)
                assert client == mock_qdrant_instance
    
    def test_get_async_client_local_environment(self):
        """Test GetAsyncClient method in local environment"""
        from managers.manager_qdrant import QDrantManager
        
        with patch('globals.Globals.is_docker', return_value=False):
            with patch('managers.manager_qdrant.AsyncQdrantClient') as mock_client:
                mock_qdrant_instance = MagicMock()
                mock_client.return_value = mock_qdrant_instance
                
                instance = QDrantManager.get_instance()
                instance._aclient = None  # Reset client
                
                client = QDrantManager.GetAsyncClient()
                
                # Verify client was created with localhost URL
                mock_client.assert_called_once_with(url="http://localhost:6333", timeout=300)
                assert client == mock_qdrant_instance
    
    def test_get_async_client_cached(self):
        """Test GetAsyncClient returns cached client"""
        from managers.manager_qdrant import QDrantManager
        
        instance = QDrantManager.get_instance()
        mock_client = MagicMock()
        instance._aclient = mock_client
        
        client = QDrantManager.GetAsyncClient()
        
        assert client == mock_client
    
    def test_create_qdrant_client_initialization(self):
        """Test CreateQDrantClient initializes client properly"""
        from managers.manager_qdrant import QDrantManager
        
        with patch('globals.Globals.is_docker', return_value=False):
            with patch('managers.manager_qdrant.QdrantClient') as mock_client:
                mock_qdrant_instance = MagicMock()
                mock_client.return_value = mock_qdrant_instance
                
                instance = QDrantManager.get_instance()
                result = instance.CreateQDrantClient()
                
                assert instance._client == mock_qdrant_instance
                assert result == mock_qdrant_instance
                mock_client.assert_called_once_with(url="http://localhost:6333", timeout=300)
    
    def test_create_qdrant_async_client_initialization(self):
        """Test CreateQDrantAsyncClient initializes client properly"""
        from managers.manager_qdrant import QDrantManager
        
        with patch('globals.Globals.is_docker', return_value=False):
            with patch('managers.manager_qdrant.AsyncQdrantClient') as mock_client:
                mock_qdrant_instance = MagicMock()
                mock_client.return_value = mock_qdrant_instance
                
                instance = QDrantManager.get_instance()
                result = instance.CreateQDrantAsyncClient()
                
                assert instance._aclient == mock_qdrant_instance
                assert result == mock_qdrant_instance
                mock_client.assert_called_once_with(url="http://localhost:6333", timeout=300)
    
    
    def test_initialization_state(self):
        """Test initialization state management"""
        from managers.manager_qdrant import QDrantManager
        
        instance = QDrantManager.get_instance()
        
        # Test default state
        assert hasattr(instance, '_initialized')
        assert hasattr(instance, '_client')
        assert hasattr(instance, '_aclient')
    
    def test_client_port_configuration(self):
        """Test that clients are configured with correct port"""
        from managers.manager_qdrant import QDrantManager
        
        with patch('managers.manager_qdrant.QdrantClient') as mock_client:
            with patch('globals.Globals.is_docker', return_value=False):
                instance = QDrantManager.get_instance()
                instance.CreateQDrantClient()
                
                # Verify port 6333 is used in URL
                mock_client.assert_called_once_with(url="http://localhost:6333", timeout=300)
    
    @pytest.mark.asyncio
    async def test_upsert_method(self):
        """Test upsert method with node creation"""
        from managers.manager_qdrant import QDrantManager
        
        with patch('managers.manager_qdrant.RetrievalManager') as mock_retrieval:
            with patch('managers.manager_qdrant.Globals') as mock_globals:
                # Mock retrieval manager methods
                mock_retrieval.get_embeddings_dense = AsyncMock(return_value=[0.1, 0.2, 0.3])
                mock_sparse = MagicMock()
                mock_sparse.indices = [0, 1, 2]
                mock_sparse.values = [0.5, 0.6, 0.7]
                mock_retrieval.get_embeddings_sparse = AsyncMock(return_value=mock_sparse)
                
                # Mock globals index
                mock_index = MagicMock()
                mock_globals.get_index.return_value = mock_index
                
                result = await QDrantManager.upsert("test_id", "test content", {"key": "value"})
                
                # Verify embeddings were retrieved
                mock_retrieval.get_embeddings_dense.assert_called_once_with("test content")
                mock_retrieval.get_embeddings_sparse.assert_called_once_with("test content")
                
                # Verify node was inserted
                mock_index.insert_nodes.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_upsert_no_insert(self):
        """Test upsert method without inserting node"""
        from managers.manager_qdrant import QDrantManager
        
        with patch('managers.manager_qdrant.RetrievalManager') as mock_retrieval:
            # Mock retrieval manager methods
            mock_retrieval.get_embeddings_dense = AsyncMock(return_value=[0.1, 0.2, 0.3])
            mock_sparse = MagicMock()
            mock_sparse.indices = [0, 1, 2]
            mock_sparse.values = [0.5, 0.6, 0.7]
            mock_retrieval.get_embeddings_sparse = AsyncMock(return_value=mock_sparse)
            
            result = await QDrantManager.upsert("test_id", "test content", {"key": "value"}, insert_node=False)
            
            # Should return node instead of inserting
            assert result is not None
            assert hasattr(result, 'text')
            assert result.text == "test content"
    
    @pytest.mark.asyncio
    async def test_upsert_multiple(self):
        """Test upsert_multiple method"""
        from managers.manager_qdrant import QDrantManager
        
        with patch('managers.manager_qdrant.RetrievalManager') as mock_retrieval:
            with patch('managers.manager_qdrant.Globals') as mock_globals:
                with patch.object(QDrantManager, 'upsert', new_callable=AsyncMock) as mock_upsert:
                    # Mock the single upsert method
                    mock_node = MagicMock()
                    mock_upsert.return_value = mock_node
                    
                    # Mock globals index
                    mock_index = MagicMock()
                    mock_globals.get_index.return_value = mock_index
                    
                    ids = ["id1", "id2"]
                    contents = ["content1", "content2"]
                    metadatas = [{"key1": "value1"}, {"key2": "value2"}]
                    
                    await QDrantManager.upsert_multiple(ids, contents, metadatas)
                    
                    # Verify upsert was called for each item
                    assert mock_upsert.call_count == 2
                    mock_index.insert_nodes.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_upsert_multimodal(self):
        """Test upsert_multimodal method"""
        from managers.manager_qdrant import QDrantManager
        
        with patch.object(QDrantManager, 'upsert', new_callable=AsyncMock) as mock_upsert:
            with patch.object(QDrantManager, '_enhance_text_with_multimodal_context', new_callable=AsyncMock) as mock_enhance:
                with patch('managers.manager_qdrant.Globals') as mock_globals:
                    # Mock methods
                    mock_node = MagicMock()
                    mock_upsert.return_value = mock_node
                    mock_enhance.return_value = "enhanced text"
                    
                    mock_index = MagicMock()
                    mock_globals.get_index.return_value = mock_index
                    
                    # Test data
                    multimodal_data = {
                        "images": [{"summary": "image1", "asset_path": "/path1", "id": "img1"}],
                        "tables": [{"summary": "table1", "markdown": "| col1 |", "id": "tbl1"}]
                    }
                    
                    result = await QDrantManager.upsert_multimodal("doc1", ["chunk1"], multimodal_data)
                    
                    # Should return number of nodes created
                    assert isinstance(result, int)
                    assert result > 0
                    
                    # Verify index insertion
                    mock_index.insert_nodes.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_enhance_text_with_multimodal_context(self):
        """Test _enhance_text_with_multimodal_context method"""
        from managers.manager_qdrant import QDrantManager
        
        multimodal_data = {
            "images": [{"summary": "A chart showing sales data"}],
            "tables": [{"summary": "Revenue breakdown by quarter"}]
        }
        
        result = await QDrantManager._enhance_text_with_multimodal_context("Original text", multimodal_data, 0)
        
        assert "Original text" in result
        assert "contains images" in result
        assert "contains tables" in result
        assert "sales data" in result
        assert "Revenue breakdown" in result
    
    @pytest.mark.asyncio
    async def test_search_multimodal(self):
        """Test search_multimodal method"""
        from managers.manager_qdrant import QDrantManager
        
        with patch('managers.manager_qdrant.Globals') as mock_globals:
            mock_index = MagicMock()
            mock_query_engine = MagicMock()
            mock_query_engine.query.return_value = "search results"
            mock_index.as_query_engine.return_value = mock_query_engine
            mock_globals.get_index.return_value = mock_index
            
            result = await QDrantManager.search_multimodal("test query", has_images=True, content_type="image_summary")
            
            assert result == "search results"
            mock_index.as_query_engine.assert_called_once()
            mock_query_engine.query.assert_called_once_with("test query")
    
    @pytest.mark.asyncio
    async def test_search_multimodal_has_tables_filter(self):
        """Test search_multimodal method with has_tables filter"""
        from managers.manager_qdrant import QDrantManager
        
        with patch('managers.manager_qdrant.Globals') as mock_globals:
            mock_index = MagicMock()
            mock_query_engine = MagicMock()
            mock_query_engine.query.return_value = "search results"
            mock_index.as_query_engine.return_value = mock_query_engine
            mock_globals.get_index.return_value = mock_index
            
            result = await QDrantManager.search_multimodal("test query", has_tables=True)
            
            assert result == "search results"
            mock_index.as_query_engine.assert_called_once()
            mock_query_engine.query.assert_called_once_with("test query")
    
    @pytest.mark.asyncio
    async def test_get_document_assets(self):
        """Test get_document_assets method"""
        from managers.manager_qdrant import QDrantManager
        
        with patch('managers.manager_qdrant.Globals') as mock_globals:
            # Mock retriever and nodes
            mock_node1 = MagicMock()
            mock_node1.metadata = {"content_type": "image_summary", "element_id": "img1"}
            mock_node1.text = "Image summary"
            
            mock_node2 = MagicMock()
            mock_node2.metadata = {"content_type": "table_summary", "element_id": "tbl1"}
            mock_node2.text = "Table summary"
            
            mock_node3 = MagicMock()
            mock_node3.metadata = {"chunk_index": 0}
            mock_node3.text = "Text chunk"
            mock_node3.id_ = "chunk1"
            
            mock_retriever = MagicMock()
            mock_retriever.retrieve.return_value = [mock_node1, mock_node2, mock_node3]
            
            mock_index = MagicMock()
            mock_index.as_retriever.return_value = mock_retriever
            mock_globals.get_index.return_value = mock_index
            
            result = await QDrantManager.get_document_assets("doc1")
            
            # Verify structure
            assert "images" in result
            assert "tables" in result
            assert "chunks" in result
            assert len(result["images"]) == 1
            assert len(result["tables"]) == 1
            assert len(result["chunks"]) == 1