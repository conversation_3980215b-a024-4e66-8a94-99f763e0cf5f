from imports import *

import asyncio
from typing import Set
import httpx
import json
from langchain.prompts import ChatPromptTemplate
from langchain_core.output_parsers import <PERSON>r<PERSON>utputParser
from langchain_openai import ChatOpenAI
from llama_index.core.vector_stores import MetadataFilters
from llama_index.core.query_engine import BaseQueryEngine
from langchain_core.tools import BaseTool, tool
from llama_index.core.postprocessor import LLMRerank

from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor, SupervisorSupervisor_ChainOfThought, SupervisorTask_SingleAgent, SupervisorTaskState

class FilePathRetrieverTool(BaseTool):
    """Tool for extracting file paths from documents matching the query"""
    name: str = "file_path_retriever"
    description: str = "Extract and return only the file paths from documents matching the query"
    
    def _run(self, query: str, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, query: str, state: SupervisorTaskState = None) -> str:
        """Extract and return only the file paths from documents matching the query"""
        try:
            # Get user and chat session for logging
            from managers.manager_users import ZairaUserManager
            user = await ZairaUserManager.find_user(state.user_guid) if state else None
            chat_session = user.get_current_chat_session() if user else None
            
            LogFire.log("DEBUG", f"FILE PATH RETRIEVER: Started for query '{query}'", chat=chat_session, severity="debug")

            # Get the index from Globals
            index = Globals.get_index()

            # Configure the query engine to focus on retrieving documents
            # We use "no_text" response mode since we only care about metadata
            query_engine = index.as_query_engine(
                response_mode="no_text",
                similarity_top_k=10,
                vector_store_query_mode="hybrid",
                node_postprocessors=[
                    LLMRerank(top_n=5)
                ]
            )

            # Expand the query to improve retrieval - use original input if available
            query_to_expand = state.original_input if state and hasattr(state, 'original_input') and state.original_input else query
            expanded_queries = await expand_query(query_to_expand)
            LogFire.log("TASK", f"Successfully generated {len(expanded_queries)} query variations.", ", ".join(expanded_queries), chat=chat_session)

            # List of all query texts to use for retrieval
            all_retrieval_query_texts = [query] + expanded_queries
            LogFire.log("TASK", f"Total queries to process: {len(all_retrieval_query_texts)}.", ", ".join(all_retrieval_query_texts), chat=chat_session)

            # Extract file paths from source nodes
            all_file_paths: Set[str] = set()

            # Process each query variation
            for query_text in all_retrieval_query_texts:
                try:
                    # Only log every 3rd query to reduce verbosity
                    query_index = all_retrieval_query_texts.index(query_text)
                    LogFire.log_conditionally("TASK", f"Processing query: {query_text}.", 
                                             condition=query_index % 3 == 0, chat=chat_session, sql_addition=query_text)

                    # Execute the query
                    response = await query_engine.aquery(query_text)

                    # Try to extract source nodes
                    if hasattr(response, 'source_nodes'):
                        source_nodes = response.source_nodes
                        LogFire.log_conditionally("TASK", f"Retrieved {len(source_nodes)} nodes for file path extraction.", 
                                                 condition=len(source_nodes) > 0, chat=chat_session, 
                                                 sql_addition=", ".join([str(node)[:100] for node in source_nodes]))

                        # Extract file paths from each node's metadata
                        for node in source_nodes:
                            if hasattr(node, 'metadata') and node.metadata:
                                # Check for common file path metadata keys
                                if 'file_path' in node.metadata:
                                    all_file_paths.add(node.metadata['file_path'])
                                elif 'source' in node.metadata:
                                    all_file_paths.add(node.metadata['source'])
                                elif 'file_name' in node.metadata:
                                    all_file_paths.add(node.metadata['file_name'])
                                elif 'filename' in node.metadata:
                                    all_file_paths.add(node.metadata['filename'])
                                elif 'path' in node.metadata:
                                    all_file_paths.add(node.metadata['path'])
                except Exception as query_error:
                    LogFire.log("ERROR", f"Error processing query: {query_error}", chat=chat_session, sql_addition=query_text)

            LogFire.log("TASK", f"FILE PATH RETRIEVER: Completed - extracted {len(all_file_paths)} unique file paths", 
                       chat=chat_session, sql_addition=", ".join(list(all_file_paths)[:10]))

            # Return only the file paths, one per line
            if all_file_paths:
                return "\n".join(sorted(all_file_paths))
            else:
                return "No file paths found for your query."
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "file_path_retriever", chat_session)
            return f"Error retrieving file paths: {e}"


class WebSearchTool(BaseTool):
    """Tool for searching the web using SearXNG"""
    name: str = "web_search_tool"
    description: str = "Search the web for information using SearXNG search engine"
    
    def _run(self, query: str, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, query: str, state: SupervisorTaskState = None) -> str:
        """Search the web for information using SearXNG"""
        try:
            # Get user and chat session for logging
            from managers.manager_users import ZairaUserManager
            user = await ZairaUserManager.find_user(state.user_guid) if state else None
            chat_session = user.get_current_chat_session() if user else None
            
            LogFire.log("DEBUG", f"Starting SearXNG web search for query: '{query}'", chat=chat_session, severity="debug")
            
            # SearXNG endpoint configuration
            searxng_base_url = "http://dns.askzaira.com:8081"
            search_endpoint = f"{searxng_base_url}/search"
            
            # SearXNG search parameters
            search_params = {
                "q": query,
                "format": "json",
                "engines": "google,bing,duckduckgo",  # Use multiple search engines
                "categories": "general",
                "time_range": None,  # No time restriction
                "safesearch": 1,  # Moderate safe search
                "pageno": 1  # First page only
            }
            
            LogFire.log("DEBUG", f"SearXNG request URL: {search_endpoint}", chat=chat_session, severity="debug")
            LogFire.log("DEBUG", f"SearXNG parameters: {search_params}", chat=chat_session, severity="debug")
            
            # Make async HTTP request to SearXNG
            async with httpx.AsyncClient(timeout=30.0) as client:
                try:
                    response = await client.get(search_endpoint, params=search_params)
                    response.raise_for_status()
                    
                    LogFire.log("DEBUG", f"SearXNG response status: {response.status_code}", chat=chat_session, severity="debug")
                    
                except httpx.HTTPStatusError as e:
                    LogFire.log("DEBUG", f"SearXNG HTTP error: {e}", chat=chat_session, severity="debug")
                    return f"SearXNG search failed with HTTP {e.response.status_code}: {e.response.text}"
                except httpx.RequestError as e:
                    LogFire.log("DEBUG", f"SearXNG request error: {e}", chat=chat_session, severity="debug")
                    return f"Failed to connect to SearXNG at {searxng_base_url}: {e}"
            
            # Parse JSON response
            try:
                search_data = response.json()
                LogFire.log("DEBUG", f"SearXNG returned {len(search_data.get('results', []))} results", chat=chat_session, severity="debug")
            except json.JSONDecodeError as e:
                LogFire.log("DEBUG", f"Failed to parse SearXNG JSON response: {e}", chat=chat_session, severity="debug")
                return f"Invalid JSON response from SearXNG: {e}"
            
            # Extract and format results
            results = search_data.get('results', [])
            if not results:
                LogFire.log("DEBUG", "No results found in SearXNG response", chat=chat_session, severity="debug")
                return f"No search results found for query: '{query}'"
            
            # Format results for output (limit to top 5 results)
            formatted_results = []
            for i, result in enumerate(results[:5]):
                title = result.get('title', 'No title')
                content = result.get('content', result.get('snippet', 'No content available'))
                url = result.get('url', 'No URL')
                
                # Clean and truncate content
                if content:
                    # Remove excessive whitespace and limit length
                    content = ' '.join(content.split())
                    if len(content) > 300:
                        content = content[:297] + "..."
                
                formatted_result = f"Result {i+1}:\nTitle: {title}\nURL: {url}\nContent: {content}\n"
                formatted_results.append(formatted_result)
                
                LogFire.log_conditionally("DEBUG", f"Processed result {i+1}: {title[:50]}...", 
                                        condition=i < 2, chat=chat_session, severity="debug")  # Only log first 2 results
            
            # Combine all results
            web_results = "\n".join(formatted_results)
            
            # Add search metadata
            query_info = search_data.get('query', query)
            engines_used = search_data.get('infoboxes', {})
            
            final_results = f"Web search results for: '{query_info}'\n"
            final_results += f"Found {len(results)} total results via SearXNG\n"
            final_results += f"Showing top {min(5, len(results))} results:\n\n"
            final_results += web_results
            
            LogFire.log("DEBUG", f"SearXNG web search completed successfully", chat=chat_session, severity="debug")
            LogFire.log("WEB_SEARCH", f"SearXNG search completed for query: {query}", f"Found {len(results)} results", chat=chat_session)
            
            return final_results
            
        except Exception as e:
            LogFire.log("ERROR", f"Unexpected error in SearXNG web search: {e}", chat=chat_session, severity="error")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "web_search_tool", chat_session)
            return f"Error performing SearXNG web search: {e}"

# Create tool instances
file_path_retriever_tool = FilePathRetrieverTool()
web_search_tool = WebSearchTool()

async def retrieve_data(query_engine: BaseQueryEngine, user_input: str) -> dict:
    """Perform RAG search using the provided query engine"""
    # Note: query_engine parameter is not used as we create a new enhanced engine
    try:
        LogFire.log("DEBUG", f"Starting retrieval for query: '{user_input}'", severity="debug")

        # Get the index from Globals to create a new query engine with enhanced settings
        LogFire.log("DEBUG", "Creating enhanced query engine with hybrid search", severity="debug")

        # Create a new query engine with advanced settings
        index = Globals.get_index()
        filters = MetadataFilters(filters=[])

        # Configure the enhanced query engine with hybrid search WITHOUT LLM reranking
        # We'll collect all nodes and do the reranking later
        enhanced_engine = index.as_query_engine(
            filters=filters,
            response_mode="no_text",  # We only want the nodes, not the summarized response
            similarity_top_k=10,      # Vector search top k
            sparse_top_k=7,           # Keyword search top k
            vector_store_query_mode="hybrid"  # Using hybrid search
        )

        # Use aquery for async operation
        LogFire.log("DEBUG", "Executing query with enhanced retrieval settings...", severity="debug")
        response = await enhanced_engine.aquery(user_input)

        # Extract source nodes for feedback
        source_nodes = []
        vector_search_results = 10  # Default from configuration
        keyword_search_results = 7  # Default from configuration
        chunk_scores = []

        # Try to extract information about nodes
        try:
            if hasattr(response, 'source_nodes'):
                source_nodes = response.source_nodes
                LogFire.log("DEBUG", f"Retrieved {len(source_nodes)} nodes with hybrid search", severity="debug")

                # Extract scores from source nodes if available
                for node in source_nodes:
                    if hasattr(node, 'score') and node.score is not None:
                        chunk_scores.append(node.score)

            # Update metrics with actual values
            vector_search_results = len(source_nodes) if source_nodes else vector_search_results

        except Exception as e:
            LogFire.log("DEBUG", f"Could not extract node information: {e}", severity="debug")

        # Return both the source nodes and metrics
        return {
            "source_nodes": source_nodes,
            "metrics": {
                "query": user_input,
                "vector_search_results": vector_search_results,
                "keyword_search_results": keyword_search_results,
                "chunk_scores": chunk_scores
            }
        }
    except ValueError as e:
        if e.args[0] == 'Vector store query result should return at least one of nodes or ids.':
            return user_input
        etc.helper_functions.exception_triggered(e)
        return {
            "source_nodes": [],
            "metrics": None,
            "error": f"An error occurred: {e}"
        }
    except Exception as e:
        etc.helper_functions.exception_triggered(e)
        return {
            "source_nodes": [],
            "metrics": None,
            "error": f"An error occurred: {e}"
        }



async def expand_query(user_input: str) -> list[str]:
    """Generate multiple perspectives of the user query"""
    LogFire.log("DEBUG", f"Expanding query: '{user_input}'", severity="debug")

    template = """You are an AI language model assistant. Your task is to generate three
    different versions of the given user question to retrieve relevant documents.
    By generating multiple perspectives on the user question, your goal is to help
    overcome limitations of similarity search. Provide these alternative questions
    separated by newlines.

    Make sure each alternative query:
    1. Rephrases the question using different synonyms
    2. Changes the perspective of the question
    3. Breaks complex questions into simpler components


    Original question: {user_input}"""

    prompt_perspectives = ChatPromptTemplate.from_template(template)

    generate_queries = (
        prompt_perspectives
        | ChatOpenAI(temperature=0)
        | StrOutputParser()
        | (lambda x: x.split("\n"))
    )

    try:
        LogFire.log("DEBUG", "Generating query variations using LLM...", severity="debug")
        expanded_queries = await generate_queries.ainvoke({"user_input": user_input})

        # Filter out potential empty strings or numbering (e.g., "1. Query") more robustly
        cleaned_queries = [
            q.strip() for q in expanded_queries if q and q.strip() and not q.strip().isnumeric()
        ]
        # Optional: Remove potential numbering like "1. ", "2. "
        cleaned_queries = [q.split(". ", 1)[-1] if q[0].isdigit() and q[1] == '.' else q for q in cleaned_queries]

        # Log query variations conditionally to reduce verbosity
        LogFire.log("DEBUG", f"Generated {len(cleaned_queries)} query variations", severity="debug")
        for i, query in enumerate(cleaned_queries[:2]):  # Only log first 2 variations
            LogFire.log("DEBUG", f"  Variation {i+1}: {query}", severity="debug")

        return cleaned_queries
    except Exception as e:
        LogFire.log("DEBUG", f"Error during query expansion: {e}", severity="debug")
        etc.helper_functions.exception_triggered(e)
        return [] # Return empty list on failure

class RagSearchTool(BaseTool):
    """Tool for performing RAG search using the provided index"""
    name: str = "rag_search_tool"
    description: str = "Function to do RAG search using the provided index"

    def _run(self, query: str, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")

    async def _arun(self, query: str, state: SupervisorTaskState = None) -> str:
        """Function to do RAG search using the provided index"""
        try:
            LogFire.log("DEBUG", "===== RAG SEARCH TOOL STARTED =====", severity="debug")
            LogFire.log("DEBUG", f"Original query: '{query}'", severity="debug")

            # Pass None as query_engine since it's not used in retrieve_data
            query_engine = None

            # Expand the query - use original input if available
            query_to_expand = state.original_input if state and hasattr(state, 'original_input') and state.original_input else query
            expanded_queries = await expand_query(query_to_expand)
            LogFire.log("DEBUG", f"Successfully generated {len(expanded_queries)} query variations", severity="debug")

            # List of all query texts to use for retrieval
            all_retrieval_query_texts = [query] + expanded_queries
            LogFire.log("DEBUG", f"Total queries to process: {len(all_retrieval_query_texts)}", severity="debug")

            # Gather results for all queries
            LogFire.log("DEBUG", f"Starting retrieval for all query variations...", severity="debug")
            tasks = []
            for query_text in all_retrieval_query_texts:
                tasks.append(retrieve_data(query_engine, query_text))

            LogFire.log("DEBUG", f"Awaiting all retrieval tasks to complete...", severity="debug")
            all_results = await asyncio.gather(*tasks, return_exceptions=True)
            LogFire.log("DEBUG", f"All retrieval tasks completed", severity="debug")

            # Variables to store aggregated metrics and all source nodes
            all_metrics = []
            all_source_nodes = []
            best_metrics = None
            successful_queries = 0

            # Process results and collect all source nodes
            LogFire.log("DEBUG", f"Processing retrieval results and collecting all source nodes...", severity="debug")
            for i, result in enumerate(all_results):
                query_used = all_retrieval_query_texts[i]
                if isinstance(result, Exception):
                    LogFire.log("DEBUG", f"Error retrieving data for query '{query_used}': {result}", severity="debug")
                elif result:
                    successful_queries += 1

                    # Extract source nodes and metrics from the result
                    if isinstance(result, dict):
                        if 'source_nodes' in result and result['source_nodes']:
                            # Add source nodes to our collection
                            all_source_nodes.extend(result['source_nodes'])
                            LogFire.log_conditionally("DEBUG", f"Added {len(result['source_nodes'])} nodes from query '{query_used}'", 
                                                    condition=i < 2, severity="debug")  # Only log first 2 queries

                        # Collect metrics if available
                        if 'metrics' in result and result['metrics'] is not None:
                            all_metrics.append(result['metrics'])

                            # Use the metrics from the original query as the best metrics
                            if query_used == query and best_metrics is None:
                                best_metrics = result['metrics']

            LogFire.log("DEBUG", f"Successfully retrieved data for {successful_queries}/{len(all_retrieval_query_texts)} queries", severity="debug")
            LogFire.log("DEBUG", f"Collected a total of {len(all_source_nodes)} source nodes from all queries", severity="debug")

            # If we have no source nodes, return empty result
            if not all_source_nodes:
                LogFire.log("DEBUG", "No relevant data found for your query.", severity="debug")
                return "No relevant information found for your query."

            # Perform LLM reranking on all collected nodes
            LogFire.log("DEBUG", f"Performing LLM reranking on all {len(all_source_nodes)} collected nodes...", severity="debug")

            # Create an LLM reranker
            llm_reranker = LLMRerank(top_n=7)  # Get top 7 nodes

            # Rerank all nodes
            reranked_nodes = llm_reranker.postprocess_nodes(all_source_nodes, query_str=query)
            LogFire.log("DEBUG", f"After LLM reranking, selected top {len(reranked_nodes)} nodes", severity="debug")

            # Extract text from reranked nodes and combine
            node_texts = []
            for node in reranked_nodes:
                if hasattr(node, 'text') and node.text:
                    node_texts.append(node.text)
                elif hasattr(node, 'get_text') and callable(node.get_text):
                    node_texts.append(node.get_text())
                elif hasattr(node, 'node') and hasattr(node.node, 'text'):
                    node_texts.append(node.node.text)

            # Create a tree summarizer to combine the node texts
            from llama_index.core.response_synthesizers import TreeSummarize
            summarizer = TreeSummarize(verbose=True)

            # Summarize the reranked nodes
            LogFire.log("DEBUG", f"Summarizing the top {len(reranked_nodes)} nodes...", severity="debug")
            summary_response = await summarizer.aget_response(query, node_texts)
            LogFire.log("DEBUG", f"Successfully generated summary from top nodes", severity="debug")

            LogFire.log("DEBUG", f"===== RAG SEARCH TOOL COMPLETED =====", severity="debug")

            # Return the final response
            return summary_response
        except Exception as e:
            LogFire.log("ERROR", f"Critical error in rag_search_tool: {e}", severity="error")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "rag_search_tool", state.user_guid if state else None)
            return f"An error occurred: {e}"

@tool
async def multimodal_search_tool(query: str, content_type: str = None, state: SupervisorTaskState = None) -> str:
    """Search for multimodal content (images, tables, or all types) in the knowledge base"""
    try:
        LogFire.log("DEBUG", f"===== MULTIMODAL SEARCH TOOL STARTED =====", severity="debug")
        LogFire.log("DEBUG", f"Query: '{query}', Content type: '{content_type}'", severity="debug")
        
        # Use the new multimodal retrieval tool
        from tools.tool_multimodal_retrieval import MultimodalRetrievalTool
        
        # Determine content types to search
        content_types = ["text", "image", "table"]  # Default to all types
        
        if content_type:
            content_type = content_type.lower()
            if "image" in content_type:
                content_types = ["image"]
            elif "table" in content_type:
                content_types = ["table"]
            elif "both" in content_type or "all" in content_type:
                content_types = ["text", "image", "table"]
        
        multimodal_tool = MultimodalRetrievalTool()
        response = await multimodal_tool._arun(
            query=query,
            content_types=content_types,
            max_results=8,
            include_assets=True,
            filter_by_doc=None
        )
        
        LogFire.log("DEBUG", f"Multimodal search completed", severity="debug")
        LogFire.log("DEBUG", f"===== MULTIMODAL SEARCH TOOL COMPLETED =====", severity="debug")
        
        return response
        
    except Exception as e:
        LogFire.log("DEBUG", f"Error in multimodal_search_tool: {e}", severity="debug")
        return f"Error searching multimodal content: {e}"

@tool
async def image_focused_search_tool(query: str, state: SupervisorTaskState = None) -> str:
    """Search specifically for documents containing images and their descriptions"""
    try:
        LogFire.log("DEBUG", f"===== IMAGE FOCUSED SEARCH STARTED =====", severity="debug")
        LogFire.log("DEBUG", f"Query: '{query}'", severity="debug")
        
        # Use the new multimodal retrieval tool
        from tools.tool_multimodal_retrieval import MultimodalRetrievalTool
        
        multimodal_tool = MultimodalRetrievalTool()
        response = await multimodal_tool._arun(
            query=query,
            content_types=["image"],
            max_results=6,
            include_assets=True,
            filter_by_doc=None
        )
        
        LogFire.log("DEBUG", f"Image search completed", severity="debug")
        LogFire.log("DEBUG", f"===== IMAGE FOCUSED SEARCH COMPLETED =====", severity="debug")
        
        return response
        
    except Exception as e:
        LogFire.log("DEBUG", f"Error in image_focused_search_tool: {e}", severity="debug")
        return f"Error searching image content: {e}"

@tool
async def table_focused_search_tool(query: str, state: SupervisorTaskState = None) -> str:
    """Search specifically for documents containing tables and their data"""
    try:
        LogFire.log("DEBUG", f"===== TABLE FOCUSED SEARCH STARTED =====", severity="debug")
        LogFire.log("DEBUG", f"Query: '{query}'", severity="debug")
        
        # Use the new multimodal retrieval tool
        from tools.tool_multimodal_retrieval import MultimodalRetrievalTool
        
        multimodal_tool = MultimodalRetrievalTool()
        response = await multimodal_tool._arun(
            query=query,
            content_types=["table"],
            max_results=6,
            include_assets=True,
            filter_by_doc=None
        )
        
        LogFire.log("DEBUG", f"Table search completed", severity="debug")
        LogFire.log("DEBUG", f"===== TABLE FOCUSED SEARCH COMPLETED =====", severity="debug")
        
        return response
        
    except Exception as e:
        LogFire.log("DEBUG", f"Error in table_focused_search_tool: {e}", severity="debug")
        return f"Error searching table content: {e}"

@tool
async def get_document_assets_tool(doc_id: str, state: SupervisorTaskState = None) -> str:
    """Retrieve all assets (images, tables, text chunks) for a specific document"""
    try:
        LogFire.log("DEBUG", f"===== GET DOCUMENT ASSETS STARTED =====", severity="debug")
        LogFire.log("DEBUG", f"Document ID: '{doc_id}'", severity="debug")
        
        from managers.manager_qdrant import QDrantManager
        
        # Get all assets for the document
        assets = await QDrantManager.get_document_assets(doc_id)
        
        # Format the response
        result = []
        
        if assets["images"]:
            result.append(f"Images ({len(assets['images'])}):")
            for img in assets["images"]:
                result.append(f"  - {img['summary']}")
                if img.get("asset_path"):
                    result.append(f"    File: {img['asset_path']}")
        
        if assets["tables"]:
            result.append(f"\nTables ({len(assets['tables'])}):")
            for tbl in assets["tables"]:
                result.append(f"  - {tbl['summary']}")
                if tbl.get("num_columns") and tbl.get("num_rows"):
                    result.append(f"    Size: {tbl['num_columns']} columns x {tbl['num_rows']} rows")
        
        if assets["chunks"]:
            result.append(f"\nText Chunks ({len(assets['chunks'])}):")
            for chunk in assets["chunks"][:3]:  # Show first 3 chunks
                preview = chunk["text"][:100] + "..." if len(chunk["text"]) > 100 else chunk["text"]
                result.append(f"  - Chunk {chunk['chunk_index']}: {preview}")
        
        LogFire.log("DEBUG", f"Retrieved assets for document", severity="debug")
        LogFire.log("DEBUG", f"===== GET DOCUMENT ASSETS COMPLETED =====", severity="debug")
        
        return "\n".join(result) if result else "No assets found for this document."
        
    except Exception as e:
        LogFire.log("DEBUG", f"Error in get_document_assets_tool: {e}", severity="debug")
        return f"Error retrieving document assets: {e}"



# Create tool instance
rag_search_tool = RagSearchTool()

class SupervisorTask_Retrieval(SupervisorTask_SingleAgent):
    pass
    # async def llm_call(self, state: SupervisorTaskState):
    #     """Execute the current task"""
    #     # Currently IDENTICAL to SupervisorTask_SingleAgent
    #     # I've created a copy in order to allow for customisation of the prompt

    #     used_model = self.model
    #     if self._tools:
    #         used_model = used_model.bind_tools(self._tools)
    #         tools_by_name = {tool.name: tool for tool in self._tools}
    #     tool_prompts = [
    #         f"{tool.name} prompt: {tool.description}."
    #         for tool in self._tools
    #     ]

    #     full_prompt = self.get_prompt(True) + "\n" + "\n".join(tool_prompts)
    #     result = await used_model.ainvoke(
    #         input=[
    #             SystemMessage(
    #                 content=f"You are an agent tasked with answering a question using the following tools: {tools_by_name}."
    #                          " Your tools are sorted in order of relevance and importance. If a tool earlier in the list could potentially solve the issue, try that one first."
    #                          " IMPORTANT: When calling tools, you MUST use the exact original user input as the query parameter. Do not translate, interpret, or modify the original question."
    #                          f" The original user input is: '{state.original_input}'"
    #                          " Use this exact text as the query parameter for any tools you call."
    #                          " Given the following original_input,"
    #                          " determine which tools need to be called to create the best result. Each tool can only be called once."
    #                          " If your tool list is empty, respond to the best of your abilities with an answer to the original_input."
    #                          " Consider the above as your logic."
    #                          " Consider the below as your prompts:"
    #                         f"\n{full_prompt}")
    #             # Context is already available through state and conversation history
    #         ]
    #     )
    #     if self._tools:
    #         results = []
    #         call_trace = []
            
    #         # Debug: Log what the LLM decided to do
    #         if hasattr(result, 'tool_calls') and result.tool_calls:
    #             print(f"[DEBUG] {self.name} calling {len(result.tool_calls)} tools: {[tc['name'] for tc in result.tool_calls]}")
    #         else:
    #             print(f"[DEBUG] {self.name} made no tool calls. Response: {getattr(result, 'content', 'No content')[:100]}...")
            
    #         for tool_call in result.tool_calls:
    #             tool = tools_by_name[tool_call["name"]]
    #             call_trace.append(self.name + ": tool " + tool.name)
                
    #             # Fix tool argument handling - always ensure state is passed correctly
    #             if "state" not in tool_call["args"] or not tool_call["args"]["state"]:
    #                 tool_call["args"]["state"] = state
                
    #             try:
    #                 observation = await tool.ainvoke(input=tool_call["args"])
    #                 results.append(ToolMessage(content=observation, tool_call_id=tool_call["id"]))
    #                 print(f"[DEBUG] Tool {tool.name} executed successfully")
    #             except Exception as e:
    #                 print(f"[DEBUG] Tool {tool.name} failed: {e}")
    #                 results.append(ToolMessage(content=f"Tool execution failed: {e}", tool_call_id=tool_call["id"]))
                    
    #         return Command(update={"call_trace": call_trace, "messages": results, "completed_tasks": [self.name]})

    #     return Command(update={"call_trace": [f"{self.name}: llm_call"], "messages": result, "completed_tasks": [self.name]})

async def create_supervisor_retrieval() -> SupervisorSupervisor:
    class TaskCreator:
        research_task: SupervisorTask_SingleAgent = None
        rag_task: SupervisorTask_SingleAgent = None
        file_path_retriever_task: SupervisorTask_SingleAgent = None
        multimodal_search_task: SupervisorTask_SingleAgent = None
        image_search_task: SupervisorTask_SingleAgent = None
        table_search_task: SupervisorTask_SingleAgent = None
        document_assets_task: SupervisorTask_SingleAgent = None

        async def create_tasks(self):
            self.research_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(name="web_search_task", tools=[web_search_tool], prompt_id="Task_Retrieval_Web_Search"))

            self.rag_task = SupervisorManager.register_task( SupervisorTask_Retrieval(name="rag_task", tools=[rag_search_tool], prompt_id="Task_Retrieval_RAG"))

            self.file_path_retriever_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(name="file_path_retriever_task", tools=[file_path_retriever_tool], prompt_id="Task_Retrieval_FilePath"))
            
            # Multimodal search tasks
            self.multimodal_search_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(name="multimodal_search_task", tools=[multimodal_search_tool], prompt_id="Task_Retrieval_Multimodal"))
            
            self.image_search_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(name="image_search_task", tools=[image_focused_search_tool], prompt_id="Task_Retrieval_Images"))
            
            self.table_search_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(name="table_search_task", tools=[table_focused_search_tool], prompt_id="Task_Retrieval_Tables"))
            
            self.document_assets_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(name="document_assets_task", tools=[get_document_assets_tool], prompt_id="Task_Retrieval_Assets"))

        async def create_supervisor(self) -> SupervisorSupervisor:
            # Use CoT-enhanced supervisor for better reasoning about search strategies
            return SupervisorManager.register_supervisor(SupervisorSupervisor_ChainOfThought(name="search_supervisor", prompt_id="Supervisor_Retrieval_CoT")) \
                 .add_task(task=self.rag_task, priority=1) \
                 .add_task(task=self.multimodal_search_task, priority=2) \
                 .add_task(task=self.image_search_task) \
                 .add_task(task=self.table_search_task) \
                 .add_task(task=self.file_path_retriever_task) \
                 .add_task(task=self.document_assets_task) \
                 .add_task(task=self.research_task) \
                .compile()

                
                

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()
