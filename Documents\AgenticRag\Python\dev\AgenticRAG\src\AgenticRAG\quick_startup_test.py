#!/usr/bin/env python3
"""
Quick startup test to verify the hang fix.
Run this instead of the full debugger to test the fix.
"""

import asyncio
import sys
import os
import time
from imports import *

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def log_with_timestamp(message: str):
    """Log message with precise timestamp"""
    timestamp = time.strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] QUICK_TEST: {message}")
    sys.stdout.flush()

async def quick_startup_test():
    """Quick test of the startup process"""
    log_with_timestamp("=== QUICK STARTUP TEST ===")
    
    try:
        # Step 1: Basic imports
        log_with_timestamp("Step 1: Basic imports...")
        
        log_with_timestamp("✅ Step 1: Basic imports successful")
        
        # Step 2: LogFire setup
        log_with_timestamp("Step 2: LogFire setup...")
        await LogFire.setup()
        log_with_timestamp("✅ Step 2: LogFire setup successful")
        
        # Step 3: Debug mode
        log_with_timestamp("Step 3: Setting debug mode...")
        ZairaSettings.IsDebugMode = True
        Globals.set_debug(ZairaSettings.IsDebugMode)
        log_with_timestamp("✅ Step 3: Debug mode set")
        
        # Step 4: Test the problematic part - late_init
        log_with_timestamp("Step 4: Testing late_init (the problematic part)...")
        
        # Import the functions we need
        from etc.setup import late_init
        
        # Run late_init with a timeout
        try:
            await asyncio.wait_for(late_init(), timeout=60.0)
            log_with_timestamp("✅ Step 4: late_init completed successfully!")
        except asyncio.TimeoutError:
            log_with_timestamp("❌ Step 4: late_init still hangs after 60 seconds")
            log_with_timestamp("The fix didn't work - there may be another issue")
            raise
        
        log_with_timestamp("=== QUICK STARTUP TEST COMPLETED SUCCESSFULLY ===")
        log_with_timestamp("The hang fix appears to be working!")
        
    except Exception as e:
        log_with_timestamp(f"❌ ERROR in quick startup test: {str(e)}")
        import traceback
        log_with_timestamp(f"❌ TRACEBACK: {traceback.format_exc()}")
        raise

async def minimal_test():
    """Even more minimal test - just test supervisor creation"""
    log_with_timestamp("=== MINIMAL SUPERVISOR TEST ===")
    
    try:
        # Basic setup
        
        await LogFire.setup()
        ZairaSettings.IsDebugMode = True
        Globals.set_debug(ZairaSettings.IsDebugMode)
        
        # Setup minimal managers
        from managers.manager_supervisors import SupervisorManager
        await SupervisorManager.setup()
        
        # Test creating supervisors in the new order
        log_with_timestamp("Creating output supervisor first...")
        from tasks.task_top_output_supervisor import create_top_output_supervisor
        output_supervisor = await asyncio.wait_for(create_top_output_supervisor(), timeout=30.0)
        log_with_timestamp("✅ Output supervisor created")
        
        log_with_timestamp("Creating top level supervisor...")
        from tasks.task_top_level_supervisor import create_top_level_supervisor
        top_supervisor = await asyncio.wait_for(create_top_level_supervisor(), timeout=30.0)
        log_with_timestamp("✅ Top level supervisor created")
        
        log_with_timestamp("=== MINIMAL SUPERVISOR TEST PASSED ===")
        
    except asyncio.TimeoutError:
        log_with_timestamp("❌ MINIMAL TEST: Still hanging after fix")
        raise
    except Exception as e:
        log_with_timestamp(f"❌ MINIMAL TEST ERROR: {str(e)}")
        raise

if __name__ == "__main__":
    log_with_timestamp("Starting quick startup test...")
    
    # First try the minimal test
    try:
        log_with_timestamp("Running minimal test first...")
        asyncio.run(minimal_test())
        log_with_timestamp("Minimal test passed! Now trying full test...")
        
        # If minimal test passes, try the full test
        asyncio.run(quick_startup_test())
        
        log_with_timestamp("🎉 ALL TESTS PASSED! The startup hang fix is working!")
        
    except KeyboardInterrupt:
        log_with_timestamp("Test interrupted by user")
    except Exception as e:
        log_with_timestamp(f"❌ Test failed: {str(e)}")
        log_with_timestamp("The hang issue may still exist or there's another problem")
        sys.exit(1)
