from imports import *

import threading
from discord import Client, Intents, Message, Member, Guild
import asyncio
from asyncio.events import AbstractEvent<PERSON>oop

from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import PERMISSION_LEVELS
from endpoints.mybot_generic import My<PERSON>ot_Generic, ChannelType, ReplyContext
from endpoints.oauth._verifier_ import OAuth2Verifier
from managers.manager_logfire import LogFire

class MyDiscordClient(Client):
    async def on_ready(self):
        await MyDiscordBot.on_ready()

    async def on_message(self, message: Message):
        await MyDiscordBot.on_message(message)

    async def on_member_join(self, member: Member):
        await MyDiscordBot.on_member_join(member)

    async def on_member_leave(self, member: Member):
        await MyDiscordBot.on_member_leave(member)
    
    async def on_guild_join(self, guild: Guild):
        await MyDiscordBot.on_guild_join(guild)

class MyDiscordBot:
    _instance = None
    _initialized = False
    bot_generic: MyBot_Generic = None
    discord_guild_id: int = 0
    members: list[Member] = []
    
    # Discord Bot Setup
    intents = Intents.default()
    intents.message_content = True
    intents.guilds = True
    intents.members = True
    bot = MyDiscordClient(intents=intents)

    loop: AbstractEventLoop = None
    bot_thread: threading.Thread = None

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    @classmethod
    async def setup(cls):
        LogFire.log("INIT", "Discord bot setup called", chat=None)
        instance = cls.get_instance()
        if instance._initialized:
            LogFire.log("DEBUG", "Discord bot already initialized", chat=None)
            return
        
        # Allow Discord bot to run in debug mode for testing
        #if Globals.is_debug() == True:
            return
        
        # Skip Discord in test environment to prevent hanging
        try:
            from etc.test_environment_detector import is_test_environment_active
            if is_test_environment_active():
                LogFire.log("DEBUG", "Skipping Discord initialization in test environment", chat=None)
                return
        except ImportError:
            pass
        
        # Also check for pytest environment
        import sys
        if 'pytest' in sys.modules:
            LogFire.log("DEBUG", "Skipping Discord initialization in pytest environment", chat=None)
            return
        
        # Skip Discord in test environment to prevent hanging
        try:
            from etc.test_environment_detector import is_test_environment_active
            if is_test_environment_active():
                LogFire.log("DEBUG", "Skipping Discord initialization in test environment", chat=None)
                return
        except ImportError:
            pass
        
        # Also check for pytest environment
        import sys
        if 'pytest' in sys.modules:
            LogFire.log("DEBUG", "Skipping Discord initialization in pytest environment", chat=None)
            return

        # Get the current event loop or create a new one
        instance.loop = asyncio.new_event_loop()
        instance.discord_guild_id = await OAuth2Verifier.get_token("discord", "token_type")
        if instance.discord_guild_id.isdigit():
            instance.discord_guild_id = int(instance.discord_guild_id)
        else:
            # String is not a valid discord Guild ID
            instance.discord_guild_id = 0
        if not Globals.is_docker() and instance.discord_guild_id == 0:
            instance.discord_guild_id = 1332344925655924796
        LogFire.log("INIT", f"Discord is listening on guild ID: {instance.discord_guild_id}", chat=None)
        
        # Function to run in thread
        def run_bot_in_loop():
            asyncio.set_event_loop(instance.loop)  # Set the loop for this thread
            instance.loop.run_until_complete(MyDiscordBot.run_discord_bot_internal())
        
        # Create and start the thread
        instance.bot_thread = threading.Thread(target=run_bot_in_loop, daemon=True)
        instance.bot_thread.start()
        LogFire.log("INIT", "Discord bot thread started", chat=None)
        instance.bot_generic = MyBot_Generic(instance, "Discord")
        
        instance._initialized = True

    @classmethod
    async def on_ready(cls):
        instance = cls.get_instance()
        bot_user = MyDiscordBot.bot.user
        LogFire.log("INIT", f"{bot_user} has connected to Discord!", chat=None)

        # Make sure we have the correct intents to get guild/member info
        for guild in MyDiscordBot.bot.guilds:
            if guild.id == instance.discord_guild_id:
                LogFire.log("INIT", f"Connected to guild: {guild.name} (id: {guild.id})", chat=None)

                # Check if you have member intent enabled
                if guild.chunked is False:
                    await guild.chunk()

                for member in guild.members:
                    if member.guild_permissions.administrator \
                        or member.guild_permissions.manage_guild \
                            or member.guild_permissions.kick_members \
                                or member.guild_permissions.ban_members:
                        # User is likely a moderator
                        instance.members.append(member)

        await instance.bot_generic.on_ready()

    @classmethod
    async def on_member_join(cls, member: Member):
        instance = cls.get_instance()
        if instance.discord_guild_id != member.guild.id:
            return
        instance.members.append(member)

    @classmethod
    async def on_member_leave(cls, member: Member):
        instance = cls.get_instance()
        if instance.discord_guild_id != member.guild.id:
            return
        instance.members.remove(member)

    @classmethod
    async def on_guild_join(cls, guild: Guild):
        instance = cls.get_instance()

    @classmethod
    async def on_message(cls, message: Message):
        instance = cls.get_instance()
        if message.author == MyDiscordBot.bot.user:
            return
        if instance.discord_guild_id == 0:
            return
        if message.author not in instance.members:
            return
        
        username: str = str(message.author)
        user_message: str = message.content
        channel: str = str(message.channel)
        if len(user_message) < 1:
            return
        
        # Check if this message is a reply to another message
        is_reply = False
        replied_message_id = None
        replied_message_content = None
        replied_message_author = None
        
        if message.reference and message.reference.message_id:
            is_reply = True
            replied_message_id = message.reference.message_id
            
            # Try to fetch the original message for additional context
            try:
                replied_message = await message.channel.fetch_message(replied_message_id)
                replied_message_content = replied_message.content
                replied_message_author = str(replied_message.author)
                LogFire.log("DEBUG", f"Discord message is a reply to message ID {replied_message_id} from {replied_message_author}: '{replied_message_content}'", chat=None)  # No user context yet
            except Exception as e:
                LogFire.log("ERROR", f"Could not fetch replied message {replied_message_id}: {e}", severity="warning", chat=None)  # No user context yet
        
        LogFire.log("EVENT", f"Discord message - Is Reply: {is_reply}, From: {username}, Content: '{user_message}'", chat=None)  # No user context yet
        
        user = await ZairaUserManager.get_user(username)
        if user == None:
            # Temporarily create a new user if it's the first message since the server was started
            # This needs replaced with a database check
            user = await ZairaUserManager.add_user(username, PERMISSION_LEVELS.USER, ZairaUserManager.create_guid(), ZairaUserManager.create_guid())
        
        # Get chat session for logging
        chat_session = user.get_current_chat_session() if user else None
        
        files_bytes = []
        if message.attachments:
            for attachment in message.attachments:
                LogFire.log("DEBUG", f"Attachment found: {attachment.filename}", chat=chat_session)
                # Download the file if needed
                file_bytes = await attachment.read()
                with open(attachment.filename, "wb") as f:
                    f.write(file_bytes)
                    files_bytes.append(attachment.filename)

        # Create reply context object
        if is_reply:
            reply_context = ReplyContext.create_reply(
                replied_message_id=str(replied_message_id),
                replied_message_content=replied_message_content,
                replied_message_author=replied_message_author,
                platform="Discord"
            )
        else:
            reply_context = ReplyContext.create_no_reply()
        
        if user != None:
            if channel == 'Direct Message with Unknown User':
                LogFire.log("EVENT", f"Discord received a private message from: {user.username}", chat=chat_session)
                await instance.bot_generic.on_message(ChannelType.PRIVATE, user, user_message, files_bytes, message, reply_context)
            elif user_message[0] == '!' and 'zaira' in channel:
                if instance.discord_guild_id != message.guild.id:
                    return
                if len(user_message) <= 1:
                    return
                user_message = user_message[1:]
                LogFire.log("EVENT", f"Discord received a message on guild ID: {message.guild.id}, guild name: {message.guild.name}", chat=chat_session)
                await instance.bot_generic.on_message(ChannelType.PUBLIC, user, user_message, files_bytes, message, reply_context)

    @classmethod
    async def send_discord_broadcast(cls, response: str):
        if not response:
            LogFire.log("DEBUG", "Broadcast message was empty.", chat=None)
            return

        try:
            instance = cls.get_instance()
            
            # Use a thread-safe approach to schedule the Discord send on the bot's event loop
            import asyncio
            import threading
            from concurrent.futures import ThreadPoolExecutor
            
            def _send_discord_sync():
                """Synchronous wrapper to schedule Discord send on bot's loop"""
                try:
                    # Get the Discord bot's event loop
                    bot_loop = None
                    if hasattr(MyDiscordBot.bot, 'loop'):
                        bot_loop = MyDiscordBot.bot.loop
                    elif hasattr(MyDiscordBot.bot, '_connection') and hasattr(MyDiscordBot.bot._connection, 'loop'):
                        bot_loop = MyDiscordBot.bot._connection.loop
                    
                    if bot_loop and bot_loop.is_running():
                        # Schedule on Discord bot's loop using thread-safe method
                        future = asyncio.run_coroutine_threadsafe(_discord_send_async(), bot_loop)
                        # Don't wait for result to avoid blocking
                        return True
                    else:
                        LogFire.log("ERROR", "Discord bot loop not available for broadcast", severity="warning", chat=None)
                        return False
                except Exception as e:
                    LogFire.log("ERROR", f"Failed to schedule Discord broadcast: {str(e)}", severity="error", chat=None)
                    return False
            
            async def _discord_send_async():
                """Async function to run on Discord bot's event loop"""
                try:
                    for guild in MyDiscordBot.bot.guilds:
                        if instance.discord_guild_id == guild.id:
                            for channel in guild.text_channels:
                                if "zaira" in channel.name.lower():
                                    permissions = channel.permissions_for(guild.me)
                                    if permissions.send_messages:
                                        try:
                                            await channel.send(response)
                                            LogFire.log("EVENT", f"Broadcast sent to {channel.name} in {guild.name}", chat=None)
                                        except Exception as e:
                                            LogFire.log("ERROR", f"Failed to send to {channel.name} in {guild.name}: {e}", severity="error", chat=None)
                                    else:
                                        LogFire.log("ERROR", f"No permission to send messages in {channel.name} in {guild.name}", severity="warning", chat=None)
                except Exception as e:
                    LogFire.log("ERROR", f"Discord send async failed: {str(e)}", severity="error", chat=None)
            
            # Try to schedule the Discord send operation
            success = _send_discord_sync()
            if not success:
                LogFire.log("ERROR", "Discord broadcast could not be scheduled", severity="warning", chat=None)
                
        except Exception as e:
            LogFire.log("ERROR", f"Discord broadcast failed: {str(e)}", severity="error", chat=None)

    @classmethod
    async def send_a_discord_message(cls, message: Message, response: str) -> None:
        if not response:
            LogFire.log("ERROR", "Message was empty because intents may not be properly configured", severity="warning", chat=None)
            return

        try:
            await message.reply(response)
        except Exception as e:
            LogFire.log("ERROR", f"Error sending message: {e}", severity="error", chat=None)

    
    @classmethod
    async def run_discord_bot_internal(cls):
        try:
            LogFire.log("INIT", "Starting Discord bot connection...", chat=None)
            await MyDiscordBot.bot.start(DISCORD_TOKEN)
        except Exception as e:
            LogFire.log("ERROR", f"Error running bot: {e}", severity="error", chat=None)
            import traceback
            traceback.print_exc()
