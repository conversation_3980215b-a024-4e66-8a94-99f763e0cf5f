# Use the official Python base image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Copy dependency list and install
COPY scripts/deployment/requirements_oauth_external.txt .
RUN pip install -r ./requirements_oauth_external.txt

# Copy application code
COPY . .

# Expose the port the app runs on
EXPOSE 8084

# Command to run the app
CMD ["python", "-m", "endpoints.standalones.relay.relay_main"]
