from imports import *

# https://langchain-ai.github.io/langgraph/tutorials/workflows/#agent
from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base, SupervisorTaskState
from managers.manager_users import ZairaUserManager
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest

class SupervisorTask_Python(SupervisorTask_Base):
    async def llm_call(self, state: SupervisorTaskState):
        input_message = state.messages[-1].content if len(state.messages) > 1 else state.messages[0].content
        user = await ZairaUserManager.find_user(state.user_guid)
        
        # Python output task should only log to console, not send responses to user
        # The actual response will be sent by the main supervisor completion flow
        LogFire.log("OUTPUT", "Python:", input_message)
        call_trace_str = "\n".join(state.call_trace)
        chat_session = await self.get_chat_session_from_state(state)
        LogFire.log("DEBUG", f"Call trace:\n{call_trace_str}\nResult:\n{input_message}", chat=chat_session, severity="debug")
        
        if state.scheduled_guid not in user.my_requests:
            chat_session = await self.get_chat_session_from_state(state)
            LogFire.log("ERROR", f"No Python task found for user {state.user_guid}", chat=chat_session, severity="warning")

async def create_out_task_python() -> SupervisorTask_Base:
    return SupervisorManager.register_task(SupervisorTask_Python(name="python_out", prompt_id="Output_Sender_Python"))
