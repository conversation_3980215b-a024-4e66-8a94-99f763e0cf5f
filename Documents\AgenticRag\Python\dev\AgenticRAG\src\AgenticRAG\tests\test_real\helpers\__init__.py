#!/usr/bin/env python3
"""
Test Real Helpers Package

This package contains category-specific helper classes for real system integration tests.
These helpers provide specialized functionality that was extracted from the base BaseRealTest
class to maintain separation of concerns.

Available Helpers:
- EmailTestHelper: Email-specific testing functionality (SMTP, IMAP, email generation)
- CalendarTestHelper: Calendar-specific testing functionality (scheduling, agenda planning)

Usage:
    from tests.test_real.helpers.email_test_helper import EmailTestHelper
    from tests.test_real.helpers.calendar_test_helper import CalendarTestHelper
    
    class TestMyEmailFeature(BaseRealTest):
        async def test_email_functionality(self):
            setup_success = await self.setup_real_system()
            assert setup_success
            
            # Use email helper for email-specific setup
            email_helper = EmailTestHelper(self)
            await email_helper.setup_email_components()
            
            # Continue with test...
"""

from .email_test_helper import EmailTestHelper, skip_if_no_smtp, skip_if_no_imap
from .calendar_test_helper import <PERSON><PERSON><PERSON><PERSON>el<PERSON>, skip_if_no_calendar, skip_if_no_google_api

# Export public API
__all__ = [
    'EmailTestHelper',
    'CalendarTestHelper', 
    'skip_if_no_smtp',
    'skip_if_no_imap',
    'skip_if_no_calendar',
    'skip_if_no_google_api'
]