from imports import *
from langchain_core.tools import BaseTool
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field

class MultiVectorSearchInput(BaseModel):
    """Input for multi-vector search tool."""
    query: str = Field(..., description="Search query")
    k: int = Field(default=5, description="Number of results to return")
    content_types: List[str] = Field(
        default=["text", "image", "table"], 
        description="Types of content to search: text, image, table"
    )
    return_parent_documents: bool = Field(
        default=True, 
        description="Whether to return original documents along with summaries"
    )
    include_asset_paths: bool = Field(
        default=True, 
        description="Include asset paths for images when available"
    )
    filter_by_file: Optional[str] = Field(
        default=None, 
        description="Filter results by specific file name"
    )

class MultiVectorRetrievalTool(BaseTool):
    """
    Multi-vector retrieval tool that searches summaries and returns linked parent documents.
    Similar to LangChain's MultiVectorRetriever but using our PostgreSQL + Qdrant infrastructure.
    """
    
    name: str = "multivector_retrieval"
    description: str = """
    Advanced multimodal retrieval tool that searches document summaries and returns original content.
    
    This tool provides:
    - Searches AI-generated summaries for faster retrieval
    - Returns original documents (text, images, tables) linked to summaries
    - Supports filtering by content type and file name
    - Includes asset paths for images when available
    - Maintains document relationships and context
    
    Use this tool when you need to:
    - Find specific information and get the complete original content
    - Search across multimodal documents efficiently
    - Retrieve images, tables, and text with their full context
    - Access original documents based on summary matches
    """
    
    args_schema = MultiVectorSearchInput
    
    def _run(self, query: str, k: int = 5, content_types: List[str] = None,
             return_parent_documents: bool = True, include_asset_paths: bool = True,
             filter_by_file: Optional[str] = None) -> str:
        raise NotImplementedError("Use async version")
    
    async def _arun(self, query: str, k: int = 5, content_types: List[str] = None,
                   return_parent_documents: bool = True, include_asset_paths: bool = True,
                   filter_by_file: Optional[str] = None) -> str:
        """Perform multi-vector search and return results with parent documents."""
        try:
            content_types = content_types or ["text", "image", "table"]
            
            # Initialize multi-vector retriever
            from managers.manager_multivector_retriever import MultiVectorRetriever
            await MultiVectorRetriever.setup()
            retriever = MultiVectorRetriever.get_instance()
            
            # Perform similarity search
            search_results = await retriever.similarity_search(
                query=query,
                k=k,
                content_types=content_types,
                return_parent_documents=return_parent_documents
            )
            
            # Filter by file name if specified
            if filter_by_file:
                filtered_results = []
                for result in search_results:
                    metadata = result.get("metadata", {})
                    file_name = metadata.get("file_name", "")
                    if filter_by_file.lower() in file_name.lower():
                        filtered_results.append(result)
                search_results = filtered_results
            
            # Format results
            return self._format_search_results(
                search_results, query, content_types, 
                return_parent_documents, include_asset_paths
            )
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "multivector_retrieval", None)
            return f"Error performing multi-vector search: {str(e)}"
    
    def _format_search_results(self, results: List[Dict], query: str, 
                              content_types: List[str], return_parent_documents: bool,
                              include_asset_paths: bool) -> str:
        """Format search results for output."""
        if not results:
            return f"No results found for query: '{query}'"
        
        output = []
        output.append(f"Multi-Vector Search Results for: '{query}'")
        output.append(f"Content types: {', '.join(content_types)}")
        output.append(f"Found {len(results)} results")
        output.append("=" * 60)
        
        for i, result in enumerate(results, 1):
            summary = result.get("summary", "")
            score = result.get("summary_score", 0.0)
            metadata = result.get("metadata", {})
            parent_document = result.get("parent_document", "")
            parent_metadata = result.get("parent_metadata")
            
            # Extract key information
            content_type = metadata.get("content_type", "unknown")
            file_name = metadata.get("file_name", "Unknown")
            element_index = metadata.get("element_index", 0)
            
            output.append(f"\nResult {i} (Score: {score:.3f})")
            output.append(f"Type: {content_type.upper()}")
            output.append(f"Source: {file_name}")
            output.append(f"Element Index: {element_index}")
            
            # Add summary
            output.append(f"\nSummary:")
            output.append(f"{summary}")
            
            # Add parent document if available
            if return_parent_documents and parent_document:
                output.append(f"\nOriginal Content:")
                
                # Handle different content types
                if "image" in content_type.lower():
                    output.append(f"Image Content: {parent_document[:200]}...")
                    
                    # Add asset path if available
                    if include_asset_paths and parent_metadata:
                        asset_path = parent_metadata.asset_path
                        if asset_path:
                            output.append(f"Asset Path: {asset_path}")
                
                elif "table" in content_type.lower():
                    output.append(f"Table Content (Markdown):")
                    output.append(f"{parent_document}")
                
                else:  # text content
                    # Limit text length for readability
                    if len(parent_document) > 1000:
                        output.append(f"{parent_document[:1000]}...")
                        output.append(f"[Content truncated - {len(parent_document)} total characters]")
                    else:
                        output.append(f"{parent_document}")
            
            output.append("")  # Empty line between results
        
        return "\n".join(output)

class ParentDocumentRetriever(BaseTool):
    """Tool for retrieving parent documents by their IDs."""
    
    name: str = "parent_document_retriever"
    description: str = """
    Retrieve original parent documents by their document IDs.
    
    This tool:
    - Retrieves full original documents from PostgreSQL storage
    - Returns complete content with metadata
    - Supports batch retrieval of multiple documents
    - Includes asset paths for images
    
    Use this tool when you have document IDs and need the complete original content.
    """
    
    def _run(self, doc_ids: List[str], include_metadata: bool = True) -> str:
        raise NotImplementedError("Use async version")
    
    async def _arun(self, doc_ids: List[str], include_metadata: bool = True) -> str:
        """Retrieve parent documents by their IDs."""
        try:
            # Initialize multi-vector retriever
            from managers.manager_multivector_retriever import MultiVectorRetriever
            await MultiVectorRetriever.setup()
            retriever = MultiVectorRetriever.get_instance()
            
            # Retrieve parent documents
            documents = await retriever.get_parent_documents(doc_ids)
            
            if not documents:
                return f"No documents found for IDs: {doc_ids}"
            
            # Format results
            output = []
            output.append(f"Retrieved {len(documents)} parent documents")
            output.append("=" * 50)
            
            for doc_id, (content, metadata) in documents.items():
                output.append(f"\nDocument ID: {doc_id}")
                output.append(f"Content Type: {metadata.content_type}")
                output.append(f"File: {metadata.file_name}")
                output.append(f"Element Index: {metadata.element_index}")
                
                if include_metadata:
                    output.append(f"Created: {metadata.created_at}")
                    if metadata.summary:
                        output.append(f"Summary: {metadata.summary}")
                    if metadata.asset_path:
                        output.append(f"Asset Path: {metadata.asset_path}")
                
                output.append(f"\nContent:")
                if len(content) > 2000:
                    output.append(f"{content[:2000]}...")
                    output.append(f"[Content truncated - {len(content)} total characters]")
                else:
                    output.append(f"{content}")
                
                output.append("")  # Empty line between documents
            
            return "\n".join(output)
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "parent_document_retriever", None)
            return f"Error retrieving parent documents: {str(e)}"

class DocumentStoreStatsRetriever(BaseTool):
    """Tool for retrieving document store statistics."""
    
    name: str = "document_store_stats"
    description: str = """
    Get statistics about the document store and multi-vector retriever.
    
    This tool provides:
    - Total number of documents stored
    - Document counts by content type
    - Number of unique files and parents
    - Storage utilization information
    
    Use this tool to understand the current state of the document store.
    """
    
    def _run(self) -> str:
        raise NotImplementedError("Use async version")
    
    async def _arun(self) -> str:
        """Get document store statistics."""
        try:
            # Initialize multi-vector retriever
            from managers.manager_multivector_retriever import MultiVectorRetriever
            await MultiVectorRetriever.setup()
            retriever = MultiVectorRetriever.get_instance()
            
            # Get statistics
            stats = await retriever.get_stats()
            
            # Format results
            output = []
            output.append("Document Store Statistics")
            output.append("=" * 30)
            
            doc_stats = stats.get("document_store_stats", {})
            
            output.append(f"Total Documents: {doc_stats.get('total_documents', 0)}")
            output.append(f"Unique Parent Documents: {doc_stats.get('unique_parents', 0)}")
            output.append(f"Unique Files: {doc_stats.get('unique_files', 0)}")
            
            # Content type breakdown
            content_counts = doc_stats.get("content_type_counts", {})
            if content_counts:
                output.append("\nContent Type Distribution:")
                for content_type, count in content_counts.items():
                    output.append(f"  {content_type}: {count}")
            
            output.append(f"\nRetriever Type: {stats.get('retriever_type', 'Unknown')}")
            
            return "\n".join(output)
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "document_store_stats", None)
            return f"Error retrieving statistics: {str(e)}"

# Create tool instances
multivector_retrieval_tool = MultiVectorRetrievalTool()
parent_document_retriever_tool = ParentDocumentRetriever()
document_store_stats_tool = DocumentStoreStatsRetriever()