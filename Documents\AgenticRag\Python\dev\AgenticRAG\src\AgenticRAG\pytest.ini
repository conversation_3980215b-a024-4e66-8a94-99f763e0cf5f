[tool:pytest]
testpaths = tests
python_files = test_*.py *test*.py run_test_real_*.py
python_classes = Test*
python_functions = test_*
asyncio_mode = auto
addopts = --tb=short -v
collect_ignore = []
markers =
    unit: Unit tests for individual components
    integration: Integration tests for multiple components
    performance: Performance and load testing
    health: System health checks
    slow: Tests that take more than 5 seconds
    asyncio: Mark test as async
filterwarnings =
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning