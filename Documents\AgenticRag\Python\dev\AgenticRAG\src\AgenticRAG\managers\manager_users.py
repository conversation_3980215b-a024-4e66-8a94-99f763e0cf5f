from imports import *

from asyncio import Lock as asyncio_Lock
from typing import Dict, Optional, List
import logging
from uuid import uuid4, UUID

from userprofiles.ZairaUser import PERMISSION_LEVELS

class ZairaUserManager:
    if TYPE_CHECKING:
        # This only runs for type checkers, not at runtime — safe to "reach inside"
        from userprofiles.ZairaUser import ZairaUser

    _instance: "ZairaUserManager" = None
    lock: asyncio_Lock = None
    users = Dict[str, "ZairaUser"]

    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    def __init__(self):
        self.users = {}
        self.lock = asyncio_Lock()
        self._lookup_log_cache = set()  # Cache to track already logged user lookups

    @classmethod
    def get_instance(cls) -> "ZairaUserManager":
        if cls._instance is None:
            cls.logger.info("Creating a new singleton instance of ZairaUserManager.")
            cls._instance = cls()
        return cls._instance

    _models_rebuilt = False
    
    @classmethod
    async def _ensure_models_rebuilt(cls):
        """Ensure Pydantic models are rebuilt to resolve forward references"""
        if cls._models_rebuilt:
            return
        
        try:
            # Import all classes that have forward references to ensure they're loaded
            from userprofiles.ZairaUser import ZairaUser
            from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
            from endpoints.mybot_generic import MyBot_Generic, ReplyContext
            from managers.manager_supervisors import SupervisorManager
            
            # Rebuild models to resolve forward references
            ZairaUser.model_rebuild()
            LongRunningZairaRequest.model_rebuild()
            
            cls._models_rebuilt = True
            cls.logger.info("Pydantic models rebuilt successfully to resolve forward references")
            
        except Exception as e:
            cls.logger.error(f"Failed to rebuild Pydantic models: {e}")
            # Don't raise to avoid crashing, but log the error
            print(f"Warning: Failed to rebuild Pydantic models: {e}")

    @classmethod
    async def add_user(cls, username: str, rank: PERMISSION_LEVELS, guid: UUID, device_guid: UUID) -> "ZairaUser":
        self = cls.get_instance()
        async with self.lock:
            if str(guid) in self.users:
                cls.logger.error(f"Attempted to add user with existing GUID: {guid}")
                raise ValueError(f"User with GUID {guid} already exists")
            
            # Ensure Pydantic models are rebuilt before first instantiation
            await cls._ensure_models_rebuilt()
            
            from userprofiles.ZairaUser import ZairaUser
            user = ZairaUser(username, rank, guid, device_guid)
            self.users[str(guid)] = user
            cls.logger.info(f"User added: {username} with GUID: {guid}")
            
            return user
        
    @classmethod
    def create_guid(cls) -> UUID:
        new_guid = uuid4()
        cls.logger.info(f"Generated new User GUID: {str(new_guid)}")
        return new_guid

    @classmethod
    async def remove_user(cls, guid: str) -> bool:
        self = cls.get_instance()
        async with self.lock:
            if guid in self.users:
                del self.users[guid]
                cls.logger.info(f"User removed: GUID {guid}")
                return True
            cls.logger.warning(f"Attempted to remove non-existent user: GUID {guid}")
            return False

    @classmethod
    async def find_user(cls, guid: str) -> Optional["ZairaUser"]:
        self = cls.get_instance()
        async with self.lock:
            # If guid is empty, use SYSTEM user
            if not guid or guid == '':
                from managers.manager_system_user import SystemUserManager
                system_manager = SystemUserManager.get_instance()
                system_user = await system_manager.get_system_user()
                if system_user:
                    cls.logger.info(f"Using SYSTEM user for empty GUID")
                    return system_user
                else:
                    cls.logger.warning(f"SYSTEM user not found for empty GUID")
                    return None
            if not isinstance(guid, str):
                guid = str(guid)
            
            # Check if this is the SYSTEM user GUID
            from managers.manager_system_user import SystemUserManager
            if guid == str(SystemUserManager.SYSTEM_USER_GUID):
                # Only log first lookup or failures/warnings
                if f"SYSTEM:{guid}" not in self._lookup_log_cache:
                    cls.logger.info(f"SYSTEM user GUID requested: {guid}")
                    self._lookup_log_cache.add(f"SYSTEM:{guid}")
                
                # Try to get from users dict first
                user = self.users.get(guid)
                if user:
                    # Only log first successful lookup
                    if f"SYSTEM:found:{guid}" not in self._lookup_log_cache:
                        cls.logger.info(f"SYSTEM user found in users dict")
                        self._lookup_log_cache.add(f"SYSTEM:found:{guid}")
                    return user
                else:
                    cls.logger.warning(f"SYSTEM user not found in users dict, trying SystemUserManager")
                    # Fallback to SystemUserManager
                    system_manager = SystemUserManager.get_instance()
                    system_user = await system_manager.get_system_user()
                    if system_user:
                        cls.logger.info(f"SYSTEM user retrieved from SystemUserManager")
                        return system_user
                    else:
                        cls.logger.error(f"SYSTEM user not found anywhere")
                        return None
            
            user = self.users.get(guid)
            if user:
                # Only log first successful lookup for each user
                if f"user:found:{guid}" not in self._lookup_log_cache:
                    cls.logger.info(f"User found: GUID {guid}")
                    self._lookup_log_cache.add(f"user:found:{guid}")
            else:
                # Always log failures for debugging
                cls.logger.warning(f"User not found: GUID {guid}")
            return user
        
    @classmethod
    async def get_user(cls, username: str) -> Optional["ZairaUser"]:
        self = cls.get_instance()
        async with self.lock:
            for user in self.users.values():
                if user.username == username:
                    # Only log first successful lookup by username
                    if f"user:username:{username}" not in self._lookup_log_cache:
                        cls.logger.info(f"User found by username: {username}")
                        self._lookup_log_cache.add(f"user:username:{username}")
                    return user
            # Always log failures for debugging
            cls.logger.warning(f"User not found by username: {username}")
            return None

    @classmethod
    async def update_user(cls, guid: str, **kwargs) -> Optional["ZairaUser"]:
        self = cls.get_instance()
        async with self.lock:
            user = self.users.get(guid)
            if user:
                for key, value in kwargs.items():
                    if hasattr(user, key):
                        setattr(user, key, value)
                        cls.logger.info(f"Updated {key} for user GUID {guid}")
                    else:
                        cls.logger.warning(f"Attribute {key} does not exist for user GUID {guid}")
                return user
            cls.logger.warning(f"User not found for update: GUID {guid}")
            return None

    @classmethod
    async def list_users(cls) -> List["ZairaUser"]:
        self = cls.get_instance()
        async with self.lock:
            users_list = list(self.users.values())
            cls.logger.info(f"Listed {len(users_list)} users")
            return users_list

    @classmethod
    async def find_users_by_rank(cls, rank: PERMISSION_LEVELS) -> List["ZairaUser"]:
        self = cls.get_instance()
        async with self.lock:
            users_with_rank = [user for user in self.users.values() if user.rank == rank]
            cls.logger.info(f"Found {len(users_with_rank)} users with rank {rank}")
            return users_with_rank

    @classmethod
    async def user_exists(cls, guid: str) -> bool:
        self = cls.get_instance()
        async with self.lock:
            exists = guid in self.users
            cls.logger.info(f"User existence check: GUID {guid} - {'exists' if exists else 'does not exist'}")
            return exists

    @classmethod
    async def find_user_by_login(cls, login_string: str) -> Optional["ZairaUser"]:
        """
        Parse username@domain format and find matching ZairaUser.
        Returns None if format invalid or domain doesn't match ZAIRA_NETWORK_NAME.
        """
        if '@' not in login_string:
            cls.logger.warning(f"Invalid login format (missing @): {login_string}")
            return None
        
        try:
            username, domain = login_string.split('@', 1)
        except ValueError:
            cls.logger.warning(f"Invalid login format (malformed): {login_string}")
            return None
        
        # Validate domain matches ZAIRA_NETWORK_NAME
        from etc.helper_functions import get_value_from_env
        expected_domain = get_value_from_env("ZAIRA_NETWORK_NAME", "")
        if domain != expected_domain:
            cls.logger.warning(f"Domain mismatch: got '{domain}', expected '{expected_domain}'")
            return None
        
        # Special handling for SYSTEM user
        if username == "SYSTEM":
            cls.logger.info(f"SYSTEM user login detected: {login_string}")
            from managers.manager_system_user import SystemUserManager
            system_manager = SystemUserManager.get_instance()
            system_user = await system_manager.get_system_user()
            if system_user:
                cls.logger.info(f"SYSTEM user retrieved for login: {login_string}")
                return system_user
            else:
                cls.logger.error(f"SYSTEM user not found during login: {login_string}")
                return None
        
        # Find regular user by username
        user = await cls.get_user(username)
        if user:
            cls.logger.info(f"User found by login: {login_string}")
        else:
            cls.logger.warning(f"User not found by login: {login_string}")
        
        return user
        
    @classmethod
    async def authenticate_user(cls, login_string: str, password: str) -> Optional["ZairaUser"]:
        """
        Complete authentication flow:
        1. Parse username@domain format
        2. Validate domain matches ZAIRA_NETWORK_NAME
        3. Find ZairaUser by username
        4. Verify password using existing MD5 algorithm
        5. Return user object or None
        """
        # Parse and find user
        user = await cls.find_user_by_login(login_string)
        if not user:
            # Extract username for user creation if needed
            try:
                username, domain = login_string.split('@', 1)
                # Validate domain matches ZAIRA_NETWORK_NAME
                from etc.helper_functions import get_value_from_env
                expected_domain = get_value_from_env("ZAIRA_NETWORK_NAME", "")
                if domain != expected_domain:
                    cls.logger.warning(f"Domain mismatch: got '{domain}', expected '{expected_domain}'")
                    return None
                
                # Don't auto-create SYSTEM user here - should exist already
                if username == "SYSTEM":
                    cls.logger.error(f"SYSTEM user should already exist but was not found")
                    return None
                
                # Temporarily create a new user if it's the first message since the server was started
                # This needs replaced with a database check
                user = await ZairaUserManager.add_user(username, PERMISSION_LEVELS.USER, ZairaUserManager.create_guid(), ZairaUserManager.create_guid())
                cls.logger.info(f"Auto-created new user: {username}")
                
            except ValueError:
                cls.logger.warning(f"Invalid login format (malformed): {login_string}")
                return None
        
        # Extract username for password verification
        username = login_string.split('@')[0]
        
        # Verify password using existing algorithm
        from etc.helper_functions import get_password
        from managers.manager_logfire import LogFire
        
        # Debug logging for authentication troubleshooting
        LogFire.log("DEBUG", f"Auth Debug - Login string: {login_string}", severity="debug")
        LogFire.log("DEBUG", f"Auth Debug - Extracted username: {username}", severity="debug")
        
        expected_password = get_password(login_string)
        LogFire.log("DEBUG", f"Auth Debug - Expected password: {expected_password}", severity="debug")
        LogFire.log("DEBUG", f"Auth Debug - Provided password: {password}", severity="debug")
        LogFire.log("DEBUG", f"Auth Debug - Passwords match: {password == expected_password}", severity="debug")
        
        if password != expected_password:
            cls.logger.warning(f"Password mismatch for user: {username}")
            LogFire.log("DEBUG", f"Auth Debug - AUTHENTICATION FAILED due to password mismatch", severity="debug")
            return None
        
        cls.logger.info(f"User authenticated successfully: {username}")
        return user
