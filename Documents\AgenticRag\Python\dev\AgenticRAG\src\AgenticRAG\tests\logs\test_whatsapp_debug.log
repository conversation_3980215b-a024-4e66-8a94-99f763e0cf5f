=== DEBUG TRACE STARTED at 2025-08-18 16:06:19.629918 ===
[2025-08-18 16:06:21.077] WhatsApp Message Sending Debug Tool
[2025-08-18 16:06:21.077] ============================================================
[2025-08-18 16:06:21.077] Testing WhatsApp API Configuration:
[2025-08-18 16:06:21.077] ==================================================
[2025-08-18 16:06:21.078] Phone Number ID: 781254471731936
[2025-08-18 16:06:21.078] Access Token: ********************...Vh6YSAZDZD
[2025-08-18 16:06:21.078] Recipient Number: +***********
[2025-08-18 16:06:21.078] [OK] All required credentials are present

[2025-08-18 16:06:21.087] Testing OAuth WhatsApp configuration:
[2025-08-18 16:06:21.087] ==================================================
[2025-08-18 16:06:25.740] [ERROR] Exception during OAuth test: DLL load failed while importing onnxruntime_pybind11_state: A dynamic link library (DLL) initialization routine failed.
[2025-08-18 16:06:25.810] Traceback (most recent call last):
[2025-08-18 16:06:25.811]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\test_whatsapp_debug.py", line 151, in test_oauth_configuration
[2025-08-18 16:06:25.811]     verifier.instantiate()
[2025-08-18 16:06:25.811]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\oauth\_verifier_.py", line 131, in instantiate
[2025-08-18 16:06:25.811]     from endpoints.oauth.imap import OAuth2IMAP
[2025-08-18 16:06:25.811]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\oauth\imap.py", line 6, in <module>
[2025-08-18 16:06:25.811]     from managers.manager_supervisors import SupervisorTaskState
[2025-08-18 16:06:25.811]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\managers\manager_supervisors.py", line 34, in <module>
[2025-08-18 16:06:25.811]     from managers.manager_users import ZairaUserManager
[2025-08-18 16:06:25.811]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\managers\manager_users.py", line 8, in <module>
[2025-08-18 16:06:25.811]     from userprofiles.ZairaUser import PERMISSION_LEVELS
[2025-08-18 16:06:25.812]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\__init__.py", line 4, in <module>
[2025-08-18 16:06:25.812]     from .ZairaUser import ZairaUser
[2025-08-18 16:06:25.812]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\ZairaUser.py", line 14, in <module>
[2025-08-18 16:06:25.812]     from managers.manager_multimodal import MultimodalManager
[2025-08-18 16:06:25.812]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\managers\manager_multimodal.py", line 10, in <module>
[2025-08-18 16:06:25.812]     from unstructured.partition.pdf import partition_pdf
[2025-08-18 16:06:25.812]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured\partition\pdf.py", line 19, in <module>
[2025-08-18 16:06:25.812]     from unstructured_inference.inference.layout import DocumentLayout
[2025-08-18 16:06:25.812]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured_inference\inference\layout.py", line 18, in <module>
[2025-08-18 16:06:25.812]     from unstructured_inference.models.base import get_model
[2025-08-18 16:06:25.812]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured_inference\models\base.py", line 7, in <module>
[2025-08-18 16:06:25.812]     from unstructured_inference.models.detectron2onnx import (
[2025-08-18 16:06:25.812]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured_inference\models\detectron2onnx.py", line 6, in <module>
[2025-08-18 16:06:25.812]     import onnxruntime
[2025-08-18 16:06:25.812]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\onnxruntime\__init__.py", line 61, in <module>
[2025-08-18 16:06:25.812]     raise import_capi_exception
[2025-08-18 16:06:25.812]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\onnxruntime\__init__.py", line 24, in <module>
[2025-08-18 16:06:25.812]     from onnxruntime.capi._pybind_state import (
[2025-08-18 16:06:25.812]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\onnxruntime\capi\_pybind_state.py", line 32, in <module>
[2025-08-18 16:06:25.812]     from .onnxruntime_pybind11_state import *  # noqa
[2025-08-18 16:06:25.812]     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-08-18 16:06:25.812] ImportError: DLL load failed while importing onnxruntime_pybind11_state: A dynamic link library (DLL) initialization routine failed.

[2025-08-18 16:06:25.813] Test 3: Direct API call with config.py recipient (+***********)

[2025-08-18 16:06:25.813] Testing direct WhatsApp API call to: +***********
[2025-08-18 16:06:25.813] ==================================================
[2025-08-18 16:06:25.813] API URL: https://graph.facebook.com/v18.0/781254471731936/messages
[2025-08-18 16:06:25.813] Payload: {
[2025-08-18 16:06:25.813]   "messaging_product": "whatsapp",
[2025-08-18 16:06:25.813]   "recipient_type": "individual",
[2025-08-18 16:06:25.813]   "to": "+***********",
[2025-08-18 16:06:25.813]   "type": "text",
[2025-08-18 16:06:25.813]   "text": {
[2025-08-18 16:06:25.813]     "preview_url": false,
[2025-08-18 16:06:25.813]     "body": "Test message from Zaira - API connectivity check"
[2025-08-18 16:06:25.813]   }
[2025-08-18 16:06:25.813] }
[2025-08-18 16:06:25.813] Headers: Content-Type and Authorization (masked)

[2025-08-18 16:06:26.274] API Response:
[2025-08-18 16:06:26.274] Status Code: 400
[2025-08-18 16:06:26.274] Response Body: {"error":{"message":"The account is not registered","type":"OAuthException","code":133010,"error_subcode":2593006,"is_transient":false,"error_user_title":"Account Not Exist","error_user_msg":"Account does not exist in Cloud API, please use \/register API to create an account first.","fbtrace_id":"ADMhS1mrCKHSMp-UQyaZ-p8"}}
[2025-08-18 16:06:26.274] [ERROR] WhatsApp API call failed
[2025-08-18 16:06:26.274] Error Message: The account is not registered
[2025-08-18 16:06:26.274] Error Code: 133010
[2025-08-18 16:06:26.274] Error Type: OAuthException

[2025-08-18 16:06:26.275] Testing bot send_a_whatsapp_message function:
[2025-08-18 16:06:26.275] ==================================================
[2025-08-18 16:06:33.990] [ERROR] Exception during bot send: fastembed not installed, use pip install fastembed
[2025-08-18 16:06:34.043] Traceback (most recent call last):
[2025-08-18 16:06:34.043]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\agno\embedder\fastembed.py", line 8, in <module>
[2025-08-18 16:06:34.043]     from fastembed import TextEmbedding  # type: ignore
[2025-08-18 16:06:34.043]     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-08-18 16:06:34.043]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\fastembed\__init__.py", line 3, in <module>
[2025-08-18 16:06:34.043]     from fastembed.image import ImageEmbedding
[2025-08-18 16:06:34.043]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\fastembed\image\__init__.py", line 1, in <module>
[2025-08-18 16:06:34.043]     from fastembed.image.image_embedding import ImageEmbedding
[2025-08-18 16:06:34.043]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\fastembed\image\image_embedding.py", line 7, in <module>
[2025-08-18 16:06:34.043]     from fastembed.image.onnx_embedding import OnnxImageEmbedding
[2025-08-18 16:06:34.043]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\fastembed\image\onnx_embedding.py", line 6, in <module>
[2025-08-18 16:06:34.043]     from fastembed.common.onnx_model import OnnxOutputContext
[2025-08-18 16:06:34.043]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\fastembed\common\onnx_model.py", line 7, in <module>
[2025-08-18 16:06:34.043]     import onnxruntime as ort
[2025-08-18 16:06:34.043]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\onnxruntime\__init__.py", line 61, in <module>
[2025-08-18 16:06:34.043]     raise import_capi_exception
[2025-08-18 16:06:34.043]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\onnxruntime\__init__.py", line 24, in <module>
[2025-08-18 16:06:34.043]     from onnxruntime.capi._pybind_state import (
[2025-08-18 16:06:34.043]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\onnxruntime\capi\_pybind_state.py", line 32, in <module>
[2025-08-18 16:06:34.043]     from .onnxruntime_pybind11_state import *  # noqa
[2025-08-18 16:06:34.043]     ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
[2025-08-18 16:06:34.044] ImportError: DLL load failed while importing onnxruntime_pybind11_state: A dynamic link library (DLL) initialization routine failed.

[2025-08-18 16:06:34.044] During handling of the above exception, another exception occurred:

[2025-08-18 16:06:34.044] Traceback (most recent call last):
[2025-08-18 16:06:34.044]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\test_whatsapp_debug.py", line 118, in test_bot_send_function
[2025-08-18 16:06:34.044]     from endpoints.whatsapp_endpoint import MyWhatsappBot
[2025-08-18 16:06:34.045]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\whatsapp_endpoint.py", line 12, in <module>
[2025-08-18 16:06:34.045]     from endpoints.api_endpoint import APIEndpoint
[2025-08-18 16:06:34.045]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\endpoints\api_endpoint.py", line 16, in <module>
[2025-08-18 16:06:34.045]     from agno.embedder.fastembed import FastEmbedEmbedder
[2025-08-18 16:06:34.045]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\agno\embedder\fastembed.py", line 11, in <module>
[2025-08-18 16:06:34.045]     raise ImportError("fastembed not installed, use pip install fastembed")
[2025-08-18 16:06:34.045] ImportError: fastembed not installed, use pip install fastembed

[2025-08-18 16:06:34.045] ============================================================
[2025-08-18 16:06:34.045] Debug Summary:
[2025-08-18 16:06:34.045] 1. Check your phone for any test messages
[2025-08-18 16:06:34.045] 2. If no messages received, check the API response errors above
[2025-08-18 16:06:34.045] 3. Common issues:
[2025-08-18 16:06:34.045]    - Invalid access token (expired or wrong)
[2025-08-18 16:06:34.045]    - Phone number format (should include country code like +***********)
[2025-08-18 16:06:34.045]    - WhatsApp Business Account not properly verified
[2025-08-18 16:06:34.045]    - Phone Number ID doesn't match your WhatsApp Business number
