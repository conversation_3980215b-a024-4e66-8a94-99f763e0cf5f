from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from uuid import uuid4
import asyncio
from datetime import datetime, timezone, timedelta

from managers.scheduled_requests.security.validator import ScheduledRequestSecurityValidator
from managers.scheduled_requests.utils.exceptions import (
    SecurityValidationError, UnauthorizedScheduledRequestError
)
from userprofiles.ZairaUser import <PERSON>airaUser, PERMISSION_LEVELS
from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest, ScheduleType

class TestScheduledRequestSecurityValidator:
    """Comprehensive test class for ScheduledRequestSecurityValidator"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.validator = ScheduledRequestSecurityValidator()
        
        self.test_user_guid = uuid4()
        self.test_scheduled_guid = str(uuid4())
        
        # Create test user
        self.test_user = Z<PERSON>aUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=self.test_user_guid,
            device_guid=uuid4()
        )
        
        # Create test admin user
        self.test_admin_user = ZairaUser(
            username="admin_user",
            rank=PERMISSION_LEVELS.ADMIN,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        # Create test scheduled request
        self.test_scheduled_request = Mock(spec=ScheduledZairaRequest)
        self.test_scheduled_request.user = self.test_user
        self.test_scheduled_request.scheduled_guid = uuid4()
        self.test_scheduled_request.schedule_prompt = "Test prompt"
        self.test_scheduled_request.target_prompt = "Test target"
        self.test_scheduled_request.delay_seconds = 300
        self.test_scheduled_request.start_delay_seconds = 60
        self.test_scheduled_request.next_execution = datetime.now(timezone.utc) + timedelta(hours=1)
        self.test_scheduled_request.schedule_type = Mock()
        self.test_scheduled_request.schedule_type.value = "once"
    
    def teardown_method(self):
        """Clean up after each test"""
        pass
    
    def test_validator_initialization(self):
        """Test validator initialization"""
        validator = ScheduledRequestSecurityValidator()
        
        assert hasattr(validator, '_blocked_patterns')
        assert hasattr(validator, '_compiled_patterns')
        assert hasattr(validator, '_failed_attempts')
        assert hasattr(validator, '_blocked_ips')
        
        assert len(validator._blocked_patterns) > 0
        assert len(validator._compiled_patterns) == len(validator._blocked_patterns)
        assert isinstance(validator._failed_attempts, dict)
        assert isinstance(validator._blocked_ips, dict)
    
    @pytest.mark.asyncio
    async def test_validate_user_authorization_success(self):
        """Test successful user authorization validation"""
        with patch.object(self.validator, '_validate_user_account_status', return_value=True), \
             patch.object(self.validator, '_validate_create_authorization', return_value=True):
            
            result = await self.validator.validate_user_authorization(
                self.test_user, "create", self.test_scheduled_guid
            )
            
            assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_user_authorization_invalid_guid(self):
        """Test user authorization with invalid GUID"""
        # Create user with invalid GUID format
        invalid_user = Mock(spec=ZairaUser)
        invalid_user.GUID = "invalid-guid-format"
        
        with pytest.raises(SecurityValidationError) as excinfo:
            await self.validator.validate_user_authorization(invalid_user, "create")
        
        assert "Invalid user GUID format" in str(excinfo.value)
    
    @pytest.mark.asyncio
    async def test_validate_user_authorization_inactive_account(self):
        """Test user authorization with inactive account"""
        with patch.object(self.validator, '_validate_user_account_status', return_value=False):
            
            with pytest.raises(UnauthorizedScheduledRequestError):
                await self.validator.validate_user_authorization(self.test_user, "create")
    
    @pytest.mark.asyncio
    async def test_validate_user_authorization_unknown_operation(self):
        """Test user authorization with unknown operation"""
        with patch.object(self.validator, '_validate_user_account_status', return_value=True):
            
            with pytest.raises(SecurityValidationError) as excinfo:
                await self.validator.validate_user_authorization(self.test_user, "unknown_operation")
            
            assert "Unknown operation: unknown_operation" in str(excinfo.value)
    
    @pytest.mark.asyncio
    async def test_validate_request_content_success(self):
        """Test successful request content validation"""
        result = await self.validator.validate_request_content(self.test_scheduled_request)
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_request_content_malicious_schedule_prompt(self):
        """Test request content validation with malicious schedule prompt"""
        malicious_request = Mock(spec=ScheduledZairaRequest)
        malicious_request.user = self.test_user
        malicious_request.schedule_prompt = "<script>alert('xss')</script>"
        malicious_request.target_prompt = "Safe prompt"
        malicious_request.delay_seconds = 300
        malicious_request.start_delay_seconds = 60
        malicious_request.next_execution = datetime.now(timezone.utc) + timedelta(hours=1)
        malicious_request.schedule_type = Mock()
        malicious_request.schedule_type.value = "once"
        
        with pytest.raises(SecurityValidationError) as excinfo:
            await self.validator.validate_request_content(malicious_request)
        
        assert "malicious_schedule_prompt" in str(excinfo.value)
    
    @pytest.mark.asyncio
    async def test_validate_request_content_malicious_target_prompt(self):
        """Test request content validation with malicious target prompt"""
        malicious_request = Mock(spec=ScheduledZairaRequest)
        malicious_request.user = self.test_user
        malicious_request.schedule_prompt = "Safe prompt"
        malicious_request.target_prompt = "javascript:alert('xss')"
        malicious_request.delay_seconds = 300
        malicious_request.start_delay_seconds = 60
        malicious_request.next_execution = datetime.now(timezone.utc) + timedelta(hours=1)
        malicious_request.schedule_type = Mock()
        malicious_request.schedule_type.value = "once"
        
        with pytest.raises(SecurityValidationError) as excinfo:
            await self.validator.validate_request_content(malicious_request)
        
        assert "malicious_target_prompt" in str(excinfo.value)
    
    @pytest.mark.asyncio
    async def test_validate_request_content_invalid_timing(self):
        """Test request content validation with invalid timing"""
        invalid_request = Mock(spec=ScheduledZairaRequest)
        invalid_request.user = self.test_user
        invalid_request.schedule_prompt = "Safe prompt"
        invalid_request.target_prompt = "Safe target"
        invalid_request.delay_seconds = -100  # Invalid negative delay
        invalid_request.start_delay_seconds = 60
        invalid_request.next_execution = datetime.now(timezone.utc) + timedelta(hours=1)
        invalid_request.schedule_type = Mock()
        invalid_request.schedule_type.value = "once"
        
        with pytest.raises(SecurityValidationError) as excinfo:
            await self.validator.validate_request_content(invalid_request)
        
        assert "invalid_timing" in str(excinfo.value)
    
    @pytest.mark.asyncio
    async def test_validate_ip_access_success(self):
        """Test successful IP access validation"""
        valid_ip = "***********"
        user_guid = str(self.test_user_guid)
        
        result = await self.validator.validate_ip_access(valid_ip, user_guid)
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_ip_access_invalid_format(self):
        """Test IP access validation with invalid IP format"""
        invalid_ip = "not.an.ip.address"
        user_guid = str(self.test_user_guid)
        
        with pytest.raises(SecurityValidationError) as excinfo:
            await self.validator.validate_ip_access(invalid_ip, user_guid)
        
        assert "Invalid IP format" in str(excinfo.value)
    
    @pytest.mark.asyncio
    async def test_validate_ip_access_blocked_ip(self):
        """Test IP access validation with blocked IP"""
        blocked_ip = "***********00"
        user_guid = str(self.test_user_guid)
        
        # Block the IP temporarily
        self.validator._blocked_ips[blocked_ip] = datetime.now(timezone.utc).timestamp() + 3600
        
        with pytest.raises(SecurityValidationError) as excinfo:
            await self.validator.validate_ip_access(blocked_ip, user_guid)
        
        assert "is temporarily blocked" in str(excinfo.value)
    
    @pytest.mark.asyncio
    async def test_validate_ip_access_rate_limit_exceeded(self):
        """Test IP access validation with rate limit exceeded"""
        test_ip = "*************"
        user_guid = str(self.test_user_guid)
        
        # Simulate rate limit exceeded by filling the attempts list
        current_time = datetime.now(timezone.utc).timestamp()
        self.validator._failed_attempts[test_ip] = [current_time - i for i in range(101)]  # 101 attempts
        
        with pytest.raises(SecurityValidationError) as excinfo:
            await self.validator.validate_ip_access(test_ip, user_guid)
        
        assert "Rate limit exceeded" in str(excinfo.value)
        # IP should be blocked after rate limit exceeded
        assert test_ip in self.validator._blocked_ips
    
    @pytest.mark.asyncio
    async def test_validate_request_ownership_success(self):
        """Test successful request ownership validation"""
        user_guid = str(self.test_user_guid)
        scheduled_guid = self.test_scheduled_guid
        
        mock_requests = [
            {'scheduled_guid': scheduled_guid, 'user_guid': user_guid},
            {'scheduled_guid': str(uuid4()), 'user_guid': user_guid}
        ]
        
        with patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager') as mock_persistence_class:
            mock_persistence = Mock()
            mock_persistence.get_active_requests.return_value = mock_requests
            mock_persistence_class.get_instance.return_value = mock_persistence
            
            result = await self.validator.validate_request_ownership(user_guid, scheduled_guid)
            
            assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_request_ownership_unauthorized(self):
        """Test request ownership validation when user doesn't own request"""
        user_guid = str(self.test_user_guid)
        scheduled_guid = self.test_scheduled_guid
        
        # Mock empty request list (user doesn't own any requests)
        mock_requests = []
        
        with patch('managers.scheduled_requests.core.persistence.ScheduledRequestPersistenceManager') as mock_persistence_class:
            mock_persistence = Mock()
            mock_persistence.get_active_requests.return_value = mock_requests
            mock_persistence_class.get_instance.return_value = mock_persistence
            
            with pytest.raises(UnauthorizedScheduledRequestError):
                await self.validator.validate_request_ownership(user_guid, scheduled_guid)
    
    @pytest.mark.asyncio
    async def test_sanitize_request_data_success(self):
        """Test successful request data sanitization"""
        raw_data = {
            'schedule_prompt': 'Test prompt with <script>bad</script> content',
            'target_prompt': 'Clean target prompt',
            'delay_seconds': '300',
            'start_delay_seconds': 60,
            'is_active': 'true',
            'user_guid': str(self.test_user_guid),
            'scheduled_guid': str(uuid4()),
            'schedule_type': 'once',
            'cancellation_reason': 'User requested'
        }
        
        sanitized = await self.validator.sanitize_request_data(raw_data)
        
        assert 'schedule_prompt' in sanitized
        assert '<script>' not in sanitized['schedule_prompt']  # Should be sanitized
        assert sanitized['target_prompt'] == 'Clean target prompt'
        assert sanitized['delay_seconds'] == 300.0
        assert sanitized['start_delay_seconds'] == 60.0
        assert sanitized['is_active'] is True
        assert sanitized['user_guid'] == str(self.test_user_guid)
        assert 'schedule_type' in sanitized
    
    @pytest.mark.asyncio
    async def test_sanitize_request_data_extreme_values(self):
        """Test request data sanitization with extreme values"""
        raw_data = {
            'delay_seconds': '999999999',  # Extremely large value
            'start_delay_seconds': -100,   # Negative value
        }
        
        sanitized = await self.validator.sanitize_request_data(raw_data)
        
        # Should be clamped to reasonable limits
        assert sanitized['delay_seconds'] == 86400 * 7  # Max 1 week
        assert sanitized['start_delay_seconds'] == 0.0  # Min 0
    
    def test_generate_audit_hash(self):
        """Test audit hash generation"""
        user_guid = str(self.test_user_guid)
        operation = "create"
        data = {'prompt': 'test', 'delay': 300}
        
        hash1 = self.validator.generate_audit_hash(user_guid, operation, data)
        hash2 = self.validator.generate_audit_hash(user_guid, operation, data)
        
        # Same input should produce same hash
        assert hash1 == hash2
        assert len(hash1) == 16  # Should be truncated to 16 chars
        
        # Different data should produce different hash
        different_data = {'prompt': 'different', 'delay': 600}
        hash3 = self.validator.generate_audit_hash(user_guid, operation, different_data)
        assert hash1 != hash3
    
    @pytest.mark.asyncio
    async def test_validate_user_account_status_valid(self):
        """Test user account status validation with valid user"""
        result = await self.validator._validate_user_account_status(self.test_user)
        
        assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_user_account_status_invalid(self):
        """Test user account status validation with invalid user"""
        # Test with None user
        result = await self.validator._validate_user_account_status(None)
        assert result is False
        
        # Test with user without user_guid
        invalid_user = Mock()
        delattr(invalid_user, 'user_guid') if hasattr(invalid_user, 'user_guid') else None
        result = await self.validator._validate_user_account_status(invalid_user)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_validate_create_authorization_success(self):
        """Test create authorization validation success"""
        result = await self.validator._validate_create_authorization(self.test_user)
        assert result is True
        
        result = await self.validator._validate_create_authorization(self.test_admin_user)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_create_authorization_no_rank(self):
        """Test create authorization with user without rank"""
        user_no_rank = Mock(spec=ZairaUser)
        user_no_rank.GUID = self.test_user_guid
        # Don't set rank attribute
        
        result = await self.validator._validate_create_authorization(user_no_rank)
        assert result is True  # Default allow
    
    @pytest.mark.asyncio
    async def test_validate_read_authorization_with_specific_request(self):
        """Test read authorization for specific request"""
        user_guid = str(self.test_user_guid)
        scheduled_guid = self.test_scheduled_guid
        
        with patch.object(self.validator, 'validate_request_ownership', return_value=True):
            result = await self.validator._validate_read_authorization(self.test_user, scheduled_guid)
            assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_read_authorization_general_access(self):
        """Test read authorization for general access"""
        result = await self.validator._validate_read_authorization(self.test_user)
        assert result is True  # All authenticated users can read their own data
    
    @pytest.mark.asyncio
    async def test_validate_modify_authorization_success(self):
        """Test modify authorization validation success"""
        scheduled_guid = self.test_scheduled_guid
        
        with patch.object(self.validator, 'validate_request_ownership', return_value=True):
            result = await self.validator._validate_modify_authorization(self.test_user, scheduled_guid)
            assert result is True
    
    @pytest.mark.asyncio
    async def test_validate_modify_authorization_no_guid(self):
        """Test modify authorization without scheduled GUID"""
        result = await self.validator._validate_modify_authorization(self.test_user, None)
        assert result is False
    
    def test_validate_prompt_content_success(self):
        """Test prompt content validation success"""
        safe_prompt = "This is a safe prompt with normal text"
        
        result = self.validator._validate_prompt_content(safe_prompt)
        assert result is True
    
    def test_validate_prompt_content_malicious_patterns(self):
        """Test prompt content validation with malicious patterns"""
        malicious_prompts = [
            "<script>alert('xss')</script>",
            "javascript:alert('xss')",
            "data:text/html;base64,PHNjcmlwdD5hbGVydCgieHNzIik8L3NjcmlwdD4=",
            "eval('malicious code')",
            "exec('malicious code')",
            "__import__('os').system('rm -rf /')",
            "subprocess.call(['rm', '-rf', '/'])",
            "os.system('malicious command')"
        ]
        
        for prompt in malicious_prompts:
            result = self.validator._validate_prompt_content(prompt)
            assert result is False, f"Should reject malicious prompt: {prompt}"
    
    def test_validate_prompt_content_too_long(self):
        """Test prompt content validation with overly long prompt"""
        long_prompt = "A" * 10001  # Exceeds 10KB limit
        
        result = self.validator._validate_prompt_content(long_prompt)
        assert result is False
    
    def test_validate_prompt_content_non_ascii(self):
        """Test prompt content validation with non-ASCII characters"""
        unicode_prompt = "This prompt has unicode characters: ñáéíóú"
        
        result = self.validator._validate_prompt_content(unicode_prompt)
        assert result is False  # Should reject non-ASCII as per project standards
    
    def test_validate_prompt_content_invalid_input(self):
        """Test prompt content validation with invalid input"""
        # Test with None
        result = self.validator._validate_prompt_content(None)
        assert result is False
        
        # Test with non-string
        result = self.validator._validate_prompt_content(123)
        assert result is False
        
        # Test with empty string
        result = self.validator._validate_prompt_content("")
        assert result is False
    
    def test_validate_timing_parameters_success(self):
        """Test timing parameters validation success"""
        result = self.validator._validate_timing_parameters(self.test_scheduled_request)
        assert result is True
    
    def test_validate_timing_parameters_invalid_delays(self):
        """Test timing parameters validation with invalid delays"""
        # Test negative delay
        invalid_request = Mock(spec=ScheduledZairaRequest)
        invalid_request.delay_seconds = -100
        invalid_request.start_delay_seconds = 60
        invalid_request.next_execution = datetime.now(timezone.utc) + timedelta(hours=1)
        
        result = self.validator._validate_timing_parameters(invalid_request)
        assert result is False
        
        # Test excessive delay (more than 1 week)
        invalid_request.delay_seconds = 86400 * 8  # 8 days
        invalid_request.start_delay_seconds = 60
        
        result = self.validator._validate_timing_parameters(invalid_request)
        assert result is False
    
    def test_validate_timing_parameters_future_execution(self):
        """Test timing parameters validation with far future execution"""
        invalid_request = Mock(spec=ScheduledZairaRequest)
        invalid_request.delay_seconds = 300
        invalid_request.start_delay_seconds = 60
        # Set execution more than 1 year in future
        invalid_request.next_execution = datetime.now(timezone.utc) + timedelta(days=400)
        
        result = self.validator._validate_timing_parameters(invalid_request)
        assert result is False
    
    def test_validate_schedule_constraints_success(self):
        """Test schedule constraints validation success"""
        result = self.validator._validate_schedule_constraints(self.test_scheduled_request)
        assert result is True
    
    def test_validate_schedule_constraints_recurring_too_frequent(self):
        """Test schedule constraints with recurring task too frequent"""
        recurring_request = Mock(spec=ScheduledZairaRequest)
        recurring_request.user = self.test_user  # USER rank
        recurring_request.schedule_type = Mock()
        recurring_request.schedule_type.value = "recurring"
        recurring_request.delay_seconds = 60  # 1 minute (too frequent for USER rank)
        
        result = self.validator._validate_schedule_constraints(recurring_request)
        assert result is False
    
    def test_validate_schedule_constraints_admin_frequent_allowed(self):
        """Test schedule constraints allows frequent recurring for admin"""
        recurring_request = Mock(spec=ScheduledZairaRequest)
        recurring_request.user = self.test_admin_user  # ADMIN rank
        recurring_request.schedule_type = Mock()
        recurring_request.schedule_type.value = "recurring"
        recurring_request.delay_seconds = 60  # 1 minute (allowed for ADMIN)
        
        result = self.validator._validate_schedule_constraints(recurring_request)
        assert result is True
    
    def test_validate_ip_format_valid(self):
        """Test IP format validation with valid IPs"""
        valid_ips = [
            "***********",
            "********",
            "**********",
            "*******",
            "::1",
            "2001:db8::1"
        ]
        
        for ip in valid_ips:
            result = self.validator._validate_ip_format(ip)
            assert result is True, f"Should accept valid IP: {ip}"
    
    def test_validate_ip_format_invalid(self):
        """Test IP format validation with invalid IPs"""
        invalid_ips = [
            "not.an.ip",
            "256.256.256.256",
            "192.168.1",
            "***********.1",
            "",
            None
        ]
        
        for ip in invalid_ips:
            result = self.validator._validate_ip_format(ip)
            assert result is False, f"Should reject invalid IP: {ip}"
    
    @pytest.mark.asyncio
    async def test_is_ip_blocked_not_blocked(self):
        """Test IP blocked check when IP is not blocked"""
        test_ip = "***********"
        
        result = await self.validator._is_ip_blocked(test_ip)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_is_ip_blocked_currently_blocked(self):
        """Test IP blocked check when IP is currently blocked"""
        test_ip = "***********"
        future_time = datetime.now(timezone.utc).timestamp() + 3600  # 1 hour from now
        self.validator._blocked_ips[test_ip] = future_time
        
        result = await self.validator._is_ip_blocked(test_ip)
        assert result is True
    
    @pytest.mark.asyncio
    async def test_is_ip_blocked_expired_block(self):
        """Test IP blocked check when block has expired"""
        test_ip = "***********"
        past_time = datetime.now(timezone.utc).timestamp() - 3600  # 1 hour ago
        self.validator._blocked_ips[test_ip] = past_time
        
        result = await self.validator._is_ip_blocked(test_ip)
        assert result is False
        # Expired block should be removed
        assert test_ip not in self.validator._blocked_ips
    
    @pytest.mark.asyncio
    async def test_check_ip_rate_limit_under_limit(self):
        """Test IP rate limit check when under limit"""
        test_ip = "***********"
        
        result = await self.validator._check_ip_rate_limit(test_ip)
        assert result is True
        
        # Should track the attempt
        assert test_ip in self.validator._failed_attempts
        assert len(self.validator._failed_attempts[test_ip]) == 1
    
    @pytest.mark.asyncio
    async def test_check_ip_rate_limit_over_limit(self):
        """Test IP rate limit check when over limit"""
        test_ip = "***********"
        current_time = datetime.now(timezone.utc).timestamp()
        
        # Fill with max requests (100)
        self.validator._failed_attempts[test_ip] = [current_time - i for i in range(100)]
        
        result = await self.validator._check_ip_rate_limit(test_ip)
        assert result is False
    
    @pytest.mark.asyncio
    async def test_check_ip_rate_limit_old_attempts_cleaned(self):
        """Test IP rate limit check cleans old attempts"""
        test_ip = "***********"
        current_time = datetime.now(timezone.utc).timestamp()
        old_time = current_time - 400  # 6+ minutes ago (outside 5-minute window)
        
        # Add old attempts that should be cleaned
        self.validator._failed_attempts[test_ip] = [old_time - i for i in range(50)]
        
        result = await self.validator._check_ip_rate_limit(test_ip)
        assert result is True
        
        # Old attempts should be cleaned, only new one should remain
        assert len(self.validator._failed_attempts[test_ip]) == 1
    
    @pytest.mark.asyncio
    async def test_block_ip_temporarily(self):
        """Test temporary IP blocking"""
        test_ip = "***********"
        
        await self.validator._block_ip_temporarily(test_ip)
        
        assert test_ip in self.validator._blocked_ips
        # Should be blocked for the configured duration
        unblock_time = self.validator._blocked_ips[test_ip]
        current_time = datetime.now(timezone.utc).timestamp()
        assert unblock_time > current_time
    
    def test_get_security_metrics(self):
        """Test getting security metrics"""
        # Add some test data
        current_time = datetime.now(timezone.utc).timestamp()
        
        # Add active block
        self.validator._blocked_ips["***********"] = current_time + 3600
        # Add expired block
        self.validator._blocked_ips["***********"] = current_time - 3600
        
        # Add recent attempts
        self.validator._failed_attempts["***********"] = [current_time - 100, current_time - 200]
        # Add old attempts
        self.validator._failed_attempts["***********"] = [current_time - 4000]
        
        metrics = self.validator.get_security_metrics()
        
        assert 'active_ip_blocks' in metrics
        assert 'total_blocked_ips' in metrics
        assert 'recent_failed_attempts' in metrics
        assert 'tracked_ips' in metrics
        assert 'security_patterns_count' in metrics
        
        assert metrics['active_ip_blocks'] == 1  # Only one active block
        assert metrics['total_blocked_ips'] == 2  # Both blocks (active and expired)
        assert metrics['recent_failed_attempts'] == 2  # Only recent attempts
        assert metrics['tracked_ips'] == 2  # Two IPs being tracked
        assert metrics['security_patterns_count'] > 0  # Should have security patterns