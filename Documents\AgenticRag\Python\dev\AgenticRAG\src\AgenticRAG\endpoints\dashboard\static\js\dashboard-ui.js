// Dashboard UI JavaScript Functions - UI and Navigation Module  
// Contains: User management, chat history, requests, tab navigation, and utility functions

// Global state variables (declared in dashboard-core.js, accessed via window)
// let currentView = 'user-list';    // Declared in dashboard-core.js
// let selectedUserGuid = null;      // Declared in dashboard-core.js

// Tab functionality with integrated log viewer support and state management
function showTab(tabName) {
    // Prevent recursion by adding a flag
    if (showTab._inProgress) {
        console.log('[DEBUG] showTab: Preventing recursive call for', tabName);
        return;
    }
    
    // Prevent rapid successive tab switches
    const now = Date.now();
    if (showTab._lastSwitch && (now - showTab._lastSwitch) < 100) {
        console.log('[DEBUG] showTab: Debouncing rapid tab switch to', tabName);
        return;
    }
    
    showTab._inProgress = true;
    showTab._lastSwitch = now;
    
    try {
        console.log('[DEBUG] showTab: Switching to tab', tabName);
        
        // Stop chat history polling when leaving chat-history tab
        if (window.currentView === 'chat-history' && tabName !== 'chat-history') {
            stopChatHistoryPolling();
        }
        
        // Stop request polling when leaving user-requests tab
        if (window.currentView === 'user-requests' && tabName !== 'user-requests') {
            stopRequestPolling();
        }
        
        // Stop live log polling when leaving chat pages
        if ((window.currentView === 'chat' || window.currentView === 'chat-history') && 
            (tabName !== 'chat' && tabName !== 'chat-history')) {
            if (typeof liveLogViewer !== 'undefined' && liveLogViewer.stopPolling) {
                liveLogViewer.stopPolling();
                console.log('[DEBUG] showTab: Stopped live log polling when leaving chat pages');
            }
        }
        
        // Hide all tab contents
        const tabContents = document.querySelectorAll('.tab-content');
        console.log('[DEBUG] showTab: Found', tabContents.length, 'tab content elements');
        tabContents.forEach(tab => {
            tab.classList.remove('active');
            tab.style.display = 'none';
            console.log('[DEBUG] showTab: Deactivated tab:', tab.id, 'Classes:', tab.className, 'Display:', tab.style.display);
        });
        
        // Remove active class from all tab buttons
        const tabButtons = document.querySelectorAll('.tab-button');
        tabButtons.forEach(btn => btn.classList.remove('active'));
        
        // Show selected tab content
        const targetTab = document.getElementById(tabName);
        if (targetTab) {
            targetTab.classList.add('active');
            // Force display to ensure visibility
            targetTab.style.display = 'block';
            console.log('[DEBUG] showTab: Successfully activated tab element:', tabName, 'Classes:', targetTab.className, 'Display:', targetTab.style.display);
        } else {
            console.error('[ERROR] showTab: Tab element not found:', tabName);
        }
        
        // Add active class to clicked button
        if (typeof event !== 'undefined' && event && event.target) {
            event.target.classList.add('active');
        }
        
        // Update current view for refresh purposes
        window.currentView = tabName;
        
        // Initialize features based on the active tab
        if (tabName === 'chat-history' || tabName === 'chat') {
            // Initialize log viewer when switching to chat tab
            setTimeout(() => {
                try {
                    console.log('[DEBUG] showTab: Re-initializing permissions and log viewer for chat tab');
                    
                    // Re-initialize user permissions to ensure proper UI state
                    if (typeof initializeUserPermissions === 'function') {
                        initializeUserPermissions();
                    }
                    
                    // Initialize Live Log Viewer for real-time system monitoring
                    if (typeof initializeLiveLogViewer === 'function') {
                        initializeLiveLogViewer();
                        console.log('[DEBUG] showTab: Live Log Viewer initialized for chat tab');
                    } else {
                        console.log('[WARNING] showTab: initializeLiveLogViewer function not available');
                    }
                } catch (error) {
                    console.error('[ERROR] showTab: Error initializing chat tab features:', error);
                }
            }, 100);
        } else if (tabName === 'user-requests') {
            // Initialize scheduled requests functionality
            setTimeout(() => {
                try {
                    console.log('[DEBUG] showTab: Initializing scheduled requests tab');
                    
                    // Ensure the search controls are properly initialized
                    const guidInput = document.getElementById('userGuidInput');
                    if (guidInput) {
                        console.log('[DEBUG] showTab: Scheduled requests search controls ready');
                    }
                    
                    // Clear any existing polling that might interfere
                    if (typeof stopChatHistoryPolling === 'function') {
                        stopChatHistoryPolling();
                    }
                    if (typeof stopRequestPolling === 'function') {
                        stopRequestPolling();
                    }
                    
                } catch (error) {
                    console.error('[ERROR] showTab: Error initializing scheduled requests tab:', error);
                }
            }, 100);
        } else if (tabName === 'user-list') {
            // Initialize user list functionality
            setTimeout(() => {
                try {
                    console.log('[DEBUG] showTab: Initializing user list tab');
                    
                    // Clear any active polling from other tabs
                    if (typeof stopChatHistoryPolling === 'function') {
                        stopChatHistoryPolling();
                    }
                    if (typeof stopRequestPolling === 'function') {
                        stopRequestPolling();
                    }
                    
                } catch (error) {
                    console.error('[ERROR] showTab: Error initializing user list tab:', error);
                }
            }, 100);
        }
        
    } catch (error) {
        console.error('[ERROR] showTab: Exception during tab switching:', error);
    } finally {
        // Always clear the flag
        showTab._inProgress = false;
    }
}

// User search functionality
function filterUsers() {
    const searchTerm = document.getElementById('userSearchInput').value.toLowerCase();
    const userCards = document.querySelectorAll('.user-card');
    
    userCards.forEach(card => {
        const usernameElement = card.querySelector('.username');
        if (usernameElement) {
            const username = usernameElement.textContent.toLowerCase();
            if (username.includes(searchTerm)) {
                card.style.display = 'block';
            } else {
                card.style.display = 'none';
            }
        }
    });
}

// Load user list
async function loadUserList() {
    const container = document.getElementById('userListContainer');
    if (!container) return;
    
    container.innerHTML = '<div class="loading">Loading users...</div>';
    
    try {
        const response = await safeFetch('/dashboard/api/user-list');
        const data = await response.json();
        
        // Handle session timeout
        if (response.status === 401 || data.error_type === 'session_timeout') {
            handleSessionTimeout(data);
            return;
        }
        
        // Handle rate limiting
        if (response.status === 429 || data.error_type === 'rate_limit') {
            const retryAfter = data.retry_after || 30;
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: 'rate-limit-error',
                title: 'Rate Limit Exceeded',
                message: data.error || 'Too many requests. Please wait before refreshing.',
                details: `Please wait ${retryAfter} seconds before trying again.`,
                retry_button: `<button onclick="setTimeout(loadUserList, ${retryAfter * 1000})" style="margin-top: 10px; padding: 5px 10px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Auto-retry in ${retryAfter}s
                </button>`
            });
            container.innerHTML = errorTemplate;
            return;
        }
        
        if (data.error) {
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: '',
                title: 'Error',
                message: data.error,
                details: '',
                retry_button: ''
            });
            container.innerHTML = errorTemplate;
            return;
        }
        
        const users = data.users || [];
        if (users.length === 0) {
            container.innerHTML = '<div class="loading">No users found</div>';
            return;
        }
        
        // Render user cards using direct HTML generation
        let html = '';
        users.forEach(user => {
            const username = user.username || 'Unknown';
            const rank = user.rank || 'USER';
            const userGuid = user.user_guid;
            
            // Direct HTML generation instead of async template loading
            html += `<div class="user-card" onclick="selectUser('${userGuid}', this)">
                <div style="font-weight: 600; margin-bottom: 0.25rem;">
                    <span class="username">${username}</span>
                    <span style="color: #60a5fa; font-size: 0.85rem; margin-left: 0.5rem;">${rank}</span>
                </div>
                <div style="font-size: 0.85rem; color: #94a3b8;">
                    GUID: ${userGuid}
                </div>
                <div style="margin-top: 0.75rem; display: flex; gap: 0.5rem; flex-wrap: wrap;">
                    <button onclick="event.stopPropagation(); showTab('user-requests'); document.getElementById('userGuidInput').value='${userGuid}'; loadUserRequests('${userGuid}');" 
                            title="View scheduled requests for this user"
                            onmouseover="this.style.background='#2563eb'"
                            onmouseout="this.style.background='#3b82f6'"
                            style="padding: 0.25rem 0.5rem; background: #3b82f6; color: white; border: none; border-radius: 4px; font-size: 0.75rem; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                        [SCHEDULED] Scheduled Requests
                    </button>
                    <button onclick="event.stopPropagation(); showTab('chat-history'); document.getElementById('chatUserGuidInput').value='${userGuid}'; loadUserChatHistory('${userGuid}');" 
                            title="View chat history for this user"
                            onmouseover="this.style.background='#d97706'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(245, 158, 11, 0.4)'"
                            onmouseout="this.style.background='#f59e0b'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(245, 158, 11, 0.3)'"
                            style="padding: 0.25rem 0.75rem; background: #f59e0b; color: white; border: none; border-radius: 4px; font-size: 0.75rem; font-weight: 600; cursor: pointer; transition: all 0.2s; box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);">
                        [CHAT] Chat History
                    </button>
                </div>
            </div>`;
        });
        
        // Add memory exhaustion warning if applicable
        if (data.is_truncated) {
            const warningTemplate = await TemplateLoader.renderTemplateWithData('warning_box.html', {
                warning_class: 'memory-limit-warning',
                title: 'Large Dataset - Results Limited',
                message: data.truncated_message,
                details: `Showing ${data.total_count} of ${data.actual_total} total users. Use search to find specific users.`,
                action_button: ''
            });
            html += warningTemplate;
        }
        
        container.innerHTML = html;
    } catch (error) {
        // Handle network errors that might indicate rate limiting
        if (error.message.includes('429') || error.message.toLowerCase().includes('rate limit')) {
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: 'rate-limit-error',
                title: 'Rate Limit Exceeded',
                message: 'Network request was rate limited. Please wait before trying again.',
                details: '',
                retry_button: `<button onclick="setTimeout(loadUserList, 30000)" style="margin-top: 10px; padding: 5px 10px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Retry in 30s
                </button>`
            });
            container.innerHTML = errorTemplate;
        } else {
            container.innerHTML = `<div class="error">Failed to load users: ${error.message}</div>`;
        }
    }
}

// Select user
function selectUser(userGuid, element) {
    // Update visual selection
    document.querySelectorAll('.user-card').forEach(card => {
        card.classList.remove('selected');
    });
    element.classList.add('selected');
    
    // Store selected user
    window.selectedUserGuid = userGuid;
    // selectedUserGuid = userGuid;  // Use window.selectedUserGuid instead
    
    // Load user's requests and chat history
    loadUserRequests(userGuid);
    loadUserChatHistory(userGuid);
}

// Load user requests (matching Chat History pattern)
async function loadUserRequests(userGuid) {
    const container = document.getElementById('requestsContainer');
    if (!container) return;
    
    container.innerHTML = '<div class="loading">Loading scheduled requests...</div>';
    
    try {
        const response = await safeFetch(`/dashboard/api/user-requests?user_guid=${encodeURIComponent(userGuid)}`);
        const data = await response.json();
        
        // Handle session timeout
        if (response.status === 401 || data.error_type === 'session_timeout') {
            handleSessionTimeout(data);
            return;
        }
        
        // Handle rate limiting
        if (response.status === 429 || data.error_type === 'rate_limit') {
            const retryAfter = data.retry_after || 30;
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: 'rate-limit-error',
                title: 'Requests Rate Limited',
                message: data.error || 'Too many requests. Please wait before refreshing.',
                details: `Please wait ${retryAfter} seconds before trying again.`,
                retry_button: `<button onclick="setTimeout(() => loadUserRequests('${userGuid}'), ${retryAfter * 1000})" style="margin-top: 10px; padding: 5px 10px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Auto-retry in ${retryAfter}s
                </button>`
            });
            container.innerHTML = errorTemplate;
            return;
        }
        
        if (data.error) {
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: '',
                title: 'Error Loading Requests',
                message: data.error,
                details: 'Unable to load scheduled requests for this user.',
                retry_button: `<button onclick="loadUserRequests('${userGuid}')" style="margin-top: 10px; padding: 5px 10px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">Retry</button>`
            });
            container.innerHTML = errorTemplate;
            return;
        }
        
        const requests = data.requests || [];
        if (requests.length === 0) {
            container.innerHTML = '<div class="loading">No scheduled requests found for this user</div>';
            return;
        }
        
        let html = '<h3>Scheduled Requests</h3>';
        html += '<div style="display: grid; gap: 1rem;">';
        
        requests.forEach((request, index) => {
            const statusColor = request.status === 'completed' ? '#10b981' : 
                              request.status === 'failed' ? '#ef4444' : '#f59e0b';
            
            const description = request.description || request.target_prompt || request.schedule_prompt || 'No description available';
            const scheduledGuid = request.scheduled_guid || 'Unknown';
            const createdAt = request.created_at ? new Date(request.created_at).toLocaleString() : 'Unknown';
            const nextRun = request.next_run ? new Date(request.next_run).toLocaleString() : 'N/A';
            const scheduleInfo = request.schedule_type ? `Schedule: ${request.schedule_type}` : '';
            const statusText = (request.status || 'UNKNOWN').toUpperCase();
            const chatSessionGuid = request.chat_session_guid || 'Unknown';
            
            // Use same expandable structure as Chat History
            const isExpanded = index === 0; // Expand first request by default
            html += `<div class="chat-session-card" data-request-id="${scheduledGuid}">
                <div class="session-header" onclick="toggleRequestSession('${scheduledGuid}')">
                    <div class="session-info">
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">
                            <span class="expand-icon ${isExpanded ? 'expanded' : ''}">&#9660;</span>
                            ${description}
                        </div>
                        <div style="font-size: 0.85rem; color: #94a3b8; margin-bottom: 0.25rem;">
                            Request ID: ${scheduledGuid}
                        </div>
                        <div style="font-size: 0.85rem; color: #94a3b8; display: flex; justify-content: space-between; align-items: center;">
                            <span>Created: ${createdAt} | Next Run: ${nextRun}</span>
                            <span style="padding: 0.25rem 0.75rem; border-radius: 12px; background: ${statusColor}20; color: ${statusColor}; font-weight: 600;">
                                ${statusText}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="session-messages" style="display: ${isExpanded ? 'block' : 'none'};">
                    ${renderRequestDetails(request, userGuid)}
                </div>
            </div>`;
        });
        
        html += '</div>';
        
        // Add memory limitation warning if applicable
        if (data.is_truncated) {
            const warningTemplate = await TemplateLoader.renderTemplateWithData('warning_box.html', {
                warning_class: 'memory-limit-warning',
                title: 'Request History Limited',
                message: data.truncated_message,
                details: `Memory limits applied: Showing ${data.truncated_info?.requests_shown || 'recent'} requests out of ${data.truncated_info?.total_requests || 'many'} total.`,
                action_button: ''
            });
            html += warningTemplate;
        }
        
        container.innerHTML = html;
        
        // Start polling for updates if not already running
        startRequestPolling(userGuid);
        
    } catch (error) {
        const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
            error_class: 'network-error',
            title: 'Network Error',
            message: 'Failed to load scheduled requests due to network issues.',
            details: error.message || 'Unknown network error occurred.',
            retry_button: `<button onclick="setTimeout(() => loadUserRequests('${userGuid}'), 5000)" style="margin-top: 10px; padding: 5px 10px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">Retry in 5s</button>`
        });
        container.innerHTML = errorTemplate;
    }
}

// Load user chat history with expandable sessions
async function loadUserChatHistory(userGuid) {
    const container = document.getElementById('chatHistoryContainer');
    if (!container) return;
    
    container.innerHTML = '<div class="loading">Loading chat history...</div>';
    
    try {
        const response = await fetch(`/dashboard/api/user-chat-history?user_guid=${encodeURIComponent(userGuid)}`);
        const data = await response.json();
        
        if (data.error) {
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: '',
                title: 'Error',
                message: data.error,
                details: '',
                retry_button: ''
            });
            container.innerHTML = errorTemplate;
            return;
        }
        
        const sessions = data.sessions_list || [];
        if (sessions.length === 0) {
            container.innerHTML = '<div class="loading">No chat history found for this user</div>';
            return;
        }
        
        let html = '<h3>Chat History</h3>';
        html += '<div style="display: grid; gap: 1rem;">';
        
        sessions.forEach((sessionId, index) => {
            const messages = data.chat_sessions[sessionId] || [];
            const metadata = data.sessions_metadata ? data.sessions_metadata[sessionId] : null;
            const lastMessage = messages[messages.length - 1];
            
            // Use metadata if available, otherwise fallback to calculated values
            const sessionTitle = metadata?.title || `Session ${sessionId.substring(0, 8)}`;
            const messageCount = metadata?.message_count !== undefined ? metadata.message_count : messages.length;
            const lastActivity = metadata?.last_activity ? 
                new Date(metadata.last_activity).toLocaleString() : 
                (lastMessage ? new Date(lastMessage.timestamp).toLocaleString() : 'No activity');
            
            // Expandable session card
            const isExpanded = index === 0; // Expand first session by default
            html += `<div class="chat-session-card" data-session-id="${sessionId}">
                <div class="session-header" onclick="toggleChatSession('${sessionId}')">
                    <div class="session-info">
                        <div style="font-weight: 600; margin-bottom: 0.5rem;">
                            <span class="expand-icon ${isExpanded ? 'expanded' : ''}">▼</span>
                            ${sessionTitle}
                        </div>
                        <div style="font-size: 0.85rem; color: #94a3b8; margin-bottom: 0.25rem;">
                            Session ID: ${sessionId}
                        </div>
                        <div style="font-size: 0.85rem; color: #94a3b8;">
                            Messages: ${messageCount}<br>
                            Last Activity: ${lastActivity}
                        </div>
                    </div>
                </div>
                <div class="session-messages" style="display: ${isExpanded ? 'block' : 'none'};">
                    ${renderSessionMessages(messages)}
                </div>
            </div>`;
        });
        
        html += '</div>';
        
        // Add memory limitation warning if applicable
        if (data.is_truncated) {
            const warningTemplate = await TemplateLoader.renderTemplateWithData('warning_box.html', {
                warning_class: 'memory-limit-warning',
                title: 'Chat History Limited',
                message: data.truncated_message,
                details: `Memory limits applied: Showing ${data.truncated_info?.sessions_shown || 'recent'} sessions with ${data.truncated_info?.total_messages_shown || 'recent'} messages.`,
                action_button: ''
            });
            html += warningTemplate;
        }
        
        container.innerHTML = html;
        
        // Start polling for updates if not already running
        startChatHistoryPolling(userGuid);
        
    } catch (error) {
        container.innerHTML = `<div class="error">Failed to load chat history: ${error.message}</div>`;
    }
}

// Render individual messages within a session
function renderSessionMessages(messages) {
    if (!messages || messages.length === 0) {
        return '<div class="chat-message">No messages in this session</div>';
    }
    
    let html = '';
    let activeInProgressIds = new Set(); // Track active In-Progress indicators
    
    messages.forEach((message, index) => {
        const role = message.role || 'user';
        const content = message.content || '';
        const timestamp = message.timestamp ? new Date(message.timestamp).toLocaleString() : 'Unknown time';
        const messageId = `msg-${message.session_id}-${index}`;
        
        // Handle In-Progress messages (check role, content, and message_type)
        const messageType = message.message_type || '';
        const isInProgress = (role.includes('in_progress') || role.includes('in progress')) &&
                            !content.includes('[DEBUG') &&
                            !content.includes('[REAL_DEBUG_CAPTURE]') &&
                            !content.includes('Captured') &&
                            !content.includes('INIT') &&
                            !content.includes('RETRIEVE') &&
                            !content.includes('TASK') &&
                            !content.includes('OUTPUT') &&
                            !content.includes('USER') &&
                            !content.includes('ERROR') &&
                            !content.includes('DEBUG') &&
                            !content.includes('DEBUG_TRACE') &&
                            (content.toLowerCase().includes('processing your request') ||
                             content.toLowerCase().includes('working on') ||
                             content.toLowerCase().includes('analyzing') ||
                             messageType.includes('progress'));
        
        if (isInProgress) {
            activeInProgressIds.add(messageId);
            html += `<div class="chat-message in-progress" id="${messageId}" data-role="${role}">
                <div class="message-header">
                    <span class="message-role">${role.toUpperCase()}</span>
                    <span class="message-timestamp">${timestamp}</span>
                </div>
                <div class="chat-content">
                    <div class="progress-indicator">
                        <div class="progress-spinner"></div>
                        <span>${content || 'Processing your request...'}</span>
                    </div>
                </div>
            </div>`;
        }
        // Handle Assistant messages
        else if (role.includes('assistant')) {
            // Check if this assistant message should hide any in-progress indicators
            activeInProgressIds.forEach(inProgressId => {
                // This assistant message replaces the in-progress state
                setTimeout(() => {
                    const inProgressElement = document.getElementById(inProgressId);
                    if (inProgressElement) {
                        inProgressElement.style.display = 'none';
                    }
                }, 100);
            });
            activeInProgressIds.clear(); // Clear in-progress tracking
            
            html += `<div class="chat-message chat-role-assistant" data-role="${role}">
                <div class="message-header">
                    <span class="message-role">ASSISTANT</span>
                    <span class="message-timestamp">${timestamp}</span>
                    ${message.tokens_used ? `<span class="chat-tokens">${message.tokens_used} tokens</span>` : ''}
                </div>
                <div class="chat-content">${escapeHtml(content)}</div>
                ${renderCallTrace(message.call_trace)}
                ${renderDebugMessages(message.debug_messages)}
            </div>`;
        }
        // Handle User messages
        else if (role.includes('user')) {
            html += `<div class="chat-message chat-role-user" data-role="${role}">
                <div class="message-header">
                    <span class="message-role">USER</span>
                    <span class="message-timestamp">${timestamp}</span>
                </div>
                <div class="chat-content">${escapeHtml(content)}</div>
            </div>`;
        }
        // Handle Debug messages (standalone)
        else if (role.includes('debug')) {
            html += `<div class="chat-message chat-role-debug" data-role="${role}">
                <div class="message-header">
                    <span class="message-role">DEBUG</span>
                    <span class="message-timestamp">${timestamp}</span>
                    ${message.tokens_used ? `<span class="chat-tokens">${message.tokens_used} tokens</span>` : ''}
                </div>
                <div class="chat-content debug-content">${escapeHtml(content)}</div>
            </div>`;
        }
        // Handle System/Other messages
        else {
            html += `<div class="chat-message chat-role-system" data-role="${role}">
                <div class="message-header">
                    <span class="message-role">${role.toUpperCase()}</span>
                    <span class="message-timestamp">${timestamp}</span>
                </div>
                <div class="chat-content">${escapeHtml(content)}</div>
            </div>`;
        }
    });
    
    return html;
}

// Render call trace for assistant messages
function renderCallTrace(callTrace) {
    if (!callTrace || callTrace.length === 0) return '';
    
    return `<div class="call-trace-section">
        <button class="expand-button" onclick="toggleCallTrace(this)">
            <span class="expand-icon">&#9654;</span> Call Trace (${callTrace.length} steps)
        </button>
        <div class="call-trace-content">
            <ol class="call-trace-list">
                ${callTrace.map(step => `<li class="call-trace-step">${escapeHtml(step)}</li>`).join('')}
            </ol>
        </div>
    </div>`;
}

// Render debug messages grouped under assistant messages
function renderDebugMessages(debugMessages) {
    if (!debugMessages || debugMessages.length === 0) return '';
    
    return `<div class="debug-messages-section">
        <button class="expand-button" onclick="toggleDebugMessages(this)">
            <span class="expand-icon">&#9654;</span> Debug & Logging Messages (${debugMessages.length})
        </button>
        <div class="debug-messages-content">
            ${debugMessages.map(debug => `
                <div class="debug-message-item">
                    <div class="debug-message-header">
                        <span class="debug-role">${debug.role.toUpperCase()}</span>
                        <span class="debug-timestamp">${debug.timestamp ? new Date(debug.timestamp).toLocaleString() : 'Unknown'}</span>
                        ${debug.tokens_used ? `<span class="debug-tokens">${debug.tokens_used} tokens</span>` : ''}
                    </div>
                    <div class="debug-message-content">${escapeHtml(debug.content)}</div>
                </div>
            `).join('')}
        </div>
    </div>`;
}

// Toggle chat session expansion
function toggleChatSession(sessionId) {
    const sessionCard = document.querySelector(`[data-session-id="${sessionId}"]`);
    if (!sessionCard) return;
    
    const messagesDiv = sessionCard.querySelector('.session-messages');
    const expandIcon = sessionCard.querySelector('.expand-icon');
    
    if (messagesDiv.style.display === 'none') {
        messagesDiv.style.display = 'block';
        expandIcon.classList.add('expanded');
    } else {
        messagesDiv.style.display = 'none';
        expandIcon.classList.remove('expanded');
    }
}

// Toggle request session expansion (matching Chat History pattern)
function toggleRequestSession(requestId) {
    const sessionCard = document.querySelector(`[data-request-id="${requestId}"]`);
    if (!sessionCard) return;
    
    const messagesDiv = sessionCard.querySelector('.session-messages');
    const expandIcon = sessionCard.querySelector('.expand-icon');
    
    if (messagesDiv.style.display === 'none') {
        messagesDiv.style.display = 'block';
        expandIcon.classList.add('expanded');
    } else {
        messagesDiv.style.display = 'none';
        expandIcon.classList.remove('expanded');
    }
}

// Render individual request details within a session (matching renderSessionMessages pattern)
function renderRequestDetails(request, userGuid) {
    if (!request) {
        return '<div class="chat-message">No request details available</div>';
    }
    
    const scheduledGuid = request.scheduled_guid || 'Unknown';
    const chatSessionGuid = request.chat_session_guid || 'Unknown';
    const scheduleType = request.schedule_type || 'N/A';
    const lastRun = request.last_run ? new Date(request.last_run).toLocaleString() : 'Never';
    const execCount = request.execution_count || 0;
    const statusColor = request.status === 'completed' ? '#10b981' : 
                       request.status === 'failed' ? '#ef4444' : '#f59e0b';
    
    // Basic request information
    let html = `<div class="chat-message">
        <div class="message-header">
            <span class="message-role" style="background: ${statusColor}; color: white;">REQUEST INFO</span>
            <span class="message-timestamp">${request.created_at ? new Date(request.created_at).toLocaleString() : 'Unknown'}</span>
        </div>
        <div class="message-content">
            <strong>Schedule Type:</strong> ${scheduleType}<br>
            <strong>Last Execution:</strong> ${lastRun}<br>
            <strong>Execution Count:</strong> ${execCount}<br>
            <strong>Chat Session:</strong> ${chatSessionGuid}
        </div>
    </div>`;
    
    // Add request prompt/description if available
    if (request.description || request.target_prompt || request.schedule_prompt) {
        const content = request.description || request.target_prompt || request.schedule_prompt;
        html += `<div class="chat-message">
            <div class="message-header">
                <span class="message-role">REQUEST CONTENT</span>
            </div>
            <div class="message-content">${escapeHtml(content)}</div>
        </div>`;
    }
    
    // Add execution history if available
    if (request.execution_history && request.execution_history.length > 0) {
        html += `<div class="chat-message">
            <div class="message-header">
                <span class="message-role">EXECUTION HISTORY</span>
            </div>
            <div class="message-content">`;
        
        request.execution_history.slice(-3).forEach(execution => {
            const execTime = execution.timestamp ? new Date(execution.timestamp).toLocaleString() : 'Unknown';
            const execStatus = execution.status || 'Unknown';
            const execColor = execStatus === 'completed' ? '#10b981' : execStatus === 'failed' ? '#ef4444' : '#f59e0b';
            html += `<div style="margin: 0.5rem 0; padding: 0.5rem; background: rgba(30, 41, 59, 0.5); border-radius: 4px; border-left: 3px solid ${execColor};">
                <strong>${execTime}</strong> - <span style="color: ${execColor};">${execStatus.toUpperCase()}</span>
                ${execution.message ? `<br><em>${escapeHtml(execution.message)}</em>` : ''}
            </div>`;
        });
        
        html += `    </div>
        </div>`;
    }
    
    // Add action buttons
    html += `<div class="chat-message">
        <div class="message-header">
            <span class="message-role">ACTIONS</span>
        </div>
        <div class="message-content" style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
            <button onclick="showCallTrace('${scheduledGuid}')" 
                    title="View execution call trace for this request"
                    onmouseover="this.style.background='#2563eb'"
                    onmouseout="this.style.background='#3b82f6'"
                    style="padding: 0.5rem 1rem; background: #3b82f6; color: white; border: none; border-radius: 6px; font-size: 0.85rem; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                View Call Trace
            </button>
            <button onclick="showDebugMessages('${scheduledGuid}')" 
                    title="View debug messages for this request"
                    onmouseover="this.style.background='#059669'"
                    onmouseout="this.style.background='#10b981'"
                    style="padding: 0.5rem 1rem; background: #10b981; color: white; border: none; border-radius: 6px; font-size: 0.85rem; font-weight: 500; cursor: pointer; transition: all 0.2s;">
                Debug Messages
            </button>
            <button onclick="navigateToChatSession('${chatSessionGuid}', '${userGuid}')" 
                    title="Navigate to chat history for this request"
                    onmouseover="this.style.background='#d97706'; this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(245, 158, 11, 0.4)'"
                    onmouseout="this.style.background='#f59e0b'; this.style.transform='translateY(0)'; this.style.boxShadow='0 2px 4px rgba(245, 158, 11, 0.3)'"
                    style="padding: 0.5rem 1rem; background: #f59e0b; color: white; border: none; border-radius: 6px; font-size: 0.85rem; font-weight: 600; cursor: pointer; transition: all 0.2s; box-shadow: 0 2px 4px rgba(245, 158, 11, 0.3);">
                Open Chat History
            </button>
        </div>
    </div>`;
    
    return html;
}

// Toggle call trace expansion with dynamic height management
function toggleCallTrace(button) {
    const content = button.nextElementSibling;
    const icon = button.querySelector('.expand-icon');
    const chatContainer = document.getElementById('chatContainer');
    const chatHistoryContainer = document.getElementById('chatHistoryContainer');
    const requestsContainer = document.getElementById('requestsContainer');
    
    const isExpanded = content.classList.contains('expanded');
    
    if (!isExpanded) {
        // Expanding
        content.classList.add('expanded');
        icon.innerHTML = '&#9660;';
        
        // Add expanded class to containers for dynamic height
        if (chatContainer) chatContainer.classList.add('has-expanded-debug');
        if (chatHistoryContainer) chatHistoryContainer.classList.add('has-expanded-debug');
        if (requestsContainer) requestsContainer.classList.add('has-expanded-debug');
        
        // Apply dynamic height adjustments
        setTimeout(() => HeightManager.adjustContainerHeights(), 50);
    } else {
        // Collapsing
        content.classList.remove('expanded');
        icon.innerHTML = '&#9654;';
        
        // Check if any other debug sections are expanded
        const hasOtherExpanded = document.querySelectorAll('.debug-messages-content.expanded, .call-trace-content.expanded').length > 0;
        if (!hasOtherExpanded) {
            if (chatContainer) chatContainer.classList.remove('has-expanded-debug');
            if (chatHistoryContainer) chatHistoryContainer.classList.remove('has-expanded-debug');
            if (requestsContainer) requestsContainer.classList.remove('has-expanded-debug');
            
            // Reset to default heights
            HeightManager.resetToDefaultHeights();
        } else {
            // Recalculate heights for remaining expanded sections
            setTimeout(() => HeightManager.adjustContainerHeights(), 50);
        }
    }
}

// Toggle debug messages expansion with dynamic height management
function toggleDebugMessages(button) {
    const content = button.nextElementSibling;
    const icon = button.querySelector('.expand-icon');
    const chatContainer = document.getElementById('chatContainer');
    const chatHistoryContainer = document.getElementById('chatHistoryContainer');
    const requestsContainer = document.getElementById('requestsContainer');
    
    const isExpanded = content.classList.contains('expanded');
    
    if (!isExpanded) {
        // Expanding
        content.classList.add('expanded');
        icon.innerHTML = '&#9660;';
        
        // Add expanded class to containers for dynamic height
        if (chatContainer) chatContainer.classList.add('has-expanded-debug');
        if (chatHistoryContainer) chatHistoryContainer.classList.add('has-expanded-debug');
        if (requestsContainer) requestsContainer.classList.add('has-expanded-debug');
        
        // Apply dynamic height adjustments
        setTimeout(() => HeightManager.adjustContainerHeights(), 50);
        
        // Scroll to make sure content is visible
        setTimeout(() => {
            content.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
        }, 100);
    } else {
        // Collapsing
        content.classList.remove('expanded');
        icon.innerHTML = '&#9654;';
        
        // Check if any other debug sections are expanded
        const hasOtherExpanded = document.querySelectorAll('.debug-messages-content.expanded, .call-trace-content.expanded').length > 0;
        if (!hasOtherExpanded) {
            if (chatContainer) chatContainer.classList.remove('has-expanded-debug');
            if (chatHistoryContainer) chatHistoryContainer.classList.remove('has-expanded-debug');
            if (requestsContainer) requestsContainer.classList.remove('has-expanded-debug');
            
            // Reset to default heights
            HeightManager.resetToDefaultHeights();
        } else {
            // Recalculate heights for remaining expanded sections
            setTimeout(() => HeightManager.adjustContainerHeights(), 50);
        }
    }
}

// Height management utilities for debug sections
const HeightManager = {
    // Detect which tab is currently active
    getActiveTab: function() {
        const activeTabContent = document.querySelector('.tab-content.active');
        if (activeTabContent) {
            return activeTabContent.id;
        }
        return null;
    },
    
    // Get the appropriate container based on active tab
    getActiveContainer: function() {
        const activeTab = this.getActiveTab();
        
        switch (activeTab) {
            case 'user-requests':
                return document.getElementById('requestsContainer');
            case 'chat-history':
                return document.getElementById('chatHistoryContainer');
            default:
                // Fallback to any available container
                return document.getElementById('requestsContainer') || 
                       document.getElementById('chatHistoryContainer') || 
                       document.getElementById('chatContainer');
        }
    },
    
    // Calculate optimal height based on expanded content
    calculateOptimalHeight: function(element) {
        if (!element) return null;
        
        const expandedSections = element.querySelectorAll('.debug-messages-content.expanded, .call-trace-content.expanded');
        let totalContentHeight = 0;
        
        expandedSections.forEach(section => {
            // Get the actual scrollHeight of the content
            const contentHeight = section.scrollHeight;
            totalContentHeight += contentHeight;
        });
        
        return {
            expandedCount: expandedSections.length,
            totalContentHeight: totalContentHeight,
            suggestedMinHeight: Math.min(2400 + totalContentHeight * 2.0, window.innerHeight * 0.99)
        };
    },
    
    // Adjust container heights dynamically
    adjustContainerHeights: function() {
        const chatContainer = document.getElementById('chatContainer');
        const chatHistoryContainer = document.getElementById('chatHistoryContainer');
        const requestsContainer = document.getElementById('requestsContainer');
        
        if (chatContainer || chatHistoryContainer || requestsContainer) {
            const hasExpanded = document.querySelectorAll('.debug-messages-content.expanded, .call-trace-content.expanded').length > 0;
            
            if (hasExpanded) {
                // Use the active container for metrics calculation
                const activeContainer = this.getActiveContainer();
                const metrics = this.calculateOptimalHeight(activeContainer);
                
                if (metrics && metrics.expandedCount > 0) {
                    const viewportHeight = window.innerHeight;
                    const headerHeight = 30; // Minimal header height for nearly full screen
                    const availableHeight = viewportHeight - headerHeight;
                    
                    // Maximum generous height calculation - nearly full screen
                    const optimalHeight = Math.min(metrics.suggestedMinHeight, availableHeight * 0.99);
                    
                    // Apply height adjustments based on active tab
                    const activeTab = this.getActiveTab();
                    
                    if (activeTab === 'user-requests' && requestsContainer) {
                        // Priority to requests container when on requests tab - nearly full screen
                        requestsContainer.style.maxHeight = `${Math.max(optimalHeight, availableHeight * 0.99)}px`;
                        requestsContainer.style.minHeight = '95vh';
                    } else if (activeTab === 'chat-history' && chatHistoryContainer) {
                        // Priority to chat history when on chat tab - nearly full screen
                        chatHistoryContainer.style.maxHeight = `${Math.max(optimalHeight, availableHeight * 0.99)}px`;
                        chatHistoryContainer.style.minHeight = '95vh';
                    } else {
                        // Apply to all containers as fallback - maximum heights
                        if (chatContainer) {
                            chatContainer.style.maxHeight = `${Math.max(optimalHeight, 2000)}px`;
                            chatContainer.style.minHeight = '2000px';
                        }
                        
                        if (chatHistoryContainer) {
                            chatHistoryContainer.style.maxHeight = `${Math.max(optimalHeight, availableHeight * 0.99)}px`;
                            chatHistoryContainer.style.minHeight = '95vh';
                        }
                        
                        if (requestsContainer) {
                            requestsContainer.style.maxHeight = `${Math.max(optimalHeight, availableHeight * 0.99)}px`;
                            requestsContainer.style.minHeight = '95vh';
                        }
                    }
                }
            }
        }
    },
    
    // Reset heights to default
    resetToDefaultHeights: function() {
        const chatContainer = document.getElementById('chatContainer');
        const chatHistoryContainer = document.getElementById('chatHistoryContainer');
        const requestsContainer = document.getElementById('requestsContainer');
        
        if (chatContainer) {
            chatContainer.style.maxHeight = '';
            chatContainer.style.minHeight = '';
        }
        
        if (chatHistoryContainer) {
            chatHistoryContainer.style.maxHeight = '';
            chatHistoryContainer.style.minHeight = '';
        }
        
        if (requestsContainer) {
            requestsContainer.style.maxHeight = '';
            requestsContainer.style.minHeight = '';
        }
    }
};

// Chat history polling variables
let chatPollingInterval = null;
let currentPollingUserGuid = null;

// Start polling for chat history updates
function startChatHistoryPolling(userGuid) {
    // Stop any existing polling
    stopChatHistoryPolling();
    
    currentPollingUserGuid = userGuid;
    
    // Register with PollingManager instead of direct setInterval
    if (window.pollingManager) {
        chatPollingInterval = window.pollingManager.register('chatHistory', () => {
            refreshChatHistoryQuietly(userGuid);
        }, 5000);
    } else {
        // Fallback to direct setInterval if PollingManager not available
        chatPollingInterval = setInterval(() => {
            refreshChatHistoryQuietly(userGuid);
        }, 5000);
    }
}

// Stop chat history polling
function stopChatHistoryPolling() {
    if (chatPollingInterval) {
        // Unregister from PollingManager or clear direct interval
        if (window.pollingManager) {
            window.pollingManager.unregister('chatHistory');
        } else {
            clearInterval(chatPollingInterval);
        }
        chatPollingInterval = null;
        currentPollingUserGuid = null;
    }
}

// Quietly refresh chat history without disrupting UI
async function refreshChatHistoryQuietly(userGuid) {
    try {
        const response = await fetch(`/dashboard/api/user-chat-history?user_guid=${encodeURIComponent(userGuid)}`);
        const newData = await response.json();
        
        if (newData.error) return; // Ignore errors during polling
        
        // Check if there are new messages by comparing message counts
        const container = document.getElementById('chatHistoryContainer');
        if (!container) return;
        
        const currentSessions = container.querySelectorAll('.chat-session-card');
        const newSessions = newData.sessions_list || [];
        
        // Simple check: if session count changed or message counts changed, refresh
        let needsRefresh = currentSessions.length !== newSessions.length;
        
        if (!needsRefresh) {
            // Check message counts in each session
            newSessions.forEach(sessionId => {
                const sessionCard = container.querySelector(`[data-session-id="${sessionId}"]`);
                if (sessionCard) {
                    const newMessages = newData.chat_sessions[sessionId] || [];
                    const currentMessages = sessionCard.querySelectorAll('.chat-message').length;
                    if (newMessages.length !== currentMessages) {
                        needsRefresh = true;
                    }
                } else {
                    needsRefresh = true; // New session appeared
                }
            });
        }
        
        if (needsRefresh) {
            // Remember which sessions were expanded
            const expandedSessions = new Set();
            currentSessions.forEach(sessionCard => {
                const sessionId = sessionCard.getAttribute('data-session-id');
                const messagesDiv = sessionCard.querySelector('.session-messages');
                if (messagesDiv && messagesDiv.style.display !== 'none') {
                    expandedSessions.add(sessionId);
                }
            });
            
            // Reload the chat history
            await loadUserChatHistory(userGuid);
            
            // Restore expanded state
            expandedSessions.forEach(sessionId => {
                const sessionCard = container.querySelector(`[data-session-id="${sessionId}"]`);
                if (sessionCard) {
                    const messagesDiv = sessionCard.querySelector('.session-messages');
                    const expandIcon = sessionCard.querySelector('.expand-icon');
                    if (messagesDiv && expandIcon) {
                        messagesDiv.style.display = 'block';
                        expandIcon.classList.add('expanded');
                    }
                }
            });
        }
    } catch (error) {
        // Silently ignore polling errors
        console.debug('Chat history polling error:', error);
    }
}

// Request polling functionality (matching Chat History pattern)
let requestPollingInterval = null;
let currentRequestPollingUserGuid = null;

// Start request polling for updates
function startRequestPolling(userGuid) {
    // Stop any existing request polling
    stopRequestPolling();
    
    currentRequestPollingUserGuid = userGuid;
    
    // Register with PollingManager instead of direct setInterval
    if (window.pollingManager) {
        requestPollingInterval = window.pollingManager.register('requestPolling', () => {
            refreshRequestsQuietly(userGuid);
        }, 10000);
    } else {
        // Fallback to direct setInterval if PollingManager not available
        requestPollingInterval = setInterval(() => {
            refreshRequestsQuietly(userGuid);
        }, 10000);
    }
}

// Stop request polling
function stopRequestPolling() {
    if (requestPollingInterval) {
        // Unregister from PollingManager or clear direct interval
        if (window.pollingManager) {
            window.pollingManager.unregister('requestPolling');
        } else {
            clearInterval(requestPollingInterval);
        }
        requestPollingInterval = null;
        currentRequestPollingUserGuid = null;
    }
}

// Quietly refresh requests without disrupting UI
async function refreshRequestsQuietly(userGuid) {
    try {
        const response = await safeFetch(`/dashboard/api/user-requests?user_guid=${encodeURIComponent(userGuid)}`);
        const newData = await response.json();
        
        if (newData.error) return; // Ignore errors during polling
        
        // Check if there are request changes by comparing request counts and statuses
        const container = document.getElementById('requestsContainer');
        if (!container) return;
        
        const currentRequests = container.querySelectorAll('.chat-session-card');
        const newRequests = newData.requests || [];
        
        // Simple check: if request count changed or status changes detected, refresh
        let needsRefresh = currentRequests.length !== newRequests.length;
        
        // Check for status changes in existing requests
        if (!needsRefresh) {
            const currentRequestIds = Array.from(currentRequests).map(card => card.dataset.requestId);
            for (const request of newRequests) {
                if (currentRequestIds.includes(request.scheduled_guid)) {
                    // Request exists, check if any status indicators need updating
                    const requestCard = container.querySelector(`[data-request-id="${request.scheduled_guid}"]`);
                    if (requestCard) {
                        const statusElement = requestCard.querySelector('.session-info');
                        if (statusElement && statusElement.textContent.includes(request.status?.toUpperCase() || 'UNKNOWN')) {
                            continue; // Status hasn't changed
                        } else {
                            needsRefresh = true; // Status changed
                            break;
                        }
                    }
                }
            }
        }
        
        if (needsRefresh) {
            // Store expanded state before refresh
            const expandedRequests = Array.from(currentRequests)
                .filter(card => card.querySelector('.session-messages').style.display === 'block')
                .map(card => card.dataset.requestId);
                
            // Refresh the requests list
            await loadUserRequests(userGuid);
            
            // Restore expanded state
            expandedRequests.forEach(requestId => {
                const requestCard = container.querySelector(`[data-request-id="${requestId}"]`);
                if (requestCard) {
                    const messagesDiv = requestCard.querySelector('.session-messages');
                    const expandIcon = requestCard.querySelector('.expand-icon');
                    if (messagesDiv && expandIcon) {
                        messagesDiv.style.display = 'block';
                        expandIcon.classList.add('expanded');
                    }
                }
            });
        }
    } catch (error) {
        // Silently ignore polling errors
        console.debug('Request polling error:', error);
    }
}

// Auto-refresh functionality
let autoRefreshInterval = null;
let autoRefreshEnabled = localStorage.getItem('autoRefresh') === 'true';

function toggleAutoRefresh() {
    const toggleBtn = document.getElementById('refreshToggle');
    const timerElement = document.getElementById('refreshTimer');
    
    if (!toggleBtn || !timerElement) return;
    
    autoRefreshEnabled = !autoRefreshEnabled;
    
    if (autoRefreshEnabled) {
        toggleBtn.textContent = 'Disable';
        startAutoRefresh();
    } else {
        toggleBtn.textContent = 'Enable';
        stopAutoRefresh();
        timerElement.textContent = 'Auto-refresh: Disabled';
    }
}

function startAutoRefresh() {
    let countdown = 30;
    const timerElement = document.getElementById('refreshTimer');
    
    // Update timer display
    function updateTimer() {
        if (timerElement) {
            timerElement.textContent = `Auto-refresh: ${countdown}s`;
        }
        countdown--;
        
        if (countdown < 0) {
            // Refresh the page
            window.location.reload();
        }
    }
    
    // Update immediately and then register with PollingManager
    updateTimer();
    
    // Register with PollingManager instead of direct setInterval
    if (window.pollingManager) {
        autoRefreshInterval = window.pollingManager.register('autoRefresh', updateTimer, 1000);
    } else {
        // Fallback to direct setInterval if PollingManager not available
        autoRefreshInterval = setInterval(updateTimer, 1000);
    }
}

function stopAutoRefresh() {
    if (autoRefreshInterval) {
        // Unregister from PollingManager or clear direct interval
        if (window.pollingManager) {
            window.pollingManager.unregister('autoRefresh');
        } else {
            clearInterval(autoRefreshInterval);
        }
        autoRefreshInterval = null;
    }
}

// System overview functionality
async function loadSystemOverview() {
    const container = document.getElementById('requestsContainer');
    if (!container) return;
    
    container.innerHTML = '<div class="loading">Loading system overview...</div>';
    
    try {
        const response = await fetch('/dashboard/api/dashboard-overview');
        const data = await response.json();
        
        // Handle rate limiting
        if (response.status === 429 || data.error_type === 'rate_limit') {
            const retryAfter = data.retry_after || 30;
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: 'rate-limit-error',
                title: 'System Overview - Rate Limited',
                message: data.error || 'Too many requests. Please wait before refreshing.',
                details: 'Showing cached data if available.',
                retry_button: `<button onclick="setTimeout(loadSystemOverview, ${retryAfter * 1000})" style="margin: 10px 0; padding: 5px 10px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Auto-retry in ${retryAfter}s
                </button>`
            });
            
            let html = errorTemplate;
            
            // Show fallback data if available
            if (data.fallback_data) {
                html += '<div class="metrics-grid" style="opacity: 0.6;">';
                const fallback = data.fallback_data;
                
                if (fallback.system_health) {
                    const metricCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                        card_style: '',
                        title: 'System Status',
                        value: fallback.system_health.overall_status || 'Rate Limited',
                        value_style: 'color: #f59e0b;',
                        subtitle: 'Cached status'
                    });
                    html += metricCard;
                }
                
                if (fallback.factory_metrics) {
                    const metricCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                        card_style: '',
                        title: 'Active Managers',
                        value: fallback.factory_metrics.active_managers || 0,
                        value_style: '',
                        subtitle: 'Last known count'
                    });
                    html += metricCard;
                }
                
                html += '</div>';
            }
            
            container.innerHTML = html;
            return;
        }
        
        if (data.error) {
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: '',
                title: 'Error',
                message: data.error,
                details: '',
                retry_button: ''
            });
            container.innerHTML = errorTemplate;
            return;
        }
        
        let html = '<h3>System Overview</h3>';
        
        // Track what data is missing for partial loading warning
        const missingData = [];
        let hasAnyData = false;
        
        html += '<div class="metrics-grid">';
        
        // System health metrics
        if (data.system_health) {
            hasAnyData = true;
            
            const dbCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: '',
                title: 'Database Status',
                value: data.system_health.database_status || 'Unknown',
                value_style: '',
                subtitle: 'PostgreSQL connection'
            });
            html += dbCard;
            
            const vectorCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: '',
                title: 'Vector DB Status',
                value: data.system_health.vector_database_status || 'Unknown',
                value_style: '',
                subtitle: 'Qdrant connection'
            });
            html += vectorCard;
            
            const overallCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: '',
                title: 'Overall Status',
                value: data.system_health.overall_status || 'Unknown',
                value_style: '',
                subtitle: 'System health'
            });
            html += overallCard;
        } else {
            missingData.push('System Health');
            // Show placeholder for missing system health data
            const placeholderCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: 'opacity: 0.5; border: 2px dashed #f59e0b;',
                title: 'Database Status',
                value: 'Unavailable',
                value_style: 'color: #f59e0b;',
                subtitle: 'Data loading failed'
            });
            html += placeholderCard;
        }
        
        // Performance metrics
        if (data.performance_metrics) {
            hasAnyData = true;
            
            const tasksCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: '',
                title: 'Active Tasks',
                value: data.performance_metrics.active_tasks || 0,
                value_style: '',
                subtitle: 'Currently running'
            });
            html += tasksCard;
            
            const requestsCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: '',
                title: 'Total Requests',
                value: data.performance_metrics.total_requests || 0,
                value_style: '',
                subtitle: 'All-time requests'
            });
            html += requestsCard;
            
            const loadCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: '',
                title: 'System Load',
                value: data.performance_metrics.system_load || 'Normal',
                value_style: '',
                subtitle: 'Performance status'
            });
            html += loadCard;
        } else {
            missingData.push('Performance Metrics');
            // Show placeholder for missing performance data
            const placeholderCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: 'opacity: 0.5; border: 2px dashed #f59e0b;',
                title: 'Active Tasks',
                value: '?',
                value_style: 'color: #f59e0b;',
                subtitle: 'Data loading failed'
            });
            html += placeholderCard;
        }
        
        // Factory metrics (if available)
        if (data.factory_metrics) {
            hasAnyData = true;
            const managersCard = await TemplateLoader.renderTemplateWithData('metric_card.html', {
                card_style: '',
                title: 'Active Managers',
                value: data.factory_metrics.active_managers || 0,
                value_style: '',
                subtitle: 'User-specific managers'
            });
            html += managersCard;
        } else {
            missingData.push('Factory Metrics');
        }
        
        html += '</div>';
        
        // Show partial data warning if some data is missing
        if (missingData.length > 0 && hasAnyData) {
            const warningTemplate = await TemplateLoader.renderTemplateWithData('warning_box.html', {
                warning_class: 'partial-data-warning',
                title: 'Partial Data Loaded',
                message: `Some data sections could not be loaded: ${missingData.join(', ')}`,
                details: 'This may be due to temporary service issues. Data will refresh automatically.',
                action_button: `<button onclick="loadSystemOverview()" style="margin-top: 0.5rem; padding: 4px 8px; background: #f59e0b; color: white; border: none; border-radius: 3px; cursor: pointer; font-size: 0.8rem;">
                    Retry Loading
                </button>`
            });
            html += warningTemplate;
        }
        
        container.innerHTML = html;
    } catch (error) {
        // Handle network errors that might indicate rate limiting
        if (error.message.includes('429') || error.message.toLowerCase().includes('rate limit')) {
            const errorTemplate = await TemplateLoader.renderTemplateWithData('error_box.html', {
                error_class: 'rate-limit-error',
                title: 'System Overview - Network Error',
                message: 'Network request was rate limited. Please wait before trying again.',
                details: '',
                retry_button: `<button onclick="setTimeout(loadSystemOverview, 30000)" style="margin-top: 10px; padding: 5px 10px; background: #f59e0b; color: white; border: none; border-radius: 4px; cursor: pointer;">
                    Retry in 30s
                </button>`
            });
            container.innerHTML = errorTemplate;
        } else {
            container.innerHTML = `<div class="error">Failed to load system overview: ${error.message}</div>`;
        }
    }
}

// Manual load functions for individual tabs
function loadUserRequestsManual() {
    console.log('[DEBUG] loadUserRequestsManual: Called');
    const userGuidInput = document.getElementById('userGuidInput');
    if (!userGuidInput) {
        console.error('[ERROR] loadUserRequestsManual: userGuidInput element not found');
        return;
    }
    
    const userGuid = userGuidInput.value.trim();
    if (!userGuid) {
        alert('Please enter a user GUID');
        return;
    }
    
    console.log('[DEBUG] loadUserRequestsManual: Loading requests for user:', userGuid);
    loadUserRequests(userGuid);
}

function loadChatHistory() {
    const chatUserGuidInput = document.getElementById('chatUserGuidInput');
    if (!chatUserGuidInput) return;
    
    const userGuid = chatUserGuidInput.value.trim();
    if (!userGuid) {
        alert('Please enter a user GUID');
        return;
    }
    
    loadUserChatHistory(userGuid);
}

// Navigate to Chat History tab for specific chat session
function navigateToChatSession(chatSessionGuid, userGuid) {
    // Handle completely missing data
    if (!userGuid || userGuid === 'Unknown') {
        showNotification('User information not available for this request. Cannot navigate to chat history.', 'error');
        return;
    }
    
    // Switch to chat-history tab
    showTab('chat-history');
    
    // Update the active tab button
    const tabButtons = document.querySelectorAll('.tab-button');
    tabButtons.forEach(btn => btn.classList.remove('active'));
    const chatHistoryButton = document.querySelector('.tab-button[onclick="showTab(\'chat-history\')"]');
    if (chatHistoryButton) {
        chatHistoryButton.classList.add('active');
    }
    
    // Pre-fill the user GUID input
    const chatUserGuidInput = document.getElementById('chatUserGuidInput');
    if (chatUserGuidInput) {
        chatUserGuidInput.value = userGuid;
    }
    
    // Load the chat history automatically
    loadUserChatHistory(userGuid);
    
    // Handle specific session highlighting if session GUID is available
    if (chatSessionGuid && chatSessionGuid !== 'Unknown') {
        // Store the target session GUID for highlighting after load
        window.targetChatSessionGuid = chatSessionGuid;
        
        // Add a small delay and try to highlight the specific session
        setTimeout(() => {
            highlightChatSession(chatSessionGuid);
        }, 1000);
        
        // Show success message with specific session
        setTimeout(() => {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 10000;
                background: #10b981; color: white; padding: 0.75rem 1rem;
                border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                font-size: 0.9rem; max-width: 300px;
            `;
            notification.textContent = `Navigated to chat history. Looking for session ${chatSessionGuid.substring(0, 8)}...`;
            document.body.appendChild(notification);
            
            // Remove notification after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }, 500);
    } else {
        // Show info message for general chat history
        setTimeout(() => {
            const notification = document.createElement('div');
            notification.style.cssText = `
                position: fixed; top: 20px; right: 20px; z-index: 10000;
                background: #f59e0b; color: white; padding: 0.75rem 1rem;
                border-radius: 8px; box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                font-size: 0.9rem; max-width: 300px;
            `;
            notification.textContent = `Navigated to chat history for user. Specific session not available.`;
            document.body.appendChild(notification);
            
            // Remove notification after 3 seconds
            setTimeout(() => {
                if (notification.parentNode) {
                    notification.parentNode.removeChild(notification);
                }
            }, 3000);
        }, 500);
    }
}

// Highlight specific chat session after loading
function highlightChatSession(chatSessionGuid) {
    // Look for the specific chat session in the loaded content
    const chatContainer = document.getElementById('chatContainer');
    if (!chatContainer) return;
    
    // Find all chat session elements (looking for session ID in the content)
    const sessionElements = chatContainer.querySelectorAll('[data-session-id], .chat-session');
    sessionElements.forEach(element => {
        const sessionId = element.getAttribute('data-session-id') || 
                         element.textContent.includes(chatSessionGuid);
        
        if (sessionId === chatSessionGuid || element.textContent.includes(chatSessionGuid)) {
            // Highlight the matching session
            element.style.border = '2px solid #f59e0b';
            element.style.backgroundColor = '#fef3c7';
            
            // Scroll to the element
            element.scrollIntoView({ behavior: 'smooth', block: 'center' });
            
            // Remove highlight after a few seconds
            setTimeout(() => {
                element.style.border = '';
                element.style.backgroundColor = '';
            }, 5000);
        }
    });
}

// Placeholder functions for call trace and debug messages (if not already implemented)
function showCallTrace(scheduledGuid) {
    alert(`Call Trace for request ${scheduledGuid} - Feature to be implemented`);
}

function showDebugMessages(scheduledGuid) {
    alert(`Debug Messages for request ${scheduledGuid} - Feature to be implemented`);
}

// Utility functions
window.safeCall = function(functionName, ...args) {
    try {
        if (typeof window[functionName] === 'function') {
            return window[functionName](...args);
        } else {
            console.warn(`Function ${functionName} is not available yet`);
            return null;
        }
    } catch (error) {
        console.error(`Error calling ${functionName}:`, error);
        return null;
    }
};

window.safeDocumentWrite = function(content) {
    try {
        if (typeof content !== 'string') {
            console.error('safeDocumentWrite: Content must be a string, got:', typeof content);
            return false;
        }
        
        if (content.includes('<') && !content.includes('>')) {
            console.error('safeDocumentWrite: Malformed HTML detected, skipping write');
            return false;
        }
        
        const tempDiv = document.createElement('div');
        tempDiv.innerHTML = content;
        document.write(content);
        return true;
        
    } catch (error) {
        console.error('safeDocumentWrite error:', error);
        console.error('Problematic content:', content?.substring(0, 200) + '...');
        return false;
    }
};

// Debug helper function
window.debugSession = async function() {
    try {
        console.log('[DEBUG] Checking session status...');
        const response = await safeFetch('/dashboard/api/session-debug');
        const data = await response.json();
        
        console.log('[DEBUG] Session Debug Response:', data);
        console.log('[DEBUG] Session Summary:');
        console.log('  - Has session data:', data.session_status.has_session_data);
        console.log('  - Authenticated:', data.session_status.authenticated);
        console.log('  - Is SYSTEM user:', data.session_status.is_system_user);
        console.log('  - Username:', data.session_status.username);
        console.log('  - Has session cookie:', data.cookie_status.has_session_cookie);
        console.log('  - Cookie length:', data.cookie_status.session_cookie_length);
        
        if (!data.session_status.authenticated) {
            console.warn('[WARNING] User is not authenticated - need to log in');
        }
        if (!data.session_status.is_system_user) {
            console.warn('[WARNING] User is not SYSTEM user - need SYSTEM permissions for live logs');
        }
        
        return data;
    } catch (error) {
        console.error('[ERROR] debugSession failed:', error);
        return null;
    }
};

console.log('[DEBUG] dashboard-ui.js module loaded');