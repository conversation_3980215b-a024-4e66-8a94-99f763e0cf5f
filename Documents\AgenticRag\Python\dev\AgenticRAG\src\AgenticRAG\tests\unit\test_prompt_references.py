from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from managers.manager_logfire import LogFire
from tests.unit.test_logging_capture import with_unit_test_logging
import pytest
import ast
import re
from glob import glob
from collections import defaultdict
from typing import Dict, List, Set


class TestPromptReferences:
    """Test suite to verify prompt management consistency"""
    
    @pytest.fixture(scope="class")
    def prompt_data(self):
        """Extract all prompts from manager_prompts.py"""
        # Get the prompts dictionary from PromptManager
        from managers.manager_prompts import PromptManager
        instance = PromptManager.get_instance()
        return instance.prompts
    
    @pytest.fixture(scope="class") 
    def codebase_files(self):
        """Get all Python files in the codebase except test files"""
        base_path = os_path.dirname(os_path.dirname(os_path.dirname(__file__)))
        pattern = os_path.join(base_path, "**", "*.py")
        all_files = glob(pattern, recursive=True)
        
        # Exclude test files and this specific test file
        python_files = [
            f for f in all_files 
            if not f.endswith('test_prompt_references.py') and '/tests/' not in f
        ]
        return python_files
    
    def find_prompt_references(self, files: List[str], prompt_keys: Set[str]) -> Dict[str, List[str]]:
        """Find all references to prompt keys in the codebase"""
        references = defaultdict(list)
        
        # Common patterns for prompt usage
        patterns = [
            r'prompt_id\s*=\s*["\']({})["\']\s*',  # prompt_id="PromptName"
            r'get_prompt\s*\(\s*["\']({})["\']\s*\)',  # get_prompt("PromptName")
            r'PromptManager\.get_prompt\s*\(\s*["\']({})["\']\s*\)',  # PromptManager.get_prompt("PromptName")
            r'["\']({})["\']',  # General string literal matching
        ]
        
        for file_path in files:
            try:
                with open(file_path, 'r', encoding='utf-8', errors='ignore') as f:
                    content = f.read()
                    
                    # Check each prompt key against each pattern
                    for prompt_key in prompt_keys:
                        for pattern in patterns:
                            # Create pattern with specific prompt key
                            specific_pattern = pattern.format(re.escape(prompt_key))
                            matches = re.findall(specific_pattern, content, re.IGNORECASE)
                            
                            if matches:
                                relative_path = os_path.relpath(file_path)
                                if relative_path not in references[prompt_key]:
                                    references[prompt_key].append(relative_path)
                                    
            except Exception as e:
                # Skip files that can't be read
                continue
                
        return dict(references)
    
    @with_unit_test_logging
    def test_all_prompts_have_references(self, prompt_data, codebase_files):
        """Test that every prompt in manager_prompts.py is referenced at least once"""
        prompt_keys = set(prompt_data.keys())
        references = self.find_prompt_references(codebase_files, prompt_keys)
        
        unreferenced_prompts = []
        for prompt_key in prompt_keys:
            if prompt_key not in references or len(references[prompt_key]) == 0:
                unreferenced_prompts.append(prompt_key)
        
        if unreferenced_prompts:
            message = f"Found {len(unreferenced_prompts)} unreferenced prompts:\n"
            for prompt in unreferenced_prompts:
                message += f"  - {prompt}\n"
            pytest.fail(message)
    
    @with_unit_test_logging
    def test_no_duplicate_prompt_references(self, prompt_data, codebase_files):
        """Test that no prompt is referenced in multiple files (excluding manager_prompts.py, test files, _verifier_.py, and AskZaira_Prompt)"""
        prompt_keys = set(prompt_data.keys())
        references = self.find_prompt_references(codebase_files, prompt_keys)
        
        over_referenced_prompts = []
        for prompt_key, file_list in references.items():
            # Skip AskZaira_Prompt as it's allowed to have multiple references
            if prompt_key == "AskZaira_Prompt":
                continue
            # Filter out manager_prompts.py, test files, and _verifier_.py from the count
            def is_test_file(filepath):
                return ('\\tests\\' in filepath or '/tests/' in filepath or 
                        '\\test_' in filepath or '/test_' in filepath or
                        filepath.endswith('_test.py'))
            
            filtered_files = [f for f in file_list if 'manager_prompts.py' not in f and not is_test_file(f) and '_verifier_.py' not in f]
            if len(filtered_files) > 1:
                over_referenced_prompts.append((prompt_key, filtered_files))
        
        if over_referenced_prompts:
            message = f"Found {len(over_referenced_prompts)} prompts referenced in multiple files:\n"
            for prompt, files in over_referenced_prompts:
                message += f"  - {prompt} referenced in:\n"
                for file_path in files:
                    message += f"    * {file_path}\n"
            pytest.fail(message)
    
    @with_unit_test_logging
    def test_prompt_reference_exactly_once(self, prompt_data, codebase_files):
        """Test that each prompt is referenced exactly once (excluding definition in manager_prompts.py, test files, _verifier_.py, and AskZaira_Prompt)"""
        prompt_keys = set(prompt_data.keys())
        references = self.find_prompt_references(codebase_files, prompt_keys)
        
        problems = []
        
        # Known unused prompts that are intentionally defined for future use
        known_unused = {'Task_EmailWriter_CoT_Prompt'}
        
        for prompt_key in prompt_keys:
            # Skip AskZaira_Prompt as it's allowed to have multiple references
            if prompt_key == "AskZaira_Prompt":
                continue
            
            # Skip known unused prompts that are intentionally defined for future use
            if prompt_key in known_unused:
                continue
                
            if prompt_key not in references:
                problems.append(f"UNREFERENCED: {prompt_key} - not used anywhere")
            else:
                # Filter out manager_prompts.py, test files, and _verifier_.py from the count
                def is_test_file(filepath):
                    return ('\\tests\\' in filepath or '/tests/' in filepath or 
                            '\\test_' in filepath or '/test_' in filepath or
                            filepath.endswith('_test.py'))
                
                usage_files = [f for f in references[prompt_key] if 'manager_prompts.py' not in f and not is_test_file(f) and '_verifier_.py' not in f]
                
                if len(usage_files) == 0:
                    problems.append(f"UNREFERENCED: {prompt_key} - not used anywhere")
                elif len(usage_files) > 1:
                    problems.append(f"OVER-REFERENCED: {prompt_key} - used in {len(usage_files)} files: {', '.join(usage_files)}")
        
        if problems:
            message = f"Found {len(problems)} prompt reference issues:\n"
            for problem in problems:
                message += f"  - {problem}\n"
            pytest.fail(message)
    
    @with_unit_test_logging
    def test_prompt_naming_consistency(self, prompt_data):
        """Test that prompt keys follow consistent naming patterns"""
        inconsistent_names = []
        
        for prompt_key in prompt_data.keys():
            # Check for consistent naming patterns
            if not prompt_key.replace('_', '').replace('CoT', '').isalnum():
                inconsistent_names.append(f"{prompt_key} - contains non-alphanumeric characters")
            
            # Check for valid naming patterns - updated to match actual codebase conventions
            valid_suffixes = [
                '_Prompt', '_CoT_Prompt', '_CoT',  # Traditional prompt suffixes
                '_Search', '_Processing', '_Sender',  # Functional suffixes
                '_Web_Search', '_RAG', '_FilePath', '_Multimodal', '_Images', '_Tables', '_Assets',  # Retrieval types
                '_GDrive', '_IMAP', '_Language_Detector',  # Service types
                '_Scanner_Supervisor', '_Scanner_Agent_Scanner', '_Scanner_Agent_Analyzer',  # Scanner patterns
                '_Chat_Sessions', '_Language_Verifier', '_Detect_Input', '_Detect_Output',  # Management patterns
                '_Discord', '_Mail', '_HTTP', '_Python', '_Teams', '_Whatsapp'  # Platform types
            ]
            
            # Check if the prompt key follows any valid pattern
            has_valid_suffix = any(prompt_key.endswith(suffix) for suffix in valid_suffixes)
            
            # Also accept certain standalone patterns
            valid_standalone_patterns = [
                'Quick_RAG_Search', 'Quick_LLM_Search'  # Quick search patterns
            ]
            
            is_valid_standalone = prompt_key in valid_standalone_patterns
            
            # Skip validation for well-known system prompts
            system_prompts = {'AskZaira_Prompt'}
            
            if prompt_key not in system_prompts and not has_valid_suffix and not is_valid_standalone and '_' in prompt_key:
                inconsistent_names.append(f"{prompt_key} - doesn't follow standard naming pattern")
        
        if inconsistent_names:
            message = f"Found {len(inconsistent_names)} prompts with inconsistent naming:\n"
            for issue in inconsistent_names:
                message += f"  - {issue}\n"
            pytest.fail(message)
    
    @with_unit_test_logging
    def test_prompt_content_quality(self, prompt_data):
        """Test basic quality checks for prompt content"""
        quality_issues = []
        
        for prompt_key, prompt_content in prompt_data.items():
            if not prompt_content or not prompt_content.strip():
                quality_issues.append(f"{prompt_key} - empty or whitespace-only content")
            elif len(prompt_content.strip()) < 10:
                quality_issues.append(f"{prompt_key} - suspiciously short content ({len(prompt_content.strip())} chars)")
            elif prompt_content.strip().count('.') == 0 and len(prompt_content.strip()) > 50:
                quality_issues.append(f"{prompt_key} - long content without proper punctuation")
        
        if quality_issues:
            message = f"Found {len(quality_issues)} prompt quality issues:\n"
            for issue in quality_issues:
                message += f"  - {issue}\n"
            pytest.fail(message)
    
    @with_unit_test_logging
    def test_generate_prompt_usage_report(self, prompt_data, codebase_files):
        """Generate a comprehensive report of prompt usage (not a failing test)"""
        prompt_keys = set(prompt_data.keys())
        references = self.find_prompt_references(codebase_files, prompt_keys)
        
        LogFire.log("DEBUG", "\n" + "="*80, severity="debug")
        LogFire.log("DEBUG", "PROMPT USAGE REPORT", severity="debug")
        LogFire.log("DEBUG", "="*80, severity="debug")
        LogFire.log("DEBUG", f"Total prompts defined: {len(prompt_keys)}", severity="debug")
        LogFire.log("DEBUG", f"Total files scanned: {len(codebase_files)}", severity="debug")
        LogFire.log("DEBUG", "", severity="debug")
        
        # Usage statistics
        unreferenced = 0
        single_reference = 0
        multiple_references = 0
        
        for prompt_key in prompt_keys:
            usage_files = references.get(prompt_key, [])
            # Filter out manager_prompts.py, test files, and _verifier_.py
            def is_test_file(filepath):
                return ('\\tests\\' in filepath or '/tests/' in filepath or 
                        '\\test_' in filepath or '/test_' in filepath or
                        filepath.endswith('_test.py'))
            
            filtered_files = [f for f in usage_files if 'manager_prompts.py' not in f and not is_test_file(f) and '_verifier_.py' not in f]
            
            # Special handling for AskZaira_Prompt - count as single reference for stats
            if prompt_key == "AskZaira_Prompt":
                single_reference += 1
            elif len(filtered_files) == 0:
                unreferenced += 1
            elif len(filtered_files) == 1:
                single_reference += 1
            else:
                multiple_references += 1
        
        LogFire.log("DEBUG", f"Prompt Usage Statistics:", severity="debug")
        LogFire.log("DEBUG", f"  - Unreferenced: {unreferenced}", severity="debug")
        LogFire.log("DEBUG", f"  - Single reference: {single_reference}", severity="debug")
        LogFire.log("DEBUG", f"  - Multiple references: {multiple_references}", severity="debug")
        LogFire.log("DEBUG", "", severity="debug")
        
        # Detailed breakdown
        if unreferenced > 0:
            LogFire.log("DEBUG", "UNREFERENCED PROMPTS:", severity="debug")
            for prompt_key in prompt_keys:
                usage_files = references.get(prompt_key, [])
                def is_test_file(filepath):
                    return ('\\tests\\' in filepath or '/tests/' in filepath or 
                            '\\test_' in filepath or '/test_' in filepath or
                            filepath.endswith('_test.py'))
                
                filtered_files = [f for f in usage_files if 'manager_prompts.py' not in f and not is_test_file(f) and '_verifier_.py' not in f]
                if len(filtered_files) == 0:
                    LogFire.log("DEBUG", f"  - {prompt_key}", severity="debug")
            LogFire.log("DEBUG", "", severity="debug")
        
        if multiple_references > 0:
            LogFire.log("DEBUG", "MULTIPLE REFERENCE PROMPTS:", severity="debug")
            for prompt_key in prompt_keys:
                # Skip AskZaira_Prompt from the multiple references report
                if prompt_key == "AskZaira_Prompt":
                    continue
                usage_files = references.get(prompt_key, [])
                def is_test_file(filepath):
                    return ('\\tests\\' in filepath or '/tests/' in filepath or 
                            '\\test_' in filepath or '/test_' in filepath or
                            filepath.endswith('_test.py'))
                
                filtered_files = [f for f in usage_files if 'manager_prompts.py' not in f and not is_test_file(f) and '_verifier_.py' not in f]
                if len(filtered_files) > 1:
                    LogFire.log("DEBUG", f"  - {prompt_key} ({len(filtered_files)} references):", severity="debug")
                    for file_path in filtered_files:
                        LogFire.log("DEBUG", f"    * {file_path}", severity="debug")
            LogFire.log("DEBUG", "", severity="debug")
        
        LogFire.log("DEBUG", "="*80, severity="debug")
        
        # This test always passes - it's just for reporting
        assert True