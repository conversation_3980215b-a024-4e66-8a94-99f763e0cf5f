"""
AskZaira Dashboard Endpoint Core Module
Contains: Imports, decorators, class setup, and core helper methods
"""

from imports import *

import os
from os import path as os_path
import json
from datetime import datetime, timezone, timedelta
from aiohttp import web
from typing import Dict, List, Any, Optional

from managers.scheduled_requests.admin_interface import get_admin_interface
from managers.scheduled_requests.integration_adapter import get_integration_adapter
from managers.manager_users import ZairaUserManager
from endpoints.dashboard.template_loader import get_dashboard_template_loader
from etc.helper_functions import is_claude_environment, exception_triggered
from managers.manager_logfire import LogFire

# Authentication decorators
def require_authentication(handler):
    """Decorator to require authenticated session"""
    async def wrapper(self, request: web.Request):
        if not request.get('session', {}).get('authenticated', False):
            LogFire.log("USER", "Unauthenticated access attempt to dashboard", severity="warning")
            return web.json_response({'error': 'Authentication required'}, status=401)
        return await handler(self, request)
    return wrapper

def require_system_user(handler):
    """Decorator to require SYSTEM user permissions"""
    async def wrapper(self, request: web.Request):
        session = request.get('session', {})
        if not session.get('authenticated', False):
            return web.json_response({'error': 'Authentication required'}, status=401)
        if not session.get('is_system_user', False):
            LogFire.log("USER", f"Access denied for non-SYSTEM user: {session.get('username', 'unknown')}", severity="warning")
            return web.json_response({'error': 'Insufficient permissions - SYSTEM user required'}, status=403)
        return await handler(self, request)
    return wrapper

def get_user_from_session(request: web.Request) -> Optional[Dict[str, Any]]:
    """Helper to extract user info from session"""
    session = request.get('session', {})
    LogFire.log("DEBUG", f"Raw session data: {session}", severity="debug")
    
    if not session.get('authenticated', False):
        LogFire.log("DEBUG", "Session not authenticated", severity="debug")
        return None
    
    user_data = {
        'user_guid': session.get('user_guid'),
        'username': session.get('username'),
        'is_system_user': session.get('is_system_user', False),
        'rank': session.get('rank', 'NONE')
    }
    LogFire.log("DEBUG", f"Extracted user data: {user_data}", severity="debug")
    return user_data

class DashboardEndpointCore:
    """
    Core functionality for administrative dashboard.
    
    Base class with initialization, session management, and data fetching.
    Extended by modules for static serving, content generation, and API endpoints.
    """
    
    def __init__(self):
        # Frontend debug logging state tracking to prevent redundant logging
        self._last_frontend_state = {}
        self._frontend_message_count = 0
        # DEBUG_WORKFLOW state tracking to prevent redundant logging
        self._last_workflow_states = {}  # Track last counts per request_guid
        LogFire.log("INIT", "Dashboard endpoint core initialized")
    
    def _check_session_timeout(self, request: web.Request) -> Optional[web.Response]:
        """
        Check for session timeout indicators in request
        
        Returns:
            Response object if session timeout detected, None otherwise
        """
        # Check for session timeout indicators in headers
        user_agent = request.headers.get('User-Agent', '').lower()
        referer = request.headers.get('Referer', '').lower()
        
        # Basic session timeout detection (can be enhanced with actual session management)
        # For now, check if request seems to come from an expired session context
        if 'expired' in referer or 'timeout' in referer or 'login' in referer:
            LogFire.log("ERROR", "Session timeout detected in dashboard request", chat=None)
            return web.json_response({
                'error': 'Session expired. Please refresh the page.',
                'error_type': 'session_timeout',
                'redirect_url': '/dashboard',
                'action_required': 'refresh'
            }, status=401)
        
        return None

    def _should_log_workflow_debug(self, request_guid: str, call_trace_count: int, debug_count: int, logging_count: int) -> bool:
        """
        Determine if we should log DEBUG_WORKFLOW message to prevent spam
        
        Args:
            request_guid: The request GUID
            call_trace_count: Number of call trace entries
            debug_count: Number of debug entries  
            logging_count: Number of logging entries
        
        Returns:
            True if we should log, False if we should skip to avoid spam
        """
        if request_guid not in self._last_workflow_states:
            # First time seeing this request_guid, always log
            self._last_workflow_states[request_guid] = {
                'call_trace_count': call_trace_count,
                'debug_count': debug_count,
                'logging_count': logging_count,
                'last_logged': datetime.now()
            }
            return True
        
        last_state = self._last_workflow_states[request_guid]
        now = datetime.now()
        time_since_last = now - last_state['last_logged']
        
        # Log if counts have changed significantly or if enough time has passed
        if (call_trace_count != last_state['call_trace_count'] or 
            debug_count != last_state['debug_count'] or 
            logging_count != last_state['logging_count'] or
            time_since_last.total_seconds() > 30):  # 30 second minimum between logs
            
            # Update state
            self._last_workflow_states[request_guid] = {
                'call_trace_count': call_trace_count,
                'debug_count': debug_count,
                'logging_count': logging_count,
                'last_logged': now
            }
            return True
        
        return False

    async def _fetch_app_data(self, data_type: str, guid: str) -> Optional[Dict[str, Any]]:
        """
        Fetch data from the app using GUIDs
        
        Args:
            data_type: Type of data to fetch ('user', 'request', 'task', etc.)
            guid: The GUID to fetch data for
        
        Returns:
            Dict with the fetched data or None if not found
        """
        try:
            admin = await get_admin_interface()
            
            if data_type == 'user':
                user_manager = ZairaUserManager.get_instance()
                user = await user_manager.find_user(guid)
                if user:
                    return {
                        'user_guid': user.user_guid,
                        'username': user.username,
                        'email': user.email,
                        'rank': user.rank.value if hasattr(user, 'rank') else 'USER',
                        'device_guid': str(user.DeviceGUID)
                    }
            
            elif data_type == 'request':
                request_data = await admin.get_request_details(guid)
                return request_data
            
            elif data_type == 'task':
                # Fetch task-specific data
                adapter = await get_integration_adapter()
                persistence = adapter._persistence
                task_data = await persistence.load_request(guid)
                return task_data
            
            elif data_type == 'quota':
                # Fetch quota information for a user
                user_details = await admin.get_user_details(guid)
                return user_details.get('quota_status')
            
            elif data_type == 'metrics':
                # Fetch metrics for a user
                user_details = await admin.get_user_details(guid)
                return user_details.get('metrics')
            
            return None
            
        except Exception as e:
            # Check for session/authentication errors
            error_msg = str(e).lower()
            if any(indicator in error_msg for indicator in ['unauthorized', 'session', 'expired', 'authentication']):
                LogFire.log("ERROR", f"Session timeout detected while fetching {data_type} data: {str(e)}")
                return {'error_type': 'session_timeout', 'error': 'Session expired'}
            
            LogFire.log("ERROR", f"Failed to fetch {data_type} data for {guid}: {str(e)}")
            return None

    def _get_user_initials(self, user) -> str:
        """
        Get user initials for display
        
        Args:
            user: User object with username
            
        Returns:
            String of initials (max 2 characters)
        """
        try:
            if hasattr(user, 'username') and user.username:
                # Split username by common separators and take first letter of each part
                parts = user.username.replace('_', ' ').replace('-', ' ').replace('.', ' ').split()
                if len(parts) >= 2:
                    return (parts[0][0] + parts[1][0]).upper()
                else:
                    return user.username[:2].upper()
            return '??'
        except:
            return '??'

# Create global instance
_dashboard_endpoint_core = DashboardEndpointCore()

def get_dashboard_endpoint_core() -> DashboardEndpointCore:
    """Get the global Dashboard endpoint core instance"""
    return _dashboard_endpoint_core