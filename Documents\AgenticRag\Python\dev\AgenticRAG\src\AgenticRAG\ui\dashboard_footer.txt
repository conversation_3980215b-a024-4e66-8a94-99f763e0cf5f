    </div>
    
    <script>
        // Extended functionality for AskZaira Dashboard
        
        // Expandable sections framework (65% code overlap)
        class ExpandableSection {
            constructor(element) {
                this.element = element;
                this.header = element.querySelector('.expandable-header');
                this.content = element.querySelector('.expandable-content');
                this.icon = element.querySelector('.expandable-icon');
                this.isExpanded = false;
                
                this.init();
            }
            
            init() {
                if (this.header) {
                    this.header.addEventListener('click', () => this.toggle());
                }
            }
            
            toggle() {
                this.isExpanded = !this.isExpanded;
                
                if (this.isExpanded) {
                    this.expand();
                } else {
                    this.collapse();
                }
                
                // Close other sections (accordion behavior)
                this.closeOtherSections();
            }
            
            expand() {
                this.header.classList.add('active');
                this.content.classList.add('active');
                this.content.style.maxHeight = this.content.scrollHeight + 'px';
                
                if (this.icon) {
                    this.icon.style.transform = 'rotate(180deg)';
                }
            }
            
            collapse() {
                this.header.classList.remove('active');
                this.content.classList.remove('active');
                this.content.style.maxHeight = '0';
                
                if (this.icon) {
                    this.icon.style.transform = 'rotate(0deg)';
                }
            }
            
            closeOtherSections() {
                if (this.isExpanded) {
                    const allSections = document.querySelectorAll('.expandable-section');
                    allSections.forEach(section => {
                        if (section !== this.element) {
                            const instance = section.expandableInstance;
                            if (instance && instance.isExpanded) {
                                instance.collapse();
                                instance.isExpanded = false;
                            }
                        }
                    });
                }
            }
        }
        
        // Initialize expandable sections
        function initializeExpandableSections() {
            const sections = document.querySelectorAll('.expandable-section');
            sections.forEach(section => {
                if (!section.expandableInstance) {
                    section.expandableInstance = new ExpandableSection(section);
                }
            });
        }
        
        // Tab functionality (using TabManager from header to avoid duplicate declaration)
        // Note: TabManager class is defined in dashboard_header.txt to prevent duplicate declarations
        
        // Initialize tab managers using the TabManager from header
        function initializeTabManagers() {
            const tabContainers = document.querySelectorAll('.tab-container');
            tabContainers.forEach(container => {
                if (!container.tabManager && window.TabManager) {
                    container.tabManager = new window.TabManager(container);
                }
            });
        }
        
        // Data loading utilities
        class DataLoader {
            constructor() {
                this.cache = new Map();
                this.loadingStates = new Set();
            }
            
            async loadData(url, options = {}) {
                const cacheKey = url + JSON.stringify(options);
                
                // Return cached data if available and not expired
                if (this.cache.has(cacheKey) && !options.forceRefresh) {
                    const cached = this.cache.get(cacheKey);
                    if (Date.now() - cached.timestamp < (options.cacheTime || 60000)) {
                        return cached.data;
                    }
                }
                
                // Prevent duplicate requests
                if (this.loadingStates.has(cacheKey)) {
                    return new Promise(resolve => {
                        const checkLoading = () => {
                            if (!this.loadingStates.has(cacheKey)) {
                                resolve(this.cache.get(cacheKey)?.data);
                            } else {
                                setTimeout(checkLoading, 100);
                            }
                        };
                        checkLoading();
                    });
                }
                
                this.loadingStates.add(cacheKey);
                
                try {
                    const response = await fetch(url, options.fetchOptions || {});
                    const data = await response.json();
                    
                    // Cache the result
                    this.cache.set(cacheKey, {
                        data: data,
                        timestamp: Date.now()
                    });
                    
                    return data;
                } catch (error) {
                    console.error(`Failed to load data from ${url}:`, error);
                    throw error;
                } finally {
                    this.loadingStates.delete(cacheKey);
                }
            }
            
            clearCache(pattern) {
                if (pattern) {
                    for (const key of this.cache.keys()) {
                        if (key.includes(pattern)) {
                            this.cache.delete(key);
                        }
                    }
                } else {
                    this.cache.clear();
                }
            }
        }
        
        // Global data loader instance
        window.dataLoader = new DataLoader();
        
        // UI feedback utilities
        function showToast(message, type = 'info', duration = 3000) {
            const toast = document.createElement('div');
            toast.className = `toast toast-${type} fade-in`;
            toast.textContent = message;
            
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                right: 20px;
                padding: 12px 20px;
                border-radius: 6px;
                color: white;
                font-weight: 500;
                z-index: 10000;
                max-width: 300px;
                word-wrap: break-word;
            `;
            
            const colors = {
                success: 'var(--status-success)',
                warning: 'var(--status-warning)',
                error: 'var(--status-error)',
                info: 'var(--status-info)'
            };
            
            toast.style.backgroundColor = colors[type] || colors.info;
            
            document.body.appendChild(toast);
            
            setTimeout(() => {
                toast.style.opacity = '0';
                toast.style.transform = 'translateX(100%)';
                setTimeout(() => document.body.removeChild(toast), 300);
            }, duration);
        }
        
        // Enhanced initialization
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize all components
            setTimeout(() => {
                initializeExpandableSections();
                initializeTabManagers();
            }, 100);
        });
        
        // Handle page-specific initializations
        function initializePageFeatures() {
            // Initialize based on current page
            switch (currentPage) {
                case 'profile':
                    initializeProfilePage();
                    break;
                case 'account':
                    initializeAccountPage();
                    break;
                case 'zaira':
                    initializeZairaPage();
                    break;
                case 'system':
                    initializeSystemPage();
                    break;
                case 'subscription':
                    initializeSubscriptionPage();
                    break;
                case 'grasshopper':
                    initializeGrasshopperPage();
                    break;
                default:
                    // General initialization
                    break;
            }
        }
        
        // Page-specific initialization functions
        function initializeZairaPage() {
            // Initialize user list, grasshopper history, and scheduled requests tabs
            setTimeout(() => {
                initializeTabManagers();
                initializeExpandableSections();
            }, 100);
        }
        
        function initializeSystemPage() {
            // Initialize system monitoring features  
            console.log('Initializing system page features');
            
            // Add organization ID validation
            const orgIdField = document.getElementById('organization_id');
            if (orgIdField) {
                orgIdField.addEventListener('blur', function() {
                    if (this.value && !isValidOrgId(this.value)) {
                        this.style.borderColor = 'var(--status-error)';
                        showToast('Organization ID should be alphanumeric with dashes', 'warning');
                    } else if (this.value) {
                        this.style.borderColor = 'var(--status-success)';
                    } else {
                        this.style.borderColor = 'var(--dashboard-glass-border)';
                    }
                });
            }
            
            // Auto-refresh component status every 30 seconds
            if (typeof systemStatusInterval !== 'undefined') {
                clearInterval(systemStatusInterval);
            }
            window.systemStatusInterval = setInterval(refreshComponents, 30000);
            
            // Initial refresh
            setTimeout(refreshComponents, 1000);
        }
        
        function initializeGrasshopperPage() {
            // Initialize grasshopper interface
            console.log('Initializing grasshopper page features');
        }
        
        // Profile page specific functions
        async function updateProfile(event) {
            event.preventDefault();
            
            const formData = new FormData();
            formData.append('first_name', document.getElementById('first_name').value);
            formData.append('last_name', document.getElementById('last_name').value);
            formData.append('email', document.getElementById('email').value);
            formData.append('job_title', document.getElementById('job_title').value);
            formData.append('company', document.getElementById('company').value);
            formData.append('personal_prompt', document.getElementById('personal_prompt').value);
            
            try {
                const submitButton = event.target.querySelector('button[type="submit"]');
                const originalText = submitButton.textContent;
                submitButton.textContent = 'Saving...';
                submitButton.disabled = true;
                
                const response = await fetch('/profile/update?user_guid=********-0000-0000-0000-************', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showToast('Profile updated successfully!', 'success');
                    
                    // Update header display if name changed
                    const firstName = document.getElementById('first_name').value;
                    const lastName = document.getElementById('last_name').value;
                    const displayName = `${firstName} ${lastName}`.trim() || 'User';
                    
                    const profileNameEl = document.querySelector('.profile-name');
                    if (profileNameEl) profileNameEl.textContent = displayName;
                    
                    const avatarInitials = document.querySelector('.avatar-initials');
                    if (avatarInitials) {
                        const initials = `${firstName.charAt(0)}${lastName.charAt(0)}`.toUpperCase() || 'U';
                        avatarInitials.textContent = initials;
                    }
                    
                } else {
                    showToast(result.error || 'Failed to update profile', 'error');
                }
                
            } catch (error) {
                console.error('Profile update error:', error);
                showToast('Network error occurred while updating profile', 'error');
            } finally {
                const submitButton = event.target.querySelector('button[type="submit"]');
                submitButton.textContent = 'Save Profile';
                submitButton.disabled = false;
            }
        }
        
        function resetProfileForm() {
            if (confirm('Are you sure you want to reset all changes? This will discard any unsaved modifications.')) {
                const form = document.getElementById('profileForm');
                if (form) {
                    form.reset();
                    showToast('Form reset successfully', 'info');
                }
            }
        }
        
        // Email validation for profile form
        function validateEmail(email) {
            const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
            return emailRegex.test(email);
        }
        
        // Initialize profile page features
        function initializeProfilePage() {
            // Add email validation
            const emailField = document.getElementById('email');
            if (emailField) {
                emailField.addEventListener('blur', function() {
                    if (this.value && !validateEmail(this.value)) {
                        this.style.borderColor = 'var(--status-error)';
                        showToast('Please enter a valid email address', 'warning');
                    } else if (this.value) {
                        this.style.borderColor = 'var(--status-success)';
                    } else {
                        this.style.borderColor = 'var(--dashboard-glass-border)';
                    }
                });
            }
            
            // Add required field validation
            const requiredFields = document.querySelectorAll('input[required]');
            requiredFields.forEach(field => {
                field.addEventListener('blur', function() {
                    if (!this.value.trim()) {
                        this.style.borderColor = 'var(--status-error)';
                    } else {
                        this.style.borderColor = 'var(--dashboard-glass-border)';
                    }
                });
            });
            
            // Auto-save draft functionality (optional)
            const formInputs = document.querySelectorAll('.profile-form input, .profile-form textarea');
            formInputs.forEach(input => {
                input.addEventListener('input', function() {
                    // Save draft to localStorage
                    const draftKey = `profile_draft_${this.id}`;
                    localStorage.setItem(draftKey, this.value);
                });
            });
        }
        
        // Account page specific functions
        async function updateAccountPreferences(event) {
            event.preventDefault();
            
            const formData = new FormData();
            formData.append('preferred_language', document.getElementById('preferred_language').value);
            formData.append('zaira_voice', document.getElementById('zaira_voice').value);
            formData.append('enable_followup_questions', document.getElementById('enable_followup_questions').checked);
            
            try {
                const submitButton = event.target.querySelector('button[type="submit"]');
                const originalText = submitButton.textContent;
                submitButton.textContent = 'Saving...';
                submitButton.disabled = true;
                
                const response = await fetch('/account/preferences?user_guid=********-0000-0000-0000-************', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showToast('Preferences updated successfully!', 'success');
                } else {
                    showToast(result.error || 'Failed to update preferences', 'error');
                }
                
            } catch (error) {
                console.error('Preferences update error:', error);
                showToast('Network error occurred while updating preferences', 'error');
            } finally {
                const submitButton = event.target.querySelector('button[type="submit"]');
                submitButton.textContent = 'Save Preferences';
                submitButton.disabled = false;
            }
        }
        
        async function updateCompanyInfo(event) {
            event.preventDefault();
            
            const formData = new FormData();
            formData.append('company_name', document.getElementById('company_name').value);
            formData.append('company_domain', document.getElementById('company_domain').value);
            
            try {
                const submitButton = event.target.querySelector('button[type="submit"]');
                const originalText = submitButton.textContent;
                submitButton.textContent = 'Updating...';
                submitButton.disabled = true;
                
                const response = await fetch('/account/company?user_guid=********-0000-0000-0000-************', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showToast('Company information updated successfully!', 'success');
                } else {
                    showToast(result.error || 'Failed to update company information', 'error');
                }
                
            } catch (error) {
                console.error('Company update error:', error);
                showToast('Network error occurred while updating company information', 'error');
            } finally {
                const submitButton = event.target.querySelector('button[type="submit"]');
                submitButton.textContent = 'Update Company Info';
                submitButton.disabled = false;
            }
        }
        
        async function updatePrivacySettings(event) {
            event.preventDefault();
            
            const formData = new FormData();
            formData.append('chat_history_interval', document.getElementById('chat_history_interval').value);
            
            try {
                const submitButton = event.target.querySelector('button[type="submit"]');
                const originalText = submitButton.textContent;
                submitButton.textContent = 'Saving...';
                submitButton.disabled = true;
                
                const response = await fetch('/account/privacy?user_guid=********-0000-0000-0000-************', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showToast('Privacy settings updated successfully!', 'success');
                } else {
                    showToast(result.error || 'Failed to update privacy settings', 'error');
                }
                
            } catch (error) {
                console.error('Privacy update error:', error);
                showToast('Network error occurred while updating privacy settings', 'error');
            } finally {
                const submitButton = event.target.querySelector('button[type="submit"]');
                submitButton.textContent = 'Save Privacy Settings';
                submitButton.disabled = false;
            }
        }
        
        async function updateSecuritySettings(event) {
            event.preventDefault();
            
            const formData = new FormData();
            formData.append('two_factor_enabled', document.getElementById('two_factor_enabled').checked);
            
            try {
                const submitButton = event.target.querySelector('button[type="submit"]');
                const originalText = submitButton.textContent;
                submitButton.textContent = 'Saving...';
                submitButton.disabled = true;
                
                const response = await fetch('/account/security?user_guid=********-0000-0000-0000-************', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showToast('Security settings updated successfully!', 'success');
                } else {
                    showToast(result.error || 'Failed to update security settings', 'error');
                }
                
            } catch (error) {
                console.error('Security update error:', error);
                showToast('Network error occurred while updating security settings', 'error');
            } finally {
                const submitButton = event.target.querySelector('button[type="submit"]');
                submitButton.textContent = 'Save Security Settings';
                submitButton.disabled = false;
            }
        }
        
        async function clearConnectors() {
            if (confirm('Are you sure you want to clear all connectors? This action cannot be undone.')) {
                try {
                    showToast('Clearing connectors...', 'info');
                    
                    const response = await fetch('/account/clear-connectors?user_guid=********-0000-0000-0000-************', {
                        method: 'POST'
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok && result.success) {
                        showToast('Connectors cleared successfully!', 'success');
                    } else {
                        showToast(result.error || 'Failed to clear connectors', 'error');
                    }
                    
                } catch (error) {
                    console.error('Clear connectors error:', error);
                    showToast('Network error occurred while clearing connectors', 'error');
                }
            }
        }
        
        async function clearAllData() {
            if (confirm('Are you sure you want to clear ALL data? This will permanently delete your grasshopper history, requests, and settings. This action cannot be undone.')) {
                if (confirm('This is your final warning. Are you absolutely sure you want to delete all your data?')) {
                    try {
                        showToast('Clearing all data...', 'info');
                        
                        const response = await fetch('/account/clear-data?user_guid=********-0000-0000-0000-************', {
                            method: 'POST'
                        });
                        
                        const result = await response.json();
                        
                        if (response.ok && result.success) {
                            showToast('All data cleared successfully!', 'success');
                            // Refresh the page after a short delay
                            setTimeout(() => {
                                window.location.reload();
                            }, 2000);
                        } else {
                            showToast(result.error || 'Failed to clear data', 'error');
                        }
                        
                    } catch (error) {
                        console.error('Clear data error:', error);
                        showToast('Network error occurred while clearing data', 'error');
                    }
                }
            }
        }
        
        async function logoutAllDevices() {
            if (confirm('Are you sure you want to log out of all devices? You will need to log in again on all devices.')) {
                try {
                    showToast('Logging out of all devices...', 'info');
                    
                    const response = await fetch('/account/logout-all?user_guid=********-0000-0000-0000-************', {
                        method: 'POST'
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok && result.success) {
                        showToast('Logged out of all devices successfully!', 'success');
                        // Redirect to login after a short delay
                        setTimeout(() => {
                            window.location.href = '/login';
                        }, 2000);
                    } else {
                        showToast(result.error || 'Failed to logout from all devices', 'error');
                    }
                    
                } catch (error) {
                    console.error('Logout all devices error:', error);
                    showToast('Network error occurred while logging out', 'error');
                }
            }
        }
        
        // Initialize account page features
        function initializeAccountPage() {
            console.log('Initializing account page features');
            
            // Add form validation for company domain field
            const domainField = document.getElementById('company_domain');
            if (domainField) {
                domainField.addEventListener('blur', function() {
                    if (this.value && !isValidDomain(this.value)) {
                        this.style.borderColor = 'var(--status-error)';
                        showToast('Please enter a valid domain name', 'warning');
                    } else if (this.value) {
                        this.style.borderColor = 'var(--status-success)';
                    } else {
                        this.style.borderColor = 'var(--dashboard-glass-border)';
                    }
                });
            }
        }
        
        // Domain validation helper
        function isValidDomain(domain) {
            const domainRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/;
            return domainRegex.test(domain) && domain.length <= 253;
        }
        
        // System page specific functions
        async function updateSystemConfig(event) {
            event.preventDefault();
            
            const formData = new FormData();
            formData.append('allow_document_generation', document.getElementById('allow_document_generation').checked);
            formData.append('data_location', document.getElementById('data_location').value);
            formData.append('organization_id', document.getElementById('organization_id').value);
            formData.append('privacy_level', document.getElementById('privacy_level').value);
            
            try {
                const submitButton = event.target.querySelector('button[type="submit"]');
                const originalText = submitButton.textContent;
                submitButton.textContent = 'Saving...';
                submitButton.disabled = true;
                
                const response = await fetch('/system/config?user_guid=********-0000-0000-0000-************', {
                    method: 'POST',
                    body: formData
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showToast('System configuration updated successfully!', 'success');
                } else {
                    showToast(result.error || 'Failed to update system configuration', 'error');
                }
                
            } catch (error) {
                console.error('System config update error:', error);
                showToast('Network error occurred while updating system configuration', 'error');
            } finally {
                const submitButton = event.target.querySelector('button[type="submit"]');
                submitButton.textContent = 'Save Configuration';
                submitButton.disabled = false;
            }
        }
        
        async function refreshComponents() {
            try {
                showToast('Refreshing component status...', 'info');
                
                // Use the existing dashboard system health endpoint instead of non-existent components-status
                const response = await fetch('/dashboard/api/system-health', {
                    method: 'GET'
                });
                
                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }
                
                const result = await response.json();
                
                if (result && !result.error) {
                    // Update component values in the UI with system health data
                    const activeTriggersEl = document.querySelector('.component-row:nth-child(1) .component-value');
                    const disabledTriggersEl = document.querySelector('.component-row:nth-child(2) .component-value');
                    const connectorsEl = document.querySelector('.component-row:nth-child(3) .component-value');
                    const automationsEl = document.querySelector('.component-row:nth-child(4) .component-value');
                    
                    // Map system health data to component display
                    const factoryMetrics = result.factory_metrics || {};
                    const performanceMetrics = result.performance_metrics || {};
                    
                    if (activeTriggersEl) activeTriggersEl.textContent = performanceMetrics.active_tasks || 0;
                    if (disabledTriggersEl) disabledTriggersEl.textContent = '0'; // No disabled trigger data available
                    if (connectorsEl) connectorsEl.textContent = factoryMetrics.active_managers || 0;
                    if (automationsEl) automationsEl.textContent = performanceMetrics.total_requests || 0;
                    
                    showToast('Component status refreshed successfully!', 'success');
                } else {
                    showToast(result.error || 'Failed to refresh component status', 'error');
                }
                
            } catch (error) {
                console.error('Refresh components error:', error);
                
                // Provide more specific error handling
                let errorMessage = 'Network error occurred while retrieving components';
                if (error.message.includes('404')) {
                    errorMessage = 'System health endpoint not found';
                } else if (error.message.includes('JSON')) {
                    errorMessage = 'Invalid response format from server';
                } else if (error.name === 'TypeError' && error.message.includes('Failed to fetch')) {
                    errorMessage = 'Unable to connect to server';
                }
                
                showToast(errorMessage, 'error');
                
                // Set default values for components when refresh fails
                const activeTriggersEl = document.querySelector('.component-row:nth-child(1) .component-value');
                const disabledTriggersEl = document.querySelector('.component-row:nth-child(2) .component-value');
                const connectorsEl = document.querySelector('.component-row:nth-child(3) .component-value');
                const automationsEl = document.querySelector('.component-row:nth-child(4) .component-value');
                
                if (activeTriggersEl) activeTriggersEl.textContent = '-';
                if (disabledTriggersEl) disabledTriggersEl.textContent = '-';
                if (connectorsEl) connectorsEl.textContent = '-';
                if (automationsEl) automationsEl.textContent = '-';
            }
        }
        
        async function deleteAllChats() {
            if (confirm('Are you sure you want to delete all grasshopper history? This action cannot be undone.')) {
                if (confirm('This will permanently remove all conversations and message history. Continue?')) {
                    try {
                        showToast('Deleting all grasshopper history...', 'info');
                        
                        const response = await fetch('/system/delete-chats?user_guid=********-0000-0000-0000-************', {
                            method: 'POST'
                        });
                        
                        const result = await response.json();
                        
                        if (response.ok && result.success) {
                            showToast('All grasshopper history deleted successfully!', 'success');
                        } else {
                            showToast(result.error || 'Failed to delete grasshopper history', 'error');
                        }
                        
                    } catch (error) {
                        console.error('Delete chats error:', error);
                        showToast('Network error occurred while deleting grasshopper history', 'error');
                    }
                }
            }
        }
        
        async function removeScheduledRequests() {
            if (confirm('Are you sure you want to remove all scheduled requests? This will stop all automated tasks.')) {
                if (confirm('This will permanently cancel all scheduled tasks and automations. Continue?')) {
                    try {
                        showToast('Removing all scheduled requests...', 'info');
                        
                        const response = await fetch('/system/remove-scheduled?user_guid=********-0000-0000-0000-************', {
                            method: 'POST'
                        });
                        
                        const result = await response.json();
                        
                        if (response.ok && result.success) {
                            showToast('All scheduled requests removed successfully!', 'success');
                            // Refresh component status to show updated counts
                            setTimeout(refreshComponents, 1000);
                        } else {
                            showToast(result.error || 'Failed to remove scheduled requests', 'error');
                        }
                        
                    } catch (error) {
                        console.error('Remove scheduled requests error:', error);
                        showToast('Network error occurred while removing scheduled requests', 'error');
                    }
                }
            }
        }
        
        async function systemReset() {
            if (confirm('DANGER: Complete system reset will permanently delete ALL data, configurations, users, and settings. Are you absolutely sure?')) {
                if (confirm('This is your final warning. This action will completely reset the system to factory defaults. Type "RESET" if you want to continue.')) {
                    const confirmation = prompt('Type "RESET" to confirm complete system reset:');
                    if (confirmation === 'RESET') {
                        try {
                            showToast('Performing complete system reset...', 'info');
                            
                            const response = await fetch('/system/complete-reset?user_guid=********-0000-0000-0000-************', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json'
                                },
                                body: JSON.stringify({
                                    confirmation: 'RESET',
                                    timestamp: Date.now()
                                })
                            });
                            
                            const result = await response.json();
                            
                            if (response.ok && result.success) {
                                showToast('System reset completed successfully!', 'success');
                                // Redirect to setup page after a delay
                                setTimeout(() => {
                                    window.location.href = '/setup';
                                }, 3000);
                            } else {
                                showToast(result.error || 'Failed to perform system reset', 'error');
                            }
                            
                        } catch (error) {
                            console.error('System reset error:', error);
                            showToast('Network error occurred during system reset', 'error');
                        }
                    } else {
                        showToast('System reset cancelled - incorrect confirmation', 'warning');
                    }
                }
            }
        }
        
        
        // Organization ID validation helper
        function isValidOrgId(orgId) {
            const orgIdRegex = /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$/;
            return orgIdRegex.test(orgId) && orgId.length >= 3 && orgId.length <= 63;
        }
        
        // Clean up system page intervals
        function cleanupSystemPage() {
            if (typeof systemStatusInterval !== 'undefined') {
                clearInterval(window.systemStatusInterval);
            }
        }
        
        // Subscription page specific functions
        async function showPlanOptions() {
            const plans = [
                { id: 'ZZP', name: 'ZZP (Freelancer)', price: 29.99, description: 'Perfect for individual freelancers and solo entrepreneurs.' },
                { id: 'MKB', name: 'MKB (Small Business)', price: 69.99, description: 'Ideal for small to medium businesses with team collaboration needs.' },
                { id: 'ZairaPlus', name: 'ZairaPlus (Enterprise)', price: 199.99, description: 'Full-featured enterprise solution with unlimited capabilities.' }
            ];
            
            let planOptions = '<h3>Choose Your Plan</h3><div class="plan-options">';
            plans.forEach(plan => {
                planOptions += `
                    <div class="plan-option" onclick="selectPlan('${plan.id}')">
                        <h4>${plan.name}</h4>
                        <p class="plan-option-price">EUR${plan.price}/month</p>
                        <p class="plan-option-desc">${plan.description}</p>
                    </div>
                `;
            });
            planOptions += '</div>';
            
            showModal('Plan Selection', planOptions);
        }
        
        async function selectPlan(planId) {
            if (confirm(`Are you sure you want to change to ${planId} plan? Billing changes will take effect on your next billing cycle.`)) {
                try {
                    showToast('Updating subscription plan...', 'info');
                    
                    const response = await fetch('/subscription/change-plan?user_guid=********-0000-0000-0000-************', {
                        method: 'POST',
                        headers: { 'Content-Type': 'application/json' },
                        body: JSON.stringify({ plan_type: planId })
                    });
                    
                    const result = await response.json();
                    
                    if (response.ok && result.success) {
                        showToast('Plan updated successfully! Changes will take effect on your next billing cycle.', 'success');
                        closeModal();
                        setTimeout(() => window.location.reload(), 2000);
                    } else {
                        showToast(result.error || 'Failed to update plan', 'error');
                    }
                    
                } catch (error) {
                    console.error('Plan change error:', error);
                    showToast('Network error occurred while updating plan', 'error');
                }
            }
        }
        
        async function modifyPaymentMethod() {
            const paymentMethods = [
                'Credit Card ****1234',
                'Credit Card ****5678',
                'PayPal account',
                'Bank Transfer (SEPA)',
                'Add new payment method...'
            ];
            
            let methodOptions = '<h3>Select Payment Method</h3><div class="payment-options">';
            paymentMethods.forEach((method, index) => {
                methodOptions += `
                    <div class="payment-option" onclick="selectPaymentMethod('${method}')">
                        <span>${method}</span>
                    </div>
                `;
            });
            methodOptions += '</div>';
            
            showModal('Payment Method', methodOptions);
        }
        
        async function selectPaymentMethod(method) {
            if (method === 'Add new payment method...') {
                showToast('Redirecting to payment provider...', 'info');
                // In a real implementation, this would redirect to Stripe/PayPal
                closeModal();
                return;
            }
            
            try {
                showToast('Updating payment method...', 'info');
                
                const response = await fetch('/subscription/payment-method?user_guid=********-0000-0000-0000-************', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ payment_method: method })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showToast('Payment method updated successfully!', 'success');
                    closeModal();
                    setTimeout(() => window.location.reload(), 1500);
                } else {
                    showToast(result.error || 'Failed to update payment method', 'error');
                }
                
            } catch (error) {
                console.error('Payment method error:', error);
                showToast('Network error occurred while updating payment method', 'error');
            }
        }
        
        async function editEmployeeCount() {
            const currentCount = parseInt(document.querySelector('.billing-row:nth-child(4) .billing-value span').textContent.split(' /')[0]);
            const newCount = prompt(`Enter the number of employees (current: ${currentCount}):`, currentCount);
            
            if (newCount === null) return; // User cancelled
            
            const count = parseInt(newCount);
            if (isNaN(count) || count < 1 || count > 100) {
                showToast('Please enter a valid number between 1 and 100', 'error');
                return;
            }
            
            if (count === currentCount) {
                showToast('Employee count unchanged', 'info');
                return;
            }
            
            try {
                showToast('Updating employee count...', 'info');
                
                const response = await fetch('/subscription/employee-count?user_guid=********-0000-0000-0000-************', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ employee_count: count })
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showToast('Employee count updated successfully!', 'success');
                    setTimeout(() => window.location.reload(), 1500);
                } else {
                    showToast(result.error || 'Failed to update employee count', 'error');
                }
                
            } catch (error) {
                console.error('Employee count error:', error);
                showToast('Network error occurred while updating employee count', 'error');
            }
        }
        
        async function downloadInvoice(invoiceId) {
            try {
                showToast('Preparing invoice download...', 'info');
                
                const response = await fetch(`/subscription/invoice/${invoiceId}?user_guid=********-0000-0000-0000-************`, {
                    method: 'GET'
                });
                
                if (response.ok) {
                    const blob = await response.blob();
                    const url = window.URL.createObjectURL(blob);
                    const a = document.createElement('a');
                    a.href = url;
                    a.download = `invoice-${invoiceId}.pdf`;
                    document.body.appendChild(a);
                    a.click();
                    window.URL.revokeObjectURL(url);
                    document.body.removeChild(a);
                    showToast('Invoice downloaded successfully!', 'success');
                } else {
                    showToast('Failed to download invoice', 'error');
                }
                
            } catch (error) {
                console.error('Invoice download error:', error);
                showToast('Network error occurred while downloading invoice', 'error');
            }
        }
        
        async function viewAllInvoices() {
            try {
                showToast('Loading all invoices...', 'info');
                
                const response = await fetch('/subscription/invoices-all?user_guid=********-0000-0000-0000-************', {
                    method: 'GET'
                });
                
                const result = await response.json();
                
                if (response.ok && result.invoices) {
                    let invoiceList = '<h3>All Invoices</h3><div class="all-invoices-list">';
                    result.invoices.forEach(invoice => {
                        invoiceList += `
                            <div class="invoice-row">
                                <span>${invoice.date}</span>
                                <span>EUR${invoice.amount}</span>
                                <span class="invoice-status ${invoice.status.toLowerCase()}">${invoice.status}</span>
                                <button onclick="downloadInvoice('${invoice.invoice_id}')" class="btn btn-small">Download</button>
                            </div>
                        `;
                    });
                    invoiceList += '</div>';
                    
                    showModal('All Invoices', invoiceList);
                } else {
                    showToast('Failed to load invoices', 'error');
                }
                
            } catch (error) {
                console.error('View all invoices error:', error);
                showToast('Network error occurred while loading invoices', 'error');
            }
        }
        
        async function manageSubscription() {
            const options = `
                <h3>Subscription Settings</h3>
                <div class="subscription-settings">
                    <div class="setting-item">
                        <label class="form-checkbox">
                            <input type="checkbox" id="auto_renewal" checked>
                            <span class="form-checkbox-label">Auto-renewal enabled</span>
                        </label>
                    </div>
                    <div class="setting-item">
                        <label class="form-checkbox">
                            <input type="checkbox" id="billing_notifications" checked>
                            <span class="form-checkbox-label">Email billing notifications</span>
                        </label>
                    </div>
                    <div class="setting-item">
                        <label class="form-checkbox">
                            <input type="checkbox" id="usage_alerts">
                            <span class="form-checkbox-label">Usage limit alerts</span>
                        </label>
                    </div>
                    <div class="setting-actions">
                        <button class="btn btn-primary" onclick="saveSubscriptionSettings()">Save Settings</button>
                    </div>
                </div>
            `;
            
            showModal('Subscription Settings', options);
        }
        
        async function saveSubscriptionSettings() {
            const settings = {
                auto_renewal: document.getElementById('auto_renewal').checked,
                billing_notifications: document.getElementById('billing_notifications').checked,
                usage_alerts: document.getElementById('usage_alerts').checked
            };
            
            try {
                showToast('Saving subscription settings...', 'info');
                
                const response = await fetch('/subscription/settings?user_guid=********-0000-0000-0000-************', {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify(settings)
                });
                
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showToast('Subscription settings saved successfully!', 'success');
                    closeModal();
                } else {
                    showToast(result.error || 'Failed to save settings', 'error');
                }
                
            } catch (error) {
                console.error('Save settings error:', error);
                showToast('Network error occurred while saving settings', 'error');
            }
        }
        
        async function viewUsageLimits() {
            try {
                showToast('Loading usage information...', 'info');
                
                const response = await fetch('/subscription/usage-limits?user_guid=********-0000-0000-0000-************', {
                    method: 'GET'
                });
                
                const result = await response.json();
                
                if (response.ok && result.usage) {
                    const usage = result.usage;
                    const usageInfo = `
                        <h3>Usage & Limits</h3>
                        <div class="usage-details">
                            <div class="usage-item">
                                <span>AI Requests</span>
                                <span>${usage.requests_used} / ${usage.requests_limit}</span>
                                <div class="usage-bar">
                                    <div class="usage-fill" style="width: ${(usage.requests_used / usage.requests_limit) * 100}%"></div>
                                </div>
                            </div>
                            <div class="usage-item">
                                <span>Storage</span>
                                <span>${usage.storage_used}GB / ${usage.storage_limit}GB</span>
                                <div class="usage-bar">
                                    <div class="usage-fill" style="width: ${(usage.storage_used / usage.storage_limit) * 100}%"></div>
                                </div>
                            </div>
                            <div class="usage-item">
                                <span>Team Members</span>
                                <span>${usage.members_used} / ${usage.members_limit}</span>
                            </div>
                        </div>
                        <div class="upgrade-section">
                            <p>Need more resources? <button class="btn btn-primary" onclick="showPlanOptions()">Upgrade Plan</button></p>
                        </div>
                    `;
                    
                    showModal('Usage & Limits', usageInfo);
                } else {
                    showToast('Failed to load usage information', 'error');
                }
                
            } catch (error) {
                console.error('Usage limits error:', error);
                showToast('Network error occurred while loading usage information', 'error');
            }
        }
        
        async function cancelSubscription() {
            if (confirm('Are you sure you want to cancel your Zaira subscription? Your access will continue until the end of the current billing period.')) {
                if (confirm('This action will cancel your subscription permanently. You can reactivate anytime, but may lose some data. Continue with cancellation?')) {
                    const reason = prompt('Please let us know why you\'re cancelling (optional):');
                    
                    try {
                        showToast('Processing cancellation request...', 'info');
                        
                        const response = await fetch('/subscription/cancel?user_guid=********-0000-0000-0000-************', {
                            method: 'POST',
                            headers: { 'Content-Type': 'application/json' },
                            body: JSON.stringify({ 
                                cancellation_reason: reason || '',
                                confirmation: true,
                                timestamp: Date.now()
                            })
                        });
                        
                        const result = await response.json();
                        
                        if (response.ok && result.success) {
                            showToast('Subscription cancelled successfully. Your access continues until the end of the billing period.', 'success');
                            setTimeout(() => window.location.reload(), 3000);
                        } else {
                            showToast(result.error || 'Failed to cancel subscription', 'error');
                        }
                        
                    } catch (error) {
                        console.error('Cancel subscription error:', error);
                        showToast('Network error occurred while cancelling subscription', 'error');
                    }
                }
            }
        }
        
        // Initialize subscription page features
        function initializeSubscriptionPage() {
            console.log('Initializing subscription page features');
            
            // Add any subscription-specific initialization here
            // For example, real-time billing updates, usage monitoring, etc.
        }
        
        // Modal utility functions
        function showModal(title, content) {
            const modal = document.createElement('div');
            modal.className = 'subscription-modal';
            modal.innerHTML = `
                <div class="modal-backdrop" onclick="closeModal()"></div>
                <div class="modal-content">
                    <div class="modal-header">
                        <h3>${title}</h3>
                        <button class="modal-close" onclick="closeModal()">&times;</button>
                    </div>
                    <div class="modal-body">
                        ${content}
                    </div>
                </div>
            `;
            
            document.body.appendChild(modal);
        }
        
        function closeModal() {
            const modal = document.querySelector('.subscription-modal');
            if (modal) {
                document.body.removeChild(modal);
            }
        }
        
        // Global error handler
        window.addEventListener('error', function(e) {
            // Suppress specific document.write() errors that don't affect functionality
            if (e.message && (
                e.message.includes('Failed to execute \'write\' on \'Document\'') ||
                e.message.includes('Unexpected token \'<\'') ||
                e.message.includes('document.write')
            )) {
                console.log('Suppressed harmless document.write error:', e.message);
                return true; // Prevent showing error toast to user
            }
            
            console.error('Global error:', e.error);
            showToast('An unexpected error occurred', 'error');
        });
        
        // Global promise rejection handler
        window.addEventListener('unhandledrejection', function(e) {
            console.error('Unhandled promise rejection:', e.reason);
            showToast('A network error occurred', 'error');
        });
        
        // Keyboard shortcuts
        document.addEventListener('keydown', function(e) {
            // Alt + M: Toggle menu
            if (e.altKey && e.key === 'm') {
                e.preventDefault();
                document.getElementById('menuToggle').click();
            }
            
            // Alt + R: Toggle auto-refresh
            if (e.altKey && e.key === 'r') {
                e.preventDefault();
                document.getElementById('autoRefreshToggle').click();
            }
            
            // Escape: Close menu if expanded
            if (e.key === 'Escape' && isMenuExpanded) {
                document.getElementById('menuToggle').click();
            }
        });
        
        // Clean up intervals on page unload
        window.addEventListener('beforeunload', function() {
            if (window.autoRefreshInterval) {
                clearInterval(window.autoRefreshInterval);
            }
        });
    </script>
    
    <!-- Inline dashboard.js content with page-level duplication guard -->
    <script>
        console.log('[DEBUG] Checking for existing dashboard initialization');
        
        // Prevent duplicate dashboard.js execution at page level
        if (window.dashboardPageInitialized) {
            console.log('[DEBUG] Dashboard already initialized on this page, skipping duplicate execution');
        } else {
            console.log('[DEBUG] First dashboard initialization on this page, proceeding');
            window.dashboardPageInitialized = true;
            
            console.log('[DEBUG] Dashboard modules loaded externally via dashboard_header.txt');
            // Inline loading disabled to prevent conflicts with external module loading
            console.log('[DEBUG] Using external script loading only');
        }
    </script>
</body>
</html>