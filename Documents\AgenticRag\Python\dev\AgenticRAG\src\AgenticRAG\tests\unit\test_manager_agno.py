from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, call
from pydantic import ValidationError

@pytest.mark.unit
class TestAgnoManager:
    """Test the AgnoManager functionality"""
    
    def setup_method(self):
        """Reset singleton for each test"""
        from managers.manager_agno import AgnoManager
        AgnoManager._instance = None
        AgnoManager._initialized = False
        AgnoManager.knowledge_base = None
        AgnoManager.vector_db = None
    
    def teardown_method(self):
        """Clean up after each test"""
        from managers.manager_agno import AgnoManager
        AgnoManager._instance = None
        AgnoManager._initialized = False
        AgnoManager.knowledge_base = None
        AgnoManager.vector_db = None
    
    def test_singleton_pattern(self):
        """Test that AgnoManager follows singleton pattern"""
        from managers.manager_agno import AgnoManager
        
        instance1 = AgnoManager()
        instance2 = AgnoManager()
        instance3 = AgnoManager.get_instance()
        
        assert instance1 is instance2
        assert instance2 is instance3
        assert isinstance(instance1, AgnoManager)
    
    def test_initial_state(self):
        """Test initial state of AgnoManager"""
        from managers.manager_agno import AgnoManager
        
        assert AgnoManager._instance is None
        assert AgnoManager._initialized is False
        assert AgnoManager.knowledge_base is None
        assert AgnoManager.vector_db is None
    
    def test_get_instance_creates_instance(self):
        """Test that get_instance creates and returns instance"""
        from managers.manager_agno import AgnoManager
        
        instance = AgnoManager.get_instance()
        
        assert instance is not None
        assert isinstance(instance, AgnoManager)
        assert AgnoManager._instance is instance
    
    @pytest.mark.asyncio
    async def test_init_vector_db_docker_environment(self):
        """Test _init_vector_db method in Docker environment"""
        from managers.manager_agno import AgnoManager
        
        with patch('globals.Globals.is_docker', return_value=True):
            with patch('agno.vectordb.qdrant.Qdrant') as mock_qdrant:
                with patch('agno.embedder.fastembed.FastEmbedEmbedder') as mock_embedder:
                    mock_embedder_instance = MagicMock()
                    mock_embedder.return_value = mock_embedder_instance
                    
                    instance = AgnoManager.get_instance()
                    result = await instance._init_vector_db()
                    
                    mock_embedder.assert_called_once_with(dimensions=EMBEDDING_SIZE)
                    mock_qdrant.assert_called_once_with(
                        collection="mainCollection",
                        url=f"http://qdrant:{PORT_QDRANT}",
                        embedder=mock_embedder_instance
                    )
    
    @pytest.mark.asyncio
    async def test_init_vector_db_local_environment(self):
        """Test _init_vector_db method in local environment"""
        from managers.manager_agno import AgnoManager
        
        with patch('globals.Globals.is_docker', return_value=False):
            with patch('agno.vectordb.qdrant.Qdrant') as mock_qdrant:
                with patch('agno.embedder.fastembed.FastEmbedEmbedder') as mock_embedder:
                    mock_embedder_instance = MagicMock()
                    mock_embedder.return_value = mock_embedder_instance
                    
                    instance = AgnoManager.get_instance()
                    result = await instance._init_vector_db()
                    
                    mock_embedder.assert_called_once_with(dimensions=EMBEDDING_SIZE)
                    mock_qdrant.assert_called_once_with(
                        collection="mainCollection",
                        url=f"http://localhost:{PORT_QDRANT}",
                        embedder=mock_embedder_instance
                    )
    
    @pytest.mark.asyncio
    async def test_init_knowledge_base(self):
        """Test _init_knowledge_base method"""
        from managers.manager_agno import AgnoManager
        
        mock_vector_db = MagicMock()
        mock_kb = MagicMock()
        
        with patch('agno.knowledge.pdf_url.PDFUrlKnowledgeBase') as mock_pdf_kb:
            mock_pdf_kb.return_value = mock_kb
            
            instance = AgnoManager.get_instance()
            instance.vector_db = mock_vector_db
            
            result = await instance._init_knowledge_base()
            
            mock_pdf_kb.assert_called_once_with(
                urls=["https://agno-public.s3.amazonaws.com/recipes/ThaiRecipes.pdf"],
                vector_db=mock_vector_db
            )
            mock_kb.load.assert_called_once_with(recreate=False)
            assert result is mock_kb
    
    @pytest.mark.asyncio
    async def test_setup_first_time(self):
        """Test setup method when not initialized"""
        from managers.manager_agno import AgnoManager
        
        await AgnoManager.setup()
        
        instance = AgnoManager.get_instance()
        assert instance._initialized is True
    
    @pytest.mark.asyncio
    async def test_setup_already_initialized(self):
        """Test setup method when already initialized"""
        from managers.manager_agno import AgnoManager
        
        # First setup
        await AgnoManager.setup()
        instance = AgnoManager.get_instance()
        assert instance._initialized is True
        
        # Mock the initialization process to ensure it's not called again
        with patch.object(instance, '_init_vector_db', new_callable=AsyncMock) as mock_init_vector:
            with patch.object(instance, '_init_knowledge_base', new_callable=AsyncMock) as mock_init_kb:
                await AgnoManager.setup()
                
                # Should not call initialization methods again
                mock_init_vector.assert_not_called()
                mock_init_kb.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_CreateAgent_docker_environment(self):
        """Test CreateAgent method in Docker environment"""
        from managers.manager_agno import AgnoManager
        
        test_prompt = "You are a test agent"
        mock_agent = MagicMock()
        mock_knowledge_base = MagicMock()
        
        with patch('globals.Globals.is_docker', return_value=True):
            with patch('agno.agent.Agent') as mock_agent_class:
                with patch('agno.models.ollama.Ollama') as mock_ollama:
                    with patch('agno.storage.agent.postgres.PostgresAgentStorage') as mock_storage:
                        mock_agent_class.return_value = mock_agent
                        mock_ollama_instance = MagicMock()
                        mock_ollama.return_value = mock_ollama_instance
                        mock_storage_instance = MagicMock()
                        mock_storage.return_value = mock_storage_instance
                        
                        # Set up instance state
                        instance = AgnoManager.get_instance()
                        instance.knowledge_base = mock_knowledge_base
                        instance._initialized = True
                        
                        result = await AgnoManager.CreateAgent(test_prompt)
                        
                        mock_ollama.assert_called_once_with(id=AGENT_MODEL_OLLAMA)
                        mock_storage.assert_called_once_with(
                            table_name="agent_sessions",
                            db_url=f"postgresql+psycopg://ai:ai@pgvector:5432/ai"
                        )
                        mock_agent_class.assert_called_once_with(
                            model=mock_ollama_instance,
                            description=test_prompt,
                            knowledge=mock_knowledge_base,
                            storage=mock_storage_instance,
                            markdown=True
                        )
                        assert result is mock_agent
    
    @pytest.mark.asyncio
    async def test_CreateAgent_local_environment(self):
        """Test CreateAgent method in local environment"""
        from managers.manager_agno import AgnoManager
        
        test_prompt = "You are a test agent"
        mock_agent = MagicMock()
        mock_knowledge_base = MagicMock()
        
        with patch('globals.Globals.is_docker', return_value=False):
            with patch('agno.agent.Agent') as mock_agent_class:
                with patch('agno.models.ollama.Ollama') as mock_ollama:
                    with patch('agno.storage.agent.postgres.PostgresAgentStorage') as mock_storage:
                        mock_agent_class.return_value = mock_agent
                        mock_ollama_instance = MagicMock()
                        mock_ollama.return_value = mock_ollama_instance
                        mock_storage_instance = MagicMock()
                        mock_storage.return_value = mock_storage_instance
                        
                        # Set up instance state
                        instance = AgnoManager.get_instance()
                        instance.knowledge_base = mock_knowledge_base
                        instance._initialized = True
                        
                        result = await AgnoManager.CreateAgent(test_prompt)
                        
                        mock_ollama.assert_called_once_with(id=AGENT_MODEL_OLLAMA)
                        mock_storage.assert_called_once_with(
                            table_name="agent_sessions",
                            db_url=f"postgresql+psycopg://ai:ai@localhost:{PORT_POSTGRESQL}/ai"
                        )
                        mock_agent_class.assert_called_once_with(
                            model=mock_ollama_instance,
                            description=test_prompt,
                            knowledge=mock_knowledge_base,
                            storage=mock_storage_instance,
                            markdown=True
                        )
                        assert result is mock_agent
    
    @pytest.mark.asyncio
    async def test_CreateAgent_not_initialized(self):
        """Test CreateAgent method when not initialized"""
        from managers.manager_agno import AgnoManager
        
        test_prompt = "You are a test agent"
        mock_agent = MagicMock()
        
        with patch('globals.Globals.is_docker', return_value=False):
            with patch('agno.agent.Agent') as mock_agent_class:
                with patch('agno.models.ollama.Ollama') as mock_ollama:
                    with patch('agno.storage.agent.postgres.PostgresAgentStorage') as mock_storage:
                        with patch.object(AgnoManager, 'setup', new_callable=AsyncMock) as mock_setup:
                            mock_agent_class.return_value = mock_agent
                            mock_ollama_instance = MagicMock()
                            mock_ollama.return_value = mock_ollama_instance
                            mock_storage_instance = MagicMock()
                            mock_storage.return_value = mock_storage_instance
                            
                            # Initially not initialized
                            instance = AgnoManager.get_instance()
                            instance.knowledge_base = None
                            instance._initialized = False
                            
                            result = await AgnoManager.CreateAgent(test_prompt)
                            
                            # Should call setup when not initialized
                            mock_setup.assert_called_once()
                            mock_agent_class.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_RunAgent_with_string_response(self):
        """Test RunAgent method with string response"""
        from managers.manager_agno import AgnoManager
        
        mock_agent = MagicMock()
        user_input = "test input"
        expected_response = "test response"
        
        mock_agent.run.return_value = expected_response
        
        result = await AgnoManager.RunAgent(mock_agent, user_input)
        
        mock_agent.run.assert_called_once_with(user_input)
        assert result == expected_response
    
    @pytest.mark.asyncio
    async def test_RunAgent_with_message_response(self):
        """Test RunAgent method with message object response"""
        from managers.manager_agno import AgnoManager
        
        mock_agent = MagicMock()
        user_input = "test input"
        expected_content = "test content"
        
        mock_message = MagicMock()
        mock_message.content = expected_content
        
        mock_agent.run.return_value = mock_message
        
        with patch('etc.helper_functions.get_any_message_as_type') as mock_get_type:
            mock_get_type.return_value = type(mock_message)
            
            result = await AgnoManager.RunAgent(mock_agent, user_input)
            
            mock_agent.run.assert_called_once_with(user_input)
            assert result == expected_content
    
    @pytest.mark.asyncio
    async def test_RunAgent_with_run_response(self):
        """Test RunAgent method with RunResponse object"""
        from managers.manager_agno import AgnoManager
        
        mock_agent = MagicMock()
        user_input = "test input"
        expected_content = "response content"
        
        mock_run_response = MagicMock()
        mock_run_response.content = expected_content
        
        mock_agent.run.return_value = mock_run_response
        
        with patch('etc.helper_functions.get_any_message_as_type') as mock_get_type:
            mock_get_type.return_value = type(mock_run_response)
            
            result = await AgnoManager.RunAgent(mock_agent, user_input)
            
            mock_agent.run.assert_called_once_with(user_input)
            assert result == expected_content
    
    def test_class_attributes(self):
        """Test class attributes are properly defined"""
        from managers.manager_agno import AgnoManager
        
        assert hasattr(AgnoManager, '_instance')
        assert hasattr(AgnoManager, '_initialized')
        assert hasattr(AgnoManager, 'knowledge_base')
        assert hasattr(AgnoManager, 'vector_db')
        assert AgnoManager._instance is None
        assert AgnoManager._initialized is False
        assert AgnoManager.knowledge_base is None
        assert AgnoManager.vector_db is None
    
    def test_singleton_instance_persistence(self):
        """Test that singleton instance persists across multiple calls"""
        from managers.manager_agno import AgnoManager
        
        # Get instance and modify it
        instance1 = AgnoManager.get_instance()
        instance1.test_attribute = "test_value"
        
        # Get instance again
        instance2 = AgnoManager.get_instance()
        
        # Should be the same instance with same attributes
        assert instance1 is instance2
        assert hasattr(instance2, 'test_attribute')
        assert instance2.test_attribute == "test_value"
    
    @pytest.mark.asyncio
    async def test_init_vector_db_with_custom_constants(self):
        """Test _init_vector_db with different constant values"""
        from managers.manager_agno import AgnoManager
        
        # Test with different PORT_QDRANT value
        with patch('globals.Globals.is_docker', return_value=False):
            with patch('agno.vectordb.qdrant.Qdrant') as mock_qdrant:
                with patch('agno.embedder.fastembed.FastEmbedEmbedder') as mock_embedder:
                    # Mock different port value
                    original_port = PORT_QDRANT
                    with patch('managers.manager_agno.PORT_QDRANT', 6334):
                        mock_embedder_instance = MagicMock()
                        mock_embedder.return_value = mock_embedder_instance
                        
                        instance = AgnoManager.get_instance()
                        result = await instance._init_vector_db()
                        
                        mock_qdrant.assert_called_once_with(
                            collection="mainCollection",
                            url="http://localhost:6334",
                            embedder=mock_embedder_instance
                        )
    
    @pytest.mark.asyncio
    async def test_init_knowledge_base_with_different_urls(self):
        """Test _init_knowledge_base with different PDF URLs"""
        from managers.manager_agno import AgnoManager
        
        mock_vector_db = MagicMock()
        mock_kb = MagicMock()
        
        with patch('agno.knowledge.pdf_url.PDFUrlKnowledgeBase') as mock_pdf_kb:
            mock_pdf_kb.return_value = mock_kb
            
            instance = AgnoManager.get_instance()
            instance.vector_db = mock_vector_db
            
            result = await instance._init_knowledge_base()
            
            # Verify the specific URL is used
            mock_pdf_kb.assert_called_once()
            args, kwargs = mock_pdf_kb.call_args
            assert "urls" in kwargs
            assert "https://agno-public.s3.amazonaws.com/recipes/ThaiRecipes.pdf" in kwargs["urls"]
            assert kwargs["vector_db"] is mock_vector_db
    
    @pytest.mark.asyncio
    async def test_CreateAgent_with_empty_prompt(self):
        """Test CreateAgent with empty prompt"""
        from managers.manager_agno import AgnoManager
        
        empty_prompt = ""
        mock_agent = MagicMock()
        
        with patch('globals.Globals.is_docker', return_value=False):
            with patch('agno.agent.Agent') as mock_agent_class:
                with patch('agno.models.ollama.Ollama') as mock_ollama:
                    with patch('agno.storage.agent.postgres.PostgresAgentStorage') as mock_storage:
                        mock_agent_class.return_value = mock_agent
                        
                        instance = AgnoManager.get_instance()
                        instance._initialized = True
                        instance.knowledge_base = MagicMock()
                        
                        result = await AgnoManager.CreateAgent(empty_prompt)
                        
                        # Should still create agent with empty prompt
                        mock_agent_class.assert_called_once()
                        call_args = mock_agent_class.call_args
                        assert call_args[1]['description'] == empty_prompt
                        assert result is mock_agent
    
    @pytest.mark.asyncio
    async def test_setup_with_commented_code(self):
        """Test setup method with commented initialization code"""
        from managers.manager_agno import AgnoManager
        
        # The current implementation has commented out initialization code
        # This test verifies the current behavior
        
        instance = AgnoManager.get_instance()
        assert instance.vector_db is None
        assert instance.knowledge_base is None
        assert instance._initialized is False
        
        await AgnoManager.setup()
        
        # After setup, should be initialized but vector_db and knowledge_base should still be None
        # due to commented code
        assert instance._initialized is True
        assert instance.vector_db is None
        assert instance.knowledge_base is None
    
    @pytest.mark.asyncio
    async def test_CreateAgent_with_none_knowledge_base(self):
        """Test CreateAgent when knowledge_base is None"""
        from managers.manager_agno import AgnoManager
        
        test_prompt = "You are a test agent"
        mock_agent = MagicMock()
        
        with patch('globals.Globals.is_docker', return_value=False):
            with patch('agno.agent.Agent') as mock_agent_class:
                with patch('agno.models.ollama.Ollama') as mock_ollama:
                    with patch('agno.storage.agent.postgres.PostgresAgentStorage') as mock_storage:
                        mock_agent_class.return_value = mock_agent
                        
                        instance = AgnoManager.get_instance()
                        instance._initialized = True
                        instance.knowledge_base = None  # Explicitly set to None
                        
                        result = await AgnoManager.CreateAgent(test_prompt)
                        
                        # Should create agent with None knowledge_base
                        mock_agent_class.assert_called_once()
                        call_args = mock_agent_class.call_args
                        assert call_args[1]['knowledge'] is None
                        assert result is mock_agent
