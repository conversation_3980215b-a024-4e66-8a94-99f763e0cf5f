from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from uuid import uuid4, UUID
from unittest.mock import Mock, MagicMock, AsyncMock, patch
from userprofiles.ZairaUser import <PERSON>aira<PERSON><PERSON>, PERMISSION_LEVELS
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
from userprofiles.ZairaChat import ZairaChat, ChatSessionType
from tests.unit.test_logging_capture import with_unit_test_logging

class TestSessionIsolationIntegration:
    """Test session isolation integration with LongRunningZairaRequest"""
    
    @with_unit_test_logging
    def test_long_running_request_uses_correct_session_for_scheduled_request(self):
        """Test that LongRunningZairaRequest uses isolated session for scheduled requests"""
        # Create test user
        user = <PERSON><PERSON><PERSON><PERSON><PERSON>(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        # Create mock calling bot
        calling_bot = Mock()
        calling_bot.name = "test_bot"
        
        # Create scheduled GUID
        scheduled_guid = uuid4()
        
        # Create LongRunningZairaRequest with scheduled_guid
        request = LongRunningZairaRequest(
            user=user,
            complete_message="test message",
            calling_bot=calling_bot,
            original_message=None,
            scheduled_guid=scheduled_guid
        )
        
        # Verify the request has the scheduled_guid
        assert request.scheduled_guid == scheduled_guid
        
        # Verify that the request created its own isolated session
        assert request.chat_session_guid != user.session_guid  # Should be different from active session
        assert request.chat_session_guid in user.chat_history
        
        # Verify the session properties
        isolated_session = user.chat_history[request.chat_session_guid]
        assert isolated_session.session_type == ChatSessionType.SCHEDULED
        assert isolated_session.metadata['scheduled_guid'] == str(scheduled_guid)
    
    @with_unit_test_logging
    def test_long_running_request_uses_active_session_for_regular_request(self):
        """Test that LongRunningZairaRequest uses active session for regular requests"""
        # Create test user
        user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        # Create mock calling bot
        calling_bot = Mock()
        calling_bot.name = "test_bot"
        
        # Create LongRunningZairaRequest without scheduled_guid
        request = LongRunningZairaRequest(
            user=user,
            complete_message="test message",
            calling_bot=calling_bot,
            original_message=None
        )
        
        # Should get a new UUID for scheduled_guid
        assert request.scheduled_guid is not None
        assert isinstance(request.scheduled_guid, UUID)
        
        # For regular requests, it should use the active session
        # The session isolation logic only applies when scheduled_guid is explicitly provided
        # from a scheduled request context (not auto-generated)
    
    @with_unit_test_logging 
    def test_multiple_scheduled_requests_get_isolated_sessions(self):
        """Test that multiple scheduled requests each get their own isolated session"""
        # Create test user
        user = ZairaUser(
            username="test_user", 
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        
        # Create mock calling bot
        calling_bot = Mock()
        calling_bot.name = "test_bot"
        
        # Create two different scheduled GUIDs
        scheduled_guid_1 = uuid4()
        scheduled_guid_2 = uuid4()
        
        # Create two LongRunningZairaRequests with different scheduled_guids
        request_1 = LongRunningZairaRequest(
            user=user,
            complete_message="test message 1",
            calling_bot=calling_bot, 
            original_message=None,
            scheduled_guid=scheduled_guid_1
        )
        
        request_2 = LongRunningZairaRequest(
            user=user,
            complete_message="test message 2",
            calling_bot=calling_bot,
            original_message=None,
            scheduled_guid=scheduled_guid_2
        )
        
        # Verify the requests have different isolated sessions
        session_guid_1 = request_1.chat_session_guid
        session_guid_2 = request_2.chat_session_guid
        
        # Should be different from each other and from active session
        assert session_guid_1 != session_guid_2
        assert session_guid_1 != user.session_guid
        assert session_guid_2 != user.session_guid
        
        # Both should be scheduled type sessions
        assert user.chat_history[session_guid_1].session_type == ChatSessionType.SCHEDULED
        assert user.chat_history[session_guid_2].session_type == ChatSessionType.SCHEDULED
        
        # Each should have correct metadata
        assert user.chat_history[session_guid_1].metadata['scheduled_guid'] == str(scheduled_guid_1)
        assert user.chat_history[session_guid_2].metadata['scheduled_guid'] == str(scheduled_guid_2)