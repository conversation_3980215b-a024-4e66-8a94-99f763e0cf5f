from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from managers.manager_logfire import LogFire
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from endpoints.testing_endpoint import MyBot_Testing
from uuid import uuid4

class TestMyBotTestingFinal:
    """Final comprehensive test suite for MyBot_Testing to achieve 90% coverage"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.bot = MyBot_Testing()
        
        # Mock task and user
        self.mock_task = MagicMock()
        self.mock_user = MagicMock()
        self.mock_user.session_guid = uuid4()
        self.mock_user.on_message = AsyncMock()
        self.mock_task.user = self.mock_user
        self.mock_task.scheduled_guid = str(uuid4())
        self.mock_task.original_physical_message = MagicMock()
        self.mock_task.human_in_the_loop_callback = MagicMock()

    def test_initialization(self):
        """Test MyBot_Testing initialization"""
        bot = MyBot_Testing()
        assert bot.name == "Testing"
        assert bot.parent_instance is None
        
    def test_initialization_with_params(self):
        """Test MyBot_Testing initialization with parameters"""
        parent = MagicMock()
        bot = MyBot_Testing(parent_instance=parent, name="CustomTesting")
        assert bot.name == "CustomTesting"
        assert bot.parent_instance == parent

    def test_get_fallback_response_email_approval(self):
        """Test _get_fallback_response for email approval patterns"""
        bot = MyBot_Testing()
        
        test_cases = [
            "Wil je deze email goedkeuren?",
            "Approve this email?",
            "Verzending bevestigen j/n?",
            "GOEDKEUR dit bericht"
        ]
        
        for request in test_cases:
            response = bot._get_fallback_response(request)
            assert response == "j"
            
    def test_get_fallback_response_email_address(self):
        """Test _get_fallback_response for email address requests"""
        bot = MyBot_Testing()
        
        test_cases = [
            "Welk email adres?",
            "Naar welk address?",
            "What email address?",
            "Van welk mailadres?",
            "Welk e-mailadres?"
        ]
        
        for request in test_cases:
            response = bot._get_fallback_response(request)
            assert response == "<EMAIL>"
            
    def test_get_fallback_response_sender_email(self):
        """Test _get_fallback_response for sender email requests"""
        bot = MyBot_Testing()
        
        test_cases = [
            "Van welke sender?",
            "From verzender?",
            "Who is the sender?",
            "Verzonden door wie?"
        ]
        
        for request in test_cases:
            response = bot._get_fallback_response(request)
            assert response == "<EMAIL>"
            
    def test_get_fallback_response_yes_no(self):
        """Test _get_fallback_response for yes/no questions"""
        bot = MyBot_Testing()
        
        test_cases = [
            "Wil je dit ja of nee?",
            "Answer yes or no?",
            "y/n?",
            "JA of NEE?"
        ]
        
        for request in test_cases:
            response = bot._get_fallback_response(request)
            assert response == "ja"
            
    def test_get_fallback_response_default(self):
        """Test _get_fallback_response default case"""
        bot = MyBot_Testing()
        
        response = bot._get_fallback_response("Unknown request type")
        assert response == "ja"

    @pytest.mark.asyncio
    async def test_request_human_in_the_loop_testing_name(self):
        """Test request_human_in_the_loop_internal with Testing name"""
        bot = MyBot_Testing(name="Testing")
        
        with patch.object(bot, '_generate_test_response', return_value="Test response") as mock_generate, \
             patch('builtins.print') as mock_print:
            
            await bot.request_human_in_the_loop_internal(
                "Test request", self.mock_task, MagicMock()
            )
            
            mock_generate.assert_called_once_with("Test request")
            self.mock_task.user.on_message.assert_called_once_with(
                "Test response", bot, [], self.mock_task.original_physical_message
            )
            
            # Verify print statements
            assert mock_print.call_count >= 4

    @pytest.mark.asyncio
    async def test_request_human_in_the_loop_other_name(self):
        """Test request_human_in_the_loop_internal with other name"""
        bot = MyBot_Testing(name="OtherBot")
        
        with patch('endpoints.mybot_generic.MyBot_Generic.request_human_in_the_loop_internal') as mock_super:
            mock_super.return_value = None
            
            await bot.request_human_in_the_loop_internal(
                "Test request", self.mock_task, MagicMock()
            )
            
            mock_super.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_test_response_with_llm(self):
        """Test _generate_test_response with LLM available"""
        bot = MyBot_Testing()
        
        mock_llm = MagicMock()
        mock_message = MagicMock()
        mock_message.content = "LLM response"
        mock_llm.ainvoke = AsyncMock(return_value=mock_message)
        
        with patch('etc.ZairaSettings.ZairaSettings.llm', mock_llm):
            response = await bot._generate_test_response("Test request")
            
            assert response == "LLM response"
            mock_llm.ainvoke.assert_called_once()

    @pytest.mark.asyncio
    async def test_generate_test_response_no_llm(self):
        """Test _generate_test_response with no LLM"""
        bot = MyBot_Testing()
        
        with patch('etc.ZairaSettings.ZairaSettings', spec=[]) as mock_settings:
            # Mock ZairaSettings to not have llm attribute
            mock_settings.llm = None
            
            with patch.object(bot, '_get_fallback_response', return_value="Fallback response") as mock_fallback:
                response = await bot._generate_test_response("Test request")
                
                assert response == "Fallback response"
                mock_fallback.assert_called_once_with("Test request")

    @pytest.mark.asyncio
    async def test_generate_test_response_empty_response(self):
        """Test _generate_test_response with empty LLM response"""
        bot = MyBot_Testing()
        
        mock_llm = MagicMock()
        mock_message = MagicMock()
        mock_message.content = ""  # Empty response
        mock_llm.ainvoke = AsyncMock(return_value=mock_message)
        
        with patch('etc.ZairaSettings.ZairaSettings.llm', mock_llm), \
             patch.object(bot, '_get_fallback_response', return_value="Fallback response") as mock_fallback:
            
            response = await bot._generate_test_response("Test request")
            
            assert response == "Fallback response"
            mock_fallback.assert_called_once_with("Test request")

    @pytest.mark.asyncio
    async def test_generate_test_response_with_successful_llm_and_LogFire.log("DEBUG", self):
        """Test _generate_test_response with successful LLM response and print statement"""
        bot = MyBot_Testing()
        
        mock_llm = MagicMock()
        mock_message = MagicMock()
        mock_message.content = "  Generated response  "  # With whitespace
        mock_llm.ainvoke = AsyncMock(return_value=mock_message)
        
        with patch('etc.ZairaSettings.ZairaSettings.llm', mock_llm), \
             patch('builtins.print') as mock_print:
            
            response = await bot._generate_test_response("Test request")
            
            # Should strip whitespace and return
            assert response == "Generated response"
            
            # Should print the response
            mock_print.assert_called()

    @pytest.mark.asyncio
    async def test_generate_test_response_exception_handling(self):
        """Test _generate_test_response exception handling"""
        bot = MyBot_Testing()
        
        mock_llm = MagicMock()
        mock_llm.ainvoke = AsyncMock(side_effect=Exception("LLM error"))
        
        with patch('etc.ZairaSettings.ZairaSettings.llm', mock_llm), \
             patch('builtins.print') as mock_print, \
             patch.object(bot, '_get_fallback_response', return_value="Fallback response") as mock_fallback:
            
            response = await bot._generate_test_response("Test request")
            
            assert response == "Fallback response"
            mock_print.assert_called()
            mock_fallback.assert_called_once_with("Test request")

    @pytest.mark.asyncio
    async def test_halt_until_response_callback_cleared(self):
        """Test halt_until_response loop with callback cleared"""
        bot = MyBot_Testing(name="Testing")
        
        # Mock task with callback that gets cleared
        task = MagicMock()
        task.user = MagicMock()
        task.user.on_message = AsyncMock()
        task.scheduled_guid = str(uuid4())
        task.original_physical_message = MagicMock()
        task.human_in_the_loop_callback = MagicMock()  # Initially set
        
        with patch.object(bot, '_generate_test_response', return_value="Test response"), \
             patch('builtins.print'), \
             patch('asyncio.sleep', return_value=None) as mock_sleep:
            
            # Clear the callback after first sleep iteration
            async def clear_callback(*args):
                task.human_in_the_loop_callback = None
            
            mock_sleep.side_effect = clear_callback
            
            # This should enter the while loop and then exit when callback is None
            await bot.request_human_in_the_loop_internal(
                "Test request", task, MagicMock(), halt_until_response=True
            )
            
            # Verify the loop was entered
            mock_sleep.assert_called_once_with(0.1)

    def test_type_checking_import(self):
        """Test TYPE_CHECKING import"""
        # This tests that the TYPE_CHECKING import is working
        from typing import TYPE_CHECKING
        
        # The import should work without error
        assert TYPE_CHECKING is not None
        
        # Verify the conditional import would work
        if TYPE_CHECKING:
            # This would only run during type checking
            pass