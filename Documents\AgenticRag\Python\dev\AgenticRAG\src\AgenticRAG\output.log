WARNING:discord.client:PyNaCl is not installed, voice will NOT be supported
LogEntries database table created/verified
=== mainFunc() started ===
No Claude environment detected
=== About to set up data directories ===
Using data subfolder: AskZaira
Data_dir: C:\Users\<USER>\Documents\AgenticRag\Python\dev\_DATA_RAW\AskZaira
Persist dir: C:\Users\<USER>\Documents\AgenticRag\Python\dev\_DATA_EMBEDDED\AskZaira
Logfire project URL: https://logfire-eu.pydantic.dev/askzaira/agentic-rag
=== About to call init() ===
Database 'vectordb' already exists.
18:00:35.635 [][INIT], '_run_once -> _run': dev_run.py main() started .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:33.254747
18:00:37.543 [][INIT], '_run_once -> _run': About to check Claude environment .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:35.636007
18:00:37.547 [][INIT], '_run_once -> _run': [Scrubbed due to 'Auth'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:37.543291
18:00:37.551 [][INIT], '_run_once -> _run': [Scrubbed due to 'Auth'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:37.547607
18:00:37.555 [][INIT], '_run_once -> _run': About to call mainFunc() .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:37.552287
Creating a new singleton instance of ZairaUserManager.
INFO:managers.manager_users:Creating a new singleton instance of ZairaUserManager.
Pydantic models rebuilt successfully to resolve forward references
INFO:managers.manager_users:Pydantic models rebuilt successfully to resolve forward references
User added: SYSTEM with GUID: 00000000-0000-0000-0000-000000000001
INFO:managers.manager_users:User added: SYSTEM with GUID: 00000000-0000-0000-0000-000000000001
Both client and aclient are provided. If using `:memory:` mode, the data between clients is not synced.
WARNING:llama_index.vector_stores.qdrant.base:Both client and aclient are provided. If using `:memory:` mode, the data between clients is not synced.
BertForMaskedLM has generative capabilities, as `prepare_inputs_for_generation` is explicitly overwritten. However, it doesn't directly inherit from `GenerationMixin`. From \U0001f449v4.50\U0001f448 onwards, `PreTrainedModel` will NOT inherit from `GenerationMixin`, and this model will lose the ability to call `generate` and other related functions.
  - If you're using `trust_remote_code=True`, you can get rid of this warning by loading the model with an auto class. See https://huggingface.co/docs/transformers/en/model_doc/auto#auto-classes
  - If you are the owner of the model architecture code, please modify your model class such that it inherits from `GenerationMixin` (after `PreTrainedModel`, otherwise you'll get an exception).
  - If you are not the owner of the model architecture class, please contact the model code owner to update it.
'doc_id' is deprecated and 'id_' will be used instead
WARNING:root:'doc_id' is deprecated and 'id_' will be used instead
=== DIRECT PRINT: create_small_tasks returned ===
=== DIRECT PRINT: First LogFire.log after create_small_tasks done ===
=== DIRECT PRINT: Second LogFire.log after create_small_tasks done ===
=== DIRECT PRINT: About to start create_supervisors ===
=== DIRECT PRINT: LogFire.log called successfully ===
=== DIRECT PRINT: About to call self.create_supervisors() ===
=== DIRECT PRINT: Entered create_supervisors method ===
=== DIRECT PRINT: About to call create_task_quick_rag_search ===
=== DIRECT PRINT: create_task_quick_rag_search returned ===
file_cache is only supported with oauth2client<4.0.0
INFO:googleapiclient.discovery_cache:file_cache is only supported with oauth2client<4.0.0
=== DIRECT PRINT: After create_supervisors log ===
=== DIRECT PRINT: About to register and compile supervisor ===
=== DIRECT PRINT: LogFire.log for registering called ===
18:00:42.465 [][INIT], '_run_once -> _run': ZairaControl endpoint routes registered successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.462146
18:00:42.468 [][INIT], '_run_once -> _run': [Scrubbed due to 'Auth'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.465122
18:00:42.486 [][INIT], '_run_once -> _run': Discord bot setup called .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.483124
18:00:42.490 [][INIT], '_run_once -> _run': ZairaControl endpoint routes registered successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.487121
18:00:42.493 [][WARNING], '_run_once -> _run': Database operation failed: column "execution_count" does not exist .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.490121
18:00:42.497 [][WARNING], '_run_once -> _run': Failed to create index idx_scheduled_requests_performance: column "execution_count" does not exist .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.494122
18:00:42.502 [][INIT], '_run_once -> _run': Database tables and indexes created/verified .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.498126
18:00:42.505 [][INIT], '_run_once -> _run': Setting up new scheduled request architecture .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.503202
18:00:42.509 [][INIT], '_run_once -> _run': ScheduledRequestIntegrationAdapter initialized successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.506201
18:00:42.513 [][INIT], '_run_once -> _run': New scheduled request system ready .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.510202
18:00:42.534 [][INIT], '_run_once -> _run': ZairaUser created: SYSTEM .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.531202
18:00:42.538 [][USER], '_run_once -> _run': User created with GUID 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 7b7ba0a1-8da6-4efa-afdd-e7fd8eab3ce4 at 2025-08-16 18:00:42.534404
18:00:42.557 [][USER], '_run_once -> _run': SYSTEM user created successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.555412
18:00:42.564 [][USER], '_run_once -> _run': SystemUserManager initialized with SYSTEM user .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.561411
18:00:42.568 [][EVENT], '_run_once -> _run': Loading stored index .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.565414
18:00:42.572 [][EVENT], '_run_once -> _run': Index loaded .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.569414
18:00:42.575 [][INIT], '_run_once -> _run': Creating top output supervisor first... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.572411
18:00:42.579 [][INIT], '_run_once -> _run': Top output supervisor created successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.576487
18:00:42.583 [][INIT], '_run_once -> _run': Creating top level supervisor... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.580412
18:00:42.587 [][INIT], '_run_once -> _run': DEBUG: Entered create_top_level_supervisor function .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.583412
18:00:42.594 [][INIT], '_run_once -> _run': DEBUG: Running in event loop: True .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.590413
18:00:42.598 [][INIT], '_run_once -> _run': DEBUG: Creating TaskCreator instance... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.595127
18:00:42.602 [][INIT], '_run_once -> _run': DEBUG: TaskCreator instance created, calling create_top_level_supervisor... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.599128
18:00:42.605 [][INIT], '_run_once -> _run': DEBUG: Entered TaskCreator.create_top_level_supervisor method .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.602127
18:00:42.616 [][INIT], '_run_once -> _run': DEBUG: create_small_tasks completed .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.613130
18:00:42.623 [][INIT], '_run_once -> _run': DEBUG: About to start create_supervisors .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.620128
18:00:42.735 [][INIT], '_run_once -> _run': DEBUG: create_supervisors completed .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-16 18:00:42.732878
