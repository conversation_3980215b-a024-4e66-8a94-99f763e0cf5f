#!/usr/bin/env python3
"""
Direct Pandas + <PERSON><PERSON><PERSON><PERSON> Test
Run from src/AgenticRAG/ directory: python test_pandas_direct.py
"""

import asyncio
import pandas as pd
from langchain_experimental.agents.agent_toolkits import create_pandas_dataframe_agent
from langchain_openai import ChatOpenAI
from langchain.agents import AgentType

async def test_with_sample_data():
    """Test pandas agent with sample data"""
    print("=== Testing Pandas Agent with Sample Data ===")
    
    # Create sample data
    sample_data = {
        'employee_id': [1, 2, 3, 4, 5],
        'name': ['<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>', '<PERSON>'],
        'department': ['Engineering', 'Sales', 'Marketing', 'Engineering', 'HR'],
        'salary': [75000, 65000, 60000, 80000, 55000],
        'years_experience': [5, 3, 2, 7, 1]
    }
    df = pd.DataFrame(sample_data)
    
    print(f"Created sample dataframe: {df.shape[0]} rows, {df.shape[1]} columns")
    print(f"Columns: {list(df.columns)}")
    print(f"Sample data:\n{df}")
    
    # Create the agent
    try:
        agent = create_pandas_dataframe_agent(
            ChatOpenAI(temperature=0, model="gpt-4o-mini"),
            df,
            verbose=True,
            agent_type=AgentType.OPENAI_FUNCTIONS
        )
        print("\nAgent created successfully!")
        
        # Test queries
        queries = [
            "How many employees are there?",
            "What is the average salary?", 
            "Which department has the most employees?",
            "Who has the highest salary?",
            "What is the total salary for Engineering department?"
        ]
        
        for query in queries:
            print(f"\n{'='*50}")
            print(f"QUERY: {query}")
            print(f"{'='*50}")
            
            try:
                result = agent.invoke(query)
                if isinstance(result, dict) and 'output' in result:
                    answer = result['output']
                else:
                    answer = str(result)
                print(f"ANSWER: {answer}")
                
            except Exception as e:
                print(f"ERROR: {e}")
                
    except Exception as e:
        print(f"Failed to create agent: {e}")

async def test_with_your_file():
    """Test with your DS salaries file"""
    print("\n=== Testing with Your DS Salaries File ===")
    
    file_path = r"C:\Users\<USER>\Documents\AgenticRag\Python\Test files\ds_salaries.csv"
    
    try:
        # Load the data
        df = pd.read_csv(file_path)
        print(f"Loaded CSV: {df.shape[0]} rows, {df.shape[1]} columns")
        print(f"Columns: {list(df.columns)}")
        print(f"First 3 rows:\n{df.head(3)}")
        
        # Create agent
        agent = create_pandas_dataframe_agent(
            ChatOpenAI(temperature=0, model="gpt-4o-mini"),
            df,
            verbose=True,
            agent_type=AgentType.OPENAI_FUNCTIONS
        )
        print("\nAgent created for DS salaries data!")
        
        # Test queries specific to your data
        queries = [
            "How many records are in this dataset?",
            "What are all the column names?",
            "What is the average salary_in_usd?",
            "What are the top 5 most common job titles?",
            "What years are covered in this dataset?",
            "Which experience level has the highest average salary?"
        ]
        
        for query in queries:
            print(f"\n{'='*50}")
            print(f"QUERY: {query}")
            print(f"{'='*50}")
            
            try:
                result = agent.invoke(query)
                if isinstance(result, dict) and 'output' in result:
                    answer = result['output']
                else:
                    answer = str(result)
                print(f"ANSWER: {answer}")
                
            except Exception as e:
                print(f"ERROR: {e}")
                
    except Exception as e:
        print(f"Error with DS salaries file: {e}")

def test_basic_data_analysis():
    """Basic data analysis without agent"""
    print("\n=== Basic Data Analysis (No Agent) ===")
    
    file_path = r"C:\Users\<USER>\Documents\AgenticRag\Python\Test files\ds_salaries.csv"
    
    try:
        df = pd.read_csv(file_path)
        
        print(f"Dataset shape: {df.shape}")
        print(f"Columns: {list(df.columns)}")
        print("\nBasic statistics:")
        print(f"- Average salary: ${df['salary_in_usd'].mean():,.2f}")
        print(f"- Median salary: ${df['salary_in_usd'].median():,.2f}")
        print(f"- Salary range: ${df['salary_in_usd'].min():,.2f} - ${df['salary_in_usd'].max():,.2f}")
        
        print(f"\nTop 5 job titles:")
        print(df['job_title'].value_counts().head())
        
        print(f"\nExperience levels:")
        print(df['experience_level'].value_counts())
        
        print(f"\nCompany sizes:")
        print(df['company_size'].value_counts())
        
    except Exception as e:
        print(f"Error: {e}")

async def main():
    """Run all tests"""
    print("Starting Direct Pandas + LangChain Tests...")
    print("This will test if your LangChain pandas agent works correctly.\n")
    
    # Test 1: Basic data analysis
    test_basic_data_analysis()
    
    # Test 2: Sample data with agent
    await test_with_sample_data()
    
    # Test 3: Your file with agent  
    await test_with_your_file()
    
    print("\n" + "="*60)
    print("TESTING COMPLETE!")
    print("="*60)
    print("If you see answers above, your Excel analyzer is working!")
    print("You can now integrate it into your main application.")

if __name__ == "__main__":
    asyncio.run(main())