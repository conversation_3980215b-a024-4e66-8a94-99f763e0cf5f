from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

import pytest
from tests.unit.test_logging_capture import with_unit_test_logging

class TestExcelAnalyzerSimple:
    
    @with_unit_test_logging
    def test_prompt_registration(self):
        """Test that Excel_Analyzer_Prompt is properly registered"""
        from managers.manager_prompts import PromptManager
        
        prompt_manager = PromptManager.get_instance()
        assert "Excel_Analyzer_Prompt" in prompt_manager.prompts
        
        prompt = prompt_manager.prompts["Excel_Analyzer_Prompt"]
        assert "Excel" in prompt and "analyst" in prompt
    
    @with_unit_test_logging 
    def test_langchain_pandas_import(self):
        """Test that LangChain pandas tools can be imported"""
        try:
            from langchain_experimental.agents.agent_toolkits import create_pandas_dataframe_agent
            from langchain_openai import ChatOpenAI
            from langchain.agents import AgentType
            
            # Basic import test passed
            assert create_pandas_dataframe_agent is not None
            assert ChatOpenAI is not None
            assert AgentType.OPENAI_FUNCTIONS is not None
            
        except ImportError as e:
            pytest.fail(f"Failed to import LangChain pandas components: {e}")
    
    @with_unit_test_logging
    def test_pandas_functionality(self):
        """Test basic pandas functionality"""
        import pandas as pd
        
        # Create sample data
        sample_data = {
            'name': ['Alice', 'Bob', 'Charlie'],
            'age': [25, 30, 35], 
            'salary': [50000, 60000, 70000]
        }
        df = pd.DataFrame(sample_data)
        
        assert len(df) == 3
        assert list(df.columns) == ['name', 'age', 'salary']
        assert df['age'].mean() == 30.0