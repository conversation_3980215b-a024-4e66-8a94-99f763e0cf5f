"""
AskZaira Dashboard Endpoint Static Module
Contains: Static file serving methods and dashboard home page
"""

from imports import *

import os
from os import path as os_path
from aiohttp import web
from typing import Dict, Any, Optional

from endpoints.dashboard_endpoint_core import get_dashboard_endpoint_core, get_user_from_session
from endpoints.dashboard.template_loader import get_dashboard_template_loader
from etc.helper_functions import create_html_out
from managers.manager_logfire import LogFire

class DashboardEndpointStatic:
    """
    Static file serving functionality for dashboard.
    
    Handles:
    - Main dashboard page with authentication
    - CSS file serving with caching
    - JavaScript file serving with caching  
    - HTML template serving for AJAX requests
    """
    
    def __init__(self):
        self.core = get_dashboard_endpoint_core()
        LogFire.log("INIT", "Dashboard static serving module initialized")

    async def dashboard_home(self, request: web.Request) -> web.Response:
        """Main dashboard page with authentication and role-based access"""
        try:
            # Version validation - must exactly match "1.0"
            # Handle both JSON and form data
            version = ""
            try:
                LogFire.log("DEBUG", f"Dashboard request content-type: '{request.content_type}', method: {request.method}", severity="debug")
                
                if request.content_type and 'application/json' in request.content_type:
                    data = await request.json()
                    version = data.get("version", "")
                    LogFire.log("DEBUG", f"Dashboard received JSON data: {data}", severity="debug")
                else:
                    # This is likely form data
                    data = await request.post()
                    version = data.get("version", "").strip()
                    LogFire.log("DEBUG", f"Dashboard received form data keys: {list(data.keys()) if data else 'None'} (content-type: {request.content_type})", severity="debug")
                    LogFire.log("DEBUG", f"Dashboard version from form: '{version}' (type: {type(version)}, repr: {repr(version)})", severity="debug")
                    
            except Exception as e:
                LogFire.log("ERROR", f"Error parsing dashboard request data: {e}", severity="error")
                return web.Response(text="Invalid request format", status=400)
            
            LogFire.log("DEBUG", f"Dashboard final version check: '{version}' == '1.0' ? {version == '1.0'}", severity="debug")
            if version != "1.0":
                LogFire.log("ERROR", f"Invalid version provided to dashboard: '{version}' (expected '1.0')", severity="error")
                return web.Response(text="Invalid version", status=400)
            
            # Get user info from session
            user_info = get_user_from_session(request)
            
            # Generate page content placeholder - Overview will be loaded via JavaScript
            try:
                template_loader = get_dashboard_template_loader()
                content = template_loader.load_template('welcome.html')
            except Exception:
                # Fallback if template fails
                content = "<p>Loading dashboard...</p>"
            
            # Add user information script to inject permissions
            user_script = f"""
            <script>
                // Set current user information for permission handling
                window.currentUser = {{
                    username: '{user_info.get('username', '') if user_info else ''}',
                    is_system_user: {str(user_info.get('is_system_user', False)).lower() if user_info else 'false'},
                    rank: '{user_info.get('rank', 'NONE') if user_info else 'NONE'}',
                    user_guid: '{user_info.get('user_guid', '') if user_info else ''}'
                }};
                console.log('User permissions loaded:', window.currentUser);
            </script>
            """
            
            # Combine content with user script
            enhanced_content = user_script + content
            
            # Use the new template system
            html = create_html_out("dashboard", enhanced_content)
            
            return web.Response(
                text=html,
                content_type='text/html',
                headers={'Cache-Control': 'no-cache'}
            )
            
        except Exception as e:
            LogFire.log("ERROR", f"Dashboard error: {str(e)}")
            return web.Response(
                text=f"<html><body><h1>Dashboard Error</h1><p>{str(e)}</p></body></html>",
                content_type='text/html',
                status=500
            )
    
    async def serve_static_css(self, request: web.Request) -> web.Response:
        """Serve CSS static files"""
        try:
            filename = request.match_info['filename']
            
            # Security check - only allow CSS files
            if not filename.endswith('.css'):
                raise web.HTTPNotFound()
            
            css_path = os_path.join(os_path.dirname(__file__), 'dashboard', 'static', 'css', filename)
            
            if not os_path.exists(css_path):
                raise web.HTTPNotFound()
            
            with open(css_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return web.Response(
                text=content,
                content_type='text/css',
                headers={'Cache-Control': 'public, max-age=86400'}  # Cache for 1 day
            )
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to serve CSS file {filename}: {str(e)}")
            raise web.HTTPNotFound()
    
    async def serve_static_js(self, request: web.Request) -> web.Response:
        """Serve JavaScript static files"""
        try:
            filename = request.match_info['filename']
            
            # Security check - only allow JS files
            if not filename.endswith('.js'):
                raise web.HTTPNotFound()
            
            js_path = os_path.join(os_path.dirname(__file__), 'dashboard', 'static', 'js', filename)
            
            if not os_path.exists(js_path):
                raise web.HTTPNotFound()
            
            with open(js_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            return web.Response(
                text=content,
                content_type='application/javascript',
                headers={'Cache-Control': 'public, max-age=86400'}  # Cache for 1 day
            )
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to serve JS file {filename}: {str(e)}")
            raise web.HTTPNotFound()
    
    async def serve_template(self, request: web.Request) -> web.Response:
        """Serve HTML templates for JavaScript use"""
        try:
            template_name = request.match_info['template_name']
            
            # Validate template name to prevent directory traversal
            if '..' in template_name or '/' in template_name or '\\' in template_name:
                raise web.HTTPNotFound()
            
            # Ensure .html extension
            if not template_name.endswith('.html'):
                template_name += '.html'
            
            template_loader = get_dashboard_template_loader()
            
            # Get query parameters for template context
            context = dict(request.query)
            
            # Render template with context if provided
            if context:
                content = template_loader.render_template(template_name, **context)
            else:
                content = template_loader.load_template(template_name)
            
            return web.Response(
                text=content,
                content_type='text/html',
                headers={
                    'Cache-Control': 'public, max-age=300',  # 5 minute cache
                    'Access-Control-Allow-Origin': '*',  # Allow CORS for fetch
                }
            )
            
        except FileNotFoundError:
            raise web.HTTPNotFound()
        except Exception as e:
            LogFire.log("ERROR", f"Failed to serve template {template_name}: {str(e)}")
            raise web.HTTPInternalServerError()

# Create global instance
_dashboard_endpoint_static = DashboardEndpointStatic()

def get_dashboard_endpoint_static() -> DashboardEndpointStatic:
    """Get the global Dashboard endpoint static instance"""
    return _dashboard_endpoint_static