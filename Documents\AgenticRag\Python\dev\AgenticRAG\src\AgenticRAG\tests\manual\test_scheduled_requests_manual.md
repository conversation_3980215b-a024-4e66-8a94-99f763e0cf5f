# Manual Testing Guide for Scheduled Tasks

This guide provides step-by-step manual testing procedures for the ScheduledZairaTask system.

## Prerequisites

1. AgenticRAG system running with PostgreSQL database
2. IMAP OAuth configuration (optional, for full IMAP testing)
3. Access to the main_loop.py interface or Discord/Slack bot

## Test Scenarios

### 1. Starting IMAP IDLE Monitoring

**Objective**: Test starting persistent email monitoring

**Steps**:
1. Open main_loop.py or connect to bot
2. Input: `"start IMAP IDLE"`
3. Expected response: "IMAP IDLE monitoring started successfully. New emails will be checked every 30 minutes."
4. Verify task persists by restarting server and checking if it continues

**Expected Results**:
- ✅ Confirmation message received
- ✅ Task appears in scheduled tasks list
- ✅ Task survives server restart
- ✅ Email checking occurs every 30 minutes

### 2. Listing Active Scheduled Tasks

**Objective**: Test viewing all active scheduled tasks

**Steps**:
1. Input: `"list scheduled tasks"`
2. Expected response: List of active tasks with IDs and descriptions

**Expected Results**:
- ✅ Shows all active tasks for the user
- ✅ Displays task GUIDs (shortened), schedules, and next execution times
- ✅ Provides instructions for cancellation

### 3. Getting Detailed Task Status

**Objective**: Test getting comprehensive task information

**Steps**:
1. Input: `"show task status"` or `"get scheduled task details"`
2. Expected response: Detailed information about each task

**Expected Results**:
- ✅ Shows full task details including intervals, types, and last execution
- ✅ Displays runtime status and messages
- ✅ Shows creation dates and next execution times

### 4. Cancelling Specific Tasks

**Objective**: Test cancelling individual scheduled tasks

**Steps**:
1. First get task list: `"list scheduled tasks"`
2. Note a task GUID from the list
3. Input: `"cancel scheduled task [scheduled_guid]"` (replace with actual GUID)
4. Expected response: Confirmation of cancellation

**Expected Results**:
- ✅ Task is successfully cancelled
- ✅ Task no longer appears in active task list
- ✅ Task execution stops immediately
- ✅ Cancellation persists after server restart

### 5. Cancelling All Tasks

**Objective**: Test bulk cancellation of all scheduled tasks

**Steps**:
1. Input: `"cancel all scheduled tasks"`
2. Expected response: Confirmation with count of cancelled tasks

**Expected Results**:
- ✅ All user's active tasks are cancelled
- ✅ Confirmation shows correct count
- ✅ No active tasks remain for the user

### 6. Server Restart Recovery

**Objective**: Test persistence across server restarts

**Steps**:
1. Start IMAP IDLE monitoring: `"start IMAP IDLE"`
2. Verify task is active: `"list scheduled tasks"`
3. Restart the AgenticRAG server
4. After restart, check tasks again: `"list scheduled tasks"`

**Expected Results**:
- ✅ Task appears in list before restart
- ✅ Task is automatically recovered after restart
- ✅ Task continues executing on schedule
- ✅ No data loss or corruption

### 7. Multiple User Isolation

**Objective**: Test that users only see/manage their own tasks

**Steps**:
1. Create scheduled task with User A
2. Switch to User B
3. List scheduled tasks as User B
4. Try to cancel User A's task as User B

**Expected Results**:
- ✅ User B sees only their own tasks
- ✅ User B cannot cancel User A's tasks
- ✅ Task isolation is properly maintained

### 8. Error Handling

**Objective**: Test system behavior with invalid inputs

**Steps**:
1. Try invalid cancellation: `"cancel scheduled task invalid_guid"`
2. Try cancelling when no tasks exist: `"cancel all scheduled tasks"`
3. Try listing tasks when none exist: `"list scheduled tasks"`

**Expected Results**:
- ✅ Clear error messages for invalid operations
- ✅ System remains stable with invalid inputs
- ✅ Helpful guidance provided for corrections

## Performance Tests

### 9. High Volume Task Management

**Objective**: Test system with multiple scheduled tasks

**Steps**:
1. Create 5-10 different scheduled tasks
2. List all tasks and verify performance
3. Cancel tasks individually and in bulk
4. Monitor system resource usage

**Expected Results**:
- ✅ System handles multiple tasks efficiently
- ✅ Database operations remain fast
- ✅ Memory usage stays reasonable
- ✅ No performance degradation

### 10. Long-Running Task Verification

**Objective**: Test tasks running over extended periods

**Steps**:
1. Create a task with very short interval (e.g., every 2 minutes)
2. Let it run for 30+ minutes
3. Monitor execution logs
4. Verify consistent execution

**Expected Results**:
- ✅ Task executes consistently over time
- ✅ No memory leaks or resource accumulation
- ✅ Timing remains accurate
- ✅ Logging remains stable

## Troubleshooting

**Common Issues**:
1. **Database Connection**: Ensure PostgreSQL is running and accessible
2. **OAuth Configuration**: IMAP tasks may fail without proper OAuth setup
3. **Permissions**: Verify user has proper permission levels
4. **Logging**: Check logs for detailed error information

**Log Locations**:
- Application logs: Check LogFire output
- Database logs: PostgreSQL logs for persistence issues
- Task execution logs: Individual task execution results

## Test Completion Checklist

- [ ] IMAP IDLE monitoring starts successfully
- [ ] Tasks appear in listings correctly
- [ ] Detailed status information is accurate
- [ ] Individual task cancellation works
- [ ] Bulk cancellation functions properly
- [ ] Server restart recovery is successful
- [ ] User isolation is maintained
- [ ] Error handling is graceful
- [ ] Performance is acceptable
- [ ] Long-running tasks remain stable

## Notes

- All tests should be performed in a test environment first
- Monitor system resources during testing
- Document any unexpected behavior or performance issues
- Test with both local and Docker deployments if applicable