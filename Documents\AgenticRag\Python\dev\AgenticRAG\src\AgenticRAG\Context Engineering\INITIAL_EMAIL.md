## FEATURE:

I want to include a new tool to mmy mail agent that allows me to read mails with IMap IDLE. The new tool needs to integrate in the codebase. 

## EXAMPLES:

In the `Context Engineering/examples/` folder, there is a example file called imap_reading_mails_codeexample.py for you to read to understand what the example is all about and also how to structure the basics of the code.



Don't copy any of these examples directly, it is for a different project entirely. But use this as inspiration and for best practices.


## DOCUMENTATION:

ALways read this first:

-task_email_writer.py 
- IMap-tools: https://pypi.org/project/imap-tools/ (important!)
-  LangChain documentation: https://python.langchain.com/docs/get_started/introduction


## OTHER CONSIDERATIONS:


- This is a Python project.
- You need to integrate this feature in the email agent in the agents/ folder.
