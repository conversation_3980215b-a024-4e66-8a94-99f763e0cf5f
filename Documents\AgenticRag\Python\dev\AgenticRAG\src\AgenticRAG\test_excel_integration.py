#!/usr/bin/env python3
"""
Integration test for Excel Analyzer task through the main system.
This tests your task as part of the full supervisor hierarchy.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

async def test_excel_via_supervisor():
    """Test Excel Analyzer task through the supervisor system"""
    print("🔗 Testing Excel Analyzer via Supervisor Integration")
    print("=" * 60)
    
    try:
        # Setup full environment (minimal version)
        print("Setting up full environment...")
        await setup_full_environment()
        
        # Get the top level supervisor
        from managers.manager_supervisors import SupervisorManager
        top_supervisor = SupervisorManager.get_supervisor("top_level_supervisor")
        
        if not top_supervisor:
            print("❌ Top level supervisor not found. Creating it...")
            from tasks.task_top_level_supervisor import create_top_level_supervisor
            top_supervisor = await create_top_level_supervisor()
        
        print("✅ Top level supervisor ready")
        
        # Test prompts that should route to Excel Analyzer
        excel_prompts = [
            "Analyze the Excel data and show me sales trends",
            "Create a report from the spreadsheet data",
            "Query the Excel file for customer information",
            "Generate insights from the Excel database",
            "Show me the top performers from the Excel data"
        ]
        
        # Test each prompt
        for i, prompt in enumerate(excel_prompts, 1):
            print(f"\n🔍 Integration Test {i}")
            print(f"Prompt: {prompt}")
            print("-" * 50)
            
            try:
                # Create test state
                state = await create_integration_test_state(prompt)
                
                # Call supervisor
                print("Calling top level supervisor...")
                result = await asyncio.wait_for(
                    top_supervisor.call_supervisor_with_state(state), 
                    timeout=60.0
                )
                
                print("✅ Supervisor call completed")
                print(f"Result: {result.get('result', 'No result')}")
                print(f"Call trace: {result.get('call_trace', [])}")
                
                # Check if Excel task was called
                call_trace = result.get('call_trace', [])
                excel_called = any('excel' in trace.lower() for trace in call_trace)
                
                if excel_called:
                    print("✅ Excel Analyzer task was called!")
                else:
                    print("⚠️  Excel Analyzer task was not called")
                    print("This might be expected depending on the prompt routing logic")
                
            except asyncio.TimeoutError:
                print("❌ Integration test timed out")
            except Exception as e:
                print(f"❌ Integration test failed: {str(e)}")
            
            print("-" * 50)
        
        print("\n🎉 Integration testing completed!")
        
    except Exception as e:
        print(f"❌ Integration test setup failed: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

async def setup_full_environment():
    """Setup the full environment for integration testing"""
    try:
        # Basic setup
        from imports import *
        await LogFire.setup()
        ZairaSettings.IsDebugMode = True
        Globals.set_debug(ZairaSettings.IsDebugMode)
        
        # Setup core managers
        from managers.manager_supervisors import SupervisorManager
        from managers.manager_users import ZairaUserManager
        from managers.manager_prompts import PromptManager
        from managers.manager_postgreSQL import PostgreSQLManager
        from managers.manager_qdrant import QDrantManager
        from managers.manager_retrieval import RetrievalManager
        
        print("Setting up core managers...")
        await PostgreSQLManager.create_database("vectordb")
        await PostgreSQLManager.connect_to_database("vectordb", use_pool=True, min_size=2, max_size=10)
        
        await QDrantManager.setup()
        await RetrievalManager.setup()
        await SupervisorManager.setup()
        await PromptManager.loadDefaultPrompts()
        
        # Create test user
        print("Creating test user...")
        from userprofiles.permission_levels import PERMISSION_LEVELS
        test_user = await ZairaUserManager.add_user(
            "IntegrationTestUser", 
            PERMISSION_LEVELS.ADMIN, 
            ZairaUserManager.create_guid(), 
            ZairaUserManager.create_guid()
        )
        
        # Setup minimal index
        print("Setting up minimal index...")
        from pathlib import Path
        from etc.parsers import create_parsers
        from etc.setup import loadEmbedding
        
        DATA_DIR = Path("assets/documents")
        PERSIST_DIR = Path("data")
        parsers = create_parsers()
        
        index = await loadEmbedding(DATA_DIR=DATA_DIR, PERSIST_DIR=PERSIST_DIR, parsers=parsers)
        Globals.set_index(index)
        
        print("✅ Full environment setup complete")
        return test_user
        
    except Exception as e:
        print(f"❌ Full environment setup failed: {str(e)}")
        raise

async def create_integration_test_state(prompt: str):
    """Create test state for integration testing"""
    from managers.manager_supervisors import SupervisorTaskState
    from langchain_core.messages import HumanMessage
    from managers.manager_users import ZairaUserManager
    
    # Get test user
    users = await ZairaUserManager.get_all_users()
    test_user = users[0] if users else None
    user_guid = str(test_user.GUID) if test_user else ""
    
    # Create state
    state = SupervisorTaskState(
        original_input=prompt,
        user_guid=user_guid,
        messages=[HumanMessage(content=prompt)],
        call_trace=["integration_test: start"]
    )
    
    return state

async def test_excel_task_registration():
    """Test if the Excel task is properly registered in the system"""
    print("📋 Testing Excel Task Registration")
    print("=" * 40)
    
    try:
        # Setup
        from imports import *
        await LogFire.setup()
        ZairaSettings.IsDebugMode = True
        Globals.set_debug(ZairaSettings.IsDebugMode)
        
        from managers.manager_supervisors import SupervisorManager
        await SupervisorManager.setup()
        
        # Check if task is registered
        print("Checking task registration...")
        
        # Import the task creation function
        from tasks.processing.task_excel_analyzer import create_task_sql_excel_analyzer
        
        # Create the task
        excel_task = await create_task_sql_excel_analyzer()
        print(f"✅ Task created: {excel_task.name}")
        
        # Check if it's in the supervisor manager
        all_tasks = SupervisorManager.get_instance()._all_tasks
        
        if excel_task.name in all_tasks:
            print(f"✅ Task '{excel_task.name}' is registered in SupervisorManager")
        else:
            print(f"⚠️  Task '{excel_task.name}' is NOT registered in SupervisorManager")
        
        # List all registered tasks
        print(f"\nAll registered tasks ({len(all_tasks)}):")
        for task_name in sorted(all_tasks.keys()):
            print(f"  - {task_name}")
        
        # Check supervisors
        all_supervisors = SupervisorManager.get_instance()._supervisors
        print(f"\nAll registered supervisors ({len(all_supervisors)}):")
        for supervisor_name in sorted(all_supervisors.keys()):
            print(f"  - {supervisor_name}")
        
    except Exception as e:
        print(f"❌ Registration test failed: {str(e)}")
        import traceback
        print(f"Traceback: {traceback.format_exc()}")

if __name__ == "__main__":
    print("Excel Analyzer Integration Testing")
    print("Choose testing mode:")
    print("1. Full integration test via supervisor")
    print("2. Task registration test")
    print("3. Both tests")
    
    try:
        choice = input("Enter choice (1-3): ").strip()
        
        if choice == "1":
            asyncio.run(test_excel_via_supervisor())
        elif choice == "2":
            asyncio.run(test_excel_task_registration())
        elif choice == "3":
            asyncio.run(test_excel_task_registration())
            print("\n" + "="*60 + "\n")
            asyncio.run(test_excel_via_supervisor())
        else:
            print("Invalid choice. Running registration test...")
            asyncio.run(test_excel_task_registration())
            
    except KeyboardInterrupt:
        print("\n👋 Testing interrupted by user")
    except Exception as e:
        print(f"❌ Testing failed: {str(e)}")
        sys.exit(1)
