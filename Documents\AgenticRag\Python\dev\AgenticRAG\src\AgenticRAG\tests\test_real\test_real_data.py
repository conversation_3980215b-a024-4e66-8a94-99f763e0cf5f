#!/usr/bin/env python3
"""
Real Data Analysis System Integration Tests

This module contains comprehensive tests for the AskZaira data analysis
and retrieval system, including data exploration, analysis workflows,
and knowledge base interactions.

Tests cover:
- Data exploration and discovery
- "Tell me about your data" functionality
- Knowledge base analysis and retrieval
- Data source validation and verification
- Information synthesis and reporting
- Data quality assessment

All tests use the full AskZaira system to ensure production-like behavior.
"""

from tests.test_real.base_real_test import BaseRealTest, requires_openai_api
import pytest
import asyncio
from typing import Dict, Any, List


@pytest.mark.asyncio
class TestRealData(BaseRealTest):
    """
    Test suite for real data analysis system functionality.
    
    This class tests the complete data analysis pipeline from discovery to reporting,
    using the full AskZaira system with automated bot responses.
    """
    
    # Data analysis test configuration
    DATA_TEST_QUERIES = [
        "tell me about your data",
        "analyze the data you have access to",
        "provide a summary of your available data sources",
        "what information do you have stored",
        "describe your knowledge base content",
        "analyze the quality of your data",
        "what types of documents are in your database"
    ]
    
    DATA_PIPELINE_STEPS = [
        "data_analysis",
        "data_retrieval",
        "rag_search",
        "knowledge_base"
    ]
    
    EXPECTED_DATA_OUTPUTS = [
        "data_summary",
        "analysis_report",
        "information_overview"
    ]
    
    async def setup_data_system(self) -> bool:
        """Set up the data analysis system for testing"""
        return await self.setup_real_system(
            test_category="data_analysis",
            create_test_docs=True,
            custom_docs=[
                ("data_sources.txt", "Available data sources and their descriptions for analysis"),
                ("data_catalog.txt", "Comprehensive data catalog with source metadata and access information"),
                ("analysis_templates.txt", "Templates for data analysis and reporting procedures"),
                ("knowledge_base_info.txt", "Information about knowledge base structure and content"),
                ("data_quality_metrics.txt", "Data quality assessment criteria and metrics"),
                ("research_papers.txt", "Academic research papers on data analysis methodologies"),
                ("technical_documentation.txt", "Technical documentation for data processing systems"),
                ("business_reports.txt", "Historical business reports and analytics"),
                ("user_manuals.txt", "User manuals and system documentation"),
                ("data_schemas.txt", "Database schemas and data structure definitions")
            ]
        )
    
    @requires_openai_api()
    async def test_tell_me_about_your_data_basic(self):
        """Test basic 'tell me about your data' functionality"""
        # Setup
        setup_success = await self.setup_data_system()
        if not setup_success:
            pytest.skip("Could not initialize data analysis system")
        
        user = await self.create_test_user(email="<EMAIL>")
        bot = self.create_test_bot()
        
        # Test basic data inquiry
        query = "tell me about your data"
        result = await self.execute_and_monitor_task(user, query, bot, timeout=25)
        
        # Verify data analysis occurred
        self.assert_task_success(result, max_execution_time=25)
        
        # Check that data analysis occurred
        data_analysis_found = False
        for task_detail in result["task_details"]:
            call_trace = task_detail.get("call_trace", [])
            call_trace_str = " ".join(str(call).lower() for call in call_trace)
            
            if any(keyword in call_trace_str for keyword in ["data", "analysis", "information", "knowledge"]):
                data_analysis_found = True
                break
        
        assert data_analysis_found, "Data analysis did not occur"
        LogFire.log("DEBUG", f"[{self.test_name}] Basic data inquiry test passed")
    
    @requires_openai_api()
    async def test_data_source_analysis(self):
        """Test comprehensive data source analysis"""
        # Setup
        setup_success = await self.setup_data_system()
        if not setup_success:
            pytest.skip("Could not initialize data analysis system")
        
        user = await self.create_test_user(email="<EMAIL>")
        bot = self.create_test_bot()
        
        # Test data source analysis
        query = "analyze the data you have access to and provide a detailed summary"
        result = await self.execute_and_monitor_task(user, query, bot, timeout=30)
        
        # Verify task success
        self.assert_task_success(result, max_execution_time=30)
        
        # Check for data source analysis components
        analysis_components = []
        for task_detail in result["task_details"]:
            call_trace = task_detail.get("call_trace", [])
            call_trace_str = " ".join(str(call).lower() for call in call_trace)
            
            if "analyze" in call_trace_str:
                analysis_components.append("analysis_component")
            if "data" in call_trace_str:
                analysis_components.append("data_component")
            if "summary" in call_trace_str:
                analysis_components.append("summary_component")
        
        assert len(analysis_components) > 0, "Data source analysis components not found"
        LogFire.log("DEBUG", f"[{self.test_name}] Data source analysis test passed")
    
    @requires_openai_api()
    async def test_knowledge_base_exploration(self):
        """Test knowledge base exploration and content discovery"""
        # Setup
        setup_success = await self.setup_data_system()
        if not setup_success:
            pytest.skip("Could not initialize data analysis system")
        
        user = await self.create_test_user(email="<EMAIL>")
        bot = self.create_test_bot()
        
        # Test knowledge base exploration
        query = "describe your knowledge base content and available information"
        result = await self.execute_and_monitor_task(user, query, bot, timeout=30)
        
        # Verify task success
        self.assert_task_success(result, max_execution_time=30)
        
        # Check for knowledge base exploration indicators
        kb_indicators = []
        for task_detail in result["task_details"]:
            call_trace = task_detail.get("call_trace", [])
            call_trace_str = " ".join(str(call).lower() for call in call_trace)
            
            if "knowledge" in call_trace_str:
                kb_indicators.append("knowledge_base")
            if "content" in call_trace_str:
                kb_indicators.append("content_analysis")
            if "describe" in call_trace_str:
                kb_indicators.append("description")
        
        assert len(kb_indicators) >= 1, f"Knowledge base exploration not sufficient: {kb_indicators}"
        LogFire.log("DEBUG", f"[{self.test_name}] Knowledge base exploration test passed")
    
    @requires_openai_api()
    async def test_data_pipeline_verification(self):
        """Test the complete data analysis pipeline"""
        # Setup
        setup_success = await self.setup_data_system()
        if not setup_success:
            pytest.skip("Could not initialize data analysis system")
        
        user = await self.create_test_user(email="<EMAIL>")
        bot = self.create_test_bot()
        
        # Test data pipeline
        query = "what information do you have stored and how is it organized"
        result = await self.execute_and_monitor_task(user, query, bot, timeout=35)
        
        # Verify pipeline executed
        self.assert_task_success(result, max_execution_time=35)
        
        # Verify pipeline components
        pipeline_components = []
        for task_detail in result["task_details"]:
            call_trace = task_detail.get("call_trace", [])
            call_trace_str = " ".join(str(call).lower() for call in call_trace)
            
            for step in self.DATA_PIPELINE_STEPS:
                if step.lower() in call_trace_str and step not in pipeline_components:
                    pipeline_components.append(step)
        
        assert len(pipeline_components) >= 1, f"Data pipeline components not found: {pipeline_components}"
        LogFire.log("DEBUG", f"[{self.test_name}] Data pipeline verification test passed")
        
        return {
            "pipeline_components": pipeline_components,
            "total_tasks": len(user.my_requests)
        }
    
    @requires_openai_api()
    async def test_data_quality_assessment(self):
        """Test data quality assessment capabilities"""
        # Setup
        setup_success = await self.setup_data_system()
        if not setup_success:
            pytest.skip("Could not initialize data analysis system")
        
        user = await self.create_test_user(email="<EMAIL>")
        bot = self.create_test_bot()
        
        # Test data quality assessment
        query = "analyze the quality of your data and identify any issues"
        result = await self.execute_and_monitor_task(user, query, bot, timeout=25)
        
        # Verify task success
        self.assert_task_success(result, max_execution_time=25)
        
        # Check for quality assessment indicators
        quality_indicators = []
        for task_detail in result["task_details"]:
            call_trace = task_detail.get("call_trace", [])
            call_trace_str = " ".join(str(call).lower() for call in call_trace)
            
            if "quality" in call_trace_str:
                quality_indicators.append("quality_analysis")
            if "analyze" in call_trace_str:
                quality_indicators.append("analysis")
            if "issues" in call_trace_str:
                quality_indicators.append("issue_detection")
        
        assert len(quality_indicators) >= 1, f"Data quality assessment not found: {quality_indicators}"
        LogFire.log("DEBUG", f"[{self.test_name}] Data quality assessment test passed")
        
        return {
            "quality_indicators": quality_indicators,
            "execution_time": result["execution_time"]
        }
    
    @requires_openai_api()
    async def test_document_type_analysis(self):
        """Test analysis of different document types in the knowledge base"""
        # Setup
        setup_success = await self.setup_data_system()
        if not setup_success:
            pytest.skip("Could not initialize data analysis system")
        
        user = await self.create_test_user(email="<EMAIL>")
        bot = self.create_test_bot()
        
        # Test document type analysis
        query = "what types of documents are in your database and how are they categorized"
        result = await self.execute_and_monitor_task(user, query, bot, timeout=30)
        
        # Verify task success
        self.assert_task_success(result, max_execution_time=30)
        
        # Check for document analysis indicators
        doc_indicators = []
        for task_detail in result["task_details"]:
            call_trace = task_detail.get("call_trace", [])
            call_trace_str = " ".join(str(call).lower() for call in call_trace)
            
            if "document" in call_trace_str:
                doc_indicators.append("document_analysis")
            if "type" in call_trace_str:
                doc_indicators.append("type_classification")
            if "categorized" in call_trace_str:
                doc_indicators.append("categorization")
        
        assert len(doc_indicators) >= 1, f"Document type analysis not found: {doc_indicators}"
        LogFire.log("DEBUG", f"[{self.test_name}] Document type analysis test passed")
        
        return {
            "doc_indicators": doc_indicators,
            "execution_time": result["execution_time"]
        }
    
    @requires_openai_api()
    async def test_information_synthesis(self):
        """Test information synthesis and reporting capabilities"""
        # Setup
        setup_success = await self.setup_data_system()
        if not setup_success:
            pytest.skip("Could not initialize data analysis system")
        
        user = await self.create_test_user(email="<EMAIL>")
        bot = self.create_test_bot()
        
        # Test information synthesis scenarios
        synthesis_scenarios = [
            ("provide a comprehensive overview of all available data", "comprehensive_overview"),
            ("create a summary report of your knowledge base", "summary_report"),
            ("synthesize information from multiple data sources", "multi_source_synthesis")
        ]
        
        successful_synthesis = 0
        
        for query, synthesis_type in synthesis_scenarios:
            LogFire.log("DEBUG", f"[{self.test_name}] Testing {synthesis_type}")
            
            result = await self.execute_and_monitor_task(user, query, bot, timeout=25)
            
            # Check if synthesis occurred
            synthesis_found = False
            for task_detail in result["task_details"]:
                call_trace = task_detail.get("call_trace", [])
                call_trace_str = " ".join(str(call).lower() for call in call_trace)
                
                if any(keyword in call_trace_str for keyword in ["synthesis", "overview", "summary", "report"]):
                    synthesis_found = True
                    break
            
            if synthesis_found:
                successful_synthesis += 1
                LogFire.log("DEBUG", f"[{self.test_name}] {synthesis_type} completed successfully")
            else:
                LogFire.log("DEBUG", f"[{self.test_name}] {synthesis_type} synthesis failed")
        
        # Verify synthesis capabilities
        synthesis_rate = successful_synthesis / len(synthesis_scenarios)
        assert synthesis_rate >= 0.6, f"Information synthesis rate too low: {synthesis_rate:.2f}"
        
        LogFire.log("DEBUG", f"[{self.test_name}] Information synthesis test passed")
        
        return {
            "successful_synthesis": successful_synthesis,
            "total_scenarios": len(synthesis_scenarios),
            "synthesis_rate": synthesis_rate
        }
    
    @requires_openai_api()
    async def test_data_retrieval_accuracy(self):
        """Test accuracy of data retrieval and search functionality"""
        # Setup
        setup_success = await self.setup_data_system()
        if not setup_success:
            pytest.skip("Could not initialize data analysis system")
        
        user = await self.create_test_user(email="<EMAIL>")
        bot = self.create_test_bot()
        
        # Test specific data retrieval queries
        retrieval_queries = [
            ("find information about data sources", "data_sources"),
            ("search for technical documentation", "technical_docs"),
            ("locate business reports", "business_reports"),
            ("retrieve user manuals", "user_manuals")
        ]
        
        successful_retrievals = 0
        
        for query, retrieval_type in retrieval_queries:
            LogFire.log("DEBUG", f"[{self.test_name}] Testing {retrieval_type} retrieval")
            
            result = await self.execute_and_monitor_task(user, query, bot, timeout=20)
            
            # Check if retrieval occurred
            retrieval_found = False
            for task_detail in result["task_details"]:
                call_trace = task_detail.get("call_trace", [])
                call_trace_str = " ".join(str(call).lower() for call in call_trace)
                
                if any(keyword in call_trace_str for keyword in ["search", "find", "retrieve", "locate"]):
                    retrieval_found = True
                    break
            
            if retrieval_found:
                successful_retrievals += 1
                LogFire.log("DEBUG", f"[{self.test_name}] {retrieval_type} retrieval successful")
            else:
                LogFire.log("DEBUG", f"[{self.test_name}] {retrieval_type} retrieval failed")
        
        # Verify retrieval accuracy
        retrieval_rate = successful_retrievals / len(retrieval_queries)
        assert retrieval_rate >= 0.7, f"Data retrieval accuracy too low: {retrieval_rate:.2f}"
        
        LogFire.log("DEBUG", f"[{self.test_name}] Data retrieval accuracy test passed")
        
        return {
            "successful_retrievals": successful_retrievals,
            "total_queries": len(retrieval_queries),
            "retrieval_rate": retrieval_rate
        }
    
    @requires_openai_api()
    async def test_data_analysis_error_handling(self):
        """Test data analysis system error handling and recovery"""
        # Setup
        setup_success = await self.setup_data_system()
        if not setup_success:
            pytest.skip("Could not initialize data analysis system")
        
        user = await self.create_test_user(email="<EMAIL>")
        bot = self.create_test_bot()
        
        # Test error scenarios
        error_scenarios = [
            "analyze data that doesn't exist",  # Non-existent data
            "tell me about invalid data source",  # Invalid source
            "provide analysis of empty dataset",  # Empty dataset
        ]
        
        handled_errors = 0
        
        for query in error_scenarios:
            LogFire.log("DEBUG", f"[{self.test_name}] Testing error handling for: '{query}'")
            
            try:
                result = await self.execute_and_monitor_task(user, query, bot, timeout=15)
                
                # Check if system handled the error gracefully
                if result["success"]:
                    handled_errors += 1
                    LogFire.log("DEBUG", f"[{self.test_name}] Error case handled gracefully")
                else:
                    LogFire.log("DEBUG", f"[{self.test_name}] Error case failed as expected")
            
            except Exception as e:
                LogFire.log("DEBUG", f"[{self.test_name}] Error case raised exception: {e}")
                # This is acceptable for some error cases
        
        # Verify that system doesn't crash on edge cases
        LogFire.log("DEBUG", f"[{self.test_name}] Error handling test completed")
        LogFire.log("DEBUG", f"[{self.test_name}] Gracefully handled cases: {handled_errors}/{len(error_scenarios)}")
        
        return {
            "handled_errors": handled_errors,
            "total_error_cases": len(error_scenarios),
            "graceful_handling_rate": handled_errors / len(error_scenarios)
        }
    
    @requires_openai_api()
    async def test_data_context_awareness(self):
        """Test data system's context awareness and intelligent responses"""
        # Setup
        setup_success = await self.setup_data_system()
        if not setup_success:
            pytest.skip("Could not initialize data analysis system")
        
        user = await self.create_test_user(email="<EMAIL>")
        bot = self.create_test_bot()
        
        # Test context-aware queries
        context_queries = [
            ("what data is most relevant for business analysis", "business_context"),
            ("which information would be useful for technical decisions", "technical_context"),
            ("what data can help with user experience improvements", "ux_context")
        ]
        
        context_aware_responses = 0
        
        for query, context_type in context_queries:
            LogFire.log("DEBUG", f"[{self.test_name}] Testing {context_type} awareness")
            
            result = await self.execute_and_monitor_task(user, query, bot, timeout=25)
            
            # Check if context-aware response occurred
            context_found = False
            for task_detail in result["task_details"]:
                call_trace = task_detail.get("call_trace", [])
                call_trace_str = " ".join(str(call).lower() for call in call_trace)
                
                if any(keyword in call_trace_str for keyword in ["relevant", "useful", "analysis", "decision"]):
                    context_found = True
                    break
            
            if context_found:
                context_aware_responses += 1
                LogFire.log("DEBUG", f"[{self.test_name}] {context_type} awareness successful")
            else:
                LogFire.log("DEBUG", f"[{self.test_name}] {context_type} awareness failed")
        
        # Verify context awareness
        context_rate = context_aware_responses / len(context_queries)
        assert context_rate >= 0.6, f"Context awareness rate too low: {context_rate:.2f}"
        
        LogFire.log("DEBUG", f"[{self.test_name}] Data context awareness test passed")
        
        return {
            "context_aware_responses": context_aware_responses,
            "total_queries": len(context_queries),
            "context_rate": context_rate
        }


# Additional utility functions specific to data analysis testing
def get_data_analysis_test_data():
    """Get test data specifically for data analysis testing"""
    return {
        "data_types": [
            "Structured data",
            "Unstructured text",
            "Document files",
            "Configuration data",
            "Log files",
            "Metadata"
        ],
        "analysis_methods": [
            "Content analysis",
            "Statistical analysis",
            "Pattern recognition",
            "Trend analysis",
            "Quality assessment",
            "Relevance scoring"
        ],
        "output_formats": [
            "Summary report",
            "Detailed analysis",
            "Data overview",
            "Quality metrics",
            "Recommendations",
            "Insights"
        ]
    }


if __name__ == "__main__":
    # Run tests individually for debugging
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "debug":
        # Debug mode - run a single test
        test_instance = TestRealData()
        test_instance.setup_method()
        
        asyncio.run(test_instance.test_tell_me_about_your_data_basic())
        LogFire.log("DEBUG", "Debug test completed")
    else:
        # Normal pytest execution
        pytest.main([__file__, "-v"], severity="debug")