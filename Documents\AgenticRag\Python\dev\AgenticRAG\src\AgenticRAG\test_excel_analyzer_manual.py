#!/usr/bin/env python3
"""
Manual testing script for Excel Analyzer
Run from src/AgenticRAG/ directory: python test_excel_analyzer_manual.py
"""

import asyncio
import sys
import os
from pathlib import Path

# Add src directory to path
sys.path.insert(0, str(Path(__file__).parent))

from tasks.processing.task_excel_analyzer import ExcelAnalyzerTool, create_supervisor_excel_analyzer
from managers.manager_logfire import LogFire

async def test_excel_analyzer_tool():
    """Test the Excel analyzer tool directly"""
    print("=== Testing Excel Analyzer Tool Directly ===")
    
    tool = ExcelAnalyzerTool()
    
    # Test with sample data (no file path)
    queries = [
        "How many rows are in the dataset?",
        "What are the column names?", 
        "What is the average salary?",
        "Show me the first 3 rows",
        "What departments do we have?",
        "Who has the highest salary?"
    ]
    
    for query in queries:
        print(f"\nQuery: {query}")
        try:
            result = await tool._arun(query)
            print(f"Result: {result}")
        except Exception as e:
            print(f"Error: {e}")

async def test_with_your_file():
    """Test with your actual CSV file"""
    print("\n=== Testing with Your CSV File ===")
    
    tool = ExcelAnalyzerTool()
    
    # Use your file path (update this to match your actual file)
    file_path = r"C:\Users\<USER>\Documents\AgenticRag\Python\Test files\ds_salaries.csv"
    
    if os.path.exists(file_path):
        queries = [
            "How many rows are in this dataset?",
            "What are all the column names?",
            "What is the average salary in the dataset?",
            "Show me summary statistics",
            "What are the top 5 job titles by count?",
            "What is the salary range (min and max)?"
        ]
        
        for query in queries:
            print(f"\nQuery: {query}")
            try:
                result = await tool._arun(query, file_path=file_path)
                print(f"Result: {result}")
            except Exception as e:
                print(f"Error: {e}")
    else:
        print(f"File not found: {file_path}")
        print("Update the file_path variable with your actual CSV file location")

async def test_supervisor_creation():
    """Test that the supervisor can be created"""
    print("\n=== Testing Supervisor Creation ===")
    
    try:
        supervisor = await create_supervisor_excel_analyzer()
        print(f"Supervisor created successfully: {supervisor.name}")
        print(f"Number of tasks: {len(supervisor.tasks)}")
        return supervisor
    except Exception as e:
        print(f"Error creating supervisor: {e}")
        return None

async def main():
    """Run all tests"""
    print("Starting Excel Analyzer Manual Tests...\n")
    
    # Test 1: Direct tool with sample data
    await test_excel_analyzer_tool()
    
    # Test 2: With your actual file
    await test_with_your_file()
    
    # Test 3: Supervisor creation
    supervisor = await test_supervisor_creation()
    
    print("\n=== Testing Complete ===")
    print("Check the output above for any errors or unexpected results")

if __name__ == "__main__":
    # Run the tests
    asyncio.run(main())