"""
Integration tests for complete message processing workflow
This test suite covers the full end-to-end message processing flow:
Endpoint -> Supervisor -> Task -> Response
"""

import sys
import os
import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from typing import Dict, List, Any
from datetime import datetime
from uuid import uuid4

# Add project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from imports import *
from endpoints.discord_endpoint import MyDiscordBot
from endpoints.mybot_generic import MyBot_Generic, ChannelType, ReplyContext
from managers.manager_supervisors import SupervisorManager
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import <PERSON><PERSON><PERSON><PERSON><PERSON>, PERMISSION_LEVELS
from tasks.task_top_level_supervisor import create_top_level_supervisor

class TestMessageProcessingWorkflow:
    """Test complete message processing workflow"""
    
    @pytest.fixture
    async def setup_test_environment(self):
        """Set up test environment with all required components"""
        # Mock all external dependencies
        with patch('endpoints.discord_endpoint.OAuth2Verifier') as mock_oauth, \
             patch('endpoints.discord_endpoint.Globals') as mock_globals, \
             patch('managers.manager_supervisors.PostgreSQLManager') as mock_db, \
             patch('managers.manager_users.PostgreSQLManager') as mock_user_db, \
             patch('tasks.task_top_level_supervisor.SupervisorManager') as mock_supervisor_manager:
            
            # Setup OAuth mock
            mock_oauth.get_token.return_value = "123456789"
            
            # Setup Globals mock
            mock_globals.is_docker.return_value = False
            mock_globals.is_debug.return_value = False
            
            # Setup database mocks
            mock_db_instance = Mock()
            mock_db.get_instance.return_value = mock_db_instance
            mock_user_db.get_instance.return_value = mock_db_instance
            
            # Setup supervisor manager
            mock_supervisor_instance = Mock()
            mock_supervisor_manager.get_instance.return_value = mock_supervisor_instance
            
            # Setup user manager
            user_manager = ZairaUserManager.get_instance()
            test_user = ZairaUser(
                username="testuser",
                rank=PERMISSION_LEVELS.USER,
                guid=uuid4(),
                device_guid=uuid4()
            )
            
            yield {
                'user_manager': user_manager,
                'test_user': test_user,
                'supervisor_manager': mock_supervisor_manager,
                'oauth': mock_oauth,
                'globals': mock_globals,
                'db': mock_db
            }
    
    @pytest.mark.asyncio
    async def test_discord_message_to_supervisor_workflow(self, setup_test_environment):
        """Test complete workflow from Discord message to supervisor response"""
        env = setup_test_environment
        
        # Setup Discord bot
        MyDiscordBot._instance = None
        MyDiscordBot._initialized = False
        
        with patch('endpoints.discord_endpoint.asyncio.new_event_loop') as mock_loop, \
             patch('endpoints.discord_endpoint.threading.Thread') as mock_thread, \
             patch('endpoints.discord_endpoint.ZairaUserManager') as mock_user_manager_class:
            
            # Mock event loop
            mock_event_loop = Mock()
            mock_loop.return_value = mock_event_loop
            
            # Mock thread
            mock_thread_instance = Mock()
            mock_thread.return_value = mock_thread_instance
            
            # Mock user manager
            mock_user_manager_class.get_user.return_value = env['test_user']
            mock_user_manager_class.add_user.return_value = env['test_user']
            
            # Setup Discord bot
            discord_bot = MyDiscordBot.get_instance()
            await MyDiscordBot.setup()
            
            # Create mock Discord message
            mock_message = Mock()
            mock_message.author = Mock()
            mock_message.author.id = "123456789"
            mock_message.content = "!test message"
            mock_message.channel = Mock()
            mock_message.channel.name = "zaira-test"
            mock_message.guild = Mock()
            mock_message.guild.id = 123456789
            mock_message.guild.name = "Test Guild"
            mock_message.reference = None
            mock_message.attachments = []
            mock_message.reply = AsyncMock()
            
            # Mock bot user
            MyDiscordBot.bot.user = Mock()
            MyDiscordBot.bot.user.id = "987654321"
            
            # Mock guild members
            mock_member = Mock()
            mock_member.guild_permissions = Mock()
            mock_member.guild_permissions.administrator = True
            mock_member.guild_permissions.manage_guild = False
            mock_member.guild_permissions.kick_members = False
            mock_member.guild_permissions.ban_members = False
            discord_bot.members = [mock_member]
            
            # Mock MyBot_Generic
            with patch('endpoints.discord_endpoint.MyBot_Generic') as mock_generic:
                mock_generic_instance = Mock()
                mock_generic.return_value = mock_generic_instance
                mock_generic_instance.on_message = AsyncMock()
                mock_generic_instance.name = "Discord"
                
                # Set up bot_generic instance
                discord_bot.bot_generic = mock_generic_instance
                
                # Process the message
                await MyDiscordBot.on_message(mock_message)
                
                # Verify the complete workflow
                mock_generic_instance.on_message.assert_called_once()
                call_args = mock_generic_instance.on_message.call_args
                
                # Verify call parameters
                assert call_args[0][0] == ChannelType.PUBLIC  # Channel type
                assert call_args[0][1] == env['test_user']  # User
                assert call_args[0][2] == "test message"  # Message (without !)
                assert call_args[0][3] == []  # Files
                assert call_args[0][4] == mock_message  # Original message
                
                # Verify reply context
                reply_context = call_args[0][5]
                assert isinstance(reply_context, ReplyContext)
                assert reply_context.is_reply == False
    
    @pytest.mark.asyncio
    async def test_message_with_reply_context_workflow(self, setup_test_environment):
        """Test workflow with reply context"""
        env = setup_test_environment
        
        # Setup Discord bot
        MyDiscordBot._instance = None
        MyDiscordBot._initialized = False
        
        with patch('endpoints.discord_endpoint.asyncio.new_event_loop') as mock_loop, \
             patch('endpoints.discord_endpoint.threading.Thread') as mock_thread, \
             patch('endpoints.discord_endpoint.ZairaUserManager') as mock_user_manager_class:
            
            # Mock components
            mock_event_loop = Mock()
            mock_loop.return_value = mock_event_loop
            mock_thread_instance = Mock()
            mock_thread.return_value = mock_thread_instance
            mock_user_manager_class.get_user.return_value = env['test_user']
            
            # Setup Discord bot
            discord_bot = MyDiscordBot.get_instance()
            await MyDiscordBot.setup()
            
            # Create mock Discord message with reply
            mock_message = Mock()
            mock_message.author = Mock()
            mock_message.author.id = "123456789"
            mock_message.content = "!reply to previous message"
            mock_message.channel = Mock()
            mock_message.channel.name = "zaira-test"
            mock_message.guild = Mock()
            mock_message.guild.id = 123456789
            mock_message.guild.name = "Test Guild"
            mock_message.attachments = []
            mock_message.reply = AsyncMock()
            
            # Mock reply reference
            mock_message.reference = Mock()
            mock_message.reference.message_id = "referenced_message_id"
            
            # Mock referenced message
            mock_referenced_message = Mock()
            mock_referenced_message.content = "Original message content"
            mock_referenced_message.author = Mock()
            mock_referenced_message.author.id = "original_author_id"
            mock_message.channel.fetch_message = AsyncMock(return_value=mock_referenced_message)
            
            # Mock bot user and members
            MyDiscordBot.bot.user = Mock()
            MyDiscordBot.bot.user.id = "987654321"
            
            mock_member = Mock()
            mock_member.guild_permissions = Mock()
            mock_member.guild_permissions.administrator = True
            mock_member.guild_permissions.manage_guild = False
            mock_member.guild_permissions.kick_members = False
            mock_member.guild_permissions.ban_members = False
            discord_bot.members = [mock_member]
            
            # Mock MyBot_Generic
            with patch('endpoints.discord_endpoint.MyBot_Generic') as mock_generic:
                mock_generic_instance = Mock()
                mock_generic.return_value = mock_generic_instance
                mock_generic_instance.on_message = AsyncMock()
                mock_generic_instance.name = "Discord"
                
                discord_bot.bot_generic = mock_generic_instance
                
                # Process the message
                await MyDiscordBot.on_message(mock_message)
                
                # Verify reply context was created correctly
                mock_generic_instance.on_message.assert_called_once()
                call_args = mock_generic_instance.on_message.call_args
                
                reply_context = call_args[0][5]
                assert isinstance(reply_context, ReplyContext)
                assert reply_context.is_reply == True
                assert reply_context.replied_message_id == "referenced_message_id"
                assert reply_context.replied_message_content == "Original message content"
                assert reply_context.platform == "Discord"
    
    @pytest.mark.asyncio
    async def test_message_with_attachments_workflow(self, setup_test_environment):
        """Test workflow with file attachments"""
        env = setup_test_environment
        
        # Setup Discord bot
        MyDiscordBot._instance = None
        MyDiscordBot._initialized = False
        
        with patch('endpoints.discord_endpoint.asyncio.new_event_loop') as mock_loop, \
             patch('endpoints.discord_endpoint.threading.Thread') as mock_thread, \
             patch('endpoints.discord_endpoint.ZairaUserManager') as mock_user_manager_class, \
             patch('builtins.open', create=True) as mock_open:
            
            # Mock components
            mock_event_loop = Mock()
            mock_loop.return_value = mock_event_loop
            mock_thread_instance = Mock()
            mock_thread.return_value = mock_thread_instance
            mock_user_manager_class.get_user.return_value = env['test_user']
            
            # Setup Discord bot
            discord_bot = MyDiscordBot.get_instance()
            await MyDiscordBot.setup()
            
            # Create mock Discord message with attachments
            mock_message = Mock()
            mock_message.author = Mock()
            mock_message.author.id = "123456789"
            mock_message.content = "!message with attachment"
            mock_message.channel = Mock()
            mock_message.channel.name = "zaira-test"
            mock_message.guild = Mock()
            mock_message.guild.id = 123456789
            mock_message.guild.name = "Test Guild"
            mock_message.reference = None
            mock_message.reply = AsyncMock()
            
            # Mock attachment
            mock_attachment = Mock()
            mock_attachment.filename = "test_file.txt"
            mock_attachment.read = AsyncMock(return_value=b"file content")
            mock_message.attachments = [mock_attachment]
            
            # Mock bot user and members
            MyDiscordBot.bot.user = Mock()
            MyDiscordBot.bot.user.id = "987654321"
            
            mock_member = Mock()
            mock_member.guild_permissions = Mock()
            mock_member.guild_permissions.administrator = True
            mock_member.guild_permissions.manage_guild = False
            mock_member.guild_permissions.kick_members = False
            mock_member.guild_permissions.ban_members = False
            discord_bot.members = [mock_member]
            
            # Mock file operations
            mock_file = Mock()
            mock_open.return_value.__enter__.return_value = mock_file
            
            # Mock MyBot_Generic
            with patch('endpoints.discord_endpoint.MyBot_Generic') as mock_generic:
                mock_generic_instance = Mock()
                mock_generic.return_value = mock_generic_instance
                mock_generic_instance.on_message = AsyncMock()
                mock_generic_instance.name = "Discord"
                
                discord_bot.bot_generic = mock_generic_instance
                
                # Process the message
                await MyDiscordBot.on_message(mock_message)
                
                # Verify file was processed
                mock_attachment.read.assert_called_once()
                mock_open.assert_called_once_with("test_file.txt", "wb")
                mock_file.write.assert_called_once_with(b"file content")
                
                # Verify message was processed with file
                mock_generic_instance.on_message.assert_called_once()
                call_args = mock_generic_instance.on_message.call_args
                
                files_bytes = call_args[0][3]
                assert files_bytes == ["test_file.txt"]
    
    @pytest.mark.asyncio
    async def test_user_creation_workflow(self, setup_test_environment):
        """Test user creation workflow for new users"""
        env = setup_test_environment
        
        # Setup Discord bot
        MyDiscordBot._instance = None
        MyDiscordBot._initialized = False
        
        with patch('endpoints.discord_endpoint.asyncio.new_event_loop') as mock_loop, \
             patch('endpoints.discord_endpoint.threading.Thread') as mock_thread, \
             patch('endpoints.discord_endpoint.ZairaUserManager') as mock_user_manager_class:
            
            # Mock components
            mock_event_loop = Mock()
            mock_loop.return_value = mock_event_loop
            mock_thread_instance = Mock()
            mock_thread.return_value = mock_thread_instance
            
            # Mock user manager to return None for first call (user doesn't exist)
            # then return created user for second call
            created_user = ZairaUser(
                username="newuser",
                rank=PERMISSION_LEVELS.USER,
                guid=uuid4(),
                device_guid=uuid4()
            )
            mock_user_manager_class.get_user.return_value = None
            mock_user_manager_class.add_user.return_value = created_user
            mock_user_manager_class.create_guid.return_value = str(uuid4())
            
            # Setup Discord bot
            discord_bot = MyDiscordBot.get_instance()
            await MyDiscordBot.setup()
            
            # Create mock Discord message from new user
            mock_message = Mock()
            mock_message.author = Mock()
            mock_message.author.id = "newuser123"
            mock_message.content = "!first message"
            mock_message.channel = Mock()
            mock_message.channel.name = "zaira-test"
            mock_message.guild = Mock()
            mock_message.guild.id = 123456789
            mock_message.guild.name = "Test Guild"
            mock_message.reference = None
            mock_message.attachments = []
            mock_message.reply = AsyncMock()
            
            # Mock bot user and members
            MyDiscordBot.bot.user = Mock()
            MyDiscordBot.bot.user.id = "987654321"
            
            mock_member = Mock()
            mock_member.guild_permissions = Mock()
            mock_member.guild_permissions.administrator = True
            mock_member.guild_permissions.manage_guild = False
            mock_member.guild_permissions.kick_members = False
            mock_member.guild_permissions.ban_members = False
            discord_bot.members = [mock_member]
            
            # Mock MyBot_Generic
            with patch('endpoints.discord_endpoint.MyBot_Generic') as mock_generic:
                mock_generic_instance = Mock()
                mock_generic.return_value = mock_generic_instance
                mock_generic_instance.on_message = AsyncMock()
                mock_generic_instance.name = "Discord"
                
                discord_bot.bot_generic = mock_generic_instance
                
                # Process the message
                await MyDiscordBot.on_message(mock_message)
                
                # Verify user creation workflow
                mock_user_manager_class.get_user.assert_called_once()
                mock_user_manager_class.add_user.assert_called_once()
                
                # Verify message was processed with created user
                mock_generic_instance.on_message.assert_called_once()
                call_args = mock_generic_instance.on_message.call_args
                
                processed_user = call_args[0][1]
                assert processed_user == created_user
    
    @pytest.mark.asyncio
    async def test_private_message_workflow(self, setup_test_environment):
        """Test private message processing workflow"""
        env = setup_test_environment
        
        # Setup Discord bot
        MyDiscordBot._instance = None
        MyDiscordBot._initialized = False
        
        with patch('endpoints.discord_endpoint.asyncio.new_event_loop') as mock_loop, \
             patch('endpoints.discord_endpoint.threading.Thread') as mock_thread, \
             patch('endpoints.discord_endpoint.ZairaUserManager') as mock_user_manager_class:
            
            # Mock components
            mock_event_loop = Mock()
            mock_loop.return_value = mock_event_loop
            mock_thread_instance = Mock()
            mock_thread.return_value = mock_thread_instance
            mock_user_manager_class.get_user.return_value = env['test_user']
            
            # Setup Discord bot
            discord_bot = MyDiscordBot.get_instance()
            await MyDiscordBot.setup()
            
            # Create mock private Discord message
            mock_message = Mock()
            mock_message.author = Mock()
            mock_message.author.id = "123456789"
            mock_message.content = "private message"
            mock_message.channel = "Direct Message with Unknown User"
            mock_message.guild = None  # Private messages don't have guilds
            mock_message.reference = None
            mock_message.attachments = []
            mock_message.reply = AsyncMock()
            
            # Mock bot user and members
            MyDiscordBot.bot.user = Mock()
            MyDiscordBot.bot.user.id = "987654321"
            
            mock_member = Mock()
            mock_member.guild_permissions = Mock()
            mock_member.guild_permissions.administrator = True
            mock_member.guild_permissions.manage_guild = False
            mock_member.guild_permissions.kick_members = False
            mock_member.guild_permissions.ban_members = False
            discord_bot.members = [mock_member]
            
            # Mock MyBot_Generic
            with patch('endpoints.discord_endpoint.MyBot_Generic') as mock_generic:
                mock_generic_instance = Mock()
                mock_generic.return_value = mock_generic_instance
                mock_generic_instance.on_message = AsyncMock()
                mock_generic_instance.name = "Discord"
                
                discord_bot.bot_generic = mock_generic_instance
                
                # Process the message
                await MyDiscordBot.on_message(mock_message)
                
                # Verify private message workflow
                mock_generic_instance.on_message.assert_called_once()
                call_args = mock_generic_instance.on_message.call_args
                
                # Verify it was processed as private message
                assert call_args[0][0] == ChannelType.PRIVATE
                assert call_args[0][1] == env['test_user']
                assert call_args[0][2] == "private message"
    
    @pytest.mark.asyncio
    async def test_message_filtering_workflow(self, setup_test_environment):
        """Test message filtering workflow"""
        env = setup_test_environment
        
        # Setup Discord bot
        MyDiscordBot._instance = None
        MyDiscordBot._initialized = False
        
        with patch('endpoints.discord_endpoint.asyncio.new_event_loop') as mock_loop, \
             patch('endpoints.discord_endpoint.threading.Thread') as mock_thread, \
             patch('endpoints.discord_endpoint.ZairaUserManager') as mock_user_manager_class:
            
            # Mock components
            mock_event_loop = Mock()
            mock_loop.return_value = mock_event_loop
            mock_thread_instance = Mock()
            mock_thread.return_value = mock_thread_instance
            mock_user_manager_class.get_user.return_value = env['test_user']
            
            # Setup Discord bot
            discord_bot = MyDiscordBot.get_instance()
            await MyDiscordBot.setup()
            
            # Mock bot user
            MyDiscordBot.bot.user = Mock()
            MyDiscordBot.bot.user.id = "987654321"
            
            # Mock members list (empty to test filtering)
            discord_bot.members = []
            
            # Mock MyBot_Generic
            with patch('endpoints.discord_endpoint.MyBot_Generic') as mock_generic:
                mock_generic_instance = Mock()
                mock_generic.return_value = mock_generic_instance
                mock_generic_instance.on_message = AsyncMock()
                mock_generic_instance.name = "Discord"
                
                discord_bot.bot_generic = mock_generic_instance
                
                # Test 1: Message from bot itself (should be filtered)
                mock_message = Mock()
                mock_message.author = MyDiscordBot.bot.user
                mock_message.content = "!bot message"
                mock_message.channel = Mock()
                mock_message.channel.name = "zaira-test"
                mock_message.guild = Mock()
                mock_message.guild.id = 123456789
                mock_message.reference = None
                mock_message.attachments = []
                
                await MyDiscordBot.on_message(mock_message)
                
                # Should not process bot's own message
                mock_generic_instance.on_message.assert_not_called()
                
                # Test 2: Message from user not in members list (should be filtered)
                mock_message.author = Mock()
                mock_message.author.id = "unauthorized_user"
                
                await MyDiscordBot.on_message(mock_message)
                
                # Should not process unauthorized user's message
                mock_generic_instance.on_message.assert_not_called()
                
                # Test 3: Empty message (should be filtered)
                mock_member = Mock()
                mock_member.guild_permissions = Mock()
                mock_member.guild_permissions.administrator = True
                mock_member.guild_permissions.manage_guild = False
                mock_member.guild_permissions.kick_members = False
                mock_member.guild_permissions.ban_members = False
                discord_bot.members = [mock_member]
                
                mock_message.author = mock_member
                mock_message.content = ""
                
                await MyDiscordBot.on_message(mock_message)
                
                # Should not process empty message
                mock_generic_instance.on_message.assert_not_called()

class TestSupervisorWorkflow:
    """Test supervisor workflow integration"""
    
    @pytest.fixture
    def mock_supervisor_dependencies(self):
        """Mock supervisor dependencies"""
        with patch('managers.manager_supervisors.PostgreSQLManager') as mock_db, \
             patch('managers.manager_supervisors.QDrantManager') as mock_qdrant, \
             patch('managers.manager_supervisors.LogFire') as mock_logfire:
            
            # Mock database
            mock_db_instance = Mock()
            mock_db.get_instance.return_value = mock_db_instance
            
            # Mock Qdrant
            mock_qdrant_instance = Mock()
            mock_qdrant.get_instance.return_value = mock_qdrant_instance
            
            yield {
                'db': mock_db,
                'qdrant': mock_qdrant,
                'logfire': mock_logfire
            }
    
    @pytest.mark.asyncio
    async def test_supervisor_task_routing_workflow(self, mock_supervisor_dependencies):
        """Test supervisor task routing workflow"""
        from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base
        
        # Reset singleton for test
        SupervisorManager._instance = None
        SupervisorManager._initialized = False
        
        # Create mock task
        mock_task = Mock(spec=SupervisorTask_Base)
        mock_task.name = "test_task"
        mock_task.llm_call = AsyncMock(return_value="Task completed successfully")
        mock_task.tools = []
        
        # Initialize supervisor manager
        supervisor_manager = SupervisorManager.get_instance()
        await SupervisorManager.setup()
        
        # Register task
        supervisor_manager.tasks["test_task"] = mock_task
        
        # Test task routing
        result = await supervisor_manager.route_to_task("test_task", "test input")
        
        # Verify task was called
        mock_task.llm_call.assert_called_once()
        assert result == "Task completed successfully"
    
    @pytest.mark.asyncio
    async def test_supervisor_error_handling_workflow(self, mock_supervisor_dependencies):
        """Test supervisor error handling workflow"""
        from managers.manager_supervisors import SupervisorManager, SupervisorTask_Base
        
        # Reset singleton for test
        SupervisorManager._instance = None
        SupervisorManager._initialized = False
        
        # Create mock task that raises exception
        mock_task = Mock(spec=SupervisorTask_Base)
        mock_task.name = "error_task"
        mock_task.llm_call = AsyncMock(side_effect=Exception("Task error"))
        mock_task.tools = []
        
        # Initialize supervisor manager
        supervisor_manager = SupervisorManager.get_instance()
        await SupervisorManager.setup()
        
        # Register task
        supervisor_manager.tasks["error_task"] = mock_task
        
        # Test error handling
        with pytest.raises(Exception) as exc_info:
            await supervisor_manager.route_to_task("error_task", "test input")
        
        # Verify exception was raised
        assert str(exc_info.value) == "Task error"
        mock_task.llm_call.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_supervisor_task_not_found_workflow(self, mock_supervisor_dependencies):
        """Test supervisor workflow when task is not found"""
        from managers.manager_supervisors import SupervisorManager
        
        # Reset singleton for test
        SupervisorManager._instance = None
        SupervisorManager._initialized = False
        
        # Initialize supervisor manager
        supervisor_manager = SupervisorManager.get_instance()
        await SupervisorManager.setup()
        
        # Test routing to non-existent task
        result = await supervisor_manager.route_to_task("non_existent_task", "test input")
        
        # Should return None or handle gracefully
        assert result is None

class TestEndToEndWorkflow:
    """Test complete end-to-end workflow"""
    
    @pytest.mark.asyncio
    async def test_complete_message_to_response_workflow(self):
        """Test complete workflow from message input to response output"""
        # This test simulates a complete workflow but with all components mocked
        # to avoid external dependencies
        
        with patch('endpoints.discord_endpoint.OAuth2Verifier') as mock_oauth, \
             patch('endpoints.discord_endpoint.Globals') as mock_globals, \
             patch('endpoints.discord_endpoint.ZairaUserManager') as mock_user_manager, \
             patch('endpoints.discord_endpoint.MyBot_Generic') as mock_generic, \
             patch('endpoints.discord_endpoint.asyncio.new_event_loop'), \
             patch('endpoints.discord_endpoint.threading.Thread'):
            
            # Setup mocks
            mock_oauth.get_token.return_value = "123456789"
            mock_globals.is_docker.return_value = False
            mock_globals.is_debug.return_value = False
            
            # Mock user
            test_user = ZairaUser(
                username="testuser",
                rank=PERMISSION_LEVELS.USER,
                guid=uuid4(),
                device_guid=uuid4()
            )
            mock_user_manager.get_user.return_value = test_user
            
            # Mock generic bot
            mock_generic_instance = Mock()
            mock_generic.return_value = mock_generic_instance
            mock_generic_instance.on_message = AsyncMock()
            mock_generic_instance.on_ready = AsyncMock()
            mock_generic_instance.name = "Discord"
            
            # Setup Discord bot
            MyDiscordBot._instance = None
            MyDiscordBot._initialized = False
            discord_bot = MyDiscordBot.get_instance()
            await MyDiscordBot.setup()
            
            # Simulate the complete workflow
            # 1. Bot receives message
            mock_message = Mock()
            mock_message.author = Mock()
            mock_message.author.id = "123456789"
            mock_message.content = "!help"
            mock_message.channel = Mock()
            mock_message.channel.name = "zaira-test"
            mock_message.guild = Mock()
            mock_message.guild.id = 123456789
            mock_message.reference = None
            mock_message.attachments = []
            mock_message.reply = AsyncMock()
            
            # Mock bot user and members
            MyDiscordBot.bot.user = Mock()
            MyDiscordBot.bot.user.id = "987654321"
            
            mock_member = Mock()
            mock_member.guild_permissions = Mock()
            mock_member.guild_permissions.administrator = True
            mock_member.guild_permissions.manage_guild = False
            mock_member.guild_permissions.kick_members = False
            mock_member.guild_permissions.ban_members = False
            discord_bot.members = [mock_member]
            
            # 2. Process message through Discord endpoint
            await MyDiscordBot.on_message(mock_message)
            
            # 3. Verify the complete workflow executed
            mock_generic_instance.on_message.assert_called_once()
            
            # Verify the message was processed correctly
            call_args = mock_generic_instance.on_message.call_args
            assert call_args[0][0] == ChannelType.PUBLIC
            assert call_args[0][1] == test_user
            assert call_args[0][2] == "help"
            
            # This represents the complete workflow:
            # Discord Message -> Discord Endpoint -> MyBot_Generic -> Supervisor -> Task -> Response
            # All steps were executed successfully