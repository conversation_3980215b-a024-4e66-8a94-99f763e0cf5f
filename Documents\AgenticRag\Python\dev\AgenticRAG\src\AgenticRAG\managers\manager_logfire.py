from datetime import datetime, timezone
from pydantic import BaseModel
import logfire
import inspect
from typing import Literal, Optional, TYPE_CHECKING, Dict, Any
import asyncio
import os

# Import globals directly to avoid circular import
from globals import *
from config import *
import etc.helper_functions

# PostgreSQL import moved to method level to avoid circular import

# https://logfire-us.pydantic.dev/askzaira/agentic-rag

if TYPE_CHECKING:
    from asyncio import Task
    from userprofiles.ZairaUser import Zaira<PERSON>ser
    from userprofiles.ZairaChat import ZairaChat

class LogFire:
    _instance = None
    _initialized = False
    asyncio_Task_await_response: "Task" = None
    isLogging = False

    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance

    @classmethod
    def get_instance(cls):
        return cls()

    @classmethod
    async def _create_logentries_table(cls):
        """Create the LogEntries table if it doesn't exist"""
        from managers.manager_postgreSQL import <PERSON>greS<PERSON><PERSON>ana<PERSON>
        try:
            # First check if connection exists, if not create it
            connection = await PostgreSQLManager.get_connection("vectordb")
            if connection is None:
                # Create database first if it doesn't exist
                await PostgreSQLManager.create_database("vectordb")
                # Then connect to it with pooling to avoid concurrency issues
                connection = await PostgreSQLManager.get_connection("vectordb")
            
            create_table_query = """
            CREATE TABLE IF NOT EXISTS LogEntries (
                id SERIAL PRIMARY KEY,
                Timestamp TIMESTAMP NOT NULL,
                UserID VARCHAR(100),
                SessionID VARCHAR(100),
                Level VARCHAR(50) NOT NULL,
                Source VARCHAR(100),
                EventCode VARCHAR(100),
                Value TEXT,
                Exception TEXT,
                MetaData JSONB,
                created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
            );
            
            CREATE INDEX IF NOT EXISTS idx_logentries_timestamp ON LogEntries(Timestamp);
            CREATE INDEX IF NOT EXISTS idx_logentries_userid ON LogEntries(UserID);
            CREATE INDEX IF NOT EXISTS idx_logentries_level ON LogEntries(Level);
            """
            
            if connection:
                await connection.execute(create_table_query)
            # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in LogFire manager itself
            print("LogEntries database table created/verified")
            
        except Exception as e:
            # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in LogFire manager itself
            print(f"Failed to create LogEntries table: {str(e)}")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "LogFire._create_logentries_table")
        finally:
            from managers.manager_postgreSQL import PostgreSQLManager
            await PostgreSQLManager.close_connection("vectordb")
            pass

    @classmethod
    async def setup(cls):
        if cls._initialized:
            return
        
        logfire.configure(token='pylf_v1_eu_PdlSrNHG8TsSLXGvf87cKtXxKtQ28dV3Tdr0GZVlvMTT')
        # Create LogEntries table if needed
        await cls._create_logentries_table()
        
        #logfire.instrument_pydantic()
        cls._initialized = True

    @classmethod    
    async def logfire_middleware(cls, request, handler):
        #with logfire.span(msg_template="Handling request {method} {path}", _span_name=f"{request.method} {request.path}"):
        response = await handler(request)
        return response
    
    @classmethod
    def get_caller_name(cls):
        # Stack[0] = get_caller_name
        # Stack[1] = function that called get_caller_name
        # Stack[2] = function that called that one (and so on)
        stack = inspect.stack()
        ret_val = ""
        if len(stack) > 3:
            ret_val += stack[3].function
            ret_val += " -> " + stack[2].function
        return ret_val

    @classmethod
    def log_conditionally(cls, event_code: str, data_logfire: str, condition: bool = True, **kwargs):
        """Only log if condition is True - prevents repetitive identical logging"""
        if condition:
            cls.log(event_code, data_logfire, **kwargs)

    @classmethod
    def log(cls, event_code: str, data_logfire: str, sql_addition: str = "", chat: Optional["ZairaChat"] = None, source: str = "", exception: str = "", severity: Literal["debug", "info", "warning", "error"] = "info"):
        if not cls._initialized:
            # Skip logging if not initialized during tests
            return
        # Data type validation before proceeding
        try:
            # Validate data_logfire
            if not isinstance(data_logfire, str):
                # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in validation
                print(f"LogFire.log: Invalid data_logfire type. Expected str, got {type(data_logfire).__name__}")
                # Don't break during testing
                import os
                if not os.environ.get('PYTEST_CURRENT_TEST'):
                    breakpoint()
                return
            
            # Validate suffix_sql
            if not isinstance(sql_addition, str):
                # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in validation
                print(f"LogFire.log: Invalid suffix_sql type. Expected str, got {type(sql_addition).__name__}")
                # Don't break during testing
                import os
                if not os.environ.get('PYTEST_CURRENT_TEST'):
                    breakpoint()
                return
            
            # Validate chat (must be None or ZairaChat instance)
            if chat is not None:
                # Check if chat has required attributes for ZairaChat
                if not hasattr(chat, 'session_guid') or not hasattr(chat, 'user_guid') or not hasattr(chat, 'get_user'):
                    # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in validation
                    print(f"LogFire.log: Invalid chat object. Missing required ZairaChat attributes (session_guid, user_guid, get_user)")
                    # Don't break during testing
                    import os
                    if not os.environ.get('PYTEST_CURRENT_TEST'):
                        breakpoint()
                    return
            
            # Validate source
            if not isinstance(source, str):
                # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in validation
                print(f"LogFire.log: Invalid source type. Expected str, got {type(source).__name__}")
                # Don't break during testing
                import os
                if not os.environ.get('PYTEST_CURRENT_TEST'):
                    breakpoint()
                return
            
            # Validate exception
            if not isinstance(exception, str):
                # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in validation
                print(f"LogFire.log: Invalid exception type. Expected str, got {type(exception).__name__}")
                # Don't break during testing
                import os
                if not os.environ.get('PYTEST_CURRENT_TEST'):
                    breakpoint()
                return
            
            # Validate severity
            valid_severities = ["debug", "info", "warning", "error"]
            if not isinstance(severity, str) or severity not in valid_severities:
                # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in validation
                print(f"LogFire.log: Invalid severity type or value. Expected str from {valid_severities}, got {type(severity).__name__}: {severity}")
                # Don't break during testing
                import os
                if not os.environ.get('PYTEST_CURRENT_TEST'):
                    breakpoint()
                return
                
        except Exception as e:
            # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in validation
            print(f"LogFire.log: Data type validation failed with exception: {e}")
            etc.helper_functions.exception_triggered(e, "LogFire", chat.user_guid if chat else None)
            return
        
        
        # In Claude environment, still allow print output for debug_trace.log, but skip Logfire/database logging
        from etc.helper_functions import is_claude_environment
        claude_env = is_claude_environment()
        if claude_env:
            # Still print to console (captured by debug_trace.log) but skip database/logfire logging
            mysource = cls.get_caller_name() if source == "" else source
            current_time = datetime.now(timezone.utc)
            timestamp = current_time.strftime("%H:%M:%S.%f")[:-3]
            user_guid = str(chat.user_guid) if chat else 'None'
            session_guid = str(chat.session_guid) if chat else 'None'
            metadata = f'{{"chat length#":{chat.message_count if chat else "-1"}}}'
            
            # CLAUDE ENVIRONMENT: Using print() for debug_trace.log capture, not LogFire.log() to avoid recursion
            print(f"{timestamp}[{event_code}], '{mysource}': {data_logfire} {sql_addition if Globals.is_debug_values() else ''}. {exception} Metadata: {metadata}. User {user_guid} on session {session_guid} at {current_time}")
            chat.receive_logging_message(f"[{event_code}] {data_logfire}")
            
            return
        
        # ENHANCED TEST DETECTION: Use global test environment detector for cross-thread detection
        import sys
        import os
        import inspect
        
        # Check global test environment state first (works across threads)
        try:
            from etc.test_environment_detector import is_test_environment_active, is_test_real_active, get_current_test_log_path, write_to_test_log, get_test_environment_info
            
            global_test_active = is_test_environment_active()
            global_test_real_active = is_test_real_active()
            global_test_log_path = get_current_test_log_path()
            
            # Enhanced debugging for IMAP/EMAIL events to track detection
            if 'IMAP' in event_code or 'EMAIL' in event_code:
                env_info = get_test_environment_info()
            
            # PRIORITY 1: Global test environment is active (works from any thread/context)
            if global_test_active:
                mysource = cls.get_caller_name() if source == "" else source
                current_time = datetime.now(timezone.utc)
                timestamp = current_time.strftime("%H:%M:%S.%f")[:-3]
                user_guid = str(chat.user_guid) if chat else 'None'
                session_guid = str(chat.session_guid) if chat else 'None'
                metadata = f'{{"chat length#":{chat.message_count if chat else "-1"}}}'
                
                log_message = f"[GLOBAL_TEST] {timestamp}[{event_code}], '{mysource}': {data_logfire} {sql_addition if Globals.is_debug_values() else ''}. {exception} Metadata: {metadata}. User {user_guid} on session {session_guid} at {current_time}"
                
                # Output to stdout for test capture
                print(log_message)
                
                # Also write directly to test log file (thread-safe)
                if global_test_log_path:
                    write_to_test_log(log_message.replace("[GLOBAL_TEST] ", ""))
                
                if chat:
                    chat.receive_logging_message(f"[{event_code}] {data_logfire}")
                
                # IMPORTANT: Return here to prevent double logging to production debug_trace.log
                # Test environment should have isolated logging only
                return
            
            # PRIORITY 2: Legacy pytest detection for compatibility
            elif 'pytest' in sys.modules:
                # Check for test_real environment by looking for test debug capture
                try:
                    from tests.test_real.test_debug_capture import is_test_capture_active, get_current_test_log_path
                    test_capture_active = is_test_capture_active()
                    current_log_path = get_current_test_log_path()
                    
                    # If test capture is active OR we detect a test_real environment pattern
                    if test_capture_active or (current_log_path and 'test_real' in str(current_log_path)):
                        # LEGACY TEST_REAL ENVIRONMENT: Output to stdout for test capture
                        mysource = cls.get_caller_name() if source == "" else source
                        current_time = datetime.now(timezone.utc)
                        timestamp = current_time.strftime("%H:%M:%S.%f")[:-3]
                        user_guid = str(chat.user_guid) if chat else 'None'
                        session_guid = str(chat.session_guid) if chat else 'None'
                        metadata = f'{{"chat length#":{chat.message_count if chat else "-1"}}}'
                        
                        # Output to stdout for test capture
                        print(f"[LEGACY_TEST] {timestamp}[{event_code}], '{mysource}': {data_logfire} {sql_addition if Globals.is_debug_values() else ''}. {exception} Metadata: {metadata}. User {user_guid} on session {session_guid} at {current_time}")
                        if chat:
                            chat.receive_logging_message(f"[{event_code}] {data_logfire}")
                        
                        # Return to prevent production logging contamination  
                        return
                    
                    # Additional fallback: Check environment variables and call stack
                    elif 'PYTEST_CURRENT_TEST' in os.environ or any('test_real' in str(frame.filename) for frame in inspect.stack()[:10]):
                        # FALLBACK: We're likely in a pytest environment, so capture to stdout as well
                        mysource = cls.get_caller_name() if source == "" else source
                        current_time = datetime.now(timezone.utc)
                        timestamp = current_time.strftime("%H:%M:%S.%f")[:-3]
                        user_guid = str(chat.user_guid) if chat else 'None'
                        session_guid = str(chat.session_guid) if chat else 'None'
                        metadata = f'{{"chat length#":{chat.message_count if chat else "-1"}}}'
                        
                        print(f"[FALLBACK_TEST] {timestamp}[{event_code}], '{mysource}': {data_logfire} {sql_addition if Globals.is_debug_values() else ''}. {exception} Metadata: {metadata}. User {user_guid} on session {session_guid} at {current_time}")
                        if chat:
                            chat.receive_logging_message(f"[{event_code}] {data_logfire}")
                        # Return to prevent production logging contamination
                        return
                        
                except ImportError as e:
                    # test_debug_capture not available
                    if 'IMAP' in event_code or 'EMAIL' in event_code:
                        pass  # Skip logging for IMAP/EMAIL events when test environment not available
                    
        except ImportError as e:
            # Global test environment detector not available, fall back to legacy detection
            if 'IMAP' in event_code or 'EMAIL' in event_code:
                pass  # Skip logging for IMAP/EMAIL events
            
            # Fallback to legacy detection
            if 'pytest' in sys.modules:
                try:
                    from tests.test_real.test_debug_capture import is_test_capture_active, get_current_test_log_path
                    test_capture_active = is_test_capture_active()
                    current_log_path = get_current_test_log_path()
                    
                    if test_capture_active or (current_log_path and 'test_real' in str(current_log_path)):
                        mysource = cls.get_caller_name() if source == "" else source
                        current_time = datetime.now(timezone.utc)
                        timestamp = current_time.strftime("%H:%M:%S.%f")[:-3]
                        user_guid = str(chat.user_guid) if chat else 'None'
                        session_guid = str(chat.session_guid) if chat else 'None'
                        metadata = f'{{"chat length#":{chat.message_count if chat else "-1"}}}'
                        
                        print(f"[LEGACY_FALLBACK] {timestamp}[{event_code}], '{mysource}': {data_logfire} {sql_addition if Globals.is_debug_values() else ''}. {exception} Metadata: {metadata}. User {user_guid} on session {session_guid} at {current_time}")
                        if chat:
                            chat.receive_logging_message(f"[{event_code}] {data_logfire}")
                        
                        # Return to prevent production logging contamination
                        return
                        
                except ImportError:
                    pass
            
            # Import here to avoid circular imports
            try:
                from tests.unit.test_logging_capture import is_unit_test_environment
                if is_unit_test_environment():
                    # Unit test logging - print to console (captured by unit test logging system) but skip database/logfire
                    mysource = cls.get_caller_name() if source == "" else source
                    current_time = datetime.now(timezone.utc)
                    timestamp = current_time.strftime("%H:%M:%S.%f")[:-3]
                    user_guid = str(chat.user_guid) if chat else 'None'
                    session_guid = str(chat.session_guid) if chat else 'None'
                    metadata = f'{{"chat length#":{chat.message_count if chat else "-1"}}}'
                    
                    # UNIT TEST ENVIRONMENT: Using print() for test log capture, not LogFire.log() to avoid recursion
                    print(f"{timestamp}[{event_code}], '{mysource}': {data_logfire} {sql_addition if Globals.is_debug_values() else ''}. {exception} Metadata: {metadata}. User {user_guid} on session {session_guid} at {current_time}")
                    if chat:
                        chat.receive_logging_message(f"[{event_code}] {data_logfire}")
                        
                    return
            except ImportError:
                # test_logging_capture not available, continue with normal pytest handling
                pass
            
        # Initialize class-level queue if it doesn't exist
        if not hasattr(cls, '_log_queue'):
            cls._log_queue = asyncio.Queue()
            cls._log_worker_started = False
        
        async def log_worker():
            """Worker that processes log entries in FIFO order"""
            while True:
                try:
                    log_entry = await cls._log_queue.get()
                    if log_entry is None:  # Shutdown signal
                        break
                    
                    instance = cls.get_instance()
                    await instance.log_internal(*log_entry)
                    cls._log_queue.task_done()
                    
                except Exception as e:
                    # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in error handling
                    print(f"Error during logging: {e}")
                    cls._log_queue.task_done()
                    cls._log_worker_started = False
                    cls._log_queue = None
        
        async def enqueue_log():
            """Enqueue the log entry and ensure worker is running"""
            try:
                mysource = cls.get_caller_name() if source == "" else source
                log_data = (
                    event_code,
                    f"{data_logfire} " + (sql_addition if Globals.is_debug_values() else ""),
                    f"{data_logfire} {sql_addition}",
                    chat,
                    mysource,
                    exception,
                    severity
                )
                
                # Start worker if not already running
                if not cls._log_worker_started:
                    cls._log_worker_started = True
                    asyncio.create_task(log_worker())
                
                await cls._log_queue.put(log_data)
                
            except Exception as e:
                # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in error handling
                print(f"Error enqueueing log: {e}")
        
        # Execute the enqueueing logic - thread-safe approach
        # Check if we have a running event loop in current context
        loop = None
        if hasattr(asyncio, '_get_running_loop') and asyncio._get_running_loop():
            loop = asyncio._get_running_loop()
        
        if loop and loop.is_running():
            # We have a running event loop, use it
            loop.create_task(enqueue_log())
        else:
            # No running event loop, run synchronously
            asyncio.run(enqueue_log())

    @classmethod
    async def shutdown_log_worker(cls):
        """Shutdown the background log worker and cleanup resources"""
        try:
            if hasattr(cls, '_log_queue') and cls._log_queue:
                # Send shutdown signal to worker
                await cls._log_queue.put(None)
                
                # Wait for all pending log entries to be processed
                await cls._log_queue.join()
                
                # Reset worker state
                cls._log_worker_started = False
                cls._log_queue = None
                
                # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion
                print("LogFire worker shutdown completed")
        except Exception as e:
            # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion
            print(f"Error shutting down LogFire worker: {e}")

    @classmethod
    async def cleanup_all_resources(cls):
        """Cleanup all LogFire resources including workers and database connections"""
        try:
            # Shutdown log worker
            await cls.shutdown_log_worker()
            
            # Close database connections
            from managers.manager_postgreSQL import PostgreSQLManager
            await PostgreSQLManager.close_all_connections()
            
            # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion
            print("LogFire cleanup completed")
        except Exception as e:
            # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion
            print(f"Error during LogFire cleanup: {e}")

    # @classmethod
    # def log(cls, event_code: Literal["INIT", "RETRIEVE", "TOP", "TASK", "OUTPUT", "USER", "ERROR"], data_logfire: str, suffix_sql: str = "", user: Optional["ZairaUser"] = None, source: str = "", exception: str = "", severity: Literal["debug", "info", "warning", "error"] = "info"):
    #     async def runthreadsafe(mysource):
    #         try:
    #             instance = cls.get_instance()
    #             while instance.isLogging == True:
    #                 await sleep(1)
    #             instance.isLogging = True
    #             await cls.get_instance().log_internal(event_code, f"{data_logfire} " + (suffix_sql if Globals.is_debug_values() else ""), f"{data_logfire} {suffix_sql}", user, mysource, exception, severity)
    #             instance.isLogging = False
    #         except Exception as e:
    #             print("Error during logging. Welp!")
    #     try:
    #         mysource = cls.get_caller_name() if source == "" else source
    #         loop = asyncio_get_running_loop()
    #         loop.create_task(runthreadsafe(mysource))
    #     except RuntimeError:
    #         asyncio_run(runthreadsafe())
    #         return

    @classmethod
    async def log_internal(cls, event_code: str, data_logfire: str, data_sql: str, chat: Optional["ZairaChat"], source: str, exception: str, severity: str):
        from managers.manager_postgreSQL import PostgreSQLManager
        instance = cls.get_instance()
        # Create timezone-naive datetime for PostgreSQL TIMESTAMP compatibility
        timestamp = datetime.now(timezone.utc).replace(tzinfo=None)

        company = etc.helper_functions.get_value_from_env("ZAIRA_NETWORK_NAME", "")
        # Safely handle data_sql parameter
        data_sql = data_sql.replace('\n', '\t\t\t') if data_sql is not None else ""
        
        # Extract user information from chat
        user_guid = None
        session_guid = None
        chat_length = -1
        
        if chat:
            try:
                user_guid = str(chat.user_guid) if chat.user_guid else None
                session_guid = str(chat.session_guid) if chat.session_guid else None
                chat_length = chat.message_count if hasattr(chat, 'message_count') else -1
                chat.receive_logging_message(f"[{event_code}] {data_logfire}")
            except Exception as chat_error:
                # If chat object is malformed, continue with defaults
                user_guid = None
                session_guid = None
                chat_length = -1
        
        params = [
            timestamp,
            user_guid,
            session_guid,
            severity,
            source,
            event_code,
            data_sql,
            exception,
            "{" + f"""\"chat length#\":{chat_length}""" + "}"#, \"environment\": \"{company}\"""" + "}",
        ]

        # Also store log in the database
        # First check if connection exists, if not create it
        # Always use pooled connections for LogFire database operations
        try:
            pool = await PostgreSQLManager.get_connection("vectordb")
            if pool is None:
                # Create database first if it doesn't exist
                await PostgreSQLManager.create_database("vectordb")
                # Then connect to it with pooling
                pool = await PostgreSQLManager.get_connection("vectordb")

            query = """INSERT INTO LogEntries (Timestamp, UserID, SessionID, Level, Source, EventCode, Value, Exception, MetaData) VALUES ($1, $2, $3, $4, $5, $6, $7, $8, $9)"""
            try:
                await PostgreSQLManager.execute_query("vectordb", query, params)
            except Exception as db_error:
                # Database logging failed, but don't crash the logging system
                # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in database error handling
                print(f"Database logging failed: {db_error}")
                # Continue with LogFire logging even if database fails
            #await PostgreSQLManager.close_connection("vectordb")
        except BaseException as e:
            pass
        
        # Finally when all other places are logged, add to LogFire
        # Add defensive parameter validation to prevent exceptions
        try:
            # Validate and sanitize parameters before creating attributes dictionary
            safe_timestamp = params[0] if params[0] is not None else datetime.now(timezone.utc).replace(tzinfo=None)
            safe_user_guid = str(params[1]) if params[1] is not None else "unknown"
            safe_session_guid = str(params[2]) if params[2] is not None else "unknown"
            safe_severity = params[3] if params[3] is not None else "info"
            safe_source = params[4] if params[4] is not None else "unknown"
            safe_event_code = params[5] if params[5] is not None else "UNKNOWN"
            safe_exception = params[7] if params[7] is not None else ""
            safe_metadata = params[8] if params[8] is not None else "{}"
            safe_company = company if company is not None else "unknown"
            safe_data_logfire = data_logfire if data_logfire is not None else ""
            
            attributes = {
                "timestamp": safe_timestamp,
                "userGUID": safe_user_guid,
                "sessieGUID": safe_session_guid,
                "severity": safe_severity,
                "source": safe_source,
                "event_code": safe_event_code,
                "value": safe_data_logfire,
                "exception": safe_exception,
                "metadata": safe_metadata,
                "company": safe_company
            }
            logfire.log(level=safe_severity, msg_template="""[{company}][{event_code}], '{source}': {value}. {exception} Metadata: {metadata}. User {userGUID} on session {sessieGUID} at {timestamp}""", attributes=attributes)
        except Exception as logfire_error:
            # Fallback error reporting if LogFire logging fails
            # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in LogFire error handling
            print(f"LogFire logging failed: {logfire_error}")
            # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in LogFire error handling
            print(f"Original log data - Event: {event_code}, Source: {source}, Data: {data_logfire}")
            # Attempt minimal logging without attributes
            try:
                logfire.log(level="error", msg_template="LogFire logging system error", attributes={"error": str(logfire_error)})
            except:
                pass  # If even basic LogFire fails, don't crash the system

    @classmethod
    async def force_refresh_debug_trace(cls) -> bool:
        """Force refresh debug_trace.log file by clearing and restarting it"""
        try:
            log_path = await cls.get_debug_trace_path()
            print(f"[DEBUG] Force refreshing debug trace file: {log_path}")
            
            # Clear the file and restart with a fresh header
            with open(log_path, 'w', encoding='utf-8') as f:
                f.write(f"=== DEBUG TRACE FORCE REFRESHED at {datetime.now()} ===\n")
            
            print(f"[DEBUG] Debug trace file successfully refreshed")
            return True
            
        except Exception as e:
            print(f"[ERROR] Failed to refresh debug trace file: {e}")
            return False

    @classmethod
    async def get_debug_trace_path(cls) -> str:
        """Get current debug_trace.log file path with dynamic resolution"""
        try:
            # Check multiple possible locations for debug_trace.log
            possible_paths = [
                "./debug_trace.log",                    # Current working directory
                "./src/AgenticRAG/debug_trace.log",    # When running from project root
                "../debug_trace.log",                   # When running from subdirectory
            ]
            
            for path in possible_paths:
                if os.path.exists(path):
                    return path
            
            # If no file found, return the most likely path based on working directory
            cwd = os.getcwd()
            if "src/AgenticRAG" in cwd:
                # Running from within src/AgenticRAG
                return "./debug_trace.log"
            else:
                # Running from project root
                return "./src/AgenticRAG/debug_trace.log"
                    
        except Exception:
            # Silent fallback on error
            return "./debug_trace.log"

    @classmethod
    async def get_log_tail(cls, last_position: int = 0, max_lines: int = 100, filter_options: dict = None, filter_type: str = "debug") -> dict:
        """Get recent log entries from debug_trace.log - UNIFIED LOG READING
        
        Args:
            last_position: File position to read from
            max_lines: Maximum lines to return
            filter_options: Additional filtering options (search, exclude patterns)
            filter_type: Type of filtering - "debug", "console", or "system"
        """
        try:
            log_path = await cls.get_debug_trace_path()
            
            if not os.path.exists(log_path):
                return {
                    'content': '',
                    'new_position': 0,
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'has_more': False,
                    'file_size': 0
                }
            
            # Read entire file content with enhanced debugging and file stat checking
            file_stat = os.stat(log_path)
            file_mtime = file_stat.st_mtime
            file_size_bytes = file_stat.st_size
            
            with open(log_path, 'r', encoding='utf-8', errors='ignore') as f:
                full_content = f.read()
                file_size = len(full_content)
            
            # File analysis without debug output
            
            # Position validation (silent)
            
            # ENHANCED: File recreation and position validation
            content_to_return = ""
            position_reset = False
            file_recreated = False
            
            # ENHANCED: Detect file recreation scenarios with better logic
            if last_position > 0:
                # Method 1: Position beyond file size (obvious recreation)
                if last_position > file_size:
                    file_recreated = True
                
                # Method 2: File starts with debug trace header (strong indicator of recreation)
                elif full_content.startswith('=== DEBUG TRACE STARTED'):
                    file_recreated = True
                
                # Method 3: Suspicious position equality (cached position matches current size)
                elif last_position == file_size:
                    # If we have a debug trace header AND position==size, definitely recreated
                    if full_content.startswith('=== DEBUG TRACE STARTED'):
                        file_recreated = True
                
                # Method 4: Large position gap that suggests file was recreated at different size
                elif last_position < file_size and last_position > 50000:
                    # Check if the content at the cached position makes sense
                    if last_position < len(full_content):
                        if '=== DEBUG TRACE STARTED' in full_content[:200]:
                            file_recreated = True
            
            # File recreation analysis complete (silent)
                
            # Process based on detection results
            if file_recreated or last_position == 0:
                # File recreation detected or initial load - return full content
                content_to_return = full_content
                position_reset = True
            elif last_position > 0:
                if last_position > file_size:
                    # Position beyond file - shouldn't happen due to recreation detection above
                    content_to_return = full_content
                    position_reset = True
                elif last_position == file_size:
                    # Genuinely at end of file - no new content
                    content_to_return = ""
                else:
                    # Read incremental content from last position
                    incremental_content = full_content[last_position:]
                    
                    if incremental_content.strip():
                        # Check if incremental content looks like valid log content
                        lines = incremental_content.split('\n')[:3]  # Check first few lines
                        valid_log_content = any(
                            '[' in line or ']' in line or ':' in line or 'DEBUG' in line or 'ERROR' in line or 'INFO' in line
                            for line in lines if line.strip()
                        )
                        
                        if valid_log_content:
                            content_to_return = incremental_content
                        else:
                            content_to_return = full_content
                            position_reset = True
                    else:
                        # No new content since last position
                        content_to_return = ""
            
            # Calculate new position correctly
            if content_to_return and not position_reset:
                # Incremental read - advance position
                new_position = last_position + len(content_to_return)
            else:
                # Full content read or no new content
                new_position = file_size
            
            # Apply filter type-specific processing
            if content_to_return:
                content_to_return = await cls._apply_log_filter(content_to_return, filter_type, filter_options)
            
            # Show ENTIRE log file content - no line limiting
            # User wants to see ALL lines from beginning to end
            
            result = {
                'content': content_to_return,
                'new_position': new_position,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'has_more': False,
                'file_size': file_size,
                'position_reset': position_reset  # Signal to browser that position was reset
            }
            
            # Log reading complete (silent)
            
            return result
            
        except Exception as e:
            print(f"Failed to read log tail: {e}")
            return {
                'content': '',
                'new_position': last_position,
                'timestamp': datetime.now(timezone.utc).isoformat(),
                'has_more': False,
                'error': str(e)
            }

    @classmethod
    async def _apply_log_filter(cls, content: str, filter_type: str, filter_options: dict = None) -> str:
        """Apply filtering based on filter type to ensure consistent log display
        
        Args:
            content: Raw log content to filter
            filter_type: "debug", "console", or "system"
            filter_options: Additional filtering options (search terms, exclusions, etc.)
        
        Returns:
            Filtered content based on the specified filter type
        """
        if not content.strip():
            return content
        
        lines = content.split('\n')
        filtered_lines = []
        
        for line in lines:
            line_stripped = line.strip()
            if not line_stripped:
                continue
                
            # Apply filter type-specific logic
            if filter_type == "debug":
                # Debug view: Show all structured LogFire logs (default behavior)
                # This matches what Python debugger shows
                filtered_lines.append(line)
                
            elif filter_type == "console":
                # Console view: Show non-LogFire structured output (like original console endpoint)
                # Skip LogFire structured logs but keep console-style output
                if (not line_stripped.startswith('[') or 
                    ('FRONTEND_DEBUG:' not in line and
                     not any(event in line for event in ['[INIT]', '[TASK]', '[USER]', '[ERROR]', '[DEBUG]', '[REQUEST]']))):
                    filtered_lines.append(line)
                    
            elif filter_type == "system":
                # System view: Show startup, warnings, and important system messages
                if any(pattern in line for pattern in [
                    'WARNING:', 'ERROR:', 'INFO:', 
                    'Database', 'Server started', 'Bot setup',
                    'SYSTEM user', 'created/verified',
                    '=== DEBUG TRACE STARTED',
                    'Logfire project URL'
                ]) and 'FRONTEND_DEBUG:' not in line:
                    filtered_lines.append(line)
            else:
                # Unknown filter type, default to debug behavior
                filtered_lines.append(line)
        
        # Apply additional filtering options if provided
        if filter_options:
            filtered_lines = await cls._apply_additional_filters(filtered_lines, filter_options)
        
        return '\n'.join(filtered_lines)

    @classmethod 
    async def _apply_additional_filters(cls, lines: list, filter_options: dict) -> list:
        """Apply additional filtering options like search terms, exclusions"""
        if not filter_options:
            return lines
            
        filtered = lines.copy()
        
        # Search term filtering
        search_term = filter_options.get('search_term')
        if search_term and search_term.strip():
            filtered = [line for line in filtered if search_term.lower() in line.lower()]
        
        # Exclude frontend debug noise
        if filter_options.get('exclude_frontend_debug', False):
            filtered = [line for line in filtered if 'FRONTEND_DEBUG:' not in line]
        
        # Exclude scrubbed auth messages
        if filter_options.get('exclude_scrubbed', False):
            filtered = [line for line in filtered if 'Scrubbed due to' not in line]
        
        # Event code filtering
        event_codes = filter_options.get('event_codes', [])
        if event_codes and len(event_codes) > 0:
            # Only show lines containing specified event codes
            def line_contains_event_code(line, codes):
                return any(f'[{code}]' in line for code in codes)
            filtered = [line for line in filtered if line_contains_event_code(line, event_codes)]
        
        return filtered

    @classmethod
    async def search_log_entries(cls, search_pattern: str, start_time, min_count: int = 1) -> Dict[str, Any]:
        """
        Search for log entries in test log files directly (no database).
        
        This method searches the current test log file for patterns, providing
        a clean interface for test assertions and monitoring.
        
        Args:
            search_pattern: Pattern to search for in log content (SQL LIKE % wildcards converted to text search)
            start_time: Datetime to filter entries after this time (compared against log timestamps)
            min_count: Minimum number of entries required (default: 1)
            
        Returns:
            Dict containing execution_count, latest_execution, meets_minimum, and optional error
            
        Example:
            result = await LogFire.search_log_entries(
                search_pattern="%Adding state to tool email_generator_tool%",
                start_time=test_start_time,
                min_count=2
            )
            if result["meets_minimum"]:
                print(f"Found {result['execution_count']} executions")
        """
        try:
            # Get current test log file path
            log_file_path = None
            try:
                from tests.test_real.test_debug_capture import get_current_test_log_path
                log_file_path = get_current_test_log_path()
            except ImportError:
                # Fallback: try to find log file in debug_trace.log
                log_file_path = await cls.get_debug_trace_path()
            
            if not log_file_path or not os.path.exists(log_file_path):
                return {
                    "execution_count": 0,
                    "latest_execution": None,
                    "meets_minimum": False,
                    "error": "Log file not found or not accessible"
                }
            
            # Convert SQL LIKE pattern to simple text search (remove % wildcards)
            search_text = search_pattern.replace("%", "").strip()
            
            # Read log file content
            with open(log_file_path, 'r', encoding='utf-8', errors='ignore') as f:
                log_content = f.read()
            
            # Split into lines for timestamp parsing
            lines = log_content.split('\n')
            matching_lines = []
            
            # Search for pattern in each line
            for line in lines:
                if search_text in line:
                    # Try to extract timestamp from line
                    line_timestamp = None
                    try:
                        # Look for timestamp patterns in the line:
                        # [2025-08-19 14:32:02.347] or 12:32:02.347
                        import re
                        timestamp_match = re.search(r'\[(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2})', line)
                        if timestamp_match:
                            from datetime import datetime
                            line_timestamp = datetime.strptime(timestamp_match.group(1), "%Y-%m-%d %H:%M:%S")
                        else:
                            # Fallback: look for time-only pattern
                            time_match = re.search(r'(\d{2}:\d{2}:\d{2})', line)
                            if time_match and start_time:
                                # Use start_time date with extracted time
                                time_str = time_match.group(1)
                                line_timestamp = datetime.strptime(f"{start_time.date()} {time_str}", "%Y-%m-%d %H:%M:%S")
                    except Exception:
                        # If timestamp parsing fails, include the line anyway
                        line_timestamp = None
                    
                    # Apply time filter if we have both timestamps
                    if start_time and line_timestamp:
                        if line_timestamp >= start_time:
                            matching_lines.append((line, line_timestamp))
                    else:
                        # No time filter or couldn't parse - include the match
                        matching_lines.append((line, line_timestamp))
            
            execution_count = len(matching_lines)
            latest_execution = None
            
            # Find latest execution timestamp
            if matching_lines:
                timestamps = [ts for _, ts in matching_lines if ts is not None]
                if timestamps:
                    latest_execution = max(timestamps)
            
            # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in LogFire manager itself
            print(f"LogFire.search_log_entries: Found {execution_count} matches for '{search_text}' in {log_file_path}")
            
            return {
                "execution_count": execution_count,
                "latest_execution": latest_execution,
                "meets_minimum": execution_count >= min_count
            }
            
        except Exception as e:
            # RECURSION PREVENTION: Using print() to avoid LogFire.log() recursion in LogFire manager itself
            print(f"LogFire.search_log_entries error: {e}")
            return {
                "execution_count": 0,
                "latest_execution": None,
                "meets_minimum": False,
                "error": str(e)
            }
