from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import patch, MagicMock, mock_open
from tempfile import TemporaryDirectory
from os import makedirs, environ
from pathlib import Path
import subprocess

# Import the module we're testing
from etc.helper_functions import (
    is_claude_environment,
    get_any_message_as_type,
    folder_has_files,
    convert_key_value_to_string,
    exception_triggered,
    handle_asyncio_task_result_errors,
    call_cmd_debug,
    call_network_docker,
    get_value_from_env,
    save_to_env,
    create_html_out,
    get_password
)

class TestHelperFunctions:
    """Test suite for helper_functions.py"""

    def test_is_claude_environment_with_claude_code(self):
        """Test is_claude_environment with CLAUDE_CODE set"""
        with patch.dict(environ, {'CLAUDE_CODE': '1'}):
            result = is_claude_environment()
            assert result is True

    def test_is_claude_environment_with_anthropic_user_id(self):
        """Test is_claude_environment with ANTHROPIC_USER_ID set"""
        with patch.dict(environ, {'ANTHROPIC_USER_ID': 'test-user'}):
            result = is_claude_environment()
            assert result is True

    def test_is_claude_environment_with_both_set(self):
        """Test is_claude_environment with both environment variables set"""
        with patch.dict(environ, {'CLAUDE_CODE': '1', 'ANTHROPIC_USER_ID': 'test-user'}):
            result = is_claude_environment()
            assert result is True

    def test_is_claude_environment_with_none_set(self):
        """Test is_claude_environment with no environment variables set"""
        with patch.dict(environ, {}, clear=True):
            result = is_claude_environment()
            assert result is False

    def test_is_claude_environment_with_empty_values(self):
        """Test is_claude_environment with empty environment variables"""
        with patch.dict(environ, {'CLAUDE_CODE': '', 'ANTHROPIC_USER_ID': ''}):
            result = is_claude_environment()
            assert result is False

    def test_get_any_message_as_type_returns_tuple(self):
        """Test get_any_message_as_type returns correct message types"""
        message_types = get_any_message_as_type()
        assert isinstance(message_types, tuple)
        assert len(message_types) == 12

    def test_get_any_message_as_type_contains_expected_types(self):
        """Test get_any_message_as_type contains expected message types"""
        message_types = get_any_message_as_type()
        type_names = [cls.__name__ for cls in message_types]
        
        expected_types = [
            'AIMessage', 'HumanMessage', 'ChatMessage', 'SystemMessage',
            'FunctionMessage', 'ToolMessage', 'AIMessageChunk', 'HumanMessageChunk',
            'ChatMessageChunk', 'SystemMessageChunk', 'FunctionMessageChunk', 'ToolMessageChunk'
        ]
        
        for expected_type in expected_types:
            assert expected_type in type_names

    def test_folder_has_files_existing_folder_with_files(self):
        """Test folder_has_files with existing folder containing files"""
        with TemporaryDirectory() as temp_dir:
            # Create a test file
            test_file = os_path.join(temp_dir, 'test.txt')
            with open(test_file, 'w') as f:
                f.write('test content')
            
            result = folder_has_files(temp_dir)
            assert result is True

    def test_folder_has_files_existing_folder_without_files(self):
        """Test folder_has_files with existing folder without files"""
        with TemporaryDirectory() as temp_dir:
            # Create a subdirectory but no files
            subdir = os_path.join(temp_dir, 'subdir')
            makedirs(subdir)
            
            result = folder_has_files(temp_dir)
            assert result is False

    def test_folder_has_files_nonexistent_folder(self):
        """Test folder_has_files with non-existent folder"""
        with patch('builtins.print') as mock_print:
            result = folder_has_files('/nonexistent/folder')
            assert result is False
            mock_print.assert_called_once()

    def test_folder_has_files_empty_folder(self):
        """Test folder_has_files with empty folder"""
        with TemporaryDirectory() as temp_dir:
            result = folder_has_files(temp_dir)
            assert result is False

    def test_convert_key_value_to_string_single_pair(self):
        """Test convert_key_value_to_string with single key-value pair"""
        result = convert_key_value_to_string(test_key='test_value')
        assert result == 'test key: test_value'

    def test_convert_key_value_to_string_multiple_pairs(self):
        """Test convert_key_value_to_string with multiple key-value pairs"""
        result = convert_key_value_to_string(first_key='first_value', second_key='second_value')
        lines = result.split('\n\n')
        assert len(lines) == 2
        assert 'first key: first_value' in lines
        assert 'second key: second_value' in lines

    def test_convert_key_value_to_string_empty_kwargs(self):
        """Test convert_key_value_to_string with no arguments"""
        result = convert_key_value_to_string()
        assert result == ''

    def test_convert_key_value_to_string_underscore_replacement(self):
        """Test convert_key_value_to_string replaces underscores with spaces"""
        result = convert_key_value_to_string(my_test_key='value')
        assert result == 'my test key: value'

    @patch('managers.manager_logfire.LogFire')
    @patch('globals.Globals')
    @patch('builtins.print')
    @patch('traceback.format_exc')
    def test_exception_triggered_debug_mode(self, mock_format_exc, mock_print, mock_globals, mock_logfire):
        """Test exception_triggered in debug mode"""
        mock_format_exc.return_value = 'test traceback'
        mock_globals.is_debug.return_value = True
        
        test_exception = ValueError('test error')
        
        # Test that function runs without breakpoint during testing
        exception_triggered(test_exception, 'test_affix')
        
        mock_print.assert_called()
        mock_logfire.log.assert_called_once_with(
            'ERROR', 'test traceback', 'test_affix', None, 'test error', 'error'
        )

    @patch('managers.manager_logfire.LogFire')
    @patch('globals.Globals')
    @patch('builtins.print')
    @patch('traceback.format_exc')
    def test_exception_triggered_production_mode(self, mock_format_exc, mock_print, mock_globals, mock_logfire):
        """Test exception_triggered in production mode"""
        mock_format_exc.return_value = 'test traceback'
        mock_globals.is_debug.return_value = False
        
        test_exception = ValueError('test error')
        
        # Test that function runs without exit during testing
        exception_triggered(test_exception, 'test_affix')
        
        mock_print.assert_called()
        mock_logfire.log.assert_called_once_with(
            'ERROR', 'test traceback', 'test_affix', None, 'test error', 'error'
        )

    @patch('managers.manager_logfire.LogFire')
    @patch('globals.Globals')
    @patch('builtins.print')
    @patch('traceback.format_exc')
    def test_exception_triggered_with_user(self, mock_format_exc, mock_print, mock_globals, mock_logfire):
        """Test exception_triggered with user parameter"""
        mock_format_exc.return_value = 'test traceback'
        mock_globals.is_debug.return_value = True
        
        test_exception = ValueError('test error')
        mock_user = MagicMock()
        
        with patch('builtins.breakpoint'):
            exception_triggered(test_exception, 'test_affix', mock_user)
            
            mock_logfire.log.assert_called_once_with(
                'ERROR', 'test traceback', 'test_affix', mock_user, 'test error', 'error'
            )

    def test_handle_asyncio_task_result_errors_no_exception(self):
        """Test handle_asyncio_task_result_errors with successful task"""
        mock_task = MagicMock()
        mock_task.result.return_value = 'success'
        
        # Should not raise any exception
        handle_asyncio_task_result_errors(mock_task)
        mock_task.result.assert_called_once()

    def test_handle_asyncio_task_result_errors_with_exception(self):
        """Test handle_asyncio_task_result_errors with exception in task"""
        mock_task = MagicMock()
        mock_task.result.side_effect = RuntimeError('test error')
        
        with patch('builtins.print') as mock_print:
            handle_asyncio_task_result_errors(mock_task)
            mock_print.assert_called_once_with('Caught exception: test error')

    @patch('etc.helper_functions.Globals')
    def test_call_cmd_debug_docker_environment(self, mock_globals):
        """Test call_cmd_debug in docker environment"""
        mock_globals.is_docker.return_value = True
        
        result = call_cmd_debug('/test/path', 'python', 'test.py arg1')
        
        assert result is None

    @patch('globals.Globals')
    @patch('subprocess.run')
    def test_call_cmd_debug_non_docker_success(self, mock_run, mock_globals):
        """Test call_cmd_debug in non-docker environment with success"""
        mock_globals.is_docker.return_value = False
        mock_result = MagicMock()
        mock_result.stdout = 'command output'
        mock_run.return_value = mock_result
        
        result = call_cmd_debug('/test/path', 'python', 'test.py arg1')
        
        assert result == 'command output'
        mock_run.assert_called_once_with(
            ['python', 'test.py', 'arg1'],
            capture_output=True,
            text=True,
            check=True,
            cwd='/test/path'
        )

    @patch('globals.Globals')
    @patch('subprocess.run')
    @patch('etc.helper_functions.exception_triggered')
    def test_call_cmd_debug_non_docker_error(self, mock_exception_triggered, mock_run, mock_globals):
        """Test call_cmd_debug in non-docker environment with error"""
        mock_globals.is_docker.return_value = False
        mock_run.side_effect = subprocess.CalledProcessError(1, 'python', stderr='error message')
        
        with patch('builtins.print') as mock_print:
            call_cmd_debug('/test/path', 'python', 'test.py')
            
            mock_print.assert_called_once_with('Call_Network_Docker Error:\n', 'error message')
            mock_exception_triggered.assert_called_once()

    @patch('etc.helper_functions.get_value_from_env')
    @patch('etc.helper_functions.Globals')
    @patch('subprocess.run')
    def test_call_network_docker_docker_environment_success(self, mock_run, mock_globals, mock_get_env):
        """Test call_network_docker in docker environment with success"""
        mock_globals.is_docker.return_value = True
        mock_get_env.return_value = 'test_network'
        mock_result = MagicMock()
        mock_result.stdout = 'docker output'
        mock_run.return_value = mock_result
        
        result = call_network_docker('test_container', 'ls -la')
        
        assert result == 'docker output'
        mock_run.assert_called_once_with(
            ['docker', 'exec', 'test_network-test_container', 'test_container', 'ls', '-la'],
            capture_output=True,
            text=True,
            check=True
        )

    @patch('etc.helper_functions.get_value_from_env')
    @patch('etc.helper_functions.Globals')
    @patch('etc.helper_functions.call_cmd_debug')
    def test_call_network_docker_non_docker_environment(self, mock_call_cmd_debug, mock_globals, mock_get_env):
        """Test call_network_docker in non-docker environment"""
        mock_globals.is_docker.return_value = False
        mock_call_cmd_debug.return_value = 'command output'
        
        result = call_network_docker('test_container', 'ls -la')
        
        mock_call_cmd_debug.assert_called_once_with(
            'src/meltano/askzaira', 'test_container', 'ls -la'
        )
        assert result == 'command output'  # Function returns the result from call_cmd_debug

    @patch('etc.helper_functions.get_value_from_env')
    @patch('globals.Globals')
    @patch('subprocess.run')
    @patch('etc.helper_functions.exception_triggered')
    def test_call_network_docker_docker_environment_error(self, mock_exception_triggered, mock_run, mock_globals, mock_get_env):
        """Test call_network_docker in docker environment with error"""
        mock_globals.is_docker.return_value = True
        mock_get_env.return_value = 'test_network'
        mock_run.side_effect = subprocess.CalledProcessError(1, 'docker', stderr='docker error')
        
        with patch('builtins.print') as mock_print:
            call_network_docker('test_container', 'ls -la')
            
            mock_print.assert_called_once_with('Call_Network_Docker Error:\n', 'docker error')
            mock_exception_triggered.assert_called_once()

    @patch('os.getenv')
    def test_get_value_from_env_existing_value(self, mock_getenv):
        """Test get_value_from_env with existing environment variable"""
        mock_getenv.return_value = 'test_value'
        
        result = get_value_from_env('TEST_KEY', 'default_value')
        
        assert result == 'test_value'
        mock_getenv.assert_called_once_with('TEST_KEY')

    @patch('os.getenv')
    def test_get_value_from_env_missing_value_string_default(self, mock_getenv):
        """Test get_value_from_env with missing value and string default"""
        mock_getenv.return_value = None
        
        result = get_value_from_env('TEST_KEY', 'default_value')
        
        assert result == 'default_value'

    @patch('os.getenv')
    def test_get_value_from_env_missing_value_list_default(self, mock_getenv):
        """Test get_value_from_env with missing value and list default"""
        mock_getenv.return_value = None
        
        result = get_value_from_env('TEST_KEY', ['default1', 'default2'])
        
        assert result == ['default1', 'default2']

    @patch('os.getenv')
    def test_get_value_from_env_list_parsing(self, mock_getenv):
        """Test get_value_from_env with list parsing enabled"""
        mock_getenv.return_value = 'item1, item2, item3'
        
        result = get_value_from_env('TEST_KEY', '', can_be_list=True)
        
        assert result == ['item1', 'item2', 'item3']

    @patch('os.getenv')
    def test_get_value_from_env_list_parsing_no_commas(self, mock_getenv):
        """Test get_value_from_env with list parsing but no commas"""
        mock_getenv.return_value = 'single_item'
        
        result = get_value_from_env('TEST_KEY', '', can_be_list=True)
        
        assert result == 'single_item'

    @patch('os.getenv')
    def test_get_value_from_env_list_parsing_empty_items(self, mock_getenv):
        """Test get_value_from_env with list parsing and empty items"""
        mock_getenv.return_value = 'item1, , item3, '
        
        result = get_value_from_env('TEST_KEY', '', can_be_list=True)
        
        assert result == ['item1', 'item3']

    @patch('globals.Globals')
    @patch('os.getcwd')
    @patch('os.path.exists')
    @patch('builtins.open', new_callable=mock_open, read_data='EXISTING_KEY=old_value\nOTHER_KEY=other_value\n')
    def test_save_to_env_update_existing_key(self, mock_file, mock_exists, mock_getcwd, mock_globals):
        """Test save_to_env updating existing key"""
        mock_globals.is_docker.return_value = False
        mock_getcwd.return_value = '/test/path'
        mock_exists.return_value = True
        
        with patch('managers.manager_logfire.LogFire') as mock_logfire:
            with patch('builtins.print') as mock_print:
                save_to_env({'EXISTING_KEY': 'new_value'})
                
                # Check that file was opened for reading and writing
                assert mock_file.call_count >= 2
                
                # Check that LogFire was called
                mock_logfire.log.assert_called()
                
                # Check that print was called
                mock_print.assert_called()

    @patch('globals.Globals')
    @patch('os.getcwd')
    @patch('os.path.exists')
    @patch('builtins.open', new_callable=mock_open, read_data='OTHER_KEY=other_value\n')
    def test_save_to_env_add_new_key(self, mock_file, mock_exists, mock_getcwd, mock_globals):
        """Test save_to_env adding new key"""
        mock_globals.is_docker.return_value = False
        mock_getcwd.return_value = '/test/path'
        mock_exists.return_value = True
        
        with patch('managers.manager_logfire.LogFire') as mock_logfire:
            with patch('builtins.print') as mock_print:
                save_to_env({'NEW_KEY': 'new_value'})
                
                # Check that file was opened for reading and writing
                assert mock_file.call_count >= 2
                
                # Check that LogFire was called
                mock_logfire.log.assert_called()
                
                # Check that print was called
                mock_print.assert_called()

    @patch('globals.Globals')
    @patch('os.getcwd')
    @patch('os.path.exists')
    @patch('builtins.open', new_callable=mock_open)
    def test_save_to_env_new_file(self, mock_file, mock_exists, mock_getcwd, mock_globals):
        """Test save_to_env with new file"""
        mock_globals.is_docker.return_value = False
        mock_getcwd.return_value = '/test/path'
        mock_exists.return_value = False
        
        with patch('managers.manager_logfire.LogFire') as mock_logfire:
            with patch('builtins.print') as mock_print:
                save_to_env({'TEST_KEY': 'test_value'})
                
                # Check that file was opened for writing
                mock_file.assert_called()
                
                # Check that LogFire was called
                mock_logfire.log.assert_called()
                
                # Check that print was called
                mock_print.assert_called()

    @patch('globals.Globals')
    @patch('os.getcwd')
    @patch('os.path.exists')
    @patch('builtins.open', side_effect=IOError('File error'))
    def test_save_to_env_error_handling(self, mock_file, mock_exists, mock_getcwd, mock_globals):
        """Test save_to_env error handling"""
        mock_globals.is_docker.return_value = False
        mock_getcwd.return_value = '/test/path'
        mock_exists.return_value = True
        
        with patch('builtins.print') as mock_print:
            save_to_env({'TEST_KEY': 'test_value'})
            
            # Check that error was printed
            mock_print.assert_called()

    @patch('globals.Globals')
    @patch('os.getcwd')
    @patch('builtins.open', new_callable=mock_open, read_data='head content')
    def test_create_html_out_non_docker(self, mock_file, mock_getcwd, mock_globals):
        """Test create_html_out in non-docker environment"""
        mock_globals.is_docker.return_value = False
        mock_getcwd.return_value = '/test/path'
        
        result = create_html_out('test_page', 'test content')
        
        assert 'test content' in result
        assert '<!DOCTYPE html>' in result
        assert '<html lang="nl">' in result

    @patch('globals.Globals')
    @patch('builtins.open', new_callable=mock_open, read_data='head content')
    def test_create_html_out_docker(self, mock_file, mock_globals):
        """Test create_html_out in docker environment"""
        mock_globals.is_docker.return_value = True
        
        result = create_html_out('test_page', 'test content')
        
        assert 'test content' in result
        assert '<!DOCTYPE html>' in result
        assert '<html lang="nl">' in result

    def test_get_password_consistent_output(self):
        """Test get_password returns consistent output for same input"""
        username = 'testuser'
        password1 = get_password(username)
        password2 = get_password(username)
        
        assert password1 == password2
        assert len(password1) == 32  # MD5 hash length

    def test_get_password_different_users(self):
        """Test get_password returns different passwords for different users"""
        password1 = get_password('user1')
        password2 = get_password('user2')
        
        assert password1 != password2

    def test_get_password_empty_username(self):
        """Test get_password with empty username"""
        password = get_password('')
        
        assert isinstance(password, str)
        assert len(password) == 32

    def test_get_password_unicode_username(self):
        """Test get_password with unicode username"""
        password = get_password('tëst_üser')
        
        assert isinstance(password, str)
        assert len(password) == 32