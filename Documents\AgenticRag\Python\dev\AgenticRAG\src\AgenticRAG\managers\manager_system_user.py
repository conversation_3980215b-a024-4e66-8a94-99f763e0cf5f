from imports import *

from uuid import UUID, uuid4
import asyncio
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any, Optional
import os
from pathlib import Path

from managers.manager_users import ZairaUserManager
from managers.scheduled_requests import ScheduledRequestPersistenceManager
from userprofiles.ZairaUser import PERMISSION_LEVELS
from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest
from etc.helper_functions import is_claude_environment

if TYPE_CHECKING:
    # This only runs for type checkers, not at runtime — safe to "reach inside"
    from userprofiles.ZairaUser import ZairaUser

class SystemUserManager:
    """
    Manages the SYSTEM user and environment-specific scheduled tasks.
    Creates appropriate scheduled tasks based on the runtime environment.
    """
    
    _instance: Optional['SystemUserManager'] = None
    _initialized: bool = False
    SYSTEM_USER_GUID = UUID("00000000-0000-0000-0000-000000000001")  # Fixed GUID for SYSTEM user
    SYSTEM_DEVICE_GUID = UUID("00000000-0000-0000-0000-000000000002")  # Fixed device GUID for SYSTEM
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_instance(cls) -> 'SystemUserManager':
        """Get singleton instance of SystemUserManager"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    async def setup(cls):
        """Initialize the system user"""
        instance = cls.get_instance()
        if instance._initialized:
            return
        
        instance._initialized = True
        
        # Create or retrieve SYSTEM user
        await instance._create_system_user()
        
        LogFire.log("USER", "SystemUserManager initialized with SYSTEM user")
    
    @classmethod
    async def late_setup(cls):
        """Create environment-specific scheduled tasks after all managers are initialized"""
        instance = cls.get_instance()
        try:
            # Create environment-specific scheduled tasks
            await instance._create_environment_tasks()
            LogFire.log("USER", "Environment-specific scheduled tasks created")
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to create environment tasks: {e}")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "SystemUserManager.late_setup", None)
    
    async def _create_system_user(self):
        """Create the SYSTEM user if it doesn't exist"""
        try:
            user_manager = ZairaUserManager.get_instance()
            system_user_guid_str = str(self.SYSTEM_USER_GUID)
            
            LogFire.log("DEBUG", f"Starting SYSTEM user creation process - GUID: {system_user_guid_str}", severity="debug")
            LogFire.log("DEBUG", f"Current users in registry: {len(user_manager.users)} users", severity="debug")
            LogFire.log("DEBUG", f"User registry keys: {list(user_manager.users.keys())[:5]}", severity="debug")  # Show first 5 for debugging
            
            # Check if SYSTEM user already exists
            if system_user_guid_str in user_manager.users:
                LogFire.log("USER", "SYSTEM user already exists in users registry", system_user_guid_str)
                LogFire.log("DEBUG", f"SYSTEM user details: username={user_manager.users[system_user_guid_str].username}, is_system_user={getattr(user_manager.users[system_user_guid_str], 'is_system_user', False)}", severity="debug")
                # Ensure existing SYSTEM user has correct flags
                await self._ensure_system_user_flags()
                return
            
            LogFire.log("DEBUG", "SYSTEM user not found in registry, creating new user", severity="debug")
            
            # Create SYSTEM user with highest permissions
            LogFire.log("DEBUG", "Calling ZairaUserManager.add_user for SYSTEM user", severity="debug")
            system_user = await ZairaUserManager.add_user(
                username="SYSTEM",
                rank=PERMISSION_LEVELS.ADMIN,  # Highest permission level
                guid=self.SYSTEM_USER_GUID,
                device_guid=self.SYSTEM_DEVICE_GUID
            )
            
            LogFire.log("DEBUG", f"ZairaUserManager.add_user returned: {system_user is not None}", severity="debug")
            
            # Set additional system user properties
            system_user.email = "<EMAIL>"
            system_user.is_system_user = True
            
            LogFire.log("DEBUG", f"Set SYSTEM user properties - email: {system_user.email}, is_system_user: {system_user.is_system_user}", severity="debug")
            
            # Verify the user is properly registered in UserManager
            user_manager = ZairaUserManager.get_instance()
            LogFire.log("DEBUG", f"Verification check - SYSTEM GUID in users registry: {system_user_guid_str in user_manager.users}", severity="debug")
            LogFire.log("DEBUG", f"Updated registry size: {len(user_manager.users)} users", severity="debug")
            
            if system_user_guid_str not in user_manager.users:
                LogFire.log("ERROR", f"SYSTEM user was created but not found in users registry! Registry has {len(user_manager.users)} users", system_user_guid_str)
                LogFire.log("DEBUG", f"Current registry keys after creation: {list(user_manager.users.keys())}", severity="debug")
                
                # Force register the user
                user_manager.users[system_user_guid_str] = system_user
                LogFire.log("USER", "SYSTEM user manually registered in users registry", system_user_guid_str)
                LogFire.log("DEBUG", f"After manual registration - registry size: {len(user_manager.users)}, SYSTEM user present: {system_user_guid_str in user_manager.users}", severity="debug")
            else:
                LogFire.log("DEBUG", "SYSTEM user automatically registered in users registry during creation", severity="debug")
            
            LogFire.log("USER", "SYSTEM user created successfully", system_user_guid_str)
            LogFire.log("DEBUG", f"Final verification - can retrieve SYSTEM user from registry: {user_manager.users.get(system_user_guid_str) is not None}", severity="debug")
            
        except ValueError as e:
            if "already exists" in str(e):
                LogFire.log("USER", "SYSTEM user already exists (ValueError caught)", str(self.SYSTEM_USER_GUID))
                LogFire.log("DEBUG", f"ValueError details: {str(e)}", severity="debug")
            else:
                LogFire.log("ERROR", f"Failed to create SYSTEM user (ValueError): {e}", str(self.SYSTEM_USER_GUID))
                raise
        except Exception as e:
            LogFire.log("ERROR", f"Unexpected error creating SYSTEM user: {e}", str(self.SYSTEM_USER_GUID))
            LogFire.log("ERROR", f"Error type: {type(e).__name__}, Details: {str(e)}", severity="error")
            raise
    
    async def _ensure_system_user_flags(self):
        """Ensure existing SYSTEM user has correct is_system_user flag"""
        try:
            user_manager = ZairaUserManager.get_instance()
            system_user_guid_str = str(self.SYSTEM_USER_GUID)
            
            if system_user_guid_str in user_manager.users:
                system_user = user_manager.users[system_user_guid_str]
                
                # Check and update is_system_user flag
                if not system_user.is_system_user:
                    system_user.is_system_user = True
                    LogFire.log("USER", "Updated existing SYSTEM user is_system_user flag to True", system_user_guid_str)
                else:
                    LogFire.log("USER", "SYSTEM user already has correct is_system_user flag", system_user_guid_str)
                
                # Ensure other system properties are set
                if not system_user.email:
                    system_user.email = "<EMAIL>"
                
                # Ensure the user is also registered in the main users registry
                if system_user_guid_str not in user_manager.users:
                    LogFire.log("WARNING", "SYSTEM user found but not registered in main users registry, registering now", system_user_guid_str)
                    user_manager.users[system_user_guid_str] = system_user
                    LogFire.log("USER", "SYSTEM user registered in main users registry", system_user_guid_str)
                
                # Debug logging for verification
                LogFire.log("USER", f"SYSTEM user verification - Username: {system_user.username}, is_system_user: {system_user.is_system_user}, rank: {system_user.rank}", system_user_guid_str)
                LogFire.log("DEBUG", f"SYSTEM user registry check - In users dict: {system_user_guid_str in user_manager.users}", system_user_guid_str)
                
            else:
                LogFire.log("ERROR", "SYSTEM user not found in user_manager.users", system_user_guid_str)
        
        except Exception as e:
            LogFire.log("ERROR", f"Failed to ensure SYSTEM user flags: {e}", str(self.SYSTEM_USER_GUID))
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "_ensure_system_user_flags", None)
    
    async def _create_environment_tasks(self):
        """Create scheduled tasks based on the current environment"""
        try:
            # Detect environment
            is_claude = is_claude_environment()
            is_docker = Globals.is_docker()
            is_debug = Globals.is_debug()
            
            LogFire.log("USER", f"Environment detection: Claude={is_claude}, Docker={is_docker}, Debug={is_debug}")
            
            # Create weekly log report task for all environments
            await self._create_weekly_log_report_task()
            LogFire.log("USER", "Weekly log report task created for SYSTEM user")
                
        except Exception as e:
            LogFire.log("ERROR", f"Failed to create environment tasks: {e}")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "SystemUserManager._create_environment_tasks", None)
    
    async def _create_weekly_log_report_task(self):
        """Create a weekly log report task that runs at 9am on Mondays"""
        try:
            # Get SYSTEM user
            system_user = await self.get_system_user()
            if not system_user:
                LogFire.log("ERROR", "SYSTEM user not found, cannot create weekly log report task")
                return
            
            # Check if weekly report task already exists
            persistence_manager = ScheduledRequestPersistenceManager.get_instance()
            existing_tasks = await persistence_manager.get_active_requests(str(self.SYSTEM_USER_GUID))
            
            for task in existing_tasks:
                if "Weekly log report generation" in task.get('schedule_prompt', ''):
                    LogFire.log("USER", "Weekly log report task already exists, skipping creation")
                    return
            
            # Create bot instance for the task
            from endpoints.mybot_generic import MyBot_Generic
            system_bot = MyBot_Generic(None, "SYSTEM_WeeklyLogReport")
            
            # Calculate next Monday at 9am
            from datetime import datetime, timedelta
            now = datetime.now()
            days_until_monday = (7 - now.weekday()) % 7
            if days_until_monday == 0 and now.hour >= 9:
                days_until_monday = 7  # If it's Monday and past 9am, schedule for next Monday
            
            next_monday_9am = now.replace(hour=9, minute=0, second=0, microsecond=0) + timedelta(days=days_until_monday)
            delay_seconds = (next_monday_9am - now).total_seconds()
            
            # Create scheduled request
            from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest, ScheduleType
            weekly_report_task = ScheduledZairaRequest(
                user=system_user,
                calling_bot=system_bot,
                original_message=None,
                schedule_prompt="Weekly log report generation at 9am on Mondays",
                target_prompt="Generate a comprehensive weekly report based on database logs from the past 7 days. Include key metrics, error summaries, user activity patterns, and system health indicators. Format the report professionally for team review.",
                start_delay_seconds=delay_seconds,
                delay_seconds=7 * 24 * 60 * 60,  # 7 days = weekly recurrence
                schedule_type=ScheduleType.RECURRING,
                run_on_startup=False
            )
            
            # Save to persistence
            await weekly_report_task._save_to_persistence()
            
            LogFire.log("USER", f"Weekly log report task created - next run: {next_monday_9am.strftime('%Y-%m-%d %H:%M:%S')}")
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to create weekly log report task: {e}")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "SystemUserManager._create_weekly_log_report_task", None)
    
    async def get_system_user(self) -> "ZairaUser":
        """Get the SYSTEM user instance, ensuring it's properly registered"""
        user_manager = ZairaUserManager.get_instance()
        system_user_guid_str = str(self.SYSTEM_USER_GUID)
        
        LogFire.log("DEBUG", f"get_system_user called - looking for GUID: {system_user_guid_str}", severity="debug")
        LogFire.log("DEBUG", f"Current users registry size: {len(user_manager.users)}", severity="debug")
        
        # Try to get from users registry first
        system_user = user_manager.users.get(system_user_guid_str)
        
        if system_user:
            LogFire.log("DEBUG", f"SYSTEM user found in registry - username: {system_user.username}, is_system_user: {getattr(system_user, 'is_system_user', False)}", severity="debug")
            return system_user
        
        LogFire.log("WARNING", "SYSTEM user not found in users registry, attempting to create/register", system_user_guid_str)
        LogFire.log("DEBUG", f"Registry contents before creation: {list(user_manager.users.keys())[:3]}", severity="debug")
        
        # Try to recreate/register the SYSTEM user
        await self._create_system_user()
        
        # Try again after creation
        system_user = user_manager.users.get(system_user_guid_str)
        
        if system_user:
            LogFire.log("USER", "SYSTEM user successfully created/registered", system_user_guid_str)
            LogFire.log("DEBUG", f"Retrieved SYSTEM user after creation - username: {system_user.username}, is_system_user: {getattr(system_user, 'is_system_user', False)}", severity="debug")
        else:
            LogFire.log("ERROR", "Failed to create/register SYSTEM user even after creation attempt", system_user_guid_str)
            LogFire.log("DEBUG", f"Registry contents after failed creation: {list(user_manager.users.keys())[:3]}", severity="debug")
            LogFire.log("DEBUG", f"Registry size after failed creation: {len(user_manager.users)}", severity="debug")
        
        return system_user
    
    async def cleanup_system_tasks(self):
        """Clean up all SYSTEM user tasks (for testing/maintenance)"""
        try:
            persistence_manager = ScheduledRequestPersistenceManager.get_instance()
            system_tasks = await persistence_manager.get_active_requests(str(self.SYSTEM_USER_GUID))
            
            for task in system_tasks:
                await persistence_manager.cancel_task(task.get("scheduled_guid"), "System cleanup")
            
            LogFire.log("USER", f"Cleaned up {len(system_tasks)} system tasks")
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to cleanup system tasks: {e}")
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "SystemUserManager.cleanup_system_tasks", None)
    

