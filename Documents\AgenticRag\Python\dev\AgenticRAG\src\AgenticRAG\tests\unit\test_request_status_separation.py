from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import Mock, AsyncMock, patch, MagicMock
from uuid import uuid4

@pytest.mark.asyncio
class TestTaskStatusSeparation:
    """Test that request_status is not included in request data and must be fetched separately"""
    
    @pytest.fixture
    def valid_guids(self):
        """Generate valid test GUIDs"""
        return {
            'user_guid': str(uuid4()),
            'scheduled_guid': str(uuid4())
        }
    
    @pytest.mark.asyncio
    async def test_user_manager_requests_no_request_status(self, valid_guids):
        """Test that get_user_requests does not include request_status"""
        from managers.scheduled_requests.core.user_manager import UserScheduledRequestManager
        
        manager = UserScheduledRequestManager(valid_guids['user_guid'])
        
        # Mock an active request
        mock_request = Mock()
        mock_request.get_schedule_info.return_value = {'type': 'daily'}
        mock_request.get_request_status.return_value = {'call_trace': ['test']}
        
        manager._active_requests[valid_guids['scheduled_guid']] = mock_request
        
        # Get user requests
        requests = await manager.get_user_requests(include_completed=False)
        
        # Verify request_status is not included
        assert len(requests) == 1
        assert requests[0]['scheduled_guid'] == valid_guids['scheduled_guid']
        assert requests[0]['status'] == 'active'
        assert 'schedule_info' in requests[0]
        assert 'request_status' not in requests[0]  # Key assertion
    
    @pytest.mark.asyncio
    async def test_user_manager_request_status_separate_method(self, valid_guids):
        """Test that request_status can be fetched separately"""
        from managers.scheduled_requests.core.user_manager import UserScheduledRequestManager
        
        manager = UserScheduledRequestManager(valid_guids['user_guid'])
        
        # Mock an active request
        mock_request = Mock()
        expected_request_status = {'call_trace': ['step1', 'step2'], 'status': 'running'}
        mock_request.get_request_status.return_value = expected_request_status
        
        manager._active_requests[valid_guids['scheduled_guid']] = mock_request
        
        # Get task status separately
        request_status = await manager.get_request_status(valid_guids['scheduled_guid'])
        
        # Verify request_status is returned correctly
        assert request_status == expected_request_status
        assert 'call_trace' in request_status
        assert request_status['call_trace'] == ['step1', 'step2']
    
    @pytest.mark.asyncio
    async def test_admin_interface_removes_request_status(self, valid_guids):
        """Test that admin interface removes request_status from request details"""
        from managers.scheduled_requests.admin_interface import ScheduledRequestAdminInterface
        
        with patch('managers.scheduled_requests.admin_interface.ScheduledRequestIntegrationAdapter') as mock_adapter_class:
            admin = ScheduledRequestAdminInterface()
            
            # Mock persistence layer returning data with request_status
            mock_persistence = AsyncMock()
            mock_persistence.load_request.return_value = {
                'scheduled_guid': valid_guids['scheduled_guid'],
                'status': 'active',
                'schedule_info': {'type': 'daily'},
                'request_status': {'should_be': 'removed'}  # This should be removed
            }
            
            mock_adapter = Mock()
            mock_adapter._persistence = mock_persistence
            mock_adapter._factory = Mock()
            mock_adapter._factory._active_requests = {}
            
            admin._adapter = mock_adapter
            
            # Get request details
            details = await admin.get_request_details(valid_guids['scheduled_guid'])
            
            # Verify request_status was removed
            assert details is not None
            assert 'scheduled_guid' in details
            assert 'schedule_info' in details
            assert 'request_status' not in details  # Key assertion
    
    @pytest.mark.asyncio
    async def test_admin_interface_get_request_status_method(self, valid_guids):
        """Test that admin interface can fetch request_status separately"""
        from managers.scheduled_requests.admin_interface import ScheduledRequestAdminInterface
        
        with patch('managers.scheduled_requests.admin_interface.ScheduledRequestIntegrationAdapter') as mock_adapter_class:
            admin = ScheduledRequestAdminInterface()
            
            # Mock factory and user manager
            mock_user_manager = AsyncMock()
            expected_request_status = {'call_trace': ['action1', 'action2']}
            mock_user_manager.get_request_status.return_value = expected_request_status
            
            mock_factory = AsyncMock()
            mock_factory.get_user_manager.return_value = mock_user_manager
            
            mock_adapter = Mock()
            mock_adapter._factory = mock_factory
            
            admin._adapter = mock_adapter
            
            # Get task status separately
            request_status = await admin.get_request_status(
                valid_guids['scheduled_guid'], 
                valid_guids['user_guid']
            )
            
            # Verify correct request_status is returned
            assert request_status == expected_request_status
            mock_user_manager.get_request_status.assert_called_once_with(valid_guids['scheduled_guid'])
    
    @pytest.mark.asyncio
    async def test_persistence_does_not_save_request_status(self, valid_guids):
        """Test that persistence layer does not save request_status"""
        from managers.scheduled_requests.core.persistence import ScheduledRequestPersistence
        
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db:
            persistence = ScheduledRequestPersistence()
            
            # Mock scheduled request
            mock_request = Mock()
            mock_request.get_schedule_info.return_value = {'type': 'weekly'}
            mock_request.get_request_status.return_value = {'call_trace': ['test']}  # This should NOT be saved
            mock_request.original_physical_message = None
            
            # Mock database connection
            mock_conn = AsyncMock()
            mock_conn.fetchval.return_value = 0  # No existing record
            mock_conn.execute = AsyncMock()
            
            mock_db.get_instance.return_value.get_connection.return_value.__aenter__.return_value = mock_conn
            
            # Save request
            await persistence.save_request(
                valid_guids['scheduled_guid'],
                valid_guids['user_guid'],
                mock_request
            )
            
            # Check what was saved
            execute_calls = mock_conn.execute.call_args_list
            assert len(execute_calls) > 0
            
            # Get the data that was saved
            saved_data_call = [call for call in execute_calls if 'INSERT INTO scheduled_requests' in str(call)]
            if saved_data_call:
                saved_json = saved_data_call[0][0][1][2]  # Third parameter is the JSON data
                import json
                saved_data = json.loads(saved_json)
                
                # Verify request_status was not saved
                assert 'schedule_info' in saved_data
                assert 'request_status' not in saved_data  # Key assertion
    
    @pytest.mark.asyncio  
    async def test_request_status_endpoint_no_request_status(self, valid_guids):
        """Test that get_request_status endpoint does not return request_status"""
        from managers.scheduled_requests.core.user_manager import UserScheduledRequestManager
        
        manager = UserScheduledRequestManager(valid_guids['user_guid'])
        
        # Mock an active request
        mock_request = Mock()
        mock_request.get_schedule_info.return_value = {'type': 'hourly'}
        mock_request.get_request_status.return_value = {'call_trace': ['should_not_be_included']}
        
        manager._active_requests[valid_guids['scheduled_guid']] = mock_request
        
        # Get request status
        status = await manager.get_request_status(valid_guids['scheduled_guid'])
        
        # Verify request_status is not included
        assert status is not None
        assert status['scheduled_guid'] == valid_guids['scheduled_guid']
        assert status['status'] == 'active'
        assert 'schedule_info' in status
        assert 'request_status' not in status  # Key assertion