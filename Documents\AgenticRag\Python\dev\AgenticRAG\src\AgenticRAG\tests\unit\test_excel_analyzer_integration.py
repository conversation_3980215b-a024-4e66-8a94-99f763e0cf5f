from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from tests.unit.test_logging_capture import with_unit_test_logging

class TestExcelAnalyzerIntegration:
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_excel_analyzer_tool_creation(self):
        """Test that ExcelAnalyzerTool can be created and initialized"""
        from tasks.processing.task_excel_analyzer import ExcelAnalyzerTool
        
        tool = ExcelAnalyzerTool()
        assert tool.name == "excel_analyzer_tool"
        assert tool.description == "Analyzes Excel/CSV data and answers questions about the dataset"
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_excel_analyzer_supervisor_creation(self):
        """Test that the Excel analyzer supervisor can be created"""
        from tasks.processing.task_excel_analyzer import create_supervisor_excel_analyzer
        
        # This should not raise an exception
        supervisor = await create_supervisor_excel_analyzer()
        assert supervisor is not None
        assert supervisor.name == "excel_analyzer_supervisor"
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_excel_analyzer_tool_with_sample_data(self):
        """Test ExcelAnalyzerTool with default sample data"""
        from tasks.processing.task_excel_analyzer import ExcelAnalyzerTool
        
        tool = ExcelAnalyzerTool()
        
        # Test with sample query using default data
        result = await tool._arun("How many rows are in the dataset?")
        
        # Should not return an error message
        assert "Error" not in result or "processed" in result.lower()
    
    @with_unit_test_logging
    def test_prompt_registration(self):
        """Test that Excel_Analyzer_Prompt is properly registered"""
        from managers.manager_prompts import PromptManager
        
        prompt_manager = PromptManager.get_instance()
        assert "Excel_Analyzer_Prompt" in prompt_manager.prompts
        
        prompt = prompt_manager.prompts["Excel_Analyzer_Prompt"]
        assert "Excel" in prompt and "analyst" in prompt
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_oauth_verifier_integration(self):
        """Test that Excel_Analyzer_Prompt is mapped in OAuth verifier"""
        from endpoints.oauth._verifier_ import OAuth2Verifier
        
        verifier = OAuth2Verifier.get_instance()
        await verifier.setup()
        
        # Check that ZairaPrompts4 includes Excel Analyzer Prompt
        app = verifier.apps.get("ZairaPrompts4")
        assert app is not None
        
        # Verify the mapping exists
        meltano_mappings = app.meltano_mappings
        assert "Excel_Analyzer_Prompt" in meltano_mappings
        assert meltano_mappings["Excel_Analyzer_Prompt"] == "str7"