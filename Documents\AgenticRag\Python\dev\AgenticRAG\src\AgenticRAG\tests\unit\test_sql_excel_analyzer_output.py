from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from pydantic import ValidationError
import pandas as pd
import json
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime
import os

from tasks.outputs.output_tasks.task_out_sql_excel_analyzer import SQLExcelAnalyzerExportTool, create_task_out_sql_excel_analyzer
from managers.manager_supervisors import SupervisorTaskState
from managers.manager_logfire import LogFire
from tests.unit.test_logging_capture import with_unit_test_logging


class TestSQLExcelAnalyzerExportTool:
    """Unit tests for SQLExcelAnalyzerExportTool"""
    
    @with_unit_test_logging
    def test_tool_initialization(self):
        """Test that the export tool initializes correctly"""
        tool = SQLExcelAnalyzerExportTool()
        assert tool.name == "sql_excel_analyzer_export_tool"
        assert "Excel, CSV, or JSON formats" in tool.description
    
    @with_unit_test_logging
    def test_run_raises_not_implemented(self):
        """Test that sync _run raises NotImplementedError"""
        tool = SQLExcelAnalyzerExportTool()
        with pytest.raises(NotImplementedError, match="Use async version"):
            tool._run(state=None)
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_arun_with_no_state(self):
        """Test _arun when no state is provided"""
        tool = SQLExcelAnalyzerExportTool()
        result = await tool._arun(state=None)
        
        result_data = json.loads(result)
        assert result_data["error"] == "No analysis results to export"
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_arun_with_successful_export(self):
        """Test successful export operation"""
        tool = SQLExcelAnalyzerExportTool()
        
        # Create state with analysis results
        state = SupervisorTaskState(
            user_guid="test-user-guid",
            scheduled_guid="test-scheduled-guid"
        )
        state.sections = {
            "analysis_results": {
                "query": "test query",
                "result": "test result",
                "data_shape": "10 rows x 3 columns"
            },
            "data_info": {
                "source": "SQL Database",
                "rows": 10,
                "columns": ["col1", "col2", "col3"]
            },
            "export_format": "json",
            "original_query": "SELECT * FROM test"
        }
        
        # Mock user manager
        mock_user = MagicMock()
        with patch('managers.manager_users.ZairaUserManager.get_instance') as mock_user_manager:
            mock_instance = MagicMock()
            mock_instance.find_user = AsyncMock(return_value=mock_user)
            mock_user_manager.return_value = mock_instance
            
            # Mock export file generation
            with patch.object(tool, '_generate_export_file', AsyncMock(return_value="/path/to/export.json")):
                result = await tool._arun(state=state)
                
                result_data = json.loads(result)
                assert result_data["success"] is True
                assert result_data["export_path"] == "/path/to/export.json"
                assert result_data["format"] == "json"
                assert "successfully" in result_data["message"]
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_export_to_json(self):
        """Test JSON export functionality"""
        tool = SQLExcelAnalyzerExportTool()
        
        analysis_results = {
            "query": "test query",
            "result": "Found 100 rows",
            "data_shape": "100 rows x 5 columns"
        }
        
        data_info = {
            "source": "Excel File",
            "rows": 100,
            "columns": ["id", "name", "value", "date", "status"]
        }
        
        # Create temporary directory for export
        import tempfile
        with tempfile.TemporaryDirectory() as tmpdir:
            base_filename = "test_export"
            
            filepath = await tool._export_to_json(
                analysis_results,
                data_info,
                tmpdir,
                base_filename
            )
            
            assert filepath.endswith(".json")
            assert os.path.exists(filepath)
            
            # Verify JSON content
            with open(filepath, 'r') as f:
                exported_data = json.load(f)
                assert exported_data["analysis_results"]["query"] == "test query"
                assert exported_data["data_info"]["source"] == "Excel File"
                assert "export_timestamp" in exported_data
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_export_to_csv(self):
        """Test CSV export functionality"""
        tool = SQLExcelAnalyzerExportTool()
        
        analysis_results = {
            "query": "count items",
            "result": "Total: 50 items",
            "data_shape": "50 rows x 3 columns"
        }
        
        data_info = {
            "source": "SQL Database",
            "rows": 50,
            "columns": ["id", "name", "quantity"]
        }
        
        # Create temporary directory for export
        import tempfile
        with tempfile.TemporaryDirectory() as tmpdir:
            base_filename = "test_csv_export"
            
            filepath = await tool._export_to_csv(
                analysis_results,
                data_info,
                tmpdir,
                base_filename
            )
            
            assert filepath.endswith(".csv")
            assert os.path.exists(filepath)
            
            # Verify CSV content
            df = pd.read_csv(filepath)
            assert df["Query"][0] == "count items"
            assert df["Result"][0] == "Total: 50 items"
            assert df["Source"][0] == "SQL Database"
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_export_to_excel(self):
        """Test Excel export functionality"""
        tool = SQLExcelAnalyzerExportTool()
        
        analysis_results = {
            "query": "analyze sales",
            "result": "Average sales: $1000",
            "data_shape": "200 rows x 10 columns",
            "summary_stats": {
                "sales": {"mean": 1000, "std": 250, "min": 100, "max": 5000}
            }
        }
        
        data_info = {
            "source": "Excel File",
            "rows": 200,
            "columns": ["date", "product", "sales", "quantity"]
        }
        
        # Create temporary directory for export
        import tempfile
        with tempfile.TemporaryDirectory() as tmpdir:
            base_filename = "test_excel_export"
            
            filepath = await tool._export_to_excel(
                analysis_results,
                data_info,
                tmpdir,
                base_filename
            )
            
            assert filepath.endswith(".xlsx")
            assert os.path.exists(filepath)
            
            # Verify Excel content
            excel_data = pd.ExcelFile(filepath)
            assert "Analysis Results" in excel_data.sheet_names
            assert "Data Info" in excel_data.sheet_names
            assert "Summary Statistics" in excel_data.sheet_names
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_generate_export_file_invalid_format(self):
        """Test export with invalid format"""
        tool = SQLExcelAnalyzerExportTool()
        
        # Create temporary directory
        import tempfile
        with tempfile.TemporaryDirectory() as tmpdir:
            result = await tool._generate_export_file(
                {},
                {},
                "invalid_format",
                "test_query"
            )
            
            assert result is None
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_error_handling_in_arun(self):
        """Test error handling in _arun method"""
        tool = SQLExcelAnalyzerExportTool()
        
        state = SupervisorTaskState(
            user_guid="test-user-guid",
            scheduled_guid="test-scheduled-guid"
        )
        state.sections = {
            "analysis_results": {},
            "export_format": "excel"
        }
        
        # Mock export to raise exception
        with patch.object(tool, '_generate_export_file', AsyncMock(side_effect=Exception("Export failed"))):
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                result = await tool._arun(state=state)
                
                result_data = json.loads(result)
                assert "error" in result_data
                assert "Export failed" in result_data["error"]
                mock_exception.assert_called_once()


class TestSQLExcelAnalyzerOutputTask:
    """Unit tests for output task creation"""
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_create_task_out_sql_excel_analyzer(self):
        """Test output task creation function"""
        with patch('managers.manager_supervisors.SupervisorManager.get_instance') as mock_supervisor:
            mock_instance = MagicMock()
            mock_instance.register_task = MagicMock()
            mock_supervisor.return_value = mock_instance
            
            task = await create_task_out_sql_excel_analyzer()
            
            mock_instance.register_task.assert_called_once()
            args = mock_instance.register_task.call_args[0][0]
            assert args.name == "sql_excel_analyzer_output_task"
            assert len(args.tools) == 1
            assert args.tools[0].name == "sql_excel_analyzer_export_tool"
            assert "data export specialist" in args.prompt