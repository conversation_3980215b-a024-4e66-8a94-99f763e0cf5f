from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from pydantic import ValidationError
import pandas as pd
import json
from unittest.mock import AsyncMock, MagicMock, patch
from datetime import datetime

from tasks.processing.task_sql_excel_analyzer import SQLExcelAnalyzerTool, create_task_sql_excel_analyzer
from managers.manager_supervisors import SupervisorTaskState
from managers.manager_logfire import LogFire
from tests.unit.test_logging_capture import with_unit_test_logging


class TestSQLExcelAnalyzerTool:
    """Unit tests for SQLExcelAnalyzerTool"""
    
    @with_unit_test_logging
    def test_tool_initialization(self):
        """Test that the tool initializes correctly"""
        tool = SQLExcelAnalyzerTool()
        assert tool.name == "sql_excel_analyzer_tool"
        assert "natural language queries" in tool.description
        assert "SQL databases and Excel files" in tool.description
    
    @with_unit_test_logging
    def test_run_raises_not_implemented(self):
        """Test that sync _run raises NotImplementedError"""
        tool = SQLExcelAnalyzerTool()
        with pytest.raises(NotImplementedError, match="Use async version"):
            tool._run("query", state=None)
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_arun_with_no_data_source(self):
        """Test _arun when no data source is provided"""
        tool = SQLExcelAnalyzerTool()
        
        # Mock state and user
        state = SupervisorTaskState(
            user_guid="test-user-guid",
            scheduled_guid="test-scheduled-guid"
        )
        
        # Mock user manager and user
        mock_user = MagicMock()
        mock_user.username = "testuser"
        mock_user.my_requests = {
            "test-scheduled-guid": MagicMock()
        }
        
        with patch('managers.manager_users.ZairaUserManager.get_instance') as mock_user_manager:
            mock_instance = MagicMock()
            mock_instance.find_user = AsyncMock(return_value=mock_user)
            mock_user_manager.return_value = mock_instance
            
            # Mock the request data source method to return None
            with patch.object(tool, '_request_data_source', AsyncMock(return_value=(None, {}))):
                result = await tool._arun("analyze data", state=state)
                
                result_data = json.loads(result)
                assert result_data["error"] == "No data loaded"
                assert result_data["message"] == "Could not load data for analysis"
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_load_sql_data_success(self):
        """Test successful SQL data loading"""
        tool = SQLExcelAnalyzerTool()
        
        # Mock SQL results
        mock_results = [
            {"id": 1, "name": "Product A", "price": 100},
            {"id": 2, "name": "Product B", "price": 200}
        ]
        
        # Mock PostgreSQL connection
        with patch('managers.manager_postgreSQL.PostgreSQLManager.get_instance') as mock_pg:
            mock_conn = AsyncMock()
            mock_conn.fetch = AsyncMock(return_value=mock_results)
            mock_conn.__aenter__ = AsyncMock(return_value=mock_conn)
            mock_conn.__aexit__ = AsyncMock(return_value=None)
            
            mock_db = MagicMock()
            mock_db.get_connection = MagicMock(return_value=mock_conn)
            mock_pg.return_value = mock_db
            
            state = SupervisorTaskState(user_guid="test-user", scheduled_guid="test-guid")
            df, data_info = await tool._load_sql_data(
                "SELECT * FROM products", 
                "vectordb",
                MagicMock(),
                state
            )
            
            assert isinstance(df, pd.DataFrame)
            assert len(df) == 2
            assert list(df.columns) == ["id", "name", "price"]
            assert data_info["source"] == "SQL Database"
            assert data_info["rows"] == 2
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_load_file_data_csv(self):
        """Test loading CSV file data"""
        tool = SQLExcelAnalyzerTool()
        
        # Create a temporary CSV file
        import tempfile
        with tempfile.NamedTemporaryFile(mode='w', suffix='.csv', delete=False) as tmp:
            tmp.write("id,name,value\n1,Item1,100\n2,Item2,200")
            tmp_path = tmp.name
        
        try:
            state = SupervisorTaskState(user_guid="test-user", scheduled_guid="test-guid")
            df, data_info = await tool._load_file_data(tmp_path, MagicMock(), state)
            
            assert isinstance(df, pd.DataFrame)
            assert len(df) == 2
            assert list(df.columns) == ["id", "name", "value"]
            assert data_info["source"] == "CSV File"
            assert data_info["file_path"] == tmp_path
        finally:
            # Clean up temp file
            import os
            os.unlink(tmp_path)
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_format_results(self):
        """Test formatting of analysis results"""
        tool = SQLExcelAnalyzerTool()
        
        analysis_result = {
            "query": "count rows",
            "result": "There are 100 rows in the dataset",
            "data_shape": "100 rows x 5 columns"
        }
        
        data_info = {
            "source": "SQL Database",
            "rows": 100,
            "columns": ["id", "name", "value", "date", "status"]
        }
        
        formatted = await tool._format_results(analysis_result, data_info, "count rows")
        
        assert "Data Analysis Results" in formatted
        assert "Query: count rows" in formatted
        assert "Source: SQL Database" in formatted
        assert "Rows: 100" in formatted
        assert "There are 100 rows in the dataset" in formatted
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_create_pandas_agent(self):
        """Test pandas agent creation"""
        tool = SQLExcelAnalyzerTool()
        
        # Create sample dataframe
        df = pd.DataFrame({
            "id": [1, 2, 3],
            "value": [10, 20, 30]
        })
        
        with patch('langchain_experimental.agents.agent_toolkits.create_pandas_dataframe_agent') as mock_create:
            mock_agent = MagicMock()
            mock_create.return_value = mock_agent
            
            agent = await tool._create_pandas_agent(df)
            
            assert agent == mock_agent
            mock_create.assert_called_once()
            args = mock_create.call_args[0]
            assert isinstance(args[1], pd.DataFrame)
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_analyze_data_success(self):
        """Test successful data analysis"""
        tool = SQLExcelAnalyzerTool()
        
        # Mock agent and dataframe
        mock_agent = MagicMock()
        mock_agent.ainvoke = AsyncMock(return_value={
            "output": "The average value is 20"
        })
        
        df = pd.DataFrame({
            "id": [1, 2, 3],
            "value": [10, 20, 30]
        })
        
        result = await tool._analyze_data(mock_agent, "what is the average value?", df)
        
        assert result["query"] == "what is the average value?"
        assert result["result"] == "The average value is 20"
        assert "3 rows x 2 columns" in result["data_shape"]
        assert result["columns"] == ["id", "value"]
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_error_handling_in_arun(self):
        """Test error handling in _arun method"""
        tool = SQLExcelAnalyzerTool()
        
        state = SupervisorTaskState(
            user_guid="test-user-guid",
            scheduled_guid="test-scheduled-guid"
        )
        
        # Mock user manager to raise an exception
        with patch('managers.manager_users.ZairaUserManager.get_instance') as mock_user_manager:
            mock_instance = MagicMock()
            mock_instance.find_user = AsyncMock(side_effect=Exception("User lookup failed"))
            mock_user_manager.return_value = mock_instance
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                result = await tool._arun("test query", state=state)
                
                result_data = json.loads(result)
                assert "error" in result_data
                assert "Analysis failed" in result_data["error"]
                mock_exception.assert_called_once()


class TestSQLExcelAnalyzerTask:
    """Unit tests for task creation"""
    
    @with_unit_test_logging
    @pytest.mark.asyncio
    async def test_create_task_sql_excel_analyzer(self):
        """Test task creation function"""
        with patch('managers.manager_supervisors.SupervisorManager.get_instance') as mock_supervisor:
            mock_instance = MagicMock()
            mock_instance.register_task = MagicMock()
            mock_supervisor.return_value = mock_instance
            
            task = await create_task_sql_excel_analyzer()
            
            mock_instance.register_task.assert_called_once()
            args = mock_instance.register_task.call_args[0][0]
            assert args.name == "sql_excel_analyzer_task"
            assert len(args.tools) == 1
            assert args.tools[0].name == "sql_excel_analyzer_tool"
            assert args.prompt_id == "SQL_Excel_Analyzer_Prompt"