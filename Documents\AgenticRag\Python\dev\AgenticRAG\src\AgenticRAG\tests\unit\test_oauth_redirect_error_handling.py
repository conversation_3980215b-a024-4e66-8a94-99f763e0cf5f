from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from aiohttp import web
from aiohttp.test_utils import AioHTTPTestCase
from unittest.mock import Mock, AsyncMock, patch
from endpoints.oauth._verifier_ import OAuth2Verifier, OAuth2App
from tests.unit.test_logging_capture import with_unit_test_logging
import json

class TestOAuthRedirectErrorHandling(AioHTTPTestCase):
    """Test OAuth redirect endpoint error handling improvements"""
    
    async def get_application(self):
        app = web.Application()
        return app
    
    @with_unit_test_logging
    async def test_oauth_redirect_post_empty_body(self):
        """Test handling of empty request body"""
        verifier = OAuth2Verifier.get_instance()
        verifier.apps["debug"] = OAuth2App("debug")
        
        # Create mock request with empty body
        request = Mock(spec=web.Request)
        request.rel_url.parts = ['', 'debug']
        request.content_type = 'application/json'
        request.content_length = 0  # Add content_length attribute
        request.json = AsyncMock(return_value={})
        
        # Call endpoint - logging will be captured automatically
        response = await verifier.oauth_redirect_post(request)
        
        # Check response (error logging captured in test log file)
        assert response.status == 400
        body = json.loads(response.text)
        assert body["error"] == "Empty request body"
        assert body["success"] == False
        
        # Test passes if proper error response is returned
        # Actual logging can be verified in tests/unit/logs/TestOAuthRedirectErrorHandling/test_oauth_redirect_post_empty_body.log
    
    @with_unit_test_logging
    async def test_oauth_redirect_post_invalid_json(self):
        """Test handling of invalid JSON"""
        verifier = OAuth2Verifier.get_instance()
        verifier.apps["debug"] = OAuth2App("debug")
        
        # Create mock request with invalid JSON
        request = Mock(spec=web.Request)
        request.rel_url.parts = ['', 'debug']
        request.content_type = 'application/json'
        request.content_length = 100  # Non-zero to indicate there's content
        request.json = AsyncMock(side_effect=json.JSONDecodeError("test", "doc", 0))
        
        # Call endpoint - logging will be captured automatically
        response = await verifier.oauth_redirect_post(request)
        
        # Check response (error logging captured in test log file)
        assert response.status == 400
        body = json.loads(response.text)
        assert "Invalid request format" in body["error"]
        assert body["success"] == False
        
        # Test passes if proper error response is returned
        # Actual logging can be verified in tests/unit/logs/TestOAuthRedirectErrorHandling/test_oauth_redirect_post_invalid_json.log
    
    @with_unit_test_logging
    async def test_oauth_redirect_post_missing_password(self):
        """Test handling of missing authentication password"""
        verifier = OAuth2Verifier.get_instance()
        verifier.apps["debug"] = OAuth2App("debug")
        
        # Create mock request without password
        request = Mock(spec=web.Request)
        request.rel_url.parts = ['', 'debug']
        request.content_type = 'application/json'
        request.content_length = 100
        request.json = AsyncMock(return_value={"someField": "value"})
        
        # Call endpoint - logging will be captured automatically
        response = await verifier.oauth_redirect_post(request)
        
        # Check response (error logging captured in test log file)
        assert response.status == 401
        body = json.loads(response.text)
        assert body["error"] == "Authentication password required"
        assert body["success"] == False
        
        # Test passes if proper error response is returned
        # Actual logging can be verified in tests/unit/logs/TestOAuthRedirectErrorHandling/test_oauth_redirect_post_missing_password.log
    
    @with_unit_test_logging
    async def test_oauth_redirect_post_invalid_password(self):
        """Test handling of invalid authentication password"""
        verifier = OAuth2Verifier.get_instance()
        verifier.apps["debug"] = OAuth2App("debug")
        
        # Create mock request with wrong password
        request = Mock(spec=web.Request)
        request.rel_url.parts = ['', 'debug']
        request.content_type = 'application/json'
        request.content_length = 100
        request.json = AsyncMock(return_value={"userpass": "wrongpassword"})
        
        # Call endpoint - logging will be captured automatically
        with patch('endpoints.oauth._verifier_.Globals.is_docker', return_value=False):
            response = await verifier.oauth_redirect_post(request)
            
            # Check response (error logging captured in test log file)
            assert response.status == 401
            body = json.loads(response.text)
            assert body["error"] == "Invalid authentication password"
            assert body["success"] == False
            
            # Test passes if proper error response is returned
            # Actual logging can be verified in tests/unit/logs/TestOAuthRedirectErrorHandling/test_oauth_redirect_post_invalid_password.log
    
    @with_unit_test_logging
    async def test_oauth_redirect_post_missing_required_field(self):
        """Test handling of missing required field"""
        verifier = OAuth2Verifier.get_instance()
        
        # Create app with required string field
        app = OAuth2App("debug")
        app.create_input("debug", ["str:Required Field"])
        verifier.apps["debug"] = app
        
        # Create valid MD5 hash for testing
        from hashlib import md5
        username = "proxyhttpaio"
        md5pass = md5(("!askzaira#" + username + "-askzaira=").encode('utf-8')).hexdigest()
        
        # Create mock request missing required field
        request = Mock(spec=web.Request)
        request.rel_url.parts = ['', 'debug']
        request.content_type = 'application/json'
        request.content_length = 100
        request.json = AsyncMock(return_value={"userpass": md5pass})
        
        # Call endpoint - logging will be captured automatically
        with patch('endpoints.oauth._verifier_.Globals.is_docker', return_value=False):
            response = await verifier.oauth_redirect_post(request)
            
            # Check response (error logging captured in test log file)
            assert response.status == 400
            body = json.loads(response.text)
            assert "Required field missing" in body["error"]
            assert body["success"] == False
            
            # Test passes if proper error response is returned
            # Actual logging can be verified in tests/unit/logs/TestOAuthRedirectErrorHandling/test_oauth_redirect_post_missing_required_field.log
    
    @with_unit_test_logging
    async def test_oauth_redirect_post_success_logging(self):
        """Test that successful requests are properly logged"""
        verifier = OAuth2Verifier.get_instance()
        
        # Create app with no required fields
        app = OAuth2App("debug")
        app.create_input("debug", ["str:Optional Field (Optional)"])
        verifier.apps["debug"] = app
        
        # Create valid MD5 hash for testing
        from hashlib import md5
        username = "proxyhttpaio"
        md5pass = md5(("!askzaira#" + username + "-askzaira=").encode('utf-8')).hexdigest()
        
        # Create mock request with valid data
        request = Mock(spec=web.Request)
        request.rel_url.parts = ['', 'debug']
        request.content_type = 'application/json'
        request.content_length = 100
        request.json = AsyncMock(return_value={
            "userpass": md5pass,
            "OptionalField(Optional)": "test_value"
        })
        
        # Call endpoint - logging will be captured automatically
        with patch('endpoints.oauth._verifier_.Globals.is_docker', return_value=False):
            with patch.object(verifier, 'save_tokens', return_value=True):
                with patch.object(verifier, 'get_token', return_value=None):
                    response = await verifier.oauth_redirect_post(request)
                    
                    # Check response (debug and success logging captured in test log file)
                    assert response.status == 200
                    body = json.loads(response.text)
                    assert body["success"] == True
                    
                    # Test passes if proper success response is returned
                    # Actual logging (DEBUG, INIT calls) can be verified in tests/unit/logs/TestOAuthRedirectErrorHandling/test_oauth_redirect_post_success_logging.log