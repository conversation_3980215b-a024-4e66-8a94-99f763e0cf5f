from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, PropertyMock
from endpoints.discord_endpoint import MyDiscordBot, MyDiscordClient
from endpoints.mybot_generic import ChannelType
from discord import Message, Member, Guild, File
from uuid import uuid4
import threading

class TestMyDiscordClient:
    """Test suite for MyDiscordClient"""
    
    def setup_method(self):
        """Setup test fixtures"""
        from discord import Intents
        intents = Intents.default()
        intents.message_content = True
        self.client = MyDiscordClient(intents=intents)
        
    @pytest.mark.asyncio
    async def test_on_ready(self):
        """Test on_ready event handler"""
        with patch('endpoints.discord_endpoint.MyDiscordBot.on_ready', new_callable=AsyncMock) as mock_on_ready:
            await self.client.on_ready()
            mock_on_ready.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_on_message(self):
        """Test on_message event handler"""
        mock_message = MagicMock(spec=Message)
        mock_message.author.bot = False
        mock_message.author.id = 12345
        mock_message.guild = MagicMock()
        mock_message.guild.id = 1332344925655924796
        mock_message.channel = MagicMock()
        mock_message.content = "test message"
        mock_message.attachments = []
        
        with patch('endpoints.discord_endpoint.MyDiscordBot.on_message') as mock_on_message:
            await self.client.on_message(mock_message)
            mock_on_message.assert_called_once_with(mock_message)
            
    @pytest.mark.asyncio
    async def test_on_message_bot_user(self):
        """Test on_message ignores bot users"""
        mock_message = MagicMock(spec=Message)
        mock_message.author.bot = True
        
        with patch('endpoints.discord_endpoint.MyDiscordBot.on_message', new_callable=AsyncMock) as mock_on_message:
            await self.client.on_message(mock_message)
            mock_on_message.assert_called_once_with(mock_message)
            
    @pytest.mark.asyncio
    async def test_on_member_join(self):
        """Test on_member_join event handler"""
        mock_member = MagicMock(spec=Member)
        mock_member.display_name = "TestUser"
        mock_member.guild = MagicMock()
        mock_member.guild.id = 1332344925655924796
        
        with patch('endpoints.discord_endpoint.MyDiscordBot.on_member_join') as mock_on_join:
            await self.client.on_member_join(mock_member)
            mock_on_join.assert_called_once_with(mock_member)
            
    @pytest.mark.asyncio
    async def test_on_member_leave(self):
        """Test on_member_leave event handler"""
        mock_member = MagicMock(spec=Member)
        mock_member.display_name = "TestUser"
        mock_member.guild = MagicMock()
        mock_member.guild.id = 1332344925655924796
        
        with patch('endpoints.discord_endpoint.MyDiscordBot.on_member_leave', new_callable=AsyncMock) as mock_on_leave:
            await self.client.on_member_leave(mock_member)
            mock_on_leave.assert_called_once_with(mock_member)
            
    @pytest.mark.asyncio
    async def test_on_guild_join(self):
        """Test on_guild_join event handler"""
        mock_guild = MagicMock(spec=Guild)
        mock_guild.name = "TestGuild"
        mock_guild.id = 1332344925655924796
        
        with patch('endpoints.discord_endpoint.MyDiscordBot.on_guild_join', new_callable=AsyncMock) as mock_on_join:
            await self.client.on_guild_join(mock_guild)
            mock_on_join.assert_called_once_with(mock_guild)
            
class TestMyDiscordBot:
    """Test suite for MyDiscordBot"""
    
    def setup_method(self):
        """Setup test fixtures"""
        # Reset singleton instance
        MyDiscordBot._instance = None
        self.bot = MyDiscordBot()
        
    def test_singleton_pattern(self):
        """Test that MyDiscordBot follows singleton pattern"""
        bot1 = MyDiscordBot()
        bot2 = MyDiscordBot()
        bot3 = MyDiscordBot.get_instance()
        
        assert bot1 is bot2
        assert bot2 is bot3
        assert bot1._instance is not None
        
    def test_initialization(self):
        """Test MyDiscordBot initialization"""
        bot = MyDiscordBot()
        
        assert hasattr(bot, '_initialized')
        assert bot._initialized == False
        assert bot.intents is not None
        assert bot.bot is not None
        assert bot.bot_thread is None
        assert bot.bot_generic is None
        
    @pytest.mark.asyncio
    async def test_setup_debug_mode(self):
        """Test setup method in debug mode"""
        with patch('endpoints.discord_endpoint.Globals.is_debug', return_value=True), \
             patch('endpoints.oauth._verifier_.OAuth2Verifier.get_token', return_value='123456789'), \
             patch('endpoints.discord_endpoint.MyDiscordBot.run_discord_bot_internal', new_callable=AsyncMock), \
             patch('endpoints.mybot_generic.MyBot_Generic'):
            await MyDiscordBot.setup()
            
            # Should not create client in debug mode
            instance = MyDiscordBot.get_instance()
            assert instance.bot is not None
            
    @pytest.mark.asyncio
    async def test_setup_production_mode(self):
        """Test setup method in production mode"""
        with patch('endpoints.discord_endpoint.Globals.is_debug', return_value=False), \
             patch('endpoints.oauth._verifier_.OAuth2Verifier.get_token', return_value='123456789'), \
             patch('endpoints.discord_endpoint.MyDiscordBot.run_discord_bot_internal', new_callable=AsyncMock), \
             patch('endpoints.mybot_generic.MyBot_Generic') as mock_bot_generic, \
             patch('endpoints.discord_endpoint.Globals.is_docker', return_value=False):
            
            mock_bot_generic.return_value = MagicMock()
            
            await MyDiscordBot.setup()
            
            instance = MyDiscordBot.get_instance()
            assert instance.bot is not None
            assert instance.bot_generic is not None
            
    @pytest.mark.asyncio
    async def test_setup_no_token(self):
        """Test setup method with no Discord token"""
        with patch('endpoints.discord_endpoint.Globals.is_debug', return_value=False), \
             patch('endpoints.oauth._verifier_.OAuth2Verifier.get_token', return_value=''), \
             patch('endpoints.discord_endpoint.MyDiscordBot.run_discord_bot_internal', new_callable=AsyncMock), \
             patch('endpoints.mybot_generic.MyBot_Generic'), \
             patch('endpoints.discord_endpoint.Globals.is_docker', return_value=False):
            
            await MyDiscordBot.setup()
            
            instance = MyDiscordBot.get_instance()
            assert instance.bot is not None
            
    @pytest.mark.asyncio
    async def test_on_ready(self):
        """Test on_ready method"""
        with patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            mock_bot_generic.on_ready = AsyncMock()
            
            await self.bot.on_ready()
            
            mock_bot_generic.on_ready.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_on_member_join(self):
        """Test on_member_join method"""
        mock_member = MagicMock(spec=Member)
        mock_member.display_name = "TestUser"
        mock_member.guild = MagicMock()
        mock_member.guild.id = 1332344925655924796
        
        # Set up the bot with correct guild_id
        self.bot.discord_guild_id = 1332344925655924796
        self.bot.members = []
        
        await self.bot.on_member_join(mock_member)
        
        # Check that member was added to members list
        assert mock_member in self.bot.members
            
    @pytest.mark.asyncio
    async def test_on_member_join_wrong_guild(self):
        """Test on_member_join with wrong guild ID"""
        mock_member = MagicMock(spec=Member)
        mock_member.display_name = "TestUser"
        mock_member.guild = MagicMock()
        mock_member.guild.id = 999999  # Wrong guild ID
        
        with patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            mock_bot_generic.on_member_join = AsyncMock()
            
            await self.bot.on_member_join(mock_member)
            
            mock_bot_generic.on_member_join.assert_not_called()
            
    @pytest.mark.asyncio
    async def test_on_message_processing(self):
        """Test on_message processing logic"""
        mock_message = MagicMock(spec=Message)
        mock_message.author.id = 12345
        mock_message.guild = MagicMock()
        mock_message.guild.id = 1332344925655924796
        mock_message.guild.name = "TestGuild"
        mock_message.channel = MagicMock()
        mock_message.channel.__str__ = MagicMock(return_value="zaira-chat")
        mock_message.content = "!test message"
        mock_message.attachments = []
        mock_message.reference = None
        
        mock_user = MagicMock()
        mock_user.username = "TestUser"
        
        # Set up the bot with correct guild_id and add author to members
        self.bot.discord_guild_id = 1332344925655924796
        self.bot.members = [mock_message.author]
        
        with patch('managers.manager_users.ZairaUserManager.get_user', return_value=mock_user), \
             patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            
            mock_bot_generic.on_message = AsyncMock()
            
            await self.bot.on_message(mock_message)
            
            mock_bot_generic.on_message.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_on_message_with_attachments(self):
        """Test on_message with file attachments"""
        mock_message = MagicMock(spec=Message)
        mock_message.author.id = 12345
        mock_message.guild = MagicMock()
        mock_message.guild.id = 1332344925655924796
        mock_message.guild.name = "TestGuild"
        mock_message.channel = MagicMock()
        mock_message.channel.__str__ = MagicMock(return_value="zaira-chat")
        mock_message.content = "!test message"
        
        # Mock attachment
        mock_attachment = MagicMock()
        mock_attachment.filename = "test.pdf"
        mock_attachment.url = "https://example.com/test.pdf"
        mock_attachment.read = AsyncMock(return_value=b"test content")
        mock_message.attachments = [mock_attachment]
        mock_message.reference = None
        
        mock_user = MagicMock()
        mock_user.username = "TestUser"
        
        # Set up the bot with correct guild_id and add author to members
        self.bot.discord_guild_id = 1332344925655924796
        self.bot.members = [mock_message.author]
        
        with patch('managers.manager_users.ZairaUserManager.get_user', return_value=mock_user), \
             patch.object(self.bot, 'bot_generic') as mock_bot_generic, \
             patch('builtins.open', create=True) as mock_open:
            
            mock_bot_generic.on_message = AsyncMock()
            
            await self.bot.on_message(mock_message)
            
            mock_bot_generic.on_message.assert_called_once()
            args, kwargs = mock_bot_generic.on_message.call_args
            assert len(args[3]) == 1  # attachments list
            assert args[3][0] == "test.pdf"
            
    @pytest.mark.asyncio
    async def test_on_message_with_reply(self):
        """Test on_message with reply context"""
        mock_message = MagicMock(spec=Message)
        mock_message.author.id = 12345
        mock_message.guild = MagicMock()
        mock_message.guild.id = 1332344925655924796
        mock_message.guild.name = "TestGuild"
        mock_message.channel = MagicMock()
        mock_message.channel.__str__ = MagicMock(return_value="zaira-chat")
        mock_message.content = "!test reply"
        mock_message.attachments = []
        
        # Mock reply reference
        mock_reference = MagicMock()
        mock_reference.message_id = 987654321
        mock_message.reference = mock_reference
        
        # Mock the replied message
        mock_replied_message = MagicMock()
        mock_replied_message.content = "Original message"
        mock_replied_message.author = MagicMock()
        mock_replied_message.author.__str__ = MagicMock(return_value="OriginalUser")
        mock_message.channel.fetch_message = AsyncMock(return_value=mock_replied_message)
        
        mock_user = MagicMock()
        mock_user.username = "TestUser"
        
        # Set up the bot with correct guild_id and add author to members
        self.bot.discord_guild_id = 1332344925655924796
        self.bot.members = [mock_message.author]
        
        with patch('managers.manager_users.ZairaUserManager.get_user', return_value=mock_user), \
             patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            
            mock_bot_generic.on_message = AsyncMock()
            
            await self.bot.on_message(mock_message)
            
            mock_bot_generic.on_message.assert_called_once()
            args, kwargs = mock_bot_generic.on_message.call_args
            reply_context = args[5]  # reply_context parameter
            assert reply_context.is_reply == True
            assert reply_context.replied_message_id == "987654321"
            
    @pytest.mark.asyncio
    async def test_on_message_wrong_guild(self):
        """Test on_message with wrong guild ID"""
        mock_message = MagicMock(spec=Message)
        mock_message.author.id = 12345
        mock_message.guild = MagicMock()
        mock_message.guild.id = 999999  # Wrong guild ID
        
        with patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            mock_bot_generic.on_message = AsyncMock()
            
            await self.bot.on_message(mock_message)
            
            mock_bot_generic.on_message.assert_not_called()
            
    @pytest.mark.asyncio
    async def test_on_message_no_exclamation(self):
        """Test on_message without exclamation mark"""
        mock_message = MagicMock(spec=Message)
        mock_message.author.id = 12345
        mock_message.guild = MagicMock()
        mock_message.guild.id = 1332344925655924796
        mock_message.channel = MagicMock()
        mock_message.content = "test message"  # No exclamation mark
        mock_message.attachments = []
        
        with patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            mock_bot_generic.on_message = AsyncMock()
            
            await self.bot.on_message(mock_message)
            
            mock_bot_generic.on_message.assert_not_called()
            
    @pytest.mark.asyncio
    async def test_send_discord_broadcast(self):
        """Test send_discord_broadcast method"""
        mock_guild = MagicMock()
        mock_guild.id = 1332344925655924796
        mock_guild.name = "TestGuild"
        mock_channel = MagicMock()
        mock_channel.name = "zaira-chat"
        mock_channel.send = AsyncMock()
        mock_permissions = MagicMock()
        mock_permissions.send_messages = True
        mock_channel.permissions_for = MagicMock(return_value=mock_permissions)
        mock_guild.text_channels = [mock_channel]
        mock_guild.me = MagicMock()
        
        # Set up the bot with correct guild_id
        self.bot.discord_guild_id = 1332344925655924796
        
        with patch.object(type(MyDiscordBot.bot), 'guilds', new_callable=PropertyMock) as mock_guilds:
            mock_guilds.return_value = [mock_guild]
            await MyDiscordBot.send_discord_broadcast("Test broadcast")
            
            mock_channel.send.assert_called_once_with("Test broadcast")
            
    @pytest.mark.asyncio
    async def test_send_discord_broadcast_no_client(self):
        """Test send_discord_broadcast with no client"""
        with patch.object(self.bot.bot, 'get_channel', return_value=None):
            # Should not raise an exception
            await MyDiscordBot.send_discord_broadcast("Test broadcast")
            
    @pytest.mark.asyncio
    async def test_send_a_discord_message(self):
        """Test send_a_discord_message method"""
        mock_message = MagicMock(spec=Message)
        mock_message.reply = AsyncMock()
        
        await MyDiscordBot.send_a_discord_message(mock_message, "Test response")
        
        mock_message.reply.assert_called_once_with("Test response")
        
    @pytest.mark.asyncio
    async def test_run_discord_bot_internal_no_client(self):
        """Test run_discord_bot_internal with no client"""
        with patch.object(self.bot.bot, 'start', new_callable=AsyncMock) as mock_start:
            # Should not raise an exception
            await self.bot.run_discord_bot_internal()
            mock_start.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_run_discord_bot_internal_with_client(self):
        """Test run_discord_bot_internal with client"""
        with patch.object(self.bot.bot, 'start', new_callable=AsyncMock) as mock_start:
            await self.bot.run_discord_bot_internal()
            mock_start.assert_called_once()
            
class TestMyDiscordBotThreading:
    """Test threading aspects of MyDiscordBot"""
    
    def setup_method(self):
        """Setup test fixtures"""
        MyDiscordBot._instance = None
        self.bot = MyDiscordBot()
        
    def test_threading_setup(self):
        """Test that threading is properly configured"""
        with patch('endpoints.discord_endpoint.Globals.is_debug', return_value=False), \
             patch('endpoints.oauth._verifier_.OAuth2Verifier.get_token', return_value='test_token'), \
             patch('endpoints.discord_endpoint.MyDiscordClient') as mock_client_class, \
             patch('threading.Thread') as mock_thread:
            
            mock_client = MagicMock()
            mock_client_class.return_value = mock_client
            mock_thread_instance = MagicMock()
            mock_thread.return_value = mock_thread_instance
            
            # This would be called in a real setup
            # We test the threading logic separately
            assert threading.current_thread() is not None
            
    @pytest.mark.asyncio
    async def test_exception_handling(self):
        """Test exception handling in various methods"""
        mock_message = MagicMock(spec=Message)
        mock_message.author.id = 12345
        mock_message.guild = MagicMock()
        mock_message.guild.id = 1332344925655924796
        mock_message.guild.name = "TestGuild"
        mock_message.channel = MagicMock()
        mock_message.channel.__str__ = MagicMock(return_value="zaira-chat")
        mock_message.content = "!test message"
        mock_message.attachments = []
        mock_message.reference = None
        
        # Set up the bot with correct guild_id and add author to members
        self.bot.discord_guild_id = 1332344925655924796
        self.bot.members = [mock_message.author]
        
        with patch('managers.manager_users.ZairaUserManager.get_user', side_effect=Exception("Test error")):
            
            # Test that exception is raised and not handled
            with pytest.raises(Exception, match="Test error"):
                await self.bot.on_message(mock_message)
            
class TestMyDiscordBotEdgeCases:
    """Test edge cases for MyDiscordBot"""
    
    def setup_method(self):
        """Setup test fixtures"""
        MyDiscordBot._instance = None
        self.bot = MyDiscordBot()
        
    @pytest.mark.asyncio
    async def test_user_creation_on_message(self):
        """Test user creation when user doesn't exist"""
        mock_message = MagicMock(spec=Message)
        mock_message.author.id = 12345
        mock_message.author.display_name = "NewUser"
        mock_message.author.__str__ = MagicMock(return_value="NewUser")
        mock_message.guild = MagicMock()
        mock_message.guild.id = 1332344925655924796
        mock_message.guild.name = "TestGuild"
        mock_message.channel = MagicMock()
        mock_message.channel.__str__ = MagicMock(return_value="zaira-chat")
        mock_message.content = "!test message"
        mock_message.attachments = []
        mock_message.reference = None
        
        mock_user = MagicMock()
        mock_user.username = "NewUser"
        
        # Set up the bot with correct guild_id and add author to members
        self.bot.discord_guild_id = 1332344925655924796
        self.bot.members = [mock_message.author]
        
        with patch('managers.manager_users.ZairaUserManager.get_user', return_value=None), \
             patch('managers.manager_users.ZairaUserManager.add_user', return_value=mock_user), \
             patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            
            mock_bot_generic.on_message = AsyncMock()
            
            await self.bot.on_message(mock_message)
            
            mock_bot_generic.on_message.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_private_message_handling(self):
        """Test handling of private messages"""
        mock_message = MagicMock(spec=Message)
        mock_message.author.id = 12345
        mock_message.author.__str__ = MagicMock(return_value="TestUser")
        mock_message.guild = None  # Private message
        mock_message.channel = MagicMock()
        mock_message.channel.__str__ = MagicMock(return_value="Direct Message with Unknown User")
        mock_message.content = "!test message"
        mock_message.attachments = []
        mock_message.reference = None
        
        mock_user = MagicMock()
        mock_user.username = "TestUser"
        
        # Set up the bot with correct guild_id and add author to members
        self.bot.discord_guild_id = 1332344925655924796
        self.bot.members = [mock_message.author]
        
        with patch('managers.manager_users.ZairaUserManager.get_user', return_value=mock_user), \
             patch.object(self.bot, 'bot_generic') as mock_bot_generic:
            
            mock_bot_generic.on_message = AsyncMock()
            
            await self.bot.on_message(mock_message)
            
            mock_bot_generic.on_message.assert_called_once()
            args, kwargs = mock_bot_generic.on_message.call_args
            assert args[0] == ChannelType.PRIVATE  # channel_type
