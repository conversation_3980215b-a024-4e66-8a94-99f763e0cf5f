from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from uuid import uuid4
import asyncio
from datetime import datetime, timezone, timedelta

from managers.scheduled_requests.resources.quota_manager import (
    UserQuotaManager, SystemQuotaManager, QuotaUsage
)
from managers.scheduled_requests.utils.config import UserQuotaConfig
from managers.scheduled_requests.utils.exceptions import UserQuotaExceededError, ResourceExhaustionError
from managers.scheduled_requests.utils.helpers import Thread<PERSON>af<PERSON><PERSON>ou<PERSON>, MemoryTracker
from userprofiles.ZairaUser import PERMISSION_LEVELS

class TestQuotaUsage:
    """Test QuotaUsage dataclass"""
    
    def test_quota_usage_initialization(self):
        """Test QuotaUsage initialization with defaults"""
        usage = QuotaUsage()
        
        assert usage.concurrent_tasks == 0
        assert usage.daily_tasks == 0
        assert usage.memory_usage_mb == 0.0
        assert usage.recurring_tasks == 0
        assert usage.total_tasks_created == 0
        assert usage.last_reset_date == ""
        assert usage.violations == 0
    
    def test_quota_usage_with_values(self):
        """Test QuotaUsage initialization with custom values"""
        usage = QuotaUsage(
            concurrent_tasks=5,
            daily_tasks=25,
            memory_usage_mb=150.5,
            recurring_tasks=3,
            total_tasks_created=100,
            last_reset_date="2023-01-01",
            violations=2
        )
        
        assert usage.concurrent_tasks == 5
        assert usage.daily_tasks == 25
        assert usage.memory_usage_mb == 150.5
        assert usage.recurring_tasks == 3
        assert usage.total_tasks_created == 100
        assert usage.last_reset_date == "2023-01-01"
        assert usage.violations == 2

class TestUserQuotaManager:
    """Comprehensive test class for UserQuotaManager"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.test_quota_config = UserQuotaConfig(
            max_concurrent_tasks=5,
            daily_task_limit=50,
            memory_limit_mb=100,
            thread_pool_size=3,
            max_task_duration_hours=24,
            max_recurring_tasks=10
        )
        
    def teardown_method(self):
        """Clean up after each test"""
        pass
    
    def test_user_quota_manager_initialization(self):
        """Test UserQuotaManager initialization"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        assert manager.user_guid == self.test_user_guid
        assert manager.quota_config == self.test_quota_config
        assert isinstance(manager._current_usage, QuotaUsage)
        assert isinstance(manager._concurrent_tasks, ThreadSafeCounter)
        assert isinstance(manager._daily_tasks, ThreadSafeCounter)
        assert isinstance(manager._recurring_tasks, ThreadSafeCounter)
        assert isinstance(manager._total_tasks, ThreadSafeCounter)
        assert isinstance(manager._violations, ThreadSafeCounter)
        assert isinstance(manager._memory_tracker, MemoryTracker)
        assert isinstance(manager._usage_history, list)
        assert isinstance(manager._peak_usage, QuotaUsage)
    
    @pytest.mark.asyncio
    async def test_check_quota_availability_success(self):
        """Test successful quota availability check"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        available, reason = await manager.check_quota_availability("create_task", 10.0)
        
        assert available is True
        assert reason is None
    
    @pytest.mark.asyncio
    async def test_check_quota_availability_concurrent_limit_exceeded(self):
        """Test quota availability check when concurrent limit is exceeded"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Set concurrent tasks to limit
        manager._concurrent_tasks._value = self.test_quota_config.max_concurrent_tasks
        
        available, reason = await manager.check_quota_availability("create_task", 10.0)
        
        assert available is False
        assert "Concurrent tasks limit exceeded" in reason
    
    @pytest.mark.asyncio
    async def test_check_quota_availability_daily_limit_exceeded(self):
        """Test quota availability check when daily limit is exceeded"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Set daily tasks to limit
        manager._daily_tasks._value = self.test_quota_config.daily_task_limit
        
        available, reason = await manager.check_quota_availability("create_task", 10.0)
        
        assert available is False
        assert "Daily tasks limit exceeded" in reason
    
    @pytest.mark.asyncio
    async def test_check_quota_availability_memory_limit_exceeded(self):
        """Test quota availability check when memory limit would be exceeded"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Set current memory usage to near limit
        manager._memory_tracker.track_user_memory(self.test_user_guid, 95.0)
        
        available, reason = await manager.check_quota_availability("create_task", 10.0)
        
        assert available is False
        assert "Memory limit would be exceeded" in reason
    
    @pytest.mark.asyncio
    async def test_check_quota_availability_recurring_limit_exceeded(self):
        """Test quota availability check when recurring limit is exceeded"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Set recurring tasks to limit
        manager._recurring_tasks._value = self.test_quota_config.max_recurring_tasks
        
        available, reason = await manager.check_quota_availability("create_recurring_task", 10.0)
        
        assert available is False
        assert "Recurring tasks limit exceeded" in reason
    
    @pytest.mark.asyncio
    async def test_reserve_quota_success(self):
        """Test successful quota reservation"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        success = await manager.reserve_quota("create_task", 10.0)
        
        assert success is True
        assert manager._concurrent_tasks.get_value() == 1
        assert manager._daily_tasks.get_value() == 1
        assert manager._total_tasks.get_value() == 1
        assert manager._memory_tracker.get_user_memory(self.test_user_guid) == 10.0
    
    @pytest.mark.asyncio
    async def test_reserve_quota_recurring_task(self):
        """Test quota reservation for recurring task"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        success = await manager.reserve_quota("create_recurring_task", 5.0)
        
        assert success is True
        assert manager._concurrent_tasks.get_value() == 1
        assert manager._daily_tasks.get_value() == 1
        assert manager._total_tasks.get_value() == 1
        assert manager._recurring_tasks.get_value() == 1
        assert manager._memory_tracker.get_user_memory(self.test_user_guid) == 5.0
    
    @pytest.mark.asyncio
    async def test_reserve_quota_concurrent_limit_exceeded(self):
        """Test quota reservation when concurrent limit is exceeded"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Fill concurrent tasks to limit
        manager._concurrent_tasks._value = self.test_quota_config.max_concurrent_tasks
        
        with pytest.raises(UserQuotaExceededError) as excinfo:
            await manager.reserve_quota("create_task", 10.0)
        
        assert str(excinfo.value.quota_type) == "max_concurrent_tasks"
        assert manager._violations.get_value() == 1
    
    @pytest.mark.asyncio
    async def test_reserve_quota_daily_limit_exceeded(self):
        """Test quota reservation when daily limit is exceeded"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Fill daily tasks to limit
        manager._daily_tasks._value = self.test_quota_config.daily_task_limit
        
        with pytest.raises(UserQuotaExceededError) as excinfo:
            await manager.reserve_quota("create_task", 10.0)
        
        assert str(excinfo.value.quota_type) == "daily_task_limit"
        assert manager._violations.get_value() == 1
    
    @pytest.mark.asyncio
    async def test_reserve_quota_memory_limit_exceeded(self):
        """Test quota reservation when memory limit is exceeded"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Set memory near limit
        manager._memory_tracker.track_user_memory(self.test_user_guid, 95.0)
        
        with pytest.raises(UserQuotaExceededError) as excinfo:
            await manager.reserve_quota("create_task", 10.0)
        
        assert str(excinfo.value.quota_type) == "memory_limit_mb"
        assert manager._violations.get_value() == 1
    
    @pytest.mark.asyncio
    async def test_reserve_quota_recurring_limit_exceeded(self):
        """Test quota reservation when recurring limit is exceeded"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Fill recurring tasks to limit
        manager._recurring_tasks._value = self.test_quota_config.max_recurring_tasks
        
        with pytest.raises(UserQuotaExceededError) as excinfo:
            await manager.reserve_quota("create_recurring_task", 10.0)
        
        assert str(excinfo.value.quota_type) == "max_recurring_tasks"
        assert manager._violations.get_value() == 1
    
    @pytest.mark.asyncio
    async def test_release_quota_standard_task(self):
        """Test quota release for standard task"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Reserve quota first
        await manager.reserve_quota("create_task", 20.0)
        assert manager._concurrent_tasks.get_value() == 1
        assert manager._memory_tracker.get_user_memory(self.test_user_guid) == 20.0
        
        # Release quota
        await manager.release_quota("create_task", 15.0)
        
        assert manager._concurrent_tasks.get_value() == 0
        assert manager._memory_tracker.get_user_memory(self.test_user_guid) == 5.0  # 20 - 15
    
    @pytest.mark.asyncio
    async def test_release_quota_recurring_task(self):
        """Test quota release for recurring task"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Reserve quota first
        await manager.reserve_quota("create_recurring_task", 10.0)
        assert manager._concurrent_tasks.get_value() == 1
        assert manager._recurring_tasks.get_value() == 1
        
        # Release quota
        await manager.release_quota("create_recurring_task", 10.0)
        
        assert manager._concurrent_tasks.get_value() == 0
        assert manager._recurring_tasks.get_value() == 0
        assert manager._memory_tracker.get_user_memory(self.test_user_guid) == 0.0
    
    @pytest.mark.asyncio
    async def test_get_quota_usage(self):
        """Test getting current quota usage"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Reserve some quota
        await manager.reserve_quota("create_task", 30.0)
        await manager.reserve_quota("create_recurring_task", 20.0)
        
        usage = await manager.get_quota_usage()
        
        assert usage.concurrent_tasks == 2
        assert usage.daily_tasks == 2
        assert usage.recurring_tasks == 1
        assert usage.total_tasks_created == 2
        assert usage.memory_usage_mb == 50.0
        assert usage.violations == 0
    
    @pytest.mark.asyncio
    async def test_get_quota_metrics(self):
        """Test getting detailed quota metrics"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Reserve some quota to get meaningful metrics
        await manager.reserve_quota("create_task", 25.0)
        await manager.reserve_quota("create_recurring_task", 15.0)
        
        metrics = await manager.get_quota_metrics()
        
        assert 'user_guid' in metrics
        assert 'quota_config' in metrics
        assert 'current_usage' in metrics
        assert 'peak_usage' in metrics
        assert 'utilization_percentages' in metrics
        assert 'quota_health' in metrics
        assert 'recent_violations' in metrics
        
        assert metrics['user_guid'] == self.test_user_guid
        assert metrics['utilization_percentages']['concurrent_tasks'] == 40.0  # 2/5 * 100
        assert metrics['utilization_percentages']['memory'] == 40.0  # 40/100 * 100
        assert metrics['quota_health'] == "healthy"  # Under 50% utilization
    
    @pytest.mark.asyncio
    async def test_update_quota_config(self):
        """Test updating quota configuration"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        new_config = UserQuotaConfig(
            max_concurrent_tasks=10,
            daily_task_limit=100,
            memory_limit_mb=200,
            thread_pool_size=5,
            max_task_duration_hours=48,
            max_recurring_tasks=20
        )
        
        await manager.update_quota_config(new_config)
        
        assert manager.quota_config == new_config
        assert manager.quota_config.max_concurrent_tasks == 10
        assert manager.quota_config.daily_task_limit == 100
        assert manager.quota_config.memory_limit_mb == 200
    
    @pytest.mark.asyncio
    async def test_reset_daily_quota(self):
        """Test resetting daily quota"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Add some daily tasks
        manager._daily_tasks._value = 25
        
        await manager.reset_daily_quota(force=True)
        
        assert manager._daily_tasks.get_value() == 0
        assert manager._last_reset_date == datetime.now(timezone.utc).date()
    
    @pytest.mark.asyncio
    async def test_get_quota_forecast(self):
        """Test quota usage forecasting"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Simulate some usage for current hour
        current_hour = datetime.now(timezone.utc).hour
        if current_hour > 0:
            manager._daily_tasks._value = 10  # 10 tasks so far today
        
        forecast = await manager.get_quota_forecast(hours_ahead=12)
        
        assert 'current_hourly_rate' in forecast
        assert 'forecasted_daily_usage' in forecast
        assert 'will_exceed_daily_limit' in forecast
        assert 'hours_until_daily_limit' in forecast
        assert 'recommended_action' in forecast
        
        assert isinstance(forecast['will_exceed_daily_limit'], bool)
    
    def test_calculate_quota_health(self):
        """Test quota health calculation"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Test healthy status
        health = manager._calculate_quota_health(25, 30, 35, 40)  # All under 50%
        assert health == "healthy"
        
        # Test moderate status
        health = manager._calculate_quota_health(60, 40, 30, 20)  # Max 60%
        assert health == "moderate"
        
        # Test high status
        health = manager._calculate_quota_health(80, 40, 30, 20)  # Max 80%
        assert health == "high"
        
        # Test critical status
        health = manager._calculate_quota_health(95, 40, 30, 20)  # Max 95%
        assert health == "critical"
    
    def test_calculate_hours_until_limit(self):
        """Test hours until limit calculation"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Test with positive rate
        hours = manager._calculate_hours_until_limit(5.0, 25)  # 5 per hour, 25 used
        expected_hours = (50 - 25) / 5.0  # (limit - used) / rate
        assert hours == expected_hours
        
        # Test with zero rate
        hours = manager._calculate_hours_until_limit(0.0, 25)
        assert hours is None
        
        # Test when already at limit
        hours = manager._calculate_hours_until_limit(5.0, 50)  # Already at limit
        assert hours == 0
    
    def test_get_forecast_recommendation(self):
        """Test forecast recommendation logic"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Test recommendation when will exceed with high rate
        recommendation = manager._get_forecast_recommendation(True, 15.0)
        assert "reducing task creation rate" in recommendation
        
        # Test recommendation when will exceed with low rate
        recommendation = manager._get_forecast_recommendation(True, 5.0)
        assert "approaching daily limit" in recommendation
        
        # Test recommendation when won't exceed
        recommendation = manager._get_forecast_recommendation(False, 5.0)
        assert "within normal limits" in recommendation
    
    def test_should_reset_daily_quota(self):
        """Test daily quota reset logic"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Test with current date (should not reset)
        manager._last_reset_date = datetime.now(timezone.utc).date()
        assert not manager._should_reset_daily_quota()
        
        # Test with yesterday's date (should reset)
        manager._last_reset_date = datetime.now(timezone.utc).date() - timedelta(days=1)
        assert manager._should_reset_daily_quota()
    
    def test_update_peak_usage(self):
        """Test peak usage tracking"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Set some current values
        manager._concurrent_tasks._value = 3
        manager._daily_tasks._value = 15
        manager._recurring_tasks._value = 2
        manager._memory_tracker.track_user_memory(self.test_user_guid, 75.0)
        
        manager._update_peak_usage()
        
        assert manager._peak_usage.concurrent_tasks == 3
        assert manager._peak_usage.daily_tasks == 15
        assert manager._peak_usage.recurring_tasks == 2
        assert manager._peak_usage.memory_usage_mb == 75.0
        
        # Test that peaks are maintained when values decrease
        manager._concurrent_tasks._value = 1
        manager._daily_tasks._value = 10
        manager._update_peak_usage()
        
        assert manager._peak_usage.concurrent_tasks == 3  # Still the peak
        assert manager._peak_usage.daily_tasks == 15     # Still the peak
    
    @pytest.mark.asyncio
    async def test_record_quota_violation(self):
        """Test quota violation recording"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Initial state
        assert len(manager._usage_history) == 0
        
        # Record a violation
        await manager._record_quota_violation("create_task", "Test violation reason")
        
        assert len(manager._usage_history) == 1
        violation = manager._usage_history[0]
        
        assert 'timestamp' in violation
        assert violation['operation_type'] == "create_task"
        assert violation['reason'] == "Test violation reason"
        assert 'usage_snapshot' in violation
    
    @pytest.mark.asyncio
    async def test_record_quota_violation_history_limit(self):
        """Test quota violation history limit"""
        manager = UserQuotaManager(self.test_user_guid, self.test_quota_config)
        
        # Record more than 100 violations
        for i in range(105):
            await manager._record_quota_violation("create_task", f"Violation {i}")
        
        # Should be limited to 100
        assert len(manager._usage_history) == 100
        # Should keep the most recent violations
        assert manager._usage_history[0]['reason'] == "Violation 5"  # First kept
        assert manager._usage_history[-1]['reason'] == "Violation 104"  # Last

class TestSystemQuotaManager:
    """Comprehensive test class for SystemQuotaManager"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.test_user_guid_2 = str(uuid4())
        
    def teardown_method(self):
        """Clean up after each test"""
        pass
    
    def test_system_quota_manager_initialization(self):
        """Test SystemQuotaManager initialization"""
        system_manager = SystemQuotaManager()
        
        assert hasattr(system_manager, '_user_quota_managers')
        assert isinstance(system_manager._user_quota_managers, dict)
        assert hasattr(system_manager, '_managers_lock')
        assert hasattr(system_manager, '_system_metrics')
        assert len(system_manager._user_quota_managers) == 0
        assert system_manager._shutdown is False
    
    @pytest.mark.asyncio
    async def test_system_manager_setup(self):
        """Test system manager setup"""
        system_manager = SystemQuotaManager()
        
        await system_manager.setup()
        
        assert system_manager._monitoring_task is not None
        assert system_manager._cleanup_task is not None
        assert not system_manager._monitoring_task.done()
        assert not system_manager._cleanup_task.done()
    
    @pytest.mark.asyncio
    async def test_system_manager_shutdown(self):
        """Test system manager shutdown"""
        system_manager = SystemQuotaManager()
        await system_manager.setup()
        
        # Add some user managers
        system_manager._user_quota_managers[self.test_user_guid] = Mock()
        
        await system_manager.shutdown()
        
        assert system_manager._shutdown is True
        assert len(system_manager._user_quota_managers) == 0
        assert system_manager._monitoring_task is None or system_manager._monitoring_task.cancelled()
        assert system_manager._cleanup_task is None or system_manager._cleanup_task.cancelled()
    
    @pytest.mark.asyncio
    async def test_get_user_quota_manager_new_user(self):
        """Test getting quota manager for new user"""
        system_manager = SystemQuotaManager()
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_get_config:
            mock_config = UserQuotaConfig(
                max_concurrent_tasks=5,
                daily_task_limit=50,
                memory_limit_mb=100,
                thread_pool_size=3,
                max_task_duration_hours=24,
                max_recurring_tasks=10
            )
            mock_get_config.return_value = mock_config
            
            manager = await system_manager.get_user_quota_manager(self.test_user_guid, PERMISSION_LEVELS.USER)
            
            assert isinstance(manager, UserQuotaManager)
            assert manager.user_guid == self.test_user_guid
            assert self.test_user_guid in system_manager._user_quota_managers
            assert system_manager._system_metrics['total_users'].get_value() == 1
    
    @pytest.mark.asyncio
    async def test_get_user_quota_manager_existing_user(self):
        """Test getting quota manager for existing user"""
        system_manager = SystemQuotaManager()
        
        # Pre-populate with existing manager
        existing_manager = Mock()
        system_manager._user_quota_managers[self.test_user_guid] = existing_manager
        
        manager = await system_manager.get_user_quota_manager(self.test_user_guid, PERMISSION_LEVELS.USER)
        
        assert manager is existing_manager
    
    @pytest.mark.asyncio
    async def test_remove_user_quota_manager_success(self):
        """Test successful removal of user quota manager"""
        system_manager = SystemQuotaManager()
        
        # Add manager first
        system_manager._user_quota_managers[self.test_user_guid] = Mock()
        
        success = await system_manager.remove_user_quota_manager(self.test_user_guid)
        
        assert success is True
        assert self.test_user_guid not in system_manager._user_quota_managers
    
    @pytest.mark.asyncio
    async def test_remove_user_quota_manager_nonexistent(self):
        """Test removal of non-existent user quota manager"""
        system_manager = SystemQuotaManager()
        
        success = await system_manager.remove_user_quota_manager(self.test_user_guid)
        
        assert success is False
    
    @pytest.mark.asyncio
    async def test_get_system_quota_overview(self):
        """Test getting system quota overview"""
        system_manager = SystemQuotaManager()
        
        # Add mock managers
        mock_manager1 = Mock()
        mock_usage1 = QuotaUsage(concurrent_tasks=2, daily_tasks=10, memory_usage_mb=50, violations=0)
        mock_metrics1 = {
            'quota_health': 'healthy',
            'utilization_percentages': {'concurrent_tasks': 40}
        }
        mock_manager1.user_guid = self.test_user_guid
        mock_manager1.get_quota_usage = AsyncMock(return_value=mock_usage1)
        mock_manager1.get_quota_metrics = AsyncMock(return_value=mock_metrics1)
        
        mock_manager2 = Mock()
        mock_usage2 = QuotaUsage(concurrent_tasks=3, daily_tasks=15, memory_usage_mb=75, violations=1)
        mock_metrics2 = {
            'quota_health': 'moderate',
            'utilization_percentages': {'concurrent_tasks': 60}
        }
        mock_manager2.user_guid = self.test_user_guid_2
        mock_manager2.get_quota_usage = AsyncMock(return_value=mock_usage2)
        mock_manager2.get_quota_metrics = AsyncMock(return_value=mock_metrics2)
        
        system_manager._user_quota_managers[self.test_user_guid] = mock_manager1
        system_manager._user_quota_managers[self.test_user_guid_2] = mock_manager2
        
        with patch.object(system_manager, '_get_system_resource_utilization', return_value={}):
            overview = await system_manager.get_system_quota_overview()
        
        assert 'system_totals' in overview
        assert 'system_health' in overview
        assert 'top_users_by_usage' in overview
        assert 'resource_utilization' in overview
        
        totals = overview['system_totals']
        assert totals['active_users'] == 2
        assert totals['total_concurrent_tasks'] == 5  # 2 + 3
        assert totals['total_daily_tasks'] == 25      # 10 + 15
        assert totals['total_memory_usage_mb'] == 125  # 50 + 75
        assert totals['total_violations'] == 1
    
    @pytest.mark.asyncio
    async def test_apply_emergency_quotas(self):
        """Test applying emergency quotas"""
        system_manager = SystemQuotaManager()
        
        # Add mock manager
        mock_manager = Mock()
        original_config = UserQuotaConfig(
            max_concurrent_tasks=10,
            daily_task_limit=100,
            memory_limit_mb=200,
            thread_pool_size=5,
            max_task_duration_hours=24,
            max_recurring_tasks=20
        )
        mock_manager.quota_config = original_config
        mock_manager.update_quota_config = AsyncMock()
        
        system_manager._user_quota_managers[self.test_user_guid] = mock_manager
        
        await system_manager.apply_emergency_quotas(reduction_factor=0.5)
        
        # Verify update_quota_config was called
        mock_manager.update_quota_config.assert_called_once()
        
        # Verify the config passed to update_quota_config
        call_args = mock_manager.update_quota_config.call_args[0][0]
        assert call_args.max_concurrent_tasks == 5     # 10 * 0.5
        assert call_args.daily_task_limit == 50        # 100 * 0.5
        assert call_args.memory_limit_mb == 100        # 200 * 0.5
        assert call_args.max_recurring_tasks == 10     # 20 * 0.5
    
    def test_calculate_system_health_healthy(self):
        """Test system health calculation - healthy state"""
        system_manager = SystemQuotaManager()
        
        user_stats = [
            {'health': 'healthy', 'violations': 0},
            {'health': 'healthy', 'violations': 0},
            {'health': 'moderate', 'violations': 1}
        ]
        
        health = system_manager._calculate_system_health(user_stats)
        assert health == "healthy"
    
    def test_calculate_system_health_moderate(self):
        """Test system health calculation - moderate state"""
        system_manager = SystemQuotaManager()
        
        user_stats = [
            {'health': 'high', 'violations': 2},
            {'health': 'high', 'violations': 1},
            {'health': 'healthy', 'violations': 0},
            {'health': 'healthy', 'violations': 0}
        ]
        
        health = system_manager._calculate_system_health(user_stats)
        assert health == "moderate"  # 50% high users
    
    def test_calculate_system_health_degraded(self):
        """Test system health calculation - degraded state"""
        system_manager = SystemQuotaManager()
        
        user_stats = [
            {'health': 'critical', 'violations': 5},
            {'health': 'high', 'violations': 3},
            {'health': 'high', 'violations': 2},
            {'health': 'healthy', 'violations': 0}
        ]
        
        health = system_manager._calculate_system_health(user_stats)
        assert health == "degraded"  # 25% critical, 75% high+critical
    
    def test_calculate_system_health_critical(self):
        """Test system health calculation - critical state"""
        system_manager = SystemQuotaManager()
        
        user_stats = [
            {'health': 'critical', 'violations': 10},
            {'health': 'critical', 'violations': 8},
            {'health': 'high', 'violations': 5},
            {'health': 'moderate', 'violations': 2}
        ]
        
        health = system_manager._calculate_system_health(user_stats)
        assert health == "critical"  # 50% critical users
    
    def test_calculate_system_health_empty(self):
        """Test system health calculation with no users"""
        system_manager = SystemQuotaManager()
        
        health = system_manager._calculate_system_health([])
        assert health == "healthy"
    
    @pytest.mark.asyncio
    async def test_get_system_resource_utilization_success(self):
        """Test getting system resource utilization successfully"""
        system_manager = SystemQuotaManager()
        
        with patch('psutil.virtual_memory') as mock_memory, \
             patch('psutil.cpu_percent') as mock_cpu, \
             patch('psutil.disk_usage') as mock_disk:
            
            # Mock memory object
            mock_memory_obj = Mock()
            mock_memory_obj.percent = 75.5
            mock_memory.return_value = mock_memory_obj
            
            # Mock CPU
            mock_cpu.return_value = 45.2
            
            # Mock disk object
            mock_disk_obj = Mock()
            mock_disk_obj.used = 500 * 1024 * 1024 * 1024  # 500GB
            mock_disk_obj.total = 1000 * 1024 * 1024 * 1024  # 1TB
            mock_disk.return_value = mock_disk_obj
            
            utilization = await system_manager._get_system_resource_utilization()
            
            assert 'memory_percent' in utilization
            assert 'cpu_percent' in utilization
            assert 'disk_percent' in utilization
            
            assert utilization['memory_percent'] == 75.5
            assert utilization['cpu_percent'] == 45.2
            assert utilization['disk_percent'] == 50.0  # 500/1000 * 100
    
    @pytest.mark.asyncio
    async def test_get_system_resource_utilization_error(self):
        """Test getting system resource utilization with error"""
        system_manager = SystemQuotaManager()
        
        with patch('psutil.virtual_memory', side_effect=Exception("Test error")):
            utilization = await system_manager._get_system_resource_utilization()
            
            assert utilization == {}
    
    @pytest.mark.asyncio
    async def test_system_monitoring_healthy(self):
        """Test system monitoring with healthy state"""
        system_manager = SystemQuotaManager()
        
        mock_overview = {
            'system_health': 'healthy',
            'system_totals': {
                'active_users': 2,
                'total_concurrent_tasks': 5,
                'total_daily_tasks': 25,
                'total_memory_usage_mb': 125.0
            }
        }
        
        with patch.object(system_manager, 'get_system_quota_overview', return_value=mock_overview):
            # Run one iteration of monitoring
            await system_manager._system_monitoring()
            
            # Should complete without errors
            assert True
    
    @pytest.mark.asyncio
    async def test_periodic_cleanup(self):
        """Test periodic cleanup of inactive quota managers"""
        system_manager = SystemQuotaManager()
        
        # Add mock active manager
        active_manager = Mock()
        active_usage = QuotaUsage(concurrent_tasks=1, daily_tasks=5, memory_usage_mb=25)
        active_manager.get_quota_usage = AsyncMock(return_value=active_usage)
        
        # Add mock inactive manager
        inactive_manager = Mock()
        inactive_usage = QuotaUsage(concurrent_tasks=0, daily_tasks=0, memory_usage_mb=0)
        inactive_manager.get_quota_usage = AsyncMock(return_value=inactive_usage)
        
        system_manager._user_quota_managers[self.test_user_guid] = active_manager
        system_manager._user_quota_managers[self.test_user_guid_2] = inactive_manager
        
        # Run one iteration of cleanup
        await system_manager._periodic_cleanup()
        
        # Active manager should remain, inactive should be removed
        assert self.test_user_guid in system_manager._user_quota_managers
        assert self.test_user_guid_2 not in system_manager._user_quota_managers
    
    @pytest.mark.asyncio
    async def test_concurrent_manager_access(self):
        """Test concurrent access to quota managers"""
        system_manager = SystemQuotaManager()
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_get_config:
            mock_config = UserQuotaConfig(
                max_concurrent_tasks=5,
                daily_task_limit=50,
                memory_limit_mb=100,
                thread_pool_size=3,
                max_task_duration_hours=24,
                max_recurring_tasks=10
            )
            mock_get_config.return_value = mock_config
            
            # Create multiple concurrent tasks trying to get the same user manager
            async def get_manager_task():
                return await system_manager.get_user_quota_manager(self.test_user_guid, PERMISSION_LEVELS.USER)
            
            tasks = [asyncio.create_task(get_manager_task()) for _ in range(5)]
            managers = await asyncio.gather(*tasks)
            
            # All tasks should get the same manager instance
            first_manager = managers[0]
            for manager in managers[1:]:
                assert manager is first_manager
            
            # Only one manager should be created
            assert len(system_manager._user_quota_managers) == 1