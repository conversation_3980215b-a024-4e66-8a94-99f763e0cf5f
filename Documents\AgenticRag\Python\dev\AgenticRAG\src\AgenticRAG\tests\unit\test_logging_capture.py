"""
Unit test logging capture module for consistent test logging.

This module provides logging infrastructure for unit tests, ensuring all tests
use LogFire.log() consistently and capture output to test-specific log files.

Key differences from test_real logging:
- No timestamps in filenames (always overwrites previous run)
- Organized by test class and method: tests/unit/logs/<TestClass>/<test_method>.log
- Integrates with pytest environment detection
- Maintains isolation between test methods

Usage:
    @with_unit_test_logging
    def test_my_feature(self):
        LogFire.log("DEBUG", "Test message")  # Goes to test-specific log file
"""
import sys
import os
import functools
from datetime import datetime
from typing import TextIO, Optional
from pathlib import Path
import inspect


class UnitTestTeeOutput:
    """Tee class that writes to both console and unit test log file simultaneously"""
    
    def __init__(self, original_stream: TextIO, log_file_path: str):
        self.original_stream = original_stream
        self.log_file_path = log_file_path
        self.log_file = None
        self._open_log_file()
    
    def _open_log_file(self):
        """Open or reopen the log file"""
        try:
            if self.log_file and not self.log_file.closed:
                self.log_file.close()
            self.log_file = open(self.log_file_path, 'a', encoding='utf-8')
        except Exception as e:
            print(f"Warning: Could not open unit test log file: {e}")
            self.log_file = None
    
    def write(self, text: str):
        """Write to both original stream and log file"""
        # Write to original stream (console)
        if self.original_stream:
            self.original_stream.write(text)
            self.original_stream.flush()
        
        # Write to log file with timestamp
        if self.log_file and not self.log_file.closed:
            try:
                # Add timestamp to each line (similar to debug_trace.log)
                if text.strip():  # Only add timestamp to non-empty lines
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    # Split by lines and add timestamp to each
                    lines = text.splitlines(keepends=True)
                    for line in lines:
                        if line.strip():  # Only process non-empty lines
                            self.log_file.write(f"[{timestamp}] {line}")
                        else:
                            self.log_file.write(line)
                else:
                    self.log_file.write(text)
                self.log_file.flush()
            except Exception as e:
                # If writing to log file fails, continue with console output
                pass
    
    def flush(self):
        """Flush both streams"""
        if self.original_stream:
            self.original_stream.flush()
        if self.log_file and not self.log_file.closed:
            try:
                self.log_file.flush()
            except Exception:
                pass
    
    def close(self):
        """Close the log file"""
        if self.log_file and not self.log_file.closed:
            try:
                self.log_file.close()
            except Exception:
                pass
    
    def __getattr__(self, name):
        """Delegate other attributes to original stream"""
        return getattr(self.original_stream, name)


# Global variables to track capture state
_unit_original_stdout = None
_unit_original_stderr = None
_unit_tee_stdout = None
_unit_tee_stderr = None
_unit_log_file_path = None


def setup_unit_test_debug_capture(test_class_name: str, test_method_name: str):
    """
    Setup stdout/stderr capture to unit test-specific log file.
    
    Args:
        test_class_name: Name of the test class (e.g., "TestOAuthRedirectErrorHandling")
        test_method_name: Name of the test method (e.g., "test_oauth_redirect_post_empty_body")
    """
    global _unit_original_stdout, _unit_original_stderr, _unit_tee_stdout, _unit_tee_stderr, _unit_log_file_path
    
    # If already capturing, clean up first
    if _unit_original_stdout is not None:
        cleanup_unit_test_debug_capture()
    
    # Store original streams
    _unit_original_stdout = sys.stdout
    _unit_original_stderr = sys.stderr
    
    # Create logs directory structure: tests/unit/logs/<TestClass>/<test_method>.log
    logs_dir = Path(f"tests/unit/logs/{test_class_name}")
    logs_dir.mkdir(parents=True, exist_ok=True)
    
    # Generate log filename WITHOUT timestamp (always overwrites)
    log_filename = f"{test_method_name}.log"
    _unit_log_file_path = logs_dir / log_filename
    
    # Initialize log file with header (overwrite mode)
    try:
        with open(_unit_log_file_path, 'w') as f:
            f.write(f"=== UNIT TEST DEBUG TRACE STARTED ===\n")
            f.write(f"Test Class: {test_class_name}\n")
            f.write(f"Test Method: {test_method_name}\n")
            f.write(f"Started: {datetime.now()}\n")
            f.write(f"{'='*80}\n\n")
    except Exception as e:
        print(f"Warning: Could not initialize unit test trace file: {e}")
        return
    
    # Replace stdout and stderr with Tee objects
    _unit_tee_stdout = UnitTestTeeOutput(_unit_original_stdout, str(_unit_log_file_path))
    _unit_tee_stderr = UnitTestTeeOutput(_unit_original_stderr, str(_unit_log_file_path))
    sys.stdout = _unit_tee_stdout
    sys.stderr = _unit_tee_stderr
    
    print(f"Unit test debug trace capture activated - logging to {_unit_log_file_path}")


def cleanup_unit_test_debug_capture():
    """Restore original stdout/stderr and close log file"""
    global _unit_original_stdout, _unit_original_stderr, _unit_tee_stdout, _unit_tee_stderr, _unit_log_file_path
    
    if _unit_original_stdout is not None:
        # Write footer to log file
        if _unit_tee_stdout and _unit_tee_stdout.log_file and not _unit_tee_stdout.log_file.closed:
            try:
                _unit_tee_stdout.log_file.write(f"\n{'='*80}\n")
                _unit_tee_stdout.log_file.write(f"=== UNIT TEST DEBUG TRACE COMPLETED ===\n")
                _unit_tee_stdout.log_file.write(f"Completed: {datetime.now()}\n")
            except:
                pass
        
        # Close tee outputs
        if _unit_tee_stdout:
            _unit_tee_stdout.close()
        if _unit_tee_stderr:
            _unit_tee_stderr.close()
        
        # Restore original streams
        sys.stdout = _unit_original_stdout
        sys.stderr = _unit_original_stderr
        
        # Print summary to original stdout
        if _unit_log_file_path:
            _unit_original_stdout.write(f"Unit test debug trace capture deactivated - log saved to {_unit_log_file_path}\n")
        
        # Reset globals
        _unit_original_stdout = None
        _unit_original_stderr = None
        _unit_tee_stdout = None
        _unit_tee_stderr = None
        _unit_log_file_path = None


def get_current_unit_test_log_path() -> Optional[Path]:
    """Get the current unit test log file path if capture is active"""
    return _unit_log_file_path


def detect_test_class_and_method():
    """
    Detect test class and method name from call stack.
    
    Returns:
        tuple: (test_class_name, test_method_name) or (None, None) if not detected
    """
    frame = inspect.currentframe()
    test_class_name = None
    test_method_name = None
    
    try:
        while frame:
            func_name = frame.f_code.co_name
            
            # Look for test method
            if func_name.startswith('test_') and test_method_name is None:
                test_method_name = func_name
            
            # Look for test class in frame locals
            if 'self' in frame.f_locals:
                obj = frame.f_locals['self']
                class_name = obj.__class__.__name__
                if class_name.startswith('Test'):
                    test_class_name = class_name
                    break
            
            frame = frame.f_back
    finally:
        del frame
    
    return test_class_name, test_method_name


def with_unit_test_logging(func):
    """Decorator to automatically enable unit test logging for test methods"""
    @functools.wraps(func)
    def wrapper(self, *args, **kwargs):
        # Detect test class and method
        test_class_name = self.__class__.__name__
        test_method_name = func.__name__
        
        # Start logging
        setup_unit_test_debug_capture(test_class_name, test_method_name)
        
        try:
            # Run the test
            result = func(self, *args, **kwargs)
            return result
        finally:
            # Always cleanup logging
            cleanup_unit_test_debug_capture()
    
    # Handle async functions
    @functools.wraps(func)
    async def async_wrapper(self, *args, **kwargs):
        # Detect test class and method
        test_class_name = self.__class__.__name__
        test_method_name = func.__name__
        
        # Start logging
        setup_unit_test_debug_capture(test_class_name, test_method_name)
        
        try:
            # Run the async test
            result = await func(self, *args, **kwargs)
            return result
        finally:
            # Always cleanup logging
            cleanup_unit_test_debug_capture()
    
    # Return appropriate wrapper based on function type
    if inspect.iscoroutinefunction(func):
        return async_wrapper
    else:
        return wrapper


def is_unit_test_environment():
    """
    Check if we're running in a unit test environment (pytest but not test_real).
    
    Returns:
        bool: True if in unit test environment
    """
    # Must be in pytest
    if 'pytest' not in sys.modules:
        return False
    
    # Check call stack for test_real
    frame = inspect.currentframe()
    try:
        while frame:
            filename = frame.f_code.co_filename
            if 'test_real' in filename:
                return False  # This is test_real, not unit test
            frame = frame.f_back
    finally:
        del frame
    
    return True  # In pytest but not test_real = unit test