"""
Unit tests for ZairaControl endpoint call_trace functionality.
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import MagicMock, AsyncMock, patch
from uuid import uuid4, UUID
import json

from endpoints.zairacontrol_endpoint import ZairaControlEndpoint
from userprofiles.ZairaUser import ZairaUser, PERMISSION_LEVELS
from userprofiles.ZairaMessage import ZairaMessage, MessageRole
from userprofiles.ZairaChat import ZairaChat

class TestZairaControlCallTrace:
    """Test ZairaControl endpoint call_trace display functionality"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.endpoint = ZairaControlEndpoint()
        self.test_user_guid = str(uuid4())
        self.test_session_guid = uuid4()
        self.test_conversation_guid = uuid4()
        
        # Create test user
        self.test_user = <PERSON>airaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            guid=UUID(self.test_user_guid),
            device_guid=uuid4()
        )
        
        # Create test messages with call_trace
        self.user_message = ZairaMessage.create_user_message(
            "Hello, how are you?",
            str(self.test_conversation_guid),
            str(self.test_session_guid)
        )
        
        self.assistant_message_with_trace = ZairaMessage.create_assistant_message(
            "I'm doing well, thank you!",
            str(self.test_conversation_guid),
            str(self.test_session_guid),
            10,
            call_trace=["supervisor_task", "processing_step", "output_generation"]
        )
        
        self.assistant_message_without_trace = ZairaMessage.create_assistant_message(
            "Another response without trace",
            str(self.test_conversation_guid),
            str(self.test_session_guid),
            15
        )
        
        # Create ZairaChat with messages
        self.test_chat = ZairaChat(
            session_guid=self.test_session_guid,
            user_guid=self.test_user_guid,
            conversation_guid=self.test_conversation_guid
        )
        self.test_chat.add_message(self.user_message)
        self.test_chat.add_message(self.assistant_message_with_trace)
        self.test_chat.add_message(self.assistant_message_without_trace)
        
        # Set up user's chat history
        self.test_user.chat_history = {
            self.test_session_guid: self.test_chat
        }
    
    @pytest.mark.asyncio
    async def test_api_user_chat_history_includes_call_trace(self):
        """Test that chat history API includes call_trace for assistant messages"""
        
        # Mock request and user manager
        request = MagicMock()
        request.query = {'user_guid': self.test_user_guid}
        
        with patch('managers.manager_users.ZairaUserManager') as mock_manager_class:
            mock_manager = MagicMock()
            mock_manager_class.get_instance.return_value = mock_manager
            mock_manager.find_user = AsyncMock(return_value=self.test_user)
            
            # Call the API method
            response = await self.endpoint.api_user_chat_history(request)
            
            # Verify response structure
            assert response.status == 200
            response_data = json.loads(response.text)
            
            # Verify basic structure
            assert 'user_info' in response_data
            assert 'chat_history' in response_data
            assert response_data['user_info']['user_guid'] == self.test_user_guid
            
            # Verify call_trace is included in response
            chat_history = response_data['chat_history']
            assert len(chat_history) == 3  # user message + 2 assistant messages
            
            # Find the assistant message with call_trace
            assistant_with_trace = None
            assistant_without_trace = None
            user_msg = None
            
            for msg in chat_history:
                if msg['role'] == 'assistant' and 'trace' in msg['content']:
                    assistant_with_trace = msg
                elif msg['role'] == 'assistant' and 'Another response' in msg['content']:
                    assistant_without_trace = msg
                elif msg['role'] == 'user':
                    user_msg = msg
            
            # Verify call_trace is present for the right message
            assert assistant_with_trace is not None
            assert 'call_trace' in assistant_with_trace
            assert isinstance(assistant_with_trace['call_trace'], list)
            assert len(assistant_with_trace['call_trace']) == 3
            assert "supervisor_task" in assistant_with_trace['call_trace']
            assert "processing_step" in assistant_with_trace['call_trace']
            assert "output_generation" in assistant_with_trace['call_trace']
            
            # Verify call_trace is empty for assistant message without trace
            assert assistant_without_trace is not None
            assert 'call_trace' in assistant_without_trace
            assert assistant_without_trace['call_trace'] == []
            
            # Verify call_trace is empty for user message
            assert user_msg is not None
            assert 'call_trace' in user_msg
            assert user_msg['call_trace'] == []
    
    @pytest.mark.asyncio
    async def test_api_user_chat_history_handles_legacy_format(self):
        """Test that chat history API handles legacy list format"""
        
        # Create user with legacy format (list of messages instead of ZairaChat)
        legacy_user = ZairaUser(
            username="legacy_user",
            rank=PERMISSION_LEVELS.USER,
            guid=UUID(self.test_user_guid),
            device_guid=uuid4()
        )
        
        # Set up legacy format - direct list of messages
        legacy_user.chat_history = {
            self.test_session_guid: [
                self.user_message,
                self.assistant_message_with_trace,
                self.assistant_message_without_trace
            ]
        }
        
        request = MagicMock()
        request.query = {'user_guid': self.test_user_guid}
        
        with patch('managers.manager_users.ZairaUserManager') as mock_manager_class:
            mock_manager = MagicMock()
            mock_manager_class.get_instance.return_value = mock_manager
            mock_manager.find_user = AsyncMock(return_value=legacy_user)
            
            # Call the API method
            response = await self.endpoint.api_user_chat_history(request)
            
            # Verify it works with legacy format
            assert response.status == 200
            response_data = json.loads(response.text)
            
            chat_history = response_data['chat_history']
            assert len(chat_history) == 3
            
            # Find the assistant message with call_trace
            assistant_with_trace = next(
                (msg for msg in chat_history 
                 if msg['role'] == 'assistant' and 'well' in msg['content']), 
                None
            )
            
            assert assistant_with_trace is not None
            assert len(assistant_with_trace['call_trace']) == 3
    
    def test_call_trace_role_filtering(self):
        """Test that call_trace is only extracted for assistant messages"""
        
        # Create messages with different roles but all having call_trace
        user_msg_with_trace = ZairaMessage.create_user_message(
            "User message",
            str(self.test_conversation_guid),
            str(self.test_session_guid)
        )
        # Manually add call_trace to user message (shouldn't normally happen)
        user_msg_with_trace.call_trace = ["fake_trace"]
        
        system_msg = ZairaMessage.create_system_message(
            "System message",
            str(self.test_conversation_guid),
            str(self.test_session_guid)
        )
        system_msg.call_trace = ["system_trace"]
        
        assistant_msg = ZairaMessage.create_assistant_message(
            "Assistant message",
            str(self.test_conversation_guid),
            str(self.test_session_guid),
            call_trace=["real_trace"]
        )
        
        # Test the filtering logic directly
        test_messages = [user_msg_with_trace, system_msg, assistant_msg]
        filtered_traces = []
        
        for message in test_messages:
            call_trace = []
            if hasattr(message, 'call_trace') and message.call_trace and hasattr(message, 'role'):
                # Only include call_trace for assistant messages
                if getattr(message, 'role', None) in ['assistant', 'ASSISTANT']:
                    call_trace = message.call_trace if isinstance(message.call_trace, list) else [message.call_trace]
            filtered_traces.append(call_trace)
        
        # Verify only assistant message has call_trace
        assert filtered_traces[0] == []  # User message
        assert filtered_traces[1] == []  # System message  
        assert filtered_traces[2] == ["real_trace"]  # Assistant message
    
    @pytest.mark.asyncio
    async def test_api_user_chat_history_empty_chat(self):
        """Test chat history API with user who has no chat history"""
        
        empty_user = ZairaUser(
            username="empty_user",
            rank=PERMISSION_LEVELS.USER,
            guid=UUID(self.test_user_guid),
            device_guid=uuid4()
        )
        # No chat history set
        
        request = MagicMock()
        request.query = {'user_guid': self.test_user_guid}
        
        with patch('managers.manager_users.ZairaUserManager') as mock_manager_class:
            mock_manager = MagicMock()
            mock_manager_class.get_instance.return_value = mock_manager
            mock_manager.find_user = AsyncMock(return_value=empty_user)
            
            response = await self.endpoint.api_user_chat_history(request)
            
            assert response.status == 200
            response_data = json.loads(response.text)
            
            assert response_data['chat_history'] == []
            assert response_data['total_sessions'] == 0
    
    @pytest.mark.asyncio
    async def test_api_user_chat_history_missing_user_guid(self):
        """Test chat history API with missing user_guid parameter"""
        
        request = MagicMock()
        request.query = {}  # No user_guid
        
        response = await self.endpoint.api_user_chat_history(request)
        
        assert response.status == 400
        response_data = json.loads(response.text)
        assert 'error' in response_data
        assert 'user_guid parameter required' in response_data['error']
    
    @pytest.mark.asyncio
    async def test_api_user_chat_history_user_not_found(self):
        """Test chat history API with non-existent user"""
        
        request = MagicMock()
        request.query = {'user_guid': 'non-existent-guid'}
        
        with patch('managers.manager_users.ZairaUserManager') as mock_manager_class:
            mock_manager = MagicMock()
            mock_manager_class.get_instance.return_value = mock_manager
            mock_manager.find_user = AsyncMock(return_value=None)
            
            response = await self.endpoint.api_user_chat_history(request)
            
            assert response.status == 404
            response_data = json.loads(response.text)
            assert 'error' in response_data
            assert 'User not found' in response_data['error']