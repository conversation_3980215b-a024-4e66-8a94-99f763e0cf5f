#!/usr/bin/env python3
"""
Debug script to identify where the startup process hangs.
This script adds detailed logging to track the exact point of failure.
"""

import asyncio
import sys
import os
import time
from typing import Optional

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def log_with_timestamp(message: str):
    """Log message with precise timestamp"""
    timestamp = time.strftime("%H:%M:%S.%f")[:-3]
    print(f"[{timestamp}] DEBUG_STARTUP: {message}")
    sys.stdout.flush()

async def debug_startup():
    """Debug the startup process step by step"""
    log_with_timestamp("=== STARTUP DEBUG SCRIPT STARTED ===")
    
    try:
        # Step 1: Basic imports
        log_with_timestamp("Step 1: Importing basic modules...")
        from imports import *
        log_with_timestamp("Step 1: ✅ Basic imports successful")
        
        # Step 2: LogFire setup
        log_with_timestamp("Step 2: Setting up LogFire...")
        await LogFire.setup()
        log_with_timestamp("Step 2: ✅ LogFire setup successful")
        
        # Step 3: Debug mode setup
        log_with_timestamp("Step 3: Setting debug mode...")
        ZairaSettings.IsDebugMode = True
        Globals.set_debug(ZairaSettings.IsDebugMode)
        log_with_timestamp("Step 3: ✅ Debug mode set")
        
        # Step 4: Check environment
        log_with_timestamp("Step 4: Checking environment...")
        from etc.helper_functions import is_claude_environment
        is_claude = is_claude_environment()
        log_with_timestamp(f"Step 4: ✅ Environment check complete (Claude: {is_claude})")
        
        # Step 5: Main function start
        log_with_timestamp("Step 5: Starting main function...")
        from main import mainFunc
        
        # Step 6: Pre-init checks
        log_with_timestamp("Step 6: Pre-init setup...")
        
        # Step 7: Call init with detailed tracking
        log_with_timestamp("Step 7: About to call init()...")
        
        # Import init function
        from etc.setup import init
        
        # Setup parameters (similar to main.py)
        newProject = False
        DATA_DIR = Path("assets/documents")
        PERSIST_DIR = Path("data")
        
        log_with_timestamp("Step 7a: Creating parsers...")
        from etc.parsers import create_parsers
        parsers = create_parsers()
        log_with_timestamp("Step 7a: ✅ Parsers created")
        
        log_with_timestamp("Step 7b: Calling init() function...")
        await debug_init_process(newProject, DATA_DIR, PERSIST_DIR, parsers)
        log_with_timestamp("Step 7b: ✅ Init completed successfully!")
        
        log_with_timestamp("=== STARTUP DEBUG COMPLETED SUCCESSFULLY ===")
        
    except Exception as e:
        log_with_timestamp(f"❌ ERROR in startup debug: {str(e)}")
        import traceback
        log_with_timestamp(f"❌ TRACEBACK: {traceback.format_exc()}")
        raise

async def debug_init_process(newProject, DATA_DIR, PERSIST_DIR, parsers):
    """Debug the init process with detailed logging"""
    log_with_timestamp("INIT: Starting init process...")
    
    try:
        # Import all required modules for init
        from etc.setup import (
            PostgreSQLManager, LogFire, APIEndpoint, OAuth2Verifier, 
            QDrantManager, RetrievalManager, MeltanoManager, SupervisorManager,
            AgnoManager, MyDiscordBot, MyTeamsBot, MySlackBot, MyWhatsappBot,
            ScheduledRequestPersistenceManager, SystemUserManager,
            generateEmbedding, loadEmbedding, late_init
        )
        from etc.setup import get_dashboard_endpoint
        from managers.scheduled_requests.integration_adapter import ScheduledRequestIntegrationAdapter
        
        log_with_timestamp("INIT: ✅ All imports successful")
        
        # Database setup
        log_with_timestamp("INIT: Setting up databases...")
        await PostgreSQLManager.create_database("vectordb")
        await PostgreSQLManager.connect_to_database("vectordb", use_pool=True, min_size=2, max_size=10)
        log_with_timestamp("INIT: ✅ Database setup complete")
        
        # API and OAuth setup
        log_with_timestamp("INIT: Setting up API and OAuth...")
        await APIEndpoint.setup()
        await OAuth2Verifier.setup()
        Globals.set_debug_values(await OAuth2Verifier.get_token("debug", "access_token"))
        log_with_timestamp("INIT: ✅ API and OAuth setup complete")
        
        # Vector database setup
        log_with_timestamp("INIT: Setting up QDrant...")
        await QDrantManager.setup()
        log_with_timestamp("INIT: ✅ QDrant setup complete")
        
        log_with_timestamp("INIT: Setting up RetrievalManager...")
        await RetrievalManager.setup()
        log_with_timestamp("INIT: ✅ RetrievalManager setup complete")
        
        # Other managers
        log_with_timestamp("INIT: Setting up MeltanoManager...")
        await MeltanoManager.setup()
        log_with_timestamp("INIT: ✅ MeltanoManager setup complete")
        
        # CRITICAL POINT: SupervisorManager setup
        log_with_timestamp("INIT: Setting up SupervisorManager...")
        await SupervisorManager.setup()
        log_with_timestamp("INIT: ✅ SupervisorManager setup complete")
        
        log_with_timestamp("INIT: Setting up AgnoManager...")
        await AgnoManager.setup()
        log_with_timestamp("INIT: ✅ AgnoManager setup complete")
        
        # Bot setups
        log_with_timestamp("INIT: Setting up bots...")
        await MyDiscordBot.setup()
        await MyTeamsBot.setup()
        await MySlackBot.setup()
        await MyWhatsappBot.setup()
        log_with_timestamp("INIT: ✅ Bot setup complete")
        
        # Dashboard
        log_with_timestamp("INIT: Setting up dashboard...")
        dashboard_endpoint = get_dashboard_endpoint()
        await dashboard_endpoint.setup()
        log_with_timestamp("INIT: ✅ Dashboard setup complete")
        
        # Scheduled requests
        log_with_timestamp("INIT: Setting up scheduled request persistence...")
        await ScheduledRequestPersistenceManager.setup()
        log_with_timestamp("INIT: ✅ Scheduled request persistence setup complete")
        
        log_with_timestamp("INIT: Setting up scheduled request integration...")
        await ScheduledRequestIntegrationAdapter.setup()
        log_with_timestamp("INIT: ✅ Scheduled request integration setup complete")
        
        # System user
        log_with_timestamp("INIT: Setting up system user...")
        await SystemUserManager.setup()
        log_with_timestamp("INIT: ✅ System user setup complete")
        
        # INDEX LOADING - This is where the hang was reported
        log_with_timestamp("INIT: About to load/generate index...")
        if newProject:
            log_with_timestamp("INIT: Generating new index...")
            index = await generateEmbedding(DATA_DIR=DATA_DIR, PERSIST_DIR=PERSIST_DIR, parsers=parsers)
        else:
            log_with_timestamp("INIT: Loading existing index...")
            index = await loadEmbedding(DATA_DIR=DATA_DIR, PERSIST_DIR=PERSIST_DIR, parsers=parsers)
        log_with_timestamp("INIT: ✅ Index loading/generation complete")
        
        log_with_timestamp("INIT: Setting global index...")
        Globals.set_index(index)
        log_with_timestamp("INIT: ✅ Global index set")
        
        # CRITICAL POINT: late_init - This is likely where the hang occurs
        log_with_timestamp("INIT: Starting late_init() - THIS IS THE LIKELY HANG POINT...")
        await debug_late_init()
        log_with_timestamp("INIT: ✅ late_init() completed successfully!")
        
    except Exception as e:
        log_with_timestamp(f"INIT: ❌ ERROR in init process: {str(e)}")
        import traceback
        log_with_timestamp(f"INIT: ❌ TRACEBACK: {traceback.format_exc()}")
        raise

async def debug_late_init():
    """Debug the late_init process with detailed logging"""
    log_with_timestamp("LATE_INIT: Starting late_init process...")
    
    try:
        from tasks.task_top_level_supervisor import create_top_level_supervisor
        from tasks.task_top_output_supervisor import create_top_output_supervisor
        from endpoints.api_endpoint import APIEndpoint
        from endpoints.slack_endpoint import MySlackBot
        from managers.scheduled_requests.persistence_manager import ScheduledRequestPersistenceManager
        from managers.manager_system_user import SystemUserManager
        
        # CRITICAL: Top level supervisor creation
        log_with_timestamp("LATE_INIT: Creating top level supervisor...")
        await debug_supervisor_creation()
        log_with_timestamp("LATE_INIT: ✅ Top level supervisor created")
        
        log_with_timestamp("LATE_INIT: Creating top output supervisor...")
        await create_top_output_supervisor()
        log_with_timestamp("LATE_INIT: ✅ Top output supervisor created")
        
        log_with_timestamp("LATE_INIT: API endpoint late setup...")
        await APIEndpoint.late_setup()
        log_with_timestamp("LATE_INIT: ✅ API endpoint late setup complete")
        
        log_with_timestamp("LATE_INIT: Slack bot late setup...")
        await MySlackBot.late_setup()
        log_with_timestamp("LATE_INIT: ✅ Slack bot late setup complete")
        
        log_with_timestamp("LATE_INIT: Scheduled request persistence late setup...")
        persistence_manager = ScheduledRequestPersistenceManager.get_instance()
        await persistence_manager.late_setup()
        log_with_timestamp("LATE_INIT: ✅ Scheduled request persistence late setup complete")
        
        log_with_timestamp("LATE_INIT: System user late setup...")
        await SystemUserManager.late_setup()
        log_with_timestamp("LATE_INIT: ✅ System user late setup complete")
        
        LogFire.log("INIT", "Setup has completed.")
        
    except Exception as e:
        log_with_timestamp(f"LATE_INIT: ❌ ERROR in late_init: {str(e)}")
        import traceback
        log_with_timestamp(f"LATE_INIT: ❌ TRACEBACK: {traceback.format_exc()}")
        raise

async def debug_supervisor_creation():
    """Debug supervisor creation process step by step"""
    log_with_timestamp("SUPERVISOR: Starting supervisor creation debug...")
    
    try:
        from tasks.task_top_level_supervisor import create_top_level_supervisor
        
        # This is likely where the hang occurs
        log_with_timestamp("SUPERVISOR: About to call create_top_level_supervisor()...")
        supervisor = await create_top_level_supervisor()
        log_with_timestamp("SUPERVISOR: ✅ create_top_level_supervisor() completed")
        
        return supervisor
        
    except Exception as e:
        log_with_timestamp(f"SUPERVISOR: ❌ ERROR in supervisor creation: {str(e)}")
        import traceback
        log_with_timestamp(f"SUPERVISOR: ❌ TRACEBACK: {traceback.format_exc()}")
        raise

if __name__ == "__main__":
    log_with_timestamp("Starting debug startup script...")
    try:
        asyncio.run(debug_startup())
        log_with_timestamp("Debug startup script completed successfully!")
    except KeyboardInterrupt:
        log_with_timestamp("Debug startup script interrupted by user")
    except Exception as e:
        log_with_timestamp(f"Debug startup script failed: {str(e)}")
        sys.exit(1)
