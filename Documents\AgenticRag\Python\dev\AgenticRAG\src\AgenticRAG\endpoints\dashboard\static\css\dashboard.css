/* 🗑️ DEAD CODE: This CSS file is not loaded by the UI file system - styles are inline in dashboard_head.txt */
/* Dashboard Main Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, sans-serif;
    background: #000000;
    background-image: 
        radial-gradient(at 0% 0%, #1e3a8a 0%, transparent 50%),
        radial-gradient(at 100% 100%, #1e40af 0%, transparent 50%);
    min-height: 100vh;
    color: #f8fafc;
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(10px); }
    to { opacity: 1; transform: translateY(0); }
}

.header {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    padding: 1rem 2rem;
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5);
    border-bottom: 1px solid rgba(59, 130, 246, 0.2);
}

.header h1 {
    background: linear-gradient(135deg, #60a5fa, #a78bfa);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    font-weight: 700;
    display: inline-block;
    text-shadow: 0 0 30px rgba(96, 165, 250, 0.5);
}

.status-badge {
    display: inline-block;
    background: #28a745;
    color: white;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.9rem;
    font-weight: 500;
    margin-left: 1rem;
    text-transform: uppercase;
}

.container {
    max-width: 1400px;
    margin: 2rem auto;
    padding: 0 2rem;
}

.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.metric-card {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 1.5rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.5);
    border: 1px solid rgba(59, 130, 246, 0.2);
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-5px);
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow: 0 12px 32px rgba(59, 130, 246, 0.2);
}

.metric-title {
    font-size: 0.9rem;
    color: #94a3b8;
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: 0.5rem;
}

.metric-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #f8fafc;
    margin-bottom: 0.5rem;
}

.metric-subtitle {
    font-size: 0.85rem;
    color: #64748b;
}

.section {
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    border-radius: 15px;
    padding: 2rem;
    margin-bottom: 2rem;
    box-shadow: 0 8px 25px rgba(0,0,0,0.5);
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.section h2 {
    color: #93c5fd;
    margin-bottom: 1.5rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.user-search {
    display: flex;
    gap: 1rem;
    margin-bottom: 2rem;
}

.user-search input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    font-size: 1rem;
    background: rgba(15, 23, 42, 0.8);
    color: #f8fafc;
}

.user-search input:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.user-search button {
    padding: 0.75rem 2rem;
    background: linear-gradient(135deg, #3b82f6, #6366f1);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
}

.user-search button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(59, 130, 246, 0.4);
}

.requests-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
    background: rgba(15, 23, 42, 0.5);
    border-radius: 8px;
    overflow: hidden;
}

.requests-table th,
.requests-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid rgba(59, 130, 246, 0.1);
    color: #e2e8f0;
}

.requests-table th {
    background: rgba(30, 41, 59, 0.8);
    font-weight: 600;
    color: #93c5fd;
    text-transform: uppercase;
    font-size: 12px;
    letter-spacing: 0.05em;
}

.status-active { color: #86efac; font-weight: 600; }
.status-paused { color: #fcd34d; font-weight: 600; }
.status-cancelled { color: #fca5a5; font-weight: 600; }
.status-completed { color: #94a3b8; font-weight: 600; }
.status-unknown { color: #64748b; font-weight: 600; }

.btn-cancel {
    background: linear-gradient(135deg, #dc2626, #b91c1c);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 6px;
    cursor: pointer;
    font-size: 0.85rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
}

.btn-cancel:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(220, 38, 38, 0.4);
}

.btn-trace {
    background: linear-gradient(135deg, #0891b2, #0e7490);
    color: white;
    border: none;
    padding: 0.3rem 0.6rem;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.75rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 2px 4px rgba(8, 145, 178, 0.3);
}

.btn-trace:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(8, 145, 178, 0.4);
}

.call-trace-dropdown {
    position: relative;
    background: rgba(30, 41, 59, 0.95);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 8px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(20px);
    z-index: 10;
    max-width: 600px;
    max-height: 400px;
    overflow: auto;
    margin-top: 5px;
}

.call-trace-content {
    padding: 1rem;
}

.call-trace-content h4 {
    margin: 0 0 0.5rem 0;
    color: #93c5fd;
    font-size: 0.9rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.call-trace-content pre {
    background: rgba(15, 23, 42, 0.8);
    border: 1px solid rgba(59, 130, 246, 0.1);
    border-radius: 6px;
    padding: 0.75rem;
    font-size: 0.8rem;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin: 0.5rem 0;
    max-height: 250px;
    overflow-y: auto;
    color: #e2e8f0;
    line-height: 1.5;
}

/* Session Tabs Styling */
.session-tabs-container {
    margin-bottom: 1.5rem;
}

.session-tabs {
    display: flex;
    gap: 0.5rem;
    border-bottom: 2px solid rgba(59, 130, 246, 0.2);
    padding-bottom: 0;
    margin-bottom: 1rem;
    overflow-x: auto;
    overflow-y: hidden;
    max-width: 100%;
}

.session-tab {
    padding: 0.75rem 1.25rem;
    border: none;
    border-radius: 8px 8px 0 0;
    cursor: pointer;
    transition: all 0.2s ease;
    background: rgba(30, 41, 59, 0.5);
    color: #94a3b8;
    border-bottom: 3px solid transparent;
    font-size: 0.9rem;
    font-weight: 500;
    min-width: 140px;
    white-space: nowrap;
    border: 1px solid rgba(59, 130, 246, 0.2);
}

.session-tab:hover {
    background: rgba(59, 130, 246, 0.2);
    color: #e2e8f0;
    border-color: rgba(59, 130, 246, 0.4);
}

.session-tab.active {
    background: linear-gradient(135deg, #3b82f6, #6366f1);
    color: white;
    border-bottom: 3px solid #3b82f6;
    border-color: transparent;
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.3);
}

.session-content {
    display: none;
}

.session-content.active {
    display: block;
}

/* Request Card Styling */
.request-card {
    transition: all 0.2s ease;
}

.request-card:hover {
    box-shadow: 0 4px 12px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

.request-expanded {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-out;
}

.request-expanded.show {
    max-height: 2000px;
}

.prompt-section {
    transition: all 0.2s ease;
}

.prompt-content {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    white-space: pre-wrap;
    word-wrap: break-word;
    line-height: 1.6;
}

.btn-cancel:hover {
    background: #c82333;
}

.loading {
    text-align: center;
    padding: 2rem;
    color: #94a3b8;
}

.error {
    background: rgba(220, 38, 38, 0.1);
    color: #f87171;
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    border: 1px solid rgba(220, 38, 38, 0.3);
}

.auto-refresh {
    position: fixed;
    top: 1rem;
    right: 2rem;
    background: rgba(30, 41, 59, 0.9);
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    color: #94a3b8;
    border: 1px solid rgba(59, 130, 246, 0.2);
    backdrop-filter: blur(10px);
}

/* Tab Interface Styles */
.tab-container {
    width: 100%;
}

.tabs {
    display: flex;
    border-bottom: 2px solid rgba(59, 130, 246, 0.2);
    margin-bottom: 1rem;
}

.tab-button {
    background: none;
    border: none;
    padding: 0.75rem 1.5rem;
    cursor: pointer;
    font-size: 0.9rem;
    color: #94a3b8;
    border-bottom: 2px solid transparent;
    transition: all 0.3s ease;
}

.tab-button:hover {
    color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.tab-button.active {
    color: #3b82f6;
    border-bottom-color: #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.tab-content {
    display: none;
    animation: fadeIn 0.3s ease-in;
}

.tab-content.active {
    display: block;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

/* User Interface Styles */
.user-search-controls {
    display: flex;
    gap: 1rem;
    margin-bottom: 1.5rem;
}

.user-search-controls input {
    flex: 1;
    padding: 0.75rem;
    border: 2px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    font-size: 1rem;
    background: rgba(15, 23, 42, 0.8);
    color: #f8fafc;
}

.user-search-controls input:focus {
    outline: none;
    border-color: rgba(59, 130, 246, 0.5);
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.user-search-controls button {
    padding: 0.75rem 2rem;
    background: linear-gradient(135deg, #3b82f6, #6366f1);
    color: white;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 1rem;
    font-weight: 500;
    transition: all 0.3s ease;
    box-shadow: 0 4px 6px rgba(59, 130, 246, 0.3);
}

.user-search-controls button:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 12px rgba(59, 130, 246, 0.4);
}

.user-card {
    background: rgba(15, 23, 42, 0.8);
    padding: 1rem;
    border-radius: 8px;
    margin-bottom: 0.5rem;
    cursor: pointer;
    transition: all 0.3s ease;
    border: 1px solid transparent;
}

.user-card:hover {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.3);
}

.user-card.selected {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.5);
}

.user-list-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    margin-bottom: 0.5rem;
    background: rgba(30, 41, 59, 0.5);
    transition: all 0.3s ease;
    backdrop-filter: blur(5px);
}

.user-list-item:hover {
    background: rgba(59, 130, 246, 0.1);
    border-color: rgba(59, 130, 246, 0.4);
    box-shadow: 0 4px 8px rgba(0,0,0,0.3);
}

.user-info {
    flex: 1;
}

.user-name {
    font-weight: 600;
    color: #f8fafc;
    margin-bottom: 0.25rem;
}

.user-details {
    font-size: 0.8rem;
    color: #94a3b8;
}

.user-actions {
    display: flex;
    gap: 0.5rem;
}

.btn-small {
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-requests {
    background: #28a745;
    color: white;
}

.btn-requests:hover {
    background: #218838;
}

.btn-chat {
    background: #17a2b8;
    color: white;
}

.btn-chat:hover {
    background: #138496;
}

.chat-message {
    display: flex;
    flex-direction: column;
    padding: 1.5rem;
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 12px;
    margin-bottom: 1.25rem;
    background: rgba(30, 41, 59, 0.8);
    backdrop-filter: blur(10px);
    box-shadow: 0 4px 24px rgba(0, 0, 0, 0.5);
    transition: all 0.2s ease;
}

.chat-message:hover {
    box-shadow: 0 8px 32px rgba(59, 130, 246, 0.2);
    transform: translateY(-2px);
    border-color: rgba(59, 130, 246, 0.4);
}

.chat-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid rgba(59, 130, 246, 0.2);
    font-size: 0.95rem;
    color: #93c5fd;
    font-weight: 600;
    letter-spacing: 0.025em;
    text-transform: uppercase;
}

.chat-content {
    color: #e2e8f0;
    font-size: 1.1rem;
    line-height: 1.8;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    white-space: pre-wrap;
    word-break: break-word;
    letter-spacing: 0.01em;
    max-width: 100%;
    overflow-wrap: break-word;
}

/* Special formatting for code blocks in chat */
.chat-content code {
    background: rgba(15, 23, 42, 0.8);
    padding: 0.125rem 0.375rem;
    border-radius: 0.25rem;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.95em;
    color: #fca5a5;
    border: 1px solid rgba(59, 130, 246, 0.1);
}

.chat-content pre {
    background: #1e293b;
    color: #e2e8f0;
    padding: 1rem;
    border-radius: 0.5rem;
    overflow-x: auto;
    margin: 0.75rem 0;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.9rem;
    line-height: 1.5;
}

.chat-role-user {
    border-left: 5px solid #3b82f6;
    background: rgba(59, 130, 246, 0.1);
}

.chat-role-assistant {
    border-left: 5px solid #10b981;
    background: rgba(16, 185, 129, 0.1);
}

.chat-tokens {
    font-size: 0.85rem;
    color: #94a3b8;
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(59, 130, 246, 0.2);
    font-weight: 500;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.no-data {
    text-align: center;
    padding: 2rem;
    color: #94a3b8;
    font-style: italic;
}

#chatContainer {
    max-height: 700px;
    overflow-y: auto;
    padding: 1rem;
    background: rgba(15, 23, 42, 0.5);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 8px;
    backdrop-filter: blur(5px);
    transition: max-height 0.3s ease;
}

/* Dynamic height adjustment when debug sections are expanded */
#chatContainer.has-expanded-debug {
    max-height: calc(100vh - 30px);
    min-height: 2000px;
}

#chatContainer::-webkit-scrollbar {
    width: 10px;
}

#chatContainer::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.5);
    border-radius: 6px;
    margin: 10px 0;
}

#chatContainer::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.5);
    border-radius: 6px;
    border: 2px solid rgba(30, 41, 59, 0.5);
}

#chatContainer::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.8);
}

/* Chat Session Cards */
.chat-session-card {
    background: rgba(15, 23, 42, 0.7);
    border: 1px solid rgba(59, 130, 246, 0.2);
    border-radius: 12px;
    margin-bottom: 1rem;
    overflow: hidden;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
}

.chat-session-card:hover {
    background: rgba(15, 23, 42, 0.9);
    border-color: rgba(59, 130, 246, 0.4);
}

.session-header {
    padding: 1rem;
    cursor: pointer;
    transition: background-color 0.2s ease;
}

.session-header:hover {
    background: rgba(59, 130, 246, 0.05);
}

.expand-icon {
    display: inline-block;
    transition: transform 0.3s ease;
    margin-right: 0.5rem;
    color: #3b82f6;
}

.expand-icon.expanded {
    transform: rotate(180deg);
}

.session-messages {
    border-top: 1px solid rgba(59, 130, 246, 0.1);
    padding: 0;
}

/* Individual Chat Messages */
.chat-message {
    margin: 0.75rem 0;
    padding: 1rem;
    border-radius: 8px;
    background: rgba(30, 41, 59, 0.5);
    border-left: 4px solid #64748b;
    position: relative;
    transition: all 0.3s ease;
}

.chat-message:hover {
    background: rgba(30, 41, 59, 0.7);
}

.message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.75rem;
    font-size: 0.85rem;
    opacity: 0.8;
}

.message-role {
    font-weight: 700;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.75rem;
}

.message-timestamp {
    color: #94a3b8;
    font-size: 0.75rem;
}

/* Role-specific styling */
.chat-role-user {
    border-left-color: #3b82f6;
    background: rgba(59, 130, 246, 0.05);
}

.chat-role-user .message-role {
    background: rgba(59, 130, 246, 0.2);
    color: #93c5fd;
}

.chat-role-assistant {
    border-left-color: #10b981;
    background: rgba(16, 185, 129, 0.05);
}

.chat-role-assistant .message-role {
    background: rgba(16, 185, 129, 0.2);
    color: #6ee7b7;
}

.chat-role-debug {
    border-left-color: #f59e0b;
    background: rgba(245, 158, 11, 0.05);
}

.chat-role-debug .message-role {
    background: rgba(245, 158, 11, 0.2);
    color: #fbbf24;
}

.chat-role-system {
    border-left-color: #8b5cf6;
    background: rgba(139, 92, 246, 0.05);
}

.chat-role-system .message-role {
    background: rgba(139, 92, 246, 0.2);
    color: #c4b5fd;
}

/* In-Progress Messages */
.in-progress {
    border-left-color: #f97316;
    background: rgba(249, 115, 22, 0.05);
    animation: pulse 2s infinite;
}

.in-progress .message-role {
    background: rgba(249, 115, 22, 0.2);
    color: #fb923c;
}

.progress-indicator {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-style: italic;
    color: #fb923c;
}

.progress-spinner {
    width: 16px;
    height: 16px;
    border: 2px solid rgba(249, 115, 22, 0.2);
    border-top-color: #f97316;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

@keyframes pulse {
    0%, 100% {
        opacity: 1;
    }
    50% {
        opacity: 0.7;
    }
}

/* Call Trace Section */
.call-trace-section {
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(59, 130, 246, 0.1);
}

.expand-button {
    background: rgba(59, 130, 246, 0.1);
    border: 1px solid rgba(59, 130, 246, 0.2);
    color: #93c5fd;
    padding: 0.5rem 0.75rem;
    border-radius: 6px;
    font-size: 0.85rem;
    cursor: pointer;
    transition: all 0.2s ease;
    width: 100%;
    text-align: left;
}

.expand-button:hover {
    background: rgba(59, 130, 246, 0.2);
    border-color: rgba(59, 130, 246, 0.4);
}

.call-trace-content {
    margin-top: 0.75rem;
    background: rgba(15, 23, 42, 0.8);
    border-radius: 6px;
    padding: 0.75rem;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease, opacity 0.3s ease;
    opacity: 0;
}

/* Expanded state for call trace */
.call-trace-content.expanded {
    max-height: min(1600px, 80vh);
    opacity: 1;
    overflow-y: auto;
}

/* Scrollable call trace content */
.call-trace-content.expanded::-webkit-scrollbar {
    width: 6px;
}

.call-trace-content.expanded::-webkit-scrollbar-track {
    background: rgba(59, 130, 246, 0.1);
    border-radius: 3px;
}

.call-trace-content.expanded::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.4);
    border-radius: 3px;
}

.call-trace-content.expanded::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.6);
}

.call-trace-list {
    margin: 0;
    padding-left: 1.25rem;
    color: #e2e8f0;
    font-size: 0.85rem;
    line-height: 1.6;
}

.call-trace-step {
    margin-bottom: 0.25rem;
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

/* Debug Messages Section */
.debug-messages-section {
    margin-top: 1rem;
    padding-top: 0.75rem;
    border-top: 1px solid rgba(245, 158, 11, 0.1);
}

.debug-messages-section .expand-button {
    background: rgba(245, 158, 11, 0.1);
    border-color: rgba(245, 158, 11, 0.2);
    color: #fbbf24;
}

.debug-messages-section .expand-button:hover {
    background: rgba(245, 158, 11, 0.2);
    border-color: rgba(245, 158, 11, 0.4);
}

.debug-messages-content {
    margin-top: 0.75rem;
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.4s ease, opacity 0.3s ease;
    opacity: 0;
}

/* Expanded state for debug messages */
.debug-messages-content.expanded {
    max-height: min(2400px, 95vh);
    opacity: 1;
    overflow-y: auto;
}

/* Scrollable debug content */
.debug-messages-content.expanded::-webkit-scrollbar {
    width: 8px;
}

.debug-messages-content.expanded::-webkit-scrollbar-track {
    background: rgba(245, 158, 11, 0.1);
    border-radius: 4px;
}

.debug-messages-content.expanded::-webkit-scrollbar-thumb {
    background: rgba(245, 158, 11, 0.4);
    border-radius: 4px;
}

.debug-messages-content.expanded::-webkit-scrollbar-thumb:hover {
    background: rgba(245, 158, 11, 0.6);
}

.debug-message-item {
    background: rgba(245, 158, 11, 0.05);
    border: 1px solid rgba(245, 158, 11, 0.1);
    border-radius: 6px;
    padding: 0.75rem;
    margin-bottom: 0.5rem;
}

.debug-message-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.5rem;
    font-size: 0.75rem;
    opacity: 0.8;
}

.debug-role {
    font-weight: 600;
    color: #fbbf24;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.debug-timestamp {
    color: #94a3b8;
}

.debug-tokens {
    color: #fb923c;
    font-weight: 500;
}

.debug-message-content {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    font-size: 0.85rem;
    line-height: 1.5;
    color: #e2e8f0;
    white-space: pre-wrap;
    word-break: break-word;
}

.debug-content {
    font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
    background: rgba(245, 158, 11, 0.05);
    padding: 0.75rem;
    border-radius: 6px;
    margin-top: 0.5rem;
    border: 1px solid rgba(245, 158, 11, 0.1);
}

/* Chat History Container Improvements */
#chatHistoryContainer {
    max-height: 80vh;
    overflow-y: auto;
    padding-right: 0.5rem;
    transition: max-height 0.3s ease;
}

/* Dynamic height adjustment when debug sections are expanded */
#chatHistoryContainer.has-expanded-debug {
    max-height: calc(100vh - 30px);
    min-height: 95vh;
}

/* Requests Container Height Management */
#requestsContainer {
    max-height: 80vh;
    overflow-y: auto;
    padding-right: 0.5rem;
    transition: max-height 0.3s ease;
}

/* Dynamic height adjustment when debug sections are expanded in requests */
#requestsContainer.has-expanded-debug {
    max-height: calc(100vh - 30px);
    min-height: 95vh;
}

#chatHistoryContainer::-webkit-scrollbar {
    width: 8px;
}

#chatHistoryContainer::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.3);
    border-radius: 4px;
}

#chatHistoryContainer::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.4);
    border-radius: 4px;
}

#chatHistoryContainer::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.6);
}

/* Requests Container Scrollbar Styling */
#requestsContainer::-webkit-scrollbar {
    width: 8px;
}

#requestsContainer::-webkit-scrollbar-track {
    background: rgba(30, 41, 59, 0.3);
    border-radius: 4px;
}

#requestsContainer::-webkit-scrollbar-thumb {
    background: rgba(59, 130, 246, 0.4);
    border-radius: 4px;
}

#requestsContainer::-webkit-scrollbar-thumb:hover {
    background: rgba(59, 130, 246, 0.6);
}

/* Responsive adjustments for chat messages */
@media (max-width: 768px) {
    .chat-message {
        padding: 0.75rem;
        margin: 0.5rem 0;
    }
    
    .message-header {
        flex-direction: column;
        align-items: flex-start;
        gap: 0.25rem;
    }
    
    .session-header {
        padding: 0.75rem;
    }
}

/* Live Log Viewer Styles */
#liveLogViewer {
    background: rgba(30, 41, 59, 0.9);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(59, 130, 246, 0.3);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
}

#logContent {
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', 'Courier New', monospace !important;
    background: #0f172a;
    border: 1px solid #374151;
    border-radius: 6px;
    overflow-y: auto;
    overflow-x: auto;
    scrollbar-width: thin;
    scrollbar-color: #4a5568 #1a202c;
    transition: all 0.2s ease;
}

#logContent::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}

#logContent::-webkit-scrollbar-track {
    background: #1a202c;
    border-radius: 4px;
}

#logContent::-webkit-scrollbar-thumb {
    background: #4a5568;
    border-radius: 4px;
    transition: background 0.2s;
}

#logContent::-webkit-scrollbar-thumb:hover {
    background: #68d391;
}

#logContent:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* Log viewer control buttons */
#liveLogViewer button {
    transition: all 0.2s ease;
    font-weight: 500;
    border: none;
    cursor: pointer;
    border-radius: 4px;
    font-size: 0.75rem;
    padding: 0.25rem 0.5rem;
}

#liveLogViewer button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

#liveLogViewer button:active {
    transform: translateY(0);
}

/* Log status indicator */
#logStatus {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    font-size: 0.8rem;
    color: #94a3b8;
}

#logStatusIcon {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    transition: all 0.3s ease;
}

/* Search input styling */
#logSearchInput {
    background: #1e293b;
    border: 1px solid #374151;
    color: #e2e8f0;
    transition: all 0.2s ease;
}

#logSearchInput:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

#logSearchInput::placeholder {
    color: #6b7280;
}

/* Log viewer controls styling */
#liveLogViewer select {
    background: #1e293b;
    border: 1px solid #374151;
    color: #e2e8f0;
    border-radius: 3px;
    padding: 0.25rem;
    font-size: 0.8rem;
    transition: all 0.2s ease;
}

#liveLogViewer select:focus {
    outline: none;
    border-color: #3b82f6;
}

#liveLogViewer input[type="checkbox"] {
    accent-color: #3b82f6;
    margin: 0;
}

/* Log stats styling */
#logStats {
    font-size: 0.75rem;
    color: #94a3b8;
    font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
}

/* Animation for log viewer initialization */
#liveLogViewer.initializing {
    opacity: 0.7;
    pointer-events: none;
}

#liveLogViewer.connected {
    opacity: 1;
    pointer-events: all;
}

/* Log content syntax highlighting (basic) */
#logContent {
    line-height: 1.4;
    white-space: pre-wrap;
    word-break: break-word;
}

/* Responsive adjustments for log viewer */
@media (max-width: 768px) {
    #liveLogViewer {
        margin-left: -1rem;
        margin-right: -1rem;
        border-radius: 0;
        border-left: none;
        border-right: none;
    }
    
    #logContent {
        max-height: 300px;
        font-size: 0.7rem;
    }
    
    #liveLogViewer .controls {
        flex-direction: column;
        gap: 0.5rem;
        align-items: stretch;
    }
    
    #liveLogViewer button {
        flex: 1;
        min-width: unset;
    }
}

/* Integration with existing height management */
.has-expanded-debug #liveLogViewer {
    max-height: 300px;
    overflow: visible;
}

.has-expanded-debug #logContent {
    max-height: 200px;
}

/* Chat page specific adjustments */
#chatPageContent #liveLogViewer {
    border-top: 2px solid rgba(59, 130, 246, 0.2);
    margin-top: 2rem;
    padding-top: 1rem;
}

/* Performance optimization for large logs */
#logContent.performance-mode {
    contain: layout style paint;
    will-change: scroll-position;
}

/* Test server mode styling - red header for localhost:40999 */
.header.test-server-mode {
    background: rgba(220, 38, 38, 0.8) !important;
    border-bottom-color: rgba(239, 68, 68, 0.3);
    box-shadow: 0 2px 8px rgba(220, 38, 38, 0.3);
}

.header.test-server-mode h1 {
    color: #fef2f2 !important;
    text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
    background: linear-gradient(135deg, #fca5a5, #fbbf24) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
}

.header.test-server-mode .status-badge {
    background: rgba(239, 68, 68, 0.9) !important;
    border: 1px solid rgba(248, 113, 113, 0.5);
    color: #fef2f2 !important;
    box-shadow: 0 2px 4px rgba(220, 38, 38, 0.3);
}