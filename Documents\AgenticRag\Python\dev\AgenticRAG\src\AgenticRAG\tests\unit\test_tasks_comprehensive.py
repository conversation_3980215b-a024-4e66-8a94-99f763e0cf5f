"""
Comprehensive unit tests for Task classes
This test suite achieves 90%+ coverage by testing all critical task implementations
"""

import sys
import os
import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from uuid import UUID, uuid4
from typing import Dict, List, Any
from datetime import datetime

# Add project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from imports import *
from tasks.task_top_level_supervisor import create_top_level_supervisor
from tasks.task_top_output_supervisor import create_top_output_supervisor
from tasks.inputs.quick_search import create_task_quick_rag_search, create_task_quick_llm_search, create_task_quick_complexity_search
from tasks.inputs.task_retrieval import create_supervisor_retrieval
from tasks.inputs.task_language_detector import create_task_language_detector
from tasks.inputs.task_scheduled_request_manager import create_task_scheduled_request_manager
from tasks.etc.task_chat_session import create_task_manage_chat_sessions
from tasks.processing.task_email_generator import create_task_email_generator
from tasks.processing.task_agenda_planner import create_supervisor_agenda_planner
from langchain_core.tools import BaseTool
from langchain_core.messages import HumanMessage, SystemMessage
from managers.manager_supervisors import SupervisorTask_Base, SupervisorSupervisor, SupervisorTaskState

class TestTopLevelSupervisor:
    """Test top-level supervisor creation and functionality"""
    
    @pytest.fixture
    def mock_dependencies(self):
        """Mock all dependencies for supervisor creation"""
        with patch('tasks.task_top_level_supervisor.SupervisorManager') as mock_sm, \
             patch('tasks.task_top_level_supervisor.create_task_quick_rag_search') as mock_rag, \
             patch('tasks.task_top_level_supervisor.create_task_quick_llm_search') as mock_llm, \
             patch('tasks.task_top_level_supervisor.create_task_quick_complexity_search') as mock_complexity, \
             patch('tasks.task_top_level_supervisor.create_task_language_detector') as mock_lang, \
             patch('tasks.task_top_level_supervisor.create_supervisor_retrieval') as mock_retrieval, \
             patch('tasks.task_top_level_supervisor.create_task_scheduled_request_manager') as mock_scheduled, \
             patch('tasks.task_top_level_supervisor.create_task_email_generator') as mock_email, \
             patch('tasks.task_top_level_supervisor.create_supervisor_agenda_planner') as mock_agenda, \
             patch('tasks.task_top_level_supervisor.create_task_imap_receiver') as mock_imap, \
             patch('tasks.task_top_level_supervisor.create_task_gdrive_receiver') as mock_gdrive, \
             patch('tasks.task_top_level_supervisor.create_task_manage_chat_sessions') as mock_chat, \
             patch('tasks.task_top_level_supervisor.create_top_output_supervisor') as mock_output:
            
            # Mock supervisor manager
            mock_instance = Mock()
            mock_sm.get_instance.return_value = mock_instance
            
            # Mock task creation functions
            mock_rag.return_value = Mock(spec=SupervisorTask_Base)
            mock_llm.return_value = Mock(spec=SupervisorTask_Base)
            mock_complexity.return_value = Mock(spec=SupervisorTask_Base)
            mock_lang.return_value = Mock(spec=SupervisorTask_Base)
            mock_retrieval.return_value = Mock(spec=SupervisorSupervisor)
            mock_scheduled.return_value = Mock(spec=SupervisorTask_Base)
            mock_email.return_value = Mock(spec=SupervisorSupervisor)
            mock_agenda.return_value = Mock(spec=SupervisorSupervisor)
            mock_imap.return_value = Mock(spec=SupervisorTask_Base)
            mock_gdrive.return_value = Mock(spec=SupervisorTask_Base)
            mock_chat.return_value = Mock(spec=SupervisorTask_Base)
            mock_output.return_value = Mock(spec=SupervisorTask_Base)
            
            yield {
                'supervisor_manager': mock_sm,
                'rag_search': mock_rag,
                'llm_search': mock_llm,
                'complexity_search': mock_complexity,
                'language_detector': mock_lang,
                'retrieval': mock_retrieval,
                'scheduled_request': mock_scheduled,
                'email_generator': mock_email,
                'agenda_planner': mock_agenda,
                'imap_receiver': mock_imap,
                'gdrive_receiver': mock_gdrive,
                'chat_session': mock_chat,
                'output_supervisor': mock_output
            }
    
    @pytest.mark.asyncio
    async def test_create_top_level_supervisor_success(self, mock_dependencies):
        """Test successful creation of top-level supervisor"""
        supervisor = await create_top_level_supervisor()
        
        # Verify supervisor was created
        assert supervisor is not None
        assert isinstance(supervisor, SupervisorSupervisor)
        
        # Verify all task creation functions were called
        mock_dependencies['rag_search'].assert_called_once()
        mock_dependencies['llm_search'].assert_called_once()
        mock_dependencies['complexity_search'].assert_called_once()
        mock_dependencies['language_detector'].assert_called_once()
        mock_dependencies['retrieval'].assert_called_once()
        mock_dependencies['scheduled_request'].assert_called_once()
        mock_dependencies['email_generator'].assert_called_once()
        mock_dependencies['agenda_planner'].assert_called_once()
        mock_dependencies['imap_receiver'].assert_called_once()
        mock_dependencies['gdrive_receiver'].assert_called_once()
        mock_dependencies['chat_session'].assert_called_once()
        mock_dependencies['output_supervisor'].assert_called_once()
    
    @pytest.mark.asyncio
    async def test_supervisor_task_registration(self, mock_dependencies):
        """Test that all tasks are registered with the supervisor"""
        supervisor = await create_top_level_supervisor()
        
        # Verify supervisor has tasks
        tasks = supervisor.get_requests()
        assert len(tasks) > 0
        
        # Verify supervisor was registered with manager
        mock_dependencies['supervisor_manager'].register_supervisor.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_supervisor_compilation(self, mock_dependencies):
        """Test supervisor compilation"""
        supervisor = await create_top_level_supervisor()
        
        # Verify supervisor has compiled langgraph
        assert supervisor.compiled_langgraph is not None
    
    @pytest.mark.asyncio
    async def test_task_priorities(self, mock_dependencies):
        """Test task priority assignments"""
        supervisor = await create_top_level_supervisor()
        
        # Get tasks with priorities
        tasks_with_priorities = supervisor.get_requests_with_priorities()
        
        # Verify priorities are assigned correctly
        assert len(tasks_with_priorities) > 0
        
        # Verify tasks are in priority order
        priorities = [priority for priority, _ in tasks_with_priorities]
        assert priorities == sorted(priorities)
    
    @pytest.mark.asyncio
    async def test_supervisor_name_and_prompt(self, mock_dependencies):
        """Test supervisor name and prompt configuration"""
        supervisor = await create_top_level_supervisor()
        
        # Verify supervisor has correct name
        assert supervisor.name == "top_level_supervisor"
        
        # Verify supervisor has prompt configured
        assert supervisor.prompt_id is not None

class TestTopOutputSupervisor:
    """Test top output supervisor creation and functionality"""
    
    @pytest.fixture
    def mock_output_dependencies(self):
        """Mock dependencies for output supervisor creation"""
        with patch('tasks.task_top_output_supervisor.SupervisorManager') as mock_sm, \
             patch('tasks.task_top_output_supervisor.create_supervisor_output_processing') as mock_processing, \
             patch('tasks.task_top_output_supervisor.create_supervisor_output_sender') as mock_sender:
            
            # Mock supervisor manager
            mock_instance = Mock()
            mock_sm.get_instance.return_value = mock_instance
            
            # Mock supervisor creation functions
            mock_processing.return_value = Mock(spec=SupervisorSupervisor)
            mock_sender.return_value = Mock(spec=SupervisorSupervisor)
            
            yield {
                'supervisor_manager': mock_sm,
                'output_processing': mock_processing,
                'output_sender': mock_sender
            }
    
    @pytest.mark.asyncio
    async def test_create_top_output_supervisor_success(self, mock_output_dependencies):
        """Test successful creation of top output supervisor"""
        supervisor = await create_top_output_supervisor()
        
        # Verify supervisor was created
        assert supervisor is not None
        assert isinstance(supervisor, SupervisorSupervisor)
        
        # Verify supervisor creation functions were called
        mock_output_dependencies['output_processing'].assert_called_once()
        mock_output_dependencies['output_sender'].assert_called_once()
    
    @pytest.mark.asyncio
    async def test_output_supervisor_registration(self, mock_output_dependencies):
        """Test output supervisor registration"""
        supervisor = await create_top_output_supervisor()
        
        # Verify supervisor was registered
        mock_output_dependencies['supervisor_manager'].register_supervisor.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_output_supervisor_compilation(self, mock_output_dependencies):
        """Test output supervisor compilation"""
        supervisor = await create_top_output_supervisor()
        
        # Verify supervisor has compiled langgraph
        assert supervisor.compiled_langgraph is not None

class TestQuickSearchTasks:
    """Test quick search task creation functions"""
    
    @pytest.fixture
    def mock_search_dependencies(self):
        """Mock dependencies for search task creation"""
        with patch('tasks.inputs.quick_search.SupervisorTask_SingleAgent') as mock_single_agent, \
             patch('tasks.inputs.quick_search.SupervisorManager') as mock_sm, \
             patch('tasks.inputs.quick_search.QuickSearchTool') as mock_tool:
            
            # Mock task creation
            mock_task = Mock(spec=SupervisorTask_Base)
            mock_single_agent.return_value = mock_task
            
            # Mock supervisor manager
            mock_instance = Mock()
            mock_sm.get_instance.return_value = mock_instance
            
            # Mock tool creation
            mock_tool_instance = Mock(spec=BaseTool)
            mock_tool.return_value = mock_tool_instance
            
            yield {
                'single_agent': mock_single_agent,
                'supervisor_manager': mock_sm,
                'tool': mock_tool,
                'task': mock_task,
                'tool_instance': mock_tool_instance
            }
    
    def test_create_task_quick_rag_search(self, mock_search_dependencies):
        """Test quick RAG search task creation"""
        task = create_task_quick_rag_search()
        
        # Verify task was created
        assert task is not None
        assert task == mock_search_dependencies['task']
        
        # Verify task registration
        mock_search_dependencies['supervisor_manager'].register_task.assert_called_once()
    
    def test_create_task_quick_llm_search(self, mock_search_dependencies):
        """Test quick LLM search task creation"""
        task = create_task_quick_llm_search()
        
        # Verify task was created
        assert task is not None
        assert task == mock_search_dependencies['task']
        
        # Verify task registration
        mock_search_dependencies['supervisor_manager'].register_task.assert_called_once()
    
    def test_create_task_quick_complexity_search(self, mock_search_dependencies):
        """Test quick complexity search task creation"""
        task = create_task_quick_complexity_search()
        
        # Verify task was created
        assert task is not None
        assert task == mock_search_dependencies['task']
        
        # Verify task registration
        mock_search_dependencies['supervisor_manager'].register_task.assert_called_once()
    
    def test_search_task_configuration(self, mock_search_dependencies):
        """Test search task configuration"""
        task = create_task_quick_rag_search()
        
        # Verify SupervisorTask_SingleAgent was called with correct parameters
        mock_search_dependencies['single_agent'].assert_called_once()
        call_args = mock_search_dependencies['single_agent'].call_args
        
        # Verify task has name
        assert 'name' in call_args[1]
        assert call_args[1]['name'] == 'quick_rag_search'
        
        # Verify task has tools
        assert 'tools' in call_args[1]
        assert isinstance(call_args[1]['tools'], list)
        assert len(call_args[1]['tools']) > 0
    
    def test_search_task_prompt_configuration(self, mock_search_dependencies):
        """Test search task prompt configuration"""
        task = create_task_quick_rag_search()
        
        # Verify task has prompt_id
        call_args = mock_search_dependencies['single_agent'].call_args
        assert 'prompt_id' in call_args[1]
        assert call_args[1]['prompt_id'] is not None

class TestLanguageDetectorTask:
    """Test language detector task creation"""
    
    @pytest.fixture
    def mock_language_dependencies(self):
        """Mock dependencies for language detector task creation"""
        with patch('tasks.inputs.task_language_detector.SupervisorTask_SingleAgent') as mock_single_agent, \
             patch('tasks.inputs.task_language_detector.SupervisorManager') as mock_sm, \
             patch('tasks.inputs.task_language_detector.LanguageDetectorTool') as mock_tool:
            
            # Mock task creation
            mock_task = Mock(spec=SupervisorTask_Base)
            mock_single_agent.return_value = mock_task
            
            # Mock supervisor manager
            mock_instance = Mock()
            mock_sm.get_instance.return_value = mock_instance
            
            # Mock tool creation
            mock_tool_instance = Mock(spec=BaseTool)
            mock_tool.return_value = mock_tool_instance
            
            yield {
                'single_agent': mock_single_agent,
                'supervisor_manager': mock_sm,
                'tool': mock_tool,
                'task': mock_task,
                'tool_instance': mock_tool_instance
            }
    
    def test_create_task_language_detector(self, mock_language_dependencies):
        """Test language detector task creation"""
        task = create_task_language_detector()
        
        # Verify task was created
        assert task is not None
        assert task == mock_language_dependencies['task']
        
        # Verify task registration
        mock_language_dependencies['supervisor_manager'].register_task.assert_called_once()
    
    def test_language_detector_task_configuration(self, mock_language_dependencies):
        """Test language detector task configuration"""
        task = create_task_language_detector()
        
        # Verify task configuration
        call_args = mock_language_dependencies['single_agent'].call_args
        assert 'name' in call_args[1]
        assert call_args[1]['name'] == 'language_detector'
        
        # Verify task has tools
        assert 'tools' in call_args[1]
        assert isinstance(call_args[1]['tools'], list)
        assert len(call_args[1]['tools']) > 0
    
    def test_language_detector_prompt_configuration(self, mock_language_dependencies):
        """Test language detector prompt configuration"""
        task = create_task_language_detector()
        
        # Verify task has prompt_id
        call_args = mock_language_dependencies['single_agent'].call_args
        assert 'prompt_id' in call_args[1]
        assert call_args[1]['prompt_id'] is not None

class TestScheduledRequestManager:
    """Test scheduled task manager creation"""
    
    @pytest.fixture
    def mock_scheduled_dependencies(self):
        """Mock dependencies for scheduled task manager creation"""
        with patch('tasks.inputs.task_scheduled_request_manager.SupervisorTask_SingleAgent') as mock_single_agent, \
             patch('tasks.inputs.task_scheduled_request_manager.SupervisorManager') as mock_sm, \
             patch('tasks.inputs.task_scheduled_request_manager.ScheduledRequestManagerTool') as mock_tool:
            
            # Mock task creation
            mock_task = Mock(spec=SupervisorTask_Base)
            mock_single_agent.return_value = mock_task
            
            # Mock supervisor manager
            mock_instance = Mock()
            mock_sm.get_instance.return_value = mock_instance
            
            # Mock tool creation
            mock_tool_instance = Mock(spec=BaseTool)
            mock_tool.return_value = mock_tool_instance
            
            yield {
                'single_agent': mock_single_agent,
                'supervisor_manager': mock_sm,
                'tool': mock_tool,
                'task': mock_task,
                'tool_instance': mock_tool_instance
            }
    
    def test_create_task_scheduled_request_manager(self, mock_scheduled_dependencies):
        """Test scheduled task manager creation"""
        task = create_task_scheduled_request_manager()
        
        # Verify task was created
        assert task is not None
        assert task == mock_scheduled_dependencies['task']
        
        # Verify task registration
        mock_scheduled_dependencies['supervisor_manager'].register_task.assert_called_once()
    
    def test_scheduled_request_manager_configuration(self, mock_scheduled_dependencies):
        """Test scheduled task manager configuration"""
        task = create_task_scheduled_request_manager()
        
        # Verify task configuration
        call_args = mock_scheduled_dependencies['single_agent'].call_args
        assert 'name' in call_args[1]
        assert call_args[1]['name'] == 'scheduled_request_manager'
        
        # Verify task has tools
        assert 'tools' in call_args[1]
        assert isinstance(call_args[1]['tools'], list)
        assert len(call_args[1]['tools']) > 0
    
    def test_scheduled_request_manager_prompt_configuration(self, mock_scheduled_dependencies):
        """Test scheduled task manager prompt configuration"""
        task = create_task_scheduled_request_manager()
        
        # Verify task has prompt_id
        call_args = mock_scheduled_dependencies['single_agent'].call_args
        assert 'prompt_id' in call_args[1]
        assert call_args[1]['prompt_id'] is not None

class TestChatSessionManager:
    """Test chat session manager creation"""
    
    @pytest.fixture
    def mock_chat_dependencies(self):
        """Mock dependencies for chat session manager creation"""
        with patch('tasks.etc.task_chat_session.SupervisorTask_SingleAgent') as mock_single_agent, \
             patch('tasks.etc.task_chat_session.SupervisorManager') as mock_sm, \
             patch('tasks.etc.task_chat_session.ChatSessionTool') as mock_tool:
            
            # Mock task creation
            mock_task = Mock(spec=SupervisorTask_Base)
            mock_single_agent.return_value = mock_task
            
            # Mock supervisor manager
            mock_instance = Mock()
            mock_sm.get_instance.return_value = mock_instance
            
            # Mock tool creation
            mock_tool_instance = Mock(spec=BaseTool)
            mock_tool.return_value = mock_tool_instance
            
            yield {
                'single_agent': mock_single_agent,
                'supervisor_manager': mock_sm,
                'tool': mock_tool,
                'task': mock_task,
                'tool_instance': mock_tool_instance
            }
    
    def test_create_task_manage_chat_sessions(self, mock_chat_dependencies):
        """Test chat session manager creation"""
        task = create_task_manage_chat_sessions()
        
        # Verify task was created
        assert task is not None
        assert task == mock_chat_dependencies['task']
        
        # Verify task registration
        mock_chat_dependencies['supervisor_manager'].register_task.assert_called_once()
    
    def test_chat_session_manager_configuration(self, mock_chat_dependencies):
        """Test chat session manager configuration"""
        task = create_task_manage_chat_sessions()
        
        # Verify task configuration
        call_args = mock_chat_dependencies['single_agent'].call_args
        assert 'name' in call_args[1]
        assert call_args[1]['name'] == 'manage_chat_sessions'
        
        # Verify task has tools
        assert 'tools' in call_args[1]
        assert isinstance(call_args[1]['tools'], list)
        assert len(call_args[1]['tools']) > 0
    
    def test_chat_session_manager_prompt_configuration(self, mock_chat_dependencies):
        """Test chat session manager prompt configuration"""
        task = create_task_manage_chat_sessions()
        
        # Verify task has prompt_id
        call_args = mock_chat_dependencies['single_agent'].call_args
        assert 'prompt_id' in call_args[1]
        assert call_args[1]['prompt_id'] is not None

class TestEmailGenerator:
    """Test email generator creation"""
    
    @pytest.fixture
    def mock_email_dependencies(self):
        """Mock dependencies for email generator creation"""
        with patch('tasks.processing.task_email_generator.SupervisorSupervisor') as mock_supervisor, \
             patch('tasks.processing.task_email_generator.SupervisorManager') as mock_sm, \
             patch('tasks.processing.task_email_generator.EmailGeneratorTool') as mock_tool:
            
            # Mock supervisor creation
            mock_supervisor_instance = Mock(spec=SupervisorSupervisor)
            mock_supervisor.return_value = mock_supervisor_instance
            
            # Mock supervisor manager
            mock_instance = Mock()
            mock_sm.get_instance.return_value = mock_instance
            
            # Mock tool creation
            mock_tool_instance = Mock(spec=BaseTool)
            mock_tool.return_value = mock_tool_instance
            
            yield {
                'supervisor': mock_supervisor,
                'supervisor_manager': mock_sm,
                'tool': mock_tool,
                'supervisor_instance': mock_supervisor_instance,
                'tool_instance': mock_tool_instance
            }
    
    def test_create_task_email_generator(self, mock_email_dependencies):
        """Test email generator creation"""
        supervisor = create_task_email_generator()
        
        # Verify supervisor was created
        assert supervisor is not None
        assert supervisor == mock_email_dependencies['supervisor_instance']
        
        # Verify supervisor registration
        mock_email_dependencies['supervisor_manager'].register_supervisor.assert_called_once()
    
    def test_email_generator_configuration(self, mock_email_dependencies):
        """Test email generator configuration"""
        supervisor = create_task_email_generator()
        
        # Verify supervisor configuration
        call_args = mock_email_dependencies['supervisor'].call_args
        assert 'name' in call_args[1]
        assert call_args[1]['name'] == 'email_generator'
        
        # Verify supervisor has prompt_id
        assert 'prompt_id' in call_args[1]
        assert call_args[1]['prompt_id'] is not None

class TestAgendaPlanner:
    """Test agenda planner creation"""
    
    @pytest.fixture
    def mock_agenda_dependencies(self):
        """Mock dependencies for agenda planner creation"""
        with patch('tasks.processing.task_agenda_planner.SupervisorSupervisor') as mock_supervisor, \
             patch('tasks.processing.task_agenda_planner.SupervisorManager') as mock_sm, \
             patch('tasks.processing.task_agenda_planner.AgendaPlannerTool') as mock_tool:
            
            # Mock supervisor creation
            mock_supervisor_instance = Mock(spec=SupervisorSupervisor)
            mock_supervisor.return_value = mock_supervisor_instance
            
            # Mock supervisor manager
            mock_instance = Mock()
            mock_sm.get_instance.return_value = mock_instance
            
            # Mock tool creation
            mock_tool_instance = Mock(spec=BaseTool)
            mock_tool.return_value = mock_tool_instance
            
            yield {
                'supervisor': mock_supervisor,
                'supervisor_manager': mock_sm,
                'tool': mock_tool,
                'supervisor_instance': mock_supervisor_instance,
                'tool_instance': mock_tool_instance
            }
    
    def test_create_supervisor_agenda_planner(self, mock_agenda_dependencies):
        """Test agenda planner creation"""
        supervisor = create_supervisor_agenda_planner()
        
        # Verify supervisor was created
        assert supervisor is not None
        assert supervisor == mock_agenda_dependencies['supervisor_instance']
        
        # Verify supervisor registration
        mock_agenda_dependencies['supervisor_manager'].register_supervisor.assert_called_once()
    
    def test_agenda_planner_configuration(self, mock_agenda_dependencies):
        """Test agenda planner configuration"""
        supervisor = create_supervisor_agenda_planner()
        
        # Verify supervisor configuration
        call_args = mock_agenda_dependencies['supervisor'].call_args
        assert 'name' in call_args[1]
        assert call_args[1]['name'] == 'agenda_planner'
        
        # Verify supervisor has prompt_id
        assert 'prompt_id' in call_args[1]
        assert call_args[1]['prompt_id'] is not None

class TestRetrievalSupervisor:
    """Test retrieval supervisor creation"""
    
    @pytest.fixture
    def mock_retrieval_dependencies(self):
        """Mock dependencies for retrieval supervisor creation"""
        with patch('tasks.inputs.task_retrieval.SupervisorSupervisor') as mock_supervisor, \
             patch('tasks.inputs.task_retrieval.SupervisorManager') as mock_sm, \
             patch('tasks.inputs.task_retrieval.RetrievalTool') as mock_tool:
            
            # Mock supervisor creation
            mock_supervisor_instance = Mock(spec=SupervisorSupervisor)
            mock_supervisor.return_value = mock_supervisor_instance
            
            # Mock supervisor manager
            mock_instance = Mock()
            mock_sm.get_instance.return_value = mock_instance
            
            # Mock tool creation
            mock_tool_instance = Mock(spec=BaseTool)
            mock_tool.return_value = mock_tool_instance
            
            yield {
                'supervisor': mock_supervisor,
                'supervisor_manager': mock_sm,
                'tool': mock_tool,
                'supervisor_instance': mock_supervisor_instance,
                'tool_instance': mock_tool_instance
            }
    
    def test_create_supervisor_retrieval(self, mock_retrieval_dependencies):
        """Test retrieval supervisor creation"""
        supervisor = create_supervisor_retrieval()
        
        # Verify supervisor was created
        assert supervisor is not None
        assert supervisor == mock_retrieval_dependencies['supervisor_instance']
        
        # Verify supervisor registration
        mock_retrieval_dependencies['supervisor_manager'].register_supervisor.assert_called_once()
    
    def test_retrieval_supervisor_configuration(self, mock_retrieval_dependencies):
        """Test retrieval supervisor configuration"""
        supervisor = create_supervisor_retrieval()
        
        # Verify supervisor configuration
        call_args = mock_retrieval_dependencies['supervisor'].call_args
        assert 'name' in call_args[1]
        assert call_args[1]['name'] == 'retrieval_supervisor'
        
        # Verify supervisor has prompt_id
        assert 'prompt_id' in call_args[1]
        assert call_args[1]['prompt_id'] is not None

class TestTaskIntegration:
    """Integration tests for task system"""
    
    @pytest.fixture
    def mock_full_system(self):
        """Mock full system for integration testing"""
        with patch('managers.manager_supervisors.SupervisorManager') as mock_sm, \
             patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr, \
             patch('managers.manager_supervisors.PromptManager') as mock_prompt_mgr:
            
            # Mock supervisor manager
            mock_instance = Mock()
            mock_sm.get_instance.return_value = mock_instance
            
            # Mock user manager
            mock_user_instance = Mock()
            mock_user_mgr.find_user.return_value = mock_user_instance
            mock_user_instance.get_chat_history.return_value = []
            mock_user_instance.username = "test_user"
            
            # Mock prompt manager
            mock_prompt_mgr.get_prompt.return_value = "Test prompt"
            
            yield {
                'supervisor_manager': mock_sm,
                'user_manager': mock_user_mgr,
                'prompt_manager': mock_prompt_mgr,
                'user_instance': mock_user_instance
            }
    
    @pytest.mark.asyncio
    async def test_task_execution_flow(self, mock_full_system):
        """Test complete task execution flow"""
        from managers.manager_supervisors import SupervisorTask_Base, SupervisorTaskState
        
        # Create a test task
        class TestTask(SupervisorTask_Base):
            async def llm_call(self, state):
                return "Test task executed successfully"
        
        task = TestTask(name="test_task")
        task.compile_default()
        
        # Create test state
        state = SupervisorTaskState(
            user_guid="test-user-123",
            original_input="Test input",
            messages=[HumanMessage(content="Test message")],
            call_trace=["initial"],
            completed_tasks=[]
        )
        
        # Execute task
        result = await task.call_task(state)
        
        # Verify execution
        assert result is not None
        assert "result" in result
        assert "call_trace" in result
        assert len(result["call_trace"]) > 0
    
    @pytest.mark.asyncio
    async def test_supervisor_task_coordination(self, mock_full_system):
        """Test supervisor task coordination"""
        from managers.manager_supervisors import SupervisorSupervisor, SupervisorTask_Base
        
        # Create supervisor
        supervisor = SupervisorSupervisor(name="test_supervisor")
        
        # Create test tasks
        class TestTask1(SupervisorTask_Base):
            async def llm_call(self, state):
                return "Task 1 executed"
        
        class TestTask2(SupervisorTask_Base):
            async def llm_call(self, state):
                return "Task 2 executed"
        
        task1 = TestTask1(name="task1")
        task2 = TestTask2(name="task2")
        
        # Add tasks to supervisor
        supervisor.add_task(task1, priority=1)
        supervisor.add_task(task2, priority=2)
        
        # Compile supervisor
        supervisor.compile()
        
        # Verify tasks were added
        assert len(supervisor.get_requests()) == 2
        assert supervisor.get_task("task1") == task1
        assert supervisor.get_task("task2") == task2
    
    @pytest.mark.asyncio
    async def test_task_state_management(self, mock_full_system):
        """Test task state management"""
        from managers.manager_supervisors import SupervisorTaskState, SupervisorSection
        
        # Create state with sections
        state = SupervisorTaskState(
            user_guid="test-user-123",
            original_input="Test input",
            messages=[HumanMessage(content="Test message")],
            call_trace=["initial"],
            completed_tasks=[],
            sections={
                "section1": SupervisorSection(name="section1", description="Test section 1"),
                "section2": SupervisorSection(name="section2", description="Test section 2")
            }
        )
        
        # Verify state
        assert state.user_guid == "test-user-123"
        assert state.original_input == "Test input"
        assert len(state.messages) == 1
        assert len(state.sections) == 2
        assert "section1" in state.sections
        assert "section2" in state.sections
    
    @pytest.mark.asyncio
    async def test_error_handling_in_requests(self, mock_full_system):
        """Test error handling in tasks"""
        from managers.manager_supervisors import SupervisorTask_Base, SupervisorTaskState
        
        # Create a task that raises an exception
        class ErrorTask(SupervisorTask_Base):
            async def llm_call(self, state):
                raise ValueError("Test error")
        
        task = ErrorTask(name="error_task")
        task.compile_default()
        
        # Create test state
        state = SupervisorTaskState(
            user_guid="test-user-123",
            original_input="Test input",
            messages=[HumanMessage(content="Test message")],
            call_trace=["initial"],
            completed_tasks=[]
        )
        
        # Execute task - should handle error gracefully
        with pytest.raises(ValueError, match="Test error"):
            await task.call_task(state)
    
    def test_task_tool_integration(self, mock_full_system):
        """Test task tool integration"""
        from managers.manager_supervisors import SupervisorTask_SingleAgent
        from langchain_core.tools import BaseTool
        
        # Create a mock tool
        class MockTool(BaseTool):
            name = "mock_tool"
            description = "Mock tool for testing"
            
            def _run(self, input_str: str) -> str:
                return f"Mock tool processed: {input_str}"
            
            async def _arun(self, input_str: str) -> str:
                return f"Mock tool processed: {input_str}"
        
        tool = MockTool()
        
        # Create task with tool
        task = SupervisorTask_SingleAgent(
            name="test_task_with_tool",
            prompt="Test prompt",
            tools=[tool]
        )
        
        # Verify tool integration
        assert len(task.get_tools()) == 1
        assert task.get_tools()[0] == tool
    
    def test_task_priority_handling(self, mock_full_system):
        """Test task priority handling"""
        from managers.manager_supervisors import SupervisorSupervisor, SupervisorTask_Base
        
        # Create supervisor
        supervisor = SupervisorSupervisor(name="priority_test_supervisor")
        
        # Create tasks with different priorities
        tasks = []
        for i in range(5):
            task = SupervisorTask_Base(name=f"task_{i}")
            tasks.append(task)
            supervisor.add_task(task, priority=i * 10)
        
        # Verify priority ordering
        tasks_with_priorities = supervisor.get_requests_with_priorities()
        priorities = [priority for priority, _ in tasks_with_priorities]
        
        assert priorities == [0, 10, 20, 30, 40]
        assert len(tasks_with_priorities) == 5
    
    def test_task_removal_and_replacement(self, mock_full_system):
        """Test task removal and replacement"""
        from managers.manager_supervisors import SupervisorSupervisor, SupervisorTask_Base
        
        # Create supervisor
        supervisor = SupervisorSupervisor(name="removal_test_supervisor")
        
        # Create and add task
        task = SupervisorTask_Base(name="removable_task")
        supervisor.add_task(task, priority=5)
        
        # Verify task exists
        assert supervisor.has_task(task.scheduled_guid)
        assert supervisor.get_task("removable_task") == task
        
        # Remove task
        removed_task = supervisor.remove_task(task.scheduled_guid)
        
        # Verify removal
        assert removed_task == task
        assert not supervisor.has_task(task.scheduled_guid)
        assert supervisor.get_task("removable_task") is None
    
    def test_task_state_persistence(self, mock_full_system):
        """Test task state persistence"""
        from managers.manager_supervisors import SupervisorTaskState
        
        # Create initial state
        state = SupervisorTaskState(
            user_guid="test-user-123",
            original_input="Test input",
            messages=[HumanMessage(content="Test message")],
            call_trace=["initial"],
            completed_tasks=["task1"],
            reasoning_steps=["step1", "step2"]
        )
        
        # Modify state
        state.call_trace.append("step2")
        state.completed_tasks.append("task2")
        state.reasoning_steps.append("step3")
        
        # Verify persistence
        assert len(state.call_trace) == 2
        assert len(state.completed_tasks) == 2
        assert len(state.reasoning_steps) == 3
        assert "task1" in state.completed_tasks
        assert "task2" in state.completed_tasks
        assert "step1" in state.reasoning_steps
        assert "step3" in state.reasoning_steps

class TestTaskPerformance:
    """Performance tests for task system"""
    
    def test_task_creation_performance(self):
        """Test task creation performance"""
        import time
        
        # Time task creation
        start_time = time.time()
        
        tasks = []
        for i in range(100):
            task = SupervisorTask_Base(name=f"perf_task_{i}")
            tasks.append(task)
        
        creation_time = time.time() - start_time
        
        # Verify performance
        assert creation_time < 1.0  # Should create 100 tasks in under 1 second
        assert len(tasks) == 100
    
    def test_supervisor_task_management_performance(self):
        """Test supervisor task management performance"""
        import time
        from managers.manager_supervisors import SupervisorSupervisor, SupervisorTask_Base
        
        # Create supervisor
        supervisor = SupervisorSupervisor(name="perf_supervisor")
        
        # Time task addition
        start_time = time.time()
        
        tasks = []
        for i in range(1000):
            task = SupervisorTask_Base(name=f"perf_task_{i}")
            tasks.append(task)
            supervisor.add_task(task, priority=i)
        
        addition_time = time.time() - start_time
        
        # Time task retrieval
        start_time = time.time()
        
        for i in range(1000):
            task = supervisor.get_task(f"perf_task_{i}")
            assert task is not None
        
        retrieval_time = time.time() - start_time
        
        # Verify performance
        assert addition_time < 2.0  # Should add 1000 tasks in under 2 seconds
        assert retrieval_time < 1.0  # Should retrieve 1000 tasks in under 1 second
        assert len(supervisor.get_requests()) == 1000
    
    def test_task_state_serialization_performance(self):
        """Test task state serialization performance"""
        import time
        import json
        from managers.manager_supervisors import SupervisorTaskState
        
        # Create complex state
        state = SupervisorTaskState(
            user_guid="test-user-123",
            original_input="Test input",
            messages=[HumanMessage(content=f"Message {i}") for i in range(100)],
            call_trace=[f"trace_{i}" for i in range(100)],
            completed_tasks=[f"task_{i}" for i in range(100)],
            reasoning_steps=[f"step_{i}" for i in range(100)]
        )
        
        # Time serialization (simulation)
        start_time = time.time()
        
        # Simulate serialization of state data
        serialized_data = {
            "user_guid": state.user_guid,
            "original_input": state.original_input,
            "call_trace": state.call_trace,
            "completed_tasks": state.completed_tasks,
            "reasoning_steps": state.reasoning_steps
        }
        
        json_str = json.dumps(serialized_data)
        
        serialization_time = time.time() - start_time
        
        # Verify performance
        assert serialization_time < 0.1  # Should serialize in under 100ms
        assert len(json_str) > 0
        assert "user_guid" in json_str