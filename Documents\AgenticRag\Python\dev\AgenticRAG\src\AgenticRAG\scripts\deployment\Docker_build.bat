pushd ".\..\bat" && call "checkdocker.bat" && popd
docker build -f ./Dockerfile -t askzaira ./../..

:: Read Docker token from file
::@echo off
::setlocal enabledelayedexpansion
::set /p DOCKER_TOKEN=<docker_token.txt
::echo !DOCKER_TOKEN! | docker login --username askzaira --password-stdin

docker tag askzaira:latest dns.askzaira.com:2054/askzaira:latest
docker push dns.askzaira.com:2054/askzaira:latest
::endlocal
pause
