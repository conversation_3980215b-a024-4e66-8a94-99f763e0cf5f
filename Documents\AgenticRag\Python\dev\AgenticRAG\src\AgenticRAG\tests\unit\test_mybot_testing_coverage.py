from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from managers.manager_logfire import LogFire
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from endpoints.testing_endpoint import MyBot_Testing
from uuid import uuid4

class TestMyBotTestingCoverage:
    """Test suite to achieve 90% coverage for MyBot_Testing"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.bot = MyBot_Testing()
        
        # Mock task and user
        self.mock_task = MagicMock()
        self.mock_user = MagicMock()
        self.mock_user.session_guid = uuid4()
        self.mock_user.on_message = AsyncMock()
        self.mock_task.user = self.mock_user
        self.mock_task.scheduled_guid = str(uuid4())
        self.mock_task.original_physical_message = MagicMock()
        self.mock_task.human_in_the_loop_callback = MagicMock()
        
    @pytest.mark.asyncio
    async def test_generate_test_response_successful_llm_with_LogFire.log("DEBUG", self):
        """Test _generate_test_response with successful LLM response and print (lines 71-78)"""
        bot = MyBot_Testing()
        
        # Mock ZairaSettings with proper llm
        mock_llm = MagicMock()
        mock_message = MagicMock()
        mock_message.content = "Generated LLM response"
        mock_llm.ainvoke = AsyncMock(return_value=mock_message)
        
        # Mock ZairaSettings class and instance
        mock_settings_class = MagicMock()
        mock_settings_class.llm = mock_llm
        
        with patch('etc.ZairaSettings.ZairaSettings', mock_settings_class), \
             patch('builtins.print') as mock_print:
            
            response = await bot._generate_test_response("Test request")
            
            # Verify the response was returned (line 78)
            assert response == "Generated LLM response"
            
            # Verify print was called with correct format (line 77)
            mock_print.assert_called()
            print_calls = [call[0][0] for call in mock_print.call_args_list]
            assert any("[TEST BOT] Generated response for 'Test request':" in call for call in print_calls)
            
    @pytest.mark.asyncio
    async def test_generate_test_response_empty_response_fallback(self):
        """Test _generate_test_response with empty LLM response fallback (lines 74-75)"""
        bot = MyBot_Testing()
        
        # Mock ZairaSettings with proper llm that returns empty response
        mock_llm = MagicMock()
        mock_message = MagicMock()
        mock_message.content = ""  # Empty response
        mock_llm.ainvoke = AsyncMock(return_value=mock_message)
        
        mock_settings_class = MagicMock()
        mock_settings_class.llm = mock_llm
        
        with patch('etc.ZairaSettings.ZairaSettings', mock_settings_class), \
             patch.object(bot, '_get_fallback_response', return_value="Fallback response") as mock_fallback:
            
            response = await bot._generate_test_response("Test request")
            
            # Verify fallback was called (line 75)
            assert response == "Fallback response"
            mock_fallback.assert_called_once_with("Test request")
            
    @pytest.mark.asyncio
    async def test_generate_test_response_response_strip(self):
        """Test _generate_test_response strips whitespace from response (line 71)"""
        bot = MyBot_Testing()
        
        # Mock ZairaSettings with proper llm that returns response with whitespace
        mock_llm = MagicMock()
        mock_message = MagicMock()
        mock_message.content = "  Response with whitespace  "
        mock_llm.ainvoke = AsyncMock(return_value=mock_message)
        
        mock_settings_class = MagicMock()
        mock_settings_class.llm = mock_llm
        
        with patch('etc.ZairaSettings.ZairaSettings', mock_settings_class):
            response = await bot._generate_test_response("Test request")
            
            # Verify response was stripped (line 71)
            assert response == "Response with whitespace"
            
    @pytest.mark.asyncio
    async def test_halt_until_response_callback_cleared(self):
        """Test halt_until_response loop with callback cleared (lines 34-37)"""
        bot = MyBot_Testing(name="Testing")
        
        # Mock task with callback that gets cleared
        task = MagicMock()
        task.user = MagicMock()
        task.user.on_message = AsyncMock()
        task.scheduled_guid = str(uuid4())
        task.original_physical_message = MagicMock()
        task.human_in_the_loop_callback = MagicMock()  # Initially set
        
        # Mock the _generate_test_response to return quickly
        with patch.object(bot, '_generate_test_response', return_value="Test response") as mock_generate, \
             patch('builtins.print'), \
             patch('asyncio.sleep', return_value=None) as mock_sleep:
            
            # Clear the callback after first sleep iteration
            async def clear_callback(*args):
                task.human_in_the_loop_callback = None
            
            mock_sleep.side_effect = clear_callback
            
            # This should enter the while loop and then exit when callback is None
            await bot.request_human_in_the_loop_internal(
                "Test request", task, MagicMock(), halt_until_response=True
            )
            
            # Verify the loop was entered (line 34-37)
            mock_sleep.assert_called_once_with(0.1)
            
    @pytest.mark.asyncio
    async def test_halt_until_response_no_callback(self):
        """Test halt_until_response with no callback set initially"""
        bot = MyBot_Testing(name="Testing")
        
        # Mock task with no callback
        task = MagicMock()
        task.user = MagicMock()
        task.user.on_message = AsyncMock()
        task.scheduled_guid = str(uuid4())
        task.original_physical_message = MagicMock()
        task.human_in_the_loop_callback = None  # No callback
        
        with patch.object(bot, '_generate_test_response', return_value="Test response"), \
             patch('builtins.print'), \
             patch('asyncio.sleep', return_value=None) as mock_sleep:
            
            # This should not enter the while loop
            await bot.request_human_in_the_loop_internal(
                "Test request", task, MagicMock(), halt_until_response=True
            )
            
            # Verify the loop was not entered since callback is None
            mock_sleep.assert_not_called()
            
    def test_type_checking_import(self):
        """Test TYPE_CHECKING import (line 10)"""
        # This tests that the TYPE_CHECKING import is working
        from typing import TYPE_CHECKING
        
        # The import should work without error
        assert TYPE_CHECKING is not None
        
        # Verify the conditional import would work
        if TYPE_CHECKING:
            # This would only run during type checking
            pass
            
    @pytest.mark.asyncio
    async def test_generate_test_response_hasattr_check_true(self):
        """Test _generate_test_response hasattr check when hasattr returns True"""
        bot = MyBot_Testing()
        
        # Create a mock settings object with llm attribute
        mock_settings = MagicMock()
        mock_llm = MagicMock()
        mock_message = MagicMock()
        mock_message.content = "LLM response"
        mock_llm.ainvoke = AsyncMock(return_value=mock_message)
        mock_settings.llm = mock_llm
        
        with patch('etc.ZairaSettings.ZairaSettings', mock_settings):
            response = await bot._generate_test_response("Test request")
            
            # Should use LLM path
            assert response == "LLM response"
            
    @pytest.mark.asyncio
    async def test_generate_test_response_hasattr_check_false(self):
        """Test _generate_test_response hasattr check when hasattr returns False"""
        bot = MyBot_Testing()
        
        # Create a mock settings object without llm attribute
        mock_settings = type('MockSettings', (), {})()
        # Don't set llm attribute, so hasattr will return False
        
        with patch('etc.ZairaSettings.ZairaSettings', mock_settings), \
             patch.object(bot, '_get_fallback_response', return_value="Fallback response") as mock_fallback:
            
            response = await bot._generate_test_response("Test request")
            
            # Should use fallback path
            assert response == "Fallback response"
            mock_fallback.assert_called_once_with("Test request")
            
    @pytest.mark.asyncio  
    async def test_generate_test_response_llm_none_check(self):
        """Test _generate_test_response when llm is None"""
        bot = MyBot_Testing()
        
        # Create a mock settings object with llm set to None
        mock_settings = MagicMock()
        mock_settings.llm = None
        
        with patch('etc.ZairaSettings.ZairaSettings', mock_settings), \
             patch.object(bot, '_get_fallback_response', return_value="Fallback response") as mock_fallback:
            
            response = await bot._generate_test_response("Test request")
            
            # Should use fallback path
            assert response == "Fallback response"
            mock_fallback.assert_called_once_with("Test request")
            
    @pytest.mark.asyncio
    async def test_request_human_in_the_loop_super_call(self):
        """Test request_human_in_the_loop_internal calls super() for non-Testing names"""
        bot = MyBot_Testing(name="OtherBot")
        
        with patch('endpoints.mybot_generic.MyBot_Generic.request_human_in_the_loop_internal') as mock_super:
            mock_super.return_value = AsyncMock()
            
            await bot.request_human_in_the_loop_internal(
                "Test request", self.mock_task, MagicMock(), halt_until_response=True
            )
            
            # Should call super implementation
            mock_super.assert_called_once_with(
                "Test request", self.mock_task, MagicMock(), True
            )
            
    @pytest.mark.asyncio
    async def test_request_human_in_the_loop_testing_name_full_flow(self):
        """Test complete flow for Testing name with all print statements"""
        bot = MyBot_Testing(name="Testing")
        
        with patch.object(bot, '_generate_test_response', return_value="Generated response") as mock_generate, \
             patch('builtins.print') as mock_print:
            
            await bot.request_human_in_the_loop_internal(
                "Test request", self.mock_task, MagicMock()
            )
            
            # Verify all operations were called
            mock_generate.assert_called_once_with("Test request")
            self.mock_task.user.on_message.assert_called_once_with(
                "Generated response", bot, [], self.mock_task.original_physical_message
            )
            
            # Verify all print statements were called (lines 23-28)
            print_calls = [call[0][0] for call in mock_print.call_args_list]
            expected_prints = [
                "[TEST BOT] Request:",
                "[TEST BOT] Response:",
                "[TEST BOT] Task:",
                "[TEST BOT] Sending response",
                "[TEST BOT] Response sent"
            ]
            
            for expected in expected_prints:
                assert any(expected in call for call in print_calls), f"Missing print: {expected}"