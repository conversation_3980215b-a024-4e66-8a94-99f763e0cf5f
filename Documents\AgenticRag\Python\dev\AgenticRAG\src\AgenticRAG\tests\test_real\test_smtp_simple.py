#!/usr/bin/env python3
"""
Simple SMTP Test for Email System
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

from imports import *
from managers.manager_logfire import LogFire
import pytest
import asyncio
import time
from uuid import uuid4
from pathlib import Path
import tempfile
import shutil
import os

from managers.manager_supervisors import SupervisorManager
from managers.manager_users import ZairaUserManager
from managers.manager_prompts import PromptManager
from userprofiles.ZairaUser import ZairaUser, PERMISSION_LEVELS
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
from etc.setup import init
from tasks.task_top_level_supervisor import create_top_level_supervisor
from endpoints.testing_endpoint import MyBot_Testing

@pytest.mark.asyncio
class TestSMTPSimple:
    """Simple SMTP test class"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_name = self.__class__.__name__
        self.test_user_guid = str(uuid4())
        self.test_session_guid = str(uuid4())
        self.test_scheduled_guid = str(uuid4())
        
        # Test environment
        self.temp_data_dir = None
        self.temp_persist_dir = None
        self.initialized = False
        self.cleanup_requests = []
        
        # Test resources
        self.test_users = []
        self.test_bots = []
        
        # Initialize Globals for testing
        try:
            Globals.Debug = False
            if not hasattr(ZairaSettings, 'IsDebugMode'):
                ZairaSettings.IsDebugMode = False
        except:
            pass
    
    def teardown_method(self):
        """Clean up after each test method"""
        LogFire.log("DEBUG", f"[{self.test_name}] Cleaning up test method")
        
        # Cancel any remaining asyncio tasks
        try:
            loop = asyncio.get_running_loop()
            tasks = [task for task in asyncio.all_requests(loop) if not task.done()]
            for task in tasks:
                if task != asyncio.current_task():
                    task.cancel()
        except RuntimeError:
            pass
        
        # Clean up temporary directories
        if self.temp_data_dir and Path(self.temp_data_dir).exists():
            shutil.rmtree(self.temp_data_dir, ignore_errors=True)
        if self.temp_persist_dir and Path(self.temp_persist_dir).exists():
            shutil.rmtree(self.temp_persist_dir, ignore_errors=True)
    
    async def setup_real_system(self) -> bool:
        """Set up the REAL system for email testing"""
        if self.initialized:
            return True
            
        try:
            LogFire.log("DEBUG", f"[{self.test_name}] Setting up REAL system for email testing")
            
            # Import parsers for document processing
            import etc.parsers as parsers
            
            # Set up temporary directories
            self.temp_data_dir = tempfile.mkdtemp(prefix="test_email_data_")
            self.temp_persist_dir = tempfile.mkdtemp(prefix="test_email_persist_")
            
            DATA_DIR = Path(self.temp_data_dir)
            PERSIST_DIR = Path(self.temp_persist_dir)
            
            # Create test documents
            test_docs = [
                ("email_templates.txt", "Professional email templates for business communication"),
                ("smtp_config.txt", "SMTP server configuration and settings"),
                ("email_policies.txt", "Email sending policies and guidelines"),
                ("signature_templates.txt", "Email signature templates")
            ]
            
            for filename, content in test_docs:
                doc_path = DATA_DIR / filename
                doc_path.write_text(content, encoding='utf-8')
                
            LogFire.log("DEBUG", f"[{self.test_name}] Created {len(test_docs)} test documents")
            
            # Initialize the complete system
            LogFire.log("DEBUG", f"[{self.test_name}] Initializing complete AskZaira system...")
            await init(newProject=True, DATA_DIR=DATA_DIR, PERSIST_DIR=PERSIST_DIR, parsers=parsers)
            
            # Verify critical managers are initialized
            await SupervisorManager.setup()
            await PromptManager.setDefaultPrompts()
            
            # Initialize OAuth verifier (critical for SMTP configuration)
            from endpoints.oauth._verifier_ import OAuth2Verifier
            await OAuth2Verifier.setup()
            
            # Create top-level supervisor
            top_supervisor = await create_top_level_supervisor()
            if not top_supervisor:
                raise RuntimeError("Failed to create top-level supervisor")
            
            # Create email supervisors
            try:
                from tasks.processing.task_email_generator import create_task_email_generator
                from tasks.outputs.output_requests.task_out_email import create_supervisor_email_sender
                
                await create_task_email_generator()
                await create_supervisor_email_sender()
                LogFire.log("DEBUG", f"[{self.test_name}] Email supervisors created successfully")
            except Exception as e:
                LogFire.log("DEBUG", f"[{self.test_name}] Warning: Could not create email supervisors: {e}")
            
            # Verify LLM is available
            if not hasattr(ZairaSettings, 'llm') or ZairaSettings.llm is None:
                LogFire.log("DEBUG", f"[{self.test_name}] Warning: No LLM configured - this may affect test results")
            
            self.initialized = True
            LogFire.log("DEBUG", f"[{self.test_name}] Real system setup completed successfully")
            return True
            
        except Exception as e:
            LogFire.log("DEBUG", f"[{self.test_name}] Real system setup failed: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    async def create_test_user(self, email: str = "<EMAIL>") -> ZairaUser:
        """Create a test user"""
        user_manager = ZairaUserManager.get_instance()
        user = await user_manager.add_user(
            f"test_user_{len(self.test_users) + 1}",
            PERMISSION_LEVELS.ADMIN,
            uuid4(),
            uuid4()
        )
        
        user.email = email
        self.test_users.append(user)
        
        LogFire.log("DEBUG", f"[{self.test_name}] Created test user with email: {email}")
        return user
    
    def create_test_bot(self, name: str = "Testing") -> MyBot_Testing:
        """Create a test bot"""
        bot = MyBot_Testing(None, name)
        self.test_bots.append(bot)
        
        LogFire.log("DEBUG", f"[{self.test_name}] Created test bot: {name}")
        return bot
    
    async def execute_and_monitor_task(self, user: ZairaUser, query: str, bot: MyBot_Testing, timeout: int = 30) -> dict:
        """Execute a task and monitor its progress"""
        LogFire.log("DEBUG", f"[{self.test_name}] Executing task: '{query}'")
        LogFire.log("DEBUG", f"[{self.test_name}] Bot will handle approval prompts automatically")
        
        start_time = time.time()
        
        # Start the request
        request = await user.on_message(
            complete_message=query,
            calling_bot=bot,
            attachments=[],
            original_message=None
        )
        
        if not request:
            return {
                "success": False,
                "error": "Failed to create request",
                "execution_time": time.time() - start_time
            }
        
        LogFire.log("DEBUG", f"[{self.test_name}] Request created: {request.scheduled_guid}")
        
        # Monitor request progression
        for i in range(timeout):
            await asyncio.sleep(1)
            
            active_request_count = len(user.my_requests)
            if i % 5 == 0:  # Log every 5 seconds
                LogFire.log("DEBUG", f"[{self.test_name}] Second {i+1}: {active_request_count} active requests")
            
            # Check for human-in-the-loop requests
            hitl_request = user.get_active_hitl_request()
            if hitl_request:
                LogFire.log("DEBUG", f"[{self.test_name}] Found HITL request: {hitl_request.scheduled_guid}")
                
                if hasattr(hitl_request, 'human_in_the_loop_callback'):
                    callback_exists = hitl_request.human_in_the_loop_callback is not None
                    LogFire.log("DEBUG", f"[{self.test_name}] HITL callback exists: {callback_exists}")
            
            # Check if all tasks are completed
            if not user.has_active_requests():
                LogFire.log("DEBUG", f"[{self.test_name}] All requests completed after {i+1} seconds")
                break
        
        execution_time = time.time() - start_time
        
        # Analyze results
        result = {
            "success": True,
            "execution_time": execution_time,
            "initial_request_guid": request.scheduled_guid,
            "final_request_count": len(user.my_requests),
            "completed_in_time": execution_time < timeout
        }
        
        # Get request details
        request_details = []
        for request_guid, request_obj in user.my_requests.items():
            details = {
                "guid": request_guid,
                "type": type(request_obj).__name__
            }
            
            if hasattr(request_obj, 'output_demands'):
                details["output_demands"] = request_obj.output_demands
            
            if hasattr(request_obj, 'call_trace'):
                details["call_trace"] = request_obj.call_trace
            
            if hasattr(request_obj, 'state') and hasattr(request_obj.state, 'sections'):
                details["state_sections"] = list(request_obj.state.sections.keys())
            
            request_details.append(details)
        
        result["request_details"] = request_details
        
        LogFire.log("DEBUG", f"[{self.test_name}] Request execution completed in {execution_time:.2f} seconds")
        return result
    
    async def test_smtp_email_sending_full_pipeline(self):
        """Test complete SMTP email sending with automated bot approval"""
        # Skip if no OpenAI API key
        api_key_configured = os.environ.get('OPENAI_API_KEY')
        if not api_key_configured:
            pytest.skip("OpenAI API key not configured - set OPENAI_API_KEY")
        
        # Setup
        setup_success = await self.setup_real_system()
        if not setup_success:
            pytest.skip("Could not initialize email system")
        
        # Check if SMTP configuration is available in the database
        from endpoints.oauth._verifier_ import OAuth2Verifier
        smtp_token = await OAuth2Verifier.get_full_token("smtp")
        gmail_token = await OAuth2Verifier.get_full_token("gmail")
        
        if not smtp_token and not gmail_token:
            pytest.skip("No SMTP or Gmail configuration found in database - configure via OAuth setup")
        
        LogFire.log("DEBUG", f"[{self.test_name}] SMTP configuration found: {'SMTP' if smtp_token else 'Gmail'}")
        
        user = await self.create_test_user(email="<EMAIL>")
        bot = self.create_test_bot()
        
        # Test full email sending pipeline
        query = "send a test <NAME_EMAIL>"
        result = await self.execute_and_monitor_task(user, query, bot, timeout=60)
        
        # Verify successful execution
        assert result["success"], f"Task execution failed: {result.get('error', 'Unknown error')}"
        assert result["execution_time"] <= 60, f"Task took {result['execution_time']:.2f}s, expected <= 60s"
        
        # Check for email sending success indicators
        email_sent_indicators = []
        
        for task_detail in result["task_details"]:
            # Check call trace for email sending
            call_trace = task_detail.get("call_trace", [])
            call_trace_str = " ".join(str(call).lower() for call in call_trace)
            
            if "email_sender" in call_trace_str:
                email_sent_indicators.append("email_sender_called")
            
            # Check state sections for email completion
            state_sections = task_detail.get("state_sections", [])
            if "email_sent" in state_sections:
                email_sent_indicators.append("email_sent_state")
        
        # Verify email sending occurred
        assert len(email_sent_indicators) > 0, f"Email sending not detected. Found: {email_sent_indicators}"
        
        LogFire.log("DEBUG", f"[{self.test_name}] SMTP email sending test passed")
        LogFire.log("DEBUG", f"[{self.test_name}] Email sending indicators: {email_sent_indicators}")
        
        return {
            "success": True,
            "execution_time": result["execution_time"],
            "email_sent_indicators": email_sent_indicators,
            "final_task_count": result["final_task_count"]
        }


if __name__ == "__main__":
    # Run tests individually for debugging
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "debug":
        # Debug mode - run a single test
        test_instance = TestSMTPSimple()
        test_instance.setup_method()
        
        asyncio.run(test_instance.test_smtp_email_sending_full_pipeline())
        LogFire.log("DEBUG", "Debug test completed")
    else:
        # Normal pytest execution
        pytest.main([__file__, "-v"], severity="debug")