from imports import *

import logging
from os import getcwd
from os import path as os_path
from os import makedirs as os_makedirs
from asyncio import sleep, Task
import asyncio
from ssl import create_default_context, Purpose
from aiohttp import web
from agno.agent import Agent, RunResponse
from agno.models.ollama import Ollama
from agno.knowledge.pdf_url import PDFUrlKnowledgeBase
from agno.vectordb.qdrant import Qdrant
from agno.storage.agent.postgres import PostgresAgentStorage
from agno.embedder.fastembed import FastEmbedEmbedder
from botbuilder.core.integration import aiohttp_error_middleware

# Session management imports
import base64
import json

# Global server registry for cleanup during test cancellation
_RUNNING_SERVERS = []
_SERVER_REGISTRY_LOCK = asyncio.Lock()
from datetime import datetime, timedelta
from cryptography.fernet import Fernet

from etc.helper_functions import *
from managers.manager_retrieval import RetrievalManager
from managers.manager_meltano import MeltanoManager
from endpoints.oauth._verifier_ import OAuth2Verifier
from managers.manager_logfire import LogFire
from managers.manager_users import ZairaUserManager
from endpoints.mybot_generic import MyBot_Generic

@web.middleware
async def logfire_middleware(request, handler):
    response = await LogFire.logfire_middleware(request, handler)
    return response

@web.middleware
async def ip_check_middleware(request, handler):
    request['real_ip'] = (
        request.headers.get('CF-Connecting-IP') or
        request.headers.get('X-Forwarded-For', request.remote)
    ).split(',')[0].strip()
    response = await handler(request)
    return response

# Simple session management (no external dependencies)
SESSION_KEY = base64.urlsafe_b64encode(b'zaira-dashboard-session-key-2024').decode()
SESSION_COOKIE_NAME = 'zaira_session'
SESSION_TIMEOUT_HOURS = 24

@web.middleware
async def session_middleware(request, handler):
    """Simple session middleware for authentication state"""
    # Initialize empty session
    request['session'] = {}
    
    # Load existing session from cookie
    session_data = request.cookies.get(SESSION_COOKIE_NAME)
    if session_data:
        try:
            # Simple base64 decode for session data
            decoded_data = base64.urlsafe_b64decode(session_data.encode()).decode()
            session_info = json.loads(decoded_data)
            
            # Check session timeout
            login_time = datetime.fromisoformat(session_info.get('login_time', ''))
            if datetime.now() - login_time < timedelta(hours=SESSION_TIMEOUT_HOURS):
                request['session'] = session_info
            else:
                LogFire.log("USER", "Session expired for user", severity="info")
        except Exception as e:
            LogFire.log("ERROR", f"Session decode error: {e}", severity="warning")
    
    # Execute the handler
    response = await handler(request)
    
    # Save session to cookie if modified
    if request.get('session_modified', False):
        session_data = base64.urlsafe_b64encode(
            json.dumps(request['session']).encode()
        ).decode()
        response.set_cookie(
            SESSION_COOKIE_NAME,
            session_data,
            max_age=SESSION_TIMEOUT_HOURS * 3600,
            httponly=True,
            secure=False,  # Set to True in production with HTTPS
            samesite='Lax'
        )
    
    return response

class APIEndpoint:
    logging.basicConfig(level=logging.INFO)
    logger = logging.getLogger(__name__)

    _instance = None
    aio_app: web.Application = None
    bot_generic: MyBot_Generic = None
    
    restart_Task: Optional[Task] = None # Debug-only

    def __new__(cls, *args, **kwargs):
        if not cls._instance:
            cls._instance = super(APIEndpoint, cls).__new__(cls, *args, **kwargs)
        return cls._instance

    def __init__(self):
        if not hasattr(self, 'initialized'):
            self.initialized = True
            self.agent = None

    @classmethod
    def get_instance(cls):
        return cls._instance or cls()

    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        instance.aio_app = web.Application(middlewares=[aiohttp_error_middleware, logfire_middleware, ip_check_middleware, session_middleware])

        instance.aio_app.add_routes([
            web.get('/', instance.home),
            web.get('/favicon.ico', instance.favicon),
            web.post('/login', instance.login),
            web.post('/validate-login', instance.validate_login),
            web.get('/connectors', instance.connectors),
            web.post('/slack/events', instance.slack_events),
            #web.get('/gmail_test', instance.GMAIL),
            web.get('/ask', instance.ask),
            web.get('/ask_url', instance.ask_url),
            web.get('/delayed/ask', instance.ask_delayed),
            web.get('/askAgno', instance.ask_agno),
            web.get('/managers/meltano/ConvertSQLToVectorStore', instance.convert_sql_to_vectorstore),
            web.post('/v1/embeddings', instance.embedding_openai),
            web.post('/onnx/v1/embeddings', instance.embedding_onnx),
            web.post('/file_upload', instance.handle_file_upload),
            web.post('/restart', instance.restart),
            web.post('/update', instance.update),
            web.post('/signup/create', instance.signup_create)
        ])
        
        # Setup dashboard routes with new LEGACY-restored functionality
        from endpoints.dashboard_endpoint import get_dashboard_endpoint
        
        # Create dashboard endpoint instance
        dashboard_endpoint = get_dashboard_endpoint()
        await dashboard_endpoint.setup()
        
        instance.bot_generic = MyBot_Generic(instance, "HTTP")

    async def start_app(self, app, host, port,ssl_context=None):
        global _RUNNING_SERVERS, _SERVER_REGISTRY_LOCK
        
        # CRITICAL: Ensure web server runs in same working directory as main app
        # This ensures debug_trace.log and other relative paths work correctly
        await self._ensure_correct_working_directory()
        
        runner = web.AppRunner(app)
        await runner.setup()
        site = web.TCPSite(runner, host, port,ssl_context=ssl_context)
        await site.start()
        
        # Store server references for cleanup during test cancellation
        async with _SERVER_REGISTRY_LOCK:
            server_info = {
                'runner': runner,
                'site': site,
                'host': host,
                'port': port,
                'app': app,
                'name': f'APIEndpoint_{host}_{port}',
                'created_at': datetime.now()
            }
            _RUNNING_SERVERS.append(server_info)
            LogFire.log("INIT", f"Server started at http://{host}:{port} (registered {len(_RUNNING_SERVERS)} total servers)")
        
        LogFire.log("INIT", f"Server started at http://{host}:{port}")

    async def _ensure_correct_working_directory(self):
        """Ensure web server uses same working directory as main app"""
        try:
            from os import getcwd, chdir
            from os.path import exists
            from pathlib import Path
            
            current_cwd = getcwd()
            LogFire.log("DEBUG", f"Web server current working directory: {current_cwd}")
            
            # Use the same logic as main.py to determine correct directory
            # Look for dev.env file to identify correct working directory
            dev_env_path = current_cwd + "/dev.env"
            dev_env_exists = exists(dev_env_path)
            
            LogFire.log("DEBUG", f"Web server checking for dev.env at: {dev_env_path}")
            LogFire.log("DEBUG", f"Web server dev.env exists: {dev_env_exists}")
            
            if not dev_env_exists:
                # Try to find the correct directory by looking for src/AgenticRAG/dev.env
                # This mimics the main app's behavior of finding the right working directory
                script_dir = Path(__file__).parent.parent.absolute()  # Go from endpoints/ to src/AgenticRAG/
                potential_dev_env = script_dir / "dev.env"
                
                if potential_dev_env.exists():
                    LogFire.log("INIT", f"Web server changing working directory from {current_cwd} to {script_dir}")
                    chdir(script_dir)
                    LogFire.log("INIT", f"Web server working directory is now: {getcwd()}")
                else:
                    LogFire.log("DEBUG", f"Web server cannot find dev.env, staying in: {current_cwd}")
            else:
                LogFire.log("DEBUG", f"Web server working directory is correct: {current_cwd}")
                
        except Exception as e:
            LogFire.log("ERROR", f"Failed to set web server working directory: {e}", severity="error")

    @staticmethod
    async def get_running_servers():
        """Get list of all running servers for cleanup purposes"""
        global _RUNNING_SERVERS, _SERVER_REGISTRY_LOCK
        async with _SERVER_REGISTRY_LOCK:
            return _RUNNING_SERVERS.copy()
    
    @staticmethod
    async def cleanup_all_servers():
        """Cleanup all registered servers - used by test framework"""
        global _RUNNING_SERVERS, _SERVER_REGISTRY_LOCK
        
        async with _SERVER_REGISTRY_LOCK:
            servers = _RUNNING_SERVERS.copy()
            _RUNNING_SERVERS.clear()
        
        if not servers:
            LogFire.log("DEBUG", "No servers to cleanup")
            return
        
        LogFire.log("DEBUG", f"Cleaning up {len(servers)} registered servers...")
        
        for server_info in servers:
            try:
                name = server_info.get('name', 'Unknown')
                port = server_info.get('port', 'Unknown')
                
                # Cleanup aiohttp server components
                if 'site' in server_info and server_info['site']:
                    await server_info['site'].stop()
                    LogFire.log("DEBUG", f"Stopped site for {name}")
                
                if 'runner' in server_info and server_info['runner']:
                    await server_info['runner'].cleanup()
                    LogFire.log("DEBUG", f"Cleaned up runner for {name}")
                    
            except Exception as e:
                LogFire.log("ERROR", f"Error cleaning up server {name}: {e}")
        
        LogFire.log("DEBUG", "Server cleanup completed")

    @classmethod
    async def late_setup(cls):
        # SSL is handled through IIS
        # if Globals.is_debug() == False and Globals.is_docker() == False:
        #     ssl_context = create_default_context(Purpose.CLIENT_AUTH)
        #     if Globals.is_docker(): # -.- always false
        #         ssl_cert = '/ssl/fullchain.pem'
        #         ssl_key = '/ssl/privkey.pem'
        #         ssl_context.load_cert_chain(ssl_cert, ssl_key)
        #     else:
        #         current_directory = getcwd()
        #         ssl_cert = f'{current_directory}/ssl/fullchain.pem'
        #         ssl_key = f'{current_directory}/ssl/privkey.pem'
        #         ssl_context.load_cert_chain(ssl_cert, ssl_key)
        #     await cls.get_instance().start_app(cls.get_instance().aio_app, host=IP_PYTHON, port=ZAIRA_PYTHON_PORT, ssl_context=ssl_context)
        #     #await web.run_app(cls.get_instance().aio_app, host=IP_PYTHON, port=PORT_PYTHON, ssl_context=ssl_context)
        # else:
        await cls.get_instance().start_app(cls.get_instance().aio_app, host="0.0.0.0", port=ZAIRA_PYTHON_PORT)
        #Thread(target=lambda: web.run_app(cls.get_instance().aio_app, host=["localhost", "0.0.0.0"], port=PORT_PYTHON),daemon=True).start()
        
        # routes = cls.get_instance().aio_app.router.routes()
        # port = get_value_from_env("ZAIRA_PYTHON_PORT", f"{ZAIRA_PYTHON_PORT}")
        # print(f"{int(port)} endpoint routes:")
        # for route in routes:
        #     print(f"{Globals.get_endpoint_address()}{str(route.resource.canonical)}")

    async def home(self, request: web.Request) -> web.Response:
        LogFire.log("EVENT", f"Endpoint home called from IP: {request['real_ip']}")
        self.logger.info("Homepage accessed")
        # <html><head><title>Home</title></head><body>Home</body></html>
        # https://www.askzaira.com/files/askzaira_reveal_720p.mp4
        html_content_without_url_change = """
                <!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8">
                    <meta name="viewport" content="width=device-width, initial-scale=1.0">
                    <title>Home</title>
                    <style>
                        body {
                            margin: 0;
                            padding: 0;
                            background: #000;
                            font-family: Arial, sans-serif;
                            overflow: hidden;
                        }
                        
                        .video-container {
                            position: relative;
                            width: 100vw;
                            height: 100vh;
                        }
                        
                        video {
                            width: 100%;
                            height: 100%;
                            object-fit: cover;
                        }
                        
                        video::-webkit-media-controls {
                            display: none !important;
                        }
                        
                        video::-webkit-media-controls-panel {
                            display: none !important;
                        }
                        
                        video::-webkit-media-controls-play-button {
                            display: none !important;
                        }
                        
                        video::-webkit-media-controls-start-playback-button {
                            display: none !important;
                        }
                        
                        .status {
                            position: absolute;
                            left: -9999px;
                            width: 1px;
                            height: 1px;
                            overflow: hidden;
                        }
                        
                        .progress-bar {
                            position: fixed;
                            bottom: 0;
                            left: 0;
                            right: 0;
                            width: 100%;
                            height: 6px;
                            background: rgba(0, 0, 0, 0.3);
                            z-index: 1000;
                        }
                        
                        .progress-fill {
                            height: 100%;
                            background: linear-gradient(90deg, #4CAF50, #2196F3);
                            width: 0%;
                            transition: width 0.1s ease;
                        }
                    </style>
                </head>
                <body>
                    <div class="video-container">
                        <video id="mainVideo" autoplay muted>
                            <source src="https://www.askzaira.com/files/askzaira_reveal_720p.mp4" type="video/mp4">
                            Your browser does not support the video tag.
                        </video>
                        
                        <div class="status" id="status">
                            Playing video...
                        </div>
                        
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress"></div>
                        </div>
                    </div>

                    <script>
                        const video = document.getElementById('mainVideo');
                        const statusElement = document.getElementById('status');
                        const progressElement = document.getElementById('progress');
                        
                        // Update progress bar based on video progress
                        function updateProgress() {
                            if (video.duration) {
                                const progress = (video.currentTime / video.duration) * 100;
                                progressElement.style.width = progress + '%';
                            }
                        }
                        
                        // Video event listeners
                        video.addEventListener('loadstart', function() {
                            statusElement.textContent = 'Loading video...';
                        });
                        
                        video.addEventListener('canplay', function() {
                            statusElement.textContent = 'Playing video...';
                            // Ensure video plays automatically
                            video.play().catch(function(error) {
                                console.log('Autoplay failed:', error);
                                statusElement.textContent = 'Click play to start video';
                            });
                        });
                        
                        video.addEventListener('timeupdate', function() {
                            updateProgress();
                        });
                        
                        video.addEventListener('ended', function() {
                            statusElement.textContent = 'Loading login page...';
                            progressElement.style.width = '100%';
                            
                            // Small delay to show the completion message
                            setTimeout(function() {
                                loadLoginPage();
                            }, 500);
                        });
                        
                        video.addEventListener('error', function() {
                            statusElement.textContent = 'Video error - loading login page...';
                            // Load login page after 2 seconds if video fails to load
                            setTimeout(function() {
                                loadLoginPage();
                            }, 2000);
                        });
                        
                        // Function to load login page content without changing URL
                        function loadLoginPage() {
                            fetch('/login', {
                                method: 'POST',
                                headers: {
                                    'Content-Type': 'application/json',
                                },
                                body: JSON.stringify({
                                    version: '1.0'
                                })
                            })
                                .then(response => response.text())
                                .then(html => {
                                    // Replace current page content with login page content
                                    document.open();
                                    document.write(html);
                                    document.close();
                                })
                                .catch(error => {
                                    console.error('Error loading login page:', error);
                                    // Fallback: create a simple login form
                                    document.body.innerHTML = `
                                        <div style="display: flex; justify-content: center; align-items: center; min-height: 100vh; background: #f5f5f5; font-family: Arial, sans-serif;">
                                            <div style="background: white; padding: 40px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); max-width: 400px; width: 100%;">
                                                <h2 style="text-align: center; margin-bottom: 30px; color: #333;">Login</h2>
                                                <form>
                                                    <div style="margin-bottom: 20px;">
                                                        <label style="display: block; margin-bottom: 5px; color: #555;">Username:</label>
                                                        <input type="text" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
                                                    </div>
                                                    <div style="margin-bottom: 20px;">
                                                        <label style="display: block; margin-bottom: 5px; color: #555;">Password:</label>
                                                        <input type="password" style="width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; box-sizing: border-box;">
                                                    </div>
                                                    <button type="submit" style="width: 100%; padding: 12px; background: #007bff; color: white; border: none; border-radius: 4px; font-size: 16px; cursor: pointer;">Login</button>
                                                </form>
                                            </div>
                                        </div>
                                    `;
                                });
                        }
                        
                        // Force play when page loads (backup)
                        window.addEventListener('load', function() {
                            if (video.paused) {
                                video.play().catch(function(error) {
                                    console.log('Autoplay failed on load:', error);
                                });
                            }
                        });
                    </script>
                </body>
                </html>
        """
        html_content = html_content_without_url_change
        return web.Response(text=html_content, content_type='text/html')
    
    async def favicon(self, request: web.Request) -> web.Response:
        """Serve the favicon.ico file"""
        return await Globals.favicon_endpoint()
    
    async def load_content_login(self, request: web.Request) -> str:
        return ""

    async def login(self, request: web.Request) -> web.Response:
        LogFire.log("EVENT", f"Endpoint login called from IP: {request['real_ip']}")
        
        try:
            data = await request.json()
            version = data.get("version", "")
            
            # Version validation - must exactly match "1.0"
            if version != "1.0":
                LogFire.log("ERROR", f"Invalid version provided: {version}", severity="error")
                return web.Response(text="Invalid version", status=400)
            
        except Exception as e:
            LogFire.log("ERROR", f"Error parsing login request: {e}", severity="error")
            return web.Response(text="Invalid request format", status=400)
        
        content = await self.load_content_login(request)
        site = etc.helper_functions.create_html_out("login", content)
        return web.Response(text=site, content_type='text/html')
    
    async def validate_login(self, request: web.Request) -> web.Response:
        """
        Validate login using ZairaUser system with username@domain format.
        Creates authenticated session on success.
        """
        try:
            data = await request.json()
            login_string = data.get("username", "")
            password = data.get("password", "")
            version = data.get("version", "").strip()
            
            # Version validation - must exactly match "1.0"
            LogFire.log("DEBUG", f"validate_login received version: '{version}' (repr: {repr(version)})", severity="debug")
            if version != "1.0":
                LogFire.log("ERROR", f"Invalid version provided to validate_login: '{version}' (expected '1.0')", severity="error")
                return web.json_response({'success': False, 'message': 'Invalid version'}, status=400)
            
            if not login_string or not password:
                return web.json_response({'success': False, 'message': 'Missing username or password'}, status=400)
            
            # Authenticate using ZairaUserManager
            from managers.manager_users import ZairaUserManager
            user_manager = ZairaUserManager.get_instance()
            
            # Debug logging for validation environment
            from etc.helper_functions import get_value_from_env
            current_network_name = get_value_from_env("ZAIRA_NETWORK_NAME", "unknown")
            LogFire.log("DEBUG", f"Validate Login Debug - Current ZAIRA_NETWORK_NAME: {current_network_name}", severity="debug")
            LogFire.log("DEBUG", f"Validate Login Debug - Login attempt: {login_string}", severity="debug")
            LogFire.log("DEBUG", f"Validate Login Debug - Password provided: {password}", severity="debug")
            
            LogFire.log("DEBUG", f"Starting authentication for: {login_string}", severity="debug")
            user = await user_manager.authenticate_user(login_string, password)
            
            if not user:
                LogFire.log("USER", f"Authentication failed for: {login_string}", severity="warning")
                return web.json_response({'success': False, 'message': 'Invalid credentials'}, status=401)
            
            # Debug: Log user properties before session creation
            LogFire.log("DEBUG", f"User found - Username: {user.username}, GUID: {user.user_guid}, is_system_user: {user.is_system_user}, rank: {user.rank}", severity="debug")
            
            # Create session
            session_data = {
                'user_guid': str(user.user_guid),
                'username': user.username,
                'is_system_user': user.is_system_user,
                'rank': user.rank.name,
                'login_time': datetime.now().isoformat(),
                'authenticated': True
            }
            request['session'] = session_data
            request['session_modified'] = True
            
            # Debug: Log session data
            LogFire.log("DEBUG", f"Session created: {session_data}", severity="debug")
            LogFire.log("USER", f"User authenticated successfully: {user.username} (SYSTEM: {user.is_system_user})")
            
            return web.json_response({
                'success': True,
                'redirect': '/dashboard',
                'version': '1.0',
                'user': {
                    'username': user.username,
                    'is_system_user': user.is_system_user,
                    'rank': user.rank.name
                }
            })
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "validate_login", None)
            return web.json_response({'success': False, 'message': 'Authentication error'}, status=500)

    async def load_content_connectors(self, request: web.Request) -> str:
        ret_val = ""
        
        # Section mapping based on verifier.py line 55
        section_headers = {
            "comm": "Communicatie Platformen",
            "input": "Op aanvraag", 
            "auto": "Automatisering",
            "debug": "Debug"
        }
        
        # Order sections based on the Literal definition in verifier.py
        section_order = ["comm", "input", "auto", "debug"]
        
        # Group apps by section
        apps_by_section = {}
        for identifier, app in OAuth2Verifier.get_instance().apps.items():
            section = app.section
            if section == "":  # Skip empty sections
                continue
            if section not in apps_by_section:
                apps_by_section[section] = []
            apps_by_section[section].append(identifier)
        
        # Generate HTML for each section
        for section in section_order[:3]:  # Process only comm, input, auto
            if section not in apps_by_section:
                continue
                
            # Add section header
            ret_val += f'<h3 class="section-title">{section_headers[section]}</h3>'
            
            # Add apps in this section
            for identifier in apps_by_section[section]:
                token = await OAuth2Verifier.get_token(identifier)
                url = f"{Globals.get_endpoint_address()}/{identifier}/oauth"
                site = f"""<div class="integration-item" onclick="connectService('{url}')"><div class="integration-icon {"teams" if not token else "pipeline"}-icon">{identifier[0:2].upper() if not token else "OK"}</div>"""
                ret_val += site + f"""<div class="integration-name">{identifier}</div></div>"""
        
        # Add user apps data to a script tag for JavaScript to use
        if "debug" in apps_by_section and Globals.is_debug_values():
            import json
            user_apps_data = []
            for identifier in apps_by_section["debug"]:
                token = await OAuth2Verifier.get_token(identifier)
                url = f"{Globals.get_endpoint_address()}/{identifier}/oauth"
                user_apps_data.append({
                    'identifier': identifier,
                    'token': token,
                    'url': url
                })
            
            ret_val += f"""<script>
                window.userApps = {json.dumps(user_apps_data)};
            </script>"""
        
        return ret_val

    async def connectors(self, request: web.Request) -> web.Response:
        """Connectors page - requires SYSTEM user access"""
        # Check authentication and SYSTEM user permission
        session = request.get('session', {})
        if not session.get('authenticated', False):
            return web.Response(text="Authentication required", status=401)
        if not session.get('is_system_user', False):
            return web.Response(text="Access denied - SYSTEM user required", status=403)
        # Handle both JSON and form data
        # if request.content_type == 'application/json':
        #     data = await request.json()
        # else:
        #     data = await request.post()
        # username = data.get("username")
        # if Globals.is_docker():
        #     if username != etc.helper_functions.get_value_from_env("ZAIRA_NETWORK_NAME"):
        #         return web.json_response({'success': False, 'message': 'Unauthorized'}, status=401)
        # else:
        #     if username != "proxyhttpaio":
        #         return web.json_response({'success': False, 'message': 'Unauthorized'}, status=401)
        # password = data.get("password")
        # md5pass = etc.helper_functions.get_password(username)
        # if password != md5pass:
        #     return web.json_response({'success': False, 'message': 'Unauthorized'}, status=401)

        LogFire.log("EVENT", f"Endpoint connectors called from IP: {request['real_ip']}")
        content = await self.load_content_connectors(request)
        site = etc.helper_functions.create_html_out("connectors", content)

        return web.Response(text=site, content_type='text/html')

    # Route to handle file upload
    async def handle_file_upload(self, request: web.Request):
        reader = await request.multipart()
        field = await reader.next()

        if field.name != 'files':
            return web.Response(text='Expected a "files" field', status=400)

        # Get the uploaded files from a single multi-part field
        filenames = field.filename
        if not filenames:
            return web.Response(text='No files uploaded.', status=400)

        if Globals.is_docker():
            path = '/meltano/output'
        else:
            from os import getcwd
            path = getcwd() + '/src/meltano/output'
        os_makedirs(path, exist_ok=True)

        files_uploaded = []

        # Handle multiple files in the same "files" field
        while field:
            filename = field.filename
            if filename:
                save_path = os_path.join(path, filename)
                with open(save_path, 'wb') as f:
                    while True:
                        chunk = await field.read_chunk()
                        if not chunk:
                            break
                        f.write(chunk)
                files_uploaded.append(filename)

            field = await reader.next()
            if not field or field.name != 'files':
                break

        if files_uploaded:
            try:
                LogFire.log("EVENT", f"Starting file processing for {len(files_uploaded)} files...")
                
                # Phase 1: Initialize and validate all required managers before processing
                LogFire.log("EVENT", "Step 1/4: Validating system dependencies...")
                initialization_success = await self._ensure_managers_initialized()
                if not initialization_success:
                    return web.Response(text="System initialization failed. Please contact administrator.", status=500)
                
                # Phase 2: Validate dependencies and files
                LogFire.log("EVENT", "Step 2/4: Validating files and dependencies...")
                validation_success = await self._validate_upload_dependencies(files_uploaded, path)
                if not validation_success:
                    return web.Response(text="File validation failed. Please check file types and system configuration.", status=400)
                
                # Phase 3: Process files with comprehensive error handling
                LogFire.log("EVENT", "Step 3/4: Converting files to vector store...")
                conversion_success = await self._process_files_to_vectorstore(path, files_uploaded)
                if not conversion_success:
                    return web.Response(text="File conversion to knowledge base failed. Files uploaded but not processed.", status=500)
                
                LogFire.log("EVENT", "Step 4/4: File processing completed successfully")
                uploaded_names = ','.join(files_uploaded)
                LogFire.log("EVENT", f"Successfully processed files: {uploaded_names}")
                return web.Response(text=f"Bestanden zijn succesvol verwerkt! {len(files_uploaded)} bestand(en) zijn opgeslagen in de kennisbank.")
                
            except Exception as e:
                from etc.helper_functions import exception_triggered
                exception_triggered(e, "handle_file_upload", None)
                return web.Response(text=f"Error processing files: {str(e)}", status=500)
        else:
            return web.Response(text='No valid files found.', status=400)

    async def _ensure_managers_initialized(self) -> bool:
        """Ensure all required managers are properly initialized before file processing"""
        try:
            LogFire.log("DEBUG", "Checking manager initialization status...", severity="debug")
            
            # Check if global Index is available
            try:
                index = Globals.get_index()
                if index is None:
                    LogFire.log("ERROR", "Global Index not initialized", severity="error")
                    return False
                LogFire.log("DEBUG", "Global Index: OK", severity="debug")
            except Exception as e:
                LogFire.log("ERROR", f"Global Index check failed: {e}", severity="error")
                return False
            
            # Initialize QDrant Manager
            try:
                from managers.manager_qdrant import QDrantManager
                await QDrantManager.setup()
                # Test QDrant connection
                client = QDrantManager.GetClient()
                if client is None:
                    LogFire.log("ERROR", "QDrant client not available", severity="error")
                    return False
                LogFire.log("DEBUG", "QDrant Manager: OK", severity="debug")
            except Exception as e:
                LogFire.log("ERROR", f"QDrant Manager initialization failed: {e}", severity="error")
                return False
            
            # Initialize Multimodal Manager
            try:
                from managers.manager_multimodal import MultimodalManager
                await MultimodalManager.setup()
                LogFire.log("DEBUG", "Multimodal Manager: OK", severity="debug")
            except Exception as e:
                LogFire.log("ERROR", f"Multimodal Manager initialization failed: {e}", severity="error")
                return False
            
            # Initialize Retrieval Manager
            try:
                from managers.manager_retrieval import RetrievalManager
                await RetrievalManager.setup()
                LogFire.log("DEBUG", "Retrieval Manager: OK", severity="debug")
            except Exception as e:
                LogFire.log("ERROR", f"Retrieval Manager initialization failed: {e}", severity="error")
                return False
                
            LogFire.log("EVENT", "All required managers initialized successfully")
            return True
            
        except Exception as e:
            LogFire.log("ERROR", f"Manager initialization check failed: {e}", severity="error")
            return False

    async def _validate_upload_dependencies(self, files_uploaded: list, upload_path: str) -> bool:
        """Validate file types, sizes, and system dependencies before processing"""
        try:
            LogFire.log("DEBUG", f"Validating {len(files_uploaded)} uploaded files...", severity="debug")
            
            # Check file types and sizes
            import mimetypes
            import os
            max_file_size = 50 * 1024 * 1024  # 50MB limit
            
            supported_types = {
                '.txt', '.md', '.pdf', '.doc', '.docx', '.xlsx', '.xls', 
                '.ppt', '.pptx', '.csv', '.json', '.xml', '.html', '.htm'
            }
            
            for filename in files_uploaded:
                file_path = os_path.join(upload_path, filename)
                
                # Check if file exists
                if not os_path.exists(file_path):
                    LogFire.log("ERROR", f"Uploaded file not found: {filename}", severity="error")
                    return False
                
                # Check file size
                file_size = os_path.getsize(file_path)
                if file_size > max_file_size:
                    LogFire.log("ERROR", f"File too large: {filename} ({file_size} bytes)", severity="error")
                    return False
                
                # Check file type
                file_ext = os_path.splitext(filename)[1].lower()
                if file_ext not in supported_types:
                    LogFire.log("ERROR", f"Unsupported file type: {filename} ({file_ext})", severity="error")
                    return False
                    
                LogFire.log("DEBUG", f"File validation passed: {filename} ({file_size} bytes)", severity="debug")
            
            # Test embedding service availability
            try:
                from managers.manager_retrieval import RetrievalManager
                test_embedding = await RetrievalManager.get_embeddings_dense("test")
                if test_embedding is None or len(test_embedding) == 0:
                    LogFire.log("ERROR", "Embedding service not responding", severity="error")
                    return False
                LogFire.log("DEBUG", "Embedding service: OK", severity="debug")
            except Exception as e:
                LogFire.log("ERROR", f"Embedding service test failed: {e}", severity="error")
                return False
            
            LogFire.log("EVENT", "File and dependency validation completed successfully")
            return True
            
        except Exception as e:
            LogFire.log("ERROR", f"Upload validation failed: {e}", severity="error")
            return False

    async def _process_files_to_vectorstore(self, upload_path: str, files_uploaded: list) -> bool:
        """Process files to vector store with detailed error handling"""
        try:
            LogFire.log("DEBUG", f"Starting vector store conversion for {len(files_uploaded)} files", severity="debug")
            
            # Call MeltanoManager with comprehensive error handling
            await MeltanoManager.ConvertFilesToVectorStore(upload_path, None)
            
            # Verify that files were actually processed by checking if they were removed
            remaining_files = []
            for filename in files_uploaded:
                file_path = os_path.join(upload_path, filename)
                if os_path.exists(file_path):
                    remaining_files.append(filename)
            
            if remaining_files:
                LogFire.log("ERROR", f"Files not processed (still exist): {remaining_files}", severity="error")
                # Clean up remaining files if they weren't processed
                for filename in remaining_files:
                    try:
                        file_path = os_path.join(upload_path, filename)
                        os.remove(file_path)
                        LogFire.log("DEBUG", f"Cleaned up unprocessed file: {filename}", severity="debug")
                    except Exception as cleanup_error:
                        LogFire.log("ERROR", f"Failed to clean up file {filename}: {cleanup_error}", severity="error")
                return False
            
            LogFire.log("EVENT", "Vector store conversion completed successfully")
            return True
            
        except Exception as e:
            LogFire.log("ERROR", f"Vector store conversion failed: {e}", severity="error")
            return False

    async def slack_events(self, request: web.Request) -> web.Response:
        data = await request.json()
        if data.get('type') == 'url_verification':
            return web.json_response({'challenge': data['challenge']})

        self.logger.info(f"Received Slack event: {data}")
        return web.json_response({"status": "ok"})

    async def ask(self, request: web.Request) -> web.Response:
        query = request.query.get("query", "")
        LogFire.log("EVENT", f"Endpoint ask called with: {query} from IP: {request['real_ip']}")
        query_engine = Globals.get_query_engine_default()
        result = query_engine.query(query)
        return web.json_response({"response": str(result)})

    async def ask_url(self, request: web.Request) -> web.Response:
        query = request.query.get("query", "")
        LogFire.log("EVENT", f"Endpoint ask_url called with: {query} from IP: {request['real_ip']}")
        query_engine = Globals.get_query_engine_default()
        result = query_engine.query(query)
        return web.Response(text=f"<html><head><title>Query Result</title></head><body>{result}</body></html>", content_type='text/html')

    async def ask_delayed(self, request) -> web.Response:
        query = request.query.get("query", "")
        user_guid = request.query.get("guid", "")
        if query != "" and user_guid != "":
            LogFire.log("EVENT", f"Endpoint ask_delayed called with: {query} from IP: {request['real_ip']}")
            user = await ZairaUserManager.find_user(user_guid)
            latest_task = await user.on_message(query, calling_bot=self.bot_generic, attachments=[], original_message=None)
            scheduled_guid = latest_task.scheduled_guid if latest_task else None
            return web.json_response({'message': 'Task started!', 'scheduled_guid': scheduled_guid}, status=202)
        return web.json_response({"error": {"message": "ask_delayed"}}, status=400)

    async def ask_agno(self, request: web.Request) -> web.Response:
        query = request.query.get("query", "")
        LogFire.log("EVENT", f"Endpoint ask_agno called with: {query} from IP: {request['real_ip']}")
        qdrant_url = f"http://localhost:{PORT_QDRANT}"
        collection_name = "thai-recipe-index"
        embedder = FastEmbedEmbedder(dimensions=EMBEDDING_SIZE)

        vector_db = Qdrant(
            collection=collection_name,
            url=qdrant_url,
            embedder=embedder,
        )
        host = "pgvector:5432" if Globals.is_docker() else f"pgvector:{PORT_POSTGRESQL}"
        db_url = f"postgresql+psycopg://ai:ai@{host}/ai"

        knowledge_base = PDFUrlKnowledgeBase(
            urls=["https://agno-public.s3.amazonaws.com/recipes/ThaiRecipes.pdf"],
            vector_db=vector_db,
        )
        knowledge_base.load(recreate=True)

        if not self.agent:
            self.agent = Agent(
                model=Ollama(id=AGENT_MODEL_OLLAMA),
                description="You are a Thai cuisine expert!",
                knowledge=knowledge_base,
                storage=PostgresAgentStorage(table_name="agent_sessions", db_url=db_url),
                markdown=True,
            )

        response: RunResponse = self.agent.run(query)
        if isinstance(response, etc.helper_functions.get_any_message_as_type()):
            response = response.content
        return web.json_response({"response": response})

    async def embedding_openai(self, request: web.Request) -> web.Response:
        try:
            data = await request.json()
            input_texts = data.get("input")
            if not isinstance(input_texts, (str, list)):
                raise ValueError("'input' must be a string or list of strings")
            model = data.get("model", "default-model")

            embeddings = await RetrievalManager.get_embeddings_dense(input_texts)

            response_data = {
                "object": "list",
                "data": [{"object": "embedding", "embedding": embedding, "index": idx} for idx, embedding in enumerate(embeddings)],
                "model": model,
                "usage": {"prompt_tokens": 0, "total_tokens": 0}
            }

            return web.json_response(response_data)

        except ValueError as ve:
            return web.json_response({"error": {"message": str(ve)}}, status=400)
        except Exception as e:
            self.logger.exception("Embedding generation failed")
            return web.json_response({"error": {"message": f"Internal server error: {str(e)}"}}, status=500)

    async def embedding_onnx(self, request: web.Request) -> web.Response:
        try:
            data = await request.json()
            input_texts = data['input']
            embeddings = await RetrievalManager.get_embeddings_dense(input_texts)

            response_data = {
                "data": [{"embedding": embedding.tolist(), "index": idx} for idx, embedding in enumerate(embeddings)]
            }
            return web.json_response(response_data)

        except KeyError:
            return web.json_response({"error": "Missing 'input' field"}, status=400)

    async def convert_sql_to_vectorstore(self, request: web.Request) -> web.Response:
        target = "target-postgres"
        if Globals.is_docker() == False:
            target += "-local"
        etc.helper_functions.call_network_docker("meltano", f"run tap-googleads {target}")
        await MeltanoManager.ConvertSQLToVectorStore(None)
        return web.Response(text="SQL -> Vector store conversion started.")

    async def restart(self, request: web.Request) -> web.Response:
        LogFire.log("TASK", "Restart endpoint called", chat=None)
        LogFire.log("TASK", f"Request headers: {dict(request.headers)}", chat=None)
        LogFire.log("TASK", f"Request method: {request.method}", chat=None)
        LogFire.log("TASK", f"Request path: {request.path}", chat=None)
        
        try:
            data = await request.json()
            LogFire.log("TASK", f"Request JSON data received: {data}", chat=None)
        except Exception as e:
            LogFire.log("TASK", f"Error parsing JSON: {str(e)}", severity="error")
            return web.json_response({'success': False, 'message': 'Invalid JSON'}, status=400)
        
        LogFire.log("TASK", f"Docker environment: {Globals.is_docker()}", chat=None)
        if Globals.is_docker():
            username = etc.helper_functions.get_value_from_env("ZAIRA_NETWORK_NAME")
            LogFire.log("TASK", f"Docker username from env: {username}", chat=None)
        else:
            username = "proxyhttpaio"
            LogFire.log("TASK", f"Non-docker username: {username}", chat=None)
        
        password = data.get("admin-userpass")
        LogFire.log("TASK", "Password received from request", chat=None)
        md5pass = etc.helper_functions.get_password(username)
        LogFire.log("TASK", "MD5 password retrieved for comparison", chat=None)
        
        if password != md5pass:
            LogFire.log("TASK", "Authentication failed - password mismatch", severity="error", chat=None)
            LogFire.log("TASK", f"Username attempted: {username}", chat=None)
            return web.json_response({'success': False, 'message': 'Unauthorized'}, status=401)
        
        LogFire.log("TASK", "Authentication successful", chat=None)
        
        if Globals.is_docker():
            LogFire.log("TASK", "Creating delayed exit task for Docker environment", chat=None)
            async def delayed_exit():
                from asyncio import sleep
                LogFire.log("TASK", "Delayed exit started - waiting 5 seconds", chat=None)
                await sleep(5)
                LogFire.log("TASK", "Executing exit() - system will restart", chat=None)
                exit()

            from asyncio import create_task
            self.restart_Task = create_task(delayed_exit())
            self.restart_Task.add_done_callback(etc.helper_functions.handle_asyncio_task_result_errors)
            LogFire.log("TASK", "Restart task created and scheduled", chat=None)
        else:
            LogFire.log("TASK", "Non-Docker environment - no restart task created", chat=None)
            
        LogFire.log("TASK", "Returning success response", chat=None)
        return web.json_response({'success': True})

    async def update(self, request: web.Request) -> web.Response:
        LogFire.log("TASK", "Update endpoint called", chat=None)
        LogFire.log("TASK", f"Request headers: {dict(request.headers)}", chat=None)
        LogFire.log("TASK", f"Request method: {request.method}", chat=None)
        LogFire.log("TASK", f"Request path: {request.path}", chat=None)
        LogFire.log("TASK", f"Request query: {dict(request.query)}", chat=None)
        
        try:
            data = await request.json()
            LogFire.log("TASK", f"Request JSON data received: {data}", chat=None)
            LogFire.log("TASK", f"Data keys present: {list(data.keys())}", chat=None)
        except Exception as e:
            LogFire.log("ERROR", f"Error parsing JSON: {str(e)}", severity="error")
            LogFire.log("TASK", f"Request content type: {request.content_type}", chat=None)
            return web.json_response({'success': False, 'message': 'Invalid JSON'}, status=400)
        
        LogFire.log("TASK", f"Docker environment: {Globals.is_docker()}", chat=None)
        if Globals.is_docker():
            username = etc.helper_functions.get_value_from_env("ZAIRA_NETWORK_NAME")
            LogFire.log("TASK", f"Docker username from env: {username}", chat=None)
        else:
            username = "proxyhttpaio"
            LogFire.log("TASK", f"Non-docker username: {username}", chat=None)
        
        password = data.get("admin-userpass")
        if password:
            LogFire.log("TASK", "Password received from request", chat=None)
        else:
            LogFire.log("ERROR", "No password provided in request", severity="error", chat=None)
            
        md5pass = etc.helper_functions.get_password(username)
        LogFire.log("TASK", "MD5 password retrieved for comparison", chat=None)
        
        if password != md5pass:
            LogFire.log("ERROR", "Authentication failed - password mismatch", severity="error", chat=None)
            LogFire.log("ERROR", f"Username attempted: {username}", chat=None)
            LogFire.log("ERROR", f"Password provided: {'Yes' if password else 'No'}", chat=None)
            return web.json_response({'success': False, 'message': 'Unauthorized'}, status=401)
        
        LogFire.log("TASK", "Authentication successful", chat=None)
        LogFire.log("TASK", f"Authenticated user: {username}", chat=None)
        
        if Globals.is_docker():
            async def delayed_exit():
                from asyncio import sleep
                from subprocess import run as subprocess_run
                from subprocess import CalledProcessError
                zaira_network_name = etc.helper_functions.get_value_from_env("ZAIRA_NETWORK_NAME").strip()
                # Define the path to your batch script
                # Assuming update_single_network.bat is in the same directory as your Python script
                script_dir = "/_DATA_RAW/Scripts/" # Get the directory of the current Python script
                batch_script = "update_client.sh"
                batch_script_path = os_path.join(script_dir, batch_script)

                # Check if the batch script exists
                if not os_path.exists(batch_script_path):
                    LogFire.log("ERROR", f"Error: Batch script not found at {batch_script_path}", severity="error")
                    LogFire.log("ERROR", "Please ensure 'update_client.bat' is in the same directory as this Python script.", severity="warning")

                LogFire.log("TOP", f"Attempting to update Docker network: {zaira_network_name}", "", chat=None)
                LogFire.log("EVENT", f"Executing batch script: {batch_script_path}")

                try:
                    # Use subprocess.run for a simpler way to run commands and capture output
                    # 'shell=True' is often needed for .bat files, but be cautious with untrusted input
                    # 'check=True' will raise CalledProcessError if the command returns a non-zero exit code
                    if batch_script_path.endswith('.bat'):
                        LogFire.log("TASK", f"Executing Windows command: cmd.exe /c {batch_script_path} {zaira_network_name}", chat=None)
                        result = subprocess_run(
                            ["cmd.exe", "/c", batch_script_path, zaira_network_name],
                            #shell=True,
                            check=True,
                            capture_output=True, # Capture stdout and stderr
                            text=True # Decode stdout and stderr as text
                        )

                        LogFire.log("TASK", f"Windows command executed - return code: {result.returncode}", chat=None)
                        LogFire.log("TASK", f"Result stdout type: {type(result.stdout)}, length: {len(result.stdout)}", chat=None)
                        LogFire.log("TASK", f"Result stderr type: {type(result.stderr)}, length: {len(result.stderr)}", chat=None)
                        LogFire.log("TOP", f"\n Update Script Output: {result.stdout}", chat=None)
                        LogFire.log("TOP", f"\n Update Script Errors: {result.stderr}", chat=None)
                    else:
                        host_data_path = "/run/desktop/mnt/host/c/CR_SVN/Projects/RAG2Riches"
                        LogFire.log("TOP", f"Rebooting with scripts from {host_data_path}", chat=None)
                            
                        # Make sure the script is executable
                        LogFire.log("TASK", "Setting script executable permissions", chat=None)
                        LogFire.log("TASK", f"chmod command: chmod +x {batch_script_path}", chat=None)
                        chmod_result = subprocess_run(["chmod", "+x", batch_script_path], check=False, capture_output=True, text=True)
                        LogFire.log("TASK", f"chmod result: return_code={chmod_result.returncode}", chat=None)
                        LogFire.log("TASK", f"chmod stdout: '{chmod_result.stdout}'", chat=None)
                        LogFire.log("TASK", f"chmod stderr: '{chmod_result.stderr}'", chat=None)
                        
                        # Create a detached Linux container that will execute the shell script
                        LogFire.log("TASK", "Preparing Docker command for detached container", chat=None)
                        docker_command = [
                            "docker", "run", "--rm", "-d",
                            "-e", f"NETWORK_NAME={zaira_network_name}",
                            "-v", "/var/run/docker.sock:/var/run/docker.sock",  # Docker socket access
                            "-v", f"{host_data_path}/_DATA_RAW:/_DATA_RAW",  # Mount the actual host directory
                            "-v", f"{host_data_path}/clients_env:/clients_env",  # Mount the actual host directory
                            "docker:latest",  # Official Docker image with Docker CLI
                            "sh", "-c", f"""
                                echo "Directory contents:"
                                ls -la /_DATA_RAW/Scripts/
                                echo "Script permissions:"
                                ls -la /_DATA_RAW/Scripts/update_client.sh
                                echo "Converting to Unix line endings:"
                                sed -i 's/\\r$//' /_DATA_RAW/Scripts/update_client.sh
                                echo "Making executable:"
                                chmod +x /_DATA_RAW/Scripts/update_client.sh
                                echo "Executing script with network: {zaira_network_name}"
                                sh /_DATA_RAW/Scripts/update_client.sh "{zaira_network_name}"
                            """
                        ]
                        
                        LogFire.log("TASK", f"Docker command prepared: {' '.join(docker_command[:7])}...", chat=None)
                        LogFire.log("TASK", f"Network name for update: {zaira_network_name}", chat=None)
                        LogFire.log("TASK", "Executing Docker container with update script", chat=None)
                        
                        result = subprocess_run(
                            docker_command,
                            check=True,
                            capture_output=True,
                            text=True
                        )
                        
                        LogFire.log("TASK", f"Docker command executed successfully", chat=None)
                        LogFire.log("TASK", f"Result type: {type(result)}", chat=None)
                        LogFire.log("TASK", f"Stdout length: {len(result.stdout)} chars", chat=None)
                        LogFire.log("TASK", f"Stderr length: {len(result.stderr)} chars", chat=None)
                        
                        container_id = result.stdout.strip()
                        LogFire.log("TOP", f"Temporary execution container started successfully: {container_id}", chat=None)
                        LogFire.log("TASK", f"Container ID extracted: {container_id}", chat=None)

                    if result.returncode == 0:
                        LogFire.log("TOP", f"Successfully updating network '{zaira_network_name}'.", chat=None)
                        LogFire.log("TASK", "Update script completed with success code 0", chat=None)
                    else:
                        LogFire.log("TOP", f"Batch script exited with an error (Code: {result.returncode}). See output above for details.", chat=None)
                        LogFire.log("TASK", f"Update script failed with return code: {result.returncode}", severity="error", chat=None)

                except CalledProcessError as e:
                    LogFire.log("TASK", f"CalledProcessError: Command failed with exit code {e.returncode}", severity="error", chat=None)
                    LogFire.log("TASK", f"Failed command: {e.cmd}", chat=None)
                    LogFire.log("TASK", f"Error stdout: {e.stdout}", chat=None)
                    LogFire.log("TASK", f"Error stderr: {e.stderr}", chat=None)
                    etc.helper_functions.exception_triggered(e, "Error running Update batch script:")
                    # print(f"\nError running Update batch script: {e}")
                    # print(f"Command: {e.cmd}")
                    # print(f"Return Code: {e.returncode}")
                    # print(f"Output (STDOUT): {e.stdout}")
                    # print(f"Errors (STDERR): {e.stderr}")
                except FileNotFoundError as e:
                    LogFire.log("TASK", f"FileNotFoundError: Script '{batch_script_path}' not found", severity="error", chat=None)
                    etc.helper_functions.exception_triggered(e, f"Error: The batch script '{batch_script_path}' was not found. Check the path.")
                    #print(f"Error: The batch script '{batch_script_path}' was not found. Check the path.")
                except Exception as e:
                    LogFire.log("TASK", f"Unexpected error occurred: {type(e).__name__}: {str(e)}", severity="error")
                    #etc.helper_functions.exception_triggered(e)
                    LogFire.log("ERROR", f"An unexpected error occurred: {e}", severity="error")
                finally:
                    LogFire.log("TASK", "Update process completed (with or without errors)", chat=None)
                #await sleep(5)
                #exit()

            # from asyncio import create_task
            # self.restart_Task = create_task(delayed_exit())
            # self.restart_Task.add_done_callback(etc.helper_functions.handle_asyncio_task_result_errors)
            LogFire.log("TASK", "Executing delayed_exit function", chat=None)
            await delayed_exit()
            LogFire.log("TASK", "Delayed exit completed - update process initiated", chat=None)
        else:
            LogFire.log("TASK", "Non-Docker environment - no update process needed", chat=None)
            
        LogFire.log("TASK", "Update endpoint completed successfully", chat=None)
        LogFire.log("TASK", "Returning success response", chat=None)
        return web.json_response({'success': True})

    async def notify(self, request: web.Request) -> web.Response:
        status = request.match_info.get("status", "")
        if not status:
            return web.Response(text="Invalid status provided", status=400)
        return web.Response(text=f"Notification status: {status}", content_type='text/plain')
    
    async def signup_create(self, request: web.Request) -> web.Response:
        pass
