from imports import *

from asyncio import run, get_event_loop
from main import mainFunc
from sys import gettrace
from endpoints.standalones.relay.relay_main import main as oauth_endpoint_external_main
import os

async def main():
    await LogFire.setup() # LogFire needs to be initialised before anything that might use it!
    LogFire.log("INIT", "dev_run.py main() started")
    
    ZairaSettings.IsDebugMode = True
    Globals.set_debug(ZairaSettings.IsDebugMode)
    
    LogFire.log("INIT", "About to check Claude environment")
    
    # Skip OAuth external endpoint if running in Claude environment
    from etc.helper_functions import is_claude_environment
    if ZairaSettings.IsDebugMode and not is_claude_environment():
        LogFire.log("INIT", "Starting OAuth external endpoint")
        await oauth_endpoint_external_main()
    elif is_claude_environment():
        LogFire.log("INIT", "Claude environment detected - skipping OAuth external endpoint")
        
    LogFire.log("INIT", "About to call mainFunc()")
    await mainFunc()
    LogFire.log("INIT", "mainFunc() completed")

    # Run multiple async tasks concurrently
    #task1 = asyncio.create_task(mainFunc(is_debug=True))

    # Wait for both tasks to complete
    #await task1

# Run the async program
if __name__ == "__main__":
    # if gettrace():
    #     # Debugger is attached, use event loop directly
    #     loop = get_event_loop()
    #     loop.run_until_complete(main())
    # else:
    run(main())
