from imports import *
from typing import Dict, Optional, List, Any
import asyncio
from datetime import datetime, timezone, timedelta

from .user_manager import UserScheduledRequestManager
from ..utils.config import ScheduledRequestsConfig
from ..utils.exceptions import UserManagerNotFoundError, ResourceExhaustionError
from ..utils.helpers import validate_guid
from userprofiles.ZairaUser import PERMISSION_LEVELS

class ScheduledRequestManagerFactory:
    """Factory for creating and managing user-specific scheduled request managers"""
    
    _instance: Optional['ScheduledRequestManagerFactory'] = None
    _initialized: bool = False
    _shutdown: bool = False
    
    def __init__(self):
        # Manager tracking
        self._user_managers: Dict[str, UserScheduledRequestManager] = {}
        self._manager_access_times: Dict[str, float] = {}
        
        # System-wide locks
        self._factory_lock = asyncio.Lock()
        self._cleanup_lock = asyncio.Lock()
        
        # Background tasks
        self._cleanup_task: Optional[asyncio.Task] = None
        self._health_monitor_task: Optional[asyncio.Task] = None
        
        # Metrics
        self._metrics = {
            'managers_created': 0,
            'managers_destroyed': 0,
            'total_requests_processed': 0,
            'peak_concurrent_managers': 0,
            'last_cleanup': None,
            'factory_started': datetime.now(timezone.utc).timestamp()
        }
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_instance(cls) -> 'ScheduledRequestManagerFactory':
        """Get singleton instance of the factory"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    async def setup(cls):
        """Initialize the factory"""
        instance = cls.get_instance()
        if instance._initialized or instance._shutdown:
            return
        
        async with instance._factory_lock:
            if instance._initialized:
                return
            
            try:
                # Start background tasks
                instance._cleanup_task = asyncio.create_task(instance._periodic_cleanup())
                instance._health_monitor_task = asyncio.create_task(instance._health_monitor())
                
                instance._initialized = True
                
            except Exception as e:
                LogFire.log("ERROR", f"Failed to initialize ScheduledRequestManagerFactory: {str(e)}")
                raise ResourceExhaustionError("factory_initialization", str(e))
    
    async def shutdown(self):
        """Gracefully shutdown the factory and all user managers"""
        if self._shutdown:
            return
        
        self._shutdown = True
        LogFire.log("INIT", "Shutting down ScheduledRequestManagerFactory")
        
        async with self._factory_lock:
            try:
                # Cancel background tasks
                if self._cleanup_task:
                    self._cleanup_task.cancel()
                    try:
                        await self._cleanup_task
                    except asyncio.CancelledError:
                        pass
                
                if self._health_monitor_task:
                    self._health_monitor_task.cancel()
                    try:
                        await self._health_monitor_task
                    except asyncio.CancelledError:
                        pass
                
                # Shutdown all user managers
                shutdown_tasks = []
                for user_guid, manager in list(self._user_managers.items()):
                    shutdown_tasks.append(manager.shutdown())
                
                if shutdown_tasks:
                    await asyncio.gather(*shutdown_tasks, return_exceptions=True)
                
                # Clear tracking
                self._user_managers.clear()
                self._manager_access_times.clear()
                
                LogFire.log("INIT", "ScheduledRequestManagerFactory shutdown complete")
                
            except Exception as e:
                LogFire.log("ERROR", f"Error during factory shutdown: {str(e)}")
    
    async def get_user_manager(self, user_guid: str) -> UserScheduledRequestManager:
        """
        Get or create a user-specific scheduled request manager
        
        Args:
            user_guid: GUID of the user
            
        Returns:
            UserScheduledRequestManager for the user
            
        Raises:
            UserManagerNotFoundError: If user is not found
            ResourceExhaustionError: If system resources are exhausted
        """
        if not validate_guid(user_guid):
            raise UserManagerNotFoundError(user_guid)
        
        if self._shutdown:
            raise ResourceExhaustionError("factory_shutdown", "Factory is shutdown")
        
        # Update access time
        self._manager_access_times[user_guid] = datetime.now(timezone.utc).timestamp()
        
        # Check if manager already exists
        if user_guid in self._user_managers:
            manager = self._user_managers[user_guid]
            if manager.is_initialized() and not manager.is_shutdown():
                return manager
            else:
                # Manager is in bad state, remove it
                await self._remove_manager(user_guid)
        
        # Create new manager
        return await self._create_manager(user_guid)
    
    async def _create_manager(self, user_guid: str) -> UserScheduledRequestManager:
        """Create a new user manager"""
        async with self._factory_lock:
            # Double-check pattern
            if user_guid in self._user_managers:
                manager = self._user_managers[user_guid]
                if manager.is_initialized() and not manager.is_shutdown():
                    return manager
            
            try:
                # Get user to determine quota configuration
                from managers.manager_users import ZairaUserManager
                user_manager = ZairaUserManager.get_instance()
                user = await user_manager.find_user(user_guid)
                
                if not user:
                    raise UserManagerNotFoundError(user_guid)
                
                # Get quota configuration based on user permission level
                quota_config = ScheduledRequestsConfig.get_user_quota_config(user.rank)
                
                # Create new manager
                manager = UserScheduledRequestManager(user_guid, quota_config)
                await manager.setup()
                
                # Track the manager
                self._user_managers[user_guid] = manager
                self._manager_access_times[user_guid] = datetime.now(timezone.utc).timestamp()
                
                # Update metrics
                self._metrics['managers_created'] += 1
                self._metrics['peak_concurrent_managers'] = max(
                    self._metrics['peak_concurrent_managers'], 
                    len(self._user_managers)
                )
                
                LogFire.log("INIT", f"Created user manager for {user_guid}")
                return manager
                
            except Exception as e:
                LogFire.log("ERROR", f"Failed to create manager for user {user_guid}: {str(e)}")
                raise ResourceExhaustionError("manager_creation", str(e))
    
    async def _remove_manager(self, user_guid: str):
        """Remove and cleanup a user manager"""
        async with self._cleanup_lock:
            if user_guid in self._user_managers:
                try:
                    manager = self._user_managers[user_guid]
                    await manager.shutdown()
                    
                    del self._user_managers[user_guid]
                    self._manager_access_times.pop(user_guid, None)
                    
                    self._metrics['managers_destroyed'] += 1
                    LogFire.log("INIT", f"Removed user manager for {user_guid}")
                    
                except Exception as e:
                    LogFire.log("ERROR", f"Error removing manager for {user_guid}: {str(e)}")
    
    async def remove_user_manager(self, user_guid: str) -> bool:
        """
        Manually remove a user manager (e.g., when user logs out)
        
        Args:
            user_guid: GUID of the user
            
        Returns:
            bool: True if removed successfully
        """
        if not validate_guid(user_guid):
            return False
        
        if user_guid in self._user_managers:
            await self._remove_manager(user_guid)
            return True
        
        return False
    
    async def get_all_user_managers(self) -> Dict[str, UserScheduledRequestManager]:
        """Get all active user managers"""
        return self._user_managers.copy()
    
    async def get_factory_metrics(self) -> Dict[str, Any]:
        """Get factory-wide metrics"""
        # Calculate total requests across all managers
        total_requests = 0
        total_active_tasks = 0
        
        for manager in self._user_managers.values():
            try:
                metrics = await manager.get_metrics()
                total_requests += metrics['metrics']['tasks_created']
                total_active_tasks += metrics['active_tasks']
            except Exception:
                pass  # Skip managers that can't provide metrics
        
        return {
            'factory_metrics': self._metrics.copy(),
            'active_managers': len(self._user_managers),
            'total_requests_all_users': total_requests,
            'total_active_tasks': total_active_tasks,
            'system_status': 'healthy' if not self._shutdown else 'shutdown',
            'uptime_seconds': datetime.now(timezone.utc).timestamp() - self._metrics['factory_started']
        }
    
    async def get_user_manager_metrics(self, user_guid: str) -> Optional[Dict[str, Any]]:
        """Get metrics for specific user manager"""
        if user_guid in self._user_managers:
            try:
                return await self._user_managers[user_guid].get_metrics()
            except Exception as e:
                LogFire.log("ERROR", f"Failed to get metrics for user {user_guid}: {str(e)}")
        
        return None
    
    async def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        healthy_managers = 0
        unhealthy_managers = 0
        manager_issues = []
        
        for user_guid, manager in self._user_managers.items():
            try:
                health = await manager.get_health_status()
                if health['status'] == 'healthy':
                    healthy_managers += 1
                else:
                    unhealthy_managers += 1
                    manager_issues.append({
                        'user_guid': user_guid,
                        'status': health['status'],
                        'issues': health.get('issues', [])
                    })
            except Exception as e:
                unhealthy_managers += 1
                manager_issues.append({
                    'user_guid': user_guid,
                    'status': 'error',
                    'issues': [f'Health check failed: {str(e)}']
                })
        
        overall_health = 'healthy' if unhealthy_managers == 0 else 'degraded' if unhealthy_managers < healthy_managers else 'unhealthy'
        
        return {
            'overall_status': overall_health,
            'healthy_managers': healthy_managers,
            'unhealthy_managers': unhealthy_managers,
            'total_managers': len(self._user_managers),
            'factory_initialized': self._initialized,
            'factory_shutdown': self._shutdown,
            'issues': manager_issues[:10]  # Limit to first 10 issues
        }
    
    async def _periodic_cleanup(self):
        """Periodic cleanup of inactive managers"""
        while not self._shutdown:
            try:
                await asyncio.sleep(ScheduledRequestsConfig.MANAGER_CLEANUP_INTERVAL_MINUTES * 60)
                await self._cleanup_inactive_managers()
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                LogFire.log("ERROR", f"Error in periodic cleanup: {str(e)}")
    
    async def _cleanup_inactive_managers(self):
        """Clean up managers that haven't been accessed recently"""
        current_time = datetime.now(timezone.utc).timestamp()
        timeout_seconds = ScheduledRequestsConfig.INACTIVE_MANAGER_TIMEOUT_MINUTES * 60
        
        inactive_managers = []
        
        for user_guid, last_access in self._manager_access_times.items():
            if current_time - last_access > timeout_seconds:
                # Check if manager has any active tasks
                if user_guid in self._user_managers:
                    manager = self._user_managers[user_guid]
                    try:
                        if manager.get_active_request_count() == 0:
                            inactive_managers.append(user_guid)
                    except Exception:
                        # If we can't check, assume it's inactive
                        inactive_managers.append(user_guid)
        
        # Remove inactive managers
        for user_guid in inactive_managers:
            await self._remove_manager(user_guid)
            LogFire.log("INIT", f"Cleaned up inactive manager for user {user_guid}")
        
        if inactive_managers:
            self._metrics['last_cleanup'] = datetime.now(timezone.utc).isoformat()
            LogFire.log("INIT", f"Cleaned up {len(inactive_managers)} inactive managers")
    
    async def _health_monitor(self):
        """Monitor system health and log issues"""
        while not self._shutdown:
            try:
                await asyncio.sleep(ScheduledRequestsConfig.HEALTH_CHECK_INTERVAL_SECONDS)
                
                health = await self.get_system_health()
                if health['overall_status'] != 'healthy':
                    LogFire.log("WARNING", f"System health: {health['overall_status']} - {health['unhealthy_managers']} unhealthy managers")
                
                # Log memory usage periodically (reduced frequency)
                factory_metrics = await self.get_factory_metrics()
                if factory_metrics['active_managers'] > 0:
                    # Only log every 5 health checks to reduce spam
                    if not hasattr(self, '_metrics_log_counter'):
                        self._metrics_log_counter = 0
                    self._metrics_log_counter += 1
                    
                    if self._metrics_log_counter >= 5:
                        LogFire.log("METRICS", f"Factory: {factory_metrics['active_managers']} managers, {factory_metrics['total_active_tasks']} active tasks")
                        self._metrics_log_counter = 0
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                LogFire.log("ERROR", f"Error in health monitor: {str(e)}")
    
    async def force_cleanup_all_managers(self):
        """Force cleanup of all managers (emergency use)"""
        LogFire.log("WARNING", "Force cleanup of all managers initiated")
        
        async with self._factory_lock:
            cleanup_tasks = []
            for user_guid in list(self._user_managers.keys()):
                cleanup_tasks.append(self._remove_manager(user_guid))
            
            if cleanup_tasks:
                await asyncio.gather(*cleanup_tasks, return_exceptions=True)
            
            LogFire.log("WARNING", "Force cleanup completed")
    
    def is_initialized(self) -> bool:
        """Check if factory is initialized"""
        return self._initialized and not self._shutdown
    
    def get_manager_count(self) -> int:
        """Get current number of active managers"""
        return len(self._user_managers)
    
    def has_user_manager(self, user_guid: str) -> bool:
        """Check if user manager exists"""
        return user_guid in self._user_managers