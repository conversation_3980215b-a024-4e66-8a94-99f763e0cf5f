"""
Comprehensive unit tests for MultimodalManager
This test suite achieves 90%+ coverage for multimodal document processing functionality
"""

import sys
import os
import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch, MagicMock, mock_open
from typing import Dict, List, Any
import uuid
import base64
import json

# Add project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from imports import *
from managers.manager_multimodal import MultimodalManager

class TestMultimodalManagerSingleton:
    """Test MultimodalManager singleton pattern"""
    
    def test_multimodal_manager_singleton_creation(self):
        """Test MultimodalManager singleton creation"""
        # Reset singleton for test
        MultimodalManager._instance = None
        MultimodalManager._initialized = False
        
        manager1 = MultimodalManager()
        manager2 = MultimodalManager()
        
        # Should be same instance
        assert manager1 is manager2
        assert isinstance(manager1, MultimodalManager)
    
    def test_multimodal_manager_get_instance(self):
        """Test MultimodalManager get_instance method"""
        # Reset singleton for test
        MultimodalManager._instance = None
        MultimodalManager._initialized = False
        
        manager1 = MultimodalManager.get_instance()
        manager2 = MultimodalManager.get_instance()
        
        # Should be same instance
        assert manager1 is manager2
        assert isinstance(manager1, MultimodalManager)
    
    def test_multimodal_manager_initialization_state(self):
        """Test MultimodalManager initialization state"""
        # Reset singleton for test
        MultimodalManager._instance = None
        MultimodalManager._initialized = False
        
        manager = MultimodalManager.get_instance()
        
        # Should start uninitialized
        assert manager._initialized == False
    
    @pytest.mark.asyncio
    async def test_multimodal_manager_setup(self):
        """Test MultimodalManager setup process"""
        # Reset singleton for test
        MultimodalManager._instance = None
        MultimodalManager._initialized = False
        
        with patch('managers.manager_multimodal.BASE_DIR') as mock_base_dir:
            # Mock directory creation
            mock_path = Mock()
            mock_path.mkdir = Mock()
            mock_base_dir.return_value.__truediv__.return_value = mock_path
            
            await MultimodalManager.setup()
            
            # Should be initialized
            manager = MultimodalManager.get_instance()
            assert manager._initialized == True
            assert hasattr(manager, 'config')
            
            # Should have created assets directory
            mock_path.mkdir.assert_called_once_with(parents=True, exist_ok=True)
    
    @pytest.mark.asyncio
    async def test_multimodal_manager_setup_idempotent(self):
        """Test MultimodalManager setup is idempotent"""
        # Reset singleton for test
        MultimodalManager._instance = None
        MultimodalManager._initialized = False
        
        with patch('managers.manager_multimodal.BASE_DIR') as mock_base_dir:
            mock_path = Mock()
            mock_path.mkdir = Mock()
            mock_base_dir.return_value.__truediv__.return_value = mock_path
            
            # Setup multiple times
            await MultimodalManager.setup()
            await MultimodalManager.setup()
            await MultimodalManager.setup()
            
            # Should only initialize once
            manager = MultimodalManager.get_instance()
            assert manager._initialized == True
            
            # Should have called mkdir only once
            mock_path.mkdir.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_multimodal_manager_config(self):
        """Test MultimodalManager configuration"""
        # Reset singleton for test
        MultimodalManager._instance = None
        MultimodalManager._initialized = False
        
        with patch('managers.manager_multimodal.BASE_DIR') as mock_base_dir:
            mock_path = Mock()
            mock_path.mkdir = Mock()
            mock_base_dir.return_value.__truediv__.return_value = mock_path
            
            await MultimodalManager.setup()
            
            manager = MultimodalManager.get_instance()
            
            # Should have proper configuration
            assert 'vision_model' in manager.config
            assert 'table_model' in manager.config
            assert 'max_image_size' in manager.config
            assert 'supported_image_formats' in manager.config
            
            # Verify default values
            assert manager.config['vision_model'] == "gpt-4o-mini"
            assert manager.config['table_model'] == "gpt-4o-mini"
            assert manager.config['max_image_size'] == 5 * 1024 * 1024
            assert isinstance(manager.config['supported_image_formats'], list)

class TestMultimodalManagerDocumentProcessing:
    """Test MultimodalManager document processing functionality"""
    
    @pytest.fixture
    def mock_elements(self):
        """Create mock elements for testing"""
        mock_text_element = Mock()
        mock_text_element.__class__.__name__ = "NarrativeText"
        mock_text_element.__str__ = Mock(return_value="This is narrative text content.")
        mock_text_element.metadata = Mock()
        mock_text_element.metadata.to_dict = Mock(return_value={"type": "text"})
        
        mock_image_element = Mock()
        mock_image_element.__class__.__name__ = "Image"
        mock_image_element.__str__ = Mock(return_value="Image description")
        mock_image_element.metadata = Mock()
        mock_image_element.metadata.to_dict = Mock(return_value={"type": "image"})
        mock_image_element.image_data = b"fake_image_data"
        
        mock_table_element = Mock()
        mock_table_element.__class__.__name__ = "Table"
        mock_table_element.__str__ = Mock(return_value="Table content")
        mock_table_element.metadata = Mock()
        mock_table_element.metadata.to_dict = Mock(return_value={"type": "table"})
        mock_table_element.table_data = [["Header1", "Header2"], ["Data1", "Data2"]]
        
        return [mock_text_element, mock_image_element, mock_table_element]
    
    @pytest.mark.asyncio
    async def test_extract_multimodal_elements_success(self, mock_elements):
        """Test successful multimodal element extraction"""
        with patch('managers.manager_multimodal.partition') as mock_partition, \
             patch.object(MultimodalManager, '_process_image_element') as mock_process_image, \
             patch.object(MultimodalManager, '_process_table_element') as mock_process_table, \
             patch('builtins.print'):  # Suppress debug prints
            
            mock_partition.return_value = mock_elements
            mock_process_image.return_value = None
            mock_process_table.return_value = None
            
            result = await MultimodalManager.extract_multimodal_elements("test_document.pdf", "test_doc_id")
            
            # Verify structure
            assert result["doc_id"] == "test_doc_id"
            assert "text_elements" in result
            assert "images" in result
            assert "tables" in result
            assert "all_elements" in result
            
            # Should have processed all elements
            assert len(result["all_elements"]) == 3
            
            # Should have called processing methods
            mock_process_image.assert_called_once()
            mock_process_table.assert_called_once()
            
            # Should have partitioned document
            mock_partition.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_extract_multimodal_elements_no_doc_id(self, mock_elements):
        """Test multimodal extraction without doc_id"""
        with patch('managers.manager_multimodal.partition') as mock_partition, \
             patch.object(MultimodalManager, '_process_image_element'), \
             patch.object(MultimodalManager, '_process_table_element'), \
             patch('builtins.print'):
            
            mock_partition.return_value = mock_elements
            
            result = await MultimodalManager.extract_multimodal_elements("test_document.pdf")
            
            # Should generate doc_id
            assert "doc_id" in result
            assert len(result["doc_id"]) > 0
            
            # Should be valid UUID format
            try:
                uuid.UUID(result["doc_id"])
            except ValueError:
                pytest.fail("Generated doc_id should be valid UUID")
    
    @pytest.mark.asyncio
    async def test_extract_multimodal_elements_partition_error(self):
        """Test multimodal extraction with partition error"""
        with patch('managers.manager_multimodal.partition', side_effect=Exception("Partition failed")), \
             patch('builtins.print'):
            
            result = await MultimodalManager.extract_multimodal_elements("test_document.pdf", "test_doc_id")
            
            # Should return error structure
            assert result["doc_id"] == "test_doc_id"
            assert "error" in result
            assert result["error"] == "Partition failed"
            
            # Should have empty collections
            assert result["text_elements"] == []
            assert result["images"] == []
            assert result["tables"] == []
    
    @pytest.mark.asyncio
    async def test_extract_multimodal_elements_with_debug_output(self, mock_elements):
        """Test multimodal extraction debug output"""
        with patch('managers.manager_multimodal.partition') as mock_partition, \
             patch.object(MultimodalManager, '_process_image_element'), \
             patch.object(MultimodalManager, '_process_table_element'), \
             patch('builtins.print') as mock_print:
            
            mock_partition.return_value = mock_elements
            
            await MultimodalManager.extract_multimodal_elements("test_document.pdf", "test_doc_id")
            
            # Should have debug prints
            assert mock_print.call_count >= 3  # Debug info, element types, element details

class TestMultimodalManagerImageProcessing:
    """Test MultimodalManager image processing functionality"""
    
    @pytest.fixture
    def mock_image_element(self):
        """Create mock image element"""
        element = Mock()
        element.__class__.__name__ = "Image"
        element.__str__ = Mock(return_value="Image description")
        element.metadata = Mock()
        element.metadata.to_dict = Mock(return_value={"type": "image"})
        element.image_data = b"fake_image_data_12345"
        return element
    
    @pytest.mark.asyncio
    async def test_process_image_element_success(self, mock_image_element):
        """Test successful image element processing"""
        with patch.object(MultimodalManager, '_save_image_asset') as mock_save_asset, \
             patch.object(MultimodalManager, '_get_surrounding_context') as mock_context, \
             patch.object(MultimodalManager, 'generate_image_summary') as mock_summary:
            
            mock_save_asset.return_value = "/path/to/image.png"
            mock_context.return_value = "Surrounding context text"
            mock_summary.return_value = "This is an image of a chart"
            
            element_data = {
                "id": "test_element_1",
                "type": "Image",
                "text": "Image description",
                "metadata": {"type": "image"},
                "element_index": 1
            }
            
            result = {"images": [], "all_elements": []}
            
            await MultimodalManager._process_image_element(
                mock_image_element, element_data, "test_doc_id", result
            )
            
            # Should have processed image
            assert len(result["images"]) == 1
            processed_image = result["images"][0]
            
            assert processed_image["asset_path"] == "/path/to/image.png"
            assert processed_image["has_asset"] == True
            assert processed_image["summary"] == "This is an image of a chart"
            
            # Should have called methods
            mock_save_asset.assert_called_once()
            mock_summary.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_image_element_no_image_data(self):
        """Test image element processing without image data"""
        element = Mock()
        element.__class__.__name__ = "Image"
        element.image_data = None
        element.metadata = Mock()
        element.metadata.image_data = None
        
        element_data = {
            "id": "test_element_1",
            "type": "Image",
            "element_index": 1
        }
        
        result = {"images": [], "all_elements": []}
        
        await MultimodalManager._process_image_element(element, element_data, "test_doc_id", result)
        
        # Should handle missing image data
        assert len(result["images"]) == 1
        processed_image = result["images"][0]
        
        assert processed_image["has_asset"] == False
        assert processed_image["summary"] == "No image data available"
    
    @pytest.mark.asyncio
    async def test_process_image_element_metadata_image_data(self):
        """Test image element processing with metadata image data"""
        element = Mock()
        element.__class__.__name__ = "Image"
        element.image_data = None  # No direct image data
        element.metadata = Mock()
        element.metadata.image_data = b"metadata_image_data"
        
        with patch.object(MultimodalManager, '_save_image_asset') as mock_save_asset, \
             patch.object(MultimodalManager, '_get_surrounding_context'), \
             patch.object(MultimodalManager, 'generate_image_summary'):
            
            mock_save_asset.return_value = "/path/to/metadata_image.png"
            
            element_data = {"id": "test_element_1", "element_index": 1}
            result = {"images": [], "all_elements": []}
            
            await MultimodalManager._process_image_element(element, element_data, "test_doc_id", result)
            
            # Should use metadata image data
            mock_save_asset.assert_called_once_with(b"metadata_image_data", "test_doc_id", "test_element_1")
    
    @pytest.mark.asyncio
    async def test_process_image_element_error_handling(self, mock_image_element):
        """Test image element processing error handling"""
        with patch.object(MultimodalManager, '_save_image_asset', side_effect=Exception("Save failed")):
            
            element_data = {"id": "test_element_1", "element_index": 1}
            result = {"images": [], "all_elements": []}
            
            await MultimodalManager._process_image_element(
                mock_image_element, element_data, "test_doc_id", result
            )
            
            # Should handle error gracefully
            assert len(result["images"]) == 1
            assert "error" in result["images"][0]
            assert result["images"][0]["error"] == "Save failed"
    
    @pytest.mark.asyncio
    async def test_save_image_asset_success(self):
        """Test successful image asset saving"""
        image_data = b"fake_image_binary_data"
        
        with patch('managers.manager_multimodal.BASE_DIR') as mock_base_dir, \
             patch('builtins.open', mock_open()) as mock_file, \
             patch('builtins.print'):
            
            # Mock directory structure
            mock_doc_dir = Mock()
            mock_doc_dir.mkdir = Mock()
            mock_base_dir.return_value.__truediv__.return_value = mock_doc_dir
            
            result = await MultimodalManager._save_image_asset(image_data, "test_doc_id", "element_1")
            
            # Should create directory
            mock_doc_dir.mkdir.assert_called_once_with(parents=True, exist_ok=True)
            
            # Should save file
            mock_file.assert_called_once()
            
            # Should return path
            assert isinstance(result, str)
            assert len(result) > 0
    
    @pytest.mark.asyncio
    async def test_save_image_asset_error(self):
        """Test image asset saving with error"""
        with patch('managers.manager_multimodal.BASE_DIR', side_effect=Exception("Directory error")), \
             patch('builtins.print'):
            
            result = await MultimodalManager._save_image_asset(b"data", "doc_id", "element_id")
            
            # Should return empty string on error
            assert result == ""

class TestMultimodalManagerTableProcessing:
    """Test MultimodalManager table processing functionality"""
    
    @pytest.fixture
    def mock_table_element(self):
        """Create mock table element"""
        element = Mock()
        element.__class__.__name__ = "Table"
        element.__str__ = Mock(return_value="Table content")
        element.metadata = Mock()
        element.metadata.to_dict = Mock(return_value={"type": "table"})
        element.table_data = [["Name", "Age", "City"], ["John", "30", "NYC"], ["Jane", "25", "LA"]]
        return element
    
    @pytest.mark.asyncio
    async def test_process_table_element_success(self, mock_table_element):
        """Test successful table element processing"""
        with patch.object(MultimodalManager, '_table_to_markdown') as mock_to_markdown, \
             patch.object(MultimodalManager, '_get_surrounding_context') as mock_context, \
             patch.object(MultimodalManager, 'generate_table_summary') as mock_summary, \
             patch.object(MultimodalManager, '_extract_table_key_info') as mock_key_info:
            
            mock_to_markdown.return_value = "| Name | Age | City |\n|------|-----|------|\n| John | 30  | NYC  |"
            mock_context.return_value = "Context about the table"
            mock_summary.return_value = "Table shows employee information"
            mock_key_info.return_value = {"headers": ["Name", "Age", "City"], "num_rows": 1}
            
            element_data = {
                "id": "test_table_1",
                "type": "Table",
                "element_index": 2
            }
            
            result = {"tables": [], "all_elements": []}
            
            await MultimodalManager._process_table_element(
                mock_table_element, element_data, "test_doc_id", result
            )
            
            # Should have processed table
            assert len(result["tables"]) == 1
            processed_table = result["tables"][0]
            
            assert processed_table["has_structure"] == True
            assert processed_table["table_structure"] == [["Name", "Age", "City"], ["John", "30", "NYC"], ["Jane", "25", "LA"]]
            assert "markdown" in processed_table
            assert "summary" in processed_table
            assert "key_info" in processed_table
            
            # Should have called processing methods
            mock_to_markdown.assert_called_once()
            mock_summary.assert_called_once()
            mock_key_info.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_process_table_element_no_table_data(self):
        """Test table element processing without table data"""
        element = Mock()
        element.__class__.__name__ = "Table"
        element.table_data = None
        element.metadata = Mock()
        element.metadata.table_data = None
        
        with patch.object(MultimodalManager, '_table_to_markdown') as mock_to_markdown, \
             patch.object(MultimodalManager, '_get_surrounding_context'), \
             patch.object(MultimodalManager, 'generate_table_summary'), \
             patch.object(MultimodalManager, '_extract_table_key_info'):
            
            mock_to_markdown.return_value = "Simple table text"
            
            element_data = {"id": "test_table_1", "element_index": 2}
            result = {"tables": [], "all_elements": []}
            
            await MultimodalManager._process_table_element(element, element_data, "test_doc_id", result)
            
            # Should handle missing table data
            assert len(result["tables"]) == 1
            processed_table = result["tables"][0]
            assert processed_table["has_structure"] == False
    
    @pytest.mark.asyncio
    async def test_process_table_element_error_handling(self, mock_table_element):
        """Test table element processing error handling"""
        with patch.object(MultimodalManager, '_table_to_markdown', side_effect=Exception("Markdown failed")):
            
            element_data = {"id": "test_table_1", "element_index": 2}
            result = {"tables": [], "all_elements": []}
            
            await MultimodalManager._process_table_element(
                mock_table_element, element_data, "test_doc_id", result
            )
            
            # Should handle error gracefully
            assert len(result["tables"]) == 1
            assert "error" in result["tables"][0]
            assert result["tables"][0]["error"] == "Markdown failed"
    
    @pytest.mark.asyncio
    async def test_table_to_markdown_structured_data(self):
        """Test table to markdown conversion with structured data"""
        table_element = Mock()
        table_element.table_data = [["Header1", "Header2"], ["Data1", "Data2"]]
        
        result = await MultimodalManager._table_to_markdown(table_element)
        
        expected = "| Header1 | Header2 |\n| --- | --- |\n| Data1 | Data2 |\n"
        assert result == expected
    
    @pytest.mark.asyncio
    async def test_table_to_markdown_no_structured_data(self):
        """Test table to markdown conversion without structured data"""
        table_element = Mock()
        table_element.table_data = None
        table_element.__str__ = Mock(return_value="Fallback table text")
        
        result = await MultimodalManager._table_to_markdown(table_element)
        
        assert result == "Fallback table text"
    
    @pytest.mark.asyncio
    async def test_table_to_markdown_error(self):
        """Test table to markdown conversion with error"""
        table_element = Mock()
        table_element.table_data = None
        table_element.__str__ = Mock(side_effect=Exception("String conversion failed"))
        
        result = await MultimodalManager._table_to_markdown(table_element)
        
        # Should handle error gracefully
        assert isinstance(result, str)
    
    def test_structured_table_to_markdown_normal(self):
        """Test structured table to markdown conversion"""
        table_data = [
            ["Name", "Age", "Occupation"],
            ["Alice", "30", "Engineer"],
            ["Bob", "25", "Designer"]
        ]
        
        result = MultimodalManager._structured_table_to_markdown(table_data)
        
        expected = "| Name | Age | Occupation |\n| --- | --- | --- |\n| Alice | 30 | Engineer |\n| Bob | 25 | Designer |\n"
        assert result == expected
    
    def test_structured_table_to_markdown_empty(self):
        """Test structured table to markdown with empty data"""
        assert MultimodalManager._structured_table_to_markdown([]) == ""
        assert MultimodalManager._structured_table_to_markdown([[]]) == ""
    
    def test_structured_table_to_markdown_uneven_rows(self):
        """Test structured table to markdown with uneven rows"""
        table_data = [
            ["Name", "Age", "City"],
            ["Alice", "30"],  # Missing city
            ["Bob", "25", "NYC", "Extra"]  # Extra column
        ]
        
        result = MultimodalManager._structured_table_to_markdown(table_data)
        
        # Should handle uneven rows by padding/truncating
        lines = result.strip().split('\n')
        assert len(lines) == 4  # Header, separator, 2 data rows
        
        # Check that rows are properly formatted
        assert "| Alice | 30 |  |" in result  # Padded with empty cell
        assert "| Bob | 25 | NYC |" in result  # Truncated to header length

class TestMultimodalManagerAIIntegration:
    """Test MultimodalManager AI integration functionality"""
    
    @pytest.mark.asyncio
    async def test_generate_image_summary_success(self):
        """Test successful image summary generation"""
        with patch('openai.OpenAI') as mock_openai_class, \
             patch.object(MultimodalManager, '_encode_image_to_base64') as mock_encode:
            
            # Mock OpenAI client and response
            mock_client = Mock()
            mock_openai_class.return_value = mock_client
            
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = "This image shows a bar chart with sales data"
            mock_client.chat.completions.create.return_value = mock_response
            
            mock_encode.return_value = "base64_encoded_image_data"
            
            result = await MultimodalManager.generate_image_summary("/path/to/image.png", "Context text")
            
            assert result == "This image shows a bar chart with sales data"
            mock_encode.assert_called_once_with("/path/to/image.png")
            mock_client.chat.completions.create.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_generate_image_summary_error(self):
        """Test image summary generation with error"""
        with patch('openai.OpenAI', side_effect=Exception("OpenAI API error")), \
             patch('builtins.print'):
            
            result = await MultimodalManager.generate_image_summary("/path/to/image.png")
            
            assert "Image description unavailable" in result
            assert "OpenAI API error" in result
    
    @pytest.mark.asyncio
    async def test_generate_table_summary_success(self):
        """Test successful table summary generation"""
        with patch('openai.OpenAI') as mock_openai_class:
            
            # Mock OpenAI client and response
            mock_client = Mock()
            mock_openai_class.return_value = mock_client
            
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = "This table shows employee data with names, ages, and cities"
            mock_client.chat.completions.create.return_value = mock_response
            
            table_markdown = "| Name | Age | City |\n|------|-----|------|\n| John | 30  | NYC  |"
            
            result = await MultimodalManager.generate_table_summary(table_markdown, "Employee data context")
            
            assert result == "This table shows employee data with names, ages, and cities"
            mock_client.chat.completions.create.assert_called_once()
            
            # Verify the prompt contains table content
            call_args = mock_client.chat.completions.create.call_args
            prompt_content = call_args[1]['messages'][0]['content']
            assert table_markdown in prompt_content
    
    @pytest.mark.asyncio
    async def test_generate_table_summary_error(self):
        """Test table summary generation with error"""
        with patch('openai.OpenAI', side_effect=Exception("OpenAI API error")), \
             patch('builtins.print'):
            
            result = await MultimodalManager.generate_table_summary("| Test | Table |")
            
            assert "Table summary unavailable" in result
            assert "OpenAI API error" in result
    
    @pytest.mark.asyncio
    async def test_encode_image_to_base64_success(self):
        """Test successful image encoding to base64"""
        fake_image_data = b"fake_image_binary_data"
        
        with patch('builtins.open', mock_open(read_data=fake_image_data)):
            
            result = await MultimodalManager._encode_image_to_base64("/path/to/image.png")
            
            expected = base64.b64encode(fake_image_data).decode('utf-8')
            assert result == expected
    
    @pytest.mark.asyncio
    async def test_encode_image_to_base64_error(self):
        """Test image encoding to base64 with error"""
        with patch('builtins.open', side_effect=FileNotFoundError("File not found")), \
             patch('builtins.print'):
            
            result = await MultimodalManager._encode_image_to_base64("/nonexistent/image.png")
            
            assert result == ""

class TestMultimodalManagerUtilities:
    """Test MultimodalManager utility functions"""
    
    def test_get_surrounding_context_normal(self):
        """Test getting surrounding context for an element"""
        all_elements = [
            {"type": "Title", "text": "Document Title"},
            {"type": "NarrativeText", "text": "First paragraph."},
            {"type": "Image", "text": "Image description"},
            {"type": "NarrativeText", "text": "Second paragraph."},
            {"type": "Table", "text": "Table content"},
            {"type": "NarrativeText", "text": "Third paragraph."}
        ]
        
        # Get context for image at index 2
        context = MultimodalManager._get_surrounding_context(all_elements, 2, context_window=1)
        
        # Should include preceding and following narrative text
        assert "First paragraph." in context
        assert "Second paragraph." in context
        assert "Document Title" not in context  # Title excluded
    
    def test_get_surrounding_context_edge_cases(self):
        """Test getting surrounding context at edges"""
        all_elements = [
            {"type": "NarrativeText", "text": "Only element."}
        ]
        
        # Context for single element
        context = MultimodalManager._get_surrounding_context(all_elements, 0, context_window=2)
        assert context == ""  # No surrounding elements
        
        # Context beyond array bounds
        context = MultimodalManager._get_surrounding_context(all_elements, 10, context_window=2)
        assert context == ""
    
    def test_get_surrounding_context_error_handling(self):
        """Test surrounding context with error"""
        # Malformed elements
        all_elements = [{"invalid": "structure"}]
        
        with patch('builtins.print'):
            context = MultimodalManager._get_surrounding_context(all_elements, 0)
            assert context == ""
    
    @pytest.mark.asyncio
    async def test_extract_table_key_info_success(self):
        """Test successful table key info extraction"""
        table_markdown = """| Name | Age | Salary |
|------|-----|--------|
| John | 30  | 50000  |
| Jane | 25  | 45000  |"""
        
        with patch.object(MultimodalManager, '_infer_column_types') as mock_infer:
            mock_infer.return_value = {"Name": "text", "Age": "numeric", "Salary": "numeric"}
            
            result = await MultimodalManager._extract_table_key_info(table_markdown)
            
            assert result["headers"] == ["Name", "Age", "Salary"]
            assert result["num_columns"] == 3
            assert result["num_rows"] == 2
            assert result["total_cells"] == 6
            assert result["has_headers"] == True
            assert "column_types" in result
    
    @pytest.mark.asyncio
    async def test_extract_table_key_info_invalid_format(self):
        """Test table key info extraction with invalid format"""
        invalid_markdown = "Not a valid table"
        
        result = await MultimodalManager._extract_table_key_info(invalid_markdown)
        
        assert "error" in result
        assert result["error"] == "Invalid table format"
    
    @pytest.mark.asyncio
    async def test_extract_table_key_info_error(self):
        """Test table key info extraction with error"""
        with patch('builtins.print'):
            # Pass None to cause error
            result = await MultimodalManager._extract_table_key_info(None)
            
            assert "error" in result
    
    @pytest.mark.asyncio
    async def test_infer_column_types_success(self):
        """Test successful column type inference"""
        data_rows = [
            ["John", "30", "2023-01-15"],
            ["Jane", "25", "2023-02-20"],
            ["Bob", "35", "2023-03-10"]
        ]
        headers = ["Name", "Age", "Date"]
        
        result = await MultimodalManager._infer_column_types(data_rows, headers)
        
        assert result["Name"] == "text"
        assert result["Age"] == "numeric"
        assert result["Date"] == "date"
    
    @pytest.mark.asyncio
    async def test_infer_column_types_mixed_data(self):
        """Test column type inference with mixed data"""
        data_rows = [
            ["John", "30", "Not a date"],
            ["Jane", "Not a number", "2023-02-20"]
        ]
        headers = ["Name", "Age", "Date"]
        
        result = await MultimodalManager._infer_column_types(data_rows, headers)
        
        # Mixed data should default to text
        assert result["Name"] == "text"
        assert result["Age"] == "text"
        assert result["Date"] == "text"
    
    @pytest.mark.asyncio
    async def test_infer_column_types_error(self):
        """Test column type inference with error"""
        with patch('builtins.print'):
            result = await MultimodalManager._infer_column_types(None, None)
            assert result == {}
    
    def test_is_numeric_valid_numbers(self):
        """Test numeric validation with valid numbers"""
        assert MultimodalManager._is_numeric("123") == True
        assert MultimodalManager._is_numeric("45.67") == True
        assert MultimodalManager._is_numeric("1,234.56") == True
        assert MultimodalManager._is_numeric("$1,000") == True
        assert MultimodalManager._is_numeric("75%") == True
    
    def test_is_numeric_invalid_values(self):
        """Test numeric validation with invalid values"""
        assert MultimodalManager._is_numeric("abc") == False
        assert MultimodalManager._is_numeric("123abc") == False
        assert MultimodalManager._is_numeric("") == False
        assert MultimodalManager._is_numeric("not a number") == False
    
    def test_is_date_valid_dates(self):
        """Test date validation with valid dates"""
        assert MultimodalManager._is_date("2023-12-25") == True
        assert MultimodalManager._is_date("12/25/2023") == True
        assert MultimodalManager._is_date("12-25-2023") == True
    
    def test_is_date_invalid_values(self):
        """Test date validation with invalid values"""
        assert MultimodalManager._is_date("not a date") == False
        assert MultimodalManager._is_date("123") == False
        assert MultimodalManager._is_date("") == False
        assert MultimodalManager._is_date("25/12/23") == False  # Doesn't match patterns

class TestMultimodalManagerAssetManagement:
    """Test MultimodalManager asset management functionality"""
    
    @pytest.mark.asyncio
    async def test_cleanup_assets_success(self):
        """Test successful asset cleanup"""
        with patch('managers.manager_multimodal.BASE_DIR') as mock_base_dir, \
             patch('shutil.rmtree') as mock_rmtree:
            
            # Mock directory structure
            mock_doc_dir = Mock()
            mock_doc_dir.exists.return_value = True
            mock_base_dir.return_value.__truediv__.return_value = mock_doc_dir
            
            await MultimodalManager.cleanup_assets("test_doc_id")
            
            # Should check if directory exists and remove it
            mock_doc_dir.exists.assert_called_once()
            mock_rmtree.assert_called_once_with(mock_doc_dir)
    
    @pytest.mark.asyncio
    async def test_cleanup_assets_directory_not_exists(self):
        """Test asset cleanup when directory doesn't exist"""
        with patch('managers.manager_multimodal.BASE_DIR') as mock_base_dir, \
             patch('shutil.rmtree') as mock_rmtree:
            
            # Mock directory that doesn't exist
            mock_doc_dir = Mock()
            mock_doc_dir.exists.return_value = False
            mock_base_dir.return_value.__truediv__.return_value = mock_doc_dir
            
            await MultimodalManager.cleanup_assets("test_doc_id")
            
            # Should not attempt to remove non-existent directory
            mock_doc_dir.exists.assert_called_once()
            mock_rmtree.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_cleanup_assets_error(self):
        """Test asset cleanup with error"""
        with patch('managers.manager_multimodal.BASE_DIR', side_effect=Exception("Path error")), \
             patch('builtins.print'):
            
            # Should handle error gracefully
            await MultimodalManager.cleanup_assets("test_doc_id")
    
    @pytest.mark.asyncio
    async def test_get_asset_path_success(self):
        """Test successful asset path retrieval"""
        with patch('managers.manager_multimodal.BASE_DIR') as mock_base_dir:
            
            # Mock directory with asset files
            mock_doc_dir = Mock()
            mock_asset_file = Mock()
            mock_asset_file.__str__ = Mock(return_value="/path/to/element_1_abc123.png")
            mock_doc_dir.glob.return_value = [mock_asset_file]
            mock_base_dir.return_value.__truediv__.return_value = mock_doc_dir
            
            result = await MultimodalManager.get_asset_path("test_doc_id", "element_1")
            
            assert result == "/path/to/element_1_abc123.png"
            mock_doc_dir.glob.assert_called_once_with("element_1_*")
    
    @pytest.mark.asyncio
    async def test_get_asset_path_not_found(self):
        """Test asset path retrieval when asset not found"""
        with patch('managers.manager_multimodal.BASE_DIR') as mock_base_dir:
            
            # Mock directory with no matching files
            mock_doc_dir = Mock()
            mock_doc_dir.glob.return_value = []
            mock_base_dir.return_value.__truediv__.return_value = mock_doc_dir
            
            result = await MultimodalManager.get_asset_path("test_doc_id", "nonexistent_element")
            
            assert result is None
    
    @pytest.mark.asyncio
    async def test_get_asset_path_error(self):
        """Test asset path retrieval with error"""
        with patch('managers.manager_multimodal.BASE_DIR', side_effect=Exception("Path error")), \
             patch('builtins.print'):
            
            result = await MultimodalManager.get_asset_path("test_doc_id", "element_1")
            
            assert result is None

class TestMultimodalManagerIntegration:
    """Test MultimodalManager integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_full_document_processing_workflow(self):
        """Test complete document processing workflow"""
        # Reset and setup manager
        MultimodalManager._instance = None
        MultimodalManager._initialized = False
        
        with patch('managers.manager_multimodal.BASE_DIR') as mock_base_dir, \
             patch('managers.manager_multimodal.partition') as mock_partition, \
             patch('openai.OpenAI') as mock_openai_class, \
             patch('builtins.open', mock_open(read_data=b"fake_image_data")), \
             patch('builtins.print'):
            
            # Setup mocks
            mock_path = Mock()
            mock_path.mkdir = Mock()
            mock_base_dir.return_value.__truediv__.return_value = mock_path
            
            # Mock document elements
            mock_text = Mock()
            mock_text.__class__.__name__ = "NarrativeText"
            mock_text.__str__ = Mock(return_value="Document text content")
            mock_text.metadata = Mock()
            mock_text.metadata.to_dict = Mock(return_value={"type": "text"})
            
            mock_image = Mock()
            mock_image.__class__.__name__ = "Image"
            mock_image.__str__ = Mock(return_value="Image description")
            mock_image.metadata = Mock()
            mock_image.metadata.to_dict = Mock(return_value={"type": "image"})
            mock_image.image_data = b"fake_image_data"
            
            mock_table = Mock()
            mock_table.__class__.__name__ = "Table"
            mock_table.__str__ = Mock(return_value="Table content")
            mock_table.metadata = Mock()
            mock_table.metadata.to_dict = Mock(return_value={"type": "table"})
            mock_table.table_data = [["Header"], ["Data"]]
            
            mock_partition.return_value = [mock_text, mock_image, mock_table]
            
            # Mock OpenAI responses
            mock_client = Mock()
            mock_openai_class.return_value = mock_client
            mock_response = Mock()
            mock_response.choices = [Mock()]
            mock_response.choices[0].message.content = "AI-generated description"
            mock_client.chat.completions.create.return_value = mock_response
            
            # Setup manager
            await MultimodalManager.setup()
            
            # Process document
            result = await MultimodalManager.extract_multimodal_elements("test_document.pdf", "test_doc")
            
            # Verify workflow completed
            assert result["doc_id"] == "test_doc"
            assert len(result["all_elements"]) == 3
            assert len(result["text_elements"]) == 1
            assert len(result["images"]) == 1
            assert len(result["tables"]) == 1
            
            # Verify AI processing was called
            assert mock_client.chat.completions.create.call_count >= 1
    
    @pytest.mark.asyncio
    async def test_multimodal_manager_with_rag_system(self):
        """Test MultimodalManager integration with RAG system"""
        with patch('managers.manager_multimodal.partition') as mock_partition, \
             patch('builtins.print'):
            
            # Mock complex document structure
            elements = []
            for i in range(10):
                element = Mock()
                element.__class__.__name__ = "NarrativeText"
                element.__str__ = Mock(return_value=f"Text content {i}")
                element.metadata = Mock()
                element.metadata.to_dict = Mock(return_value={"chunk": i})
                elements.append(element)
            
            mock_partition.return_value = elements
            
            result = await MultimodalManager.extract_multimodal_elements("large_document.pdf")
            
            # Should process large document efficiently
            assert len(result["all_elements"]) == 10
            assert len(result["text_elements"]) == 10
            
            # Should be suitable for RAG chunking
            for i, element in enumerate(result["all_elements"]):
                assert element["id"].endswith(f"_{i}")
                assert element["type"] == "NarrativeText"
    
    @pytest.mark.asyncio
    async def test_error_recovery_in_complex_workflow(self):
        """Test error recovery in complex processing workflow"""
        with patch('managers.manager_multimodal.partition') as mock_partition, \
             patch.object(MultimodalManager, '_process_image_element', side_effect=Exception("Image processing failed")), \
             patch.object(MultimodalManager, '_process_table_element', side_effect=Exception("Table processing failed")), \
             patch('builtins.print'):
            
            # Mock elements that will cause processing errors
            mock_image = Mock()
            mock_image.__class__.__name__ = "Image"
            mock_image.__str__ = Mock(return_value="Image")
            mock_image.metadata = Mock()
            mock_image.metadata.to_dict = Mock(return_value={"type": "image"})
            
            mock_table = Mock()
            mock_table.__class__.__name__ = "Table"
            mock_table.__str__ = Mock(return_value="Table")
            mock_table.metadata = Mock()
            mock_table.metadata.to_dict = Mock(return_value={"type": "table"})
            
            mock_partition.return_value = [mock_image, mock_table]
            
            # Should handle errors gracefully
            result = await MultimodalManager.extract_multimodal_elements("error_document.pdf")
            
            # Should still return valid structure
            assert "doc_id" in result
            assert "all_elements" in result
            assert len(result["all_elements"]) == 2

class TestMultimodalManagerPerformance:
    """Test MultimodalManager performance characteristics"""
    
    @pytest.mark.asyncio
    async def test_large_document_processing_performance(self):
        """Test performance with large documents"""
        import time
        
        with patch('managers.manager_multimodal.partition') as mock_partition, \
             patch('builtins.print'):
            
            # Create large number of mock elements
            elements = []
            for i in range(1000):
                element = Mock()
                element.__class__.__name__ = "NarrativeText"
                element.__str__ = Mock(return_value=f"Content {i}")
                element.metadata = Mock()
                element.metadata.to_dict = Mock(return_value={"index": i})
                elements.append(element)
            
            mock_partition.return_value = elements
            
            start_time = time.time()
            
            result = await MultimodalManager.extract_multimodal_elements("large_document.pdf")
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Should process efficiently
            assert processing_time < 5.0, f"Large document processing should be efficient, took {processing_time:.3f}s"
            assert len(result["all_elements"]) == 1000
    
    def test_memory_efficiency(self):
        """Test MultimodalManager memory efficiency"""
        import sys
        
        manager = MultimodalManager.get_instance()
        
        # Check memory usage
        manager_size = sys.getsizeof(manager)
        
        # Should be reasonably sized
        assert manager_size < 5000, f"Manager should be memory efficient, uses {manager_size} bytes"
    
    @pytest.mark.asyncio
    async def test_concurrent_processing(self):
        """Test concurrent document processing"""
        with patch('managers.manager_multimodal.partition') as mock_partition, \
             patch('builtins.print'):
            
            mock_element = Mock()
            mock_element.__class__.__name__ = "NarrativeText"
            mock_element.__str__ = Mock(return_value="Content")
            mock_element.metadata = Mock()
            mock_element.metadata.to_dict = Mock(return_value={"type": "text"})
            
            mock_partition.return_value = [mock_element]
            
            # Process multiple documents concurrently
            tasks = []
            for i in range(10):
                task = asyncio.create_task(
                    MultimodalManager.extract_multimodal_elements(f"document_{i}.pdf", f"doc_{i}")
                )
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
            
            # All should complete successfully
            assert len(results) == 10
            for i, result in enumerate(results):
                assert result["doc_id"] == f"doc_{i}"
                assert len(result["all_elements"]) == 1