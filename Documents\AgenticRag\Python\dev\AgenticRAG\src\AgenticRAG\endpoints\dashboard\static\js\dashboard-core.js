// Dashboard Core JavaScript Functions - Core Module
// Contains: Template loading, browser compatibility, data fetching, and basic UI functionality

console.log('[DEBUG] dashboard-core.js: Declaring global variables...');
let currentView = 'user-list';
let selectedUserGuid = null;
let templateCache = new Map();
console.log('[DEBUG] dashboard-core.js: Global variables declared - selectedUserGuid:', typeof selectedUserGuid, 'currentView:', typeof currentView);

// Template loading system
const TemplateLoader = {
    cache: new Map(),
    
    // Clear template cache
    clearCache() {
        this.cache.clear();
        console.log('Template cache cleared');
    },
    
    async loadTemplate(templateName, context = {}) {
        // Check cache first
        const cacheKey = templateName + JSON.stringify(context);
        if (this.cache.has(cacheKey)) {
            return this.cache.get(cacheKey);
        }
        
        try {
            // Build query string for template context
            const queryString = new URLSearchParams(context).toString();
            // Add cache-busting timestamp to ensure fresh templates during development
            const timestamp = new Date().getTime();
            const separator = queryString ? '&' : '?';
            const url = `/dashboard/templates/${templateName}${queryString ? '?' + queryString : ''}${separator}_t=${timestamp}`;
            
            const response = await safeFetch(url);
            const html = await response.text();
            
            // Cache the result
            this.cache.set(cacheKey, html);
            return html;
        } catch (error) {
            console.error(`Failed to load template ${templateName}:`, error);
            return `<div class="error">Failed to load template: ${templateName}</div>`;
        }
    },
    
    renderTemplate(templateHtml, data) {
        // Simple template variable replacement
        let rendered = templateHtml;
        for (const [key, value] of Object.entries(data)) {
            const regex = new RegExp(`\\{${key}\\}`, 'g');
            rendered = rendered.replace(regex, value || '');
        }
        return rendered;
    },
    
    async renderTemplateWithData(templateName, data) {
        const template = await this.loadTemplate(templateName);
        return this.renderTemplate(template, data);
    }
};

// Browser compatibility check and fallbacks
const BrowserCompat = {
    // Check for modern JavaScript features
    features: {
        fetch: typeof fetch !== 'undefined',
        localStorage: (function() {
            try {
                return 'localStorage' in window && window.localStorage !== null;
            } catch (e) {
                return false;
            }
        })(),
        arrow_functions: (function() {
            try {
                eval('() => {}');
                return true;
            } catch (e) {
                return false;
            }
        })(),
        template_literals: (function() {
            try {
                eval('`template`');
                return true;
            } catch (e) {
                return false;
            }
        })(),
        const_let: (function() {
            try {
                eval('const test = 1; let test2 = 2;');
                return true;
            } catch (e) {
                return false;
            }
        })()
    },
    
    // Show compatibility warning
    async showCompatibilityWarning() {
        const template = await TemplateLoader.loadTemplate('browser_warning.html');
        const warningDiv = document.createElement('div');
        warningDiv.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            background: #dc3545;
            color: white;
            padding: 10px;
            text-align: center;
            z-index: 9999;
            font-family: Arial, sans-serif;
        `;
        warningDiv.innerHTML = template || `
            <strong>Browser Compatibility Warning:</strong> 
            Your browser may not support all dashboard features. 
            Please update to a modern browser for the best experience.
            <button onclick="this.parentElement.style.display='none'" style="background: none; border: 1px solid white; color: white; margin-left: 10px; padding: 2px 8px; cursor: pointer;">
                Dismiss
            </button>
        `;
        document.body.insertBefore(warningDiv, document.body.firstChild);
    },
    
    // Fetch fallback using XMLHttpRequest
    fetchFallback: function(url, options = {}) {
        return new Promise(function(resolve, reject) {
            var xhr = new XMLHttpRequest();
            xhr.open(options.method || 'GET', url);
            
            if (options.headers) {
                for (var key in options.headers) {
                    xhr.setRequestHeader(key, options.headers[key]);
                }
            }
            
            xhr.onload = function() {
                var response = {
                    status: xhr.status,
                    text: function() {
                        return Promise.resolve(xhr.responseText);
                    },
                    json: function() {
                        return Promise.resolve(JSON.parse(xhr.responseText));
                    }
                };
                resolve(response);
            };
            
            xhr.onerror = function() {
                reject(new Error('Network error'));
            };
            
            xhr.send(options.body);
        });
    },
    
    // Safe localStorage with fallback
    storage: {
        setItem: function(key, value) {
            try {
                if (BrowserCompat.features.localStorage) {
                    localStorage.setItem(key, value);
                } else {
                    // Fallback to in-memory storage
                    BrowserCompat._memoryStorage = BrowserCompat._memoryStorage || {};
                    BrowserCompat._memoryStorage[key] = value;
                }
            } catch (e) {
                console.warn('Could not save to storage:', e);
            }
        },
        
        getItem: function(key) {
            try {
                if (BrowserCompat.features.localStorage) {
                    return localStorage.getItem(key);
                } else {
                    return BrowserCompat._memoryStorage ? BrowserCompat._memoryStorage[key] : null;
                }
            } catch (e) {
                console.warn('Could not read from storage:', e);
                return null;
            }
        },
        
        removeItem: function(key) {
            try {
                if (BrowserCompat.features.localStorage) {
                    localStorage.removeItem(key);
                } else if (BrowserCompat._memoryStorage) {
                    delete BrowserCompat._memoryStorage[key];
                }
            } catch (e) {
                console.warn('Could not remove from storage:', e);
            }
        }
    },
    
    // Check compatibility and initialize fallbacks
    init: function() {
        var hasIssues = false;
        
        if (!this.features.fetch) {
            console.warn('Fetch API not supported, using XMLHttpRequest fallback');
            hasIssues = true;
        }
        
        if (!this.features.localStorage) {
            console.warn('localStorage not supported, using memory fallback');
            hasIssues = true;
        }
        
        if (!this.features.arrow_functions || !this.features.template_literals || !this.features.const_let) {
            console.warn('Modern JavaScript features not supported');
            hasIssues = true;
        }
        
        if (hasIssues) {
            this.showCompatibilityWarning();
        }
        
        return !hasIssues;
    }
};

// Safe fetch function that uses fallback
function safeFetch(url, options) {
    if (BrowserCompat.features.fetch) {
        return fetch(url, options);
    } else {
        return BrowserCompat.fetchFallback(url, options);
    }
}

// Session timeout handling
async function handleSessionTimeout(data) {
    // Save current state before redirecting using safe storage
    BrowserCompat.storage.setItem('dashboard_state', JSON.stringify({
        currentView: currentView,
        selectedUserGuid: selectedUserGuid,
        timestamp: new Date().toISOString()
    }));
    
    // Show session expired message using template
    const template = await TemplateLoader.renderTemplateWithData('session_expired.html', {
        message: data.error || 'Your session has expired. Please refresh the page to continue.'
    });
    
    const body = document.body;
    const overlay = document.createElement('div');
    overlay.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.8);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 10000;
        color: white;
        font-family: Arial, sans-serif;
    `;
    
    overlay.innerHTML = template;
    body.appendChild(overlay);
}

// Check and restore dashboard state after refresh
function restoreDashboardState() {
    try {
        const savedState = BrowserCompat.storage.getItem('dashboard_state');
        if (savedState) {
            const state = JSON.parse(savedState);
            const timeDiff = new Date() - new Date(state.timestamp);
            
            // Only restore if state is less than 5 minutes old
            if (timeDiff < 300000) {
                currentView = state.currentView;
                selectedUserGuid = state.selectedUserGuid;
                
                // Switch to saved view
                if (currentView && currentView !== 'user-list') {
                    showTab(currentView);
                }
                
                // Restore selected user if available
                if (selectedUserGuid) {
                    window.selectedUserGuid = selectedUserGuid;
                }
            }
            
            // Clear old state
            BrowserCompat.storage.removeItem('dashboard_state');
        }
    } catch (error) {
        console.log('Could not restore dashboard state:', error);
    }
}

// Escape HTML to prevent XSS
function escapeHtml(text) {
    const map = {
        '&': '&amp;',
        '<': '&lt;',
        '>': '&gt;',
        '"': '&quot;',
        "'": '&#039;'
    };
    return text.replace(/[&<>"']/g, function(m) { return map[m]; });
}

// Initialize user permissions and menu visibility
async function initializeUserPermissions() {
    try {
        // Get current user session info
        const userInfo = window.currentUser || null;
        
        // Debug logging
        console.log('[DEBUG] initializeUserPermissions called');
        console.log('[DEBUG] window.currentUser:', userInfo);
        
        // For SYSTEM users, add the class to enable system-only menu items
        if (userInfo && userInfo.is_system_user === true) {
            document.body.classList.add('user-is-system');
            console.log('[OK] User has SYSTEM permissions - showing all menu items');
            console.log('[DEBUG] Added user-is-system class to body');
            
            // Also log the specific properties for debugging
            console.log('[DEBUG] User properties:', {
                username: userInfo.username,
                is_system_user: userInfo.is_system_user,
                rank: userInfo.rank,
                user_guid: userInfo.user_guid
            });
            
        } else {
            document.body.classList.remove('user-is-system');
            console.log('[INFO] User has standard permissions - hiding SYSTEM-only menu items');
            if (userInfo) {
                console.log('[DEBUG] Non-SYSTEM user properties:', {
                    username: userInfo.username,
                    is_system_user: userInfo.is_system_user,
                    rank: userInfo.rank
                });
            }
        }
        
        // Wait a moment then double-check the body class
        setTimeout(() => {
            const hasSystemClass = document.body.classList.contains('user-is-system');
            console.log('[DEBUG] Body has user-is-system class:', hasSystemClass);
        }, 100);
        
    } catch (error) {
        console.error('Failed to initialize user permissions:', error);
        // Default to non-system user if error occurs
        document.body.classList.remove('user-is-system');
    }
}

// Initialize dashboard on page load
function initializeDashboard() {
    // Initialize browser compatibility checks first
    BrowserCompat.init();
    
    // Clear template cache on initialization to ensure fresh templates
    TemplateLoader.clearCache();
    
    // Initialize user permissions and menu visibility
    initializeUserPermissions();
    
    // Set up any initial state
    console.log('Dashboard core initialized');
    
    // Restore previous state if available
    restoreDashboardState();
}

// Initialize when DOM is ready
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeDashboard);
} else {
    initializeDashboard();
}