<div class="subscription-container">
    <div class="subscription-header">
        <div class="subscription-avatar">
            <span class="avatar-initials">{initials}</span>
        </div>
        <h1 class="subscription-title">Subscription Management</h1>
        <p class="subscription-subtitle">{display_name} - {current_plan_name}</p>
        <div class="status-badge {subscription_status_class}">
            {subscription_status}
        </div>
    </div>
    
    <div class="subscription-grid">
        <!-- Current Plan Section -->
        <div class="plan-card card">
            <h3 class="card-title">Current Plan</h3>
            <div class="plan-details">
                <div class="plan-name">{current_plan_name}</div>
                <div class="plan-price">€{monthly_cost}<span class="plan-period">/month</span></div>
                <div class="plan-features">
                    {plan_features}
                </div>
                <div class="plan-actions">
                    <button type="button" class="btn btn-secondary" onclick="showPlanOptions()">Change Plan</button>
                </div>
            </div>
        </div>
        
        <!-- Payment & Billing Section -->
        <div class="billing-card card">
            <h3 class="card-title">Payment & Billing</h3>
            <div class="billing-details">
                <div class="billing-row">
                    <span class="billing-label">Payment Method</span>
                    <div class="billing-value">
                        <span>{payment_method}</span>
                        <button type="button" class="btn btn-small" onclick="modifyPaymentMethod()">Modify</button>
                    </div>
                </div>
                <div class="billing-row">
                    <span class="billing-label">Next Billing Date</span>
                    <span class="billing-value">{next_billing_date}</span>
                </div>
                <div class="billing-row">
                    <span class="billing-label">Next Charge</span>
                    <span class="billing-value">€{monthly_cost}</span>
                </div>
                <div class="billing-row">
                    <span class="billing-label">Employee Count</span>
                    <div class="billing-value">
                        <span>{employee_count} / {max_employees}</span>
                        <button type="button" class="btn btn-small" onclick="editEmployeeCount()">Edit</button>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- Recent Invoices Section -->
        <div class="invoices-card card">
            <h3 class="card-title">Recent Invoices</h3>
            <div class="invoices-list">
                {invoices_html}
            </div>
            <div class="invoices-actions">
                <button type="button" class="btn btn-secondary" onclick="viewAllInvoices()">View All Invoices</button>
            </div>
        </div>
        
        <!-- Account Management Section -->
        <div class="account-management-card card">
            <h3 class="card-title">Account Management</h3>
            <div class="management-actions">
                <div class="action-item">
                    <h4>Subscription Settings</h4>
                    <p>Manage auto-renewal, billing notifications, and subscription preferences.</p>
                    <button type="button" class="btn btn-secondary" onclick="manageSubscription()">Manage Settings</button>
                </div>
                
                <div class="action-item">
                    <h4>Usage & Limits</h4>
                    <p>View your current usage against plan limits and upgrade options.</p>
                    <button type="button" class="btn btn-secondary" onclick="viewUsageLimits()">View Usage</button>
                </div>
                
                <div class="action-item danger">
                    <h4>Cancel Subscription</h4>
                    <p>Cancel your Zaira subscription. Access will continue until the end of the billing period.</p>
                    <button type="button" class="btn btn-danger" onclick="cancelSubscription()">Cancel Zaira</button>
                </div>
            </div>
        </div>
    </div>
</div>