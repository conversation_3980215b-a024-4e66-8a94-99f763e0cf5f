"""
Unified comprehensive unit tests for Scheduled Task Management
Consolidated from multiple scheduled task test files following CLAUDE.md guidance
"""
import pytest
import asyncio
from unittest.mock import AsyncMock, patch, MagicMock
from datetime import datetime, timezone, timedelta
from uuid import uuid4, UUID
from imports import *

@pytest.mark.unit 
class TestScheduledRequestsUnified:
    """Unified tests for scheduled task functionality"""
    
    def setup_method(self):
        """Reset singletons and setup test data"""
        # Reset singleton instances
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        # Setup common test data
        self.test_guid = str(uuid4())
        self.test_user_guid = str(uuid4())
        self.base_task_data = {
            'scheduled_guid': self.test_guid,
            'user_guid': self.test_user_guid,
            'schedule_prompt': 'test task every 5 minutes',
            'target_prompt': 'Test target',
            'delay_seconds': 300.0,
            'start_delay_seconds': 0.0,
            'schedule_type': 'recurring',
            'next_execution': datetime.now(timezone.utc),
            'is_active': True,
            'created_at': datetime.now(timezone.utc),
            'updated_at': datetime.now(timezone.utc),
            'calling_bot_name': 'test_bot',
            'task_data': {'key': 'value'},
            'cancellation_reason': None,
            'cancelled_at': None
        }
    
    def teardown_method(self):
        """Clean up after each test"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False

    # ===== CORE MANAGER FUNCTIONALITY =====
    
    def test_persistence_manager_singleton_pattern(self):
        """Test that ScheduledRequestPersistenceManager follows singleton pattern"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        instance1 = ScheduledRequestPersistenceManager()
        instance2 = ScheduledRequestPersistenceManager()
        instance3 = ScheduledRequestPersistenceManager.get_instance()
        
        assert instance1 is instance2
        assert instance2 is instance3
        assert isinstance(instance1, ScheduledRequestPersistenceManager)

    @pytest.mark.parametrize("initialization_state,should_setup", [
        (False, True),   # First time initialization
        (True, False),   # Already initialized
    ])
    async def test_manager_setup_scenarios(self, initialization_state, should_setup):
        """Test manager setup in different states"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        # Set initial state
        ScheduledRequestPersistenceManager._initialized = initialization_state
        
        with patch.object(ScheduledRequestPersistenceManager, '_initialize_database') as mock_init:
            await ScheduledRequestPersistenceManager.setup()
            
            if should_setup:
                mock_init.assert_called_once()
                assert ScheduledRequestPersistenceManager._initialized is True
            else:
                mock_init.assert_not_called()

    # ===== TASK CREATION AND MANAGEMENT =====
    
    @pytest.mark.parametrize("schedule_type,delay_seconds,expected_result", [
        ("RECURRING", 300.0, "recurring_task"),
        ("ONCE", 0.0, "one_time_task"),
        ("RECURRING", 86400.0, "daily_task"),
    ])
    async def test_task_creation_variants(self, schedule_type, delay_seconds, expected_result):
        """Test creation of different task types"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        # Prepare task data
        task_data = self.base_task_data.copy()
        task_data['schedule_type'] = schedule_type
        task_data['delay_seconds'] = delay_seconds
        
        with patch.object(manager, 'save_task', new_callable=AsyncMock) as mock_save:
            mock_save.return_value = True
            
            result = await manager.create_task(task_data)
            
            mock_save.assert_called_once()
            assert result is True  # Task creation succeeded

    @pytest.mark.parametrize("task_state,operation,expected_behavior", [
        ("active", "cancel", "task_cancelled"),
        ("active", "update", "task_updated"),
        ("cancelled", "reactivate", "task_reactivated"),
        ("completed", "cleanup", "task_removed"),
    ])
    async def test_task_state_operations(self, task_state, operation, expected_behavior):
        """Test various task state operations"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        # Setup task with specific state
        task_data = self.base_task_data.copy()
        task_data['is_active'] = (task_state == "active")
        if task_state == "cancelled":
            task_data['cancellation_reason'] = "User cancelled"
            task_data['cancelled_at'] = datetime.now(timezone.utc)
        
        with patch.object(manager, 'save_task', new_callable=AsyncMock) as mock_save:
            with patch.object(manager, 'get_task', new_callable=AsyncMock) as mock_get:
                mock_get.return_value = task_data
                mock_save.return_value = True
                
                if operation == "cancel":
                    result = await manager.cancel_task(self.test_guid, "Test cancellation")
                elif operation == "update":
                    result = await manager.update_task(self.test_guid, {"delay_seconds": 600.0})
                else:
                    # For other operations, just verify the state handling works
                    result = await manager.get_task(self.test_guid)
                
                assert result is not None

    # ===== PERSISTENCE OPERATIONS =====
    
    @pytest.mark.parametrize("persistence_scenario,database_response,expected_outcome", [
        ("save_success", True, "success"),
        ("save_failure", False, "failure"),
        ("load_success", {"data": "test"}, "data_returned"),
        ("load_empty", None, "no_data"),
        ("connection_error", Exception("DB Error"), "error_handled"),
    ])
    async def test_persistence_scenarios(self, persistence_scenario, database_response, expected_outcome):
        """Test various persistence scenarios"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager') as mock_db_manager:
            mock_db = MagicMock()
            mock_db_manager.get_instance.return_value = mock_db
            
            # Setup mock connection
            mock_conn = MagicMock()
            mock_db.get_connection.return_value.__aenter__.return_value = mock_conn
            
            if isinstance(database_response, Exception):
                mock_conn.execute.side_effect = database_response
                mock_conn.fetch.side_effect = database_response
            else:
                mock_conn.execute.return_value = "INSERT 0 1" if database_response else None
                mock_conn.fetch.return_value = [database_response] if database_response else []
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                try:
                    if "save" in persistence_scenario:
                        result = await manager.save_task(self.base_task_data)
                    else:
                        result = await manager.get_task(self.test_guid)
                    
                    if expected_outcome == "success":
                        assert result is True
                    elif expected_outcome == "data_returned":
                        assert result is not None
                    elif expected_outcome == "no_data":
                        assert result is None
                        
                except Exception:
                    if expected_outcome == "error_handled":
                        mock_exception.assert_called()
                    else:
                        raise

    # ===== RECURRING TASK LOGIC =====
    
    @pytest.mark.parametrize("schedule_config,current_time,expected_next_execution", [
        ({"delay_seconds": 300, "start_delay_seconds": 0}, "2024-01-01 12:00:00", "2024-01-01 12:05:00"),
        ({"delay_seconds": 3600, "start_delay_seconds": 300}, "2024-01-01 12:00:00", "2024-01-01 13:05:00"),
        ({"delay_seconds": 86400, "start_delay_seconds": 0}, "2024-01-01 12:00:00", "2024-01-02 12:00:00"),
    ])
    async def test_recurring_task_scheduling(self, schedule_config, current_time, expected_next_execution):
        """Test recurring task scheduling logic"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        # Parse test timestamps
        current_dt = datetime.fromisoformat(current_time).replace(tzinfo=timezone.utc)
        expected_dt = datetime.fromisoformat(expected_next_execution).replace(tzinfo=timezone.utc)
        
        # Setup task data
        task_data = self.base_task_data.copy()
        task_data.update(schedule_config)
        task_data['next_execution'] = current_dt
        
        with patch('datetime.datetime') as mock_datetime:
            mock_datetime.now.return_value = current_dt
            mock_datetime.fromisoformat = datetime.fromisoformat
            
            # Calculate next execution
            next_exec = manager._calculate_next_execution(task_data)
            
            # Verify the calculation (allowing for small time differences)
            time_diff = abs((next_exec - expected_dt).total_seconds())
            assert time_diff < 60  # Within 1 minute tolerance

    # ===== TASK ROUTING AND EXECUTION =====
    
    @pytest.mark.parametrize("routing_scenario,task_type,expected_route", [
        ("standard_route", "email_task", "email_supervisor"),
        ("default_route", "unknown_task", "general_supervisor"),
        ("priority_route", "urgent_task", "priority_supervisor"),
    ])
    async def test_task_routing_logic(self, routing_scenario, task_type, expected_route):
        """Test task routing to appropriate supervisors"""
        from tasks.scheduled.task_scheduled_request_manager import route_task_to_supervisor
        
        task_data = self.base_task_data.copy()
        task_data['task_type'] = task_type
        
        with patch('managers.manager_supervisors.SupervisorManager') as mock_supervisor:
            mock_supervisor.get_instance.return_value.route_task.return_value = expected_route
            
            result = await route_task_to_supervisor(task_data)
            
            # Verify routing logic was called appropriately
            mock_supervisor.get_instance.assert_called()

    # ===== USER LOGIN PERSISTENCE =====
    
    @pytest.mark.parametrize("user_state,login_event,persistence_action", [
        ("new_user", "first_login", "create_persistence"),
        ("existing_user", "regular_login", "update_persistence"),
        ("returning_user", "login_after_break", "restore_requests"),
    ])
    async def test_user_login_persistence_scenarios(self, user_state, login_event, persistence_action):
        """Test task persistence across user login scenarios"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        user_guid = str(uuid4())
        
        with patch.object(manager, 'get_requests_for_user', new_callable=AsyncMock) as mock_get_requests:
            with patch.object(manager, 'save_task', new_callable=AsyncMock) as mock_save:
                
                if user_state == "new_user":
                    mock_get_requests.return_value = []
                else:
                    mock_get_requests.return_value = [self.base_task_data]
                
                # Simulate user login event
                result = await manager.handle_user_login(user_guid)
                
                if persistence_action == "create_persistence":
                    mock_get_requests.assert_called_with(user_guid)
                elif persistence_action == "restore_requests":
                    mock_get_requests.assert_called_with(user_guid)
                    assert result is not None

    # ===== RESTART PERSISTENCE =====
    
    @pytest.mark.parametrize("restart_scenario,task_states,expected_recovery", [
        ("clean_restart", ["active", "active"], "all_requests_restored"),
        ("crash_recovery", ["active", "executing"], "partial_restoration"),
        ("maintenance_restart", ["paused", "paused"], "paused_requests_maintained"),
    ])
    async def test_restart_persistence_scenarios(self, restart_scenario, task_states, expected_recovery):
        """Test task persistence across system restarts"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        # Create tasks with different states
        test_requests = []
        for i, state in enumerate(task_states):
            task = self.base_task_data.copy()
            task['scheduled_guid'] = str(uuid4())
            task['is_active'] = (state in ["active", "executing"])
            task['execution_state'] = state
            test_requests.append(task)
        
        with patch.object(manager, 'get_all_active_requests', new_callable=AsyncMock) as mock_get_all:
            mock_get_all.return_value = test_requests
            
            # Simulate system restart recovery
            recovered_requests = await manager.recover_requests_after_restart()
            
            if expected_recovery == "all_requests_restored":
                assert len(recovered_requests) == len(task_states)
            elif expected_recovery == "partial_restoration":
                # Only active tasks should be recovered
                active_count = sum(1 for state in task_states if state == "active")
                assert len(recovered_requests) >= active_count

    # ===== BUG FIXES AND EDGE CASES =====
    
    @pytest.mark.parametrize("recurring_bug_scenario,task_config,expected_fix", [
        ("duplicate_execution", {"delay_seconds": 60}, "execution_lock"),
        ("missed_execution", {"delay_seconds": 300}, "catch_up_logic"),
        ("timezone_drift", {"timezone": "UTC"}, "timezone_normalization"),
    ])
    async def test_recurring_task_bug_fixes(self, recurring_bug_scenario, task_config, expected_fix):
        """Test fixes for known recurring task bugs"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        task_data = self.base_task_data.copy()
        task_data.update(task_config)
        
        with patch.object(manager, '_check_execution_lock', return_value=True) as mock_lock:
            with patch.object(manager, '_handle_missed_execution') as mock_catch_up:
                
                # Simulate the bug scenario
                if recurring_bug_scenario == "duplicate_execution":
                    # Test that execution lock prevents duplicates
                    result = await manager.execute_task_if_ready(task_data)
                    mock_lock.assert_called()
                
                elif recurring_bug_scenario == "missed_execution":
                    # Test catch-up logic for missed executions
                    task_data['next_execution'] = datetime.now(timezone.utc) - timedelta(hours=1)
                    result = await manager.handle_overdue_task(task_data)
                    mock_catch_up.assert_called()
                
                assert result is not None

    # ===== INTEGRATION TESTS =====
    
    async def test_complete_request_lifecycle_integration(self):
        """Test complete request lifecycle from creation to completion"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager') as mock_db:
            # Mock successful database operations
            mock_conn = MagicMock()
            mock_db.get_instance.return_value.get_connection.return_value.__aenter__.return_value = mock_conn
            mock_conn.execute.return_value = "INSERT 0 1"
            mock_conn.fetch.return_value = [self.base_task_data]
            
            # Test complete lifecycle
            # 1. Create task
            created = await manager.create_task(self.base_task_data)
            assert created is True
            
            # 2. Retrieve task
            retrieved = await manager.get_task(self.test_guid)
            assert retrieved is not None
            
            # 3. Update task
            updated = await manager.update_task(self.test_guid, {"delay_seconds": 600})
            assert updated is not None
            
            # 4. Cancel task
            cancelled = await manager.cancel_task(self.test_guid, "Test completion")
            assert cancelled is not None

    async def test_error_handling_integration(self):
        """Test comprehensive error handling across all operations"""
        from managers.scheduled_requests import ScheduledRequestPersistenceManager
        
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        with patch('managers.manager_postgreSQL.PostgreSQLManager') as mock_db:
            # Setup database to raise errors
            mock_db.get_instance.return_value.get_connection.side_effect = Exception("Database error")
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                # Test that all operations handle errors gracefully
                operations = [
                    manager.create_task(self.base_task_data),
                    manager.get_task(self.test_guid),
                    manager.update_task(self.test_guid, {}),
                    manager.cancel_task(self.test_guid, "error test")
                ]
                
                for operation in operations:
                    try:
                        await operation
                    except Exception:
                        pass  # Expected to fail
                
                # Verify error handling was called
                assert mock_exception.call_count >= len(operations)