// Dashboard Script Loader - Load dashboard JavaScript modules with error handling
// Contains: Module loading coordination and error detection

<script>
    // Monitor script loading and provide detailed error reporting
    console.log('[DEBUG] Starting dashboard.js modules loading...');
    
    // List of modules to load in order
    const dashboardModules = [
        '/dashboard/static/js/dashboard-core.js',
        '/dashboard/static/js/dashboard-live-logs.js', 
        '/dashboard/static/js/dashboard-ui.js',
        '/dashboard/static/js/dashboard-exports.js'
    ];
    
    let loadedModules = 0;
    const totalModules = dashboardModules.length;
    
    // Function to load modules sequentially
    function loadNextModule(index) {
        if (index >= totalModules) {
            console.log('[DEBUG] All dashboard modules loaded successfully');
            return;
        }
        
        const script = document.createElement('script');
        script.src = dashboardModules[index];
        script.async = false;
        
        script.onload = function() {
            loadedModules++;
            console.log(`[DEBUG] Module ${index + 1}/${totalModules} loaded: ${dashboardModules[index]}`);
            
            // Load next module
            loadNextModule(index + 1);
        };
        
        script.onerror = function(error) {
            console.error(`[ERROR] Failed to load module: ${dashboardModules[index]}`, error);
            window.dashboardScriptFailed = true;
            
            // Continue loading other modules even if one fails
            loadNextModule(index + 1);
        };
        
        document.head.appendChild(script);
    }
    
    // Start loading modules
    loadNextModule(0);
    
    // Wait for all dashboard modules to fully load using proper coordination
    setTimeout(() => {
        // Check if dashboard.js has fully loaded using the completion flag
        if (window.dashboardJsLoaded) {
            console.log('[DEBUG] Dashboard modules confirmed fully loaded with all functions exported');
            return;
        }
        
        // If not loaded yet, check individual modules
        console.log(`[DEBUG] Module loading status: ${loadedModules}/${totalModules} modules loaded`);
        
        if (loadedModules < totalModules) {
            console.log('[WARNING] Not all dashboard modules loaded successfully');
        } else {
            console.log('[DEBUG] All modules loaded but functions may still be initializing...');
            // Wait longer for function exports to complete
            setTimeout(() => {
                if (!window.dashboardJsLoaded) {
                    console.log('[WARNING] Modules loaded but functions still not available after extended wait');
                }
            }, 2000);
        }
    }, 2000);
</script>