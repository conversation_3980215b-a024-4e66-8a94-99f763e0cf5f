from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from managers.manager_logfire import LogFire
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from endpoints.testing_endpoint import MyBot_Testing
from uuid import uuid4

class TestMyBotTestingExtended:
    """Extended test suite for MyBot_Testing to achieve 90% coverage"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.bot = MyBot_Testing()
        
        # Mock task and user
        self.mock_task = MagicMock()
        self.mock_user = MagicMock()
        self.mock_user.session_guid = uuid4()
        self.mock_user.on_message = AsyncMock()
        self.mock_task.user = self.mock_user
        self.mock_task.scheduled_guid = str(uuid4())
        self.mock_task.original_physical_message = MagicMock()
        self.mock_task.human_in_the_loop_callback = MagicMock()
        
    @pytest.mark.asyncio
    async def test_generate_test_response_empty_response(self):
        """Test _generate_test_response with empty LLM response"""
        bot = MyBot_Testing()
        
        mock_llm = MagicMock()
        mock_message = MagicMock()
        mock_message.content = ""  # Empty response
        mock_llm.ainvoke = AsyncMock(return_value=mock_message)
        
        with patch('etc.ZairaSettings.ZairaSettings.llm', mock_llm), \
             patch.object(bot, '_get_fallback_response', return_value="Fallback response") as mock_fallback:
            
            response = await bot._generate_test_response("Test request")
            
            assert response == "Fallback response"
            mock_fallback.assert_called_once_with("Test request")
            
    @pytest.mark.asyncio
    async def test_generate_test_response_with_LogFire.log("DEBUG", self):
        """Test _generate_test_response with successful LLM response and print"""
        bot = MyBot_Testing()
        
        mock_llm = MagicMock()
        mock_message = MagicMock()
        mock_message.content = "Generated response"
        mock_llm.ainvoke = AsyncMock(return_value=mock_message)
        
        with patch('etc.ZairaSettings.ZairaSettings.llm', mock_llm), \
             patch('builtins.print') as mock_print:
            
            response = await bot._generate_test_response("Test request")
            
            assert response == "Generated response"
            mock_print.assert_called()
            
    @pytest.mark.asyncio
    async def test_generate_test_response_hasattr_check(self):
        """Test _generate_test_response hasattr check for ZairaSettings"""
        bot = MyBot_Testing()
        
        # Mock ZairaSettings class without llm attribute
        with patch('etc.ZairaSettings.ZairaSettings') as mock_settings_class:
            mock_settings_class.llm = None
            
            with patch.object(bot, '_get_fallback_response', return_value="Fallback response") as mock_fallback:
                response = await bot._generate_test_response("Test request")
                
                assert response == "Fallback response"
                mock_fallback.assert_called_once_with("Test request")
                
    def test_get_fallback_response_email_specific_patterns(self):
        """Test _get_fallback_response with specific email patterns"""
        bot = MyBot_Testing()
        
        # Test patterns that should return email address
        test_cases = [
            "Welk email adres?",
            "Naar welk adres?",
            "Van welk address?",
            "Wat is het mailadres?",
            "Geef het e-mailadres"
        ]
        
        for request in test_cases:
            response = bot._get_fallback_response(request)
            assert response == "<EMAIL>", f"Failed for request: {request}"
            
    def test_get_fallback_response_email_without_keywords(self):
        """Test _get_fallback_response with email word but no specific keywords"""
        bot = MyBot_Testing()
        
        # Test email word without specific keywords - should not return email
        response = bot._get_fallback_response("Email is belangrijk")
        assert response == "ja"  # Should fall back to default
        
    def test_get_fallback_response_priority_order(self):
        """Test _get_fallback_response priority order"""
        bot = MyBot_Testing()
        
        # Test approval pattern priority
        response = bot._get_fallback_response("Goedkeur deze actie")
        assert response == "j"
        
        # Test email pattern priority over approval
        response = bot._get_fallback_response("Welk email adres goedkeuren?")
        assert response == "<EMAIL>"
        
        # Test sender pattern priority
        response = bot._get_fallback_response("Wie is de sender?")
        assert response == "<EMAIL>"
        
    def test_get_fallback_response_comprehensive_patterns(self):
        """Test _get_fallback_response with comprehensive pattern matching"""
        bot = MyBot_Testing()
        
        # Test all approval patterns
        approval_patterns = ["goedkeur", "verzending", "j/n", "approve"]
        for pattern in approval_patterns:
            response = bot._get_fallback_response(f"Wil je dit {pattern}?")
            assert response == "j", f"Failed for approval pattern: {pattern}"
            
        # Test all email patterns
        email_patterns = ["email", "adres", "address", "mailadres", "e-mailadres"]
        for pattern in email_patterns:
            response = bot._get_fallback_response(f"Welk {pattern} gebruiken?")
            assert response == "<EMAIL>", f"Failed for email pattern: {pattern}"
            
        # Test all sender patterns
        sender_patterns = ["sender", "verzender", "from", "verzonden"]
        for pattern in sender_patterns:
            response = bot._get_fallback_response(f"Wie is de {pattern}?")
            assert response == "<EMAIL>", f"Failed for sender pattern: {pattern}"
            
        # Test all yes/no patterns
        yesno_patterns = ["ja", "nee", "yes", "no", "y/n"]
        for pattern in yesno_patterns:
            response = bot._get_fallback_response(f"Antwoord {pattern}?")
            assert response == "ja", f"Failed for yes/no pattern: {pattern}"
            
    def test_get_fallback_response_edge_cases(self):
        """Test _get_fallback_response edge cases"""
        bot = MyBot_Testing()
        
        # Test with multiple spaces
        response = bot._get_fallback_response("  goedkeur   dit  ")
        assert response == "j"
        
        # Test with punctuation
        response = bot._get_fallback_response("Goedkeur dit!")
        assert response == "j"
        
        # Test with mixed case
        response = bot._get_fallback_response("GOEDKEUR DIT")
        assert response == "j"
        
        # Test with no matching patterns
        response = bot._get_fallback_response("Random text with no patterns")
        assert response == "ja"  # Default response
        
    @pytest.mark.asyncio
    async def test_request_human_in_the_loop_with_long_request(self):
        """Test request_human_in_the_loop_internal with long request"""
        bot = MyBot_Testing(name="Testing")
        
        long_request = "This is a very long request that exceeds 100 characters to test the truncation in the print statement for logging purposes"
        
        with patch.object(bot, '_generate_test_response', return_value="Test response") as mock_generate, \
             patch('builtins.print') as mock_print:
            
            await bot.request_human_in_the_loop_internal(
                long_request, self.mock_task, MagicMock()
            )
            
            mock_generate.assert_called_once_with(long_request)
            # Check that print was called with truncated request
            print_calls = [call[0][0] for call in mock_print.call_args_list]
            assert any("Request:" in call and "..." in call for call in print_calls)
            
    @pytest.mark.asyncio
    async def test_request_human_in_the_loop_full_flow(self):
        """Test complete request_human_in_the_loop_internal flow"""
        bot = MyBot_Testing(name="Testing")
        
        with patch.object(bot, '_generate_test_response', return_value="Generated response") as mock_generate, \
             patch('builtins.print') as mock_print:
            
            await bot.request_human_in_the_loop_internal(
                "Test request", self.mock_task, MagicMock()
            )
            
            mock_generate.assert_called_once_with("Test request")
            self.mock_task.user.on_message.assert_called_once_with(
                "Generated response", bot, [], self.mock_task.original_physical_message
            )
            
            # Verify all print statements were called
            print_calls = [call[0][0] for call in mock_print.call_args_list]
            expected_prints = [
                "[TEST BOT] Request:",
                "[TEST BOT] Response:",
                "[TEST BOT] Task:",
                "[TEST BOT] Sending response",
                "[TEST BOT] Response sent"
            ]
            
            for expected in expected_prints:
                assert any(expected in call for call in print_calls), f"Missing expected print: {expected}"
                
    @pytest.mark.asyncio
    async def test_generate_test_response_system_prompt_formatting(self):
        """Test that system prompt is properly formatted"""
        bot = MyBot_Testing()
        
        mock_llm = MagicMock()
        mock_message = MagicMock()
        mock_message.content = "Test response"
        mock_llm.ainvoke = AsyncMock(return_value=mock_message)
        
        test_request = "Test request content"
        
        with patch('etc.ZairaSettings.ZairaSettings.llm', mock_llm):
            response = await bot._generate_test_response(test_request)
            
            # Verify ainvoke was called with correct message structure
            mock_llm.ainvoke.assert_called_once()
            call_args = mock_llm.ainvoke.call_args[1]
            messages = call_args['input']
            
            # Check that we have both system and human messages
            assert len(messages) == 2
            assert messages[0].content is not None
            assert messages[1].content == test_request
            
    @pytest.mark.asyncio
    async def test_generate_test_response_strip_response(self):
        """Test that response is properly stripped"""
        bot = MyBot_Testing()
        
        mock_llm = MagicMock()
        mock_message = MagicMock()
        mock_message.content = "  Test response with whitespace  "
        mock_llm.ainvoke = AsyncMock(return_value=mock_message)
        
        with patch('etc.ZairaSettings.ZairaSettings.llm', mock_llm):
            response = await bot._generate_test_response("Test request")
            
            assert response == "Test response with whitespace"
            
    @pytest.mark.asyncio
    async def test_generate_test_response_hasattr_no_llm(self):
        """Test _generate_test_response when hasattr returns False"""
        bot = MyBot_Testing()
        
        # Create a mock class without llm attribute
        mock_settings = type('MockSettings', (), {})()
        
        with patch('etc.ZairaSettings.ZairaSettings', mock_settings), \
             patch.object(bot, '_get_fallback_response', return_value="Fallback response") as mock_fallback:
            
            response = await bot._generate_test_response("Test request")
            
            assert response == "Fallback response"
            mock_fallback.assert_called_once_with("Test request", severity="debug")