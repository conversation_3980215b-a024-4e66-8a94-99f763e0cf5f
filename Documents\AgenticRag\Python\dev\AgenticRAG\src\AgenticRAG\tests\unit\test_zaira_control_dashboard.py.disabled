from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from uuid import uuid4
import asyncio
from datetime import datetime, timezone, timedelta
import json
from aiohttp import web
from aiohttp.test_utils import AioHTTPTestCase, unittest_run_loop

from endpoints.zairacontrol_endpoint import ZairaControlEndpoint
from managers.scheduled_requests.admin_interface import ScheduledRequestAdminInterface

class TestZairaControlDashboard:
    """Comprehensive test class for ZairaControl Dashboard"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.dashboard = ZairaControlDashboard()
        self.test_user_guid = str(uuid4())
        
        # Mock admin interface
        self.mock_admin = Mock(spec=AdminInterface)
        
        # Mock system overview data
        self.mock_system_overview = {
            'active_managers': 5,
            'total_active_requests': 25,
            'total_users': 10,
            'system_health': 'healthy',
            'memory_usage_mb': 150.5,
            'uptime_seconds': 3600,
            'recent_errors': []
        }
        
        # Mock user requests data
        self.mock_user_requests = [
            {
                'scheduled_guid': str(uuid4()),
                'user_guid': self.test_user_guid,
                'schedule_prompt': 'Test prompt 1',
                'target_prompt': 'Test target 1',
                'status': 'active',
                'created_at': datetime.now(timezone.utc).isoformat(),
                'next_execution': (datetime.now(timezone.utc) + timedelta(hours=1)).isoformat()
            },
            {
                'scheduled_guid': str(uuid4()),
                'user_guid': self.test_user_guid,
                'schedule_prompt': 'Test prompt 2',
                'target_prompt': 'Test target 2',
                'status': 'pending',
                'created_at': datetime.now(timezone.utc).isoformat(),
                'next_execution': (datetime.now(timezone.utc) + timedelta(hours=2)).isoformat()
            }
        ]
        
        # Mock security report data
        self.mock_security_report = {
            'active_violations': 3,
            'blocked_ips_count': 2,
            'recent_violations': [
                {
                    'timestamp': datetime.now(timezone.utc).isoformat(),
                    'user_guid': self.test_user_guid,
                    'violation_type': 'rate_limit_exceeded',
                    'details': 'Too many requests'
                }
            ],
            'top_violating_users': [
                {'user_guid': self.test_user_guid, 'violation_count': 3}
            ]
        }
        
        # Mock performance report data
        self.mock_performance_report = {
            'average_response_time': 250.5,
            'total_requests_processed': 1000,
            'success_rate': 98.5,
            'error_rate': 1.5,
            'system_load': 45.2,
            'recommendations': [
                'Consider increasing thread pool size for user XYZ',
                'Monitor memory usage for user ABC'
            ]
        }
        
    def teardown_method(self):
        """Clean up after each test"""
        pass
    
    def test_dashboard_initialization(self):
        """Test dashboard initialization"""
        dashboard = ZairaControlDashboard()
        
        assert hasattr(dashboard, 'get_admin_interface')
        assert callable(dashboard.get_admin_interface)
    
    @pytest.mark.asyncio
    async def test_get_admin_interface(self):
        """Test getting admin interface"""
        with patch('managers.scheduled_requests.admin.admin_interface.AdminInterface.get_instance') as mock_get_admin:
            mock_get_admin.return_value = self.mock_admin
            
            admin_interface = await self.dashboard.get_admin_interface()
            
            assert admin_interface == self.mock_admin
            mock_get_admin.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_dashboard_home_success(self):
        """Test successful dashboard home page rendering"""
        mock_request = Mock()
        mock_request.method = 'GET'
        mock_request.path = '/dashboard'
        
        with patch.object(self.dashboard, 'get_admin_interface', return_value=self.mock_admin):
            self.mock_admin.get_system_overview.return_value = self.mock_system_overview
            
            response = await self.dashboard.dashboard_home(mock_request)
            
            assert isinstance(response, web.Response)
            assert response.status == 200
            assert response.content_type == 'text/html'
            
            # Verify system overview data is in the response
            response_text = response.text
            assert 'ZairaControl Dashboard' in response_text
            assert str(self.mock_system_overview['active_managers']) in response_text
            assert str(self.mock_system_overview['total_active_requests']) in response_text
            assert self.mock_system_overview['system_health'] in response_text
    
    @pytest.mark.asyncio
    async def test_dashboard_home_error_handling(self):
        """Test dashboard home error handling"""
        mock_request = Mock()
        mock_request.method = 'GET'
        mock_request.path = '/dashboard'
        
        with patch.object(self.dashboard, 'get_admin_interface', side_effect=Exception("Admin interface error")):
            response = await self.dashboard.dashboard_home(mock_request)
            
            assert isinstance(response, web.Response)
            assert response.status == 500
            assert 'Error loading dashboard' in response.text
    
    @pytest.mark.asyncio
    async def test_system_status_api_success(self):
        """Test successful system status API response"""
        mock_request = Mock()
        
        with patch.object(self.dashboard, 'get_admin_interface', return_value=self.mock_admin):
            self.mock_admin.get_system_overview.return_value = self.mock_system_overview
            
            response = await self.dashboard.system_status_api(mock_request)
            
            assert isinstance(response, web.Response)
            assert response.status == 200
            assert response.content_type == 'application/json'
            
            # Parse response JSON
            response_data = json.loads(response.text)
            assert response_data['success'] is True
            assert response_data['data'] == self.mock_system_overview
    
    @pytest.mark.asyncio
    async def test_system_status_api_error(self):
        """Test system status API error handling"""
        mock_request = Mock()
        
        with patch.object(self.dashboard, 'get_admin_interface', side_effect=Exception("System error")):
            response = await self.dashboard.system_status_api(mock_request)
            
            assert isinstance(response, web.Response)
            assert response.status == 500
            assert response.content_type == 'application/json'
            
            response_data = json.loads(response.text)
            assert response_data['success'] is False
            assert 'error' in response_data
    
    @pytest.mark.asyncio
    async def test_user_requests_api_success(self):
        """Test successful user requests API response"""
        mock_request = Mock()
        mock_request.query = {'user_guid': self.test_user_guid}
        
        with patch.object(self.dashboard, 'get_admin_interface', return_value=self.mock_admin):
            self.mock_admin.get_user_requests.return_value = self.mock_user_requests
            
            response = await self.dashboard.user_requests_api(mock_request)
            
            assert isinstance(response, web.Response)
            assert response.status == 200
            assert response.content_type == 'application/json'
            
            response_data = json.loads(response.text)
            assert response_data['success'] is True
            assert response_data['data'] == self.mock_user_requests
            
            self.mock_admin.get_user_requests.assert_called_once_with(self.test_user_guid)
    
    @pytest.mark.asyncio
    async def test_user_requests_api_missing_user_guid(self):
        """Test user requests API with missing user_guid parameter"""
        mock_request = Mock()
        mock_request.query = {}  # No user_guid
        
        response = await self.dashboard.user_requests_api(mock_request)
        
        assert isinstance(response, web.Response)
        assert response.status == 400
        assert response.content_type == 'application/json'
        
        response_data = json.loads(response.text)
        assert response_data['success'] is False
        assert 'user_guid parameter is required' in response_data['error']
    
    @pytest.mark.asyncio
    async def test_user_requests_api_error(self):
        """Test user requests API error handling"""
        mock_request = Mock()
        mock_request.query = {'user_guid': self.test_user_guid}
        
        with patch.object(self.dashboard, 'get_admin_interface', return_value=self.mock_admin):
            self.mock_admin.get_user_requests.side_effect = Exception("Database error")
            
            response = await self.dashboard.user_requests_api(mock_request)
            
            assert isinstance(response, web.Response)
            assert response.status == 500
            assert response.content_type == 'application/json'
            
            response_data = json.loads(response.text)
            assert response_data['success'] is False
            assert 'error' in response_data
    
    @pytest.mark.asyncio
    async def test_security_report_api_success(self):
        """Test successful security report API response"""
        mock_request = Mock()
        
        with patch.object(self.dashboard, 'get_admin_interface', return_value=self.mock_admin):
            self.mock_admin.get_security_report.return_value = self.mock_security_report
            
            response = await self.dashboard.security_report_api(mock_request)
            
            assert isinstance(response, web.Response)
            assert response.status == 200
            assert response.content_type == 'application/json'
            
            response_data = json.loads(response.text)
            assert response_data['success'] is True
            assert response_data['data'] == self.mock_security_report
    
    @pytest.mark.asyncio
    async def test_security_report_api_error(self):
        """Test security report API error handling"""
        mock_request = Mock()
        
        with patch.object(self.dashboard, 'get_admin_interface', side_effect=Exception("Security error")):
            response = await self.dashboard.security_report_api(mock_request)
            
            assert isinstance(response, web.Response)
            assert response.status == 500
            assert response.content_type == 'application/json'
            
            response_data = json.loads(response.text)
            assert response_data['success'] is False
            assert 'error' in response_data
    
    @pytest.mark.asyncio
    async def test_performance_report_api_success(self):
        """Test successful performance report API response"""
        mock_request = Mock()
        
        with patch.object(self.dashboard, 'get_admin_interface', return_value=self.mock_admin):
            self.mock_admin.get_performance_report.return_value = self.mock_performance_report
            
            response = await self.dashboard.performance_report_api(mock_request)
            
            assert isinstance(response, web.Response)
            assert response.status == 200
            assert response.content_type == 'application/json'
            
            response_data = json.loads(response.text)
            assert response_data['success'] is True
            assert response_data['data'] == self.mock_performance_report
    
    @pytest.mark.asyncio
    async def test_performance_report_api_error(self):
        """Test performance report API error handling"""
        mock_request = Mock()
        
        with patch.object(self.dashboard, 'get_admin_interface', side_effect=Exception("Performance error")):
            response = await self.dashboard.performance_report_api(mock_request)
            
            assert isinstance(response, web.Response)
            assert response.status == 500
            assert response.content_type == 'application/json'
            
            response_data = json.loads(response.text)
            assert response_data['success'] is False
            assert 'error' in response_data
    
    @pytest.mark.asyncio
    async def test_cancel_request_api_success(self):
        """Test successful request cancellation API"""
        test_scheduled_guid = str(uuid4())
        test_reason = "Test cancellation"
        
        mock_request = Mock()
        mock_request.post = AsyncMock(return_value={
            'scheduled_guid': test_scheduled_guid,
            'user_guid': self.test_user_guid,
            'reason': test_reason
        })
        
        with patch.object(self.dashboard, 'get_admin_interface', return_value=self.mock_admin):
            self.mock_admin.cancel_user_request.return_value = True
            
            response = await self.dashboard.cancel_request_api(mock_request)
            
            assert isinstance(response, web.Response)
            assert response.status == 200
            assert response.content_type == 'application/json'
            
            response_data = json.loads(response.text)
            assert response_data['success'] is True
            assert 'Request cancelled successfully' in response_data['message']
            
            self.mock_admin.cancel_user_request.assert_called_once_with(
                test_scheduled_guid, self.test_user_guid, test_reason
            )
    
    @pytest.mark.asyncio
    async def test_cancel_request_api_missing_data(self):
        """Test request cancellation API with missing data"""
        mock_request = Mock()
        mock_request.post = AsyncMock(return_value={
            'scheduled_guid': str(uuid4())
            # Missing user_guid and reason
        })
        
        response = await self.dashboard.cancel_request_api(mock_request)
        
        assert isinstance(response, web.Response)
        assert response.status == 400
        assert response.content_type == 'application/json'
        
        response_data = json.loads(response.text)
        assert response_data['success'] is False
        assert 'Missing required fields' in response_data['error']
    
    @pytest.mark.asyncio
    async def test_cancel_request_api_cancellation_failed(self):
        """Test request cancellation API when cancellation fails"""
        test_scheduled_guid = str(uuid4())
        test_reason = "Test cancellation"
        
        mock_request = Mock()
        mock_request.post = AsyncMock(return_value={
            'scheduled_guid': test_scheduled_guid,
            'user_guid': self.test_user_guid,
            'reason': test_reason
        })
        
        with patch.object(self.dashboard, 'get_admin_interface', return_value=self.mock_admin):
            self.mock_admin.cancel_user_request.return_value = False
            
            response = await self.dashboard.cancel_request_api(mock_request)
            
            assert isinstance(response, web.Response)
            assert response.status == 400
            assert response.content_type == 'application/json'
            
            response_data = json.loads(response.text)
            assert response_data['success'] is False
            assert 'Failed to cancel request' in response_data['error']
    
    @pytest.mark.asyncio
    async def test_cancel_request_api_error(self):
        """Test request cancellation API error handling"""
        mock_request = Mock()
        mock_request.post = AsyncMock(side_effect=Exception("Request parsing error"))
        
        response = await self.dashboard.cancel_request_api(mock_request)
        
        assert isinstance(response, web.Response)
        assert response.status == 500
        assert response.content_type == 'application/json'
        
        response_data = json.loads(response.text)
        assert response_data['success'] is False
        assert 'error' in response_data
    
    def test_dashboard_html_template_structure(self):
        """Test that dashboard HTML template has required structure"""
        # This test verifies the HTML template structure without rendering
        dashboard = ZairaControlDashboard()
        
        # Test that get_dashboard_html method exists and is callable
        assert hasattr(dashboard, 'get_dashboard_html')
        assert callable(dashboard.get_dashboard_html)
        
        # Test HTML generation
        html_content = dashboard.get_dashboard_html(self.mock_system_overview)
        
        # Verify essential HTML elements
        assert '<html>' in html_content
        assert '</html>' in html_content
        assert '<title>ZairaControl Dashboard</title>' in html_content
        assert 'Active Managers' in html_content
        assert 'Total Active Requests' in html_content
        assert 'System Health' in html_content
        
        # Verify data is properly inserted
        assert str(self.mock_system_overview['active_managers']) in html_content
        assert str(self.mock_system_overview['total_active_requests']) in html_content
        assert self.mock_system_overview['system_health'] in html_content
    
    def test_dashboard_html_template_escaping(self):
        """Test that dashboard HTML template properly escapes data"""
        dashboard = ZairaControlDashboard()
        
        # Mock data with potentially dangerous content
        dangerous_overview = {
            'active_managers': 5,
            'total_active_requests': 25,
            'total_users': 10,
            'system_health': '<script>alert("xss")</script>',  # XSS attempt
            'memory_usage_mb': 150.5,
            'uptime_seconds': 3600,
            'recent_errors': ['<script>alert("error")</script>']
        }
        
        html_content = dashboard.get_dashboard_html(dangerous_overview)
        
        # Verify dangerous content is escaped
        assert '<script>alert("xss")</script>' not in html_content
        assert '&lt;script&gt;alert("xss")&lt;/script&gt;' in html_content
    
    def test_dashboard_css_and_javascript(self):
        """Test that dashboard includes CSS and JavaScript for functionality"""
        dashboard = ZairaControlDashboard()
        html_content = dashboard.get_dashboard_html(self.mock_system_overview)
        
        # Verify CSS is included
        assert '<style>' in html_content
        assert '</style>' in html_content
        assert 'body {' in html_content
        assert 'font-family:' in html_content
        
        # Verify JavaScript is included
        assert '<script>' in html_content
        assert '</script>' in html_content
        assert 'function' in html_content
        assert 'setInterval' in html_content  # For auto-refresh
    
    def test_dashboard_responsive_design(self):
        """Test that dashboard includes responsive design elements"""
        dashboard = ZairaControlDashboard()
        html_content = dashboard.get_dashboard_html(self.mock_system_overview)
        
        # Verify responsive meta tag
        assert '<meta name="viewport"' in html_content
        assert 'width=device-width' in html_content
        
        # Verify responsive CSS
        assert '@media' in html_content or 'max-width' in html_content
    
    def test_dashboard_accessibility(self):
        """Test that dashboard includes accessibility features"""
        dashboard = ZairaControlDashboard()
        html_content = dashboard.get_dashboard_html(self.mock_system_overview)
        
        # Verify semantic HTML elements
        assert '<main>' in html_content
        assert '<section>' in html_content or '<article>' in html_content
        assert 'aria-label' in html_content or 'role=' in html_content
        
        # Verify proper heading structure
        assert '<h1>' in html_content
        assert '<h2>' in html_content or '<h3>' in html_content
    
    @pytest.mark.asyncio
    async def test_error_response_format(self):
        """Test that error responses follow consistent format"""
        dashboard = ZairaControlDashboard()
        
        # Test various error scenarios
        mock_request = Mock()
        
        # Test with admin interface error
        with patch.object(dashboard, 'get_admin_interface', side_effect=Exception("Test error")):
            response = await dashboard.system_status_api(mock_request)
            
            assert response.status == 500
            response_data = json.loads(response.text)
            
            # Verify error response structure
            assert 'success' in response_data
            assert response_data['success'] is False
            assert 'error' in response_data
            assert isinstance(response_data['error'], str)
    
    @pytest.mark.asyncio
    async def test_dashboard_handles_empty_data(self):
        """Test that dashboard handles empty data gracefully"""
        dashboard = ZairaControlDashboard()
        
        # Mock empty system overview
        empty_overview = {
            'active_managers': 0,
            'total_active_requests': 0,
            'total_users': 0,
            'system_health': 'unknown',
            'memory_usage_mb': 0.0,
            'uptime_seconds': 0,
            'recent_errors': []
        }
        
        html_content = dashboard.get_dashboard_html(empty_overview)
        
        # Verify it handles zero values gracefully
        assert '0' in html_content
        assert 'unknown' in html_content
        assert 'No recent errors' in html_content or 'recent_errors' in html_content
    
    @pytest.mark.asyncio
    async def test_concurrent_dashboard_requests(self):
        """Test dashboard handling concurrent requests"""
        dashboard = ZairaControlDashboard()
        
        with patch.object(dashboard, 'get_admin_interface', return_value=self.mock_admin):
            self.mock_admin.get_system_overview.return_value = self.mock_system_overview
            
            # Create multiple concurrent requests
            mock_requests = [Mock() for _ in range(10)]
            
            async def make_request(request):
                return await dashboard.system_status_api(request)
            
            # Execute concurrent requests
            tasks = [asyncio.create_task(make_request(req)) for req in mock_requests]
            responses = await asyncio.gather(*tasks)
            
            # Verify all requests completed successfully
            assert len(responses) == 10
            for response in responses:
                assert response.status == 200
                response_data = json.loads(response.text)
                assert response_data['success'] is True