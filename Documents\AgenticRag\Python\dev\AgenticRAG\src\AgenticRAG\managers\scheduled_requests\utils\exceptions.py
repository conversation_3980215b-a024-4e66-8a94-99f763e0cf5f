from imports import *

class ScheduledRequestError(Exception):
    """Base exception for scheduled request operations"""
    pass

class UserQuotaExceededError(ScheduledRequestError):
    """Raised when user exceeds their allocated quota"""
    def __init__(self, user_guid: str, quota_type: str, current_value: int, max_value: int):
        self.user_guid = user_guid
        self.quota_type = quota_type
        self.current_value = current_value
        self.max_value = max_value
        super().__init__(f"User {user_guid} exceeded {quota_type} quota: {current_value}/{max_value}")

class UserRateLimitExceededError(ScheduledRequestError):
    """Raised when user exceeds rate limit"""
    def __init__(self, user_guid: str, rate_limit: str, retry_after: float):
        self.user_guid = user_guid
        self.rate_limit = rate_limit
        self.retry_after = retry_after
        super().__init__(f"User {user_guid} exceeded {rate_limit} rate limit. Retry after {retry_after} seconds")

class UnauthorizedScheduledRequestError(ScheduledRequestError):
    """Raised when user tries to access unauthorized scheduled request"""
    def __init__(self, user_guid: str, scheduled_guid: str):
        self.user_guid = user_guid
        self.scheduled_guid = scheduled_guid
        super().__init__(f"User {user_guid} not authorized to access scheduled request {scheduled_guid}")

class ScheduledRequestNotFoundError(ScheduledRequestError):
    """Raised when scheduled request is not found"""
    def __init__(self, scheduled_guid: str):
        self.scheduled_guid = scheduled_guid
        super().__init__(f"Scheduled request {scheduled_guid} not found")

class UserManagerNotFoundError(ScheduledRequestError):
    """Raised when user manager is not found or not initialized"""
    def __init__(self, user_guid: str):
        self.user_guid = user_guid
        super().__init__(f"User manager for {user_guid} not found or not initialized")

class ResourceExhaustionError(ScheduledRequestError):
    """Raised when system resources are exhausted"""
    def __init__(self, resource_type: str, details: str = ""):
        self.resource_type = resource_type
        self.details = details
        super().__init__(f"Resource exhaustion: {resource_type}. {details}")


class SecurityValidationError(ScheduledRequestError):
    """Raised when security validation fails"""
    def __init__(self, validation_type: str, user_guid: str, details: str = ""):
        self.validation_type = validation_type
        self.user_guid = user_guid
        self.details = details
        super().__init__(f"Security validation failed for {validation_type} (user: {user_guid}): {details}")