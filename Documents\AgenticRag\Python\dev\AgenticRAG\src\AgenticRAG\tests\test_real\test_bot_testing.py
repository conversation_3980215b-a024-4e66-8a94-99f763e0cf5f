#!/usr/bin/env python3
"""
Test the MyBot_Testing automated response functionality
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

from imports import *
from managers.manager_logfire import LogFire
import pytest
import asyncio
from uuid import uuid4

from endpoints.testing_endpoint import MyBot_Testing
from userprofiles.ZairaUser import <PERSON><PERSON><PERSON><PERSON><PERSON>, PERMISSION_LEVELS
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest

@pytest.mark.asyncio
class TestBotTesting:
    """Test the automated bot response functionality"""
    
    def setup_method(self):
        """Set up test fixtures"""
        self.test_user_guid = str(uuid4())
        self.test_scheduled_guid = str(uuid4())
    
    async def test_bot_testing_responses(self):
        """Test that MyBot_Testing generates appropriate responses"""
        
        # Create testing bot
        bot = MyBot_Testing(None, "Testing")
        
        # Test various approval scenarios
        test_cases = [
            {
                "request": "Ik heb de volgende mail opgesteld. Wil je dat ik deze goedkeur voor verzending? (j/n)",
                "expected_response": "j",
                "description": "Email approval request"
            },
            {
                "request": "Het is mij niet duidelijk naar welk email adres de mail gestuurd moet worden?",
                "expected_response": "<EMAIL>",
                "description": "Email address request"
            },
            {
                "request": "Je e-mail adres is nog niet bekend bij ons. Van welk e-mailadres moet de mail verzonden worden?",
                "expected_response": "<EMAIL>",
                "description": "Sender email request"
            },
            {
                "request": "Approve this action? (yes/no)",
                "expected_response": "ja",
                "description": "General approval"
            }
        ]
        
        for case in test_cases:
            LogFire.log("DEBUG", f"[TEST] Testing: {case['description']}")
            LogFire.log("DEBUG", f"[TEST] Request: {case['request']}")
            
            # Test the response generation
            response = await bot._generate_test_response(case["request"])
            
            LogFire.log("DEBUG", f"[TEST] Response: {response}")
            
            # Verify response is appropriate
            assert response is not None, f"Response should not be None for {case['description']}"
            assert len(response.strip()) > 0, f"Response should not be empty for {case['description']}"
            
            # For email approval, should respond with "j"
            if "goedkeur" in case["request"].lower() and "j/n" in case["request"]:
                assert response.lower() == "j", f"Email approval should respond with 'j', got '{response}'"
            
            # For email address requests, should contain email
            if "email" in case["request"].lower() and "adres" in case["request"].lower():
                assert "@" in response, f"Email address request should contain @, got '{response}'"
        
        LogFire.log("DEBUG", "[TEST] All bot response tests passed!")
    
    async def test_bot_fallback_responses(self):
        """Test fallback responses when LLM is not available"""
        
        # Create testing bot
        bot = MyBot_Testing(None, "Testing")
        
        # Test fallback scenarios
        test_cases = [
            {
                "request": "Wil je dat ik deze goedkeur voor verzending? (j/n)",
                "expected_pattern": "j",
                "description": "Email approval fallback"
            },
            {
                "request": "email address needed",
                "expected_pattern": "<EMAIL>",
                "description": "Email address fallback"
            },
            {
                "request": "Do you want to continue? (yes/no)",
                "expected_pattern": "ja",
                "description": "General yes/no fallback"
            }
        ]
        
        for case in test_cases:
            LogFire.log("DEBUG", f"[TEST] Testing fallback: {case['description']}")
            
            # Test the fallback response generation
            response = bot._get_fallback_response(case["request"])
            
            LogFire.log("DEBUG", f"[TEST] Fallback response: {response}")
            
            # Verify response is appropriate
            assert response is not None, f"Fallback response should not be None for {case['description']}"
            assert len(response.strip()) > 0, f"Fallback response should not be empty for {case['description']}"
        
        LogFire.log("DEBUG", "[TEST] All fallback response tests passed!")
    
    async def test_bot_name_behavior(self):
        """Test that bot only provides automated responses when name is 'Testing'"""
        
        # Test with "Testing" name
        testing_bot = MyBot_Testing(None, "Testing")
        assert testing_bot.name == "Testing"
        
        # Test with different name
        other_bot = MyBot_Testing(None, "Other")
        assert other_bot.name == "Other"
        
        # The automated response should only work for "Testing" name
        # This is verified by the request_human_in_the_loop_internal method
        # which checks self.name == "Testing"
        
        LogFire.log("DEBUG", "[TEST] Bot name behavior test passed!")
    
    async def test_bot_integration_with_user_message(self):
        """Test bot integration with user message flow"""
        
        # This is a conceptual test - in practice, the bot would be used
        # within the full system context
        
        bot = MyBot_Testing(None, "Testing")
        
        # Test that bot can generate responses for common email scenarios
        email_scenarios = [
            "Ik heb de volgende mail opgesteld. Wil je dat ik deze goedkeur voor verzending? (j/n)\n\nFrom: <EMAIL>\nTo: <EMAIL>\nSubject: Test\n\nTest message",
            "Je e-mail adres is nog niet bekend bij ons. Van welk e-mailadres moet de mail verzonden worden?",
            "Het is mij niet duidelijk naar welk email adres de mail gestuurd moet worden?"
        ]
        
        for scenario in email_scenarios:
            LogFire.log("DEBUG", f"[TEST] Testing email scenario: {scenario[:50]}...")
            
            response = await bot._generate_test_response(scenario)
            
            LogFire.log("DEBUG", f"[TEST] Generated response: {response}")
            
            # Verify response is appropriate for email scenarios
            assert response is not None
            assert len(response.strip()) > 0
            
            # Email scenarios should result in appropriate responses
            if "goedkeur" in scenario.lower():
                assert response.lower() == "j"
            elif "email" in scenario.lower():
                assert "@" in response or response.lower() == "<EMAIL>"
        
        LogFire.log("DEBUG", "[TEST] Email scenario integration test passed!", severity="debug")