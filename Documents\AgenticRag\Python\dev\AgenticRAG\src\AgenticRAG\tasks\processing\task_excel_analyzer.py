from imports import *

from langchain_core.tools import BaseTool
from langchain_experimental.agents.agent_toolkits import create_pandas_dataframe_agent
from langchain_openai import ChatOpenAI
from langchain.agents import AgentType
from typing import Optional, Any
import pandas as pd
import json
import sys
import os
from datetime import datetime

from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTask_Create_agent, SupervisorTaskState
from managers.manager_users import ZairaUserManager
from managers.manager_logfire import LogFire

class ExcelAnalyzerTool(BaseTool):
    """Tool for analyzing Excel/CSV data using pandas and LangChain agents"""
    name: str = "excel_analyzer_tool"
    description: str = "Analyzes Excel/CSV data and answers questions about the dataset"
    df: Optional[Any] = None
    agent: Optional[Any] = None
    
    def _run(self, query: str, file_path: str = None, state: SupervisorTaskState = None) -> str:
        """Not implemented for sync"""
        raise NotImplementedError("Use async version")
    
    async def _arun(self, query: str, file_path: str = None, state: SupervisorTaskState = None) -> str:
        """Analyze data and answer questions"""
        try:
            # Handle case where state might be passed as dict
            if isinstance(state, dict):
                from managers.manager_supervisors import SupervisorTaskState
                state = SupervisorTaskState(**state)
            
            # Load data if file path provided
            if file_path and self.df is None:
                await self._load_data(file_path)
            
            # Use default dataset if no data loaded
            if self.df is None:
                await self._load_default_data()
            
            # Create agent if not exists
            if self.agent is None:
                await self._create_agent()
            
            # Execute query with proper timeout handling
            result = await self._execute_query(query)
            
            # Conditional logging
            if not self._is_debugging():
                LogFire.log("TASK", f"Excel analysis completed: {query[:50]}...")
            return result
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "excel_analyzer_tool", state)
            return f"Error analyzing data: {str(e)}"
    
    async def _load_data(self, file_path: str):
        """Load data from Excel or CSV file"""
        try:
            if file_path.endswith('.csv'):
                self.df = pd.read_csv(file_path)
            elif file_path.endswith(('.xlsx', '.xls')):
                self.df = pd.read_excel(file_path)
            else:
                raise ValueError("Unsupported file format. Use CSV or Excel files.")
            
            # Conditional logging
            if not self._is_debugging():
                LogFire.log("INIT", f"Loaded data from {file_path}: {self.df.shape[0]} rows, {self.df.shape[1]} columns")
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "load_data", None)
            raise
    
    async def _load_default_data(self):
        """Load default dataset for demonstration"""
        try:
            sample_data = {
                'name': ['Alice', 'Bob', 'Charlie', 'Diana'],
                'age': [25, 30, 35, 28],
                'salary': [50000, 60000, 70000, 55000],
                'department': ['Engineering', 'Sales', 'Marketing', 'Engineering']
            }
            self.df = pd.DataFrame(sample_data)
            # Conditional logging
            if not self._is_debugging():
                LogFire.log("INIT", "Loaded default sample dataset")
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "load_default_data", None)
            raise
    
    async def _create_agent(self):
        """Create pandas dataframe agent with proper safeguards"""
        try:
            # Check if we're in debug mode
            is_debugging = self._is_debugging()
            
            # Create agent with debugger-friendly settings
            self.agent = create_pandas_dataframe_agent(
                ChatOpenAI(temperature=0, model="gpt-4o-mini", request_timeout=30),  # Reduced timeout
                self.df,
                verbose=False,  # Always False to prevent output issues
                agent_type=AgentType.OPENAI_FUNCTIONS,
                handle_parsing_errors=True,
                max_iterations=1 if is_debugging else 2,  # Single iteration when debugging
                early_stopping_method="generate",  # Stop early if possible
                return_intermediate_steps=False  # Don't return intermediate steps
            )
            
            # Conditional logging - skip in debug mode
            if not is_debugging:
                LogFire.log("INIT", "Created pandas dataframe agent")
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "create_agent", None)
            raise
    
    async def _execute_query(self, query: str) -> str:
        """Execute query using the pandas agent with timeout protection"""
        try:
            import asyncio
            
            # Check if we're in debug mode
            is_debugging = self._is_debugging()
            
            if is_debugging:
                # Direct synchronous call when debugging - no executor, no timeout
                result = self._safe_agent_invoke(query)
            else:
                # Use executor with timeout for normal execution
                loop = asyncio.get_event_loop()
                # Simplified execution without nested async function
                result = await asyncio.wait_for(
                    loop.run_in_executor(None, self._safe_agent_invoke, query),
                    timeout=10.0  # Reduced timeout
                )
            
            # Extract output from agent result
            if isinstance(result, dict) and 'output' in result:
                return str(result['output'])
            else:
                return str(result)
                
        except asyncio.TimeoutError:
            if not self._is_debugging():
                LogFire.log("ERROR", f"Query execution timed out: {query[:50]}...")
            return f"Query timed out. Try a simpler question about the data."
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "execute_query", None)
            return f"Query execution failed: {str(e)}"
    
    def _safe_agent_invoke(self, query: str):
        """Safely invoke agent with additional error handling"""
        try:
            # Add query validation to prevent problematic queries
            if len(query.strip()) == 0:
                return "Please provide a valid query about the data."
            
            # Invoke the agent
            result = self.agent.invoke(query)
            return result
            
        except Exception as e:
            if not self._is_debugging():
                LogFire.log("ERROR", f"Agent invoke failed: {str(e)}")
            return f"Unable to process query: {str(e)}"
    
    def _is_debugging(self) -> bool:
        """Check if we're running in debug mode (F5 in VS Code or other debuggers)"""
        # Check for common debugger indicators
        if sys.gettrace() is not None:
            return True
        
        # Check for VS Code debugger
        if 'debugpy' in sys.modules or 'pydevd' in sys.modules:
            return True
        
        # Check for Claude Code environment with DEBUG flag
        if os.environ.get('CLAUDE_CODE') == '1' and os.environ.get('DEBUG') == 'True':
            return True
        
        return False


async def create_supervisor_excel_analyzer() -> SupervisorSupervisor:
    """Create the Excel analyzer supervisor with data analysis tools"""
    class TaskCreator:
        excel_analyzer_task: SupervisorTask_SingleAgent = None
            
        async def create_tasks(self):
            # Create Excel analyzer tool
            excel_tool = ExcelAnalyzerTool()
            
            # Register the Excel analyzer task
            self.excel_analyzer_task = SupervisorManager.register_task(SupervisorTask_SingleAgent(
                name="excel_analyzer_expert", 
                tools=[excel_tool], 
                prompt_id="Excel_Analyzer_Prompt"
            ))

        async def create_supervisor(self) -> SupervisorSupervisor:
            return SupervisorManager.register_supervisor(SupervisorSupervisor(
                name="excel_analyzer_supervisor",
                prompt="""You are an expert supervisor for Excel and CSV data analysis. 
                You coordinate data analysis tasks and provide insights from spreadsheet data.
                Use the available tools to analyze data, answer questions, and generate reports.
                Always ensure data is loaded before attempting analysis.
                """
            )) \
            .add_task(self.excel_analyzer_task, priority=1) \
            .compile()

    creator = TaskCreator()
    await creator.create_tasks()
    return await creator.create_supervisor()
   