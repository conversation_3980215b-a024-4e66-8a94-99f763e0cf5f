"""
Debug output capture module for AgenticRAG.

This module captures stdout/stderr to debug_trace.log for debugging purposes.
The capture is automatically disabled in the following environments:
- Testing (pytest detected or PYTEST_CURRENT_TEST set)
- Claude environment (CLAUDE_CODE or ANTHROPIC_USER_ID set)

This ensures debug_trace.log is only modified during regular development/production runs.
"""
import sys
import os
import re
from datetime import datetime
from typing import TextIO
from pathlib import Path

class TeeOutput:
    """Tee class that writes to both console and file simultaneously"""
    
    def __init__(self, original_stream: TextIO, log_file_path: str):
        self.original_stream = original_stream
        self.log_file_path = log_file_path
        self.log_file = None
        self._open_log_file()
    
    def _open_log_file(self):
        """Open or reopen the log file"""
        try:
            if self.log_file and not self.log_file.closed:
                self.log_file.close()
            self.log_file = open(self.log_file_path, 'a', encoding='utf-8')
            
            # Get calling script name for context
            import __main__
            script_name = "unknown"
            if hasattr(__main__, '__file__') and __main__.__file__:
                script_name = os.path.basename(__main__.__file__)
            
        except Exception as e:
            self.log_file = None
    
    def write(self, text: str):
        """Write to both original stream and log file"""
        # Write to original stream (console)
        if self.original_stream:
            self.original_stream.write(text)
            self.original_stream.flush()
        
        # Write to log file with timestamp (strip ANSI color codes)
        if self.log_file and not self.log_file.closed:
            try:
                # Add timestamp to each line
                if text.strip():  # Only add timestamp to non-empty lines
                    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S.%f")[:-3]
                    # Split by lines and add timestamp to each
                    lines = text.splitlines(keepends=True)
                    for line in lines:
                        if line.strip():  # Only process non-empty lines
                            # Strip ANSI color codes before writing to file
                            clean_line = self._strip_ansi_codes(line)
                            self.log_file.write(f"[{timestamp}] {clean_line}")
                        else:
                            self.log_file.write(line)
                else:
                    # Strip ANSI codes from text before writing
                    clean_text = self._strip_ansi_codes(text)
                    self.log_file.write(clean_text)
                self.log_file.flush()
            except Exception as e:
                # If writing to log file fails, continue with console output
                pass
        else:
            if text.strip():  # Only log for non-empty content
                pass  # Content already handled in the try block above
    
    def flush(self):
        """Flush both streams"""
        if self.original_stream:
            self.original_stream.flush()
        if self.log_file and not self.log_file.closed:
            try:
                self.log_file.flush()
            except Exception:
                pass
    
    def _strip_ansi_codes(self, text: str) -> str:
        """Remove ANSI color codes and escape sequences from text"""
        # Pattern to match ANSI escape sequences
        ansi_pattern = re.compile(r'\x1b\[[0-9;]*[mGKH]')
        return ansi_pattern.sub('', text)
    
    def close(self):
        """Close the log file"""
        if self.log_file and not self.log_file.closed:
            try:
                self.log_file.close()
            except Exception:
                pass
    
    def __getattr__(self, name):
        """Delegate other attributes to original stream"""
        return getattr(self.original_stream, name)

# Global flag to prevent double initialization
_debug_capture_initialized = False

def _determine_log_file_path():
    """Determine the appropriate log file path based on script name"""
    
    # Get script name from __main__
    import __main__
    script_name = None
    if hasattr(__main__, '__file__') and __main__.__file__:
        script_name = os.path.basename(__main__.__file__)
    
    # Use debug_trace.log ONLY for production scripts
    production_scripts = ['dev.py', 'dev_run.py', 'production.py']
    if script_name in production_scripts:
        # Production script: use debug_trace.log
        try:
            from globals import BASE_DIR, Globals
            base_dir = BASE_DIR()
            if Globals.is_docker() == True:
                return "/app/debug_trace.log"
            else:
                return base_dir / "AgenticRAG" / "debug_trace.log"
        except Exception as e:
            return "./debug_trace.log"
    
    # Everything else: use test/script logging
    return _get_test_script_log_path()

def _get_test_script_log_path():
    """Get the appropriate log path for test or script execution"""
    
    # Get script name from __main__
    import traceback
    import __main__
    
    script_name = None
    function_name = None
    
    # Try to get script name from __main__
    if hasattr(__main__, '__file__') and __main__.__file__:
        script_name = os.path.basename(__main__.__file__).replace('.py', '')
    
    # Check stack trace for test_real functions
    stack_trace = traceback.format_stack()
    for frame in stack_trace:
        if 'test_real' in frame:
            # Extract function name from test_real frame
            lines = frame.split('\n')
            for line in lines:
                if 'def test_' in line:
                    function_name = line.strip().split('def ')[1].split('(')[0]
                    break
                elif 'test_real_' in line or 'test_basic_' in line:
                    # Try to extract function name from file path or call
                    parts = line.split('/')
                    for part in parts:
                        if part.startswith('test_') and '.py' in part:
                            function_name = part.replace('.py', '')
                            break
            break
    
    # Determine base directory for tests
    try:
        from globals import BASE_DIR
        base_dir = BASE_DIR() / "AgenticRAG" / "tests"
    except Exception:
        base_dir = Path("./tests")
    
    # Create the log path based on detection
    # PRIORITY 1: Check if script name itself is a test_real file
    if script_name and script_name.startswith('test_real_'):
        # test_real script: timestamped subdirectory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_dir = base_dir / "test_real" / "logs" / script_name
        log_dir.mkdir(parents=True, exist_ok=True)
        return log_dir / f"{timestamp}.log"
    
    # PRIORITY 2: Check for test_real function calls within other scripts
    elif function_name and 'test_real' in str(stack_trace):
        # test_real function: timestamped subdirectory
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_dir = base_dir / "test_real" / "logs" / function_name
        log_dir.mkdir(parents=True, exist_ok=True)
        return log_dir / f"{timestamp}.log"
    
    # PRIORITY 3: Other scripts
    elif script_name:
        # Other script: tests/logs/script_name.log (no timestamp, overwrites)
        log_dir = base_dir / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        return log_dir / f"{script_name}.log"
    
    else:
        # Fallback: tests/logs/unknown.log
        log_dir = base_dir / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        return log_dir / "unknown.log"

def setup_debug_capture():
    """Setup stdout/stderr capture to debug_trace.log"""
    global _debug_capture_initialized
    
    # Prevent double initialization
    if _debug_capture_initialized:
        return
    
    # Setup file path and TeeOutput immediately to capture all messages
    # Determine log file path based on environment
    debug_file = _determine_log_file_path()
    
    # Clear the debug file at startup
    try:
        with open(str(debug_file), 'w') as f:
            f.write(f"=== DEBUG TRACE STARTED at {datetime.now()} ===\n")
    except Exception as e:
        return
    
    # Replace stdout and stderr with Tee objects EARLY to capture everything
    sys.stdout = TeeOutput(sys.stdout, str(debug_file))
    sys.stderr = TeeOutput(sys.stderr, str(debug_file))
    
    # Mark as initialized
    _debug_capture_initialized = True
    
    
    # Now print environment info (will be captured)
    
    # Check if we're in a testing environment
    # Don't capture debug output in these environments - tests have their own logging
    
    # Check for pytest - but allow test_real to proceed
    if 'pytest' in sys.modules:
        # Allow test_real to use debug capture even with pytest imported
        import traceback
        stack_trace = traceback.format_stack()
        if not any('test_real' in frame for frame in stack_trace):
            return
        else:
            pass
    
    # Check for PYTEST_CURRENT_TEST environment variable
    if os.environ.get('PYTEST_CURRENT_TEST'):
        return
    
    # Remove this redundant check - already handled above with pytest detection
    
    # Check if test capture is already active (highest priority check)
    # AVOID circular import by checking global state instead of importing test modules
    try:
        # Check for test environment using our global detector (import-safe)
        try:
            from etc.test_environment_detector import is_test_environment_active
            if is_test_environment_active():
                return
        except ImportError:
            pass
        
        # Check for existing test capture through environment variable instead of import
        if os.environ.get('TEST_CAPTURE_ACTIVE') == '1':
            return
        
        # Legacy check - only import if absolutely necessary and safe
        if 'test_debug_capture' not in sys.modules:
            try:
                from tests.test_real.test_debug_capture import is_test_capture_active
                if is_test_capture_active():
                    return
            except (ImportError, AttributeError):
                # Test modules may not be available or may cause circular import
                pass
    except Exception as e:
        pass
    
    # This check is redundant - already handled above
    
    # Check for test environment variables
    pytest_current_test = os.environ.get('PYTEST_CURRENT_TEST')
    if pytest_current_test:
        return
    
    # Check for custom test environment flag
    if os.environ.get('PYTEST_RUNNING'):
        test_name = os.environ.get('TEST_NAME', 'unknown')
        return
    
    # Check for Claude CLI environment (not just VS Code debugging)
    # ALWAYS ENABLE debug_trace.log for Claude F5 debugging communication
    # Only skip for actual CLI commands, but allow F5 debugging
    claude_code_entrypoint = os.environ.get('CLAUDE_CODE_ENTRYPOINT')
    anthropic_user_id = os.environ.get('ANTHROPIC_USER_ID')
    
    # Enable debug_trace.log in Claude environment for F5 debugging
    # Skip only for specific CLI operations that don't need debug output
    if claude_code_entrypoint == 'cli' and not os.environ.get('ENABLE_DEBUG_TRACE'):
        cleanup_debug_capture()
        return
    
    # Always enable for Claude F5 debugging and VS Code debugging
    if anthropic_user_id or claude_code_entrypoint:
        pass

def cleanup_debug_capture():
    """Restore original stdout/stderr"""
    global _debug_capture_initialized
    
    if hasattr(sys.stdout, 'original_stream'):
        sys.stdout.close()
        sys.stdout = sys.stdout.original_stream
    
    if hasattr(sys.stderr, 'original_stream'):
        sys.stderr.close()
        sys.stderr = sys.stderr.original_stream
    
    # Reset initialization flag
    _debug_capture_initialized = False
    
    print("Debug trace capture deactivated")

# Auto-setup when imported - DISABLED to prevent double initialization
# Call setup_debug_capture() explicitly from imports.py instead
# if __name__ != "__main__":
#     setup_debug_capture()