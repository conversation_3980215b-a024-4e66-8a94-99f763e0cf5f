"""
Comprehensive end-to-end system integration test covering the complete AgenticRAG workflow.
This test simulates real-world usage scenarios from user interaction through to task completion,
including RAG retrieval, database operations, scheduled tasks, and multi-platform communication.
"""
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from managers.manager_users import ZairaUserManager
from managers.manager_supervisors import SupervisorManager, SupervisorTaskState
from managers.scheduled_requests import ScheduledRequestPersistenceManager
from managers.manager_postgreSQL import PostgreSQLManager
from managers.manager_qdrant import QDrantManager
from managers.manager_retrieval import RetrievalManager
from userprofiles.ZairaUser import ZairaUser
from userprofiles.ZairaUser import PERMISSION_LEVELS
from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest
from userprofiles.LongRunningZairaRequest import LongRunningZairaRequest
from userprofiles.ZairaMessage import ZairaMessage
from endpoints.mybot_generic import MyBot_Generic
from tasks.etc.task_chat_session import create_task_manage_chat_sessions
from tasks.inputs.task_scheduled_request_manager import (
    create_task_scheduled_request_manager, CreateScheduledRequestTool,
    ListScheduledRequestsTool, CancelScheduledRequestTool
)
from langchain_core.messages import HumanMessage, AIMessage
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4
import asyncio
from datetime import datetime

class TestEndToEndSystemIntegration:
    """Comprehensive end-to-end system integration test"""
    
    def setup_method(self):
        """Set up complete system test environment"""
        # Reset all singleton instances
        ZairaUserManager._instance = None
        SupervisorManager._instance = None
        ScheduledRequestPersistenceManager._instance = None
        PostgreSQLManager._instance = None
        QDrantManager._instance = None
        RetrievalManager._instance = None
        
        # Set up test data
        self.test_user_data = {
            'username': 'e2e_test_user',
            'guid': uuid4(),
            'device_guid': uuid4(),
            'rank': PERMISSION_LEVELS.USER,
            'real_name': 'End-to-End Test User',
            'email': '<EMAIL>',
            'platform': 'Discord'
        }
        
        # Mock external services
        self.mock_bot = MagicMock(spec=MyBot_Generic)
        self.mock_bot.name = "Discord"
        self.mock_bot.parent_instance = MagicMock()
        self.mock_bot.send_reply = AsyncMock()
        self.mock_bot.send_broadcast = AsyncMock()
        
    def teardown_method(self):
        """Clean up after tests"""
        # Reset all singleton instances
        ZairaUserManager._instance = None
        SupervisorManager._instance = None
        ScheduledRequestPersistenceManager._instance = None
        PostgreSQLManager._instance = None
        QDrantManager._instance = None
        RetrievalManager._instance = None
    
    @pytest.mark.asyncio
    async def test_complete_user_interaction_workflow(self):
        """Test complete user interaction workflow from registration to task completion"""
        with patch('managers.scheduled_requests.ScheduledRequestPersistenceManager.get_instance') as mock_get_persistence, \
             patch('userprofiles.ZairaUser.LogFire.log'), \
             patch('managers.manager_supervisors.LogFire.log'), \
             patch('userprofiles.ZairaMessage.LogFire.log'), \
             patch('userprofiles.LongRunningZairaRequest.LogFire.log'), \
             patch('etc.helper_functions.exception_triggered') as mock_exception:
            
            # Mock persistence manager
            mock_persistence = AsyncMock()
            mock_persistence.create_task = AsyncMock()
            mock_persistence.get_active_requests = AsyncMock(return_value=[])
            mock_persistence.save_task = AsyncMock()
            mock_get_persistence.return_value = mock_persistence
            
            # Step 1: System Initialization
            user_manager = ZairaUserManager.get_instance()
            supervisor_manager = SupervisorManager.get_instance()
            
            assert user_manager is not None
            assert supervisor_manager is not None
            assert len(user_manager.users) == 0
            assert len(supervisor_manager.tasks) == 0
            
            # Step 2: User Registration and Setup
            user = await ZairaUserManager.add_user(
                username=self.test_user_data['username'],
                rank=self.test_user_data['rank'],
                guid=self.test_user_data['guid'],
                device_guid=self.test_user_data['device_guid']
            )
            
            # Update user with additional information
            await ZairaUserManager.update_user(
                str(user.GUID),
                real_name=self.test_user_data['real_name'],
                email=self.test_user_data['email'],
                platform=self.test_user_data['platform']
            )
            
            assert user.username == self.test_user_data['username']
            assert user.real_name == self.test_user_data['real_name']
            assert user.email == self.test_user_data['email']
            
            # Step 3: Register Core System Tasks
            chat_session_task = await create_task_manage_chat_sessions()
            scheduled_request_manager = create_task_scheduled_request_manager()
            supervisor_manager.register_task(scheduled_request_manager)
            
            assert chat_session_task.name == "manage_sessions_task"
            assert scheduled_request_manager.name == "scheduled_request_manager"
            assert len(supervisor_manager.tasks) >= 2
            
            # Step 4: Simulate Real User Interactions
            
            # 4a: User sends a test query
            test_query = "Search for user documentation"
            
            with patch('userprofiles.LongRunningZairaRequest.LongRunningZairaRequest') as mock_long_task:
                mock_task_instance = MagicMock()
                mock_task_instance.run_task = AsyncMock()
                mock_task_instance.await_status_complete = AsyncMock()
                mock_task_instance.user = user
                mock_task_instance.human_in_the_loop_callback = None
                mock_long_task.return_value = mock_task_instance
                
                with patch('userprofiles.ZairaUser.Globals.is_debug', return_value=True):
                    await user.on_message(
                        complete_message=test_query,
                        calling_bot=self.mock_bot,
                        attachments=[]
                    )
                
                # Verify task was created and executed
                assert user.my_task == mock_task_instance
                mock_task_instance.run_task.assert_called_once()
                
                # Verify message was added to chat history
                assert user.chat_history[user.session_guid].message_count == 1
                first_message = user.chat_history[user.session_guid].messages[0]
                assert test_query in first_message.content
                assert first_message.message_type == "user"
            
            # 4b: User creates a new chat session
            user.my_task = None  # Reset task state
            
            # Create a message and add it to history (simulating bot response)
            bot_response = ZairaMessage.create_assistant_message(
                "I can help you with many tasks! You can create scheduled tasks, manage chat sessions, and more.",
                user.session_guid,
                user.session_guid,
                100
            )
            user.chat_history[user.session_guid].add_message(bot_response)
            
            # Test chat session management
            state = SupervisorTaskState(
                user_guid=str(user.GUID),
                original_input="Create a new chat session",
                additional_input={},
                messages=[HumanMessage(content="Create a new chat session")]
            )
            
            # Mock chat session task execution
            with patch('tasks.etc.task_chat_session.ZairaUserManager.find_user', new_callable=AsyncMock) as mock_find_user:
                mock_find_user.return_value = user
                
                # Execute chat session creation
                from tasks.etc.task_chat_session import new_chat_session_tool
                new_session_result = await new_chat_session_tool._arun(state)
                
                assert "New chat history session has been started with GUID" in new_session_result
                # User should now have multiple sessions
                assert len(user.chat_history) >= 2
            
            # 4c: User schedules a task
            with patch('tasks.inputs.task_scheduled_request_manager.ScheduledRequestPersistenceManager') as mock_sched_persistence:
                mock_sched_instance = AsyncMock()
                mock_sched_persistence.get_instance.return_value = mock_sched_instance
                
                # Test scheduled task creation
                create_tool = CreateScheduledRequestTool()
                
                with patch('userprofiles.ScheduledZairaRequest.ScheduledZairaRequest') as mock_scheduled_request, \
                     patch('managers.manager_users.ZairaUserManager.find_user', new_callable=AsyncMock) as mock_find_user2, \
                     patch('asyncio.create_task') as mock_create_task, \
                     patch('endpoints.mybot_generic.MyBot_Generic') as mock_bot_class:
                    
                    mock_find_user2.return_value = user
                    mock_task = MagicMock()
                    mock_task.scheduled_guid = str(uuid4())
                    mock_task.run_task = AsyncMock()
                    mock_scheduled_request.return_value = mock_task
                    
                    schedule_result = await create_tool._arun(
                        schedule_prompt="Send me a daily summary at 9 AM",
                        user_guid=str(user.GUID)
                    )
                    
                    assert f"Created scheduled task: {mock_task.scheduled_guid}" in schedule_result
                    mock_scheduled_request.assert_called_once()
                    mock_create_task.assert_called_once()
            
            # Step 5: Test Chat History and Message Management
            
            # Verify chat history structure
            assert isinstance(user.chat_history, dict)
            assert len(user.chat_history) >= 2  # Original session + new session
            
            # Test message retrieval
            langchain_history = user.get_chat_history(typing="LangChain")
            assert isinstance(langchain_history, list)
            assert len(langchain_history) >= 2  # User message + bot response
            
            # Test different typing parameter
            other_history = user.get_chat_history(typing="Other")
            assert other_history == []  # Should return empty for non-langchain
            
            # Step 6: Test User Permission Management
            
            # Test current permissions
            vector_stores = user.get_available_vector_stores()
            assert vector_stores == ["user_vectors"]
            
            # Escalate permissions
            await ZairaUserManager.update_user(
                str(user.GUID),
                rank=PERMISSION_LEVELS.ADMIN
            )
            
            admin_vector_stores = user.get_available_vector_stores()
            assert admin_vector_stores == ["user_vectors", "admin_vectors"]
            
            # Step 7: Test System Error Handling
            
            # Simulate error condition
            user.original_physical_message = MagicMock()  # Required for error handling
            
            with patch('userprofiles.LongRunningZairaRequest.LongRunningZairaRequest') as mock_error_task:
                mock_error_instance = MagicMock()
                mock_error_instance.await_status_complete = AsyncMock(side_effect=Exception("Simulated error"))
                mock_error_task.return_value = mock_error_instance
                
                user.my_task = None  # Reset task state
                await user.start_request(
                    complete_message="Test error handling",
                    calling_bot=self.mock_bot,
                    original_message=None
                )
                
                # Verify error was handled gracefully
                mock_exception.assert_called()
                self.mock_bot.send_reply.assert_called()
                assert user.my_task is None  # Task should be cleared after error
            
            # Step 8: Test System Cleanup
            
            # Remove user
            removal_result = await ZairaUserManager.remove_user(str(user.GUID))
            assert removal_result is True
            assert len(user_manager.users) == 0
            
            # Verify user cannot be found after removal
            not_found = await ZairaUserManager.find_user(str(user.GUID))
            assert not_found is None
    
    @pytest.mark.asyncio
    async def test_multi_platform_communication_integration(self):
        """Test multi-platform communication integration workflow"""
        with patch('managers.scheduled_requests.ScheduledRequestPersistenceManager.get_instance') as mock_get_persistence, \
             patch('userprofiles.ZairaUser.LogFire.log'), \
             patch('endpoints.mybot_generic.create_task') as mock_create_task, \
             patch('endpoints.mybot_generic.etc.helper_functions.handle_asyncio_task_result_errors'):
            
            mock_persistence = AsyncMock()
            mock_get_persistence.return_value = mock_persistence
            
            # Step 1: Create users for different platforms
            platforms = ['Discord', 'Teams', 'Whatsapp', 'Python']
            users = {}
            bots = {}
            
            for platform in platforms:
                user = await ZairaUserManager.add_user(
                    username=f"{platform.lower()}_user",
                    rank=PERMISSION_LEVELS.USER,
                    guid=uuid4(),
                    device_guid=uuid4()
                )
                await ZairaUserManager.update_user(
                    str(user.GUID),
                    platform=platform
                )
                users[platform] = user
                
                # Create platform-specific bot
                bot = MyBot_Generic(parent_instance=MagicMock(), name=platform)
                bots[platform] = bot
            
            # Step 2: Test platform-specific message handling
            
            # Test Discord (with message splitting)
            discord_bot = bots['Discord']
            discord_user = users['Discord']
            
            with patch('endpoints.mybot_generic.ZairaMessage.create_assistant_message') as mock_create_message:
                mock_message = MagicMock()
                mock_message.reply = AsyncMock()
                mock_create_message.return_value = MagicMock()
                
                # Mock task for reply
                mock_task = MagicMock()
                mock_task.user = discord_user
                
                # Test short message
                await discord_bot.send_reply("Short message", mock_task, mock_message)
                mock_message.reply.assert_called_once_with("Short message")
                
                # Test long message (should be split)
                long_message = "This is a very long message. " * 100  # About 3000 characters
                mock_message.reply.reset_mock()
                await discord_bot.send_reply(long_message, mock_task, mock_message)
                # Should be called multiple times due to splitting
                assert mock_message.reply.call_count >= 2
            
            # Test Teams
            teams_bot = bots['Teams']
            teams_user = users['Teams']
            
            with patch('endpoints.mybot_generic.ZairaMessage.create_assistant_message'):
                mock_teams_message = MagicMock()
                mock_teams_message.send_activity = AsyncMock()
                
                mock_teams_task = MagicMock()
                mock_teams_task.user = teams_user
                
                await teams_bot.send_reply("Teams message", mock_teams_task, mock_teams_message)
                mock_teams_message.send_activity.assert_called_once_with("Teams message")
            
            # Test WhatsApp
            whatsapp_bot = bots['Whatsapp']
            whatsapp_user = users['Whatsapp']
            
            with patch('endpoints.mybot_generic.MyWhatsappBot') as mock_whatsapp_class, \
                 patch('endpoints.mybot_generic.ZairaMessage.create_assistant_message'):
                
                mock_whatsapp_class.send_a_whatsapp_message = AsyncMock()
                
                mock_whatsapp_task = MagicMock()
                mock_whatsapp_task.user = whatsapp_user
                
                sender_id = "1234567890"
                await whatsapp_bot.send_reply("WhatsApp message", mock_whatsapp_task, sender_id)
                mock_whatsapp_class.send_a_whatsapp_message.assert_called()
            
            # Test Python (console output)
            python_bot = bots['Python']
            python_user = users['Python']
            
            mock_asyncio_task = MagicMock()
            mock_asyncio_task.add_done_callback = MagicMock()
            mock_create_task.return_value = mock_asyncio_task
            
            mock_python_task = MagicMock()
            mock_python_task.user = python_user
            
            await python_bot.send_reply("Python message", mock_python_task, MagicMock())
            mock_create_task.assert_called_once()
            assert python_bot.asyncio_Task == mock_asyncio_task
            
            # Step 3: Test broadcast functionality
            
            # Test Python broadcast
            with patch('builtins.print') as mock_print:
                await python_bot.send_broadcast("Test broadcast")
                mock_print.assert_called_once_with("Test broadcast")
            
            # Test Discord broadcast
            with patch('endpoints.mybot_generic.MyDiscordBot') as mock_discord_class:
                mock_discord_class.send_discord_broadcast = AsyncMock()
                await discord_bot.send_broadcast("Discord broadcast")
                mock_discord_class.send_discord_broadcast.assert_called_once_with("Discord broadcast")
            
            # Test member join events
            mock_member_message = MagicMock()
            
            # Discord member join
            with patch('endpoints.mybot_generic.MyDiscordBot') as mock_discord_class:
                discord_bot.parent_instance = MagicMock()
                discord_bot.parent_instance.__class__ = mock_discord_class
                
                mock_member_message.create_dm = AsyncMock()
                mock_member_message.dm_channel = MagicMock()
                mock_member_message.dm_channel.send = AsyncMock()
                
                await discord_bot.on_member_join("new_user", mock_member_message)
                mock_member_message.create_dm.assert_called_once()
                mock_member_message.dm_channel.send.assert_called_once()
            
            # Teams member join
            with patch('endpoints.mybot_generic.MyTeamsBot') as mock_teams_class:
                teams_bot.parent_instance = MagicMock()
                teams_bot.parent_instance.__class__ = mock_teams_class
                
                mock_teams_join_message = MagicMock()
                mock_teams_join_message.send_activity = AsyncMock()
                
                await teams_bot.on_member_join("new_teams_user", mock_teams_join_message)
                mock_teams_join_message.send_activity.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_comprehensive_database_integration(self):
        """Test comprehensive database integration including PostgreSQL and Qdrant"""
        with patch('managers.manager_postgreSQL.LogFire.log'), \
             patch('managers.manager_qdrant.LogFire.log'), \
             patch('managers.manager_retrieval.LogFire.log'), \
             patch('managers.scheduled_requests.ScheduledRequestPersistenceManager.get_instance') as mock_get_persistence:
            
            # Mock database managers
            mock_postgres = MagicMock()
            mock_postgres.get_connection = AsyncMock()
            mock_postgres.execute_query = AsyncMock()
            mock_postgres.create_database = AsyncMock()
            mock_postgres.create_table = AsyncMock()
            
            mock_qdrant = MagicMock()
            mock_qdrant.search_similar = AsyncMock(return_value=[])
            mock_qdrant.upsert_points = AsyncMock()
            mock_qdrant.create_collection = AsyncMock()
            
            mock_retrieval = MagicMock()
            mock_retrieval.retrieve_similar = AsyncMock(return_value=[
                {'text': 'Sample document 1', 'score': 0.9},
                {'text': 'Sample document 2', 'score': 0.8}
            ])
            
            # Mock persistence manager
            mock_persistence = AsyncMock()
            mock_persistence.create_task = AsyncMock()
            mock_persistence.get_active_requests = AsyncMock(return_value=[])
            mock_persistence.save_task = AsyncMock()
            mock_persistence.cancel_task = AsyncMock(return_value=True)
            mock_get_persistence.return_value = mock_persistence
            
            # Step 1: Test PostgreSQL integration
            with patch('managers.manager_postgreSQL.PostgreSQLManager.get_instance', return_value=mock_postgres):
                postgres_manager = PostgreSQLManager.get_instance()
                assert postgres_manager == mock_postgres
                
                # Test connection
                connection = await postgres_manager.get_connection()
                mock_postgres.get_connection.assert_called_once()
                
                # Test query execution
                await postgres_manager.execute_query("SELECT * FROM test_table")
                mock_postgres.execute_query.assert_called_once()
            
            # Step 2: Test Qdrant integration
            with patch('managers.manager_qdrant.QDrantManager.get_instance', return_value=mock_qdrant):
                qdrant_manager = QDrantManager.get_instance()
                assert qdrant_manager == mock_qdrant
                
                # Test search
                search_results = await qdrant_manager.search_similar("test query", limit=5)
                assert search_results == []
                mock_qdrant.search_similar.assert_called_once()
                
                # Test upsert
                await qdrant_manager.upsert_points("test_collection", [])
                mock_qdrant.upsert_points.assert_called_once()
            
            # Step 3: Test RAG retrieval integration
            with patch('managers.manager_retrieval.RetrievalManager.get_instance', return_value=mock_retrieval):
                retrieval_manager = RetrievalManager.get_instance()
                assert retrieval_manager == mock_retrieval
                
                # Test retrieval
                retrieval_results = await retrieval_manager.retrieve_similar("test query")
                assert len(retrieval_results) == 2
                assert retrieval_results[0]['text'] == 'Sample document 1'
                assert retrieval_results[0]['score'] == 0.9
                mock_retrieval.retrieve_similar.assert_called_once()
            
            # Step 4: Test scheduled task persistence
            persistence_manager = ScheduledRequestPersistenceManager.get_instance()
            
            # Test task creation
            test_task_data = {
                'scheduled_guid': str(uuid4()),
                'user_guid': str(uuid4()),
                'schedule_prompt': 'Test scheduled task',
                'created_at': datetime.now().isoformat()
            }
            
            await persistence_manager.create_task(test_task_data)
            mock_persistence.create_task.assert_called_once_with(test_task_data)
            
            # Test task retrieval
            active_requests = await persistence_manager.get_active_requests(test_task_data['user_guid'])
            assert active_requests == []
            mock_persistence.get_active_requests.assert_called_once()
            
            # Test task cancellation
            cancel_result = await persistence_manager.cancel_task(test_task_data['scheduled_guid'], "Test cancellation")
            assert cancel_result is True
            mock_persistence.cancel_task.assert_called_once()
            
            # Step 5: Test integrated workflow with all database components
            
            # Create a user that would interact with all database systems
            with patch('userprofiles.ZairaUser.LogFire.log'):
                user = await ZairaUserManager.add_user(
                    username="db_integration_user",
                    rank=PERMISSION_LEVELS.ADMIN,
                    guid=uuid4(),
                    device_guid=uuid4()
                )
                
                # Simulate user performing operations that touch all database systems
                
                # 1. User query that would trigger RAG retrieval
                user_query = "Tell me about the project documentation"
                
                # 2. Create scheduled task (touches PostgreSQL)
                with patch('tasks.inputs.task_scheduled_request_manager.ScheduledRequestPersistenceManager') as mock_sched_persistence:
                    mock_sched_instance = AsyncMock()
                    mock_sched_persistence.get_instance.return_value = mock_sched_instance
                    
                    create_tool = CreateScheduledRequestTool()
                    
                    with patch('userprofiles.ScheduledZairaRequest.ScheduledZairaRequest') as mock_scheduled_request, \
                         patch('managers.manager_users.ZairaUserManager.find_user', new_callable=AsyncMock) as mock_find_user, \
                         patch('asyncio.create_task') as mock_create_task:
                        
                        mock_find_user.return_value = user
                        mock_task = MagicMock()
                        mock_task.scheduled_guid = str(uuid4())
                        mock_scheduled_request.return_value = mock_task
                        
                        schedule_result = await create_tool._arun(
                            schedule_prompt="Check database status every hour",
                            user_guid=str(user.GUID)
                        )
                        
                        assert "Created scheduled task" in schedule_result
                
                # 3. Chat session management (stores in user object, could sync to PostgreSQL)
                state = SupervisorTaskState(
                    user_guid=str(user.GUID),
                    original_input="Create new session for database testing",
                    additional_input={},
                    messages=[HumanMessage(content="Create new session")]
                )
                
                with patch('tasks.etc.task_chat_session.ZairaUserManager.find_user', new_callable=AsyncMock) as mock_find_user2:
                    mock_find_user2.return_value = user
                    
                    from tasks.etc.task_chat_session import new_chat_session_tool
                    session_result = await new_chat_session_tool._arun(state)
                    
                    assert "New chat history session has been started" in session_result
                    assert len(user.chat_history) >= 2
                
                # Verify user was properly managed
                assert user.rank == PERMISSION_LEVELS.ADMIN
                assert user.get_available_vector_stores() == ["user_vectors", "admin_vectors"]