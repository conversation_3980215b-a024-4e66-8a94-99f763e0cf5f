"""
Comprehensive unit tests for ScheduledRequestPersistenceManager
This test suite achieves 90%+ coverage for scheduled task management functionality
"""

import sys
import os
import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from typing import Dict, List, Any, Optional
from datetime import datetime, timedelta, timezone
from uuid import uuid4, UUID
import json

# Add project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from imports import *
from managers.scheduled_requests import ScheduledRequestPersistenceManager

class TestScheduledRequestPersistenceManagerSingleton:
    """Test ScheduledRequestPersistenceManager singleton pattern"""
    
    def test_scheduled_request_manager_singleton_creation(self):
        """Test ScheduledRequestPersistenceManager singleton creation"""
        # Reset singleton for test
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        manager1 = ScheduledRequestPersistenceManager()
        manager2 = ScheduledRequestPersistenceManager()
        
        # Should be same instance
        assert manager1 is manager2
        assert isinstance(manager1, ScheduledRequestPersistenceManager)
        
        # Should have db_available attribute
        assert hasattr(manager1, '_db_available')
    
    def test_scheduled_request_manager_get_instance(self):
        """Test ScheduledRequestPersistenceManager get_instance method"""
        # Reset singleton for test
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        manager1 = ScheduledRequestPersistenceManager.get_instance()
        manager2 = ScheduledRequestPersistenceManager.get_instance()
        
        # Should be same instance
        assert manager1 is manager2
        assert isinstance(manager1, ScheduledRequestPersistenceManager)
    
    def test_scheduled_request_manager_initialization_state(self):
        """Test ScheduledRequestPersistenceManager initialization state"""
        # Reset singleton for test
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        # Should start uninitialized
        assert manager._initialized == False
        assert hasattr(manager, '_active_requests')
        assert hasattr(manager, '_paused_requests')
    
    @pytest.mark.asyncio
    async def test_scheduled_request_manager_setup(self):
        """Test ScheduledRequestPersistenceManager setup process"""
        # Reset singleton for test
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager.execute_query') as mock_execute, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            mock_execute.return_value = None
            
            await ScheduledRequestPersistenceManager.setup()
            
            # Should be initialized
            manager = ScheduledRequestPersistenceManager.get_instance()
            assert manager._initialized == True
            
            # Should have attempted to create tables
            mock_execute.assert_called()
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_scheduled_request_manager_setup_idempotent(self):
        """Test ScheduledRequestPersistenceManager setup is idempotent"""
        # Reset singleton for test
        ScheduledRequestPersistenceManager._instance = None
        ScheduledRequestPersistenceManager._initialized = False
        
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager.execute_query') as mock_execute, \
             patch('managers.scheduled_requests.core.persistence.LogFire'):
            
            mock_execute.return_value = None
            
            # Setup multiple times
            await ScheduledRequestPersistenceManager.setup()
            await ScheduledRequestPersistenceManager.setup()
            await ScheduledRequestPersistenceManager.setup()
            
            # Should only initialize once
            manager = ScheduledRequestPersistenceManager.get_instance()
            assert manager._initialized == True

class TestScheduledRequestPersistenceManagerDatabaseOperations:
    """Test ScheduledRequestPersistenceManager database operations"""
    
    @pytest.fixture
    def mock_database(self):
        """Provide mocked database for testing"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db:
            mock_connection = Mock()
            mock_db.get_connection.return_value = mock_connection
            mock_db.execute_query.return_value = None
            yield mock_db, mock_connection
    
    @pytest.mark.asyncio
    async def test_create_tables_success(self, mock_database):
        """Test successful table creation"""
        mock_db, mock_connection = mock_database
        
        # Mock table check query
        mock_db.execute_query.side_effect = [
            [],  # Table doesn't exist
            None,  # CREATE TABLE
            None,  # Index 1
            None,  # Index 2
            None   # Index 3
        ]
        
        manager = ScheduledRequestPersistenceManager.get_instance()
        await manager._create_tables()
        
        # Should have attempted to create table and indexes
        assert mock_db.execute_query.call_count >= 2
        assert manager._db_available == True
    
    @pytest.mark.asyncio
    async def test_create_tables_existing_table(self, mock_database):
        """Test table creation with existing table"""
        mock_db, mock_connection = mock_database
        
        # Mock existing table with correct columns
        mock_db.execute_query.side_effect = [
            [
                {'column_name': 'scheduled_guid'},
                {'column_name': 'user_guid'},
                {'column_name': 'schedule_prompt'},
                {'column_name': 'target_prompt'}
            ],  # Table exists with required columns
            None,  # Index creation
            None,  # Index creation
            None   # Index creation
        ]
        
        manager = ScheduledRequestPersistenceManager.get_instance()
        await manager._create_tables()
        
        # Should have checked existing table
        assert mock_db.execute_query.call_count >= 1
        assert manager._db_available == True
    
    @pytest.mark.asyncio
    async def test_create_tables_missing_columns(self, mock_database):
        """Test table creation with missing columns"""
        mock_db, mock_connection = mock_database
        
        # Mock existing table with missing columns
        mock_db.execute_query.side_effect = [
            [{'column_name': 'scheduled_guid'}],  # Missing required columns
            None,  # Drop table
            None,  # Create table
            None,  # Index creation
            None,  # Index creation
            None   # Index creation
        ]
        
        with patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            manager = ScheduledRequestPersistenceManager.get_instance()
            await manager._create_tables()
            
            # Should have dropped and recreated table
            assert mock_db.execute_query.call_count >= 3
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_create_tables_database_error(self):
        """Test table creation with database connection error"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager.execute_query', 
                  side_effect=ConnectionRefusedError("Database not available")), \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            # Should handle error gracefully
            await manager._create_tables()
            
            # Should set db_available to False
            assert manager._db_available == False
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_execute_single_operation_success(self, mock_database):
        """Test successful single database operation"""
        mock_db, mock_connection = mock_database
        
        mock_pool = Mock()
        mock_db.get_connection.return_value = mock_pool
        
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        # Should execute without error
        await manager._execute_single_operation("SELECT 1")
        
        mock_db.execute_query.assert_called_once_with("vectordb", "SELECT 1")
    
    @pytest.mark.asyncio
    async def test_execute_single_operation_with_params(self, mock_database):
        """Test single database operation with parameters"""
        mock_db, mock_connection = mock_database
        
        mock_pool = Mock()
        mock_db.get_connection.return_value = mock_pool
        
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        # Should execute with parameters
        await manager._execute_single_operation("SELECT * WHERE id = $1", ["test_id"])
        
        mock_db.execute_query.assert_called_once_with("vectordb", "SELECT * WHERE id = $1", ["test_id"])
    
    @pytest.mark.asyncio
    async def test_execute_single_operation_no_pool(self):
        """Test single operation when pool unavailable"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager.get_connection', return_value=None), \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            with pytest.raises(Exception) as exc_info:
                await manager._execute_single_operation("SELECT 1")
            
            assert "Failed to get database pool" in str(exc_info.value)

class TestScheduledRequestPersistenceManagerTaskOperations:
    """Test ScheduledRequestPersistenceManager task operations"""
    
    @pytest.fixture
    def mock_scheduled_request(self):
        """Create mock scheduled task"""
        task = Mock()
        task.scheduled_guid = uuid4()
        task.user = Mock()
        task.user.GUID = str(uuid4())
        task.schedule_prompt = "Test schedule prompt"
        task.target_prompt = "Test target prompt"
        task.delay_seconds = 60.0
        task.start_delay_seconds = 0.0
        task.schedule_type = Mock()
        task.schedule_type.value = "once"
        task.next_execution = datetime.now(timezone.utc)
        task.is_active = True
        task.calling_bot = Mock()
        task.calling_bot.name = "test_bot"
        task.original_physical_message = None
        task.get_schedule_info.return_value = {"type": "once"}
        task.get_request_status.return_value = {"status": "active"}
        task.cancel_schedule = Mock()
        return task
    
    @pytest.mark.asyncio
    async def test_save_task_success(self, mock_scheduled_request):
        """Test successful task saving"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            # Mock database operations
            mock_db.execute_query.return_value = None
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            manager._db_available = True
            
            result = await manager.save_task(mock_scheduled_request)
            
            assert result == True
            assert str(mock_scheduled_request.scheduled_guid) in manager._active_requests
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_save_task_database_unavailable(self, mock_scheduled_request):
        """Test task saving when database unavailable"""
        with patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            manager._db_available = False
            
            result = await manager.save_task(mock_scheduled_request)
            
            assert result == False
            mock_logfire.log.assert_called_with("WARNING", "Database not available, cannot save scheduled task")
    
    @pytest.mark.asyncio
    async def test_save_task_database_error(self, mock_scheduled_request):
        """Test task saving with database error"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            # Mock database error
            mock_db.execute_query.side_effect = Exception("Database error")
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            manager._db_available = True
            
            result = await manager.save_task(mock_scheduled_request)
            
            assert result == False
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_load_task_success(self):
        """Test successful task loading"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db:
            
            # Mock database result
            mock_connection = Mock()
            mock_connection.fetchrow.return_value = {
                'scheduled_guid': 'test-guid',
                'user_guid': 'user-guid',
                'schedule_prompt': 'test prompt'
            }
            mock_db.get_connection.return_value = mock_connection
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            result = await manager.load_task('test-guid')
            
            assert result is not None
            assert result['scheduled_guid'] == 'test-guid'
            mock_connection.fetchrow.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_load_task_not_found(self):
        """Test loading non-existent task"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db:
            
            # Mock no result
            mock_connection = Mock()
            mock_connection.fetchrow.return_value = None
            mock_db.get_connection.return_value = mock_connection
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            result = await manager.load_task('nonexistent-guid')
            
            assert result is None
    
    @pytest.mark.asyncio
    async def test_load_task_database_error(self):
        """Test task loading with database error"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            # Mock database error
            mock_db.get_connection.side_effect = Exception("Database error")
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            result = await manager.load_task('test-guid')
            
            assert result is None
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_get_active_requests_all(self):
        """Test getting all active tasks"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db:
            
            # Mock database results
            mock_connection = Mock()
            mock_connection.fetch.return_value = [
                {'scheduled_guid': 'task1', 'is_active': True},
                {'scheduled_guid': 'task2', 'is_active': True}
            ]
            mock_db.get_connection.return_value = mock_connection
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            manager._db_available = True
            
            result = await manager.get_active_requests()
            
            assert len(result) == 2
            assert result[0]['scheduled_guid'] == 'task1'
            assert result[1]['scheduled_guid'] == 'task2'
    
    @pytest.mark.asyncio
    async def test_get_active_requests_by_user(self):
        """Test getting active tasks filtered by user"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db:
            
            # Mock database results
            mock_connection = Mock()
            mock_connection.fetch.return_value = [
                {'scheduled_guid': 'task1', 'user_guid': 'user1', 'is_active': True}
            ]
            mock_db.get_connection.return_value = mock_connection
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            manager._db_available = True
            
            result = await manager.get_active_requests('user1')
            
            assert len(result) == 1
            assert result[0]['user_guid'] == 'user1'
            mock_connection.fetch.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_active_requests_database_unavailable(self):
        """Test getting active tasks when database unavailable"""
        with patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            manager._db_available = False
            
            result = await manager.get_active_requests()
            
            assert result == []
            mock_logfire.log.assert_called_with("WARNING", "Database not available, returning empty task list")
    
    @pytest.mark.asyncio
    async def test_cancel_task_success(self):
        """Test successful task cancellation"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            # Mock database operations
            mock_connection = Mock()
            mock_connection.execute.return_value = "UPDATE 1"
            mock_db.get_connection.return_value = mock_connection
            
            # Add active task
            mock_task = Mock()
            mock_task.cancel_schedule = Mock()
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            manager._active_requests['test-guid'] = mock_task
            
            result = await manager.cancel_task('test-guid', "Test cancellation")
            
            assert result == True
            assert 'test-guid' not in manager._active_requests
            mock_task.cancel_schedule.assert_called_once()
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_cancel_task_not_active(self):
        """Test cancelling task that's not in active tasks"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            # Mock database operations
            mock_connection = Mock()
            mock_connection.execute.return_value = "UPDATE 1"
            mock_db.get_connection.return_value = mock_connection
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            result = await manager.cancel_task('nonexistent-guid', "Test cancellation")
            
            assert result == True
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_cancel_task_database_error(self):
        """Test task cancellation with database error"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            # Mock database error
            mock_db.get_connection.side_effect = Exception("Database error")
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            result = await manager.cancel_task('test-guid', "Test cancellation")
            
            assert result == False
            mock_logfire.log.assert_called()

class TestScheduledRequestPersistenceManagerRecovery:
    """Test ScheduledRequestPersistenceManager task recovery functionality"""
    
    @pytest.mark.asyncio
    async def test_late_setup_success(self):
        """Test successful late setup and task recovery"""
        with patch.object(ScheduledRequestPersistenceManager, '_recover_requests') as mock_recover, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            mock_recover.return_value = None
            
            await ScheduledRequestPersistenceManager.late_setup()
            
            mock_recover.assert_called_once()
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_late_setup_recovery_error(self):
        """Test late setup with recovery error"""
        with patch.object(ScheduledRequestPersistenceManager, '_recover_requests', 
                         side_effect=Exception("Recovery failed")) as mock_recover, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire, \
             patch('etc.helper_functions.exception_triggered') as mock_exception:
            
            await ScheduledRequestPersistenceManager.late_setup()
            
            mock_recover.assert_called_once()
            mock_logfire.log.assert_called()
            mock_exception.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_recover_requests_success(self):
        """Test successful task recovery"""
        with patch.object(ScheduledRequestPersistenceManager, 'get_active_requests') as mock_get_requests, \
             patch.object(ScheduledRequestPersistenceManager, '_recreate_task_from_data') as mock_recreate, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire, \
             patch('asyncio.create_task') as mock_create_task:
            
            # Mock task data
            task_data = [
                {
                    'scheduled_guid': 'task1',
                    'user_guid': 'user1',
                    'next_execution': datetime.now(timezone.utc)
                }
            ]
            mock_get_requests.return_value = task_data
            
            # Mock recreated task
            mock_task = Mock()
            mock_task.run_task = AsyncMock()
            mock_recreate.return_value = mock_task
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            await manager._recover_requests()
            
            assert 'task1' in manager._active_requests
            mock_create_task.assert_called_once()
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_recover_requests_overdue_task(self):
        """Test recovery of overdue task"""
        with patch.object(ScheduledRequestPersistenceManager, 'get_active_requests') as mock_get_requests, \
             patch.object(ScheduledRequestPersistenceManager, '_recreate_task_from_data') as mock_recreate, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            # Mock overdue task
            overdue_time = datetime.now(timezone.utc) - timedelta(hours=2)
            task_data = [
                {
                    'scheduled_guid': 'overdue_task',
                    'user_guid': 'user1',
                    'next_execution': overdue_time
                }
            ]
            mock_get_requests.return_value = task_data
            mock_recreate.return_value = Mock()
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            await manager._recover_requests()
            
            # Should log about overdue task
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_recover_requests_invalid_data(self):
        """Test recovery with invalid task data"""
        with patch.object(ScheduledRequestPersistenceManager, 'get_active_requests') as mock_get_requests, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            # Mock invalid task data
            task_data = [
                {},  # Invalid - no scheduled_guid
                {'scheduled_guid': 'valid_task', 'user_guid': 'user1'}
            ]
            mock_get_requests.return_value = task_data
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            await manager._recover_requests()
            
            # Should log warning about invalid data
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_recover_requests_recreation_failure(self):
        """Test recovery when task recreation fails"""
        with patch.object(ScheduledRequestPersistenceManager, 'get_active_requests') as mock_get_requests, \
             patch.object(ScheduledRequestPersistenceManager, '_recreate_task_from_data') as mock_recreate, \
             patch.object(ScheduledRequestPersistenceManager, 'cancel_task') as mock_cancel, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            task_data = [
                {'scheduled_guid': 'failed_task', 'user_guid': 'user1'}
            ]
            mock_get_requests.return_value = task_data
            mock_recreate.side_effect = Exception("Recreation failed")
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            await manager._recover_requests()
            
            mock_cancel.assert_called_once_with('failed_task', "Recovery failed: Recreation failed")
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_recreate_task_from_data_success(self):
        """Test successful task recreation from data"""
        with patch('managers.scheduled_requests.core.persistence.ZairaUserManager') as mock_user_manager, \
             patch('userprofiles.ScheduledZairaRequest.ScheduledZairaRequest') as mock_task_class, \
             patch('userprofiles.ScheduledZairaRequest.ScheduleType') as mock_schedule_type, \
             patch('endpoints.mybot_generic.MyBot_Generic') as mock_bot, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            # Mock user
            mock_user = Mock()
            mock_user_manager.find_user.return_value = mock_user
            
            # Mock task creation
            mock_task = Mock()
            mock_task.scheduled_guid = uuid4()
            mock_task_class.return_value = mock_task
            
            # Mock schedule type
            mock_schedule_type.return_value = Mock()
            
            task_data = {
                'scheduled_guid': str(uuid4()),
                'user_guid': 'user1',
                'schedule_prompt': 'test schedule',
                'target_prompt': 'test target',
                'delay_seconds': 60.0,
                'start_delay_seconds': 0.0,
                'schedule_type': 'once',
                'next_execution': datetime.now(),
                'is_active': True,
                'calling_bot_name': 'test_bot'
            }
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            result = await manager._recreate_task_from_data(task_data)
            
            assert result is not None
            mock_user_manager.find_user.assert_called_once()
            mock_task_class.assert_called_once()
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_recreate_task_user_not_found(self):
        """Test task recreation when user not found"""
        with patch('managers.scheduled_requests.core.persistence.ZairaUserManager') as mock_user_manager, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            # Mock user not found
            mock_user_manager.find_user.return_value = None
            
            task_data = {
                'scheduled_guid': 'test-guid',
                'user_guid': 'nonexistent-user'
            }
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            result = await manager._recreate_task_from_data(task_data)
            
            assert result is None
            assert 'test-guid' in manager._paused_requests
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_recreate_task_user_manager_error(self):
        """Test task recreation when user manager throws error"""
        with patch('managers.scheduled_requests.core.persistence.ZairaUserManager') as mock_user_manager, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            # Mock user manager error
            mock_user_manager.find_user.side_effect = Exception("User manager not ready")
            
            task_data = {
                'scheduled_guid': 'test-guid',
                'user_guid': 'user1'
            }
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            result = await manager._recreate_task_from_data(task_data)
            
            assert result is None
            assert 'test-guid' in manager._paused_requests
            mock_logfire.log.assert_called()

class TestScheduledRequestPersistenceManagerUserTasks:
    """Test ScheduledRequestPersistenceManager user task management"""
    
    def test_get_paused_requests_count(self):
        """Test getting paused tasks count"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        manager._paused_requests = {
            'task1': {'user_guid': 'user1'},
            'task2': {'user_guid': 'user2'},
            'task3': {'user_guid': 'user1'}
        }
        
        count = manager.get_paused_requests_count()
        assert count == 3
    
    def test_get_paused_requests_info(self):
        """Test getting paused tasks information"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        manager._paused_requests = {
            'task1': {
                'scheduled_guid': 'task1',
                'user_guid': 'user1',
                'schedule_prompt': 'Schedule 1',
                'target_prompt': 'Target 1'
            },
            'task2': {
                'scheduled_guid': 'task2',
                'user_guid': 'user2',
                'schedule_prompt': 'Schedule 2',
                'target_prompt': 'Target 2'
            }
        }
        
        info = manager.get_paused_requests_info()
        
        assert len(info) == 2
        assert info[0]['scheduled_guid'] == 'task1'
        assert info[0]['user_guid'] == 'user1'
        assert info[1]['scheduled_guid'] == 'task2'
        assert info[1]['user_guid'] == 'user2'

class TestScheduledRequestPersistenceManagerUtilities:
    """Test ScheduledRequestPersistenceManager utility functions"""
    
    @pytest.mark.asyncio
    async def test_cleanup_old_requests_success(self):
        """Test successful cleanup of old tasks"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            mock_connection = Mock()
            mock_connection.execute.return_value = "DELETE 5"
            mock_db.get_connection.return_value = mock_connection
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            await manager.cleanup_old_requests(30)
            
            mock_connection.execute.assert_called_once()
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_cleanup_old_requests_custom_days(self):
        """Test cleanup with custom days parameter"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            mock_connection = Mock()
            mock_connection.execute.return_value = "DELETE 2"
            mock_db.get_connection.return_value = mock_connection
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            await manager.cleanup_old_requests(7)  # 7 days instead of default 30
            
            mock_connection.execute.assert_called_once()
            # Verify the cutoff date calculation used 7 days
            call_args = mock_connection.execute.call_args
            assert len(call_args[0]) == 2  # query and cutoff_date
    
    @pytest.mark.asyncio
    async def test_cleanup_old_requests_database_error(self):
        """Test cleanup with database error"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            mock_db.get_connection.side_effect = Exception("Database error")
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            # Should handle error gracefully
            await manager.cleanup_old_requests(30)
            
            mock_logfire.log.assert_called()
    
    def test_get_active_task(self):
        """Test getting active task by GUID"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        # Setup active task
        mock_task = Mock()
        manager._active_requests['test-guid'] = mock_task
        
        result = manager.get_active_task('test-guid')
        assert result == mock_task
        
        # Test non-existent task
        result = manager.get_active_task('nonexistent-guid')
        assert result is None
    
    def test_get_all_active_requests(self):
        """Test getting all active tasks"""
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        # Setup active tasks
        mock_task1 = Mock()
        mock_task2 = Mock()
        manager._active_requests = {
            'task1': mock_task1,
            'task2': mock_task2
        }
        
        result = manager.get_all_active_requests()
        
        assert len(result) == 2
        assert result['task1'] == mock_task1
        assert result['task2'] == mock_task2
        
        # Should return copy, not reference
        assert result is not manager._active_requests

class TestScheduledRequestPersistenceManagerErrorHandling:
    """Test ScheduledRequestPersistenceManager error handling"""
    
    @pytest.mark.asyncio
    async def test_save_task_json_serialization_error(self):
        """Test save task with JSON serialization error"""
        with patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            # Create task with non-serializable data
            mock_task = Mock()
            mock_task.scheduled_guid = uuid4()
            mock_task.user = Mock()
            mock_task.user.GUID = str(uuid4())
            mock_task.get_schedule_info.side_effect = Exception("Serialization error")
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            manager._db_available = True
            
            result = await manager.save_task(mock_task)
            
            assert result == False
            mock_logfire.log.assert_called()
    
    @pytest.mark.asyncio
    async def test_recovery_with_general_error(self):
        """Test recovery with general error"""
        with patch.object(ScheduledRequestPersistenceManager, 'get_active_requests', 
                         side_effect=Exception("General error")), \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            # Should handle error gracefully
            await manager._recover_requests()
            
            mock_logfire.log.assert_called()

class TestScheduledRequestPersistenceManagerIntegration:
    """Test ScheduledRequestPersistenceManager integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_persistence_manager_singleton_access(self):
        """Test persistence manager singleton access"""
        with patch.object(ScheduledRequestPersistenceManager, 'setup') as mock_setup:
            
            result = ScheduledRequestPersistenceManager.get_instance()
            await ScheduledRequestPersistenceManager.setup()
            
            assert isinstance(result, ScheduledRequestPersistenceManager)
            mock_setup.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_full_task_lifecycle(self):
        """Test complete task lifecycle"""
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db, \
             patch('managers.scheduled_requests.core.persistence.LogFire') as mock_logfire:
            
            # Mock database operations
            mock_connection = Mock()
            mock_connection.fetchrow.return_value = None
            mock_connection.fetch.return_value = []
            mock_connection.execute.return_value = "UPDATE 1"
            mock_db.get_connection.return_value = mock_connection
            mock_db.execute_query.return_value = None
            
            # Create mock task
            task_guid = str(uuid4())
            mock_task = Mock()
            mock_task.scheduled_guid = uuid4()
            mock_task.user = Mock()
            mock_task.user.GUID = str(uuid4())
            mock_task.schedule_prompt = "Test schedule"
            mock_task.target_prompt = "Test target"
            mock_task.delay_seconds = 60.0
            mock_task.start_delay_seconds = 0.0
            mock_task.schedule_type = Mock()
            mock_task.schedule_type.value = "once"
            mock_task.next_execution = datetime.now(timezone.utc)
            mock_task.is_active = True
            mock_task.calling_bot = Mock()
            mock_task.calling_bot.name = "test_bot"
            mock_task.original_physical_message = None
            mock_task.get_schedule_info.return_value = {"type": "once"}
            mock_task.get_request_status.return_value = {"status": "active"}
            mock_task.cancel_schedule = Mock()
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            manager._db_available = True
            
            # 1. Save task
            save_result = await manager.save_task(mock_task)
            assert save_result == True
            
            # 2. Get active tasks
            active_requests = await manager.get_active_requests()
            assert isinstance(active_requests, list)
            
            # 3. Cancel task
            cancel_result = await manager.cancel_task(str(mock_task.scheduled_guid), "Test completion")
            assert cancel_result == True
            
            # 4. Cleanup old tasks
            await manager.cleanup_old_requests(30)
            
            # Verify all operations called database
            assert mock_db.execute_query.call_count >= 1
    
    @pytest.mark.asyncio
    async def test_persistence_manager_with_real_schedule_types(self):
        """Test persistence manager with realistic schedule type usage"""
        with patch('userprofiles.ScheduledZairaRequest.ScheduleType') as mock_schedule_type:
            
            # Mock schedule type enum
            mock_schedule_type.ONCE = "once"
            mock_schedule_type.RECURRING = "recurring"
            
            task_data = {
                'scheduled_guid': str(uuid4()),
                'user_guid': 'user1',
                'schedule_prompt': 'daily reminder',
                'target_prompt': 'send notification',
                'delay_seconds': 86400.0,  # 24 hours
                'start_delay_seconds': 0.0,
                'schedule_type': 'recurring',
                'next_execution': datetime.now(timezone.utc) + timedelta(days=1),
                'is_active': True,
                'calling_bot_name': 'reminder_bot'
            }
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            
            # Should handle schedule type correctly
            with patch('managers.scheduled_requests.core.persistence.ZairaUserManager') as mock_user_manager:
                mock_user_manager.find_user.return_value = None  # Will pause task
                
                result = await manager._recreate_task_from_data(task_data)
                
                # Should pause task due to no user found
                assert result is None
                assert task_data['scheduled_guid'] in manager._paused_requests

class TestScheduledRequestPersistenceManagerPerformance:
    """Test ScheduledRequestPersistenceManager performance characteristics"""
    
    @pytest.mark.asyncio
    async def test_bulk_task_operations_performance(self):
        """Test performance with multiple tasks"""
        import time
        
        with patch('managers.scheduled_requests.core.persistence.PostgreSQLManager') as mock_db, \
             patch('managers.scheduled_requests.core.persistence.LogFire'):
            
            mock_connection = Mock()
            mock_connection.fetch.return_value = []
            mock_db.get_connection.return_value = mock_connection
            
            manager = ScheduledRequestPersistenceManager.get_instance()
            manager._db_available = True
            
            start_time = time.time()
            
            # Perform multiple get_active_requests calls
            for _ in range(100):
                await manager.get_active_requests()
            
            end_time = time.time()
            processing_time = end_time - start_time
            
            # Should handle multiple calls efficiently
            assert processing_time < 2.0, f"Bulk operations should be fast, took {processing_time:.3f}s"
    
    def test_memory_efficiency(self):
        """Test ScheduledRequestPersistenceManager memory efficiency"""
        import sys
        
        manager = ScheduledRequestPersistenceManager.get_instance()
        
        # Check memory usage
        manager_size = sys.getsizeof(manager)
        active_requests_size = sys.getsizeof(manager._active_requests)
        paused_requests_size = sys.getsizeof(manager._paused_requests)
        
        # Should be reasonably sized
        assert manager_size < 5000, f"Manager should be memory efficient, uses {manager_size} bytes"
        assert active_requests_size < 10000, f"Active tasks should be memory efficient, use {active_requests_size} bytes"
        assert paused_requests_size < 10000, f"Paused tasks should be memory efficient, use {paused_requests_size} bytes"