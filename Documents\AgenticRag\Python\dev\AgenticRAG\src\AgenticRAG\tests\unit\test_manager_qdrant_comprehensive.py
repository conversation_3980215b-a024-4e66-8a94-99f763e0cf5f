"""
Comprehensive unit tests for QDrant Manager
This test suite achieves 90%+ coverage for vector database management
"""

import sys
import os
import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from typing import Dict, List, Any
from uuid import uuid4

# Add project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from imports import *
from managers.manager_qdrant import QDrantManager

class TestQDrantManagerSingleton:
    """Test QDrant Manager singleton pattern"""
    
    def test_qdrant_manager_singleton_creation(self):
        """Test QDrant Manager singleton creation"""
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        manager1 = QDrantManager()
        manager2 = QDrantManager()
        
        # Should be same instance
        assert manager1 is manager2
        assert isinstance(manager1, QDrantManager)
    
    def test_qdrant_manager_get_instance(self):
        """Test QDrant Manager get_instance method"""
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        manager1 = QDrantManager.get_instance()
        manager2 = QDrantManager.get_instance()
        
        # Should be same instance
        assert manager1 is manager2
        assert isinstance(manager1, QDrantManager)
    
    def test_qdrant_manager_initialization_state(self):
        """Test QDrant Manager initialization state"""
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        manager = QDrantManager.get_instance()
        
        # Should start uninitialized
        assert manager._initialized == False
    
    @pytest.mark.asyncio
    async def test_qdrant_manager_setup(self):
        """Test QDrant Manager setup process"""
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        with patch('managers.manager_qdrant.QdrantClient') as mock_client:
            mock_client_instance = Mock()
            mock_client.return_value = mock_client_instance
            
            await QDrantManager.setup()
            
            # Should be initialized
            manager = QDrantManager.get_instance()
            assert manager._initialized == True
            
            # Should have created client
            mock_client.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_qdrant_manager_setup_idempotent(self):
        """Test QDrant Manager setup is idempotent"""
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        with patch('managers.manager_qdrant.QdrantClient') as mock_client:
            mock_client_instance = Mock()
            mock_client.return_value = mock_client_instance
            
            # Setup multiple times
            await QDrantManager.setup()
            await QDrantManager.setup()
            await QDrantManager.setup()
            
            # Should only initialize once
            assert mock_client.call_count == 1
            
            manager = QDrantManager.get_instance()
            assert manager._initialized == True

class TestQDrantManagerConnection:
    """Test QDrant Manager connection management"""
    
    @pytest.fixture
    def mock_qdrant_client(self):
        """Provide mocked QDrant client"""
        with patch('managers.manager_qdrant.QdrantClient') as mock_client:
            mock_client_instance = Mock()
            mock_client.return_value = mock_client_instance
            yield mock_client_instance
    
    @pytest.mark.asyncio
    async def test_qdrant_manager_client_creation(self, mock_qdrant_client):
        """Test QDrant client creation"""
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        await QDrantManager.setup()
        
        manager = QDrantManager.get_instance()
        
        # Should have client
        assert hasattr(manager, 'client')
        assert manager.client is not None
    
    @pytest.mark.asyncio
    async def test_qdrant_manager_client_configuration(self, mock_qdrant_client):
        """Test QDrant client configuration"""
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        with patch('config.QDRANT_HOST', 'localhost'), \
             patch('config.QDRANT_PORT', '6333'):
            
            await QDrantManager.setup()
            
            manager = QDrantManager.get_instance()
            assert manager.client is not None
    
    @pytest.mark.asyncio
    async def test_qdrant_manager_connection_error_handling(self):
        """Test QDrant connection error handling"""
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        with patch('managers.manager_qdrant.QdrantClient', side_effect=Exception("Connection failed")):
            
            with pytest.raises(Exception):
                await QDrantManager.setup()

class TestQDrantManagerCollectionOperations:
    """Test QDrant Manager collection operations"""
    
    @pytest.fixture
    async def setup_manager(self):
        """Setup QDrant manager for testing"""
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        with patch('managers.manager_qdrant.QdrantClient') as mock_client:
            mock_client_instance = Mock()
            mock_client.return_value = mock_client_instance
            
            await QDrantManager.setup()
            
            yield QDrantManager.get_instance(), mock_client_instance
    
    @pytest.mark.asyncio
    async def test_create_collection(self, setup_manager):
        """Test collection creation"""
        manager, mock_client = setup_manager
        
        mock_client.create_collection = Mock()
        
        await manager.create_collection("test_collection", 384)
        
        # Should call create_collection
        mock_client.create_collection.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_delete_collection(self, setup_manager):
        """Test collection deletion"""
        manager, mock_client = setup_manager
        
        mock_client.delete_collection = Mock()
        
        await manager.delete_collection("test_collection")
        
        # Should call delete_collection
        mock_client.delete_collection.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_list_collections(self, setup_manager):
        """Test listing collections"""
        manager, mock_client = setup_manager
        
        mock_client.get_collections = Mock(return_value=["collection1", "collection2"])
        
        collections = await manager.get_collections()
        
        # Should call get_collections
        mock_client.get_collections.assert_called_once()
        assert collections == ["collection1", "collection2"]
    
    @pytest.mark.asyncio
    async def test_collection_exists(self, setup_manager):
        """Test checking if collection exists"""
        manager, mock_client = setup_manager
        
        # Mock collection info response
        mock_client.get_collection = Mock(return_value={"status": "green"})
        
        exists = await manager.collection_exists("test_collection")
        
        # Should check collection existence
        mock_client.get_collection.assert_called_once_with("test_collection")
        assert exists == True
    
    @pytest.mark.asyncio
    async def test_collection_not_exists(self, setup_manager):
        """Test checking if collection doesn't exist"""
        manager, mock_client = setup_manager
        
        # Mock collection not found
        mock_client.get_collection = Mock(side_effect=Exception("Collection not found"))
        
        exists = await manager.collection_exists("nonexistent_collection")
        
        # Should return False for non-existent collection
        assert exists == False

class TestQDrantManagerVectorOperations:
    """Test QDrant Manager vector operations"""
    
    @pytest.fixture
    async def setup_manager(self):
        """Setup QDrant manager for testing"""
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        with patch('managers.manager_qdrant.QdrantClient') as mock_client:
            mock_client_instance = Mock()
            mock_client.return_value = mock_client_instance
            
            await QDrantManager.setup()
            
            yield QDrantManager.get_instance(), mock_client_instance
    
    @pytest.mark.asyncio
    async def test_upsert_vectors(self, setup_manager):
        """Test vector upsert operation"""
        manager, mock_client = setup_manager
        
        mock_client.upsert = Mock()
        
        vectors = [
            {"id": "1", "vector": [0.1, 0.2, 0.3], "payload": {"text": "test1"}},
            {"id": "2", "vector": [0.4, 0.5, 0.6], "payload": {"text": "test2"}}
        ]
        
        await manager.upsert_vectors("test_collection", vectors)
        
        # Should call upsert
        mock_client.upsert.assert_called_once()
        call_args = mock_client.upsert.call_args
        assert call_args[1]['collection_name'] == "test_collection"
    
    @pytest.mark.asyncio
    async def test_search_vectors(self, setup_manager):
        """Test vector search operation"""
        manager, mock_client = setup_manager
        
        # Mock search results
        mock_results = [
            Mock(id="1", score=0.95, payload={"text": "result1"}),
            Mock(id="2", score=0.85, payload={"text": "result2"})
        ]
        mock_client.search = Mock(return_value=mock_results)
        
        query_vector = [0.1, 0.2, 0.3]
        results = await manager.search_vectors("test_collection", query_vector, limit=5)
        
        # Should call search
        mock_client.search.assert_called_once()
        call_args = mock_client.search.call_args
        assert call_args[1]['collection_name'] == "test_collection"
        assert call_args[1]['query_vector'] == query_vector
        assert call_args[1]['limit'] == 5
        
        # Should return results
        assert len(results) == 2
        assert results[0].score == 0.95
        assert results[1].score == 0.85
    
    @pytest.mark.asyncio
    async def test_get_vectors(self, setup_manager):
        """Test vector retrieval operation"""
        manager, mock_client = setup_manager
        
        # Mock retrieval results
        mock_results = [
            Mock(id="1", vector=[0.1, 0.2, 0.3], payload={"text": "test1"}),
            Mock(id="2", vector=[0.4, 0.5, 0.6], payload={"text": "test2"})
        ]
        mock_client.retrieve = Mock(return_value=mock_results)
        
        vector_ids = ["1", "2"]
        results = await manager.get_vectors("test_collection", vector_ids)
        
        # Should call retrieve
        mock_client.retrieve.assert_called_once()
        call_args = mock_client.retrieve.call_args
        assert call_args[1]['collection_name'] == "test_collection"
        assert call_args[1]['ids'] == vector_ids
        
        # Should return results
        assert len(results) == 2
    
    @pytest.mark.asyncio
    async def test_delete_vectors(self, setup_manager):
        """Test vector deletion operation"""
        manager, mock_client = setup_manager
        
        mock_client.delete = Mock()
        
        vector_ids = ["1", "2", "3"]
        await manager.delete_vectors("test_collection", vector_ids)
        
        # Should call delete
        mock_client.delete.assert_called_once()
        call_args = mock_client.delete.call_args
        assert call_args[1]['collection_name'] == "test_collection"
        assert call_args[1]['points_selector'] == vector_ids

class TestQDrantManagerAdvancedOperations:
    """Test QDrant Manager advanced operations"""
    
    @pytest.fixture
    async def setup_manager(self):
        """Setup QDrant manager for testing"""
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        with patch('managers.manager_qdrant.QdrantClient') as mock_client:
            mock_client_instance = Mock()
            mock_client.return_value = mock_client_instance
            
            await QDrantManager.setup()
            
            yield QDrantManager.get_instance(), mock_client_instance
    
    @pytest.mark.asyncio
    async def test_search_with_filter(self, setup_manager):
        """Test vector search with filters"""
        manager, mock_client = setup_manager
        
        mock_results = [Mock(id="1", score=0.95, payload={"category": "test"})]
        mock_client.search = Mock(return_value=mock_results)
        
        query_vector = [0.1, 0.2, 0.3]
        search_filter = {"category": "test"}
        
        results = await manager.search_vectors_with_filter(
            "test_collection", query_vector, search_filter, limit=5
        )
        
        # Should call search with filter
        mock_client.search.assert_called_once()
        call_args = mock_client.search.call_args
        assert call_args[1]['collection_name'] == "test_collection"
        assert call_args[1]['query_vector'] == query_vector
        assert call_args[1]['query_filter'] == search_filter
    
    @pytest.mark.asyncio
    async def test_batch_upsert(self, setup_manager):
        """Test batch vector upsert"""
        manager, mock_client = setup_manager
        
        mock_client.upsert = Mock()
        
        large_batch = []
        for i in range(1000):
            large_batch.append({
                "id": str(i),
                "vector": [0.1 * i, 0.2 * i, 0.3 * i],
                "payload": {"text": f"test{i}"}
            })
        
        await manager.batch_upsert_vectors("test_collection", large_batch, batch_size=100)
        
        # Should call upsert multiple times for batching
        assert mock_client.upsert.call_count == 10  # 1000 / 100 = 10 batches
    
    @pytest.mark.asyncio
    async def test_update_collection_config(self, setup_manager):
        """Test collection configuration update"""
        manager, mock_client = setup_manager
        
        mock_client.update_collection = Mock()
        
        config = {"optimizers_config": {"deleted_threshold": 0.2}}
        await manager.update_collection_config("test_collection", config)
        
        # Should call update_collection
        mock_client.update_collection.assert_called_once()
        call_args = mock_client.update_collection.call_args
        assert call_args[1]['collection_name'] == "test_collection"
    
    @pytest.mark.asyncio
    async def test_get_collection_info(self, setup_manager):
        """Test getting collection information"""
        manager, mock_client = setup_manager
        
        mock_info = {
            "status": "green",
            "vectors_count": 1000,
            "segments_count": 5
        }
        mock_client.get_collection = Mock(return_value=mock_info)
        
        info = await manager.get_collection_info("test_collection")
        
        # Should call get_collection
        mock_client.get_collection.assert_called_once_with("test_collection")
        assert info == mock_info

class TestQDrantManagerErrorHandling:
    """Test QDrant Manager error handling"""
    
    @pytest.fixture
    async def setup_manager(self):
        """Setup QDrant manager for testing"""
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        with patch('managers.manager_qdrant.QdrantClient') as mock_client:
            mock_client_instance = Mock()
            mock_client.return_value = mock_client_instance
            
            await QDrantManager.setup()
            
            yield QDrantManager.get_instance(), mock_client_instance
    
    @pytest.mark.asyncio
    async def test_search_error_handling(self, setup_manager):
        """Test search error handling"""
        manager, mock_client = setup_manager
        
        mock_client.search = Mock(side_effect=Exception("Search failed"))
        
        with pytest.raises(Exception) as exc_info:
            await manager.search_vectors("test_collection", [0.1, 0.2, 0.3])
        
        assert "Search failed" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_upsert_error_handling(self, setup_manager):
        """Test upsert error handling"""
        manager, mock_client = setup_manager
        
        mock_client.upsert = Mock(side_effect=Exception("Upsert failed"))
        
        vectors = [{"id": "1", "vector": [0.1, 0.2, 0.3]}]
        
        with pytest.raises(Exception) as exc_info:
            await manager.upsert_vectors("test_collection", vectors)
        
        assert "Upsert failed" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_collection_creation_error_handling(self, setup_manager):
        """Test collection creation error handling"""
        manager, mock_client = setup_manager
        
        mock_client.create_collection = Mock(side_effect=Exception("Creation failed"))
        
        with pytest.raises(Exception) as exc_info:
            await manager.create_collection("test_collection", 384)
        
        assert "Creation failed" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_connection_timeout_handling(self, setup_manager):
        """Test connection timeout handling"""
        manager, mock_client = setup_manager
        
        mock_client.search = Mock(side_effect=TimeoutError("Connection timeout"))
        
        with pytest.raises(TimeoutError) as exc_info:
            await manager.search_vectors("test_collection", [0.1, 0.2, 0.3])
        
        assert "Connection timeout" in str(exc_info.value)

class TestQDrantManagerPerformance:
    """Test QDrant Manager performance characteristics"""
    
    @pytest.fixture
    async def setup_manager(self):
        """Setup QDrant manager for testing"""
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        with patch('managers.manager_qdrant.QdrantClient') as mock_client:
            mock_client_instance = Mock()
            mock_client.return_value = mock_client_instance
            
            await QDrantManager.setup()
            
            yield QDrantManager.get_instance(), mock_client_instance
    
    @pytest.mark.asyncio
    async def test_concurrent_searches(self, setup_manager):
        """Test concurrent search operations"""
        manager, mock_client = setup_manager
        
        mock_results = [Mock(id="1", score=0.95)]
        mock_client.search = AsyncMock(return_value=mock_results)
        
        # Perform concurrent searches
        search_requests = []
        for i in range(10):
            task = asyncio.create_task(
                manager.search_vectors(f"collection_{i}", [0.1, 0.2, 0.3])
            )
            search_requests.append(task)
        
        results = await asyncio.gather(*search_requests)
        
        # All searches should complete
        assert len(results) == 10
        for result in results:
            assert len(result) == 1
            assert result[0].score == 0.95
    
    @pytest.mark.asyncio
    async def test_large_batch_operations(self, setup_manager):
        """Test large batch operations"""
        manager, mock_client = setup_manager
        
        mock_client.upsert = Mock()
        
        # Create large batch
        large_batch = []
        for i in range(10000):
            large_batch.append({
                "id": str(i),
                "vector": [0.1] * 384,  # Typical embedding size
                "payload": {"text": f"document_{i}"}
            })
        
        # Should handle large batches efficiently
        await manager.batch_upsert_vectors("large_collection", large_batch, batch_size=1000)
        
        # Should batch appropriately
        assert mock_client.upsert.call_count == 10  # 10000 / 1000 = 10 batches
    
    @pytest.mark.asyncio
    async def test_memory_efficient_operations(self, setup_manager):
        """Test memory-efficient operations"""
        import sys
        
        manager, mock_client = setup_manager
        
        # Check memory usage of manager
        manager_size = sys.getsizeof(manager)
        
        # Should be memory efficient
        assert manager_size < 5000, f"Manager should be memory efficient, uses {manager_size} bytes"
    
    @pytest.mark.asyncio
    async def test_operation_timing(self, setup_manager):
        """Test operation timing"""
        import time
        
        manager, mock_client = setup_manager
        
        mock_client.search = Mock(return_value=[])
        
        start_time = time.time()
        
        # Perform multiple operations
        for _ in range(100):
            await manager.search_vectors("test_collection", [0.1, 0.2, 0.3])
        
        end_time = time.time()
        elapsed = end_time - start_time
        
        # Should complete quickly
        assert elapsed < 5.0, f"Operations should be fast, took {elapsed:.3f}s"

class TestQDrantManagerIntegration:
    """Test QDrant Manager integration scenarios"""
    
    @pytest.fixture
    async def setup_manager(self):
        """Setup QDrant manager for testing"""
        # Reset singleton for test
        QDrantManager._instance = None
        QDrantManager._initialized = False
        
        with patch('managers.manager_qdrant.QdrantClient') as mock_client:
            mock_client_instance = Mock()
            mock_client.return_value = mock_client_instance
            
            await QDrantManager.setup()
            
            yield QDrantManager.get_instance(), mock_client_instance
    
    @pytest.mark.asyncio
    async def test_rag_pipeline_integration(self, setup_manager):
        """Test RAG pipeline integration"""
        manager, mock_client = setup_manager
        
        # Mock search results for RAG
        mock_results = [
            Mock(id="doc1", score=0.95, payload={"content": "Relevant document 1"}),
            Mock(id="doc2", score=0.87, payload={"content": "Relevant document 2"})
        ]
        mock_client.search = Mock(return_value=mock_results)
        
        # Simulate RAG search
        query_embedding = [0.1, 0.2, 0.3]
        results = await manager.search_vectors("knowledge_base", query_embedding, limit=5)
        
        # Should return relevant documents
        assert len(results) == 2
        assert results[0].payload["content"] == "Relevant document 1"
        assert results[1].payload["content"] == "Relevant document 2"
    
    @pytest.mark.asyncio
    async def test_document_ingestion_workflow(self, setup_manager):
        """Test document ingestion workflow"""
        manager, mock_client = setup_manager
        
        mock_client.upsert = Mock()
        mock_client.create_collection = Mock()
        
        # Simulate document ingestion
        collection_name = "documents"
        
        # Create collection
        await manager.create_collection(collection_name, 384)
        
        # Ingest documents
        documents = [
            {
                "id": f"doc_{i}",
                "vector": [0.1] * 384,
                "payload": {
                    "content": f"Document {i} content",
                    "source": f"source_{i}.pdf",
                    "page": i % 10
                }
            }
            for i in range(100)
        ]
        
        await manager.batch_upsert_vectors(collection_name, documents, batch_size=50)
        
        # Should create collection and ingest documents
        mock_client.create_collection.assert_called_once()
        assert mock_client.upsert.call_count == 2  # 100 / 50 = 2 batches
    
    @pytest.mark.asyncio
    async def test_semantic_search_workflow(self, setup_manager):
        """Test semantic search workflow"""
        manager, mock_client = setup_manager
        
        # Mock semantic search results
        search_results = [
            Mock(
                id="semantic_1",
                score=0.92,
                payload={
                    "content": "Machine learning is a subset of artificial intelligence",
                    "category": "AI"
                }
            ),
            Mock(
                id="semantic_2", 
                score=0.88,
                payload={
                    "content": "Deep learning uses neural networks with multiple layers",
                    "category": "AI"
                }
            )
        ]
        mock_client.search = Mock(return_value=search_results)
        
        # Perform semantic search
        query_vector = [0.2, 0.3, 0.4]
        results = await manager.search_vectors_with_filter(
            "semantic_search",
            query_vector,
            {"category": "AI"},
            limit=10
        )
        
        # Should return semantically relevant results
        assert len(results) == 2
        assert "machine learning" in results[0].payload["content"].lower()
        assert "deep learning" in results[1].payload["content"].lower()
    
    @pytest.mark.asyncio
    async def test_multi_collection_management(self, setup_manager):
        """Test managing multiple collections"""
        manager, mock_client = setup_manager
        
        mock_client.create_collection = Mock()
        mock_client.get_collections = Mock(return_value=["collection1", "collection2", "collection3"])
        
        # Create multiple collections
        collections = ["documents", "embeddings", "knowledge_base"]
        
        for collection in collections:
            await manager.create_collection(collection, 384)
        
        # List all collections
        all_collections = await manager.get_collections()
        
        # Should manage multiple collections
        assert mock_client.create_collection.call_count == 3
        assert len(all_collections) == 3