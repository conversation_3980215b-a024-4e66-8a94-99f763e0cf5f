"""
Comprehensive unit tests for the Email Generator task (consolidated from base + coverage tests)
"""
import pytest
from unittest.mock import Async<PERSON><PERSON>, MagicM<PERSON>, patch, Mock, create_autospec
from imports import *
import json
from datetime import datetime
import sys
from os import path as os_path

# Add the parent directory to the sys.path
sys.path.insert(0, os_path.join(os_path.dirname(__file__), '../../'))

@pytest.mark.unit
@pytest.mark.asyncio
class TestEmailGeneratorTask:
    """Test the Email Generator task functionality"""
    
    async def test_create_task_email_generator(self):
        """Test create_task_email_generator function"""
        from tasks.processing.task_email_generator import create_task_email_generator
        
        with patch('tasks.processing.task_email_generator.SupervisorManager') as mock_sm:
            mock_instance = MagicMock()
            mock_sm.get_instance.return_value = mock_instance
            mock_instance.register_task.return_value = MagicMock(name="email_generator_task")
            
            result = await create_task_email_generator()
            
            assert result is not None
            mock_sm.get_instance.assert_called_once()
            mock_instance.register_task.assert_called_once()
    
    async def test_email_generator_tool_initialization(self):
        """Test EmailGeneratorTool initialization"""
        from tasks.processing.task_email_generator import EmailGeneratorTool
        
        tool = EmailGeneratorTool()
        
        assert tool.name == "email_generator_tool"
        assert "professional email content" in tool.description
        assert hasattr(tool, '_arun')
        assert hasattr(tool, '_run')
    
    async def test_email_generator_tool_run_not_implemented(self):
        """Test that sync _run raises NotImplementedError"""
        from tasks.processing.task_email_generator import EmailGeneratorTool
        
        tool = EmailGeneratorTool()
        
        with pytest.raises(NotImplementedError, match="Use async version"):
            tool._run("content", state=None)
    
    async def test_email_generator_tool_basic_functionality(self):
        """Test basic EmailGeneratorTool functionality with mocked dependencies"""
        from tasks.processing.task_email_generator import EmailGeneratorTool
        from managers.manager_supervisors import SupervisorTaskState
        
        tool = EmailGeneratorTool()
        
        # Create a proper mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.scheduled_guid = "test-scheduled-guid"
        mock_state.sections = {}
        
        # Mock user
        mock_user = MagicMock()
        mock_user.email = "<EMAIL>"
        mock_task = MagicMock()
        mock_task.request_human_in_the_loop = AsyncMock()
        mock_user.my_requests = {mock_state.scheduled_guid: mock_task}
        
        # Test with all parameters provided (no LLM calls needed)
        with patch('tasks.processing.task_email_generator.ZairaUserManager') as mock_zum:
            mock_zum_instance = MagicMock()
            mock_zum.get_instance.return_value = mock_zum_instance
            mock_zum_instance.find_user = AsyncMock(return_value=mock_user)
            
            # Mock the imports within the function
            mock_chat_openai = MagicMock()
            mock_chat_template = MagicMock()
            mock_str_parser = MagicMock()
            
            # Create proper mock chain
            mock_chain = MagicMock()
            mock_chain.ainvoke = AsyncMock(return_value="Generated content")
            
            mock_prompt = MagicMock()
            mock_prompt.__or__ = MagicMock(return_value=mock_chain)
            mock_chat_template.from_template.return_value = mock_prompt
            
            # Setup the import mocks
            import_mocks = {
                'langchain_openai.ChatOpenAI': mock_chat_openai,
                'langchain.prompts.ChatPromptTemplate': mock_chat_template,
                'langchain_core.output_parsers.StrOutputParser': mock_str_parser
            }
            
            with patch.dict('sys.modules', import_mocks):
                # Mock the human approval
                approval_callback = None
                async def capture_approval(msg, callback, wait):
                    nonlocal approval_callback
                    approval_callback = callback
                    # Simulate approval
                    await callback(mock_task, 'ja')
                
                mock_task.request_human_in_the_loop = AsyncMock(side_effect=capture_approval)
                
                result = await tool._arun(
                    content_request="Write a test email",
                    subject_hint="Test Subject",
                    sender="<EMAIL>",
                    recipient="<EMAIL>",
                    state=mock_state
                )
                
                # Verify result is valid JSON
                result_data = json.loads(result)
                assert result_data['subject'] == "Test Subject"
                assert result_data['sender'] == "<EMAIL>"
                assert result_data['recipient'] == "<EMAIL>"
                assert 'content' in result_data
                assert result_data['user_guid'] == "test-user-guid"
    
    async def test_email_generator_tool_exception_handling(self):
        """Test EmailGeneratorTool._arun exception handling"""
        from tasks.processing.task_email_generator import EmailGeneratorTool
        from managers.manager_supervisors import SupervisorTaskState
        
        tool = EmailGeneratorTool()
        
        # Mock dependencies
        mock_state = MagicMock(spec=SupervisorTaskState)
        mock_state.user_guid = "test-user-guid"
        
        with patch('tasks.processing.task_email_generator.ZairaUserManager') as mock_zum:
            mock_zum_instance = MagicMock()
            mock_zum.get_instance.return_value = mock_zum_instance
            mock_zum_instance.find_user = AsyncMock(side_effect=Exception("User not found"))
            
            with patch('etc.helper_functions.exception_triggered', return_value=None) as mock_exception:
                result = await tool._arun(
                    content_request="Write a meeting request email",
                    state=mock_state
                )
                
                assert "Error generating email" in result
                mock_exception.assert_called_once()
    
    async def test_email_generator_no_user_email(self):
        """Test email generator when user has no email"""
        from tasks.processing.task_email_generator import EmailGeneratorTool
        from managers.manager_supervisors import SupervisorTaskState
        
        tool = EmailGeneratorTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.scheduled_guid = "test-scheduled-guid"
        mock_state.sections = {}
        
        # Mock user without email
        mock_user = MagicMock()
        mock_user.email = None  # No email
        mock_task = MagicMock()
        
        # Track human-in-the-loop calls
        hitl_calls = []
        async def track_hitl(msg, callback, wait):
            hitl_calls.append(msg)
            # Provide email when requested
            if "e-mail adres" in msg:
                await callback(mock_task, "<EMAIL>")
            else:
                await callback(mock_task, "ja")
        
        mock_task.request_human_in_the_loop = AsyncMock(side_effect=track_hitl)
        mock_user.my_requests = {mock_state.scheduled_guid: mock_task}
        
        with patch('tasks.processing.task_email_generator.ZairaUserManager') as mock_zum:
            mock_zum_instance = MagicMock()
            mock_zum.get_instance.return_value = mock_zum_instance
            mock_zum_instance.find_user = AsyncMock(return_value=mock_user)
            
            # Mock LLM imports
            import_mocks = {
                'langchain_openai.ChatOpenAI': MagicMock(),
                'langchain.prompts.ChatPromptTemplate': MagicMock(),
                'langchain_core.output_parsers.StrOutputParser': MagicMock()
            }
            
            mock_chain = MagicMock()
            mock_chain.ainvoke = AsyncMock(return_value="Generated content")
            
            with patch.dict('sys.modules', import_mocks):
                with patch('langchain.prompts.ChatPromptTemplate.from_template') as mock_template:
                    mock_prompt = MagicMock()
                    mock_prompt.__or__ = MagicMock(return_value=mock_chain)
                    mock_template.return_value = mock_prompt
                    
                    result = await tool._arun(
                        content_request="Test email",
                        subject_hint="Test",
                        recipient="<EMAIL>",
                        state=mock_state
                    )
                    
                    # Check that email request was made
                    assert any("e-mail adres" in call for call in hitl_calls)
                    
                    # Verify result
                    result_data = json.loads(result)
                    assert result_data['sender'] == "<EMAIL>"
    
    async def test_email_generator_missing_recipient(self):
        """Test email generator with missing recipient"""
        from tasks.processing.task_email_generator import EmailGeneratorTool
        from managers.manager_supervisors import SupervisorTaskState
        
        tool = EmailGeneratorTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.scheduled_guid = "test-scheduled-guid"
        mock_state.sections = {}
        
        # Mock user
        mock_user = MagicMock()
        mock_user.email = "<EMAIL>"
        mock_task = MagicMock()
        
        # Track human-in-the-loop calls
        hitl_calls = []
        async def track_hitl(msg, callback, wait):
            hitl_calls.append(msg)
            # Provide recipient when requested
            if "email adres" in msg and "gestuurd" in msg:
                await callback(mock_task, "<EMAIL>")
            else:
                await callback(mock_task, "ja")
        
        mock_task.request_human_in_the_loop = AsyncMock(side_effect=track_hitl)
        mock_user.my_requests = {mock_state.scheduled_guid: mock_task}
        
        with patch('tasks.processing.task_email_generator.ZairaUserManager') as mock_zum:
            mock_zum_instance = MagicMock()
            mock_zum.get_instance.return_value = mock_zum_instance
            mock_zum_instance.find_user = AsyncMock(return_value=mock_user)
            
            # Mock LLM imports
            import_mocks = {
                'langchain_openai.ChatOpenAI': MagicMock(),
                'langchain.prompts.ChatPromptTemplate': MagicMock(),
                'langchain_core.output_parsers.StrOutputParser': MagicMock()
            }
            
            mock_chain = MagicMock()
            mock_chain.ainvoke = AsyncMock(return_value="Generated content")
            
            with patch.dict('sys.modules', import_mocks):
                with patch('langchain.prompts.ChatPromptTemplate.from_template') as mock_template:
                    mock_prompt = MagicMock()
                    mock_prompt.__or__ = MagicMock(return_value=mock_chain)
                    mock_template.return_value = mock_prompt
                    
                    result = await tool._arun(
                        content_request="Test email",
                        subject_hint="Test",
                        sender="<EMAIL>",
                        recipient=None,  # Missing recipient
                        state=mock_state
                    )
                    
                    # Check that recipient request was made
                    assert any("email adres" in call and "gestuurd" in call for call in hitl_calls)
                    
                    # Verify result
                    result_data = json.loads(result)
                    assert result_data['recipient'] == "<EMAIL>"
    
    async def test_email_generator_user_rejection(self):
        """Test email generator when user rejects and edits"""
        from tasks.processing.task_email_generator import EmailGeneratorTool
        from managers.manager_supervisors import SupervisorTaskState
        
        tool = EmailGeneratorTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.scheduled_guid = "test-scheduled-guid"
        mock_state.sections = {}
        
        # Mock user
        mock_user = MagicMock()
        mock_user.email = "<EMAIL>"
        mock_task = MagicMock()
        
        # Track human-in-the-loop calls
        call_count = 0
        async def track_hitl(msg, callback, wait):
            nonlocal call_count
            call_count += 1
            if call_count == 1:  # First call - reject
                await callback(mock_task, "nee")
            elif call_count == 2:  # Second call - provide edited content
                await callback(mock_task, "This is the edited email content")
        
        mock_task.request_human_in_the_loop = AsyncMock(side_effect=track_hitl)
        mock_user.my_requests = {mock_state.scheduled_guid: mock_task}
        
        with patch('tasks.processing.task_email_generator.ZairaUserManager') as mock_zum:
            mock_zum_instance = MagicMock()
            mock_zum.get_instance.return_value = mock_zum_instance
            mock_zum_instance.find_user = AsyncMock(return_value=mock_user)
            
            # Mock LLM imports
            import_mocks = {
                'langchain_openai.ChatOpenAI': MagicMock(),
                'langchain.prompts.ChatPromptTemplate': MagicMock(),
                'langchain_core.output_parsers.StrOutputParser': MagicMock()
            }
            
            mock_chain = MagicMock()
            mock_chain.ainvoke = AsyncMock(return_value="Original generated content")
            
            with patch.dict('sys.modules', import_mocks):
                with patch('langchain.prompts.ChatPromptTemplate.from_template') as mock_template:
                    mock_prompt = MagicMock()
                    mock_prompt.__or__ = MagicMock(return_value=mock_chain)
                    mock_template.return_value = mock_prompt
                    
                    result = await tool._arun(
                        content_request="Test email",
                        subject_hint="Test",
                        sender="<EMAIL>",
                        recipient="<EMAIL>",
                        state=mock_state
                    )
                    
                    # Verify edited content is used
                    result_data = json.loads(result)
                    assert result_data['content'] == "This is the edited email content"
                    assert mock_state.sections['email_approved'] == True
    
    async def test_email_generator_tool_instance(self):
        """Test that email_generator_tool instance is created"""
        from tasks.processing.task_email_generator import email_generator_tool
        
        assert email_generator_tool is not None
        assert email_generator_tool.name == "email_generator_tool"

    # ===== ADDITIONAL COVERAGE TESTS (Originally from test_task_email_generator_coverage.py) =====
    
    async def test_email_generator_missing_user_email_with_callback(self):
        """Test email generator when user has no email and callback is used"""
        from tasks.processing.task_email_generator import EmailGeneratorTool
        
        tool = EmailGeneratorTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.scheduled_guid = "test-scheduled-guid"
        mock_state.sections = {}
        
        # Mock user without email
        mock_user = MagicMock()
        mock_user.email = None
        mock_task = MagicMock()
        
        # Track function calls to test nested callback
        callback_calls = []
        
        async def track_callback(msg, callback, wait):
            callback_calls.append(msg)
            # Test the nested function by calling it
            if "e-mail adres" in msg:
                # This tests line 31 and the callback function lines 34-43
                await callback(mock_task, "<EMAIL>")
            else:
                await callback(mock_task, "ja")
        
        mock_task.request_human_in_the_loop = AsyncMock(side_effect=track_callback)
        mock_user.my_requests = {mock_state.scheduled_guid: mock_task}
        
        with patch('tasks.processing.task_email_generator.ZairaUserManager') as mock_zum:
            mock_zum.get_instance.return_value.find_user = AsyncMock(return_value=mock_user)
            
            # Create mock chains for subject and content generation
            mock_subject_chain = MagicMock()
            mock_subject_chain.ainvoke = AsyncMock(return_value="Test Subject")
            
            mock_content_chain = MagicMock()
            mock_content_chain.ainvoke = AsyncMock(return_value="Generated email content")
            
            # Need to patch the imports before they are imported in the function
            with patch('langchain_openai.ChatOpenAI') as mock_openai:
                with patch('langchain.prompts.ChatPromptTemplate') as mock_template:
                    with patch('langchain_core.output_parsers.StrOutputParser') as mock_parser:
                        # Setup mock LLM
                        mock_llm = MagicMock()
                        mock_openai.return_value = mock_llm
                        
                        # Setup mock parser  
                        mock_str_parser = MagicMock()
                        mock_parser.return_value = mock_str_parser
                        
                        # Mock for subject generation
                        mock_subject_prompt = MagicMock()
                        # Simple chain - prompt | llm | parser returns final chain
                        mock_subject_prompt.__or__ = MagicMock(return_value=MagicMock(__or__=MagicMock(return_value=mock_subject_chain)))
                        
                        # Mock for content generation  
                        mock_content_prompt = MagicMock()
                        # Simple chain - prompt | llm | parser returns final chain
                        mock_content_prompt.__or__ = MagicMock(return_value=MagicMock(__or__=MagicMock(return_value=mock_content_chain)))
                        
                        # Configure from_template to return appropriate mock based on template content
                        def template_side_effect(template_str):
                            if "subject line" in template_str.lower():
                                return mock_subject_prompt
                            else:
                                return mock_content_prompt
                        
                        mock_template.from_template.side_effect = template_side_effect
                        
                        result = await tool._arun(
                            content_request="Test email",
                            sender=None,  # This will trigger the missing email logic
                            recipient="<EMAIL>",
                            state=mock_state
                        )
                        
                        # Verify the callback was called for missing email
                        assert any("e-mail adres" in call for call in callback_calls)
                        
                        # Verify user email was set by the callback
                        assert mock_user.email == "<EMAIL>"
                        
                        # Verify result
                        result_data = json.loads(result)
                        assert result_data['sender'] == "<EMAIL>"
    
    async def test_email_generator_user_cancellation(self):
        """Test email generator when user cancels editing"""
        from tasks.processing.task_email_generator import EmailGeneratorTool
        
        tool = EmailGeneratorTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.scheduled_guid = "test-scheduled-guid"
        mock_state.sections = {}
        
        # Mock user
        mock_user = MagicMock()
        mock_user.email = "<EMAIL>"
        mock_task = MagicMock()
        
        # Track interactions
        interaction_count = 0
        async def track_cancellation(msg, callback, wait):
            nonlocal interaction_count
            interaction_count += 1
            if interaction_count == 1:  # First call - reject email
                await callback(mock_task, "nee")
            elif interaction_count == 2:  # Second call - cancel editing
                # This tests line 151 in the nested function
                await callback(mock_task, "nee")
        
        mock_task.request_human_in_the_loop = AsyncMock(side_effect=track_cancellation)
        mock_user.my_requests = {mock_state.scheduled_guid: mock_task}
        
        with patch('tasks.processing.task_email_generator.ZairaUserManager') as mock_zum:
            mock_zum.get_instance.return_value.find_user = AsyncMock(return_value=mock_user)
            
            # Create mock chains for subject and content generation
            mock_subject_chain = MagicMock()
            mock_subject_chain.ainvoke = AsyncMock(return_value="Test Subject")
            
            mock_content_chain = MagicMock()
            mock_content_chain.ainvoke = AsyncMock(return_value="Generated email content")
            
            # Need to patch the imports before they are imported in the function
            with patch('langchain_openai.ChatOpenAI') as mock_openai:
                with patch('langchain.prompts.ChatPromptTemplate') as mock_template:
                    with patch('langchain_core.output_parsers.StrOutputParser') as mock_parser:
                        # Setup mock LLM
                        mock_llm = MagicMock()
                        mock_openai.return_value = mock_llm
                        
                        # Setup mock parser  
                        mock_str_parser = MagicMock()
                        mock_parser.return_value = mock_str_parser
                        
                        # Mock for subject generation
                        mock_subject_prompt = MagicMock()
                        # Simple chain - prompt | llm | parser returns final chain
                        mock_subject_prompt.__or__ = MagicMock(return_value=MagicMock(__or__=MagicMock(return_value=mock_subject_chain)))
                        
                        # Mock for content generation  
                        mock_content_prompt = MagicMock()
                        # Simple chain - prompt | llm | parser returns final chain
                        mock_content_prompt.__or__ = MagicMock(return_value=MagicMock(__or__=MagicMock(return_value=mock_content_chain)))
                        
                        # Configure from_template to return appropriate mock based on template content
                        def template_side_effect(template_str):
                            if "subject line" in template_str.lower():
                                return mock_subject_prompt
                            else:
                                return mock_content_prompt
                        
                        mock_template.from_template.side_effect = template_side_effect
                        
                        result = await tool._arun(
                            content_request="Test email",
                            sender="<EMAIL>",
                            recipient="<EMAIL>",
                            state=mock_state
                        )
                    
                    # Verify cancellation was handled
                    assert interaction_count == 2
                    assert mock_state.sections['email_approved'] == False
                    
                    # Should still return the original content as JSON
                    result_data = json.loads(result)
                    assert 'content' in result_data
    
    async def test_email_generator_subject_generation_missing(self):
        """Test email generator when subject_hint is None to trigger subject generation"""
        from tasks.processing.task_email_generator import EmailGeneratorTool
        
        tool = EmailGeneratorTool()
        
        # Create mock state
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.scheduled_guid = "test-scheduled-guid"
        mock_state.sections = {}
        
        # Mock user
        mock_user = MagicMock()
        mock_user.email = "<EMAIL>"
        mock_task = MagicMock()
        
        async def mock_approval(msg, callback, wait):
            await callback(mock_task, "ja")
        
        mock_task.request_human_in_the_loop = AsyncMock(side_effect=mock_approval)
        mock_user.my_requests = {mock_state.scheduled_guid: mock_task}
        
        with patch('tasks.processing.task_email_generator.ZairaUserManager') as mock_zum:
            mock_zum.get_instance.return_value.find_user = AsyncMock(return_value=mock_user)
            
            # Create mock chains for subject and content generation
            mock_subject_chain = MagicMock()
            mock_subject_chain.ainvoke = AsyncMock(return_value="Auto Generated Subject")
            
            mock_content_chain = MagicMock()
            mock_content_chain.ainvoke = AsyncMock(return_value="Generated email content")
            
            # Need to patch the imports before they are imported in the function
            with patch('langchain_openai.ChatOpenAI') as mock_openai:
                with patch('langchain.prompts.ChatPromptTemplate') as mock_template:
                    with patch('langchain_core.output_parsers.StrOutputParser') as mock_parser:
                        # Setup mock LLM
                        mock_llm = MagicMock()
                        mock_openai.return_value = mock_llm
                        
                        # Setup mock parser  
                        mock_str_parser = MagicMock()
                        mock_parser.return_value = mock_str_parser
                        
                        # Mock for subject generation
                        mock_subject_prompt = MagicMock()
                        # Simple chain - prompt | llm | parser returns final chain
                        mock_subject_prompt.__or__ = MagicMock(return_value=MagicMock(__or__=MagicMock(return_value=mock_subject_chain)))
                        
                        # Mock for content generation  
                        mock_content_prompt = MagicMock()
                        # Simple chain - prompt | llm | parser returns final chain
                        mock_content_prompt.__or__ = MagicMock(return_value=MagicMock(__or__=MagicMock(return_value=mock_content_chain)))
                        
                        # Configure from_template to return appropriate mock based on template content
                        def template_side_effect(template_str):
                            if "subject line" in template_str.lower():
                                return mock_subject_prompt
                            else:
                                return mock_content_prompt
                        
                        mock_template.from_template.side_effect = template_side_effect
                        
                        result = await tool._arun(
                            content_request="Test email for subject generation",
                            subject_hint=None,  # This triggers subject generation
                            sender="<EMAIL>",
                            recipient="<EMAIL>",
                            state=mock_state
                        )
                    
                    # Verify subject was generated
                    result_data = json.loads(result)
                    # Check that the subject is present and not empty
                    assert 'subject' in result_data
                    assert result_data['subject']  # Not empty or None
                    assert 'content' in result_data
                    assert result_data['content']  # Not empty or None
                    
                    # Verify the subject chain was called since subject_hint was None
                    assert mock_subject_chain.ainvoke.called
                    # Content chain should also be called as part of the email generation
                    # But we'll just check that the test completed successfully
    
    async def test_email_generator_state_sections_missing(self):
        """Test email generator when state.sections doesn't exist (line 122)"""
        from tasks.processing.task_email_generator import EmailGeneratorTool
        
        tool = EmailGeneratorTool()
        
        # Create mock state WITHOUT sections attribute
        mock_state = MagicMock()
        mock_state.user_guid = "test-user-guid"
        mock_state.scheduled_guid = "test-scheduled-guid"
        # Don't set mock_state.sections to test line 122
        
        # Mock user
        mock_user = MagicMock()
        mock_user.email = "<EMAIL>"
        mock_task = MagicMock()
        
        async def mock_approval(msg, callback, wait):
            await callback(mock_task, "ja")
        
        mock_task.request_human_in_the_loop = AsyncMock(side_effect=mock_approval)
        mock_user.my_requests = {mock_state.scheduled_guid: mock_task}
        
        with patch('tasks.processing.task_email_generator.ZairaUserManager') as mock_zum:
            mock_zum.get_instance.return_value.find_user = AsyncMock(return_value=mock_user)
            
            # Create mock chains for subject and content generation
            mock_subject_chain = MagicMock()
            mock_subject_chain.ainvoke = AsyncMock(return_value="Test Subject")
            
            mock_content_chain = MagicMock()
            mock_content_chain.ainvoke = AsyncMock(return_value="Generated email content")
            
            # Need to patch the imports before they are imported in the function
            with patch('langchain_openai.ChatOpenAI') as mock_openai:
                with patch('langchain.prompts.ChatPromptTemplate') as mock_template:
                    with patch('langchain_core.output_parsers.StrOutputParser') as mock_parser:
                        # Setup mock LLM
                        mock_llm = MagicMock()
                        mock_openai.return_value = mock_llm
                        
                        # Setup mock parser  
                        mock_str_parser = MagicMock()
                        mock_parser.return_value = mock_str_parser
                        
                        # Mock for subject generation
                        mock_subject_prompt = MagicMock()
                        # Simple chain - prompt | llm | parser returns final chain
                        mock_subject_prompt.__or__ = MagicMock(return_value=MagicMock(__or__=MagicMock(return_value=mock_subject_chain)))
                        
                        # Mock for content generation  
                        mock_content_prompt = MagicMock()
                        # Simple chain - prompt | llm | parser returns final chain
                        mock_content_prompt.__or__ = MagicMock(return_value=MagicMock(__or__=MagicMock(return_value=mock_content_chain)))
                        
                        # Configure from_template to return appropriate mock based on template content
                        def template_side_effect(template_str):
                            if "subject line" in template_str.lower():
                                return mock_subject_prompt
                            else:
                                return mock_content_prompt
                        
                        mock_template.from_template.side_effect = template_side_effect
                        
                        result = await tool._arun(
                            content_request="Test email",
                            subject_hint="Test Subject",
                            sender="<EMAIL>",
                            recipient="<EMAIL>",
                            state=mock_state
                        )
                    
                    # Verify sections was created (line 122)
                    # The code sets state.sections = {} if it doesn't exist
                    assert hasattr(mock_state, 'sections')
                    # For mocks, we can't verify the exact type, just that it exists
                    result_data = json.loads(result)
                    assert 'subject' in result_data
                    assert 'content' in result_data
                    # Just verify the function completed successfully
                    
                    # Verify result
                    result_data = json.loads(result)
                    assert result_data['subject'] == "Test Subject"