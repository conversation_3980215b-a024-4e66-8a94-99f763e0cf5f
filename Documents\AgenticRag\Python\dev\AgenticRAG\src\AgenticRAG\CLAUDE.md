# CLAUDE

## Project Overview

**Ask<PERSON>air<PERSON>** (AgenticRAG) is a sophisticated multi-agent AI assistant system built on LangGraph supervisors with LlamaIndex RAG capabilities. The system implements hierarchical task coordination with multiple communication channels (<PERSON>rd, <PERSON>lack, <PERSON>, WhatsApp) and supports natural language scheduled task management with PostgreSQL persistence.

**Key Features:**
- Multi-agent LangGraph supervisors with hierarchical routing
- LlamaIndex RAG pipeline with hybrid search capabilities
- Multiple communication endpoints (Discord, Teams, WhatsApp, HTTP)
- Natural language scheduled task management
- PostgreSQL persistence with connection pooling
- Qdrant vector database for semantic search
- OAuth 2.0 authentication system
- Comprehensive testing framework

## Table of Contents
1. [Env](#1-environment-setup)
2. [Quick](#2-quick-start)
3. [Std](#3-critical-standards)
4. [Arch](#4-architecture)
5. [Dev](#5-development-patterns)
6. [Perf](#6-performance--security)
7. [Test](#7-testing)
8. [Snippets](#8-code-snippets-library)
9. [Debug](#9-troubleshooting)
10. [Ref](#10-reference)
11. [Docs](#11-documentation--references)
12. [Prompts](#12-prompt-slot-allocation)
13. [Manual](#13-manual-testing)
---

## 1. Environment Setup
### 1.1 Prerequisites
**Required Software:**
- Python 3.11+
- Poetry (dependency management)
- Docker Desktop
- PostgreSQL 15+
- Git

### 1.2 Environment Variables
```bash
# Core Configuration
CLAUDE_CODE=1
ANTHROPIC_USER_ID=claude-dev
DEBUG=True

# Database Configuration
POSTGRES_HOST=localhost
POSTGRES_PORT=5432
POSTGRES_DB=vectordb  # ONLY "vectordb" or "meltanodb"

# External Services
OPENAI_API_KEY=sk-your-key
DISCORD_TOKEN=your-token

# Vector Database
QDRANT_HOST=localhost
QDRANT_PORT=6333
```

### 1.3 Setup Commands
```bash
# Navigate to project
cd /path/to/AgenticRAG/src/AgenticRAG

# Install dependencies (user runs this)
poetry install --with dev
poetry shell

# Start databases
docker-compose up -d postgres qdrant

# Initialize databases
python -c "from etc.setup import initialize_databases; initialize_databases()"

# Verify setup
python -c "from etc.setup import verify_environment; verify_environment()"
```

### 1.4 IDE Configuration
**VS Code (`.vscode/settings.json`):**
```json
{
  "python.defaultInterpreter": "../../.venv/Scripts/python.exe",
  "python.testing.pytestEnabled": true,
  "python.testing.pytestArgs": ["tests/"],
  "files.encoding": "utf8"
}
```

---

## 2. Quick Start
### 2.1 Commands
```bash
# Run application
../../.venv/Scripts/python.exe dev_run.py

# Run tests (always from src/AgenticRAG/)
../../.venv/Scripts/pytest.exe tests/health/ -v
../../.venv/Scripts/pytest.exe tests/integration/ -v
../../.venv/Scripts/pytest.exe tests/performance/ -v
../../.venv/Scripts/pytest.exe tests/stress/ -v
../../.venv/Scripts/pytest.exe tests/test_real/ -v
../../.venv/Scripts/pytest.exe tests/unit/ -v

# Dependencies (user runs)
poetry add --group dev <package-name>
```
### 2.2 Key Environment Variables
```bash
CLAUDE_CODE=1  # Auto-detected
DEBUG=True     # Development mode
```

---

## 3. Critical Standards
### 3.1 Character Encoding (CRITICAL)
**MANDATORY: ASCII-only characters in ALL code files**
```python
# CORRECT
status = "Complete [OK]"
print("Task failed [ERROR]")

# WRONG - Will crash on Windows!
status = "Complete ✓"  # Unicode
print("Task failed ❌")  # Emoji
```
### 3.2 Exception Handling
```python
# ALWAYS use centralized handling
try:
    result = await operation()
except Exception as e:
    from etc.helper_functions import exception_triggered
    # Get user's chat session for exception logging
    chat_session = user.get_current_chat_session() if user else None
    exception_triggered(e, "context", chat_session)
```
### 3.3 Imports
```python
# REQUIRED - Start every file with
from imports import *

# For large libraries
from os import path as os_path
from os import environ as os_environ
```
### 3.4 Database Names (MANDATORY)
**CRITICAL: ALWAYS use "vectordb" or "meltanodb" database names unless explicitly instructed otherwise.**

**Default Database Names:**
- **Primary choice**: `"vectordb"` - Main database for all operations
- **Alternative choice**: `"meltanodb"` - Secondary database option
- **FORBIDDEN**: Creating new database names without explicit instruction

**Database Usage Pattern:**
```python
# CORRECT - Always use these database names:
connection = await PostgreSQLManager.get_connection("vectordb")
await PostgreSQLManager.create_database("vectordb") 
await PostgreSQLManager.get_connection("vectordb")

# ACCEPTABLE - Alternative database name:
connection = await PostgreSQLManager.get_connection("meltanodb")

# WRONG - Never create arbitrary database names:
connection = await PostgreSQLManager.get_connection("mydb")  # Crashes!
connection = await PostgreSQLManager.get_connection("testdb")  # Crashes!
```

---

## 4. Architecture
### 4.1 Directory Structure
```
src/AgenticRAG/
├── managers/        # Singleton managers
├── tasks/           # LangGraph agent tasks
│   ├── inputs/      # Input processing
│   ├── processing/  # Core processing
│   ├── outputs/     # Output handling
│   └── scheduled/   # Scheduled tasks
├── endpoints/       # Communication channels
├── userprofiles/    # User management
├── etc/             # Utilities
└── tests/           # Test suite
    ├── unit/        # Component tests
    ├── integration/ # End-to-end tests
    └── manual/      # Human-guided testing procedures
```
### 4.2 System Flow
```
[User] → [Endpoint] → [Supervisor] → [Task] → [Tool] → [Database] → [Response]
```
### 4.3 Core Patterns
- **Multi-Agent**: LangGraph supervisors with hierarchical routing
- **Singleton**: All managers use `get_instance()`
- **Async-First**: All operations are async
- **Centralized Imports**: Single `imports.py` file

---

## 5. Development Patterns
### 5.1 Pydantic Classes (REQUIRED)
```python
from pydantic import BaseModel, Field
from datetime import datetime
from uuid import uuid4

class MyClass(BaseModel):
    user_guid: str = Field(default_factory=lambda: str(uuid4()))
    name: str = Field(..., min_length=1, description="Required name")
    created_at: datetime = Field(default_factory=datetime.now)
    
    class Config:
        validate_assignment = True
```
### 5.2 Manager Singleton (REQUIRED)
```python
class MyManager:
    _instance: Optional['MyManager'] = None
    _initialized: bool = False
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_instance(cls) -> "MyManager":
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        if instance._initialized:
            return
        # Init logic
        instance._initialized = True

# USAGE
manager = MyManager.get_instance()  # CORRECT
manager = MyManager()  # WRONG!
```
### 5.3 BaseTool Development (REQUIRED)
```python
from langchain_core.tools import BaseTool

class MyTool(BaseTool):
    name: str = "my_tool"
    description: str = "Tool description"
    
    def _run(self, input_param: str) -> str:
        raise NotImplementedError("Use async version")
    
    async def _arun(self, input_param: str) -> str:
        try:
            result = await process(input_param)
            return f"Result: {result}"
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "my_tool", None)
            return "Error occurred"

# Create instance
my_tool = MyTool()
```
### 5.4 Prompt ID Management (CRITICAL)
**MUST update BOTH files:**

1. **Add to `managers/manager_prompts.py`:**
```python
"My_Task_Prompt": "You are a specialist...",
```

2. **Add to `endpoints/oauth/_verifier_.py`:**
```python
# In create_input list
"str:My Task Prompt"

# In set_meltano mapping  
"My_Task_Prompt": "str7"
```
### 5.5 GUID Standards (MANDATORY)
```python
# CORRECT - Always use GUIDs
user_guid: str = Field(..., description="User GUID")
scheduled_guid: str = Field(default_factory=lambda: str(uuid4()))

async def process_user(user_guid: str):  # CORRECT
    pass

# WRONG - Never use simple IDs
user_id: int  # Wrong!
async def process_user(user_id: int):  # Wrong!
```

---

## 6. Performance & Security
### 6.1 Database Optimization
**Connection Pooling:**
```python
# Configure in manager setup
pool_config = {
    'min_size': 5,
    'max_size': 20,
    'max_queries': 50000,
    'max_inactive_connection_lifetime': 300
}
```
**Query Optimization:**
```python
# CORRECT - Efficient batch operations
async def bulk_insert_tasks(tasks: List[Dict]):
    query = "INSERT INTO tasks (user_guid, content) VALUES ($1, $2)"
    await db.executemany(query, [(t['user_guid'], t['content']) for t in tasks])

# WRONG - Individual inserts
for task in tasks:  # Wrong!
    await db.execute("INSERT INTO tasks...", task)
```
### 6.2 Async Optimization
**Concurrent Processing:**
```python
import asyncio

async def process_multiple_tasks(tasks: List[str]) -> List[str]:
    """Process multiple tasks concurrently"""
    async def process_single(task: str) -> str:
        await asyncio.sleep(0.1)  # Simulate work
        return f"processed_{task}"
    
    # Execute concurrently
    results = await asyncio.gather(*[
        process_single(task) for task in tasks
    ])
    return results
```
**Background Tasks:**
```python
from asyncio import create_task
from etc.helper_functions import handle_asyncio_task_result_errors

class TaskManager:
    def __init__(self):
        self.background_tasks = set()
    
    def create_background_task(self, coro):
        task = create_task(coro)
        self.background_tasks.add(task)
        task.add_done_callback(self.background_tasks.discard)
        handle_asyncio_task_result_errors(task)
        return task
```
### 6.3 Security Guidelines
**Input Validation:**
```python
# CORRECT - Parameterized queries
async def get_user_data(user_guid: str):
    # Users are managed in-memory, not in database
    from managers.manager_users import ZairaUserManager
    user_manager = ZairaUserManager.get_instance()
    result = await user_manager.find_user(user_guid)
    return result

# WRONG - String concatenation
query = f"SELECT * FROM scheduled_requests WHERE content = '{user_input}'"  # Wrong!
```
**PII Protection:**
```python
# CORRECT - PII masking in logs
from etc.helper_functions import mask_pii
logger.info(f"Processing user: {mask_pii(user_email)}")
# Output: Processing user: j****@example.com

# WRONG - Logging sensitive data
logger.info(f"User email: {user_email}")  # Wrong!
```
### 6.4 Logging Standards (MANDATORY)
**CRITICAL: Use ONLY LogFire.log() and exception_triggered() for ALL logging and error handling**

**Centralized Logging:**
```python
# REQUIRED - Replace ALL print() statements
from managers.manager_logfire import LogFire
LogFire.log("EVENT_CODE", "message", chat=chat_session, severity="info")

# Event codes: INIT, RETRIEVE, TASK, OUTPUT, USER, ERROR, DEBUG, DEBUG_TRACE
LogFire.log("INIT", "Manager initialized successfully")
LogFire.log("ERROR", f"Connection failed: {error_msg}", severity="error")
LogFire.log("DEBUG", f"Processing {count} items", severity="debug")

# With chat session context (preferred when available)
chat_session = user.get_current_chat_session()  # Get chat session from user
LogFire.log("TASK", "User action completed", chat=chat_session)
LogFire.log("DEBUG", "Processing user request", chat=chat_session, severity="debug")

# WRONG - Direct print statements
print("Manager initialized")  # FORBIDDEN
logging.info("Connection failed")  # FORBIDDEN
logger.debug("Processing items")  # FORBIDDEN
```

**Exception Handling Standards:**
```python
# REQUIRED - Replace ALL exception handling
from etc.helper_functions import exception_triggered
try:
    await database_operation()
except Exception as e:
    exception_triggered(e, "database_operation", user_guid)
    return {"error": "Operation failed"}

# With chat session context for additional logging
try:
    await user_operation()
except Exception as e:
    chat_session = user.get_current_chat_session() if user else None
    LogFire.log("ERROR", f"Operation failed: {str(e)}", chat=chat_session, severity="error")
    exception_triggered(e, "user_operation", chat_session)
    return {"error": "Operation failed"}

# WRONG - Manual exception handling
except Exception as e:
    print(f"Error: {e}")  # FORBIDDEN
    logger.error(f"Error: {e}")  # FORBIDDEN
    traceback.print_exc()  # FORBIDDEN
```

**Conditional Logging (Avoid Redundancy):**
```python
# CORRECT - Only log state changes
if current_status != previous_status:
    chat_session = user.get_current_chat_session() if user else None
    LogFire.log("STATUS", f"Status changed: {previous_status} -> {current_status}", chat=chat_session)

# CORRECT - Reduce debug frequency
if debug_counter % 10 == 0:  # Only every 10th check
    LogFire.log("DEBUG_TRACE", f"Periodic status check: {status}", chat=chat_session)

# WRONG - Repetitive identical logging
for i in range(100):
    LogFire.log("DEBUG", "Same message")  # Don't repeat identical messages
```

**Debug Trace Requirements:**
```python
# CRITICAL: debug_trace.log MUST work in Claude environment for F5 debugging
# This is automatically handled by the updated LogFire.log() function
# Claude uses debug_trace.log to communicate with user when pressing F5

# Test files may use print() for test output ONLY
if 'pytest' in sys.modules:
    print("Test output allowed")  # Exception for tests
```

**Logging Severity Levels:**
```python
# Use appropriate severity for different log types
LogFire.log("INIT", "System started", severity="info")        # General info
LogFire.log("DEBUG", "Variable state", severity="debug")      # Debug details
LogFire.log("ERROR", "Operation failed", severity="error")    # Errors
LogFire.log("ERROR", "Critical failure", severity="warning")  # Warnings

# With chat session context (recommended when user/task context available)
chat_session = user.get_current_chat_session() if user else None
LogFire.log("TASK", "User task completed", chat=chat_session, severity="info")
LogFire.log("DEBUG", "User state updated", chat=chat_session, severity="debug")
LogFire.log("ERROR", "User operation failed", chat=chat_session, severity="error")
```

---

## 7. Testing
### 7.1 Test Requirements
- **Unit tests**: `tests/unit/test_[class].py` for every new class
- **Integration tests**: For complex features
- **ASCII-only**: All test content must be ASCII
- **MANDATORY**: Use `@with_unit_test_logging` decorator on ALL unit test methods
- **MANDATORY**: Use `LogFire.log()` for ALL logging in tests
### 7.2 Test Template
```python
from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from pydantic import ValidationError
from tests.unit.test_logging_capture import with_unit_test_logging

class TestMyFeature:
    @with_unit_test_logging
    def test_valid_creation(self):
        feature = MyFeature(name="test")
        assert feature.name == "test"
    
    @with_unit_test_logging
    def test_validation_error(self):
        with pytest.raises(ValidationError):
            MyFeature(name="")
    
    @with_unit_test_logging
    async def test_async_method(self):
        feature = MyFeature(name="test")
        result = await feature.process()
        assert result is not None
```

### 7.3 Unit Test Logging Standards (MANDATORY)
**All unit tests MUST use the new logging system:**

```python
# REQUIRED - Import the decorator
from tests.unit.test_logging_capture import with_unit_test_logging

# REQUIRED - Apply to ALL test methods
@with_unit_test_logging
async def test_my_feature(self):
    # Use LogFire.log() for any logging needs
    LogFire.log("DEBUG", "Starting test execution")
    
    # Test logic here
    result = await my_function()
    
    # Assertions - logging automatically captured
    assert result is not None
    
    # Log files created at: tests/unit/logs/TestClassName/test_method_name.log
    # NO timestamps in filename - always overwrites previous run
```

**Key Differences from test_real:**
- **No timestamps**: Log files always overwrite previous run
- **Method-level isolation**: Each test method gets its own log file
- **Automatic capture**: LogFire.log() automatically routes to test log files
- **No mocking needed**: Use real LogFire.log() calls, not mocks

**Test Log Structure:**
```
tests/unit/logs/
├── TestOAuthRedirectErrorHandling/
│   ├── test_oauth_redirect_post_empty_body.log      # Always overwrites
│   ├── test_oauth_redirect_post_invalid_json.log    # Always overwrites
│   └── test_oauth_redirect_post_success.log         # Always overwrites
├── TestManagerLogfire/
│   ├── test_log_internal_with_user.log
│   └── test_setup_initialization.log
└── ...
```

**Migration from Old Pattern:**
```python
# OLD - Don't use LogFire.log mocking
with patch('managers.manager_logfire.LogFire.log') as mock_log:
    result = function_call()
    mock_log.assert_any_call("ERROR", "Expected message")

# NEW - Use real LogFire.log with automatic capture
@with_unit_test_logging
async def test_function_call(self):
    result = function_call()  # LogFire.log() calls automatically captured
    assert result.status == 400  # Test behavior, not logging
    # Check actual log content in: tests/unit/logs/TestClass/test_function_call.log
```

### 7.4 Complete Test Logging System Overview

**System Integration Summary:**

**Unit Tests**:
```
tests/unit/logs/
├── TestOAuthRedirectErrorHandling/
│   ├── test_oauth_redirect_post_empty_body.log      # No timestamp, always overwrites
│   ├── test_oauth_redirect_post_invalid_json.log    # No timestamp, always overwrites  
│   └── test_oauth_redirect_post_success.log         # No timestamp, always overwrites
└── TestManagerLogfire/
    ├── test_log_internal_with_user.log
    └── test_setup_initialization.log
```

**Test_Real Tests** (Unchanged):
```
tests/test_real/logs/
└── test_function_name/
    └── YYYYMMDD_HHMMSS.log    # Keeps timestamps as before
```

**Key Benefits Achieved:**
- ✅ **Unit test logs to their own files** in local subdirectories
- ✅ **No timestamps in filenames** - always overwrites previous iteration  
- ✅ **Uses LogFire.log() consistently** instead of print statements or mocking
- ✅ **Each test method gets isolated log file** for debugging
- ✅ **Maintains all existing functionality** for other environments

**Environment-Specific Behavior:**
- **Claude Environment**: debug_trace.log continues working for F5 debugging
- **Unit Tests**: Each test method gets isolated log file, no timestamps
- **Test_Real**: Existing timestamped logging system unchanged  
- **Production**: Normal LogFire/database logging continues unchanged

**Full Migration Steps:**
1. **Apply pattern to remaining test files**: Add `@with_unit_test_logging` decorator
2. **Remove LogFire.log mocking**: Replace `with patch(...)` patterns
3. **Import the decorator**: Add `from tests.unit.test_logging_capture import with_unit_test_logging`
4. **Verify log output**: Each test will generate its own log file for debugging
### 7.5 Running Tests
**CRITICAL: Always run from `src/AgenticRAG/`**
**CRITICAL: NEVER impose time constraints or timeouts when running tests - let them complete naturally**

**Standard Test Commands:**
```bash
# Run all unit tests (NO timeout limits - can take 10+ minutes)
../../.venv/Scripts/pytest.exe tests/unit/ -v

# Run integration tests (NO timeout limits)
../../.venv/Scripts/pytest.exe tests/integration/ -v --cov

# ALWAYS use maximum tool timeout (600000ms = 10 minutes) when running pytest
# Tests may legitimately take several minutes to complete
```

**Test Exclusions:**
- Skip multimodal tests: `pytest tests/unit/ -v -k "not multimodal"`
- Skip agenda tests: `pytest tests/unit/ -v -k "not agenda"`
- Skip both: `pytest tests/unit/ -v -k "not multimodal and not agenda"`

**CRITICAL TEST EXECUTION RULES:**
- **NO timeout constraints** - Tests can take 15-30 minutes to complete
- **Run from correct directory** - Always `src/AgenticRAG/`
- **Use maximum tool timeout** - Set to maximum available or run in batches
- **Do not interrupt** - Let test suite complete fully
- **Monitor progress** - Tests will show progress but may appear slow
- **Batch testing recommended** - Run tests in smaller groups if hitting timeout limits
- **Individual test files** - Run single test files for detailed debugging
### 7.6 Test Modification Strategy
**When tests fail, ALWAYS re-use existing tests rather than creating new ones:**

#### Decision Tree
```
Test Failed? → Analyze failure cause
├── Code Logic Error → Fix code, keep test unchanged
├── Test Data Invalid → Update test data only
├── Requirements Changed → Modify test assertions
└── New Feature Added → Extend existing test, don't create new
```
#### Key Principles
```python
# CORRECT - Extend existing test
class TestMyFeature:
    def test_valid_creation(self):
        feature = MyFeature(name="test")
        assert feature.name == "test"
        # ADDED - Extended for new requirements
        assert feature.status == "pending"
        assert feature.created_at is not None
    
    @pytest.mark.parametrize("input_data,expected,should_pass", [
        ({"name": "valid"}, "valid", True),
        ({"name": ""}, None, False),
        # ADDED - New test cases
        ({"name": "valid", "type": "premium"}, "valid", True),
    ])
    def test_input_validation(self, input_data, expected, should_pass):
        if should_pass:
            assert validate_input(input_data) == expected
        else:
            with pytest.raises(ValidationError):
                validate_input(input_data)
```
**CRITICAL PRINCIPLES:**
- **NEVER delete working test code** - comment out if temporarily not needed
- **ALWAYS extend, don't replace** - build on existing test foundation
- **MAINTAIN test history** - use comments to track evolution
- **TEST both old and new behavior** - ensure backward compatibility

---

## 8. Code Snippets Library
### 8.1 Manager Pattern Template
```python
from typing import Optional
import asyncio

class MyNewManager:
    _instance: Optional['MyNewManager'] = None
    _initialized: bool = False
    _lock = asyncio.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_instance(cls) -> "MyNewManager":
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    async def setup(cls):
        instance = cls.get_instance()
        async with cls._lock:
            if instance._initialized:
                return
            await instance._initialize()
            instance._initialized = True
    
    async def _initialize(self):
        pass
```
### 8.2 Pydantic Model with Validation
```python
from pydantic import BaseModel, Field, validator
from typing import List
from datetime import datetime
from uuid import uuid4

class TaskModel(BaseModel):
    scheduled_guid: str = Field(default_factory=lambda: str(uuid4()))
    user_guid: str = Field(..., description="User GUID")
    content: str = Field(..., min_length=1, max_length=1000)
    status: str = Field(default="pending")
    priority: int = Field(default=1, ge=1, le=5)
    tags: List[str] = Field(default_factory=list)
    created_at: datetime = Field(default_factory=datetime.now)
    
    @validator('status')
    def validate_status(cls, v):
        allowed = ['pending', 'in_progress', 'completed', 'failed']
        if v not in allowed:
            raise ValueError(f'Status must be one of {allowed}')
        return v
    
    @validator('content')
    def validate_ascii_content(cls, v):
        try:
            v.encode('ascii')
        except UnicodeEncodeError:
            raise ValueError('Content must contain only ASCII characters')
        return v
    
    class Config:
        validate_assignment = True
```
### 8.3 Database Operation Pattern
```python
async def database_operation_template(user_guid: str, data: Dict[str, Any]):
    """Template for safe database operations"""
    from managers.manager_postgreSQL import PostgreSQLManager
    
    db = PostgreSQLManager.get_instance()
    async with db.get_connection("vectordb") as conn:
        async with conn.transaction():
            try:
                # Validate inputs
                if not user_guid or not data:
                    raise ValueError("Missing required parameters")
                
                # Check permissions
                from managers.manager_users import ZairaUserManager
                user_manager = ZairaUserManager.get_instance()
                user_exists = await user_manager.find_user(user_guid)
                if not user_exists:
                    raise ValueError("User not found")
                
                # Perform operation
                result = await conn.fetchrow(
                    "INSERT INTO tasks (user_guid, data) VALUES ($1, $2) RETURNING scheduled_guid",
                    user_guid, json.dumps(data)
                )
                
                return result['scheduled_guid']
                
            except Exception as e:
                from etc.helper_functions import exception_triggered
                # Get user's chat session for exception logging
                chat_session = user.get_current_chat_session() if user else None
                exception_triggered(e, "database_operation", chat_session)
                raise
```
### 8.4 Error Handling Decorator
```python
from functools import wraps

def handle_errors(context: str, user_guid: str = None):
    """Decorator for comprehensive error handling"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            try:
                return await func(*args, **kwargs)
            except ValidationError as e:
                return {"error": "Invalid input data", "details": str(e)}
            except Exception as e:
                from etc.helper_functions import exception_triggered
                # Get user's chat session for exception logging (context-dependent)\n                chat_session = None  # Set appropriately based on function context\n                exception_triggered(e, context, chat_session)
                return {"error": "Internal server error"}
        return wrapper
    return decorator

# Usage
@handle_errors("user_creation")
async def create_user(user_data: Dict[str, Any]) -> Dict[str, Any]:
    pass
```
### 8.5 Complete Workflow Examples
**Adding New Supervisor Task:**
```python
# Step 1: Create task class in tasks/processing/
from managers.manager_supervisors import SupervisorTask_SingleAgent
from langchain_core.tools import BaseTool

class MyProcessingTask(SupervisorTask_SingleAgent):
    def __init__(self, **kwargs):
        super().__init__(**kwargs)

class MyProcessingTool(BaseTool):
    name: str = "my_processing_tool"
    description: str = "Processes data according to requirements"
    
    def _run(self, input_data: str) -> str:
        raise NotImplementedError("Use async version")
    
    async def _arun(self, input_data: str) -> str:
        try:
            result = await self._process_data(input_data)
            return f"Processed: {result}"
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "my_processing_tool", None)
            return "Error processing data"
    
    async def _process_data(self, data: str) -> str:
        return f"processed_{data}"

# Step 2: Register task with supervisor
async def setup_my_task():
    tool = MyProcessingTool()
    task = MyProcessingTask(
        name="my_processing_task",
        tools=[tool],
        prompt_id="My_Processing_Prompt"  # Must exist in both files
    )
    
    from managers.manager_supervisors import SupervisorManager
    supervisor_manager = SupervisorManager.get_instance()
    return supervisor_manager.register_task(task)
```
**User Management Workflow:**
```python
async def complete_user_workflow(user_data: Dict[str, Any]) -> Dict[str, Any]:
    try:
        user = ZairaUser(**user_data)
        user_manager = ZairaUserManager.get_instance()
        
        # Check for existing user
        existing = await user_manager.get_user_by_email(user.email)
        if existing:
            return {"error": "User exists", "user_guid": existing.user_guid}
        
        # Register user (in-memory only)
        # example:
        from userprofiles.ZairaUser import PERMISSION_LEVELS
        await user_manager.add_user(username, PERMISSION_LEVELS.USER, ZairaUserManager.create_guid(), ZairaUserManager.create_guid())
        return {"success": True, "user_guid": user.user_guid}
        
    except Exception as e:
        exception_triggered(e, "user_workflow", None)
        return {"error": "User creation failed"}
```

---

## 9. Troubleshooting
### Common Issues
```
UnicodeEncodeError? → Check for non-ASCII characters
ImportError? → Add "from imports import *" and check paths  
Database Error? → Use only "vectordb" or "meltanodb" names
Test Failures? → Run from src/AgenticRAG/ directory
Manager Issues? → Always use get_instance()
Test Modifications? → Extend existing tests, don't create new ones
```
### Test Failure Resolution
```
Test Failed? → Follow this decision tree:
├── Assertion Error → Code logic changed, modify assertions in existing test
├── Import Error → Fix imports, keep test structure
├── Data Error → Update test data in existing fixtures
├── New Feature → Extend existing test with new assertions
├── API Changed → Update existing test calls, maintain test intent
└── Environment → Fix setup, keep all existing tests
```
### Security Checklist
- Input validation with Pydantic
- Parameterized SQL queries (no concatenation)
- HTML escaping for XSS prevention
- Rate limiting implemented
- PII masking in logs
- Environment variables for secrets
- OAuth token verification

---

## 10. Reference
### Essential Patterns
```python
# Manager access
manager = MyManager.get_instance()
await manager.setup()

# Exception handling
try:
    await operation()
except Exception as e:
    # Get user's chat session for exception logging\n    chat_session = user.get_current_chat_session() if user else None\n    exception_triggered(e, "context", chat_session)

# Database operation
async with db.get_connection("vectordb") as conn:
    result = await conn.fetch("SELECT * FROM table WHERE guid = $1", guid)

# Async task creation
from asyncio import create_task
from etc.helper_functions import handle_asyncio_task_result_errors
task = create_task(operation())
handle_asyncio_task_result_errors(task)
```
### Key Files
- `managers/manager_prompts.py` - Prompt definitions
- `endpoints/oauth/_verifier_.py` - OAuth/prompt configuration
- `etc/helper_functions.py` - Utilities
- `imports.py` - Centralized imports
### Core Libraries
- **LangChain**: https://python.langchain.com/docs/
- **LlamaIndex**: https://llamaindex.ai/
- **Pydantic**: https://ai.pydantic.dev/
- **LangGraph**: https://langchain-ai.github.io/langgraph/

---

## 11. Documentation & References
### Key Files to Read for Context
**Core Development Standards:**
- `CLAUDE.md` - Complete development standards, ASCII-only requirements, singleton patterns
- `imports.py` - Centralized import system, "from imports import *" requirement
- `pytest.ini` - Test categorization (unit, integration, performance, health)

**Architecture Examples:**
- `endpoints/api_endpoint.py` - aiohttp route definitions, middleware patterns
- `managers/manager_supervisors.py` - LangGraph supervisor patterns, multi-agent coordination
- `managers/manager_users.py` - Singleton implementation pattern with get_instance()
- `userprofiles/ZairaUser.py` - Pydantic BaseModel patterns, Field validation, GUID usage

### External Documentation
**Core Libraries:**
- **Sphinx**: https://www.sphinx-doc.org/en/master/ (autodoc, asyncio extensions, themes)
- **Sphinx Asyncio**: https://pythonhosted.org/sphinxcontrib-asyncio/ (async function documentation)
- **aiohttp-swagger3**: https://pypi.org/project/aiohttp-swagger3/ (OpenAPI for aiohttp)
- **GitHub Pages**: https://docs.github.com/en/pages (deployment automation)

**Framework Documentation:**
- **LangChain**: https://python.langchain.com/docs/ (prompts, chains, agents, tools)
- **LlamaIndex**: https://llamaindex.ai/ (RAG pipeline development)
- **Pydantic**: https://ai.pydantic.dev/ (data validation and type safety)
- **aiohttp**: https://docs.aiohttp.org/en/stable/ (web server development)
- **LangGraph**: https://langchain-ai.github.io/langgraph/ (hierarchical task coordination)

---

## 12. Prompt Slot Allocation
### Adding New Prompts to the System
When adding a new prompt, you must update BOTH files:

**Step 1: Add to `managers/manager_prompts.py`:**
```python
"My_Task_Prompt": "You are a specialist...",
```

**Step 2: Find Available Slot in `endpoints/oauth/_verifier_.py`:**

**Current Slot Availability:**
- **ZairaPrompts1**: str6, str7 available (uses access_token through str5)
- **ZairaPrompts2**: str4, str5, str6, str7 available (uses access_token through str3)
- **ZairaPrompts3**: str6, str7 available (uses access_token through str5)
- **ZairaPrompts4**: str7 available (uses access_token through str6)
- **ZairaPrompts5+**: Create new entry if all existing ones are full

**Step 3: Add to ZairaPrompts Entry:**
```python
# Example: Adding to ZairaPrompts4 using str7 slot
self.apps["ZairaPrompts4"] = OAuth2App("ZairaPrompts4").create_input("debug", [
    "str:Output Processing Supervisor Language Verifier",
    "str:Output Procesing Language Verifier Prompt Language Input", 
    "str:Output Processing Language Verifier Prompt Language Output",
    "str:Output Sender Discord",
    "str:Output Sender Mail", 
    "str:Output Sender HTTP",
    "str:Output Sender Python",
    "str:Output Sender Teams",
    "str:Output Sender Whatsapp",
    "str:My New Task Prompt"  # NEW: Added to str7 slot
]) \
.set_meltano({
    "Output_Processing_Language_Verifier": "access_token",
    "Output_Processing_Language_Verifier_Detect_Input": "refresh_token", 
    "Output_Processing_Language_Verifier_Detect_Output": "token_type",
    "Output_Sender_Discord": "str1",
    "Output_Sender_Agenda": "str2",
    "Output_Sender_HTTP": "str3", 
    "Output_Sender_Python": "str4",
    "Output_Sender_Teams": "str5",
    "Output_Sender_Whatsapp": "str6",
    "My_New_Task_Prompt": "str7"  # NEW: Maps to str7 slot
})
```

**Step 4: Create New ZairaPrompts if Needed:**
```python
# If all ZairaPrompts1-4 are full, create ZairaPrompts5
self.apps["ZairaPrompts5"] = OAuth2App("ZairaPrompts5").create_input("debug", [
    "str:My New Task Prompt",
    # Add up to 7 more prompts (str1-str7 available)
]) \
.set_meltano({
    "My_New_Task_Prompt": "access_token",
    # Map additional prompts to remaining slots
})
```

### Slot Allocation Rules:
1. **Maximum per ZairaPrompts**: 10 slots total (access_token, refresh_token, token_type, str1-str7)
2. **Recommended usage**: Use str1-str7 for prompts, reserve access_token/refresh_token/token_type for special cases
3. **Always check current usage**: Verify available slots before adding new prompts

---

## 13. Manual Testing
### Manual Testing Procedures
The project includes human-guided testing procedures for complex workflows that require manual verification.

**Manual Test Location:**
- Test scripts: `tests/manual/`
- Test guide: `tests/manual/test_scheduled_requests_manual.md`

**Running Manual Tests:**
```bash
# Run manual tests from project root with guide
../../.venv/Scripts/python.exe tests/manual/ -v
```

**Manual Testing Guidelines:**
1. Follow the step-by-step guide in the markdown file
2. Verify each step completes successfully before proceeding
3. Document any deviations or issues encountered
4. Manual tests complement automated testing for user interaction scenarios

---

## Quick Reference Card
### Critical Do's
- ASCII-only characters in ALL code
- Pydantic BaseModel for ALL classes  
- Singleton `get_instance()` for managers
- BaseTool classes (never `@tool`)
- GUIDs not IDs (`user_guid` not `user_id`)
- Update BOTH files for prompt_id
- Run tests from project root
- Extend existing tests, never create new ones
- Use 600000ms timeout for test runs
- Let tests complete naturally without interruption
- **LogFire.log() for ALL logging** (MANDATORY)
- **exception_triggered() for ALL exceptions** (MANDATORY)
- **Conditional logging** - only log state changes
- **@with_unit_test_logging for ALL unit tests** (MANDATORY)
### Critical Don'ts
- NO Unicode/emojis in code
- NO direct manager instantiation  
- NO `@tool` decorator
- NO hardcoded secrets
- NO simple integer IDs
- NO arbitrary database names
- NO synchronous functions
- NO creating new tests when existing ones can be extended
- **NO print() statements** - Use LogFire.log() only
- **NO logging.* calls** - Use LogFire.log() only  
- **NO manual exception handling** - Use exception_triggered() only
- **NO repetitive identical logging** - Use conditional logging
- **NO LogFire.log mocking in tests** - Use @with_unit_test_logging instead
### Emergency Fixes
- **UnicodeEncodeError** → Remove non-ASCII characters
- **ImportError** → Add `from imports import *`
- **Database Error** → Use "vectordb"/"meltanodb" only
- **Test Failure** → Run from `src/AgenticRAG/`
- memorize when a file exceeds the maximum allowed Claude tokens, fix it before continuing. When this happens, ALWAYS warn the developer in the log
- always run test_real_ files with maximum timeout