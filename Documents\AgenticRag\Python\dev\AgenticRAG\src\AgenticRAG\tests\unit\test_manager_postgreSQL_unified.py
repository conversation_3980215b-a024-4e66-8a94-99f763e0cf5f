"""
Unified comprehensive unit tests for PostgreSQL Manager
Consolidated from test_manager_postgreSQL_comprehensive.py, test_manager_postgreSQL_final.py, and test_database_operations.py
Following CLAUDE.md guidance: extend existing tests rather than creating new ones
"""
import pytest
import asyncio
import signal
from unittest.mock import AsyncMock, MagicMock, patch, call, PropertyMock
from asyncpg import CannotConnectNowError, Record
from socket import gaierror
from threading import Lock
from imports import *

@pytest.mark.unit
class TestPostgreSQLManagerUnified:
    """Unified comprehensive tests for PostgreSQL Manager"""
    
    def setup_method(self):
        """Reset singleton for each test"""
        from managers.manager_postgreSQL import PostgreSQLManager
        PostgreSQLManager._instance = None
        PostgreSQLManager._initialized = False
        if hasattr(PostgreSQLManager, "_exit_hooks_registered"):
            PostgreSQLManager._exit_hooks_registered = False
    
    def teardown_method(self):
        """Clean up after each test"""
        from managers.manager_postgreSQL import PostgreSQLManager
        PostgreSQLManager._instance = None
        PostgreSQLManager._initialized = False
        if hasattr(PostgreSQLManager, "_exit_hooks_registered"):
            PostgreSQLManager._exit_hooks_registered = False

    # ===== CORE FUNCTIONALITY TESTS =====
    
    def test_singleton_pattern(self):
        """Test that PostgreSQLManager follows singleton pattern"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        instance1 = PostgreSQLManager()
        instance2 = PostgreSQLManager()
        instance3 = PostgreSQLManager.get_instance()
        
        assert instance1 is instance2
        assert instance2 is instance3
        assert isinstance(instance1, PostgreSQLManager)
    
    def test_initial_state(self):
        """Test initial state of PostgreSQLManager"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        assert PostgreSQLManager._instance is None
        assert PostgreSQLManager._initialized is False
        
        # Test that get_instance creates new instance
        instance = PostgreSQLManager.get_instance()
        assert PostgreSQLManager._instance is not None
        assert isinstance(instance, PostgreSQLManager)
    
    @pytest.mark.parametrize("initialization_state,should_initialize", [
        (False, True),   # First time setup
        (True, False),   # Already initialized
    ])
    async def test_setup_scenarios(self, initialization_state, should_initialize):
        """Test setup behavior in different initialization states"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Set initial state
        PostgreSQLManager._initialized = initialization_state
        
        with patch.object(PostgreSQLManager, '_register_exit_hooks') as mock_hooks:
            await PostgreSQLManager.setup()
            
            if should_initialize:
                mock_hooks.assert_called_once()
                assert PostgreSQLManager._initialized is True
            else:
                mock_hooks.assert_not_called()
    
    @pytest.mark.parametrize("host_env,expected_host", [
        ("localhost", "localhost"),
        ("host.docker.internal", "host.docker.internal"),
        (None, "localhost"),  # Default fallback
    ])
    def test_host_configuration(self, host_env, expected_host):
        """Test host configuration from environment"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch.dict(os.environ, {"POSTGRES_HOST": host_env} if host_env else {}, clear=True):
            manager = PostgreSQLManager.get_instance()
            assert manager.host == expected_host

    @pytest.mark.parametrize("port_env,expected_port", [
        ("5432", 5432),
        ("5433", 5433),
        (None, 5432),  # Default
    ])
    def test_port_configuration(self, port_env, expected_port):
        """Test port configuration from environment"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch.dict(os.environ, {"POSTGRES_PORT": port_env} if port_env else {}, clear=True):
            manager = PostgreSQLManager.get_instance()
            assert manager.port == expected_port

    # ===== CONNECTION MANAGEMENT TESTS =====
    
    @pytest.mark.parametrize("connection_mode,should_use_pool", [
        ("safe_connection_mode", False),  # Direct connection
        ("pool_mode", True),             # Pool connection
    ])
    async def test_connection_management_modes(self, connection_mode, should_use_pool):
        """Test different connection management approaches"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        
        with patch('asyncpg.create_pool', new_callable=AsyncMock) as mock_pool:
            with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
                mock_connection = MagicMock()
                mock_connect.return_value = mock_connection
                mock_pool.return_value = MagicMock()
                
                if should_use_pool:
                    manager.connection_pool = MagicMock()
                    await manager.get_connection("vectordb")
                    assert manager.connection_pool is not None
                else:
                    manager.connection_pool = None
                    await manager.get_connection("vectordb")
                    mock_connect.assert_called()

    @pytest.mark.parametrize("database_name,is_valid", [
        ("vectordb", True),
        ("meltanodb", True),
        ("invalid_db", False),
    ])
    async def test_database_name_validation(self, database_name, is_valid):
        """Test database name validation"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        
        if is_valid:
            # Should not raise exception
            with patch('asyncpg.connect', new_callable=AsyncMock):
                result = await manager.get_connection(database_name)
                assert result is not None
        else:
            # Should handle invalid database names gracefully
            with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
                mock_connect.side_effect = Exception("Invalid database")
                try:
                    await manager.get_connection(database_name)
                except Exception:
                    pass  # Expected for invalid database names

    # ===== ERROR HANDLING TESTS =====
    
    @pytest.mark.parametrize("error_type,expected_behavior", [
        (CannotConnectNowError, "retry_logic"),
        (gaierror, "connection_failure"),
        (Exception, "general_error_handling"),
    ])
    async def test_connection_error_scenarios(self, error_type, expected_behavior):
        """Test various connection error scenarios"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_connect.side_effect = error_type("Test error")
            
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                try:
                    await manager.get_connection("vectordb")
                except:
                    pass  # Expected to fail
                
                # Verify error was logged appropriately
                if expected_behavior != "retry_logic":
                    mock_exception.assert_called()

    # ===== DATABASE CREATION/DELETION TESTS =====
    
    @pytest.mark.asyncio
    async def test_create_database_success(self):
        """Test successful database creation"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            mock_conn.fetch.return_value = []  # Database doesn't exist
            mock_conn.execute.return_value = None
            mock_conn.close.return_value = None
            
            await PostgreSQLManager.create_database("testdb")
            
            # Verify database creation was attempted
            mock_connect.assert_called_once()
            mock_conn.execute.assert_called_once()
            mock_conn.close.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_create_database_already_exists(self):
        """Test database creation when database already exists"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            mock_conn.fetch.return_value = [{'datname': 'testdb'}]  # Database exists
            mock_conn.close.return_value = None
            
            await PostgreSQLManager.create_database("testdb")
            
            # Verify no creation was attempted
            mock_conn.execute.assert_not_called()
            mock_conn.close.assert_called_once()
    
    async def test_create_database_error(self):
        """Test database creation error handling"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_connect.side_effect = Exception("Connection failed")
            
            with pytest.raises(Exception):
                await PostgreSQLManager.create_database("testdb")
    
    async def test_delete_database_success(self):
        """Test successful database deletion"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            mock_conn.fetch.side_effect = [
                [{'datname': 'testdb'}],  # Database exists
                []  # No active connections
            ]
            mock_conn.execute.return_value = None
            mock_conn.close.return_value = None
            
            await PostgreSQLManager.delete_database("testdb")
            
            # Verify database deletion was attempted
            mock_conn.execute.assert_called_once()
            mock_conn.close.assert_called_once()
    
    async def test_delete_database_not_exists(self):
        """Test database deletion when database doesn't exist"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            mock_conn.fetch.return_value = []  # Database doesn't exist
            mock_conn.close.return_value = None
            
            await PostgreSQLManager.delete_database("testdb")
            
            # Verify no deletion was attempted
            mock_conn.execute.assert_not_called()
            mock_conn.close.assert_called_once()
    
    async def test_delete_database_active_connections(self):
        """Test database deletion with active connections"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            mock_conn.fetch.side_effect = [
                [{'datname': 'testdb'}],  # Database exists
                [{'pid': 123}]  # Active connection
            ]
            mock_conn.close.return_value = None
            
            await PostgreSQLManager.delete_database("testdb")
            
            # Verify no deletion was attempted
            mock_conn.execute.assert_not_called()
            mock_conn.close.assert_called_once()
    
    async def test_delete_database_error(self):
        """Test database deletion error handling"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_connect.side_effect = Exception("Connection failed")
            
            # Should not raise exception, just print error
            await PostgreSQLManager.delete_database("testdb")
    
    # ===== CONNECTION POOL TESTS =====
    
    async def test_connect_to_database_pool_success(self):
        """Test successful pool connection creation"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.create_pool', new_callable=AsyncMock) as mock_create_pool:
            mock_pool = MagicMock()
            mock_create_pool.return_value = mock_pool
            
            result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=True)
            
            assert result == mock_pool
            mock_create_pool.assert_called_once()
    
    async def test_connect_to_database_direct_success(self):
        """Test successful direct connection creation"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_connect.return_value = mock_conn
            
            result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=False)
            
            assert result == mock_conn
            mock_connect.assert_called_once()
    
    async def test_connect_to_database_retry_logic(self):
        """Test connection retry logic with exponential backoff"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_connect.side_effect = [ConnectionRefusedError("Connection refused"), MagicMock()]
            
            with patch('asyncio.sleep', new_callable=AsyncMock) as mock_sleep:
                result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=False, retries=2)
                
                # Verify retry happened
                assert mock_connect.call_count == 2
                mock_sleep.assert_called_once()
                assert result is not None
    
    async def test_connect_to_database_max_retries_exceeded(self):
        """Test connection failure after max retries"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('asyncpg.connect', new_callable=AsyncMock) as mock_connect:
            mock_connect.side_effect = ConnectionRefusedError("Connection refused")
            
            with pytest.raises(RuntimeError):
                await PostgreSQLManager.connect_to_database("vectordb", use_pool=False, retries=2)
    
    async def test_connect_to_database_existing_connection(self):
        """Test returning existing connection when available"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        existing_conn = MagicMock()
        manager._safe_set_connection("vectordb", existing_conn, False)
        
        result = await PostgreSQLManager.connect_to_database("vectordb", use_pool=False)
        
        assert result == existing_conn
    
    # ===== QUERY EXECUTION TESTS =====
    
    async def test_execute_query_with_pool_success(self):
        """Test successful query execution with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = [{"id": 1, "name": "test"}]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
        
        assert result == [{"id": 1, "name": "test"}]
        mock_conn.fetch.assert_called_once_with("SELECT * FROM test")
    
    async def test_execute_query_with_direct_connection_success(self):
        """Test successful query execution with direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.fetch.return_value = [{"id": 1, "name": "test"}]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
        
        assert result == [{"id": 1, "name": "test"}]
        mock_conn.fetch.assert_called_once_with("SELECT * FROM test")
    
    async def test_execute_query_pool_health_check_failure(self):
        """Test query execution with pool health check failure"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.side_effect = [Exception("Health check failed"), [{"id": 1}]]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_new_pool = MagicMock()
            mock_new_conn = MagicMock()
            mock_new_pool.acquire.return_value.__aenter__.return_value = mock_new_conn
            mock_new_conn.fetch.return_value = [{"id": 1}]
            mock_connect.return_value = mock_new_pool
            
            result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
            
            assert result == [{"id": 1}]
            mock_connect.assert_called()
    
    async def test_execute_query_pool_fallback_to_direct(self):
        """Test query execution falling back to direct connection when pool fails"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.side_effect = Exception("Pool operation failed")
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_direct_conn = MagicMock()
            mock_direct_conn.fetch.return_value = [{"id": 1}]
            mock_connect.return_value = mock_direct_conn
            
            with patch.object(PostgreSQLManager, 'close_connection', new_callable=AsyncMock) as mock_close:
                result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
                
                assert result == [{"id": 1}]
                mock_connect.assert_called()
                mock_close.assert_called()
    
    async def test_execute_query_create_connection_on_demand(self):
        """Test query execution creating connection on demand"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # No existing connection
        PostgreSQLManager._safe_set_connection("vectordb", None, False)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_conn.fetch.return_value = [{"id": 1}]
            mock_connect.return_value = mock_conn
            
            with patch.object(PostgreSQLManager, 'close_connection', new_callable=AsyncMock) as mock_close:
                result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
                
                assert result == [{"id": 1}]
                mock_connect.assert_called()
                mock_close.assert_called()
    
    async def test_execute_non_query_with_pool(self):
        """Test non-query execution with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.execute.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        await PostgreSQLManager.execute_non_query("vectordb", "INSERT INTO test VALUES (1)")
        
        mock_conn.execute.assert_called_once_with("INSERT INTO test VALUES (1)")
    
    async def test_execute_non_query_with_direct_connection(self):
        """Test non-query execution with direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.execute.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        await PostgreSQLManager.execute_non_query("vectordb", "INSERT INTO test VALUES (1)")
        
        mock_conn.execute.assert_called_once_with("INSERT INTO test VALUES (1)")
    
    async def test_execute_non_query_no_pool_found(self):
        """Test non-query execution when no pool found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Set up to use pool but no pool exists
        PostgreSQLManager._safe_set_connection("vectordb", None, True)
        
        with pytest.raises(Exception, match="No pool found"):
            await PostgreSQLManager.execute_non_query("vectordb", "INSERT INTO test VALUES (1)")
    
    async def test_execute_non_query_no_connection_found(self):
        """Test non-query execution when no connection found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Set up to use direct connection but no connection exists
        PostgreSQLManager._safe_set_connection("vectordb", None, False)
        
        with pytest.raises(Exception, match="No connection found"):
            await PostgreSQLManager.execute_non_query("vectordb", "INSERT INTO test VALUES (1)")
    
    async def test_execute_many_with_pool(self):
        """Test execute_many with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.executemany.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        params = [(1, "test1"), (2, "test2")]
        await PostgreSQLManager.execute_many("vectordb", "INSERT INTO test VALUES ($1, $2)", params)
        
        mock_conn.executemany.assert_called_once_with("INSERT INTO test VALUES ($1, $2)", params)
    
    async def test_execute_many_with_direct_connection(self):
        """Test execute_many with direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.executemany.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        params = [(1, "test1"), (2, "test2")]
        await PostgreSQLManager.execute_many("vectordb", "INSERT INTO test VALUES ($1, $2)", params)
        
        mock_conn.executemany.assert_called_once_with("INSERT INTO test VALUES ($1, $2)", params)
    
    async def test_execute_many_no_pool_found(self):
        """Test execute_many when no pool found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, True)
        
        with pytest.raises(Exception, match="No pool found"):
            await PostgreSQLManager.execute_many("vectordb", "INSERT INTO test VALUES ($1, $2)", [])
    
    async def test_execute_many_no_connection_found(self):
        """Test execute_many when no connection found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, False)
        
        with pytest.raises(Exception, match="No connection found"):
            await PostgreSQLManager.execute_many("vectordb", "INSERT INTO test VALUES ($1, $2)", [])
    
    # ===== TABLE OPERATIONS TESTS =====
    
    async def test_get_table_names_with_pool(self):
        """Test getting table names with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
        mock_conn.fetch.return_value = [{"table_name": "test_table"}, {"table_name": "another_table"}]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        result = await PostgreSQLManager.get_table_names("vectordb")
        
        assert result == ["test_table", "another_table"]
        mock_conn.fetch.assert_called_once()
    
    async def test_get_table_names_with_direct_connection(self):
        """Test getting table names with direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.fetch.return_value = [{"table_name": "test_table"}]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        result = await PostgreSQLManager.get_table_names("vectordb")
        
        assert result == ["test_table"]
        mock_conn.fetch.assert_called_once()
    
    async def test_get_table_names_no_pool_found(self):
        """Test getting table names when no pool found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, True)
        
        result = await PostgreSQLManager.get_table_names("vectordb")
        
        assert result == []
    
    async def test_get_table_names_no_connection_found(self):
        """Test getting table names when no connection found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, False)
        
        result = await PostgreSQLManager.get_table_names("vectordb")
        
        assert result == []
    
    async def test_get_table_names_with_custom_schema(self):
        """Test getting table names with custom schema"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.fetch.return_value = [{"table_name": "test_table"}]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        result = await PostgreSQLManager.get_table_names("vectordb", "custom_schema")
        
        assert result == ["test_table"]
        # Verify the schema was used in the query
        call_args = mock_conn.fetch.call_args[0][0]
        assert "custom_schema" in call_args
    
    async def test_get_table_names_exception_handling(self):
        """Test getting table names with exception handling"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.fetch.side_effect = Exception("Database error")
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        result = await PostgreSQLManager.get_table_names("vectordb")
        
        assert result == []
    
    # ===== TEST CONNECTION TESTS =====
    
    async def test_test_connection_with_pool_success(self):
        """Test connection test with pool success"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_pool = MagicMock()
            mock_conn = MagicMock()
            mock_pool.acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetch.return_value = [{"?column?": 1}]
            mock_connect.return_value = mock_pool
            
            # Set up to use pool
            PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
            
            result = await PostgreSQLManager.test_connection("vectordb")
            
            assert result is True
            mock_connect.assert_called_once_with("vectordb", use_pool=True)
    
    async def test_test_connection_with_direct_success(self):
        """Test connection test with direct connection success"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_conn = MagicMock()
            mock_conn.fetch.return_value = [{"?column?": 1}]
            mock_connect.return_value = mock_conn
            
            # Set up to use direct connection
            PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
            
            result = await PostgreSQLManager.test_connection("vectordb")
            
            assert result is True
            mock_connect.assert_called_once_with("vectordb")
    
    async def test_test_connection_failure(self):
        """Test connection test failure"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_connect.side_effect = Exception("Connection failed")
            
            # Set up to use direct connection
            PostgreSQLManager._safe_set_connection("vectordb", None, False)
            
            result = await PostgreSQLManager.test_connection("vectordb")
            
            assert result is False
    
    # ===== RECONNECTION TESTS =====
    
    async def test_reconnect_with_existing_mode(self):
        """Test reconnection with existing connection mode"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        # Set up existing connection mode
        PostgreSQLManager._safe_set_connection("vectordb", MagicMock(), True)
        
        with patch.object(PostgreSQLManager, 'close_connection', new_callable=AsyncMock) as mock_close:
            with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
                mock_new_pool = MagicMock()
                mock_connect.return_value = mock_new_pool
                
                result = await PostgreSQLManager.reconnect("vectordb")
                
                assert result == mock_new_pool
                mock_close.assert_called_once_with("vectordb")
                mock_connect.assert_called_once_with("vectordb", use_pool=True, min_size=1, max_size=10)

    # ===== TRANSACTION TESTS =====
    
    async def test_start_transaction_with_pool(self):
        """Test starting transaction with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_pool.acquire.return_value = mock_conn
        mock_conn.transaction.return_value = mock_tx
        mock_tx.start.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        conn, tx = await PostgreSQLManager.start_transaction("vectordb")
        
        assert conn == mock_conn
        assert tx == mock_tx
        mock_pool.acquire.assert_called_once()
        mock_conn.transaction.assert_called_once()
        mock_tx.start.assert_called_once()
    
    async def test_start_transaction_with_direct_connection(self):
        """Test starting transaction with direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_conn.transaction.return_value = mock_tx
        mock_tx.start.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        conn, tx = await PostgreSQLManager.start_transaction("vectordb")
        
        assert conn == mock_conn
        assert tx == mock_tx
        mock_conn.transaction.assert_called_once()
        mock_tx.start.assert_called_once()
    
    async def test_start_transaction_no_pool_found(self):
        """Test starting transaction when no pool found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, True)
        
        with pytest.raises(Exception, match="No pool found"):
            await PostgreSQLManager.start_transaction("vectordb")
    
    async def test_start_transaction_no_connection_found(self):
        """Test starting transaction when no connection found"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        PostgreSQLManager._safe_set_connection("vectordb", None, False)
        
        with pytest.raises(Exception, match="No connection found"):
            await PostgreSQLManager.start_transaction("vectordb")
    
    async def test_commit_transaction_with_pool(self):
        """Test committing transaction with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_tx.commit.return_value = None
        mock_pool.release.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        await PostgreSQLManager.commit_transaction(mock_conn, mock_tx, "vectordb")
        
        mock_tx.commit.assert_called_once()
        mock_pool.release.assert_called_once_with(mock_conn)
    
    async def test_commit_transaction_without_pool(self):
        """Test committing transaction without pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_tx.commit.return_value = None
        
        await PostgreSQLManager.commit_transaction(mock_conn, mock_tx)
        
        mock_tx.commit.assert_called_once()
    
    async def test_commit_transaction_no_dbname(self):
        """Test committing transaction without database name"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_tx.commit.return_value = None
        
        await PostgreSQLManager.commit_transaction(mock_conn, mock_tx, None)
        
        mock_tx.commit.assert_called_once()
    
    async def test_rollback_transaction_with_pool(self):
        """Test rolling back transaction with pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_tx.rollback.return_value = None
        mock_pool.release.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        await PostgreSQLManager.rollback_transaction(mock_conn, mock_tx, "vectordb")
        
        mock_tx.rollback.assert_called_once()
        mock_pool.release.assert_called_once_with(mock_conn)
    
    async def test_rollback_transaction_without_pool(self):
        """Test rolling back transaction without pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_tx.rollback.return_value = None
        
        await PostgreSQLManager.rollback_transaction(mock_conn, mock_tx)
        
        mock_tx.rollback.assert_called_once()
    
    async def test_rollback_transaction_no_dbname(self):
        """Test rolling back transaction without database name"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_tx = MagicMock()
        mock_tx.rollback.return_value = None
        
        await PostgreSQLManager.rollback_transaction(mock_conn, mock_tx, None)
        
        mock_tx.rollback.assert_called_once()

    # ===== INTEGRATION AND DATABASE OPERATIONS TESTS =====
    
    @pytest.fixture
    async def mock_database_connections(self):
        """Mock database connections for integration tests"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch.object(PostgreSQLManager, 'get_connection') as mock_get_conn:
            mock_connection = MagicMock()
            mock_get_conn.return_value.__aenter__.return_value = mock_connection
            
            # Setup common mock responses
            mock_connection.fetch.return_value = [{"id": 1, "data": "test"}]
            mock_connection.execute.return_value = "INSERT 0 1"
            mock_connection.executemany.return_value = None
            
            yield mock_connection

    async def test_scheduled_request_persistence_integration(self, mock_database_connections):
        """Test scheduled task persistence through PostgreSQL manager"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        
        # Sample task data
        task_data = {
            "user_guid": "test-user-guid",
            "scheduled_guid": "test-scheduled-guid", 
            "task_type": "ONCE",
            "content": "Test task content"
        }
        
        # Test task insertion
        async with manager.get_connection("vectordb") as conn:
            result = await conn.execute(
                "INSERT INTO scheduled_requests (user_guid, content) VALUES ($1, $2)",
                task_data["user_guid"], task_data["content"]
            )
            assert "INSERT" in result

    async def test_vector_operations_integration(self, mock_database_connections):
        """Test vector database operations"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        
        # Test vector upsert
        vector_data = {
            "user_guid": "test-user",
            "content": "test content",
            "embedding": [0.1, 0.2, 0.3]
        }
        
        async with manager.get_connection("vectordb") as conn:
            # Test upsert operation
            await conn.execute(
                "INSERT INTO vectors (user_guid, content, embedding) VALUES ($1, $2, $3)",
                vector_data["user_guid"], vector_data["content"], str(vector_data["embedding"])
            )
            
            # Test vector search
            search_results = await conn.fetch(
                "SELECT * FROM vectors WHERE user_guid = $1",
                vector_data["user_guid"]
            )
            assert len(search_results) >= 0  # Should return results or empty

    async def test_database_error_handling_integration(self, mock_database_connections):
        """Test comprehensive database error handling"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        
        # Setup connection to raise exception
        mock_database_connections.execute.side_effect = Exception("Database error")
        
        with patch('etc.helper_functions.exception_triggered') as mock_exception:
            try:
                async with manager.get_connection("vectordb") as conn:
                    await conn.execute("INVALID SQL")
            except Exception:
                pass  # Expected to fail
            
            # Verify error handling was triggered
            mock_exception.assert_called()

    # ===== CLEANUP AND EXIT HANDLING TESTS =====
    
    def test_exit_hooks_registration(self):
        """Test exit hooks registration and duplicate prevention"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        with patch('atexit.register') as mock_atexit:
            with patch('signal.signal') as mock_signal:
                PostgreSQLManager._register_exit_hooks()
                
                # Verify exit hooks registered
                assert PostgreSQLManager._exit_hooks_registered is True
                mock_atexit.assert_called_once()
                assert mock_signal.call_count == 2  # SIGINT and SIGTERM
                
                # Test that duplicate registration is prevented
                PostgreSQLManager._register_exit_hooks()
                # Should still only be called once
                mock_atexit.assert_called_once()

    async def test_cleanup_scenarios(self):
        """Test various cleanup scenarios"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        
        # Setup mock connections for cleanup
        mock_pool = MagicMock()
        mock_direct = MagicMock()
        manager.connection_pool = {"vectordb": mock_pool}
        manager.direct_connections = {"vectordb": mock_direct}
        
        # Test cleanup
        await manager.close_all_connections()
        
        # Verify cleanup was attempted
        mock_pool.close.assert_called_once()
        mock_direct.close.assert_called_once()

    @pytest.mark.parametrize("connection_state,cleanup_success", [
        ("healthy", True),
        ("already_closed", True),  # Should handle gracefully
        ("error_on_close", False), # Should log error but continue
    ])
    async def test_connection_cleanup_states(self, connection_state, cleanup_success):
        """Test connection cleanup in various states"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        
        # Setup mock based on state
        mock_connection = MagicMock()
        if connection_state == "error_on_close":
            mock_connection.close.side_effect = Exception("Close failed")
            mock_connection.is_closed.return_value = False
        elif connection_state == "already_closed":
            mock_connection.close.side_effect = None
            mock_connection.is_closed.return_value = True
        else:
            mock_connection.close.side_effect = None
            mock_connection.is_closed.return_value = False
        
        # Use the thread-safe method to set connections
        manager._safe_set_connection("vectordb", mock_connection, False)
        
        await manager.close_all_connections()
        
        if connection_state == "error_on_close":
            # Should still try to close even if it fails
            mock_connection.close.assert_called()
        elif connection_state == "already_closed":
            # Should still try to close
            mock_connection.close.assert_called()
        else:
            # Should close normally
            mock_connection.close.assert_called()
    
    async def test_close_connection_pool_success(self):
        """Test closing connection pool successfully"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_pool._closed = False
        mock_pool.close.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        await PostgreSQLManager.close_connection("vectordb")
        
        mock_pool.close.assert_called_once()
    
    async def test_close_connection_direct_success(self):
        """Test closing direct connection successfully"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = False
        mock_conn.close.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        await PostgreSQLManager.close_connection("vectordb")
        
        mock_conn.close.assert_called_once()
    
    async def test_close_connection_already_closed_pool(self):
        """Test closing already closed pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_pool._closed = True
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        await PostgreSQLManager.close_connection("vectordb")
        
        # Should not call close on already closed pool
        mock_pool.close.assert_not_called()
    
    async def test_close_connection_already_closed_direct(self):
        """Test closing already closed direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = True
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        await PostgreSQLManager.close_connection("vectordb")
        
        # Should not call close on already closed connection
        mock_conn.close.assert_not_called()
    
    async def test_close_connection_with_errors(self):
        """Test closing connection with errors"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = False
        mock_conn.close.side_effect = RuntimeError("Event loop closed")
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        # Should not raise exception
        await PostgreSQLManager.close_connection("vectordb")
        
        mock_conn.close.assert_called_once()
    
    async def test_close_all_connections_with_pools_and_direct(self):
        """Test closing all connections including both pools and direct connections"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        
        # Setup both pool and direct connection
        mock_pool = MagicMock()
        mock_pool._closed = False
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = False
        
        manager._safe_set_connection("vectordb", mock_pool, True)
        manager._safe_set_connection("meltanodb", mock_conn, False)
        
        await manager.close_all_connections()
        
        # Verify both were closed
        mock_pool.close.assert_called_once()
        mock_conn.close.assert_called_once()
        
        # Verify dictionaries were cleared
        assert len(manager._connections) == 0
        assert len(manager._pools) == 0
        assert len(manager._connection_modes) == 0
    
    async def test_close_all_connections_with_close_errors(self):
        """Test closing all connections with close errors"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        manager = PostgreSQLManager.get_instance()
        
        # Setup connections that will fail to close
        mock_pool = MagicMock()
        mock_pool._closed = False
        mock_pool.close.side_effect = OSError("Connection error")
        
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = False
        mock_conn.close.side_effect = AttributeError("No close method")
        
        manager._safe_set_connection("vectordb", mock_pool, True)
        manager._safe_set_connection("meltanodb", mock_conn, False)
        
        # Should not raise exception
        await manager.close_all_connections()
        
        # Verify close was attempted
        mock_pool.close.assert_called_once()
        mock_conn.close.assert_called_once()
        
        # Verify dictionaries were still cleared
        assert len(manager._connections) == 0
        assert len(manager._pools) == 0
        assert len(manager._connection_modes) == 0
    
    # ===== THREAD SAFETY TESTS =====
    
    def test_safe_connection_methods(self):
        """Test thread-safe connection methods"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        
        # Test safe set and get
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        result = PostgreSQLManager._safe_get_connection("vectordb")
        assert result == mock_conn
        
        # Test safe get mode
        mode = PostgreSQLManager._safe_get_connection_mode("vectordb")
        assert mode is False
        
        # Test safe set mode
        PostgreSQLManager._safe_set_connection_mode("vectordb", True)
        mode = PostgreSQLManager._safe_get_connection_mode("vectordb")
        assert mode is True
        
        # Test safe remove
        removed_conn, removed_mode = PostgreSQLManager._safe_remove_connection("vectordb")
        assert removed_conn == mock_conn
        assert removed_mode is True
    
    def test_safe_connection_with_closed_pool(self):
        """Test safe connection handling with closed pool"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_pool._closed = True
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        # Should return None for closed pool
        result = PostgreSQLManager._safe_get_connection("vectordb")
        assert result is None
        
        # Should also clear the connection mode
        mode = PostgreSQLManager._safe_get_connection_mode("vectordb")
        assert mode is False
    
    def test_safe_connection_with_closed_direct(self):
        """Test safe connection handling with closed direct connection"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.is_closed.return_value = True
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        # Should return None for closed connection
        result = PostgreSQLManager._safe_get_connection("vectordb")
        assert result is None
        
        # Should also clear the connection mode
        mode = PostgreSQLManager._safe_get_connection_mode("vectordb")
        assert mode is False
    
    # ===== POOL HEALTH AND RECREATION TESTS =====
    
    async def test_execute_query_with_closed_pool(self):
        """Test query execution with closed pool recreation"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_pool = MagicMock()
        mock_pool._closed = True
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_pool, True)
        
        with patch.object(PostgreSQLManager, 'connect_to_database', new_callable=AsyncMock) as mock_connect:
            mock_new_pool = MagicMock()
            mock_new_pool._closed = False
            mock_conn = MagicMock()
            mock_new_pool.acquire.return_value.__aenter__.return_value = mock_conn
            mock_conn.fetch.return_value = [{"id": 1}]
            mock_connect.return_value = mock_new_pool
            
            result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test")
            
            assert result == [{"id": 1}]
            mock_connect.assert_called()
    
    async def test_execute_query_with_params(self):
        """Test query execution with parameters"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.fetch.return_value = [{"id": 1, "name": "test"}]
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        params = ["test", 1]
        result = await PostgreSQLManager.execute_query("vectordb", "SELECT * FROM test WHERE name = $1 AND id = $2", params)
        
        assert result == [{"id": 1, "name": "test"}]
        mock_conn.fetch.assert_called_once_with("SELECT * FROM test WHERE name = $1 AND id = $2", "test", 1)
    
    async def test_execute_non_query_with_params(self):
        """Test non-query execution with parameters"""
        from managers.manager_postgreSQL import PostgreSQLManager
        
        mock_conn = MagicMock()
        mock_conn.execute.return_value = None
        
        PostgreSQLManager._safe_set_connection("vectordb", mock_conn, False)
        
        params = ["test", 1]
        await PostgreSQLManager.execute_non_query("vectordb", "INSERT INTO test (name, id) VALUES ($1, $2)", params)
        
        mock_conn.execute.assert_called_once_with("INSERT INTO test (name, id) VALUES ($1, $2)", "test", 1)