from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, MagicMock, patch, Mock
from uuid import uuid4
import asyncio
from datetime import datetime, timezone, timedelta

from managers.scheduled_requests.core.factory import ScheduledRequestManagerFactory
from managers.scheduled_requests.core.user_manager import UserScheduledRequestManager
from managers.scheduled_requests.utils.config import UserQuotaConfig
from managers.scheduled_requests.utils.exceptions import UserQuotaExceededError
from userprofiles.ZairaUser import ZairaUser, PERMISSION_LEVELS

class TestScheduledRequestManagerFactory:
    """Comprehensive test class for ScheduledRequestManagerFactory"""
    
    def setup_method(self):
        """Set up test fixtures"""
        # Reset singleton instance for each test
        ScheduledRequestManagerFactory._instance = None
        ScheduledRequestManagerFactory._initialized = False
        
        self.test_user_guid = str(uuid4())
        self.test_user_guid_2 = str(uuid4())
        
        # Create test users
        self.test_user = ZairaUser(
            username="test_user",
            rank=PERMISSION_LEVELS.USER,
            GUID=uuid4(),
            device_GUID=uuid4()
        )
        
        self.test_admin_user = ZairaUser(
            username="admin_user", 
            rank=PERMISSION_LEVELS.ADMIN,
            GUID=uuid4(),
            device_GUID=uuid4()
        )
    
    def teardown_method(self):
        """Clean up after each test"""
        # Reset singleton
        ScheduledRequestManagerFactory._instance = None
        ScheduledRequestManagerFactory._initialized = False
    
    def test_singleton_pattern(self):
        """Test that ScheduledRequestManagerFactory follows singleton pattern"""
        factory1 = ScheduledRequestManagerFactory.get_instance()
        factory2 = ScheduledRequestManagerFactory.get_instance()
        
        assert factory1 is factory2
        assert isinstance(factory1, ScheduledRequestManagerFactory)
    
    def test_factory_initialization(self):
        """Test factory initialization"""
        factory = ScheduledRequestManagerFactory.get_instance()
        
        assert hasattr(factory, '_user_managers')
        assert isinstance(factory._user_managers, dict)
        assert hasattr(factory, '_manager_locks')
        assert isinstance(factory._manager_locks, dict)
        assert hasattr(factory, '_cleanup_task')
        assert len(factory._user_managers) == 0
    
    @pytest.mark.asyncio
    async def test_factory_setup(self):
        """Test factory setup process"""
        factory = ScheduledRequestManagerFactory.get_instance()
        
        await factory.setup()
        
        assert factory._initialized
        assert factory._cleanup_task is not None
        assert not factory._cleanup_task.done()
    
    @pytest.mark.asyncio
    async def test_get_user_manager_new_user(self):
        """Test getting manager for new user"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_config:
            mock_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5,
                daily_task_limit=50,
                memory_limit_mb=100,
                thread_pool_size=3,
                max_task_duration_hours=24,
                max_recurring_tasks=10
            )
            
            manager = await factory.get_user_manager(self.test_user_guid, self.test_user.rank)
            
            assert isinstance(manager, UserScheduledRequestManager)
            assert manager.user_guid == self.test_user_guid
            assert self.test_user_guid in factory._user_managers
            assert self.test_user_guid in factory._manager_locks
    
    @pytest.mark.asyncio
    async def test_get_user_manager_existing_user(self):
        """Test getting manager for existing user returns same instance"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_config:
            mock_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5,
                daily_task_limit=50,
                memory_limit_mb=100,
                thread_pool_size=3,
                max_task_duration_hours=24,
                max_recurring_tasks=10
            )
            
            # Get manager first time
            manager1 = await factory.get_user_manager(self.test_user_guid, self.test_user.rank)
            
            # Get manager second time
            manager2 = await factory.get_user_manager(self.test_user_guid, self.test_user.rank)
            
            assert manager1 is manager2
            assert len(factory._user_managers) == 1
    
    @pytest.mark.asyncio
    async def test_get_user_manager_different_users(self):
        """Test getting managers for different users"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_config:
            mock_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5,
                daily_task_limit=50,
                memory_limit_mb=100,
                thread_pool_size=3,
                max_task_duration_hours=24,
                max_recurring_tasks=10
            )
            
            # Get managers for different users
            manager1 = await factory.get_user_manager(self.test_user_guid, self.test_user.rank)
            manager2 = await factory.get_user_manager(self.test_user_guid_2, self.test_admin_user.rank)
            
            assert manager1 is not manager2
            assert manager1.user_guid == self.test_user_guid
            assert manager2.user_guid == self.test_user_guid_2
            assert len(factory._user_managers) == 2
    
    @pytest.mark.asyncio
    async def test_remove_user_manager_success(self):
        """Test successful removal of user manager"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_config:
            mock_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5,
                daily_task_limit=50,
                memory_limit_mb=100,
                thread_pool_size=3,
                max_task_duration_hours=24,
                max_recurring_tasks=10
            )
            
            # Create manager
            manager = await factory.get_user_manager(self.test_user_guid, self.test_user.rank)
            assert self.test_user_guid in factory._user_managers
            
            # Mock manager shutdown
            with patch.object(manager, 'shutdown') as mock_shutdown:
                # Remove manager
                success = await factory.remove_user_manager(self.test_user_guid)
                
                assert success is True
                assert self.test_user_guid not in factory._user_managers
                assert self.test_user_guid not in factory._manager_locks
                mock_shutdown.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_remove_user_manager_nonexistent(self):
        """Test removal of non-existent user manager"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        nonexistent_guid = str(uuid4())
        success = await factory.remove_user_manager(nonexistent_guid)
        
        assert success is False
    
    @pytest.mark.asyncio
    async def test_get_all_user_managers(self):
        """Test getting all user managers"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_config:
            mock_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5,
                daily_task_limit=50,
                memory_limit_mb=100,
                thread_pool_size=3,
                max_task_duration_hours=24,
                max_recurring_tasks=10
            )
            
            # Create multiple managers
            manager1 = await factory.get_user_manager(self.test_user_guid, self.test_user.rank)
            manager2 = await factory.get_user_manager(self.test_user_guid_2, self.test_admin_user.rank)
            
            # Get all managers
            all_managers = await factory.get_all_user_managers()
            
            assert len(all_managers) == 2
            assert manager1 in all_managers.values()
            assert manager2 in all_managers.values()
            assert self.test_user_guid in all_managers
            assert self.test_user_guid_2 in all_managers
    
    @pytest.mark.asyncio
    async def test_get_all_user_managers_empty(self):
        """Test getting all user managers when none exist"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        all_managers = await factory.get_all_user_managers()
        
        assert len(all_managers) == 0
        assert all_managers == {}
    
    @pytest.mark.asyncio
    async def test_get_active_manager_count(self):
        """Test getting count of active managers"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        # Initially no managers
        assert factory.get_active_manager_count() == 0
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_config:
            mock_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5,
                daily_task_limit=50,
                memory_limit_mb=100,
                thread_pool_size=3,
                max_task_duration_hours=24,
                max_recurring_tasks=10
            )
            
            # Create managers
            await factory.get_user_manager(self.test_user_guid, self.test_user.rank)
            assert factory.get_active_manager_count() == 1
            
            await factory.get_user_manager(self.test_user_guid_2, self.test_admin_user.rank)
            assert factory.get_active_manager_count() == 2
            
            # Remove manager
            await factory.remove_user_manager(self.test_user_guid)
            assert factory.get_active_manager_count() == 1
    
    @pytest.mark.asyncio
    async def test_factory_shutdown(self):
        """Test factory shutdown process"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_config:
            mock_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5,
                daily_task_limit=50,
                memory_limit_mb=100,
                thread_pool_size=3,
                max_task_duration_hours=24,
                max_recurring_tasks=10
            )
            
            # Create some managers
            manager1 = await factory.get_user_manager(self.test_user_guid, self.test_user.rank)
            manager2 = await factory.get_user_manager(self.test_user_guid_2, self.test_admin_user.rank)
            
            # Mock manager shutdowns
            with patch.object(manager1, 'shutdown') as mock_shutdown1, \
                 patch.object(manager2, 'shutdown') as mock_shutdown2:
                
                await factory.shutdown()
                
                # Verify all managers were shut down
                mock_shutdown1.assert_called_once()
                mock_shutdown2.assert_called_once()
                
                # Verify cleanup
                assert len(factory._user_managers) == 0
                assert len(factory._manager_locks) == 0
                assert factory._cleanup_task is None or factory._cleanup_task.cancelled()
    
    @pytest.mark.asyncio
    async def test_concurrent_access(self):
        """Test concurrent access to factory methods"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_config:
            mock_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5,
                daily_task_limit=50,
                memory_limit_mb=100,
                thread_pool_size=3,
                max_task_duration_hours=24,
                max_recurring_tasks=10
            )
            
            # Create multiple concurrent tasks getting the same manager
            async def get_manager_task():
                return await factory.get_user_manager(self.test_user_guid, self.test_user.rank)
            
            tasks = [asyncio.create_task(get_manager_task()) for _ in range(5)]
            managers = await asyncio.gather(*tasks)
            
            # All tasks should get the same manager instance
            first_manager = managers[0]
            for manager in managers[1:]:
                assert manager is first_manager
            
            # Only one manager should be created
            assert len(factory._user_managers) == 1
    
    @pytest.mark.asyncio
    async def test_manager_initialization_failure(self):
        """Test handling of manager initialization failure"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        with patch('managers.scheduled_requests.core.user_manager.UserScheduledRequestManager') as mock_manager_class:
            mock_manager_class.side_effect = Exception("Initialization failed")
            
            with pytest.raises(Exception, match="Initialization failed"):
                await factory.get_user_manager(self.test_user_guid, self.test_user.rank)
            
            # Manager should not be added to the factory
            assert self.test_user_guid not in factory._user_managers
    
    @pytest.mark.asyncio
    async def test_cleanup_inactive_managers(self):
        """Test cleanup of inactive managers"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_config:
            mock_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5,
                daily_task_limit=50,
                memory_limit_mb=100,
                thread_pool_size=3,
                max_task_duration_hours=24,
                max_recurring_tasks=10
            )
            
            # Create manager
            manager = await factory.get_user_manager(self.test_user_guid, self.test_user.rank)
            
            # Mock manager as inactive
            with patch.object(manager, 'get_active_request_count', return_value=0), \
                 patch.object(manager, '_last_activity_time', datetime.now(timezone.utc).timestamp() - 7200), \
                 patch.object(manager, 'shutdown') as mock_shutdown:
                
                # Run cleanup
                await factory._cleanup_inactive_managers()
                
                # Manager should be removed
                assert self.test_user_guid not in factory._user_managers
                mock_shutdown.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_get_factory_metrics(self):
        """Test getting factory metrics"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_config:
            mock_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5,
                daily_task_limit=50,
                memory_limit_mb=100,
                thread_pool_size=3,
                max_task_duration_hours=24,
                max_recurring_tasks=10
            )
            
            # Create managers
            manager1 = await factory.get_user_manager(self.test_user_guid, self.test_user.rank)
            manager2 = await factory.get_user_manager(self.test_user_guid_2, self.test_admin_user.rank)
            
            # Mock manager metrics
            with patch.object(manager1, 'get_active_request_count', return_value=3), \
                 patch.object(manager2, 'get_active_request_count', return_value=5):
                
                metrics = await factory.get_factory_metrics()
                
                assert 'total_managers' in metrics
                assert 'total_active_requests' in metrics
                assert 'memory_usage_mb' in metrics
                assert 'uptime_seconds' in metrics
                
                assert metrics['total_managers'] == 2
                assert metrics['total_active_requests'] == 8  # 3 + 5
    
    @pytest.mark.asyncio
    async def test_is_user_manager_active(self):
        """Test checking if user manager is active"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        # Initially no manager exists
        assert not await factory.is_user_manager_active(self.test_user_guid)
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_config:
            mock_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5,
                daily_task_limit=50,
                memory_limit_mb=100,
                thread_pool_size=3,
                max_task_duration_hours=24,
                max_recurring_tasks=10
            )
            
            # Create manager
            await factory.get_user_manager(self.test_user_guid, self.test_user.rank)
            
            # Now manager should be active
            assert await factory.is_user_manager_active(self.test_user_guid)
            
            # Different user should not be active
            assert not await factory.is_user_manager_active(self.test_user_guid_2)
    
    @pytest.mark.asyncio
    async def test_user_lock_mechanism(self):
        """Test user-specific lock mechanism"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_config:
            mock_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5,
                daily_task_limit=50,
                memory_limit_mb=100,
                thread_pool_size=3,
                max_task_duration_hours=24,
                max_recurring_tasks=10
            )
            
            # Get manager (creates lock)
            await factory.get_user_manager(self.test_user_guid, self.test_user.rank)
            
            # Verify lock exists
            assert self.test_user_guid in factory._manager_locks
            assert isinstance(factory._manager_locks[self.test_user_guid], asyncio.Lock)
            
            # Remove manager should also remove lock
            await factory.remove_user_manager(self.test_user_guid)
            assert self.test_user_guid not in factory._manager_locks
    
    @pytest.mark.asyncio
    async def test_multiple_quota_configurations(self):
        """Test factory handles different quota configurations correctly"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        # Mock different configurations for different permission levels
        def mock_get_quota_config(permission_level):
            if permission_level == PERMISSION_LEVELS.ADMIN:
                return UserQuotaConfig(
                    max_concurrent_tasks=100,
                    daily_task_limit=1000,
                    memory_limit_mb=1000,
                    thread_pool_size=10,
                    max_task_duration_hours=48,
                    max_recurring_tasks=50
                )
            else:  # USER level
                return UserQuotaConfig(
                    max_concurrent_tasks=5,
                    daily_task_limit=50,
                    memory_limit_mb=100,
                    thread_pool_size=3,
                    max_task_duration_hours=24,
                    max_recurring_tasks=10
                )
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config', side_effect=mock_get_quota_config):
            # Create managers with different permission levels
            user_manager = await factory.get_user_manager(self.test_user_guid, PERMISSION_LEVELS.USER)
            admin_manager = await factory.get_user_manager(self.test_user_guid_2, PERMISSION_LEVELS.ADMIN)
            
            # Verify different quota configurations
            assert user_manager.quota_config.max_concurrent_tasks == 5
            assert admin_manager.quota_config.max_concurrent_tasks == 100
            
            assert user_manager.quota_config.daily_task_limit == 50
            assert admin_manager.quota_config.daily_task_limit == 1000
    
    def test_factory_properties(self):
        """Test factory property methods"""
        factory = ScheduledRequestManagerFactory.get_instance()
        
        # Test initial state
        assert factory.get_active_manager_count() == 0
        assert not factory._initialized
    
    @pytest.mark.asyncio
    async def test_error_handling_in_setup(self):
        """Test error handling during factory setup"""
        factory = ScheduledRequestManagerFactory.get_instance()
        
        # Mock cleanup task creation failure
        with patch('asyncio.create_task', side_effect=Exception("Task creation failed")):
            with pytest.raises(Exception, match="Task creation failed"):
                await factory.setup()
            
            assert not factory._initialized
    
    @pytest.mark.asyncio
    async def test_graceful_shutdown_with_active_managers(self):
        """Test graceful shutdown when managers have active requests"""
        factory = ScheduledRequestManagerFactory.get_instance()
        await factory.setup()
        
        with patch('managers.scheduled_requests.utils.config.ScheduledRequestsConfig.get_user_quota_config') as mock_config:
            mock_config.return_value = UserQuotaConfig(
                max_concurrent_tasks=5,
                daily_task_limit=50,
                memory_limit_mb=100,
                thread_pool_size=3,
                max_task_duration_hours=24,
                max_recurring_tasks=10
            )
            
            # Create manager with active requests
            manager = await factory.get_user_manager(self.test_user_guid, self.test_user.rank)
            
            # Mock manager with active requests
            with patch.object(manager, 'get_active_request_count', return_value=3), \
                 patch.object(manager, 'shutdown') as mock_shutdown:
                
                # Shutdown should still complete gracefully
                await factory.shutdown()
                
                mock_shutdown.assert_called_once()
                assert len(factory._user_managers) == 0