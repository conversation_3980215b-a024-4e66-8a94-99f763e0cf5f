from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import MagicMock, AsyncMock, patch

class TestAgendaOAuthScopes:
    """Test that Google Calendar OAuth scopes are properly filtered"""
    
    @pytest.mark.asyncio
    async def test_gcalendar_scopes_filtering(self):
        """Test that input fields are filtered out from OAuth scopes"""
        from endpoints.oauth._verifier_ import OAuth2App
        
        # Simulate the gcalendar app creation as it happens in _verifier_.py
        gcalendar_app = OAuth2App("gcalendar")
        gcalendar_app.scopes = []  # Reset scopes
        
        # Simulate create_oauth adding OAuth scopes
        oauth_scopes = ["https://www.googleapis.com/auth/calendar"]
        gcalendar_app.scopes.extend(oauth_scopes)
        
        # Simulate create_input adding input fields
        input_fields = ["bool:Opslaan in Zaira geheugen?"]
        gcalendar_app.scopes.extend(input_fields)
        
        # Verify mixed scopes
        assert len(gcalendar_app.scopes) == 2
        assert "https://www.googleapis.com/auth/calendar" in gcalendar_app.scopes
        assert "bool:Opslaan in Zaira geheugen?" in gcalendar_app.scopes
        
        # Test the filtering logic we added
        valid_scopes = [scope for scope in gcalendar_app.scopes if scope.startswith("https://")]
        
        # Verify only valid OAuth scopes remain
        assert len(valid_scopes) == 1
        assert valid_scopes[0] == "https://www.googleapis.com/auth/calendar"
        assert "bool:Opslaan in Zaira geheugen?" not in valid_scopes
    
    @pytest.mark.asyncio
    async def test_scope_filtering_logic(self):
        """Test the scope filtering logic that should be used in agenda tools"""
        # Create a mock gcalendar app with mixed scopes
        mixed_scopes = [
            "https://www.googleapis.com/auth/calendar",
            "https://www.googleapis.com/auth/calendar.events",
            "bool:Opslaan in Zaira geheugen?",
            "str:Some other input field"
        ]
        
        # Apply the same filtering logic that's now in task_out_agenda.py
        valid_scopes = [scope for scope in mixed_scopes if scope.startswith("https://")]
        
        # Verify filtering results
        assert len(valid_scopes) == 2
        assert "https://www.googleapis.com/auth/calendar" in valid_scopes
        assert "https://www.googleapis.com/auth/calendar.events" in valid_scopes
        assert "bool:Opslaan in Zaira geheugen?" not in valid_scopes
        assert "str:Some other input field" not in valid_scopes
        
    def test_verify_oauth_verifier_setup(self):
        """Verify that OAuth2Verifier creates mixed scopes for gcalendar"""
        from endpoints.oauth._verifier_ import OAuth2App
        
        # Simulate what happens in OAuth2Verifier.instantiate()
        gcalendar_app = OAuth2App("gcalendar")
        gcalendar_app.scopes = []
        
        # First create_oauth is called
        gcalendar_app.scopes.extend(["https://www.googleapis.com/auth/calendar"])
        
        # Then create_input is called
        gcalendar_app.scopes.extend(["bool:Opslaan in Zaira geheugen?"])
        
        # Verify the app has mixed scopes that need filtering
        assert len(gcalendar_app.scopes) == 2
        assert any(scope.startswith("https://") for scope in gcalendar_app.scopes)
        assert any(not scope.startswith("https://") for scope in gcalendar_app.scopes)