from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from discord import Message, MessageReference
from endpoints.discord_endpoint import MyDiscordBot
from endpoints.mybot_generic import MyBot_Generic, ChannelType, ReplyContext
from userprofiles.ZairaUser import <PERSON><PERSON><PERSON><PERSON><PERSON>
from userprofiles.ZairaUser import PERMISSION_LEVELS

class TestDiscordReplyFunctionality:
    """Test suite for Discord reply detection and handling functionality"""
    
    def setup_method(self):
        """Setup test environment"""
        self.mock_message = MagicMock(spec=Message)
        self.mock_message.author = MagicMock()
        self.mock_message.author.id = 12345
        self.mock_message.guild = MagicMock()
        self.mock_message.guild.id = 1332344925655924796
        self.mock_message.channel = MagicMock()
        self.mock_message.content = "Test message content"
        self.mock_message.attachments = []
        
    def test_reply_context_creation_with_reply(self):
        """Test that reply context is properly created when message is a reply"""
        # Setup reply reference
        self.mock_message.reference = MagicMock(spec=MessageReference)
        self.mock_message.reference.message_id = 987654321
        
        # Test reply detection logic
        is_reply = False
        replied_message_id = None
        replied_message_content = None
        replied_message_author = None
        
        if self.mock_message.reference and self.mock_message.reference.message_id:
            is_reply = True
            replied_message_id = self.mock_message.reference.message_id
        
        # Create ReplyContext object
        if is_reply:
            reply_context = ReplyContext.create_reply(
                replied_message_id=str(replied_message_id),
                replied_message_content=replied_message_content,
                replied_message_author=replied_message_author,
                platform="Discord"
            )
        else:
            reply_context = ReplyContext.create_no_reply()
        
        assert reply_context.is_reply == True
        assert reply_context.replied_message_id == "987654321"
        assert reply_context.platform == "Discord"
        
    def test_reply_context_creation_without_reply(self):
        """Test that reply context is properly created when message is not a reply"""
        # Setup no reply reference
        self.mock_message.reference = None
        
        # Test reply detection logic
        is_reply = False
        replied_message_id = None
        
        if self.mock_message.reference and self.mock_message.reference.message_id:
            is_reply = True
            replied_message_id = self.mock_message.reference.message_id
        
        # Create ReplyContext object
        if is_reply:
            reply_context = ReplyContext.create_reply(
                replied_message_id=str(replied_message_id),
                platform="Discord"
            )
        else:
            reply_context = ReplyContext.create_no_reply()
        
        assert reply_context.is_reply == False
        assert reply_context.replied_message_id is None
        
    @pytest.mark.asyncio
    async def test_mybot_generic_handles_reply_context(self):
        """Test that MyBot_Generic properly handles reply context"""
        mock_user = MagicMock(spec=ZairaUser)
        mock_user.username = "test_user"
        mock_user.on_message = AsyncMock()
        
        bot_generic = MyBot_Generic(None, "TestBot")
        
        reply_context = ReplyContext.create_reply(
            replied_message_id="123456",
            replied_message_content="Original message",
            replied_message_author="TestUser",
            platform="Discord"
        )
        
        await bot_generic.on_message(
            ChannelType.PUBLIC,
            mock_user,
            "Reply message",
            [],
            self.mock_message,
            reply_context
        )
        
        # Verify that user.on_message was called with reply_context
        mock_user.on_message.assert_called_once_with(
            complete_message="Reply message",
            calling_bot=bot_generic,
            attachments=[],
            original_message=self.mock_message,
            reply_context=reply_context
        )
        
    def test_reply_context_message_formatting(self):
        """Test that reply context is properly formatted in message content"""
        original_message = "Hello, how are you?"
        reply_context = ReplyContext.create_reply(
            replied_message_id="123456",
            replied_message_content=original_message,
            replied_message_author="TestUser",
            platform="Discord"
        )
        
        complete_message = "I'm doing well, thanks!"
        
        # Simulate the logic from ZairaUser.on_message
        if reply_context and isinstance(reply_context, ReplyContext):
            complete_message += reply_context.format_reply_context_for_ai()
        
        expected_content = "I'm doing well, thanks!\n\n[Reply Context: This message is replying to TestUser: 'Hello, how are you?']"
        assert complete_message == expected_content
        
    def test_no_reply_context_fallback(self):
        """Test graceful handling when no reply context is provided"""
        # Test with None reply_context
        reply_context = None
        complete_message = "Normal message"
        
        # Simulate the logic from ZairaUser.on_message
        if reply_context and isinstance(reply_context, ReplyContext):
            complete_message += reply_context.format_reply_context_for_ai()
        
        # Message should remain unchanged
        assert complete_message == "Normal message"
        
    def test_reply_context_class_methods(self):
        """Test ReplyContext class factory methods and functionality"""
        # Test create_no_reply
        no_reply = ReplyContext.create_no_reply()
        assert no_reply.is_reply == False
        assert no_reply.replied_message_id is None
        assert no_reply.format_reply_context_for_ai() == ""
        
        # Test create_reply
        reply = ReplyContext.create_reply(
            replied_message_id="999888",
            replied_message_content="Test content",
            replied_message_author="Author",
            platform="Discord"
        )
        assert reply.is_reply == True
        assert reply.replied_message_id == "999888"
        assert reply.replied_message_content == "Test content"
        assert reply.replied_message_author == "Author"
        assert reply.platform == "Discord"
        
        expected_ai_format = "\n\n[Reply Context: This message is replying to Author: 'Test content']"
        assert reply.format_reply_context_for_ai() == expected_ai_format