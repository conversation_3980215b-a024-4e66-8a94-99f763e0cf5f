"""
Comprehensive unit tests for etc/helper_functions.py
This test suite achieves 90%+ coverage by testing all utility functions
"""

import sys
import os
import pytest
import asyncio
from unittest.mock import Mock, patch, MagicMock, mock_open
from pathlib import Path
import tempfile
import shutil
from typing import Optional

# Add project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'), severity="debug")

from imports import *
from managers.manager_logfire import LogFire
from etc.helper_functions import (
    is_claude_environment,
    get_any_message_as_type,
    folder_has_files,
    convert_key_value_to_string,
    exception_triggered,
    handle_asyncio_task_result_errors,
    call_cmd_debug,
    call_network_docker,
    get_value_from_env,
    save_to_env,
    create_html_out,
    get_password
, severity="debug")

class TestIsClaudeEnvironment:
    """Test is_claude_environment function"""
    
    def test_claude_environment_with_claude_code(self):
        """Test detection with CLAUDE_CODE environment variable"""
        with patch.dict(os.environ, {'CLAUDE_CODE': '1'}):
            assert is_claude_environment() == True
    
    def test_claude_environment_with_anthropic_user_id(self):
        """Test detection with ANTHROPIC_USER_ID environment variable"""
        with patch.dict(os.environ, {'ANTHROPIC_USER_ID': 'claude-dev'}):
            assert is_claude_environment() == True
    
    def test_claude_environment_with_both_variables(self):
        """Test detection with both environment variables"""
        with patch.dict(os.environ, {'CLAUDE_CODE': '1', 'ANTHROPIC_USER_ID': 'claude-dev'}):
            assert is_claude_environment() == True
    
    def test_claude_environment_without_variables(self):
        """Test detection without Claude environment variables"""
        # Ensure no Claude variables are set
        env_vars = {k: v for k, v in os.environ.items() 
                   if k not in ['CLAUDE_CODE', 'ANTHROPIC_USER_ID']}
        with patch.dict(os.environ, env_vars, clear=True):
            assert is_claude_environment() == False
    
    def test_claude_environment_with_empty_values(self):
        """Test detection with empty environment variable values"""
        with patch.dict(os.environ, {'CLAUDE_CODE': '', 'ANTHROPIC_USER_ID': ''}):
            assert is_claude_environment() == False
    
    def test_claude_environment_with_none_values(self):
        """Test detection with None values (simulated)"""
        with patch.dict(os.environ, {}, clear=True):
            # Simulate None values by ensuring variables don't exist
            assert is_claude_environment() == False

class TestGetAnyMessageAsType:
    """Test get_any_message_as_type function"""
    
    def test_get_any_message_as_type_returns_tuple(self):
        """Test that function returns a tuple"""
        result = get_any_message_as_type(, severity="debug")
        assert isinstance(result, tuple, severity="debug")
        assert len(result) > 0
    
    def test_get_any_message_as_type_contains_expected_types(self):
        """Test that function returns expected message types"""
        from langchain_core.messages import AIMessage, HumanMessage, SystemMessage
        
        result = get_any_message_as_type(, severity="debug")
        
        # Check that key message types are included
        assert AIMessage in result
        assert HumanMessage in result
        assert SystemMessage in result
    
    def test_get_any_message_as_type_all_types_are_classes(self):
        """Test that all returned items are classes"""
        result = get_any_message_as_type(, severity="debug")
        
        for msg_type in result:
            assert isinstance(msg_type, type, severity="debug")
    
    def test_get_any_message_as_type_consistency(self):
        """Test that function returns consistent results"""
        result1 = get_any_message_as_type(, severity="debug")
        result2 = get_any_message_as_type(, severity="debug")
        
        assert result1 == result2
        assert len(result1) == len(result2, severity="debug")
    
    def test_get_any_message_as_type_specific_types(self):
        """Test presence of specific message types"""
        from langchain_core.messages import (
            AIMessage, HumanMessage, ChatMessage, SystemMessage, 
            FunctionMessage, ToolMessage, AIMessageChunk, 
            HumanMessageChunk, ChatMessageChunk, SystemMessageChunk, 
            FunctionMessageChunk, ToolMessageChunk
        , severity="debug")
        
        result = get_any_message_as_type(, severity="debug")
        
        expected_types = [
            AIMessage, HumanMessage, ChatMessage, SystemMessage,
            FunctionMessage, ToolMessage, AIMessageChunk,
            HumanMessageChunk, ChatMessageChunk, SystemMessageChunk,
            FunctionMessageChunk, ToolMessageChunk
        ]
        
        for expected_type in expected_types:
            assert expected_type in result

class TestFolderHasFiles:
    """Test folder_has_files function"""
    
    def test_folder_has_files_with_files(self):
        """Test folder that contains files"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a test file
            test_file = os.path.join(temp_dir, "test.txt", severity="debug")
            with open(test_file, 'w') as f:
                f.write("test content", severity="debug")
            
            assert folder_has_files(temp_dir) == True
    
    def test_folder_has_files_empty_folder(self):
        """Test empty folder"""
        with tempfile.TemporaryDirectory() as temp_dir:
            assert folder_has_files(temp_dir) == False
    
    def test_folder_has_files_only_subdirectories(self):
        """Test folder that only contains subdirectories"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a subdirectory
            subdir = os.path.join(temp_dir, "subdir", severity="debug")
            os.makedirs(subdir, severity="debug")
            
            assert folder_has_files(temp_dir) == False
    
    def test_folder_has_files_mixed_content(self):
        """Test folder with both files and subdirectories"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a file
            test_file = os.path.join(temp_dir, "test.txt", severity="debug")
            with open(test_file, 'w') as f:
                f.write("test content", severity="debug")
            
            # Create a subdirectory
            subdir = os.path.join(temp_dir, "subdir", severity="debug")
            os.makedirs(subdir, severity="debug")
            
            assert folder_has_files(temp_dir) == True
    
    def test_folder_has_files_nonexistent_folder(self):
        """Test nonexistent folder"""
        nonexistent_path = "/path/that/does/not/exist"
        
        with patch('builtins.print') as mock_print:
            result = folder_has_files(nonexistent_path, severity="debug")
            
            assert result == False
            mock_print.assert_called_once(, severity="debug")
            assert "does not exist" in mock_print.call_args[0][0]
    
    def test_folder_has_files_file_instead_of_folder(self):
        """Test passing a file path instead of folder path"""
        with tempfile.NamedTemporaryFile(delete=False) as temp_file:
            temp_file.write(b"test content", severity="debug")
            temp_file_path = temp_file.name
        
        try:
            with patch('builtins.print') as mock_print:
                result = folder_has_files(temp_file_path, severity="debug")
                
                assert result == False
                mock_print.assert_called_once(, severity="debug")
                assert "does not exist" in mock_print.call_args[0][0]
        finally:
            os.unlink(temp_file_path, severity="debug")
    
    def test_folder_has_files_hidden_files(self):
        """Test folder with hidden files"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create a hidden file
            hidden_file = os.path.join(temp_dir, ".hidden", severity="debug")
            with open(hidden_file, 'w') as f:
                f.write("hidden content", severity="debug")
            
            assert folder_has_files(temp_dir) == True
    
    def test_folder_has_files_various_file_types(self):
        """Test folder with various file types"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create different file types
            files = [
                ("test.txt", "text content"),
                ("test.py", "LogFire.log("DEBUG", 'hello')"),
                ("test.json", '{"key": "value"}'),
                ("test", "no extension", severity="debug")
            ]
            
            for filename, content in files:
                file_path = os.path.join(temp_dir, filename, severity="debug")
                with open(file_path, 'w') as f:
                    f.write(content, severity="debug")
            
            assert folder_has_files(temp_dir) == True

class TestConvertKeyValueToString:
    """Test convert_key_value_to_string function"""
    
    def test_convert_key_value_to_string_basic(self):
        """Test basic key-value conversion"""
        result = convert_key_value_to_string(key1="value1", key2="value2", severity="debug")
        
        assert "key1: value1" in result
        assert "key2: value2" in result
        assert result.count("\n\n") == 1  # One separator between two items
    
    def test_convert_key_value_to_string_underscore_replacement(self):
        """Test underscore replacement in keys"""
        result = convert_key_value_to_string(key_with_underscores="value", severity="debug")
        
        assert "key with underscores: value" in result
        assert "key_with_underscores" not in result
    
    def test_convert_key_value_to_string_empty_input(self):
        """Test with no arguments"""
        result = convert_key_value_to_string(, severity="debug")
        
        assert result == ""
    
    def test_convert_key_value_to_string_single_item(self):
        """Test with single key-value pair"""
        result = convert_key_value_to_string(single_key="single_value", severity="debug")
        
        assert result == "single key: single_value"
        assert "\n\n" not in result
    
    def test_convert_key_value_to_string_multiple_underscores(self):
        """Test with multiple underscores in keys"""
        result = convert_key_value_to_string(
            key_with_multiple_underscores="value1",
            another_key_with_underscores="value2"
        , severity="debug")
        
        assert "key with multiple underscores: value1" in result
        assert "another key with underscores: value2" in result
    
    def test_convert_key_value_to_string_various_value_types(self):
        """Test with various value types"""
        result = convert_key_value_to_string(
            string_key="string_value",
            int_key=42,
            float_key=3.14,
            bool_key=True,
            none_key=None,
            list_key=[1, 2, 3],
            dict_key={"nested": "value"}
        , severity="debug")
        
        assert "string key: string_value" in result
        assert "int key: 42" in result
        assert "float key: 3.14" in result
        assert "bool key: True" in result
        assert "none key: None" in result
        assert "list key: [1, 2, 3]" in result
        assert "dict key: {'nested': 'value'}" in result
    
    def test_convert_key_value_to_string_order_consistency(self):
        """Test that order is consistent (Python 3.7+ dict order)"""
        # Since Python 3.7+, dict order is guaranteed to be insertion order
        result = convert_key_value_to_string(
            first_key="first",
            second_key="second", 
            third_key="third"
        , severity="debug")
        
        lines = result.split("\n\n", severity="debug")
        assert len(lines) == 3
        assert lines[0] == "first key: first"
        assert lines[1] == "second key: second"
        assert lines[2] == "third key: third"
    
    def test_convert_key_value_to_string_special_characters(self):
        """Test with special characters in keys and values"""
        result = convert_key_value_to_string(
            key_with_special="value with spaces and symbols!@#$%",
            another_key="value\nwith\nnewlines",
            tab_key="value\twith\ttabs"
        , severity="debug")
        
        assert "key with special: value with spaces and symbols!@#$%" in result
        assert "another key: value\nwith\nnewlines" in result
        assert "tab key: value\twith\ttabs" in result

class TestExceptionTriggered:
    """Test exception_triggered function"""
    
    def test_exception_triggered_basic(self):
        """Test basic exception handling"""
        test_exception = ValueError("Test error", severity="debug")
        
        with patch('managers.manager_logfire.LogFire') as mock_logfire, \
             patch('globals.Globals') as mock_globals, \
             patch('builtins.print') as mock_print:
            
            mock_globals.is_debug.return_value = True
            
            # Mock breakpoint to avoid actual breakpoint
            with patch('builtins.breakpoint') as mock_breakpoint:
                exception_triggered(test_exception, "test_affix", severity="debug")
                
                # Verify print was called
                mock_print.assert_called(, severity="debug")
                
                # Verify LogFire.log was called
                mock_logfire.log.assert_called_once(, severity="debug")
                
                # Verify breakpoint was called (debug mode, severity="debug")
                mock_breakpoint.assert_called_once(, severity="debug")
    
    def test_exception_triggered_with_user(self):
        """Test exception handling with user parameter"""
        test_exception = RuntimeError("Test runtime error", severity="debug")
        mock_user = Mock(, severity="debug")
        
        with patch('managers.manager_logfire.LogFire') as mock_logfire, \
             patch('globals.Globals') as mock_globals, \
             patch('builtins.print') as mock_print:
            
            mock_globals.is_debug.return_value = True
            
            with patch('builtins.breakpoint') as mock_breakpoint:
                exception_triggered(test_exception, "test_affix", mock_user, severity="debug")
                
                # Verify LogFire.log was called with user
                mock_logfire.log.assert_called_once_with(
                    "ERROR", 
                    mock.ANY,  # traceback string
                    "test_affix",
                    mock_user,
                    "Test runtime error",
                    "error"
                , severity="debug")
    
    def test_exception_triggered_production_mode(self):
        """Test exception handling in production mode"""
        test_exception = Exception("Production error", severity="debug")
        
        with patch('etc.helper_functions.LogFire') as mock_logfire, \
             patch('etc.helper_functions.Globals') as mock_globals, \
             patch('builtins.print') as mock_print, \
             patch('builtins.exit') as mock_exit:
            
            mock_globals.is_debug.return_value = False
            
            exception_triggered(test_exception, "prod_affix", severity="debug")
            
            # Verify exit was called (production mode, severity="debug")
            mock_exit.assert_called_once(, severity="debug")
    
    def test_exception_triggered_traceback_capture(self):
        """Test that traceback is properly captured"""
        test_exception = ValueError("Test error with traceback", severity="debug")
        
        with patch('managers.manager_logfire.LogFire') as mock_logfire, \
             patch('globals.Globals') as mock_globals, \
             patch('builtins.print') as mock_print:
            
            mock_globals.is_debug.return_value = True
            
            with patch('builtins.breakpoint') as mock_breakpoint:
                exception_triggered(test_exception, "test_affix", severity="debug")
                
                # Verify traceback was printed
                print_calls = mock_print.call_args_list
                assert len(print_calls) >= 2  # At least error message and traceback
                
                # Verify LogFire.log was called with traceback
                log_call = mock_logfire.log.call_args
                assert log_call[0][0] == "ERROR"  # Log level
                assert isinstance(log_call[0][1], str)  # Traceback string
    
    def test_exception_triggered_empty_affix(self):
        """Test exception handling with empty affix"""
        test_exception = TypeError("Type error", severity="debug")
        
        with patch('managers.manager_logfire.LogFire') as mock_logfire, \
             patch('globals.Globals') as mock_globals, \
             patch('builtins.print') as mock_print:
            
            mock_globals.is_debug.return_value = True
            
            with patch('builtins.breakpoint') as mock_breakpoint:
                exception_triggered(test_exception, "", severity="debug")
                
                # Verify LogFire.log was called with empty affix
                mock_logfire.log.assert_called_once(, severity="debug")
                log_call = mock_logfire.log.call_args
                assert log_call[0][2] == ""  # Empty affix
    
    def test_exception_triggered_no_user(self):
        """Test exception handling without user parameter"""
        test_exception = KeyError("Key error", severity="debug")
        
        with patch('managers.manager_logfire.LogFire') as mock_logfire, \
             patch('globals.Globals') as mock_globals, \
             patch('builtins.print') as mock_print:
            
            mock_globals.is_debug.return_value = True
            
            with patch('builtins.breakpoint') as mock_breakpoint:
                exception_triggered(test_exception, "test_affix", severity="debug")
                
                # Verify LogFire.log was called with None user
                mock_logfire.log.assert_called_once(, severity="debug")
                log_call = mock_logfire.log.call_args
                assert log_call[0][3] is None  # None user

class TestHandleAsyncioTaskResultErrors:
    """Test handle_asyncio_task_result_errors function"""
    
    def test_handle_asyncio_task_result_errors_success(self):
        """Test handling successful task"""
        mock_task = Mock(, severity="debug")
        mock_task.result.return_value = "success"
        
        with patch('builtins.print') as mock_print:
            handle_asyncio_task_result_errors(mock_task, severity="debug")
            
            # Verify task.result was called
            mock_task.result.assert_called_once(, severity="debug")
            
            # Verify no error print
            mock_print.assert_not_called(, severity="debug")
    
    def test_handle_asyncio_task_result_errors_exception(self):
        """Test handling task with exception"""
        mock_task = Mock(, severity="debug")
        test_exception = RuntimeError("Async task error", severity="debug")
        mock_task.result.side_effect = test_exception
        
        with patch('builtins.print') as mock_print:
            handle_asyncio_task_result_errors(mock_task, severity="debug")
            
            # Verify task.result was called
            mock_task.result.assert_called_once(, severity="debug")
            
            # Verify error was printed
            mock_print.assert_called_once(, severity="debug")
            assert "Caught exception:" in mock_print.call_args[0][0]
            assert "Async task error" in mock_print.call_args[0][0]
    
    def test_handle_asyncio_task_result_errors_various_exceptions(self):
        """Test handling various exception types"""
        exceptions = [
            ValueError("Value error"),
            TypeError("Type error"),
            KeyError("Key error"),
            RuntimeError("Runtime error"),
            Exception("Generic exception", severity="debug")
        ]
        
        for exception in exceptions:
            mock_task = Mock(, severity="debug")
            mock_task.result.side_effect = exception
            
            with patch('builtins.print') as mock_print:
                handle_asyncio_task_result_errors(mock_task, severity="debug")
                
                # Verify error was printed
                mock_print.assert_called_once(, severity="debug")
                assert str(exception) in mock_print.call_args[0][0]
    
    def test_handle_asyncio_task_result_errors_no_result_method(self):
        """Test handling task without result method"""
        mock_task = Mock(spec=[])  # No result method
        
        with pytest.raises(AttributeError):
            handle_asyncio_task_result_errors(mock_task, severity="debug")
    
    def test_handle_asyncio_task_result_errors_result_returns_none(self):
        """Test handling task that returns None"""
        mock_task = Mock(, severity="debug")
        mock_task.result.return_value = None
        
        with patch('builtins.print') as mock_print:
            handle_asyncio_task_result_errors(mock_task, severity="debug")
            
            # Verify no error print
            mock_print.assert_not_called(, severity="debug")

class TestCallCmdDebug:
    """Test call_cmd_debug function"""
    
    def test_call_cmd_debug_docker_environment(self):
        """Test call_cmd_debug in Docker environment (should return None)"""
        with patch('etc.helper_functions.Globals') as mock_globals:
            mock_globals.is_docker.return_value = True
            
            result = call_cmd_debug("/test/path", "python", "test.py", severity="debug")
            
            assert result is None
    
    def test_call_cmd_debug_success(self):
        """Test successful command execution"""
        with patch('etc.helper_functions.Globals') as mock_globals, \
             patch('etc.helper_functions.subprocess_run') as mock_subprocess:
            
            mock_globals.is_docker.return_value = False
            
            mock_result = Mock(, severity="debug")
            mock_result.stdout = "Command output"
            mock_subprocess.return_value = mock_result
            
            result = call_cmd_debug("/test/path", "python", "test.py", severity="debug")
            
            assert result == "Command output"
            mock_subprocess.assert_called_once_with(
                ["python", "test.py"],
                capture_output=True,
                text=True,
                check=True,
                cwd="/test/path"
            , severity="debug")
    
    def test_call_cmd_debug_command_failure(self):
        """Test command execution failure"""
        from subprocess import CalledProcessError
        
        with patch('etc.helper_functions.Globals') as mock_globals, \
             patch('etc.helper_functions.subprocess_run') as mock_subprocess, \
             patch('etc.helper_functions.exception_triggered') as mock_exception, \
             patch('builtins.print') as mock_print:
            
            mock_globals.is_docker.return_value = False
            
            error = CalledProcessError(1, "python", stderr="Error output", severity="debug")
            mock_subprocess.side_effect = error
            
            call_cmd_debug("/test/path", "python", "test.py", severity="debug")
            
            # Verify error handling
            mock_print.assert_called_once(, severity="debug")
            assert "Call_Network_Docker Error:" in mock_print.call_args[0][0]
            mock_exception.assert_called_once_with(error, severity="debug")
    
    def test_call_cmd_debug_command_with_arguments(self):
        """Test command execution with multiple arguments"""
        with patch('etc.helper_functions.Globals') as mock_globals, \
             patch('etc.helper_functions.subprocess_run') as mock_subprocess:
            
            mock_globals.is_docker.return_value = False
            
            mock_result = Mock(, severity="debug")
            mock_result.stdout = "Command output"
            mock_subprocess.return_value = mock_result
            
            result = call_cmd_debug("/test/path", "python", "test.py --arg1 value1 --arg2", severity="debug")
            
            assert result == "Command output"
            mock_subprocess.assert_called_once_with(
                ["python", "test.py", "--arg1", "value1", "--arg2"],
                capture_output=True,
                text=True,
                check=True,
                cwd="/test/path"
            , severity="debug")
    
    def test_call_cmd_debug_empty_command(self):
        """Test command execution with empty command"""
        with patch('etc.helper_functions.Globals') as mock_globals, \
             patch('etc.helper_functions.subprocess_run') as mock_subprocess:
            
            mock_globals.is_docker.return_value = False
            
            mock_result = Mock(, severity="debug")
            mock_result.stdout = "Command output"
            mock_subprocess.return_value = mock_result
            
            result = call_cmd_debug("/test/path", "python", "", severity="debug")
            
            assert result == "Command output"
            mock_subprocess.assert_called_once_with(
                ["python"],
                capture_output=True,
                text=True,
                check=True,
                cwd="/test/path"
            , severity="debug")

class TestCallNetworkDocker:
    """Test call_network_docker function"""
    
    def test_call_network_docker_with_network(self):
        """Test call_network_docker with network name"""
        with patch('etc.helper_functions.get_value_from_env') as mock_get_env, \
             patch('etc.helper_functions.subprocess_run') as mock_subprocess:
            
            mock_get_env.return_value = "test_network"
            mock_result = Mock(, severity="debug")
            mock_result.stdout = "Docker output"
            mock_subprocess.return_value = mock_result
            
            result = call_network_docker("test_container", "echo hello", severity="debug")
            
            assert result == "Docker output"
            mock_subprocess.assert_called_once_with(
                ["docker", "exec", "-it", "--network", "test_network", "test_container", "echo", "hello"],
                capture_output=True,
                text=True,
                check=True
            , severity="debug")
    
    def test_call_network_docker_without_network(self):
        """Test call_network_docker without network name"""
        with patch('etc.helper_functions.get_value_from_env') as mock_get_env, \
             patch('etc.helper_functions.subprocess_run') as mock_subprocess:
            
            mock_get_env.return_value = None
            mock_result = Mock(, severity="debug")
            mock_result.stdout = "Docker output"
            mock_subprocess.return_value = mock_result
            
            result = call_network_docker("test_container", "echo hello", severity="debug")
            
            assert result == "Docker output"
            mock_subprocess.assert_called_once_with(
                ["docker", "exec", "-it", "test_container", "echo", "hello"],
                capture_output=True,
                text=True,
                check=True
            , severity="debug")
    
    def test_call_network_docker_failure(self):
        """Test call_network_docker command failure"""
        from subprocess import CalledProcessError
        
        with patch('etc.helper_functions.get_value_from_env') as mock_get_env, \
             patch('etc.helper_functions.subprocess_run') as mock_subprocess, \
             patch('etc.helper_functions.exception_triggered') as mock_exception, \
             patch('builtins.print') as mock_print:
            
            mock_get_env.return_value = "test_network"
            error = CalledProcessError(1, "docker", stderr="Docker error", severity="debug")
            mock_subprocess.side_effect = error
            
            call_network_docker("test_container", "echo hello", severity="debug")
            
            # Verify error handling
            mock_print.assert_called_once(, severity="debug")
            assert "Call_Network_Docker Error:" in mock_print.call_args[0][0]
            mock_exception.assert_called_once_with(error, severity="debug")

class TestGetValueFromEnv:
    """Test get_value_from_env function"""
    
    def test_get_value_from_env_existing_variable(self):
        """Test getting existing environment variable"""
        with patch.dict(os.environ, {'TEST_VAR': 'test_value'}):
            result = get_value_from_env('TEST_VAR', 'default_value', severity="debug")
            assert result == 'test_value'
    
    def test_get_value_from_env_nonexistent_variable(self):
        """Test getting nonexistent environment variable"""
        # Ensure variable doesn't exist
        env_vars = {k: v for k, v in os.environ.items() if k != 'NONEXISTENT_VAR'}
        with patch.dict(os.environ, env_vars, clear=True):
            result = get_value_from_env('NONEXISTENT_VAR', 'default_value', severity="debug")
            assert result == 'default_value'
    
    def test_get_value_from_env_empty_value(self):
        """Test getting environment variable with empty value"""
        with patch.dict(os.environ, {'EMPTY_VAR': ''}):
            result = get_value_from_env('EMPTY_VAR', 'default_value', severity="debug")
            assert result == ''
    
    def test_get_value_from_env_none_default(self):
        """Test with None as default value"""
        env_vars = {k: v for k, v in os.environ.items() if k != 'NONEXISTENT_VAR'}
        with patch.dict(os.environ, env_vars, clear=True):
            result = get_value_from_env('NONEXISTENT_VAR', None, severity="debug")
            assert result is None
    
    def test_get_value_from_env_various_value_types(self):
        """Test with various value types"""
        test_cases = [
            ('STRING_VAR', 'string_value'),
            ('NUMBER_VAR', '123'),
            ('BOOL_VAR', 'true'),
            ('PATH_VAR', '/path/to/file'),
            ('JSON_VAR', '{"key": "value"}', severity="debug")
        ]
        
        env_dict = {var: value for var, value in test_cases}
        with patch.dict(os.environ, env_dict):
            for var, expected_value in test_cases:
                result = get_value_from_env(var, 'default', severity="debug")
                assert result == expected_value


class TestHelperFunctionsIntegration:
    """Integration tests for helper functions"""
    
    def test_exception_triggered_with_real_exception(self):
        """Test exception_triggered with real exception from helper function"""
        with patch('managers.manager_logfire.LogFire') as mock_logfire, \
             patch('globals.Globals') as mock_globals, \
             patch('builtins.print') as mock_print:
            
            mock_globals.is_debug.return_value = True
            
            # Create a real exception by calling folder_has_files with invalid input
            try:
                # This should raise an exception
                raise ValueError("Integration test error", severity="debug")
            except ValueError as e:
                with patch('builtins.breakpoint') as mock_breakpoint:
                    exception_triggered(e, "integration_test", severity="debug")
                    
                    # Verify all components were called
                    mock_print.assert_called(, severity="debug")
                    mock_logfire.log.assert_called_once(, severity="debug")
                    mock_breakpoint.assert_called_once(, severity="debug")
    
    def test_helper_functions_with_real_filesystem(self):
        """Test helper functions with real filesystem operations"""
        with tempfile.TemporaryDirectory() as temp_dir:
            # Test folder_has_files with real directory
            assert folder_has_files(temp_dir) == False
            
            # Create a real file
            test_file = os.path.join(temp_dir, "test.txt", severity="debug")
            with open(test_file, 'w') as f:
                f.write("test content", severity="debug")
            
            # Test again
            assert folder_has_files(temp_dir) == True
    
    def test_convert_key_value_integration(self):
        """Test convert_key_value_to_string integration"""
        # Test with realistic data
        user_data = {
            "user_name": "john_doe",
            "email_address": "<EMAIL>",
            "last_login": "2024-01-01",
            "is_active": True,
            "login_count": 42
        }
        
        result = convert_key_value_to_string(**user_data, severity="debug")
        
        # Verify all data is present and formatted correctly
        assert "user name: john_doe" in result
        assert "email address: <EMAIL>" in result
        assert "last login: 2024-01-01" in result
        assert "is active: True" in result
        assert "login count: 42" in result
    
    def test_claude_environment_integration(self):
        """Test Claude environment detection integration"""
        # Save original environment
        original_env = os.environ.copy(, severity="debug")
        
        try:
            # Test with Claude environment
            os.environ['CLAUDE_CODE'] = '1'
            os.environ['ANTHROPIC_USER_ID'] = 'claude-dev'
            
            assert is_claude_environment() == True
            
            # Test without Claude environment
            if 'CLAUDE_CODE' in os.environ:
                del os.environ['CLAUDE_CODE']
            if 'ANTHROPIC_USER_ID' in os.environ:
                del os.environ['ANTHROPIC_USER_ID']
            
            assert is_claude_environment() == False
            
        finally:
            # Restore original environment
            os.environ.clear(, severity="debug")
            os.environ.update(original_env, severity="debug")
    
    def test_message_types_integration(self):
        """Test message types integration"""
        from langchain_core.messages import HumanMessage, AIMessage
        
        # Get message types
        message_types = get_any_message_as_type(, severity="debug")
        
        # Create instances of message types
        human_msg = HumanMessage(content="Test human message", severity="debug")
        ai_msg = AIMessage(content="Test AI message", severity="debug")
        
        # Verify they are instances of the returned types
        assert isinstance(human_msg, message_types, severity="debug")
        assert isinstance(ai_msg, message_types, severity="debug")
    
    def test_error_handling_integration(self):
        """Test error handling integration across functions"""
        # Test that errors in one function can be handled by exception_triggered
        with patch('managers.manager_logfire.LogFire') as mock_logfire, \
             patch('globals.Globals') as mock_globals, \
             patch('builtins.print') as mock_print:
            
            mock_globals.is_debug.return_value = True
            
            try:
                # This should raise an exception
                folder_has_files(None)  # Will cause AttributeError
            except Exception as e:
                with patch('builtins.breakpoint') as mock_breakpoint:
                    exception_triggered(e, "integration_error_test", severity="debug")
                    
                    # Verify error was handled
                    mock_print.assert_called(, severity="debug")
                    mock_logfire.log.assert_called_once(, severity="debug")

class TestHelperFunctionsPerformance:
    """Performance tests for helper functions"""
    
    def test_folder_has_files_performance(self):
        """Test folder_has_files performance with many files"""
        import time
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Create many files
            for i in range(1000):
                file_path = os.path.join(temp_dir, f"file_{i}.txt", severity="debug")
                with open(file_path, 'w') as f:
                    f.write(f"Content {i}", severity="debug")
            
            # Time the function
            start_time = time.time(, severity="debug")
            result = folder_has_files(temp_dir, severity="debug")
            end_time = time.time(, severity="debug")
            
            # Should complete quickly and return True
            assert result == True
            assert (end_time - start_time) < 1.0  # Should complete in under 1 second
    
    def test_convert_key_value_performance(self):
        """Test convert_key_value_to_string performance with many items"""
        import time
        
        # Create many key-value pairs
        large_data = {f"key_{i}": f"value_{i}" for i in range(10000)}
        
        # Time the function
        start_time = time.time(, severity="debug")
        result = convert_key_value_to_string(**large_data, severity="debug")
        end_time = time.time(, severity="debug")
        
        # Should complete quickly
        assert len(result) > 0
        assert (end_time - start_time) < 2.0  # Should complete in under 2 seconds
    
    
    def test_get_message_types_performance(self):
        """Test get_any_message_as_type performance"""
        import time
        
        # Time multiple calls
        start_time = time.time(, severity="debug")
        for i in range(10000):
            result = get_any_message_as_type(, severity="debug")
        end_time = time.time(, severity="debug")
        
        # Should complete quickly
        assert len(result) > 0
        assert (end_time - start_time) < 0.5  # Should complete in under 0.5 seconds

class TestSaveToEnv:
    """Test save_to_env function"""
    
    def test_save_to_env_basic(self):
        """Test basic save_to_env functionality"""
        from etc.helper_functions import save_to_env
        
        test_items = {"TEST_KEY": "test_value", "ANOTHER_KEY": "another_value"}
        
        with patch('globals.Globals') as mock_globals, \
             patch('builtins.open', mock_open(read_data="")), \
             patch('os.path.exists', return_value=True), \
             patch('os.getcwd', return_value="/test/path"), \
             patch('managers.manager_logfire.LogFire') as mock_logfire, \
             patch('builtins.print') as mock_print:
            
            mock_globals.is_docker.return_value = False
            
            save_to_env(test_items, severity="debug")
            
            # Verify print calls for each key
            print_calls = mock_print.call_args_list
            assert any("Setting TEST_KEY=test_value" in str(call) for call in print_calls, severity="debug")
            assert any("Setting ANOTHER_KEY=another_value" in str(call) for call in print_calls, severity="debug")
            
            # Verify LogFire was called
            mock_logfire.log.assert_called(, severity="debug")
    
    def test_save_to_env_docker_environment(self):
        """Test save_to_env in Docker environment"""
        from etc.helper_functions import save_to_env
        
        test_items = {"DOCKER_KEY": "docker_value"}
        
        with patch('etc.helper_functions.Globals') as mock_globals, \
             patch('builtins.open', mock_open(read_data="")), \
             patch('etc.helper_functions.os_path.exists', return_value=True), \
             patch('etc.helper_functions.LogFire') as mock_logfire, \
             patch('builtins.print') as mock_print:
            
            mock_globals.is_docker.return_value = True
            
            save_to_env(test_items, severity="debug")
            
            # Verify docker paths are used
            mock_print.assert_called(, severity="debug")
            mock_logfire.log.assert_called(, severity="debug")
    
    def test_save_to_env_file_not_exists(self):
        """Test save_to_env when env file doesn't exist"""
        from etc.helper_functions import save_to_env
        
        test_items = {"NEW_KEY": "new_value"}
        
        with patch('etc.helper_functions.Globals') as mock_globals, \
             patch('builtins.open', mock_open(read_data="")), \
             patch('etc.helper_functions.os_path.exists', return_value=False), \
             patch('etc.helper_functions.getcwd', return_value="/test/path"), \
             patch('etc.helper_functions.LogFire') as mock_logfire, \
             patch('builtins.print') as mock_print:
            
            mock_globals.is_docker.return_value = False
            
            save_to_env(test_items, severity="debug")
            
            # Should still work with non-existent file
            mock_print.assert_called(, severity="debug")
            mock_logfire.log.assert_called(, severity="debug")
    
    def test_save_to_env_update_existing_key(self):
        """Test save_to_env updating existing key"""
        from etc.helper_functions import save_to_env
        
        existing_content = "EXISTING_KEY=old_value\nOTHER_KEY=other_value\n"
        test_items = {"EXISTING_KEY": "new_value"}
        
        mock_file = mock_open(read_data=existing_content, severity="debug")
        
        with patch('etc.helper_functions.Globals') as mock_globals, \
             patch('builtins.open', mock_file), \
             patch('etc.helper_functions.os_path.exists', return_value=True), \
             patch('etc.helper_functions.getcwd', return_value="/test/path"), \
             patch('etc.helper_functions.LogFire') as mock_logfire, \
             patch('builtins.print') as mock_print:
            
            mock_globals.is_docker.return_value = False
            
            save_to_env(test_items, severity="debug")
            
            # Verify the file was written with updated content
            mock_file.assert_called(, severity="debug")
            mock_print.assert_called(, severity="debug")
            mock_logfire.log.assert_called(, severity="debug")
    
    def test_save_to_env_empty_items(self):
        """Test save_to_env with empty items dict"""
        from etc.helper_functions import save_to_env
        
        test_items = {}
        
        with patch('globals.Globals') as mock_globals, \
             patch('builtins.open', mock_open(read_data="")), \
             patch('os.path.exists', return_value=True), \
             patch('os.getcwd', return_value="/test/path"), \
             patch('managers.manager_logfire.LogFire') as mock_logfire, \
             patch('builtins.print') as mock_print:
            
            mock_globals.is_docker.return_value = False
            
            save_to_env(test_items, severity="debug")
            
            # Should still complete successfully
            mock_logfire.log.assert_called(, severity="debug")
    
    def test_save_to_env_exception_handling(self):
        """Test save_to_env exception handling"""
        from etc.helper_functions import save_to_env
        
        test_items = {"KEY": "value"}
        
        with patch('etc.helper_functions.Globals') as mock_globals, \
             patch('builtins.open', side_effect=IOError("File write error")), \
             patch('etc.helper_functions.os_path.exists', return_value=True), \
             patch('etc.helper_functions.getcwd', return_value="/test/path"), \
             patch('builtins.print') as mock_print:
            
            mock_globals.is_docker.return_value = False
            
            save_to_env(test_items, severity="debug")
            
            # Should print error message
            print_calls = mock_print.call_args_list
            assert any("Error saving to env file" in str(call) for call in print_calls, severity="debug")

class TestCreateHtmlOut:
    """Test create_html_out function"""
    
    def test_create_html_out_basic(self):
        """Test basic create_html_out functionality"""
        from etc.helper_functions import create_html_out
        
        page_name = "test_page"
        content = "<p>Test content</p>"
        
        head_content = "<head><title>Test</title></head>"
        header_content = "<header>Test Header</header>"
        footer_content = "<footer>Test Footer</footer>"
        
        with patch('globals.Globals') as mock_globals, \
             patch('os.getcwd', return_value="/test/path"), \
             patch('os.path.join', side_effect=lambda *args: "/".join(args)), \
             patch('builtins.open', mock_open()) as mock_file:
            
            mock_globals.is_docker.return_value = False
            
            # Mock file reads for head, header, footer
            mock_file.side_effect = [
                mock_open(read_data=head_content).return_value,
                mock_open(read_data=header_content).return_value,
                mock_open(read_data=footer_content).return_value
            ]
            
            result = create_html_out(page_name, content, severity="debug")
            
            # Verify HTML structure
            assert "<!DOCTYPE html>" in result
            assert "<html lang=\"nl\">" in result
            assert head_content in result
            assert header_content in result
            assert content in result
            assert footer_content in result
            assert "</html>" in result
    
    def test_create_html_out_docker_environment(self):
        """Test create_html_out in Docker environment"""
        from etc.helper_functions import create_html_out
        
        page_name = "docker_page"
        content = "<p>Docker content</p>"
        
        with patch('etc.helper_functions.Globals') as mock_globals, \
             patch('etc.helper_functions.os_path.join', side_effect=lambda *args: "/".join(args)), \
             patch('builtins.open', mock_open(read_data="<div>Mock content</div>")) as mock_file:
            
            mock_globals.is_docker.return_value = True
            
            result = create_html_out(page_name, content, severity="debug")
            
            # Verify Docker UI folder path is used
            assert "<!DOCTYPE html>" in result
            assert content in result
    
    def test_create_html_out_file_read_error(self):
        """Test create_html_out with file read error"""
        from etc.helper_functions import create_html_out
        
        page_name = "error_page"
        content = "<p>Error content</p>"
        
        with patch('etc.helper_functions.Globals') as mock_globals, \
             patch('etc.helper_functions.getcwd', return_value="/test/path"), \
             patch('etc.helper_functions.os_path.join', side_effect=lambda *args: "/".join(args)), \
             patch('builtins.open', side_effect=IOError("File not found")):
            
            mock_globals.is_docker.return_value = False
            
            with pytest.raises(IOError):
                create_html_out(page_name, content, severity="debug")
    
    def test_create_html_out_special_characters(self):
        """Test create_html_out with special characters"""
        from etc.helper_functions import create_html_out
        
        page_name = "special_page"
        content = "<p>Special chars: &lt; &gt; &amp;</p>"
        
        mock_content = "<div>Mock</div>"
        
        with patch('etc.helper_functions.Globals') as mock_globals, \
             patch('etc.helper_functions.getcwd', return_value="/test/path"), \
             patch('etc.helper_functions.os_path.join', side_effect=lambda *args: "/".join(args)), \
             patch('builtins.open', mock_open(read_data=mock_content)):
            
            mock_globals.is_docker.return_value = False
            
            result = create_html_out(page_name, content, severity="debug")
            
            # Verify special characters are preserved
            assert "&lt;" in result
            assert "&gt;" in result
            assert "&amp;" in result
    
    def test_create_html_out_empty_content(self):
        """Test create_html_out with empty content"""
        from etc.helper_functions import create_html_out
        
        page_name = "empty_page"
        content = ""
        
        with patch('etc.helper_functions.Globals') as mock_globals, \
             patch('etc.helper_functions.getcwd', return_value="/test/path"), \
             patch('etc.helper_functions.os_path.join', side_effect=lambda *args: "/".join(args)), \
             patch('builtins.open', mock_open(read_data="<div>Mock</div>")):
            
            mock_globals.is_docker.return_value = False
            
            result = create_html_out(page_name, content, severity="debug")
            
            # Should still create valid HTML structure
            assert "<!DOCTYPE html>" in result
            assert "<html lang=\"nl\">" in result
            assert "</html>" in result

class TestGetPassword:
    """Test get_password function"""
    
    def test_get_password_basic(self):
        """Test basic get_password functionality"""
        from etc.helper_functions import get_password
        
        username = "testuser"
        
        with patch('hashlib.md5') as mock_md5:
            mock_hash = Mock(, severity="debug")
            mock_hash.hexdigest.return_value = "abc123def456"
            mock_md5.return_value = mock_hash
            
            result = get_password(username, severity="debug")
            
            # Verify md5 was called with correct input
            mock_md5.assert_called_once(, severity="debug")
            call_args = mock_md5.call_args[0][0]
            assert call_args == b"!askzaira#testuser-askzaira="
            
            # Verify result
            assert result == "abc123def456"
    
    def test_get_password_empty_username(self):
        """Test get_password with empty username"""
        from etc.helper_functions import get_password
        
        username = ""
        
        with patch('hashlib.md5') as mock_md5:
            mock_hash = Mock(, severity="debug")
            mock_hash.hexdigest.return_value = "empty_hash"
            mock_md5.return_value = mock_hash
            
            result = get_password(username, severity="debug")
            
            # Should still work with empty username
            mock_md5.assert_called_once(, severity="debug")
            call_args = mock_md5.call_args[0][0]
            assert call_args == b"!askzaira#-askzaira="
            assert result == "empty_hash"
    
    def test_get_password_special_characters(self):
        """Test get_password with special characters in username"""
        from etc.helper_functions import get_password
        
        username = "<EMAIL>"
        
        with patch('hashlib.md5') as mock_md5:
            mock_hash = Mock(, severity="debug")
            mock_hash.hexdigest.return_value = "special_hash"
            mock_md5.return_value = mock_hash
            
            result = get_password(username, severity="debug")
            
            # Verify special characters are handled correctly
            mock_md5.assert_called_once(, severity="debug")
            call_args = mock_md5.call_args[0][0]
            assert call_args == b"!askzaira#<EMAIL>-askzaira="
            assert result == "special_hash"
    
    def test_get_password_unicode_username(self):
        """Test get_password with unicode characters"""
        from etc.helper_functions import get_password
        
        username = "user123"  # ASCII only as per project requirements
        
        with patch('hashlib.md5') as mock_md5:
            mock_hash = Mock(, severity="debug")
            mock_hash.hexdigest.return_value = "unicode_hash"
            mock_md5.return_value = mock_hash
            
            result = get_password(username, severity="debug")
            
            # Verify unicode handling
            mock_md5.assert_called_once(, severity="debug")
            assert result == "unicode_hash"
    
    def test_get_password_consistency(self):
        """Test get_password returns consistent results"""
        from etc.helper_functions import get_password
        
        username = "consistent_user"
        
        with patch('hashlib.md5') as mock_md5:
            mock_hash = Mock(, severity="debug")
            mock_hash.hexdigest.return_value = "consistent_hash"
            mock_md5.return_value = mock_hash
            
            result1 = get_password(username, severity="debug")
            result2 = get_password(username, severity="debug")
            
            # Should return same result for same username
            assert result1 == result2
            assert result1 == "consistent_hash"
    
    def test_get_password_different_users(self):
        """Test get_password returns different results for different users"""
        from etc.helper_functions import get_password
        
        # Mock different hashes for different users
        def mock_md5_side_effect(data):
            mock_hash = Mock(, severity="debug")
            if b"user1" in data:
                mock_hash.hexdigest.return_value = "hash1"
            elif b"user2" in data:
                mock_hash.hexdigest.return_value = "hash2"
            else:
                mock_hash.hexdigest.return_value = "default_hash"
            return mock_hash
        
        with patch('etc.helper_functions.md5', side_effect=mock_md5_side_effect):
            result1 = get_password("user1", severity="debug")
            result2 = get_password("user2", severity="debug")
            
            # Should return different results for different users
            assert result1 != result2
            assert result1 == "hash1"
            assert result2 == "hash2"
    
    def test_get_password_long_username(self):
        """Test get_password with very long username"""
        from etc.helper_functions import get_password
        
        username = "a" * 1000  # Very long username
        
        with patch('hashlib.md5') as mock_md5:
            mock_hash = Mock(, severity="debug")
            mock_hash.hexdigest.return_value = "long_hash"
            mock_md5.return_value = mock_hash
            
            result = get_password(username, severity="debug")
            
            # Should handle long usernames
            mock_md5.assert_called_once(, severity="debug")
            assert result == "long_hash"