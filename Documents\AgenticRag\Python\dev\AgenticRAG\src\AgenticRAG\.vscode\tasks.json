{"version": "2.0.0", "tasks": [{"label": "startDockerDesktop", "type": "shell", "command": "powershell", "args": ["-ExecutionPolicy", "Bypass", "-File", ".vscode/start-docker.ps1"], "group": "build", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared"}, "problemMatcher": []}, {"label": "Run test_real_email", "type": "shell", "command": "${workspaceFolder}/tests/test_real/bats/run_test_real_email.bat", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "Run test_real_bot", "type": "shell", "command": "${workspaceFolder}/tests/test_real/bats/run_test_real_bot.bat", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "Run test_real_data", "type": "shell", "command": "${workspaceFolder}/tests/test_real/bats/run_test_real_data.bat", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "Run test_real_agenda", "type": "shell", "command": "${workspaceFolder}/tests/test_real/bats/run_test_real_agenda.bat", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "Run test_real_execution_suite", "type": "shell", "command": "${workspaceFolder}/tests/test_real/bats/run_test_real_execution_suite.bat", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "Run test_real_imap_smtp_broadcast", "type": "shell", "command": "${workspaceFolder}/tests/test_real/bats/run_test_real_imap_smtp_broadcast.bat", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}, {"label": "Run test_real_smtp_with_bot", "type": "shell", "command": "${workspaceFolder}/tests/test_real/bats/run_test_real_smtp_with_bot.bat", "group": "test", "presentation": {"echo": true, "reveal": "always", "focus": false, "panel": "shared", "showReuseMessage": true, "clear": false}, "options": {"cwd": "${workspaceFolder}"}, "problemMatcher": []}], "inputs": [{"id": "batchFile", "description": "Select batch file to run", "type": "pickString", "options": ["${workspaceFolder}/tests/test_real/bats/run_test_real_email.bat", "${workspaceFolder}/tests/test_real/bats/run_test_real_bot.bat", "${workspaceFolder}/tests/test_real/bats/run_test_real_data.bat", "${workspaceFolder}/tests/test_real/bats/run_test_real_agenda.bat", "${workspaceFolder}/tests/test_real/bats/run_test_real_execution_suite.bat", "${workspaceFolder}/tests/test_real/bats/run_test_real_imap_smtp_broadcast.bat", "${workspaceFolder}/tests/test_real/bats/run_test_real_smtp_with_bot.bat"]}]}