import asyncio
import sys
import os

# Add current directory to path
sys.path.insert(0, os.path.dirname(__file__))

async def test_basic_flow():
    print("=== Starting basic flow test ===")
    
    try:
        # Import ZairaSettings
        from etc.ZairaSettings import ZairaSettings
        ZairaSettings.IsDebugMode = True
        print("=== ZairaSettings imported ===")
        
        # Import LogFire
        from managers.manager_logfire import LogFire
        print("=== LogFire imported ===")
        
        # Try LogFire.log with INIT
        LogFire.log("INIT", "Test INIT log")
        print("=== INIT log successful ===")
        
        # Try LogFire.log with DEBUG severity
        print("=== About to try DEBUG severity log ===")
        LogFire.log("DEBUG", "Test DEBUG log", severity="debug")
        print("=== DEBUG log successful ===")
        
        print("=== All tests passed ===")
        
    except Exception as e:
        print(f"=== ERROR: {e} ===")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Running basic flow test...")
    asyncio.run(test_basic_flow())
    print("Test completed")