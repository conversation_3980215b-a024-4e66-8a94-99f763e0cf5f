from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
import asyncio
from unittest.mock import AsyncMock, MagicMock, patch
from endpoints.testing_endpoint import MyBot_Testing
from uuid import uuid4

class TestMyBotTesting:
    """Test suite for MyBot_Testing"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.bot = MyBot_Testing()
        
        # Mock task and user
        self.mock_task = MagicMock()
        self.mock_user = MagicMock()
        self.mock_user.session_guid = uuid4()
        self.mock_user.on_message = AsyncMock()
        self.mock_task.user = self.mock_user
        self.mock_task.scheduled_guid = str(uuid4())
        self.mock_task.original_physical_message = MagicMock()
        self.mock_task.human_in_the_loop_callback = MagicMock()
        
    def test_initialization(self):
        """Test MyBot_Testing initialization"""
        bot = MyBot_Testing()
        
        assert bot.name == "Testing"
        assert bot.parent_instance is None
        
    def test_initialization_with_params(self):
        """Test MyBot_Testing initialization with parameters"""
        parent = MagicMock()
        bot = MyBot_Testing(parent_instance=parent, name="CustomTesting")
        
        assert bot.name == "CustomTesting"
        assert bot.parent_instance == parent
        
    @pytest.mark.asyncio
    async def test_request_human_in_the_loop_testing_name(self):
        """Test request_human_in_the_loop_internal with Testing name"""
        bot = MyBot_Testing(name="Testing")
        
        with patch.object(bot, '_generate_test_response', return_value="Test response") as mock_generate, \
             patch('builtins.print') as mock_print:
            
            await bot.request_human_in_the_loop_internal(
                "Test request", self.mock_task, MagicMock()
            )
            
            mock_generate.assert_called_once_with("Test request")
            self.mock_task.user.on_message.assert_called_once_with(
                "Test response", bot, [], self.mock_task.original_physical_message
            )
            
            # Verify print statements
            assert mock_print.call_count >= 4
            
    @pytest.mark.asyncio
    async def test_request_human_in_the_loop_other_name(self):
        """Test request_human_in_the_loop_internal with other name"""
        bot = MyBot_Testing(name="OtherBot")
        
        with patch('endpoints.mybot_generic.MyBot_Generic.request_human_in_the_loop_internal') as mock_super:
            mock_super.return_value = AsyncMock()
            mock_message = MagicMock()
            
            await bot.request_human_in_the_loop_internal(
                "Test request", self.mock_task, mock_message
            )
            
            mock_super.assert_called_once_with(
                "Test request", self.mock_task, mock_message, False
            )
            
    @pytest.mark.asyncio
    async def test_request_human_in_the_loop_halt_until_response(self):
        """Test request_human_in_the_loop_internal with halt_until_response=True"""
        bot = MyBot_Testing(name="Testing")
        
        # Set up callback that will be cleared after a few iterations
        self.mock_task.human_in_the_loop_callback = MagicMock()
        
        async def clear_callback():
            await asyncio.sleep(0.1)
            self.mock_task.human_in_the_loop_callback = None
        
        with patch.object(bot, '_generate_test_response', return_value="Test response"), \
             patch('builtins.print'), \
             patch('asyncio.sleep', return_value=None) as mock_sleep:
            
            # Start the clearing task
            clear_task = asyncio.create_task(clear_callback())
            
            mock_message = MagicMock()
            await bot.request_human_in_the_loop_internal(
                "Test request", self.mock_task, mock_message, halt_until_response=True
            )
            
            await clear_task
            mock_sleep.assert_called()
            
    @pytest.mark.asyncio
    async def test_generate_test_response_with_llm(self):
        """Test _generate_test_response with LLM available"""
        bot = MyBot_Testing()
        
        mock_llm = MagicMock()
        mock_message = MagicMock()
        mock_message.content = "LLM response"
        mock_llm.ainvoke = AsyncMock(return_value=mock_message)
        
        with patch('etc.ZairaSettings.ZairaSettings.llm', mock_llm):
            response = await bot._generate_test_response("Test request")
            
            assert response == "LLM response"
            mock_llm.ainvoke.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_generate_test_response_llm_exception(self):
        """Test _generate_test_response with LLM exception"""
        bot = MyBot_Testing()
        
        mock_llm = MagicMock()
        mock_llm.ainvoke = AsyncMock(side_effect=Exception("LLM error"))
        
        with patch('etc.ZairaSettings.ZairaSettings.llm', mock_llm), \
             patch('builtins.print') as mock_print, \
             patch.object(bot, '_get_fallback_response', return_value="Fallback response") as mock_fallback:
            
            response = await bot._generate_test_response("Test request")
            
            assert response == "Fallback response"
            mock_print.assert_called()
            mock_fallback.assert_called_once_with("Test request")
            
    @pytest.mark.asyncio
    async def test_generate_test_response_no_llm(self):
        """Test _generate_test_response with no LLM"""
        bot = MyBot_Testing()
        
        with patch('etc.ZairaSettings.ZairaSettings', spec=[]) as mock_settings:
            # Mock ZairaSettings to not have llm attribute
            mock_settings.llm = None
            
            with patch.object(bot, '_get_fallback_response', return_value="Fallback response") as mock_fallback:
                response = await bot._generate_test_response("Test request")
                
                assert response == "Fallback response"
                mock_fallback.assert_called_once_with("Test request")
            
    def test_get_fallback_response_email_approval(self):
        """Test _get_fallback_response for email approval patterns"""
        bot = MyBot_Testing()
        
        test_cases = [
            "Wil je deze email goedkeuren?",
            "Approve this email?",
            "Verzending bevestigen j/n?",
            "GOEDKEUR dit bericht"
        ]
        
        for request in test_cases:
            response = bot._get_fallback_response(request)
            assert response == "j"
            
    def test_get_fallback_response_email_address(self):
        """Test _get_fallback_response for email address requests"""
        bot = MyBot_Testing()
        
        test_cases = [
            "Welk email adres?",
            "Naar welk address?",
            "What email address?",
            "Van welk mailadres?",
            "Welk e-mailadres?"
        ]
        
        for request in test_cases:
            response = bot._get_fallback_response(request)
            assert response == "<EMAIL>"
            
    def test_get_fallback_response_sender_email(self):
        """Test _get_fallback_response for sender email requests"""
        bot = MyBot_Testing()
        
        test_cases = [
            "Van welke sender?",
            "From verzender?",
            "Who is the sender?",
            "Verzonden door wie?"
        ]
        
        for request in test_cases:
            response = bot._get_fallback_response(request)
            assert response == "<EMAIL>"
            
    def test_get_fallback_response_yes_no(self):
        """Test _get_fallback_response for yes/no questions"""
        bot = MyBot_Testing()
        
        test_cases = [
            "Wil je dit ja of nee?",
            "Answer yes or no?",
            "y/n?",
            "JA of NEE?"
        ]
        
        for request in test_cases:
            response = bot._get_fallback_response(request)
            assert response == "ja"
            
    def test_get_fallback_response_default(self):
        """Test _get_fallback_response default case"""
        bot = MyBot_Testing()
        
        response = bot._get_fallback_response("Unknown request type")
        assert response == "ja"
        
    def test_get_fallback_response_case_insensitive(self):
        """Test _get_fallback_response is case insensitive"""
        bot = MyBot_Testing()
        
        test_cases = [
            "GOEDKEUR",
            "EMAIL",
            "SENDER",
            "YES"
        ]
        
        for request in test_cases:
            response = bot._get_fallback_response(request)
            assert response in ["j", "<EMAIL>", "ja"]
            
    def test_get_fallback_response_mixed_patterns(self):
        """Test _get_fallback_response with mixed patterns"""
        bot = MyBot_Testing()
        
        # Email pattern should take precedence over approval pattern
        response = bot._get_fallback_response("Welk email adres gebruiken?")
        assert response == "<EMAIL>"
        
        # Sender pattern should take precedence over yes/no pattern
        response = bot._get_fallback_response("Sender ja of nee?")
        assert response == "<EMAIL>"
        
class TestMyBotTestingEdgeCases:
    """Test edge cases for MyBot_Testing"""
    
    def setup_method(self):
        """Setup test fixtures"""
        self.bot = MyBot_Testing()
        
    @pytest.mark.asyncio
    async def test_exception_handling_in_generate_response(self):
        """Test exception handling in _generate_test_response"""
        bot = MyBot_Testing()
        
        with patch('etc.ZairaSettings.ZairaSettings.llm', side_effect=AttributeError("No LLM")), \
             patch('builtins.print') as mock_print:
            
            response = await bot._generate_test_response("Test request")
            
            # Should fall back to default response
            assert response == "ja"
            mock_print.assert_called()
            
    @pytest.mark.asyncio
    async def test_empty_request_handling(self):
        """Test handling of empty requests"""
        bot = MyBot_Testing()
        
        response = await bot._generate_test_response("")
        assert response == "ja"  # Default fallback
        
        response = bot._get_fallback_response("")
        assert response == "ja"  # Default fallback
        
    @pytest.mark.asyncio
    async def test_long_request_handling(self):
        """Test handling of very long requests"""
        bot = MyBot_Testing()
        
        long_request = "This is a very long request " * 100
        
        with patch.object(bot, '_get_fallback_response', return_value="Fallback") as mock_fallback:
            response = await bot._generate_test_response(long_request)
            
            assert response == "Fallback"
            mock_fallback.assert_called_once_with(long_request)
            
    def test_inheritance_from_mybot_generic(self):
        """Test that MyBot_Testing properly inherits from MyBot_Generic"""
        from endpoints.mybot_generic import MyBot_Generic
        
        bot = MyBot_Testing()
        
        assert isinstance(bot, MyBot_Generic)
        assert hasattr(bot, 'parent_instance')
        assert hasattr(bot, 'name')
        assert hasattr(bot, 'on_ready')
        assert hasattr(bot, 'on_message')
        assert hasattr(bot, 'send_broadcast')
        assert hasattr(bot, 'send_reply')
        
    @pytest.mark.asyncio
    async def test_unicode_handling(self):
        """Test handling of Unicode characters in requests"""
        bot = MyBot_Testing()
        
        unicode_request = "Goedkeur deze email?"
        
        response = bot._get_fallback_response(unicode_request)
        assert response == "j"  # Should match approval pattern
        
    @pytest.mark.asyncio
    async def test_multiple_pattern_matches(self):
        """Test requests that match multiple patterns"""
        bot = MyBot_Testing()
        
        # Email pattern should take precedence over approval pattern  
        response = bot._get_fallback_response("Welk email adres gebruiken voor verzending?")
        assert response == "<EMAIL>"
        
        # Should prioritize sender pattern over yes/no
        response = bot._get_fallback_response("Is de sender ja of nee bekend?")
        assert response == "<EMAIL>"
