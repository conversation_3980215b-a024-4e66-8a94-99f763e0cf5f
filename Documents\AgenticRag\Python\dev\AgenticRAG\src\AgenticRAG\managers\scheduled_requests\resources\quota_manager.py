from imports import *
from typing import Dict, Optional, List, Any, Tuple
from datetime import datetime, timezone, timedelta
import asyncio
import threading
from dataclasses import dataclass

from ..utils.config import UserQuotaConfig, ScheduledRequestsConfig
from ..utils.exceptions import UserQuotaExceededError, ResourceExhaustionError
from ..utils.helpers import ThreadSafeCounter, MemoryTracker
from userprofiles.ZairaUser import PERMISSION_LEVELS

@dataclass
class QuotaUsage:
    """Current quota usage for a user"""
    concurrent_tasks: int = 0
    daily_tasks: int = 0
    memory_usage_mb: float = 0.0
    recurring_tasks: int = 0
    total_tasks_created: int = 0
    last_reset_date: str = ""
    violations: int = 0

class UserQuotaManager:
    """Per-user quota management with real-time tracking"""
    
    def __init__(self, user_guid: str, quota_config: UserQuotaConfig):
        self.user_guid = user_guid
        self.quota_config = quota_config
        
        # Usage tracking
        self._current_usage = QuotaUsage()
        self._last_reset_date = datetime.now(timezone.utc).date()
        
        # Thread-safe counters
        self._concurrent_tasks = ThreadSafeCounter()
        self._daily_tasks = ThreadSafeCounter()
        self._recurring_tasks = ThreadSafeCounter()
        self._total_tasks = ThreadSafeCounter()
        self._violations = ThreadSafeCounter()
        
        # Memory tracking
        self._memory_tracker = MemoryTracker()
        
        # Thread safety
        self._lock = threading.Lock()
        
        # Metrics and history
        self._usage_history: List[Dict[str, Any]] = []
        self._peak_usage = QuotaUsage()
        
        LogFire.log("INIT", f"UserQuotaManager created for user {user_guid}")
    
    async def check_quota_availability(self, operation_type: str, 
                                     estimated_memory_mb: float = 0.0) -> Tuple[bool, Optional[str]]:
        """
        Check if user has quota available for requested operation
        
        Args:
            operation_type: Type of operation (create_task, create_recurring_task)
            estimated_memory_mb: Estimated memory usage for the operation
            
        Returns:
            Tuple[bool, Optional[str]]: (available, violation_reason)
        """
        await self._reset_daily_quota_if_needed()
        
        with self._lock:
            # Check concurrent tasks limit
            if self._concurrent_tasks.get_value() >= self.quota_config.max_concurrent_tasks:
                violation_reason = f"Concurrent tasks limit exceeded: {self._concurrent_tasks.get_value()}/{self.quota_config.max_concurrent_tasks}"
                return False, violation_reason
            
            # Check daily tasks limit
            if self._daily_tasks.get_value() >= self.quota_config.daily_task_limit:
                violation_reason = f"Daily tasks limit exceeded: {self._daily_tasks.get_value()}/{self.quota_config.daily_task_limit}"
                return False, violation_reason
            
            # Check memory limit
            current_memory = self._memory_tracker.get_user_memory(self.user_guid)
            if current_memory + estimated_memory_mb > self.quota_config.memory_limit_mb:
                violation_reason = f"Memory limit would be exceeded: {current_memory + estimated_memory_mb:.1f}/{self.quota_config.memory_limit_mb}MB"
                return False, violation_reason
            
            # Check recurring tasks limit for recurring operations
            if operation_type == "create_recurring_task":
                if self._recurring_tasks.get_value() >= self.quota_config.max_recurring_tasks:
                    violation_reason = f"Recurring tasks limit exceeded: {self._recurring_tasks.get_value()}/{self.quota_config.max_recurring_tasks}"
                    return False, violation_reason
            
            return True, None
    
    async def reserve_quota(self, operation_type: str, estimated_memory_mb: float = 0.0) -> bool:
        """
        Reserve quota for an operation
        
        Args:
            operation_type: Type of operation
            estimated_memory_mb: Estimated memory usage
            
        Returns:
            bool: True if reservation successful
            
        Raises:
            UserQuotaExceededError: If quota is exceeded
        """
        available, violation_reason = await self.check_quota_availability(operation_type, estimated_memory_mb)
        
        if not available:
            # Record violation
            self._violations.increment()
            await self._record_quota_violation(operation_type, violation_reason)
            
            # Determine which quota was exceeded for appropriate error
            if "concurrent" in violation_reason.lower():
                raise UserQuotaExceededError(
                    self.user_guid, "max_concurrent_tasks",
                    self._concurrent_tasks.get_value(), self.quota_config.max_concurrent_tasks
                )
            elif "daily" in violation_reason.lower():
                raise UserQuotaExceededError(
                    self.user_guid, "daily_task_limit",
                    self._daily_tasks.get_value(), self.quota_config.daily_task_limit
                )
            elif "memory" in violation_reason.lower():
                current_memory = self._memory_tracker.get_user_memory(self.user_guid)
                raise UserQuotaExceededError(
                    self.user_guid, "memory_limit_mb",
                    int(current_memory), self.quota_config.memory_limit_mb
                )
            elif "recurring" in violation_reason.lower():
                raise UserQuotaExceededError(
                    self.user_guid, "max_recurring_tasks",
                    self._recurring_tasks.get_value(), self.quota_config.max_recurring_tasks
                )
            else:
                raise ResourceExhaustionError("quota_check", violation_reason)
        
        # Reserve the quota
        with self._lock:
            self._concurrent_tasks.increment()
            self._daily_tasks.increment()
            self._total_tasks.increment()
            
            if operation_type == "create_recurring_task":
                self._recurring_tasks.increment()
            
            if estimated_memory_mb > 0:
                current_memory = self._memory_tracker.get_user_memory(self.user_guid)
                self._memory_tracker.track_user_memory(self.user_guid, current_memory + estimated_memory_mb)
            
            # Update peak usage tracking
            self._update_peak_usage()
            
            LogFire.log("QUOTA", f"Reserved quota for user {self.user_guid}: {operation_type}")
            return True
    
    async def release_quota(self, operation_type: str, actual_memory_mb: float = 0.0):
        """
        Release reserved quota after task completion
        
        Args:
            operation_type: Type of operation that completed
            actual_memory_mb: Actual memory that was used
        """
        with self._lock:
            # Release concurrent task quota
            self._concurrent_tasks.decrement()
            
            # Release recurring task quota if applicable
            if operation_type == "create_recurring_task":
                self._recurring_tasks.decrement()
            
            # Update memory usage
            if actual_memory_mb > 0:
                current_memory = self._memory_tracker.get_user_memory(self.user_guid)
                new_memory = max(0, current_memory - actual_memory_mb)
                self._memory_tracker.track_user_memory(self.user_guid, new_memory)
            
            LogFire.log("QUOTA", f"Released quota for user {self.user_guid}: {operation_type}")
    
    async def get_quota_usage(self) -> QuotaUsage:
        """Get current quota usage"""
        await self._reset_daily_quota_if_needed()
        
        with self._lock:
            return QuotaUsage(
                concurrent_tasks=self._concurrent_tasks.get_value(),
                daily_tasks=self._daily_tasks.get_value(),
                memory_usage_mb=self._memory_tracker.get_user_memory(self.user_guid),
                recurring_tasks=self._recurring_tasks.get_value(),
                total_tasks_created=self._total_tasks.get_value(),
                last_reset_date=self._last_reset_date.isoformat(),
                violations=self._violations.get_value()
            )
    
    async def get_quota_metrics(self) -> Dict[str, Any]:
        """Get detailed quota metrics"""
        usage = await self.get_quota_usage()
        
        # Calculate utilization percentages
        concurrent_utilization = (usage.concurrent_tasks / self.quota_config.max_concurrent_tasks) * 100
        daily_utilization = (usage.daily_tasks / self.quota_config.daily_task_limit) * 100
        memory_utilization = (usage.memory_usage_mb / self.quota_config.memory_limit_mb) * 100
        recurring_utilization = (usage.recurring_tasks / self.quota_config.max_recurring_tasks) * 100
        
        return {
            'user_guid': self.user_guid,
            'quota_config': self.quota_config.dict(),
            'current_usage': usage.__dict__,
            'peak_usage': self._peak_usage.__dict__,
            'utilization_percentages': {
                'concurrent_tasks': concurrent_utilization,
                'daily_tasks': daily_utilization,
                'memory': memory_utilization,
                'recurring_tasks': recurring_utilization
            },
            'quota_health': self._calculate_quota_health(
                concurrent_utilization, daily_utilization, 
                memory_utilization, recurring_utilization
            ),
            'recent_violations': len(self._usage_history)
        }
    
    async def update_quota_config(self, new_config: UserQuotaConfig):
        """Update quota configuration (admin operation)"""
        with self._lock:
            old_config = self.quota_config
            self.quota_config = new_config
            
            LogFire.log("ADMIN", f"Updated quota config for user {self.user_guid}")
            LogFire.log("ADMIN", f"  Concurrent tasks: {old_config.max_concurrent_tasks} -> {new_config.max_concurrent_tasks}")
            LogFire.log("ADMIN", f"  Daily limit: {old_config.daily_task_limit} -> {new_config.daily_task_limit}")
            LogFire.log("ADMIN", f"  Memory limit: {old_config.memory_limit_mb}MB -> {new_config.memory_limit_mb}MB")
    
    async def reset_daily_quota(self, force: bool = False):
        """Reset daily quota counters"""
        with self._lock:
            if force or self._should_reset_daily_quota():
                old_daily_count = self._daily_tasks.get_value()
                self._daily_tasks.reset()
                self._last_reset_date = datetime.now(timezone.utc).date()
                
                LogFire.log("QUOTA", f"Reset daily quota for user {self.user_guid} (was: {old_daily_count})")
    
    async def get_quota_forecast(self, hours_ahead: int = 24) -> Dict[str, Any]:
        """Forecast quota usage based on current trends"""
        usage = await self.get_quota_usage()
        
        # Calculate current rate (tasks per hour)
        current_hour = datetime.now(timezone.utc).hour
        if current_hour > 0:
            hourly_rate = usage.daily_tasks / current_hour
        else:
            hourly_rate = 0
        
        # Forecast future usage
        forecasted_daily = usage.daily_tasks + (hourly_rate * hours_ahead)
        
        # Predict if limits will be exceeded
        will_exceed_daily = forecasted_daily > self.quota_config.daily_task_limit
        
        return {
            'current_hourly_rate': hourly_rate,
            'forecasted_daily_usage': min(forecasted_daily, self.quota_config.daily_task_limit * 2),  # Cap at 2x limit
            'will_exceed_daily_limit': will_exceed_daily,
            'hours_until_daily_limit': self._calculate_hours_until_limit(hourly_rate, usage.daily_tasks),
            'recommended_action': self._get_forecast_recommendation(will_exceed_daily, hourly_rate)
        }
    
    def _calculate_quota_health(self, concurrent_util: float, daily_util: float, 
                              memory_util: float, recurring_util: float) -> str:
        """Calculate overall quota health status"""
        max_util = max(concurrent_util, daily_util, memory_util, recurring_util)
        
        if max_util < 50:
            return "healthy"
        elif max_util < 75:
            return "moderate"
        elif max_util < 90:
            return "high"
        else:
            return "critical"
    
    def _calculate_hours_until_limit(self, hourly_rate: float, current_usage: int) -> Optional[float]:
        """Calculate hours until daily limit is reached"""
        if hourly_rate <= 0:
            return None
        
        remaining_quota = self.quota_config.daily_task_limit - current_usage
        if remaining_quota <= 0:
            return 0
        
        return remaining_quota / hourly_rate
    
    def _get_forecast_recommendation(self, will_exceed: bool, hourly_rate: float) -> str:
        """Get recommendation based on forecast"""
        if will_exceed:
            if hourly_rate > 10:
                return "Consider reducing task creation rate - high usage detected"
            else:
                return "Monitor usage - approaching daily limit"
        else:
            return "Usage within normal limits"
    
    async def _reset_daily_quota_if_needed(self):
        """Reset daily quota if date has changed"""
        if self._should_reset_daily_quota():
            await self.reset_daily_quota()
    
    def _should_reset_daily_quota(self) -> bool:
        """Check if daily quota should be reset"""
        return datetime.now(timezone.utc).date() > self._last_reset_date
    
    def _update_peak_usage(self):
        """Update peak usage tracking"""
        current_concurrent = self._concurrent_tasks.get_value()
        current_daily = self._daily_tasks.get_value()
        current_memory = self._memory_tracker.get_user_memory(self.user_guid)
        current_recurring = self._recurring_tasks.get_value()
        
        self._peak_usage.concurrent_tasks = max(self._peak_usage.concurrent_tasks, current_concurrent)
        self._peak_usage.daily_tasks = max(self._peak_usage.daily_tasks, current_daily)
        self._peak_usage.memory_usage_mb = max(self._peak_usage.memory_usage_mb, current_memory)
        self._peak_usage.recurring_tasks = max(self._peak_usage.recurring_tasks, current_recurring)
    
    async def _record_quota_violation(self, operation_type: str, reason: str):
        """Record quota violation for analysis"""
        violation_record = {
            'timestamp': datetime.now(timezone.utc).isoformat(),
            'operation_type': operation_type,
            'reason': reason,
            'usage_snapshot': (await self.get_quota_usage()).__dict__
        }
        
        # Keep only recent violations (last 100)
        self._usage_history.append(violation_record)
        if len(self._usage_history) > 100:
            self._usage_history = self._usage_history[-100:]
        
        LogFire.log("VIOLATION", f"Quota violation for user {self.user_guid}: {reason}")

class SystemQuotaManager:
    """System-wide quota management and monitoring"""
    
    def __init__(self):
        # User quota managers
        self._user_quota_managers: Dict[str, UserQuotaManager] = {}
        self._managers_lock = threading.Lock()
        
        # System-wide tracking
        self._system_metrics = {
            'total_users': ThreadSafeCounter(),
            'total_concurrent_tasks': ThreadSafeCounter(),
            'total_daily_tasks': ThreadSafeCounter(),
            'total_violations': ThreadSafeCounter(),
            'system_memory_usage': 0.0
        }
        
        # Background tasks
        self._monitoring_task: Optional[asyncio.Task] = None
        self._cleanup_task: Optional[asyncio.Task] = None
        self._shutdown = False
    
    async def setup(self):
        """Initialize system quota manager"""
        if not self._monitoring_task:
            self._monitoring_task = asyncio.create_task(self._system_monitoring())
            self._cleanup_task = asyncio.create_task(self._periodic_cleanup())
    
    async def shutdown(self):
        """Shutdown system quota manager"""
        self._shutdown = True
        
        # Cancel background tasks
        if self._monitoring_task:
            self._monitoring_task.cancel()
            try:
                await self._monitoring_task
            except asyncio.CancelledError:
                pass
        
        if self._cleanup_task:
            self._cleanup_task.cancel()
            try:
                await self._cleanup_task
            except asyncio.CancelledError:
                pass
        
        # Clear managers
        with self._managers_lock:
            self._user_quota_managers.clear()
        
        LogFire.log("INIT", "SystemQuotaManager shutdown complete")
    
    async def get_user_quota_manager(self, user_guid: str, permission_level: PERMISSION_LEVELS) -> UserQuotaManager:
        """Get or create user quota manager"""
        with self._managers_lock:
            if user_guid not in self._user_quota_managers:
                quota_config = ScheduledRequestsConfig.get_user_quota_config(permission_level)
                self._user_quota_managers[user_guid] = UserQuotaManager(user_guid, quota_config)
                self._system_metrics['total_users'].increment()
                LogFire.log("INIT", f"Created quota manager for user {user_guid}")
            
            return self._user_quota_managers[user_guid]
    
    async def remove_user_quota_manager(self, user_guid: str) -> bool:
        """Remove user quota manager"""
        with self._managers_lock:
            if user_guid in self._user_quota_managers:
                del self._user_quota_managers[user_guid]
                LogFire.log("INIT", f"Removed quota manager for user {user_guid}")
                return True
            return False
    
    async def get_system_quota_overview(self) -> Dict[str, Any]:
        """Get system-wide quota overview"""
        with self._managers_lock:
            user_managers = list(self._user_quota_managers.values())
        
        # Aggregate metrics from all user managers
        total_concurrent = 0
        total_daily = 0
        total_memory = 0.0
        total_violations = 0
        user_stats = []
        
        for manager in user_managers:
            try:
                usage = await manager.get_quota_usage()
                metrics = await manager.get_quota_metrics()
                
                total_concurrent += usage.concurrent_tasks
                total_daily += usage.daily_tasks
                total_memory += usage.memory_usage_mb
                total_violations += usage.violations
                
                user_stats.append({
                    'user_guid': manager.user_guid,
                    'health': metrics['quota_health'],
                    'concurrent_utilization': metrics['utilization_percentages']['concurrent_tasks'],
                    'violations': usage.violations
                })
                
            except Exception as e:
                LogFire.log("ERROR", f"Error collecting metrics for user {manager.user_guid}: {str(e)}")
        
        # Sort users by health issues
        user_stats.sort(key=lambda x: (x['health'] == 'critical', x['violations']), reverse=True)
        
        return {
            'system_totals': {
                'active_users': len(user_managers),
                'total_concurrent_tasks': total_concurrent,
                'total_daily_tasks': total_daily,
                'total_memory_usage_mb': total_memory,
                'total_violations': total_violations
            },
            'system_health': self._calculate_system_health(user_stats),
            'top_users_by_usage': user_stats[:10],
            'resource_utilization': await self._get_system_resource_utilization()
        }
    
    async def apply_emergency_quotas(self, reduction_factor: float = 0.5):
        """Apply emergency quota reductions during system stress"""
        with self._managers_lock:
            affected_users = 0
            
            for manager in self._user_quota_managers.values():
                original_config = manager.quota_config
                emergency_config = UserQuotaConfig(
                    max_concurrent_tasks=max(1, int(original_config.max_concurrent_tasks * reduction_factor)),
                    daily_task_limit=max(1, int(original_config.daily_task_limit * reduction_factor)),
                    memory_limit_mb=max(50, int(original_config.memory_limit_mb * reduction_factor)),
                    thread_pool_size=max(1, int(original_config.thread_pool_size * reduction_factor)),
                    max_task_duration_hours=original_config.max_task_duration_hours,
                    max_recurring_tasks=max(1, int(original_config.max_recurring_tasks * reduction_factor))
                )
                
                await manager.update_quota_config(emergency_config)
                affected_users += 1
        
        LogFire.log("WARNING", f"Applied emergency quotas to {affected_users} users (reduction factor: {reduction_factor})")
    
    def _calculate_system_health(self, user_stats: List[Dict[str, Any]]) -> str:
        """Calculate overall system quota health"""
        if not user_stats:
            return "healthy"
        
        critical_users = sum(1 for stat in user_stats if stat['health'] == 'critical')
        high_users = sum(1 for stat in user_stats if stat['health'] == 'high')
        total_users = len(user_stats)
        
        critical_ratio = critical_users / total_users
        high_ratio = (critical_users + high_users) / total_users
        
        if critical_ratio > 0.5:
            return "critical"
        elif critical_ratio > 0.2 or high_ratio > 0.5:
            return "degraded"
        elif high_ratio > 0.3:
            return "moderate"
        else:
            return "healthy"
    
    async def _get_system_resource_utilization(self) -> Dict[str, float]:
        """Get system resource utilization"""
        try:
            import psutil
            
            # System memory
            memory = psutil.virtual_memory()
            cpu_percent = psutil.cpu_percent(interval=1)
            
            # Disk usage for database
            disk = psutil.disk_usage('/')
            
            return {
                'memory_percent': memory.percent,
                'cpu_percent': cpu_percent,
                'disk_percent': (disk.used / disk.total) * 100
            }
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get system resource utilization: {str(e)}")
            return {}
    
    async def _system_monitoring(self):
        """Background system monitoring"""
        while not self._shutdown:
            try:
                await asyncio.sleep(300)  # Every 5 minutes
                
                overview = await self.get_system_quota_overview()
                system_health = overview['system_health']
                
                if system_health in ['degraded', 'critical']:
                    LogFire.log("WARNING", f"System quota health: {system_health}")
                    
                    # Consider emergency measures for critical state
                    if system_health == 'critical':
                        LogFire.log("CRITICAL", "System quota in critical state - consider emergency quotas")
                
                # Log periodic summary (reduced frequency)
                if not hasattr(self, '_quota_log_counter'):
                    self._quota_log_counter = 0
                self._quota_log_counter += 1
                
                # Only log every 10 monitoring cycles to reduce spam
                if self._quota_log_counter >= 10:
                    totals = overview['system_totals']
                    LogFire.log("METRICS", f"Quota Summary - Users: {totals['active_users']}, "
                              f"Concurrent: {totals['total_concurrent_tasks']}, "
                              f"Daily: {totals['total_daily_tasks']}, "
                              f"Memory: {totals['total_memory_usage_mb']:.1f}MB")
                    self._quota_log_counter = 0
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                LogFire.log("ERROR", f"Error in quota monitoring: {str(e)}")
    
    async def _periodic_cleanup(self):
        """Periodic cleanup of inactive quota managers"""
        while not self._shutdown:
            try:
                await asyncio.sleep(1800)  # Every 30 minutes
                
                with self._managers_lock:
                    inactive_users = []
                    
                    for user_guid, manager in self._user_quota_managers.items():
                        usage = await manager.get_quota_usage()
                        
                        # Remove managers for users with no activity
                        if (usage.concurrent_tasks == 0 and 
                            usage.daily_tasks == 0 and 
                            usage.memory_usage_mb == 0):
                            inactive_users.append(user_guid)
                    
                    # Remove inactive managers
                    for user_guid in inactive_users:
                        del self._user_quota_managers[user_guid]
                    
                    if inactive_users:
                        LogFire.log("INIT", f"Cleaned up {len(inactive_users)} inactive quota managers")
                
            except asyncio.CancelledError:
                break
            except Exception as e:
                LogFire.log("ERROR", f"Error in quota cleanup: {str(e)}")