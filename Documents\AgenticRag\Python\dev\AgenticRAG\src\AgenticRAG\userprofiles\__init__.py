# Import all userprofiles classes
from .ZairaMessage import ZairaMessage
from .ZairaChat import ZairaChat
from .ZairaUser import ZairaUser
from .LongRunningZairaRequest import LongRunningZairaRequest
from .ScheduledZairaRequest import ScheduledZairaRequest

# Import processing data classes to make them available during model rebuild
try:
    from tasks.data.processing_output_data import ProcessingOutputDataBase
    # Make the class available in the global namespace for model rebuilding
    globals()['ProcessingOutputDataBase'] = ProcessingOutputDataBase
except ImportError:
    # Handle case where tasks.data may not be available yet
    pass

# Rebuild models after all classes are imported to handle circular references
ZairaChat.model_rebuild()
ZairaUser.model_rebuild()
LongRunningZairaRequest.model_rebuild()
ScheduledZairaRequest.model_rebuild()

__all__ = [
    'ZairaMessage',
    'ZairaChat', 
    'ZairaUser',
    'LongRunningZairaRequest',
    'ScheduledZairaRequest'
]