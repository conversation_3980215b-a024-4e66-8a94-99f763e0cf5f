=== TEST DEBUG TRACE STARTED ===
Test Function: test_real_email
Started: 2025-08-19 09:18:18.685394
================================================================================

[2025-08-19 09:18:18.686] Test debug trace capture activated - all console output will be logged to tests\test_real\logs\test_real_email\20250819_091818.log
[2025-08-19 09:18:20.724] [TEST_DEBUG] TestRealEmail: Changing directory from C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG to C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG
[2025-08-19 09:18:20.733] [TEST_DEBUG] TestRealEmail: test_real getcwd(): C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG
[2025-08-19 09:18:20.733] [TEST_DEBUG] TestRealEmail: test_real looking for dev.env at: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG/dev.env
[2025-08-19 09:18:20.733] [TEST_DEBUG] TestRealEmail: test_real dev.env exists: True
[2025-08-19 09:18:20.795] [MAIN_DEBUG] Globals.is_docker() = False
[2025-08-19 09:18:20.795] [MAIN_DEBUG] main.py getcwd(): C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG
[2025-08-19 09:18:20.795] [MAIN_DEBUG] main.py looking for dev.env at: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG/dev.env
[2025-08-19 09:18:20.795] [MAIN_DEBUG] main.py dev.env exists: True
[2025-08-19 09:18:23.157] Database 'vectordb' already exists.
[2025-08-19 09:18:23.358] LogEntries database table created/verified
[2025-08-19 09:18:23.941] [GLOBAL_TEST] 07:18:23.941[INIT], 'setup -> setup': ZairaControl endpoint routes registered successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:23.941407+00:00
[2025-08-19 09:18:24.190] [GLOBAL_TEST] 07:18:24.190[INIT], 'setup -> _create_oauth_table': OAuth database table created/verified .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:24.190458+00:00
[2025-08-19 09:18:24.326] 07:18:24.325 [Python][INIT], '_run_once -> _run': ZairaControl endpoint routes registered successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:24.000401
[2025-08-19 09:18:27.739] [GLOBAL_TEST] 07:18:27.738[INIT], 'init -> setup': ZairaControl endpoint routes registered successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:27.738577+00:00
[2025-08-19 09:18:27.857] 07:18:27.856 [Python][INIT], '_run_once -> _run': [Scrubbed due to 'Auth'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:24.326502
[2025-08-19 09:18:27.861] 07:18:27.860 [Python][INIT], '_run_once -> _run': ZairaControl endpoint routes registered successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:27.857623
[2025-08-19 09:18:27.937] [GLOBAL_TEST] 07:18:27.937[INIT], 'setup -> _create_tables_and_indexes': Database tables and indexes created/verified .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:27.937623+00:00
[2025-08-19 09:18:27.990] [GLOBAL_TEST] 07:18:27.990[INIT], 'mainFunc -> init': Setting up new scheduled request architecture .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:27.990620+00:00
[2025-08-19 09:18:28.060] [GLOBAL_TEST] 07:18:28.060[INIT], 'init -> setup': ScheduledRequestIntegrationAdapter initialized successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.060624+00:00
[2025-08-19 09:18:28.120] [GLOBAL_TEST] 07:18:28.119[INIT], 'mainFunc -> init': New scheduled request system ready .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.119624+00:00
[2025-08-19 09:18:28.176] [GLOBAL_TEST] 07:18:28.176[DEBUG], 'setup -> _create_system_user': Starting SYSTEM user creation process - GUID: 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.176435+00:00
[2025-08-19 09:18:28.233] [GLOBAL_TEST] 07:18:28.233[DEBUG], 'setup -> _create_system_user': Current users in registry: 0 users .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.233485+00:00
[2025-08-19 09:18:28.292] [GLOBAL_TEST] 07:18:28.292[DEBUG], 'setup -> _create_system_user': User registry keys: [] .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.292016+00:00
[2025-08-19 09:18:28.342] [GLOBAL_TEST] 07:18:28.342[DEBUG], 'setup -> _create_system_user': SYSTEM user not found in registry, creating new user .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.342015+00:00
[2025-08-19 09:18:28.391] [GLOBAL_TEST] 07:18:28.391[DEBUG], 'setup -> _create_system_user': Calling ZairaUserManager.add_user for SYSTEM user .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.391016+00:00
[2025-08-19 09:18:28.436] [GLOBAL_TEST] 07:18:28.436[INIT], 'add_user -> __init__': ZairaUser created: SYSTEM .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.436014+00:00
[2025-08-19 09:18:28.488] [GLOBAL_TEST] 07:18:28.488[USER], 'add_user -> __init__': User created with GUID 00000000-0000-0000-0000-000000000001 . Username: SYSTEM, rank: PERMISSION_LEVELS.ADMIN.  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 294522a2-4bd7-4266-8a20-9ffa916cd4cb at 2025-08-19 07:18:28.488014+00:00
[2025-08-19 09:18:28.542] [GLOBAL_TEST] 07:18:28.542[DEBUG], 'setup -> _create_system_user': ZairaUserManager.add_user returned: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.542049+00:00
[2025-08-19 09:18:28.586] [GLOBAL_TEST] 07:18:28.586[DEBUG], 'setup -> _create_system_user': Set SYSTEM user properties - email: <EMAIL>, is_system_user: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.586049+00:00
[2025-08-19 09:18:28.634] [GLOBAL_TEST] 07:18:28.634[DEBUG], 'setup -> _create_system_user': Verification check - SYSTEM GUID in users registry: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.634052+00:00
[2025-08-19 09:18:28.690] [GLOBAL_TEST] 07:18:28.690[DEBUG], 'setup -> _create_system_user': Updated registry size: 1 users .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.690165+00:00
[2025-08-19 09:18:28.740] [GLOBAL_TEST] 07:18:28.740[DEBUG], 'setup -> _create_system_user': SYSTEM user automatically registered in users registry during creation .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.740164+00:00
[2025-08-19 09:18:28.781] [GLOBAL_TEST] 07:18:28.781[USER], 'setup -> _create_system_user': SYSTEM user created successfully 00000000-0000-0000-0000-000000000001.  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.781164+00:00
[2025-08-19 09:18:28.833] [GLOBAL_TEST] 07:18:28.832[DEBUG], 'setup -> _create_system_user': Final verification - can retrieve SYSTEM user from registry: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.832176+00:00
[2025-08-19 09:18:28.879] [GLOBAL_TEST] 07:18:28.879[USER], 'init -> setup': SystemUserManager initialized with SYSTEM user .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.879167+00:00
[2025-08-19 09:18:28.926] [GLOBAL_TEST] 07:18:28.926[EVENT], 'init -> loadEmbedding': Loading stored index .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:28.926164+00:00
[2025-08-19 09:18:31.201] [GLOBAL_TEST] 07:18:31.201[EVENT], 'init -> loadEmbedding': Index loaded .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:31.201179+00:00
[2025-08-19 09:18:31.260] [GLOBAL_TEST] 07:18:31.260[DEBUG], 'create_top_level_supervisor -> create_small_tasks': create_small_tasks called .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:31.260450+00:00
[2025-08-19 09:18:33.055] 07:18:33.054 [Python][INIT], '_run_once -> _run': Database tables and indexes created/verified .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:33.050328
[2025-08-19 09:18:33.314] [GLOBAL_TEST] 07:18:33.314[INIT], 'late_setup -> start_app': Server started at http://0.0.0.0:40999 (registered 1 total servers) .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:33.314718+00:00
[2025-08-19 09:18:33.360] [GLOBAL_TEST] 07:18:33.360[INIT], 'late_setup -> start_app': Server started at http://0.0.0.0:40999 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:33.360277+00:00
[2025-08-19 09:18:33.441] 07:18:33.441 [Python][INIT], '_run_once -> _run': Setting up new scheduled request architecture .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:33.055364
[2025-08-19 09:18:33.456] 07:18:33.455 [Python][INIT], '_run_once -> _run': ScheduledRequestIntegrationAdapter initialized successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:33.441093
[2025-08-19 09:18:33.496] [GLOBAL_TEST] 07:18:33.496[REQUEST], 'late_init -> late_setup': Task c495831e-d3f1-444a-b2ab-b31c9cea4da5 available for recovery .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:33.496466+00:00
[2025-08-19 09:18:33.537] [GLOBAL_TEST] 07:18:33.537[REQUEST], 'late_init -> late_setup': Task 9c35ee38-958c-47a9-962f-8fe7660df928 available for recovery .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:33.537127+00:00
[2025-08-19 09:18:33.580] [GLOBAL_TEST] 07:18:33.580[INIT], '_create_manager -> __init__': UserScheduledRequestManager created for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:33.580159+00:00
[2025-08-19 09:18:33.715] 07:18:33.715 [Python][INIT], '_run_once -> _run': New scheduled request system ready .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:33.456838
[2025-08-19 09:18:33.756] [GLOBAL_TEST] 07:18:33.755[USER], '__init__ -> _create_isolated_session_for_scheduled_request': Created isolated chat session 593110e8-961f-4488-bcf1-7234e2a13229 for scheduled request 61a0ec82-d70e-477c-a5d8-63dcd438fdad .  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:33.755773+00:00
[2025-08-19 09:18:33.803] [GLOBAL_TEST] 07:18:33.803[INIT], '__init__ -> __init__': New request started with question of length: 74. Question: Scheduled request: IMAP IDLE Email Monitoring - Recurring every 30 minutes.  Metadata: {"chat length#":1}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:33.803808+00:00
[2025-08-19 09:18:33.853] [GLOBAL_TEST] 07:18:33.853[TASK], '_load_user_requests -> __init__': Using provided schedule parameters .  Metadata: {"chat length#":2}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:33.853911+00:00
[2025-08-19 09:18:33.902] [GLOBAL_TEST] 07:18:33.901[EVENT], 'create_scheduled_request -> add_request': Added request c495831e-d3f1-444a-b2ab-b31c9cea4da5 to user SYSTEM. Total requests: 1 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:33.901551+00:00
[2025-08-19 09:18:33.910] [GLOBAL_TEST] 07:18:33.910[REQUEST], 'run_request -> run_scheduled_request': Executing startup request immediately: Start IMAP IDLE email monitoring session .  Metadata: {"chat length#":3}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:33.910519+00:00
[2025-08-19 09:18:33.915] [GLOBAL_TEST] 07:18:33.915[INIT], 'run_scheduled_request -> __init__': New request started with question of length: 40. Question: Start IMAP IDLE email monitoring session.  Metadata: {"chat length#":4}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:33.915519+00:00
[2025-08-19 09:18:33.920] [GLOBAL_TEST] 07:18:33.919[REQUEST], 'run_request -> run_scheduled_request': Executed scheduled request: Start IMAP IDLE email monitoring session .  Metadata: {"chat length#":5}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:33.919517+00:00
[2025-08-19 09:18:33.965] [GLOBAL_TEST] 07:18:33.965[REQUEST], 'create_scheduled_request -> _start_request_in_thread': Started thread for scheduled request c495831e-d3f1-444a-b2ab-b31c9cea4da5 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:33.965520+00:00
[2025-08-19 09:18:33.968] [GLOBAL_TEST] 07:18:33.968[REQUEST], 'run_request -> run_scheduled_request': Waiting 1800.0 seconds until next execution at 2025-08-19 07:48:33.920517+00:00 .  Metadata: {"chat length#":6}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:33.968713+00:00
[2025-08-19 09:18:34.049] [GLOBAL_TEST] 07:18:34.049[REQUEST], '_load_user_requests -> create_scheduled_request': Created scheduled request c495831e-d3f1-444a-b2ab-b31c9cea4da5 for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:34.049813+00:00
[2025-08-19 09:18:34.134] [GLOBAL_TEST] 07:18:34.134[REQUEST], '_initialize -> _load_user_requests': Successfully recovered task c495831e-d3f1-444a-b2ab-b31c9cea4da5 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:34.134488+00:00
[2025-08-19 09:18:34.137] [GLOBAL_TEST] 07:18:34.137[DEBUG], '_run -> monitor_and_capture_call_trace': Starting call trace monitoring for execution request (max 15 attempts) .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:34.137484+00:00
[2025-08-19 09:18:34.200] [GLOBAL_TEST] 07:18:34.200[USER], '__init__ -> _create_isolated_session_for_scheduled_request': Created isolated chat session f07baf09-a775-4619-b70e-896b369ac9b9 for scheduled request 4d1447cd-cf04-4552-9491-6f14e890fcd5 .  Metadata: {"chat length#":0}. User 00000000-0000-0000-0000-000000000001 on session f07baf09-a775-4619-b70e-896b369ac9b9 at 2025-08-19 07:18:34.200308+00:00
[2025-08-19 09:18:34.206] [GLOBAL_TEST] 07:18:34.206[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: c495831e-d3f1-444a-b2ab-b31c9cea4da5]  Start IMAP IDLE email monitoring session.  Metadata: {"chat length#":7}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:34.206272+00:00
[2025-08-19 09:18:34.211] [GLOBAL_TEST] 07:18:34.211[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: initial [scheduled_guid: c495831e-d3f1-444a-b2ab-b31c9cea4da5] .  Metadata: {"chat length#":8}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:34.211271+00:00
[2025-08-19 09:18:34.217] [GLOBAL_TEST] 07:18:34.216[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) performing initial reasoning to choose task .  Metadata: {"chat length#":9}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:34.216271+00:00
[2025-08-19 09:18:34.273] [GLOBAL_TEST] 07:18:34.273[INIT], '__init__ -> __init__': New request started with question of length: 65. Question: Scheduled request: Weekly log report generation at 9am on Mondays.  Metadata: {"chat length#":1}. User 00000000-0000-0000-0000-000000000001 on session f07baf09-a775-4619-b70e-896b369ac9b9 at 2025-08-19 07:18:34.273679+00:00
[2025-08-19 09:18:34.286] [GLOBAL_TEST] 07:18:34.286[DEBUG], 'llm_call_router -> _perform_cot_reasoning': top_level_supervisor performing CoT reasoning with original_input: Start IMAP IDLE email monitoring session [scheduled_guid: c495831e-d3f1-444a-b2ab-b31c9cea4da5] .  Metadata: {"chat length#":10}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:34.286073+00:00
[2025-08-19 09:18:34.292] [GLOBAL_TEST] 07:18:34.292[DEBUG], 'llm_call_router -> _perform_cot_reasoning': [CoT REASONING] top_level_supervisor starting chain of thought for task selection... .  Metadata: {"chat length#":11}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:34.292074+00:00
[2025-08-19 09:18:34.336] [GLOBAL_TEST] 07:18:34.336[TASK], '_load_user_requests -> __init__': Using provided schedule parameters .  Metadata: {"chat length#":2}. User 00000000-0000-0000-0000-000000000001 on session f07baf09-a775-4619-b70e-896b369ac9b9 at 2025-08-19 07:18:34.336073+00:00
[2025-08-19 09:18:34.397] [GLOBAL_TEST] 07:18:34.393[EVENT], 'create_scheduled_request -> add_request': Added request 9c35ee38-958c-47a9-962f-8fe7660df928 to user SYSTEM. Total requests: 2 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:34.393074+00:00
[2025-08-19 09:18:34.415] [GLOBAL_TEST] 07:18:34.414[REQUEST], 'run_request -> run_scheduled_request': Waiting 510085.598921 seconds until next execution at 2025-08-25 05:00:00+00:00 .  Metadata: {"chat length#":3}. User 00000000-0000-0000-0000-000000000001 on session f07baf09-a775-4619-b70e-896b369ac9b9 at 2025-08-19 07:18:34.414075+00:00
[2025-08-19 09:18:34.518] [GLOBAL_TEST] 07:18:34.518[REQUEST], 'create_scheduled_request -> _start_request_in_thread': Started thread for scheduled request 9c35ee38-958c-47a9-962f-8fe7660df928 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:34.518096+00:00
[2025-08-19 09:18:34.629] [GLOBAL_TEST] 07:18:34.629[REQUEST], '_load_user_requests -> create_scheduled_request': Created scheduled request 9c35ee38-958c-47a9-962f-8fe7660df928 for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:34.629070+00:00
[2025-08-19 09:18:34.718] [GLOBAL_TEST] 07:18:34.717[REQUEST], '_initialize -> _load_user_requests': Successfully recovered task 9c35ee38-958c-47a9-962f-8fe7660df928 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:34.717701+00:00
[2025-08-19 09:18:34.769] [GLOBAL_TEST] 07:18:34.769[REQUEST], '_initialize -> _load_user_requests': Loaded 2 requests for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:34.769722+00:00
[2025-08-19 09:18:34.874] [GLOBAL_TEST] 07:18:34.874[INIT], 'setup -> _initialize': User manager initialized with 2 active requests .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:34.874078+00:00
[2025-08-19 09:18:34.881] [GLOBAL_TEST] 07:18:34.881[REQUEST], '_save_to_persistence -> save_task': Saved scheduled request c495831e-d3f1-444a-b2ab-b31c9cea4da5 for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:34.881808+00:00
[2025-08-19 09:18:34.924] [GLOBAL_TEST] 07:18:34.924[INIT], '_create_manager -> setup': Manager initialized for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:34.924835+00:00
[2025-08-19 09:18:34.988] [GLOBAL_TEST] 07:18:34.988[INIT], 'get_user_manager -> _create_manager': Created user manager for 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:34.988827+00:00
[2025-08-19 09:18:35.049] [GLOBAL_TEST] 07:18:35.049[REQUEST], 'late_init -> late_setup': User manager created for 00000000-0000-0000-0000-000000000001 to recover 2 tasks .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:35.049445+00:00
[2025-08-19 09:18:35.102] [GLOBAL_TEST] 07:18:35.102[REQUEST], 'late_init -> late_setup': Task recovery completed: 2 tasks available for 1 users .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:35.102444+00:00
[2025-08-19 09:18:35.155] [GLOBAL_TEST] 07:18:35.154[USER], 'late_setup -> _create_environment_tasks': Environment detection: Claude=False, Docker=False, Debug=False .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:35.154443+00:00
[2025-08-19 09:18:35.209] [GLOBAL_TEST] 07:18:35.209[DEBUG], '_create_weekly_log_report_task -> get_system_user': get_system_user called - looking for GUID: 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:35.209466+00:00
[2025-08-19 09:18:35.266] [GLOBAL_TEST] 07:18:35.266[DEBUG], '_create_weekly_log_report_task -> get_system_user': Current users registry size: 1 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:35.266442+00:00
[2025-08-19 09:18:35.319] [GLOBAL_TEST] 07:18:35.319[DEBUG], '_create_weekly_log_report_task -> get_system_user': SYSTEM user found in registry - username: SYSTEM, is_system_user: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:35.319476+00:00
[2025-08-19 09:18:36.182] [GLOBAL_TEST] 07:18:36.182[DEBUG], '_run -> monitor_and_capture_call_trace': Execution request status check 1/15: status=running, call_trace=0 entries .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:36.182321+00:00
[2025-08-19 09:18:36.498] [GLOBAL_TEST] 07:18:36.498[USER], '_create_environment_tasks -> _create_weekly_log_report_task': Weekly log report task already exists, skipping creation .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:36.498358+00:00
[2025-08-19 09:18:36.543] [GLOBAL_TEST] 07:18:36.543[USER], 'late_setup -> _create_environment_tasks': Weekly log report task created for SYSTEM user .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:36.543913+00:00
[2025-08-19 09:18:36.602] [GLOBAL_TEST] 07:18:36.602[USER], 'late_init -> late_setup': Environment-specific scheduled tasks created .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:36.602130+00:00
[2025-08-19 09:18:36.655] [GLOBAL_TEST] 07:18:36.654[INIT], 'init -> late_init': Setup has completed. .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:36.654127+00:00
[2025-08-19 09:18:36.705] [GLOBAL_TEST] 07:18:36.705[INIT], 'setup_real_system -> mainFunc': init() completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:36.705334+00:00
[2025-08-19 09:18:37.747] [GLOBAL_TEST] 07:18:37.747[DEBUG], 'llm_call_router -> _perform_cot_reasoning': [CoT THINKING] top_level_supervisor task selection reasoning:
[2025-08-19 09:18:37.747] ------------------------------------------------------------
[2025-08-19 09:18:37.747] 1. **UNDERSTAND**: The user's request is to start an IMAP IDLE email monitoring session.

[2025-08-19 09:18:37.747] 2. **EVALUATE**: No tasks have been called yet, so this is the first action required to fulfill the user's request.

[2025-08-19 09:18:37.747] 3. **IDENTIFY**: The task to start the IMAP IDLE email monitoring session needs to be executed.

[2025-08-19 09:18:37.747] 4. **PRIORITIZE**: The most valuable task to call next is the IMAP IDLE activation task.

[2025-08-19 09:18:37.747] 5. **DECIDE**: I will call the imap_idle_activate task to start the email monitoring session.

[2025-08-19 09:18:37.747] Now, I will execute the imap_idle_activate task.
[2025-08-19 09:18:37.747] ------------------------------------------------------------ .  Metadata: {"chat length#":12}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:37.747112+00:00
[2025-08-19 09:18:39.112] [GLOBAL_TEST] 07:18:39.112[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) chose task: imap_idle_activate [scheduled_guid: c495831e-d3f1-444a-b2ab-b31c9cea4da5] .  Metadata: {"chat length#":14}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:39.112684+00:00
[2025-08-19 09:18:39.117] [GLOBAL_TEST] 07:18:39.117[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) executing next always_call_FIRST task: quick_rag_task (no re-reasoning) .  Metadata: {"chat length#":15}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:39.117686+00:00
[2025-08-19 09:18:39.130] [GLOBAL_TEST] 07:18:39.130[TASK], 'ainvoke -> llm_call_wrapper': Task quick_rag_task [scheduled_guid: c495831e-d3f1-444a-b2ab-b31c9cea4da5]  Start IMAP IDLE email monitoring session'.  Metadata: {"chat length#":16}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:39.130688+00:00
[2025-08-19 09:18:39.133] [GLOBAL_TEST] 07:18:39.133[DEBUG], 'llm_call_wrapper -> llm_call': Starting retrieve_data for: Start IMAP IDLE email monitoring session .  Metadata: {"chat length#":17}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:39.133687+00:00
[2025-08-19 09:18:39.136] [GLOBAL_TEST] 07:18:39.136[DEBUG], 'llm_call_wrapper -> llm_call': About to call query_engine.aquery... .  Metadata: {"chat length#":18}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:39.136685+00:00
[2025-08-19 09:18:42.113] [GLOBAL_TEST] 07:18:42.113[DEBUG], 'llm_call_wrapper -> llm_call': Query completed, response: Start IMAP IDLE email monitoring session .  Metadata: {"chat length#":1}. User 00000000-0000-0000-0000-000000000001 on session 294522a2-4bd7-4266-8a20-9ffa916cd4cb at 2025-08-19 07:18:42.113089+00:00
[2025-08-19 09:18:42.116] [GLOBAL_TEST] 07:18:42.116[TASK], 'llm_call_wrapper -> llm_call':  RAGed: Start IMAP IDLE email monitoring session.  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:42.116087+00:00
[2025-08-19 09:18:42.128] [GLOBAL_TEST] 07:18:42.128[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: c495831e-d3f1-444a-b2ab-b31c9cea4da5]  Start IMAP IDLE email monitoring session.  Metadata: {"chat length#":20}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.128084+00:00
[2025-08-19 09:18:42.132] [GLOBAL_TEST] 07:18:42.131[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: always_first [scheduled_guid: c495831e-d3f1-444a-b2ab-b31c9cea4da5] .  Metadata: {"chat length#":21}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.131084+00:00
[2025-08-19 09:18:42.137] [GLOBAL_TEST] 07:18:42.136[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) executing next always_call_FIRST task: quick_llm_task (no re-reasoning) .  Metadata: {"chat length#":22}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.136087+00:00
[2025-08-19 09:18:42.150] [GLOBAL_TEST] 07:18:42.149[TASK], 'ainvoke -> llm_call_wrapper': Task quick_llm_task [scheduled_guid: c495831e-d3f1-444a-b2ab-b31c9cea4da5]  Start IMAP IDLE email monitoring session'.  Metadata: {"chat length#":23}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.149880+00:00
[2025-08-19 09:18:42.711] [GLOBAL_TEST] 07:18:42.710[TASK], 'llm_call_wrapper -> llm_call':  LLMed: Start IMAP IDLE email monitoring session.  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:42.710496+00:00
[2025-08-19 09:18:42.717] [GLOBAL_TEST] 07:18:42.717[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: c495831e-d3f1-444a-b2ab-b31c9cea4da5]  Start IMAP IDLE email monitoring session.  Metadata: {"chat length#":25}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.717496+00:00
[2025-08-19 09:18:42.720] [GLOBAL_TEST] 07:18:42.720[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: always_first [scheduled_guid: c495831e-d3f1-444a-b2ab-b31c9cea4da5] .  Metadata: {"chat length#":26}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.720496+00:00
[2025-08-19 09:18:42.722] [GLOBAL_TEST] 07:18:42.722[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) executing next always_call_FIRST task: quick_complexity_task (no re-reasoning) .  Metadata: {"chat length#":27}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.722496+00:00
[2025-08-19 09:18:42.739] [GLOBAL_TEST] 07:18:42.739[TASK], 'ainvoke -> llm_call_wrapper': Task quick_complexity_task [scheduled_guid: c495831e-d3f1-444a-b2ab-b31c9cea4da5]  Start IMAP IDLE email monitoring session'.  Metadata: {"chat length#":28}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.739497+00:00
[2025-08-19 09:18:42.742] [GLOBAL_TEST] 07:18:42.742[TASK], 'llm_call_wrapper -> llm_call':  Compl: 1.  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:42.742496+00:00
[2025-08-19 09:18:42.749] [GLOBAL_TEST] 07:18:42.749[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: c495831e-d3f1-444a-b2ab-b31c9cea4da5]  Start IMAP IDLE email monitoring session.  Metadata: {"chat length#":30}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.749531+00:00
[2025-08-19 09:18:42.753] [GLOBAL_TEST] 07:18:42.753[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: always_first [scheduled_guid: c495831e-d3f1-444a-b2ab-b31c9cea4da5] .  Metadata: {"chat length#":31}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.753542+00:00
[2025-08-19 09:18:42.757] [GLOBAL_TEST] 07:18:42.757[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) executing chosen task: imap_idle_activate .  Metadata: {"chat length#":32}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.757536+00:00
[2025-08-19 09:18:42.767] [GLOBAL_TEST] 07:18:42.767[TASK], 'ainvoke -> llm_call_wrapper': Task imap_idle_activate [scheduled_guid: c495831e-d3f1-444a-b2ab-b31c9cea4da5]  Start IMAP IDLE email monitoring session'.  Metadata: {"chat length#":33}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.767496+00:00
[2025-08-19 09:18:42.771] [GLOBAL_TEST] 07:18:42.771[IMAP IDLE], 'llm_call_wrapper -> llm_call': Task called with state: user_guid=00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":34}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.771500+00:00
[2025-08-19 09:18:42.774] [GLOBAL_TEST] 07:18:42.774[IMAP IDLE], 'llm_call_wrapper -> llm_call': Found user: True .  Metadata: {"chat length#":35}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.774531+00:00
[2025-08-19 09:18:42.777] [GLOBAL_TEST] 07:18:42.777[IMAP IDLE], 'llm_call_wrapper -> llm_call': Starting task for user: 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":36}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.777533+00:00
[2025-08-19 09:18:42.780] [GLOBAL_TEST] 07:18:42.780[DEBUG], 'llm_call_wrapper -> llm_call': DIRECT: Updated execution task c495831e-d3f1-444a-b2ab-b31c9cea4da5 with 10 call traces .  Metadata: {"chat length#":37}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.780536+00:00
[2025-08-19 09:18:42.783] [GLOBAL_TEST] 07:18:42.783[IMAP IDLE], 'llm_call -> _run_30_minute_session': Starting 30-minute session for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":38}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:42.783533+00:00
[2025-08-19 09:18:42.848] [GLOBAL_TEST] 07:18:42.848[IMAP CONFIG], '_run_30_minute_session -> _get_imap_config': Server: mail.askzaira.com:143, Email: <EMAIL>, SSL: False .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:42.848917+00:00
[2025-08-19 09:18:43.180] [GLOBAL_TEST] 07:18:43.180[IMAP CONNECTION], '_run_with_context -> _sync_establish_imap_connection': Successfully established IMAP connection .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:43.180899+00:00
[2025-08-19 09:18:43.189] [GLOBAL_TEST] 07:18:43.189[IMAP IDLE], 'llm_call -> _run_30_minute_session': Starting IDLE mode (duration: 29.0 minutes) .  Metadata: {"chat length#":39}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:43.189909+00:00
[2025-08-19 09:18:43.192] [GLOBAL_TEST] 07:18:43.192[IMAP IDLE], 'llm_call -> _run_30_minute_session': Attempting IDLE mode .  Metadata: {"chat length#":40}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:43.192932+00:00
[2025-08-19 09:18:43.195] [GLOBAL_TEST] 07:18:43.195[IMAP IDLE], '_attempt_idle_mode -> _run_idle_cycle': Starting IDLE cycle for 300 seconds .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:43.195934+00:00
[2025-08-19 09:18:43.250] [GLOBAL_TEST] 07:18:43.250[IMAP IDLE], '_attempt_idle_mode -> _run_idle_cycle': IDLE started: + IDLE accepted, awaiting DONE command. .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:43.250083+00:00
[2025-08-19 09:18:45.524] [GLOBAL_TEST] 07:18:45.524[EVENT], 'setup_real_system -> mainFunc': The data is secured using industry-standard encryption and strict access controls, ensuring its safety and confidentiality. It is processed entirely within a secure environment without involvement from external parties, which minimizes the risk of data breaches. The system allows for continuous synchronization with connected data sources, providing real-time access to up-to-date information. Furthermore, there are no limitations on the volume of data that can be stored or processed, accommodating various data scales without incurring additional storage costs. .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:45.524734+00:00
[2025-08-19 09:18:45.578] [GLOBAL_TEST] 07:18:45.578[DEBUG], 'mainFunc -> test_main_loop': [TestRealEmail] Production initialization complete - setting up test environment .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:45.578731+00:00
[2025-08-19 09:18:45.626] [GLOBAL_TEST] 07:18:45.626[INIT], 'add_user -> __init__': ZairaUser created: TestUser .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:45.626766+00:00
[2025-08-19 09:18:45.694] [GLOBAL_TEST] 07:18:45.694[USER], 'add_user -> __init__': User created with GUID 703af770-18d0-4382-af97-96b3d0934291 . Username: TestUser, rank: PERMISSION_LEVELS.ADMIN.  Metadata: {"chat length#":0}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:18:45.694735+00:00
[2025-08-19 09:18:45.793] [GLOBAL_TEST] 07:18:45.793[DEBUG], 'mainFunc -> test_main_loop': [TestRealEmail] Test environment ready - TestUser and Testing bot created .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:45.793795+00:00
[2025-08-19 09:18:45.922] [GLOBAL_TEST] 07:18:45.922[DEBUG], 'test_real_email -> setup_real_system': [TestRealEmail] Production mainFunc() completed successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:45.922797+00:00
[2025-08-19 09:18:46.008] [GLOBAL_TEST] 07:18:46.007[DEBUG], 'setup_real_system -> setup_signal_handlers': [TestRealEmail] Signal handlers registered for graceful shutdown .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.007782+00:00
[2025-08-19 09:18:46.088] [GLOBAL_TEST] 07:18:46.088[DEBUG], 'setup_real_system -> _track_started_servers': [TestRealEmail] Verified 1 servers in global registry: .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.088788+00:00
[2025-08-19 09:18:46.160] [GLOBAL_TEST] 07:18:46.160[DEBUG], 'setup_real_system -> _track_started_servers': [TestRealEmail] - APIEndpoint_0.0.0.0_40999 at 0.0.0.0:40999 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.160782+00:00
[2025-08-19 09:18:46.215] [GLOBAL_TEST] 07:18:46.215[DEBUG], 'test_real_email -> setup_real_system': [TestRealEmail] Restoring directory to C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.215768+00:00
[2025-08-19 09:18:46.277] [GLOBAL_TEST] 07:18:46.277[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: Setup result = True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.277768+00:00
[2025-08-19 09:18:46.278] 07:18:46.277[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: Setup result = True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.277768+00:00
[2025-08-19 09:18:46.335] [GLOBAL_TEST] 07:18:46.335[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: [OK] Production system initialized successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.335776+00:00
[2025-08-19 09:18:46.336] 07:18:46.335[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: [OK] Production system initialized successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.335776+00:00
[2025-08-19 09:18:46.390] [GLOBAL_TEST] 07:18:46.390[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: Getting production test user and bot... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.390774+00:00
[2025-08-19 09:18:46.390] 07:18:46.390[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: Getting production test user and bot... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.390774+00:00
[2025-08-19 09:18:46.435] [GLOBAL_TEST] 07:18:46.435[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: [OK] User: TestUser, Bot: Testing, Email: <EMAIL> .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.435833+00:00
[2025-08-19 09:18:46.436] 07:18:46.435[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: [OK] User: TestUser, Bot: Testing, Email: <EMAIL> .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.435833+00:00
[2025-08-19 09:18:46.481] [GLOBAL_TEST] 07:18:46.480[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: Importing required managers... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.480802+00:00
[2025-08-19 09:18:46.481] 07:18:46.480[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: Importing required managers... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.480802+00:00
[2025-08-19 09:18:46.526] [GLOBAL_TEST] 07:18:46.526[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: [OK] Managers imported successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.526802+00:00
[2025-08-19 09:18:46.526] 07:18:46.526[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: [OK] Managers imported successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.526802+00:00
[2025-08-19 09:18:46.570] [GLOBAL_TEST] 07:18:46.570[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: [OK] Test config: Subject='Test Email a6bb7510', Start=2025-08-19 09:18:46.526802 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.570803+00:00
[2025-08-19 09:18:46.571] 07:18:46.570[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] PHASE 0: [OK] Test config: Subject='Test Email a6bb7510', Start=2025-08-19 09:18:46.526802 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.570803+00:00
[2025-08-19 09:18:46.611] [GLOBAL_TEST] 07:18:46.611[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ===== STEP 1: IMAP IDLE VERIFICATION ===== .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.611802+00:00
[2025-08-19 09:18:46.612] 07:18:46.611[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ===== STEP 1: IMAP IDLE VERIFICATION ===== .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.611802+00:00
[2025-08-19 09:18:46.677] [GLOBAL_TEST] 07:18:46.677[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Getting SystemUserManager instance... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.677939+00:00
[2025-08-19 09:18:46.679] 07:18:46.677[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Getting SystemUserManager instance... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.677939+00:00
[2025-08-19 09:18:46.751] [GLOBAL_TEST] 07:18:46.751[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: [OK] SystemUserManager obtained .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.751991+00:00
[2025-08-19 09:18:46.752] 07:18:46.751[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: [OK] SystemUserManager obtained .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.751991+00:00
[2025-08-19 09:18:46.805] [GLOBAL_TEST] 07:18:46.804[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Getting SYSTEM user... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.804937+00:00
[2025-08-19 09:18:46.805] 07:18:46.804[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Getting SYSTEM user... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.804937+00:00
[2025-08-19 09:18:46.880] [GLOBAL_TEST] 07:18:46.880[DEBUG], 'test_real_email -> get_system_user': get_system_user called - looking for GUID: 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.880932+00:00
[2025-08-19 09:18:46.881] 07:18:46.880[DEBUG], 'test_real_email -> get_system_user': get_system_user called - looking for GUID: 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.880932+00:00
[2025-08-19 09:18:46.947] [GLOBAL_TEST] 07:18:46.947[DEBUG], 'test_real_email -> get_system_user': Current users registry size: 2 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.947935+00:00
[2025-08-19 09:18:46.948] 07:18:46.947[DEBUG], 'test_real_email -> get_system_user': Current users registry size: 2 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:46.947935+00:00
[2025-08-19 09:18:47.002] [GLOBAL_TEST] 07:18:47.002[DEBUG], 'test_real_email -> get_system_user': SYSTEM user found in registry - username: SYSTEM, is_system_user: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:47.002934+00:00
[2025-08-19 09:18:47.003] 07:18:47.002[DEBUG], 'test_real_email -> get_system_user': SYSTEM user found in registry - username: SYSTEM, is_system_user: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:47.002934+00:00
[2025-08-19 09:18:47.043] [GLOBAL_TEST] 07:18:47.043[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: System user result: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:47.043933+00:00
[2025-08-19 09:18:47.044] 07:18:47.043[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: System user result: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:47.043933+00:00
[2025-08-19 09:18:47.090] [GLOBAL_TEST] 07:18:47.090[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: [OK] SYSTEM user found: SYSTEM .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:47.090967+00:00
[2025-08-19 09:18:47.091] 07:18:47.090[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: [OK] SYSTEM user found: SYSTEM .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:47.090967+00:00
[2025-08-19 09:18:47.144] [GLOBAL_TEST] 07:18:47.144[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Getting ScheduledRequestPersistenceManager... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:47.144973+00:00
[2025-08-19 09:18:47.145] 07:18:47.144[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Getting ScheduledRequestPersistenceManager... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:47.144973+00:00
[2025-08-19 09:18:47.196] [GLOBAL_TEST] 07:18:47.196[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: [OK] PersistenceManager obtained .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:47.196910+00:00
[2025-08-19 09:18:47.197] 07:18:47.196[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: [OK] PersistenceManager obtained .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:47.196910+00:00
[2025-08-19 09:18:47.244] [GLOBAL_TEST] 07:18:47.244[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Querying active requests for SYSTEM user GUID: 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:47.244912+00:00
[2025-08-19 09:18:47.245] 07:18:47.244[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Querying active requests for SYSTEM user GUID: 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:47.244912+00:00
[2025-08-19 09:18:48.890] [GLOBAL_TEST] 07:18:48.889[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Found 2 active requests .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:48.889719+00:00
[2025-08-19 09:18:48.890] 07:18:48.889[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Found 2 active requests .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:48.889719+00:00
[2025-08-19 09:18:48.940] [GLOBAL_TEST] 07:18:48.940[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Request 1: target_prompt='Start IMAP IDLE email monitoring session' .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:48.940719+00:00
[2025-08-19 09:18:48.940] 07:18:48.940[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Request 1: target_prompt='Start IMAP IDLE email monitoring session' .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:48.940719+00:00
[2025-08-19 09:18:48.978] [GLOBAL_TEST] 07:18:48.978[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Request 2: target_prompt='Generate a comprehensive weekly report based on database logs from the past 7 days. Include key metrics, error summaries, user activity patterns, and system health indicators. Format the report professionally for team review.' .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:48.978754+00:00
[2025-08-19 09:18:48.979] 07:18:48.978[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Request 2: target_prompt='Generate a comprehensive weekly report based on database logs from the past 7 days. Include key metrics, error summaries, user activity patterns, and system health indicators. Format the report professionally for team review.' .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:48.978754+00:00
[2025-08-19 09:18:49.014] [GLOBAL_TEST] 07:18:49.014[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Checking for IMAP-related requests... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:49.014754+00:00
[2025-08-19 09:18:49.015] 07:18:49.014[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: Checking for IMAP-related requests... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:49.014754+00:00
[2025-08-19 09:18:49.052] [GLOBAL_TEST] 07:18:49.052[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: IMAP task exists: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:49.052778+00:00
[2025-08-19 09:18:49.053] 07:18:49.052[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: IMAP task exists: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:49.052778+00:00
[2025-08-19 09:18:49.095] [GLOBAL_TEST] 07:18:49.095[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: [OK] IMAP idle request exists and is active .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:49.095772+00:00
[2025-08-19 09:18:49.095] 07:18:49.095[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1: [OK] IMAP idle request exists and is active .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:49.095772+00:00
[2025-08-19 09:18:49.136] [GLOBAL_TEST] 07:18:49.136[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ===== STEP 1.5: IMAP ACTIVATION WAIT ===== .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:49.136719+00:00
[2025-08-19 09:18:49.137] 07:18:49.136[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ===== STEP 1.5: IMAP ACTIVATION WAIT ===== .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:49.136719+00:00
[2025-08-19 09:18:49.182] [GLOBAL_TEST] 07:18:49.182[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: About to wait 30 seconds for IMAP to fully activate... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:49.182251+00:00
[2025-08-19 09:18:49.182] 07:18:49.182[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: About to wait 30 seconds for IMAP to fully activate... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:49.182251+00:00
[2025-08-19 09:18:49.225] [GLOBAL_TEST] 07:18:49.225[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: During this wait, background IMAP tasks should be running .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:49.225284+00:00
[2025-08-19 09:18:49.225] 07:18:49.225[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: During this wait, background IMAP tasks should be running .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:49.225284+00:00
[2025-08-19 09:18:49.261] [GLOBAL_TEST] 07:18:49.261[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: Look for 'imap_idle_activate' logs in the output .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:49.261286+00:00
[2025-08-19 09:18:49.261] 07:18:49.261[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: Look for 'imap_idle_activate' logs in the output .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:49.261286+00:00
[2025-08-19 09:18:49.923] 07:18:49.923 [Python][INIT], '_run_once -> _run': ZairaUser created: SYSTEM .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:49.918847
[2025-08-19 09:18:49.928] 07:18:49.927 [Python][USER], '_run_once -> _run': User created with GUID 00000000-0000-0000-0000-000000000001 . Username: SYSTEM, rank: PERMISSION_LEVELS.ADMIN.  Metadata: {"chat length#":2}. User 00000000-0000-0000-0000-000000000001 on session 294522a2-4bd7-4266-8a20-9ffa916cd4cb at 2025-08-19 07:18:49.923841
[2025-08-19 09:18:49.955] 07:18:49.954 [Python][USER], '_run_once -> _run': SYSTEM user created successfully 00000000-0000-0000-0000-000000000001.  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:49.950871
[2025-08-19 09:18:49.962] 07:18:49.962 [Python][USER], '_run_once -> _run': SystemUserManager initialized with SYSTEM user .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:49.959859
[2025-08-19 09:18:49.967] 07:18:49.967 [Python][EVENT], '_run_once -> _run': Loading stored index .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:49.962831
[2025-08-19 09:18:49.971] 07:18:49.970 [Python][EVENT], '_run_once -> _run': Index loaded .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:49.967832
[2025-08-19 09:18:49.978] 07:18:49.978 [Python][INIT], '_run_once -> _run': Server started at http://0.0.0.0:40999 (registered 1 total servers) .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:49.975835
[2025-08-19 09:18:49.984] 07:18:49.983 [Python][INIT], '_run_once -> _run': Server started at http://0.0.0.0:40999 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:49.978838
[2025-08-19 09:18:49.989] 07:18:49.988 [Python][REQUEST], '_run_once -> _run': Task c495831e-d3f1-444a-b2ab-b31c9cea4da5 available for recovery .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:49.985834
[2025-08-19 09:18:49.992] 07:18:49.992 [Python][REQUEST], '_run_once -> _run': Task 9c35ee38-958c-47a9-962f-8fe7660df928 available for recovery .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:49.989859
[2025-08-19 09:18:49.996] 07:18:49.996 [Python][INIT], '_run_once -> _run': UserScheduledRequestManager created for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:49.992832
[2025-08-19 09:18:50.003] 07:18:50.002 [Python][REQUEST], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":41}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:49.996831
[2025-08-19 09:18:50.007] 07:18:50.006 [Python][INIT], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":42}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.003836
[2025-08-19 09:18:50.010] 07:18:50.010 [Python][REQUEST], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":43}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.007858
[2025-08-19 09:18:50.015] 07:18:50.014 [Python][REQUEST], '_run_once -> _run': Waiting 1800.0 seconds until next execution at 2025-08-19 07:48:33.920517+00:00 .  Metadata: {"chat length#":44}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.010832
[2025-08-19 09:18:50.021] 07:18:50.020 [Python][DEBUG], '_run_once -> _run': Starting call trace monitoring for execution request (max 15 attempts) .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.015854
[2025-08-19 09:18:50.025] 07:18:50.025 [Python][TASK], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":45}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.021869
[2025-08-19 09:18:50.051] 07:18:50.050 [Python][REQUEST], '_run_once -> _run': Waiting 510085.598921 seconds until next execution at 2025-08-25 05:00:00+00:00 .  Metadata: {"chat length#":4}. User 00000000-0000-0000-0000-000000000001 on session f07baf09-a775-4619-b70e-896b369ac9b9 at 2025-08-19 07:18:50.044882
[2025-08-19 09:18:50.057] 07:18:50.056 [Python][REQUEST], '_run_once -> _run': Saved scheduled request c495831e-d3f1-444a-b2ab-b31c9cea4da5 for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.051912
[2025-08-19 09:18:50.061] 07:18:50.061 [Python][USER], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":50}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.057895
[2025-08-19 09:18:50.068] 07:18:50.067 [Python][INIT], '_run_once -> _run': New request started with question of length: 74. Question: Sch...quest: IMAP IDLE Email Monitoring - Recurring every 30 minutes.  Metadata: {"chat length#":51}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.061881
[2025-08-19 09:18:50.073] 07:18:50.073 [Python][TASK], '_run_once -> _run': Using provided schedule parameters .  Metadata: {"chat length#":52}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.068897
[2025-08-19 09:18:50.077] 07:18:50.077 [Python][EVENT], '_run_once -> _run': Added request c495831e-d3f1-444a-b2ab-b31c9cea4da5 to user SYSTEM. Total requests: 1 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.073889
[2025-08-19 09:18:50.081] 07:18:50.080 [Python][REQUEST], '_run_once -> _run': Started thread for scheduled request c495831e-d3f1-444a-b2ab-b31c9cea4da5 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.077912
[2025-08-19 09:18:50.086] 07:18:50.086 [Python][REQUEST], '_run_once -> _run': Created scheduled request c495831e-d3f1-444a-b2ab-b31c9cea4da5 for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.081886
[2025-08-19 09:18:50.089] 07:18:50.089 [Python][REQUEST], '_run_once -> _run': Successfully recovered task c495831e-d3f1-444a-b2ab-b31c9cea4da5 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.086916
[2025-08-19 09:18:50.093] 07:18:50.093 [Python][USER], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":5}. User 00000000-0000-0000-0000-000000000001 on session f07baf09-a775-4619-b70e-896b369ac9b9 at 2025-08-19 07:18:50.090906
[2025-08-19 09:18:50.098] 07:18:50.097 [Python][INIT], '_run_once -> _run': New request started with question of length: 65. Question: Scheduled request: Weekly log report generation at 9am on Mondays.  Metadata: {"chat length#":6}. User 00000000-0000-0000-0000-000000000001 on session f07baf09-a775-4619-b70e-896b369ac9b9 at 2025-08-19 07:18:50.094882
[2025-08-19 09:18:50.102] 07:18:50.102 [Python][TASK], '_run_once -> _run': Using provided schedule parameters .  Metadata: {"chat length#":7}. User 00000000-0000-0000-0000-000000000001 on session f07baf09-a775-4619-b70e-896b369ac9b9 at 2025-08-19 07:18:50.098884
[2025-08-19 09:18:50.105] 07:18:50.105 [Python][EVENT], '_run_once -> _run': Added request 9c35ee38-958c-47a9-962f-8fe7660df928 to user SYSTEM. Total requests: 2 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.103880
[2025-08-19 09:18:50.109] 07:18:50.108 [Python][REQUEST], '_run_once -> _run': Started thread for scheduled request 9c35ee38-958c-47a9-962f-8fe7660df928 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.106880
[2025-08-19 09:18:50.113] 07:18:50.112 [Python][REQUEST], '_run_once -> _run': Created scheduled request 9c35ee38-958c-47a9-962f-8fe7660df928 for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.109883
[2025-08-19 09:18:50.119] 07:18:50.119 [Python][REQUEST], '_run_once -> _run': Successfully recovered task 9c35ee38-958c-47a9-962f-8fe7660df928 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.113882
[2025-08-19 09:18:50.124] 07:18:50.123 [Python][REQUEST], '_run_once -> _run': Loaded 2 requests for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.120887
[2025-08-19 09:18:50.127] 07:18:50.127 [Python][INIT], '_run_once -> _run': User manager initialized with 2 active requests .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.124906
[2025-08-19 09:18:50.132] 07:18:50.132 [Python][INIT], '_run_once -> _run': Manager initialized for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.127882
[2025-08-19 09:18:50.137] 07:18:50.137 [Python][INIT], '_run_once -> _run': Created user manager for 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.132883
[2025-08-19 09:18:50.141] 07:18:50.141 [Python][DEBUG], '_run_once -> _run': Execution request status check 1/15: status=running, call_trace=0 entries .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.137888
[2025-08-19 09:18:50.145] 07:18:50.144 [Python][REQUEST], '_run_once -> _run': User manager created for 00000000-0000-0000-0000-000000000001 to recover 2 tasks .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.141883
[2025-08-19 09:18:50.149] 07:18:50.149 [Python][REQUEST], '_run_once -> _run': Task recovery completed: 2 tasks available for 1 users .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.145882
[2025-08-19 09:18:50.154] 07:18:50.154 [Python][USER], '_run_once -> _run': Environment detection: Claude=False, Docker=False, Debug=False .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.150889
[2025-08-19 09:18:50.184] 07:18:50.183 [Python][TASK], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":56}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.178880
[2025-08-19 09:18:50.202] 07:18:50.201 [Python][TASK], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.197884
[2025-08-19 09:18:50.206] 07:18:50.205 [Python][TASK], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":59}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.202902
[2025-08-19 09:18:50.216] 07:18:50.216 [Python][TASK], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":62}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.212881
[2025-08-19 09:18:50.219] 07:18:50.219 [Python][TASK], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.216880
[2025-08-19 09:18:50.224] 07:18:50.223 [Python][TASK], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":63}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.219882
[2025-08-19 09:18:50.238] 07:18:50.237 [Python][TASK], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":66}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.232892
[2025-08-19 09:18:50.241] 07:18:50.241 [Python][TASK], '_run_once -> _run':  Compl: 1.  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.238915
[2025-08-19 09:18:50.245] 07:18:50.245 [Python][TASK], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":67}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.241882
[2025-08-19 09:18:50.310] [GLOBAL_TEST] 07:18:50.310[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: IMAP wait progress: 1/30 seconds elapsed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:50.310884+00:00
[2025-08-19 09:18:50.310] 07:18:50.310[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: IMAP wait progress: 1/30 seconds elapsed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:50.310884+00:00
[2025-08-19 09:18:50.370] 07:18:50.370 [Python][TASK], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":70}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.364883
[2025-08-19 09:18:50.374] 07:18:50.374 [Python][IMAP IDLE], '_run_once -> _run': Task called with state: user_guid=00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":71}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.370883
[2025-08-19 09:18:50.381] 07:18:50.381 [Python][IMAP IDLE], '_run_once -> _run': Found user: True .  Metadata: {"chat length#":72}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.375881
[2025-08-19 09:18:50.387] 07:18:50.387 [Python][IMAP IDLE], '_run_once -> _run': Starting task for user: 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":73}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.381887
[2025-08-19 09:18:50.392] 07:18:50.391 [Python][DEBUG], '_run_once -> _run': DIRECT: Updated execution task c495831e-d3f1-444a-b2ab-b31c9cea4da5 with 10 call traces .  Metadata: {"chat length#":74}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.387916
[2025-08-19 09:18:50.395] 07:18:50.395 [Python][IMAP IDLE], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":75}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:18:50.392918
[2025-08-19 09:18:50.402] 07:18:50.401 [Python][IMAP CONFIG], '_run_once -> _run': Server: mail.askzaira.com:143, Email: <EMAIL>, SSL: False .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.396882
[2025-08-19 09:18:50.406] 07:18:50.405 [Python][IMAP CONNECTION], '_run_once -> _run': Successfully established IMAP connection .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.402887
[2025-08-19 09:18:50.409] 07:18:50.409 [Python][USER], '_run_once -> _run': Weekly log report task already exists, skipping creation .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.406916
[2025-08-19 09:18:50.414] 07:18:50.413 [Python][USER], '_run_once -> _run': Weekly log report task created for SYSTEM user .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.410916
[2025-08-19 09:18:50.420] 07:18:50.419 [Python][USER], '_run_once -> _run': Environment-specific scheduled tasks created .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.414882
[2025-08-19 09:18:50.425] 07:18:50.425 [Python][INIT], '_run_once -> _run': Setup has completed. .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.420931
[2025-08-19 09:18:50.429] 07:18:50.429 [Python][INIT], '_run_once -> _run': init() completed .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.425919
[2025-08-19 09:18:50.439] 07:18:50.438 [Python][EVENT], '_run_once -> _run': The data is secured using industry-standard encryption and str...rious data scales without incurring additional storage costs. .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.429883
[2025-08-19 09:18:50.443] 07:18:50.442 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] Production initialization complete - setting up test environment .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.439918
[2025-08-19 09:18:50.448] 07:18:50.447 [Python][INIT], '_run_once -> _run': ZairaUser created: TestUser .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.443918
[2025-08-19 09:18:50.454] 07:18:50.454 [Python][USER], '_run_once -> _run': User created with GUID 703af770-18d0-4382-af97-96b3d0934291 . Username: TestUser, rank: PERMISSION_LEVELS.ADMIN.  Metadata: {"chat length#":1}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:18:50.448886
[2025-08-19 09:18:50.459] 07:18:50.459 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] Test environment ready - TestUser and Testing bot created .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.454916
[2025-08-19 09:18:50.464] 07:18:50.464 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] Production mainFunc() completed successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.459882
[2025-08-19 09:18:50.471] 07:18:50.470 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] Signal handlers registered for graceful shutdown .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.465888
[2025-08-19 09:18:50.475] 07:18:50.475 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] Verified 1 servers in global registry: .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.471883
[2025-08-19 09:18:50.479] 07:18:50.478 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] - APIEndpoint_0.0.0.0_40999 at 0.0.0.0:40999 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.475881
[2025-08-19 09:18:50.485] 07:18:50.485 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] Restoring directory to C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.479887
[2025-08-19 09:18:50.491] 07:18:50.491 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] PHASE 0: Setup result = True .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.485882
[2025-08-19 09:18:50.495] 07:18:50.495 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] PHASE 0: [OK] Production system initialized successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.491889
[2025-08-19 09:18:50.501] 07:18:50.501 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] PHASE 0: Getting production test user and bot... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.495884
[2025-08-19 09:18:50.505] 07:18:50.505 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] PHASE 0: [OK] User: TestUser, Bot: Testing, Email: <EMAIL> .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.501924
[2025-08-19 09:18:50.509] 07:18:50.509 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] PHASE 0: Importing required managers... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.505908
[2025-08-19 09:18:50.513] 07:18:50.513 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] PHASE 0: [OK] Managers imported successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.509908
[2025-08-19 09:18:50.518] 07:18:50.518 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] PHASE 0: [OK] Test config: Subject='Test Email a6bb7510', Start=2025-08-19 09:18:46.526802 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.513917
[2025-08-19 09:18:50.521] 07:18:50.521 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] ===== STEP 1: IMAP IDLE VERIFICATION ===== .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.518886
[2025-08-19 09:18:50.525] 07:18:50.524 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1: Getting SystemUserManager instance... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.522906
[2025-08-19 09:18:50.528] 07:18:50.527 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1: [OK] SystemUserManager obtained .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.525906
[2025-08-19 09:18:50.531] 07:18:50.531 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1: Getting SYSTEM user... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.528905
[2025-08-19 09:18:50.544] 07:18:50.543 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1: System user result: True .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.540885
[2025-08-19 09:18:50.547] 07:18:50.547 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1: [OK] SYSTEM user found: SYSTEM .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.544884
[2025-08-19 09:18:50.551] 07:18:50.551 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1: Getting ScheduledRequestPersistenceManager... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.547882
[2025-08-19 09:18:50.556] 07:18:50.555 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1: [OK] PersistenceManager obtained .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.552908
[2025-08-19 09:18:50.559] 07:18:50.559 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1: Querying active requests for SYSTEM user GUID: 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.556906
[2025-08-19 09:18:50.564] 07:18:50.563 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1: Found 2 active requests .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.560910
[2025-08-19 09:18:50.570] 07:18:50.569 [Python][DEBUG], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.564883
[2025-08-19 09:18:50.574] 07:18:50.574 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1: Request 2: target_prompt='Generate a c...ndicators. Format the report professionally for team review.' .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.570882
[2025-08-19 09:18:50.578] 07:18:50.578 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1: Checking for IMAP-related requests... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.574880
[2025-08-19 09:18:50.585] 07:18:50.584 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1: IMAP task exists: True .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.578882
[2025-08-19 09:18:50.589] 07:18:50.589 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1: [OK] IMAP idle request exists and is active .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.585890
[2025-08-19 09:18:50.593] 07:18:50.593 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] ===== STEP 1.5: IMAP ACTIVATION WAIT ===== .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.589885
[2025-08-19 09:18:50.597] 07:18:50.597 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1.5: About to wait 30 seconds for IMAP to fully activate... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.593884
[2025-08-19 09:18:50.602] 07:18:50.601 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1.5: During this wait, background IMAP tasks should be running .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.597890
[2025-08-19 09:18:50.606] 07:18:50.605 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1.5: Look for 'imap_idle_activate' logs in the output .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.602885
[2025-08-19 09:18:50.609] 07:18:50.609 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1.5: IMAP wait progress: 1/30 seconds elapsed .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:50.606885
[2025-08-19 09:18:55.421] [GLOBAL_TEST] 07:18:55.421[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: IMAP wait progress: 6/30 seconds elapsed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:55.421415+00:00
[2025-08-19 09:18:55.422] 07:18:55.421[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: IMAP wait progress: 6/30 seconds elapsed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:18:55.421415+00:00
[2025-08-19 09:18:55.507] 07:18:55.507 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1.5: IMAP wait progress: 6/30 seconds elapsed .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:18:55.503416
[2025-08-19 09:19:00.482] [GLOBAL_TEST] 07:19:00.482[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: IMAP wait progress: 11/30 seconds elapsed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:00.482669+00:00
[2025-08-19 09:19:00.483] 07:19:00.482[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: IMAP wait progress: 11/30 seconds elapsed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:00.482669+00:00
[2025-08-19 09:19:00.542] 07:19:00.541 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1.5: IMAP wait progress: 11/30 seconds elapsed .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:00.526692
[2025-08-19 09:19:05.549] [GLOBAL_TEST] 07:19:05.549[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: IMAP wait progress: 16/30 seconds elapsed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:05.549525+00:00
[2025-08-19 09:19:05.549] 07:19:05.549[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: IMAP wait progress: 16/30 seconds elapsed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:05.549525+00:00
[2025-08-19 09:19:05.592] 07:19:05.592 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1.5: IMAP wait progress: 16/30 seconds elapsed .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:05.587489
[2025-08-19 09:19:10.643] [GLOBAL_TEST] 07:19:10.643[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: IMAP wait progress: 21/30 seconds elapsed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:10.643374+00:00
[2025-08-19 09:19:10.644] 07:19:10.643[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: IMAP wait progress: 21/30 seconds elapsed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:10.643374+00:00
[2025-08-19 09:19:10.694] 07:19:10.693 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1.5: IMAP wait progress: 21/30 seconds elapsed .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:10.689410
[2025-08-19 09:19:15.738] [GLOBAL_TEST] 07:19:15.738[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: IMAP wait progress: 26/30 seconds elapsed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:15.738966+00:00
[2025-08-19 09:19:15.739] 07:19:15.738[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: IMAP wait progress: 26/30 seconds elapsed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:15.738966+00:00
[2025-08-19 09:19:15.788] 07:19:15.788 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1.5: IMAP wait progress: 26/30 seconds elapsed .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:15.774937
[2025-08-19 09:19:19.814] [GLOBAL_TEST] 07:19:19.814[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: IMAP wait progress: 30/30 seconds elapsed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:19.814509+00:00
[2025-08-19 09:19:19.814] 07:19:19.814[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: IMAP wait progress: 30/30 seconds elapsed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:19.814509+00:00
[2025-08-19 09:19:19.853] [GLOBAL_TEST] 07:19:19.852[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: [OK] IMAP activation wait complete - IMAP should now be active .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:19.852152+00:00
[2025-08-19 09:19:19.853] 07:19:19.852[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5: [OK] IMAP activation wait complete - IMAP should now be active .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:19.852152+00:00
[2025-08-19 09:19:19.892] [GLOBAL_TEST] 07:19:19.892[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ===== STEP 2: EMAIL SENDING ===== .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:19.892155+00:00
[2025-08-19 09:19:19.893] 07:19:19.892[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ===== STEP 2: EMAIL SENDING ===== .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:19.892155+00:00
[2025-08-19 09:19:19.939] [GLOBAL_TEST] 07:19:19.938[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: Clearing broadcast history... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:19.938157+00:00
[2025-08-19 09:19:19.939] 07:19:19.938[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: Clearing broadcast history... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:19.938157+00:00
[2025-08-19 09:19:19.982] [GLOBAL_TEST] 07:19:19.981[DEBUG], 'test_real_email -> clear_broadcast_history': [TESTING BOT] Broadcast history cleared .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:19.981155+00:00
[2025-08-19 09:19:19.982] 07:19:19.981[DEBUG], 'test_real_email -> clear_broadcast_history': [TESTING BOT] Broadcast history cleared .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:19.981155+00:00
[2025-08-19 09:19:20.018] [GLOBAL_TEST] 07:19:20.018[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: [OK] Broadcast history cleared .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.018153+00:00
[2025-08-19 09:19:20.018] 07:19:20.018[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: [OK] Broadcast history cleared .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.018153+00:00
[2025-08-19 09:19:20.053] [GLOBAL_TEST] 07:19:20.053[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: Preparing email query: 'send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'' .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.053158+00:00
[2025-08-19 09:19:20.054] 07:19:20.053[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: Preparing email query: 'send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'' .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.053158+00:00
[2025-08-19 09:19:20.090] [GLOBAL_TEST] 07:19:20.090[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: About to call user.on_message() for email sending... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.090171+00:00
[2025-08-19 09:19:20.090] 07:19:20.090[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: About to call user.on_message() for email sending... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.090171+00:00
[2025-08-19 09:19:20.128] [GLOBAL_TEST] 07:19:20.128[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: Calling user.on_message()... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.128120+00:00
[2025-08-19 09:19:20.129] 07:19:20.128[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: Calling user.on_message()... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.128120+00:00
[2025-08-19 09:19:20.170] [GLOBAL_TEST] 07:19:20.170[INIT], 'start_request -> __init__': New request started with question of length: 71. Question: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":3}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:20.170152+00:00
[2025-08-19 09:19:20.171] 07:19:20.170[INIT], 'start_request -> __init__': New request started with question of length: 71. Question: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":3}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:20.170152+00:00
[2025-08-19 09:19:20.215] [GLOBAL_TEST] 07:19:20.215[EVENT], 'start_request -> add_request': Added request aeda2478-67ba-4b5b-835f-ec1cf129e911 to user TestUser. Total requests: 1 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.215162+00:00
[2025-08-19 09:19:20.216] 07:19:20.215[EVENT], 'start_request -> add_request': Added request aeda2478-67ba-4b5b-835f-ec1cf129e911 to user TestUser. Total requests: 1 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.215162+00:00
[2025-08-19 09:19:20.258] [GLOBAL_TEST] 07:19:20.258[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: user.on_message() completed successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.258158+00:00
[2025-08-19 09:19:20.259] 07:19:20.258[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: user.on_message() completed successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.258158+00:00
[2025-08-19 09:19:20.307] [GLOBAL_TEST] 07:19:20.307[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: Task result type: <class 'userprofiles.LongRunningZairaRequest.LongRunningZairaRequest'>, Value: human_in_the_loop_callback=None human_in_the_loop_request_message=None scheduled_guid=UUID('aeda2478-67ba-4b5b-835f-ec1cf129e911') chat_session_guid=UUID('8f02b140-e894-4b44-b2d4-454d89271da6') output_demands=['Testing'] complete_message="send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'" user=ZairaUser(username='TestUser', rank=<PERMISSION_LEVELS.ADMIN: 2>, real_name='', email='<EMAIL>', is_system_user=False, first_name='', last_name='', job_title='', company='', personal_prompt='', preferred_language='en', timezone='UTC', zaira_voice='default', enable_followup_questions=True, chat_history_interval='never', company_domain='', two_factor_enabled=False, tokens_used=0, allow_document_generation=True, gb_remaining=100.0, data_location='Local Server', active_triggers=0, disabled_triggers=0, total_connectors=0, total_automations=0, organization_id='', privacy_level='Standard', plan_type='ZZP', payment_method='Credit Card ****1234', next_billing_date='2024-02-15', monthly_cost=29.99, employee_count=1, subscription_status='Active', user_guid=UUID('703af770-18d0-4382-af97-96b3d0934291'), DeviceGUID=UUID('8f6cf1a7-905c-43b3-902b-f9b07facc1d6'), session_guid=UUID('8f02b140-e894-4b44-b2d4-454d89271da6'), my_requests={UUID('aeda2478-67ba-4b5b-835f-ec1cf129e911'): LongRunningZairaRequest(human_in_the_loop_callback=None, human_in_the_loop_request_message=None, scheduled_guid=UUID('aeda2478-67ba-4b5b-835f-ec1cf129e911'), chat_session_guid=UUID('8f02b140-e894-4b44-b2d4-454d89271da6'), output_demands=['Testing'], complete_message="send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'", user=ZairaUser(username='TestUser', rank=<PERMISSION_LEVELS.ADMIN: 2>, real_name='', email='<EMAIL>', is_system_user=False, first_name='', last_name='', job_title='', company='', personal_prompt='', preferred_language='en', timezone='UTC', zaira_voice='default', enable_followup_questions=True, chat_history_interval='never', company_domain='', two_factor_enabled=False, tokens_used=0, allow_document_generation=True, gb_remaining=100.0, data_location='Local Server', active_triggers=0, disabled_triggers=0, total_connectors=0, total_automations=0, organization_id='', privacy_level='Standard', plan_type='ZZP', payment_method='Credit Card ****1234', next_billing_date='2024-02-15', monthly_cost=29.99, employee_count=1, subscription_status='Active', user_guid=UUID('703af770-18d0-4382-af97-96b3d0934291'), DeviceGUID=UUID('8f6cf1a7-905c-43b3-902b-f9b07facc1d6'), session_guid=UUID('8f02b140-e894-4b44-b2d4-454d89271da6'), my_requests={...}, asyncio_Task=<Task pending name='Task-410' coro=<LongRunningZairaRequest.run_request() running at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\LongRunningZairaRequest.py:87> cb=[handle_asyncio_task_result_errors() at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\etc\helper_functions.py:76]>, chat_history={UUID('8f02b140-e894-4b44-b2d4-454d89271da6'): ZairaChat(session_guid='8f02b140-e894-4b44-b2d4-454d89271da6', user_guid='703af770-18d0-4382-af97-96b3d0934291', title='Main Chat Session', message_count=4, status='ChatSessionStatus.ACTIVE', created_at='2025-08-19T07:18:45.578731+00:00')}), calling_bot=MyBot_Testing(parent_instance=None, name='Testing', asyncio_Task=None), original_physical_message=None, asyncio_Task_await_response=<Task pending name='Task-411' coro=<LongRunningZairaRequest.await_status_complete.<locals>.long_running_query() running at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\LongRunningZairaRequest.py:304> cb=[handle_asyncio_task_result_errors() at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\etc\helper_functions.py:76]>, asyncio_Task_long_running_query=None, call_trace=[], processing_data=None, status='running', message='Request aeda2478-67ba-4b5b-835f-ec1cf129e911 started!', started_at=1755587960.1291194, completed_at=None, call_trace_updated_at=None, final_call_trace_updated_at=None)}, asyncio_Task=<Task pending name='Task-410' coro=<LongRunningZairaRequest.run_request() running at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\LongRunningZairaRequest.py:87> cb=[handle_asyncio_task_result_errors() at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\etc\helper_functions.py:76]>, chat_history={UUID('8f02b140-e894-4b44-b2d4-454d89271da6'): ZairaChat(session_guid='8f02b140-e894-4b44-b2d4-454d89271da6', user_guid='703af770-18d0-4382-af97-96b3d0934291', title='Main Chat Session', message_count=4, status='ChatSessionStatus.ACTIVE', created_at='2025-08-19T07:18:45.578731+00:00')}) calling_bot=MyBot_Testing(parent_instance=None, name='Testing', asyncio_Task=None) original_physical_message=None asyncio_Task_await_response=<Task pending name='Task-411' coro=<LongRunningZairaRequest.await_status_complete.<locals>.long_running_query() running at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\LongRunningZairaRequest.py:304> cb=[handle_asyncio_task_result_errors() at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\etc\helper_functions.py:76]> asyncio_Task_long_running_query=None call_trace=[] processing_data=None status='running' message='Request aeda2478-67ba-4b5b-835f-ec1cf129e911 started!' started_at=1755587960.1291194 completed_at=None call_trace_updated_at=None final_call_trace_updated_at=None .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.307123+00:00
[2025-08-19 09:19:20.308] 07:19:20.307[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: Task result type: <class 'userprofiles.LongRunningZairaRequest.LongRunningZairaRequest'>, Value: human_in_the_loop_callback=None human_in_the_loop_request_message=None scheduled_guid=UUID('aeda2478-67ba-4b5b-835f-ec1cf129e911') chat_session_guid=UUID('8f02b140-e894-4b44-b2d4-454d89271da6') output_demands=['Testing'] complete_message="send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'" user=ZairaUser(username='TestUser', rank=<PERMISSION_LEVELS.ADMIN: 2>, real_name='', email='<EMAIL>', is_system_user=False, first_name='', last_name='', job_title='', company='', personal_prompt='', preferred_language='en', timezone='UTC', zaira_voice='default', enable_followup_questions=True, chat_history_interval='never', company_domain='', two_factor_enabled=False, tokens_used=0, allow_document_generation=True, gb_remaining=100.0, data_location='Local Server', active_triggers=0, disabled_triggers=0, total_connectors=0, total_automations=0, organization_id='', privacy_level='Standard', plan_type='ZZP', payment_method='Credit Card ****1234', next_billing_date='2024-02-15', monthly_cost=29.99, employee_count=1, subscription_status='Active', user_guid=UUID('703af770-18d0-4382-af97-96b3d0934291'), DeviceGUID=UUID('8f6cf1a7-905c-43b3-902b-f9b07facc1d6'), session_guid=UUID('8f02b140-e894-4b44-b2d4-454d89271da6'), my_requests={UUID('aeda2478-67ba-4b5b-835f-ec1cf129e911'): LongRunningZairaRequest(human_in_the_loop_callback=None, human_in_the_loop_request_message=None, scheduled_guid=UUID('aeda2478-67ba-4b5b-835f-ec1cf129e911'), chat_session_guid=UUID('8f02b140-e894-4b44-b2d4-454d89271da6'), output_demands=['Testing'], complete_message="send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'", user=ZairaUser(username='TestUser', rank=<PERMISSION_LEVELS.ADMIN: 2>, real_name='', email='<EMAIL>', is_system_user=False, first_name='', last_name='', job_title='', company='', personal_prompt='', preferred_language='en', timezone='UTC', zaira_voice='default', enable_followup_questions=True, chat_history_interval='never', company_domain='', two_factor_enabled=False, tokens_used=0, allow_document_generation=True, gb_remaining=100.0, data_location='Local Server', active_triggers=0, disabled_triggers=0, total_connectors=0, total_automations=0, organization_id='', privacy_level='Standard', plan_type='ZZP', payment_method='Credit Card ****1234', next_billing_date='2024-02-15', monthly_cost=29.99, employee_count=1, subscription_status='Active', user_guid=UUID('703af770-18d0-4382-af97-96b3d0934291'), DeviceGUID=UUID('8f6cf1a7-905c-43b3-902b-f9b07facc1d6'), session_guid=UUID('8f02b140-e894-4b44-b2d4-454d89271da6'), my_requests={...}, asyncio_Task=<Task pending name='Task-410' coro=<LongRunningZairaRequest.run_request() running at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\LongRunningZairaRequest.py:87> cb=[handle_asyncio_task_result_errors() at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\etc\helper_functions.py:76]>, chat_history={UUID('8f02b140-e894-4b44-b2d4-454d89271da6'): ZairaChat(session_guid='8f02b140-e894-4b44-b2d4-454d89271da6', user_guid='703af770-18d0-4382-af97-96b3d0934291', title='Main Chat Session', message_count=4, status='ChatSessionStatus.ACTIVE', created_at='2025-08-19T07:18:45.578731+00:00')}), calling_bot=MyBot_Testing(parent_instance=None, name='Testing', asyncio_Task=None), original_physical_message=None, asyncio_Task_await_response=<Task pending name='Task-411' coro=<LongRunningZairaRequest.await_status_complete.<locals>.long_running_query() running at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\LongRunningZairaRequest.py:304> cb=[handle_asyncio_task_result_errors() at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\etc\helper_functions.py:76]>, asyncio_Task_long_running_query=None, call_trace=[], processing_data=None, status='running', message='Request aeda2478-67ba-4b5b-835f-ec1cf129e911 started!', started_at=1755587960.1291194, completed_at=None, call_trace_updated_at=None, final_call_trace_updated_at=None)}, asyncio_Task=<Task pending name='Task-410' coro=<LongRunningZairaRequest.run_request() running at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\LongRunningZairaRequest.py:87> cb=[handle_asyncio_task_result_errors() at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\etc\helper_functions.py:76]>, chat_history={UUID('8f02b140-e894-4b44-b2d4-454d89271da6'): ZairaChat(session_guid='8f02b140-e894-4b44-b2d4-454d89271da6', user_guid='703af770-18d0-4382-af97-96b3d0934291', title='Main Chat Session', message_count=4, status='ChatSessionStatus.ACTIVE', created_at='2025-08-19T07:18:45.578731+00:00')}) calling_bot=MyBot_Testing(parent_instance=None, name='Testing', asyncio_Task=None) original_physical_message=None asyncio_Task_await_response=<Task pending name='Task-411' coro=<LongRunningZairaRequest.await_status_complete.<locals>.long_running_query() running at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\LongRunningZairaRequest.py:304> cb=[handle_asyncio_task_result_errors() at C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\etc\helper_functions.py:76]> asyncio_Task_long_running_query=None call_trace=[] processing_data=None status='running' message='Request aeda2478-67ba-4b5b-835f-ec1cf129e911 started!' started_at=1755587960.1291194 completed_at=None call_trace_updated_at=None final_call_trace_updated_at=None .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.307123+00:00
[2025-08-19 09:19:20.365] [GLOBAL_TEST] 07:19:20.365[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: Email sending result - Success: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.365047+00:00
[2025-08-19 09:19:20.366] 07:19:20.365[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: Email sending result - Success: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.365047+00:00
[2025-08-19 09:19:20.412] [GLOBAL_TEST] 07:19:20.412[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: [OK] Email task created successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.412807+00:00
[2025-08-19 09:19:20.413] 07:19:20.412[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2: [OK] Email task created successfully .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.412807+00:00
[2025-08-19 09:19:20.451] [GLOBAL_TEST] 07:19:20.451[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ===== STEP 3: BROADCAST DETECTION ===== .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.451810+00:00
[2025-08-19 09:19:20.452] 07:19:20.451[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ===== STEP 3: BROADCAST DETECTION ===== .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.451810+00:00
[2025-08-19 09:19:20.489] [GLOBAL_TEST] 07:19:20.489[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3: Setting up broadcast detection using Testing bot... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.489814+00:00
[2025-08-19 09:19:20.490] 07:19:20.489[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3: Setting up broadcast detection using Testing bot... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.489814+00:00
[2025-08-19 09:19:20.539] [GLOBAL_TEST] 07:19:20.539[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3: Creating broadcast assertion function... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.539775+00:00
[2025-08-19 09:19:20.539] 07:19:20.539[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3: Creating broadcast assertion function... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.539775+00:00
[2025-08-19 09:19:20.587] [GLOBAL_TEST] 07:19:20.587[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3: Starting broadcast polling (8 minutes timeout, 30-second intervals)... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.587810+00:00
[2025-08-19 09:19:20.587] 07:19:20.587[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3: Starting broadcast polling (8 minutes timeout, 30-second intervals)... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.587810+00:00
[2025-08-19 09:19:20.627] [GLOBAL_TEST] 07:19:20.627[DEBUG], 'test_real_email -> wait_for_assertions': [TestRealEmail] Starting assertion polling for: direct_broadcast_detection .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.627823+00:00
[2025-08-19 09:19:20.627] 07:19:20.627[DEBUG], 'test_real_email -> wait_for_assertions': [TestRealEmail] Starting assertion polling for: direct_broadcast_detection .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.627823+00:00
[2025-08-19 09:19:20.670] [GLOBAL_TEST] 07:19:20.670[DEBUG], 'test_real_email -> wait_for_assertions': [TestRealEmail] Timeout: 480s, Poll interval: 30s .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.670811+00:00
[2025-08-19 09:19:20.671] 07:19:20.670[DEBUG], 'test_real_email -> wait_for_assertions': [TestRealEmail] Timeout: 480s, Poll interval: 30s .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.670811+00:00
[2025-08-19 09:19:20.708] [GLOBAL_TEST] 07:19:20.708[DEBUG], 'wait_for_assertions -> direct_broadcast_assertion': [TestRealEmail] STEP 3: Checking for broadcasts since 2025-08-19 09:18:46.526802... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.708808+00:00
[2025-08-19 09:19:20.709] 07:19:20.708[DEBUG], 'wait_for_assertions -> direct_broadcast_assertion': [TestRealEmail] STEP 3: Checking for broadcasts since 2025-08-19 09:18:46.526802... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.708808+00:00
[2025-08-19 09:19:20.747] [GLOBAL_TEST] 07:19:20.747[DEBUG], 'wait_for_assertions -> direct_broadcast_assertion': [TestRealEmail] STEP 3: Found 0 broadcasts, has_broadcast=False .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.747809+00:00
[2025-08-19 09:19:20.747] 07:19:20.747[DEBUG], 'wait_for_assertions -> direct_broadcast_assertion': [TestRealEmail] STEP 3: Found 0 broadcasts, has_broadcast=False .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:20.747809+00:00
[2025-08-19 09:19:21.822] [GLOBAL_TEST] 07:19:21.821[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":4}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:21.821426+00:00
[2025-08-19 09:19:21.822] 07:19:21.821[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":4}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:21.821426+00:00
[2025-08-19 09:19:21.858] [GLOBAL_TEST] 07:19:21.858[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: initial [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":5}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:21.858462+00:00
[2025-08-19 09:19:21.858] 07:19:21.858[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: initial [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":5}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:21.858462+00:00
[2025-08-19 09:19:21.894] [GLOBAL_TEST] 07:19:21.894[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) performing initial reasoning to choose task .  Metadata: {"chat length#":6}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:21.894462+00:00
[2025-08-19 09:19:21.895] 07:19:21.894[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) performing initial reasoning to choose task .  Metadata: {"chat length#":6}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:21.894462+00:00
[2025-08-19 09:19:21.941] [GLOBAL_TEST] 07:19:21.941[DEBUG], 'llm_call_router -> _perform_cot_reasoning': top_level_supervisor performing CoT reasoning with original_input: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510' [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":7}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:21.941461+00:00
[2025-08-19 09:19:21.942] 07:19:21.941[DEBUG], 'llm_call_router -> _perform_cot_reasoning': top_level_supervisor performing CoT reasoning with original_input: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510' [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":7}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:21.941461+00:00
[2025-08-19 09:19:21.980] [GLOBAL_TEST] 07:19:21.980[DEBUG], 'llm_call_router -> _perform_cot_reasoning': [CoT REASONING] top_level_supervisor starting chain of thought for task selection... .  Metadata: {"chat length#":8}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:21.980460+00:00
[2025-08-19 09:19:21.980] 07:19:21.980[DEBUG], 'llm_call_router -> _perform_cot_reasoning': [CoT REASONING] top_level_supervisor starting chain of thought for task selection... .  Metadata: {"chat length#":8}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:21.980460+00:00
[2025-08-19 09:19:22.176] 07:19:22.176 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1.5: IMAP wait progress: 30/30 seconds elapsed .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:21.776461
[2025-08-19 09:19:22.189] 07:19:22.188 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 1.5: [OK] IMAP activation wait complete - IMAP should now be active .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.176084
[2025-08-19 09:19:22.192] 07:19:22.192 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] ===== STEP 2: EMAIL SENDING ===== .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.189391
[2025-08-19 09:19:22.196] 07:19:22.195 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 2: Clearing broadcast history... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.192360
[2025-08-19 09:19:22.199] 07:19:22.199 [Python][DEBUG], '_run_once -> _run': [TESTING BOT] Broadcast history cleared .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.196358
[2025-08-19 09:19:22.203] 07:19:22.202 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 2: [OK] Broadcast history cleared .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.199360
[2025-08-19 09:19:22.206] 07:19:22.206 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 2: Preparing email query: 'send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'' .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.203383
[2025-08-19 09:19:22.209] 07:19:22.209 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 2: About to call user.on_message() for email sending... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.206358
[2025-08-19 09:19:22.213] 07:19:22.212 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 2: Calling user.on_message()... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.209386
[2025-08-19 09:19:22.216] 07:19:22.216 [Python][INIT], '_run_once -> _run': New request started with question of length: 71. Question: sen...st <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":9}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:22.213384
[2025-08-19 09:19:22.220] 07:19:22.219 [Python][EVENT], '_run_once -> _run': Added request aeda2478-67ba-4b5b-835f-ec1cf129e911 to user TestUser. Total requests: 1 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.216357
[2025-08-19 09:19:22.225] 07:19:22.225 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 2: user.on_message() completed successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.220384
[2025-08-19 09:19:22.229] 07:19:22.229 [Python][DEBUG], '_run_once -> _run': [Scrubbed due to 'session'].  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.225356
[2025-08-19 09:19:22.233] 07:19:22.233 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 2: Email sending result - Success: True .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.230381
[2025-08-19 09:19:22.237] 07:19:22.236 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 2: [OK] Email task created successfully .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.233358
[2025-08-19 09:19:22.240] 07:19:22.240 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] ===== STEP 3: BROADCAST DETECTION ===== .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.237356
[2025-08-19 09:19:22.244] 07:19:22.244 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 3: Setting up broadcast detection using Testing bot... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.240356
[2025-08-19 09:19:22.248] 07:19:22.247 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 3: Creating broadcast assertion function... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.244360
[2025-08-19 09:19:22.252] 07:19:22.251 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 3: Starting broadcast polling (8 minutes timeout, 30-second intervals)... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.248356
[2025-08-19 09:19:22.255] 07:19:22.255 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] Starting assertion polling for: direct_broadcast_detection .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.252365
[2025-08-19 09:19:22.258] 07:19:22.258 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] Timeout: 480s, Poll interval: 30s .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.255360
[2025-08-19 09:19:22.262] 07:19:22.262 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 3: Checking for broadcasts since 2025-08-19 09:18:46.526802... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.258359
[2025-08-19 09:19:22.267] 07:19:22.267 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 3: Found 0 broadcasts, has_broadcast=False .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:22.262358
[2025-08-19 09:19:22.271] 07:19:22.270 [Python][TASK], '_run_once -> _run': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba...st <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":10}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:22.267362
[2025-08-19 09:19:25.277] [GLOBAL_TEST] 07:19:25.277[DEBUG], 'llm_call_router -> _perform_cot_reasoning': [CoT THINKING] top_level_supervisor task selection reasoning:
[2025-08-19 09:19:25.277] ------------------------------------------------------------
[2025-08-19 09:19:25.277] 1. UNDERSTAND: The user's original request is to send a test email to the address "<EMAIL>" with the subject "Test Email a6bb7510."

[2025-08-19 09:19:25.277] 2. EVALUATE: No tasks have been called yet for this request.

[2025-08-19 09:19:25.277] 3. IDENTIFY: The task that needs to be done is to generate and send the email.

[2025-08-19 09:19:25.277] 4. PRIORITIZE: The most valuable task to call next is the email_generator_task to create and send the email.

[2025-08-19 09:19:25.277] 5. DECIDE: I will call the email_generator_task to fulfill the user's request to send the email.
[2025-08-19 09:19:25.277] ------------------------------------------------------------ .  Metadata: {"chat length#":15}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:25.277620+00:00
[2025-08-19 09:19:25.277] 07:19:25.277[DEBUG], 'llm_call_router -> _perform_cot_reasoning': [CoT THINKING] top_level_supervisor task selection reasoning:
------------------------------------------------------------
1. UNDERSTAND: The user's original request is to send a test email to the address "<EMAIL>" with the subject "Test Email a6bb7510."

2. EVALUATE: No tasks have been called yet for this request.

3. IDENTIFY: The task that needs to be done is to generate and send the email.

4. PRIORITIZE: The most valuable task to call next is the email_generator_task to create and send the email.

5. DECIDE: I will call the email_generator_task to fulfill the user's request to send the email.
------------------------------------------------------------ .  Metadata: {"chat length#":15}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:25.277620+00:00
[2025-08-19 09:19:26.354] [GLOBAL_TEST] 07:19:26.354[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) chose task: email_generator_task [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":18}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:26.354728+00:00
[2025-08-19 09:19:26.355] 07:19:26.354[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) chose task: email_generator_task [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":18}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:26.354728+00:00
[2025-08-19 09:19:26.391] [GLOBAL_TEST] 07:19:26.391[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) executing next always_call_FIRST task: quick_rag_task (no re-reasoning) .  Metadata: {"chat length#":19}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:26.391724+00:00
[2025-08-19 09:19:26.392] 07:19:26.391[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) executing next always_call_FIRST task: quick_rag_task (no re-reasoning) .  Metadata: {"chat length#":19}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:26.391724+00:00
[2025-08-19 09:19:26.530] [GLOBAL_TEST] 07:19:26.530[TASK], 'ainvoke -> llm_call_wrapper': Task quick_rag_task [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510''.  Metadata: {"chat length#":21}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:26.530904+00:00
[2025-08-19 09:19:26.531] 07:19:26.530[TASK], 'ainvoke -> llm_call_wrapper': Task quick_rag_task [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510''.  Metadata: {"chat length#":21}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:26.530904+00:00
[2025-08-19 09:19:26.573] [GLOBAL_TEST] 07:19:26.573[DEBUG], 'llm_call_wrapper -> llm_call': Starting retrieve_data for: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510' .  Metadata: {"chat length#":22}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:26.573864+00:00
[2025-08-19 09:19:26.574] 07:19:26.573[DEBUG], 'llm_call_wrapper -> llm_call': Starting retrieve_data for: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510' .  Metadata: {"chat length#":22}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:26.573864+00:00
[2025-08-19 09:19:26.617] [GLOBAL_TEST] 07:19:26.617[DEBUG], 'llm_call_wrapper -> llm_call': About to call query_engine.aquery... .  Metadata: {"chat length#":23}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:26.617896+00:00
[2025-08-19 09:19:26.618] 07:19:26.617[DEBUG], 'llm_call_wrapper -> llm_call': About to call query_engine.aquery... .  Metadata: {"chat length#":23}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:26.617896+00:00
[2025-08-19 09:19:33.385] [GLOBAL_TEST] 07:19:33.385[DEBUG], 'llm_call_wrapper -> llm_call': Query completed, response: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510' .  Metadata: {"chat length#":26}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:33.385047+00:00
[2025-08-19 09:19:33.386] 07:19:33.385[DEBUG], 'llm_call_wrapper -> llm_call': Query completed, response: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510' .  Metadata: {"chat length#":26}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:33.385047+00:00
[2025-08-19 09:19:33.461] [GLOBAL_TEST] 07:19:33.461[TASK], 'llm_call_wrapper -> llm_call':  RAGed: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:33.461240+00:00
[2025-08-19 09:19:33.461] 07:19:33.461[TASK], 'llm_call_wrapper -> llm_call':  RAGed: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:33.461240+00:00
[2025-08-19 09:19:33.667] 07:19:33.667 [Python][TASK], '_run_once -> _run': Task quick_rag_task [scheduled_guid: aeda2478-67ba-4b5b-835f-e...t <NAME_EMAIL> with subject 'Test Email a6bb7510''.  Metadata: {"chat length#":25}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:26.902958
[2025-08-19 09:19:33.735] [GLOBAL_TEST] 07:19:33.735[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":29}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:33.735430+00:00
[2025-08-19 09:19:33.736] 07:19:33.735[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":29}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:33.735430+00:00
[2025-08-19 09:19:33.782] [GLOBAL_TEST] 07:19:33.782[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: always_first [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":30}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:33.782432+00:00
[2025-08-19 09:19:33.782] 07:19:33.782[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: always_first [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":30}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:33.782432+00:00
[2025-08-19 09:19:33.827] [GLOBAL_TEST] 07:19:33.827[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) executing next always_call_FIRST task: quick_llm_task (no re-reasoning) .  Metadata: {"chat length#":31}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:33.827430+00:00
[2025-08-19 09:19:33.827] 07:19:33.827[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) executing next always_call_FIRST task: quick_llm_task (no re-reasoning) .  Metadata: {"chat length#":31}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:33.827430+00:00
[2025-08-19 09:19:34.025] [GLOBAL_TEST] 07:19:34.024[TASK], 'ainvoke -> llm_call_wrapper': Task quick_llm_task [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510''.  Metadata: {"chat length#":32}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:34.024125+00:00
[2025-08-19 09:19:34.025] 07:19:34.024[TASK], 'ainvoke -> llm_call_wrapper': Task quick_llm_task [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510''.  Metadata: {"chat length#":32}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:34.024125+00:00
[2025-08-19 09:19:34.106] 07:19:34.106 [Python][TASK], '_run_once -> _run':  RAGed: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:34.103415
[2025-08-19 09:19:34.110] 07:19:34.110 [Python][TASK], '_run_once -> _run': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba...st <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":35}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:34.106993
[2025-08-19 09:19:34.123] 07:19:34.123 [Python][TASK], '_run_once -> _run': Task quick_llm_task [scheduled_guid: aeda2478-67ba-4b5b-835f-e...t <NAME_EMAIL> with subject 'Test Email a6bb7510''.  Metadata: {"chat length#":38}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:34.119185
[2025-08-19 09:19:35.118] [GLOBAL_TEST] 07:19:35.118[TASK], 'llm_call_wrapper -> llm_call':  LLMed: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:35.118464+00:00
[2025-08-19 09:19:35.119] 07:19:35.118[TASK], 'llm_call_wrapper -> llm_call':  LLMed: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:35.118464+00:00
[2025-08-19 09:19:35.213] [GLOBAL_TEST] 07:19:35.213[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":40}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:35.213465+00:00
[2025-08-19 09:19:35.213] 07:19:35.213[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":40}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:35.213465+00:00
[2025-08-19 09:19:35.265] [GLOBAL_TEST] 07:19:35.265[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: always_first [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":41}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:35.265465+00:00
[2025-08-19 09:19:35.266] 07:19:35.265[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: always_first [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":41}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:35.265465+00:00
[2025-08-19 09:19:35.318] [GLOBAL_TEST] 07:19:35.318[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) executing next always_call_FIRST task: quick_complexity_task (no re-reasoning) .  Metadata: {"chat length#":42}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:35.318429+00:00
[2025-08-19 09:19:35.319] 07:19:35.318[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) executing next always_call_FIRST task: quick_complexity_task (no re-reasoning) .  Metadata: {"chat length#":42}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:35.318429+00:00
[2025-08-19 09:19:35.526] [GLOBAL_TEST] 07:19:35.525[TASK], 'ainvoke -> llm_call_wrapper': Task quick_complexity_task [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510''.  Metadata: {"chat length#":43}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:35.525077+00:00
[2025-08-19 09:19:35.526] 07:19:35.525[TASK], 'ainvoke -> llm_call_wrapper': Task quick_complexity_task [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510''.  Metadata: {"chat length#":43}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:35.525077+00:00
[2025-08-19 09:19:35.565] [GLOBAL_TEST] 07:19:35.565[TASK], 'llm_call_wrapper -> llm_call':  Compl: 1.  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:35.565075+00:00
[2025-08-19 09:19:35.565] 07:19:35.565[TASK], 'llm_call_wrapper -> llm_call':  Compl: 1.  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:35.565075+00:00
[2025-08-19 09:19:35.664] 07:19:35.663 [Python][TASK], '_run_once -> _run':  LLMed: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:35.166432
[2025-08-19 09:19:35.720] [GLOBAL_TEST] 07:19:35.720[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":46}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:35.720142+00:00
[2025-08-19 09:19:35.721] 07:19:35.720[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":46}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:35.720142+00:00
[2025-08-19 09:19:35.768] [GLOBAL_TEST] 07:19:35.768[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: always_first [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":47}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:35.768176+00:00
[2025-08-19 09:19:35.768] 07:19:35.768[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: always_first [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":47}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:35.768176+00:00
[2025-08-19 09:19:35.815] [GLOBAL_TEST] 07:19:35.815[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) executing chosen task: email_generator_task .  Metadata: {"chat length#":48}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:35.815167+00:00
[2025-08-19 09:19:35.816] 07:19:35.815[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) executing chosen task: email_generator_task .  Metadata: {"chat length#":48}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:35.815167+00:00
[2025-08-19 09:19:36.011] [GLOBAL_TEST] 07:19:36.011[TASK], 'ainvoke -> llm_call_wrapper': Task email_generator_task [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510''.  Metadata: {"chat length#":49}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:36.011174+00:00
[2025-08-19 09:19:36.012] 07:19:36.011[TASK], 'ainvoke -> llm_call_wrapper': Task email_generator_task [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  send a test <NAME_EMAIL> with subject 'Test Email a6bb7510''.  Metadata: {"chat length#":49}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:36.011174+00:00
[2025-08-19 09:19:36.146] 07:19:36.145 [Python][TASK], '_run_once -> _run': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba...st <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":45}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:35.664617
[2025-08-19 09:19:36.160] 07:19:36.160 [Python][TASK], '_run_once -> _run': Task quick_complexity_task [scheduled_guid: aeda2478-67ba-4b5b...t <NAME_EMAIL> with subject 'Test Email a6bb7510''.  Metadata: {"chat length#":52}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:36.156148
[2025-08-19 09:19:36.164] 07:19:36.164 [Python][TASK], '_run_once -> _run':  Compl: 1.  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:36.160147
[2025-08-19 09:19:36.168] 07:19:36.168 [Python][TASK], '_run_once -> _run': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba...st <NAME_EMAIL> with subject 'Test Email a6bb7510'.  Metadata: {"chat length#":53}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:36.164146
[2025-08-19 09:19:36.180] 07:19:36.180 [Python][TASK], '_run_once -> _run': Task email_generator_task [scheduled_guid: aeda2478-67ba-4b5b-...t <NAME_EMAIL> with subject 'Test Email a6bb7510''.  Metadata: {"chat length#":56}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:36.177146
[2025-08-19 09:19:38.971] [GLOBAL_TEST] 07:19:38.970[TASK], 'llm_call -> call_my_tool': tool_call Adding state to tool email_generator_tool. user_guid: 703af770-18d0-4382-af97-96b3d0934291, scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911.  Metadata: {"chat length#":57}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:38.970996+00:00
[2025-08-19 09:19:38.971] 07:19:38.970[TASK], 'llm_call -> call_my_tool': tool_call Adding state to tool email_generator_tool. user_guid: 703af770-18d0-4382-af97-96b3d0934291, scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911.  Metadata: {"chat length#":57}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:38.970996+00:00
[2025-08-19 09:19:39.095] [GLOBAL_TEST] 07:19:39.095[DEBUG], '_run -> _arun': [EmailGeneratorTool] Starting email generation with content_request: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510' .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:39.095276+00:00
[2025-08-19 09:19:39.095] 07:19:39.095[DEBUG], '_run -> _arun': [EmailGeneratorTool] Starting email generation with content_request: send a test <NAME_EMAIL> with subject 'Test Email a6bb7510' .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:39.095276+00:00
[2025-08-19 09:19:39.149] [GLOBAL_TEST] 07:19:39.149[DEBUG], '_run -> _arun': [EmailGeneratorTool] subject_hint: Test Email a6bb7510, sender: None, recipient: <EMAIL> .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:39.149184+00:00
[2025-08-19 09:19:39.150] 07:19:39.149[DEBUG], '_run -> _arun': [EmailGeneratorTool] subject_hint: Test Email a6bb7510, sender: None, recipient: <EMAIL> .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:39.149184+00:00
[2025-08-19 09:19:39.215] [GLOBAL_TEST] 07:19:39.215[DEBUG], '_run -> _arun': [EmailGeneratorTool] state.user_guid: 703af770-18d0-4382-af97-96b3d0934291 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:39.215211+00:00
[2025-08-19 09:19:39.215] 07:19:39.215[DEBUG], '_run -> _arun': [EmailGeneratorTool] state.user_guid: 703af770-18d0-4382-af97-96b3d0934291 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:39.215211+00:00
[2025-08-19 09:19:39.268] [GLOBAL_TEST] 07:19:39.268[DEBUG], '_run -> _arun': [EmailGeneratorTool] Found user: TestUser .  Metadata: {"chat length#":58}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:39.268186+00:00
[2025-08-19 09:19:39.268] 07:19:39.268[DEBUG], '_run -> _arun': [EmailGeneratorTool] Found user: TestUser .  Metadata: {"chat length#":58}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:39.268186+00:00
[2025-08-19 09:19:39.325] [GLOBAL_TEST] 07:19:39.325[DEBUG], '_run -> _arun': [EmailGeneratorTool] Initial final_sender: None .  Metadata: {"chat length#":59}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:39.325189+00:00
[2025-08-19 09:19:39.326] 07:19:39.325[DEBUG], '_run -> _arun': [EmailGeneratorTool] Initial final_sender: None .  Metadata: {"chat length#":59}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:39.325189+00:00
[2025-08-19 09:19:39.386] [GLOBAL_TEST] 07:19:39.385[DEBUG], '_run -> _arun': [EmailGeneratorTool] No valid sender, checking user.email: <EMAIL> .  Metadata: {"chat length#":60}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:39.385189+00:00
[2025-08-19 09:19:39.386] 07:19:39.385[DEBUG], '_run -> _arun': [EmailGeneratorTool] No valid sender, checking user.email: <EMAIL> .  Metadata: {"chat length#":60}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:39.385189+00:00
[2025-08-19 09:19:39.434] [GLOBAL_TEST] 07:19:39.434[DEBUG], '_run -> _arun': [EmailGeneratorTool] Using user email as sender: <EMAIL> .  Metadata: {"chat length#":61}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:39.434182+00:00
[2025-08-19 09:19:39.434] 07:19:39.434[DEBUG], '_run -> _arun': [EmailGeneratorTool] Using user email as sender: <EMAIL> .  Metadata: {"chat length#":61}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:39.434182+00:00
[2025-08-19 09:19:39.485] [GLOBAL_TEST] 07:19:39.485[DEBUG], '_run -> _arun': [EmailGeneratorTool] Initial final_recipient: <EMAIL> .  Metadata: {"chat length#":62}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:39.485193+00:00
[2025-08-19 09:19:39.486] 07:19:39.485[DEBUG], '_run -> _arun': [EmailGeneratorTool] Initial final_recipient: <EMAIL> .  Metadata: {"chat length#":62}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:39.485193+00:00
[2025-08-19 09:19:39.536] [GLOBAL_TEST] 07:19:39.536[DEBUG], '_run -> _arun': [EmailGeneratorTool] Starting email content generation with LLM .  Metadata: {"chat length#":63}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:39.536183+00:00
[2025-08-19 09:19:39.537] 07:19:39.536[DEBUG], '_run -> _arun': [EmailGeneratorTool] Starting email content generation with LLM .  Metadata: {"chat length#":63}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:39.536183+00:00
[2025-08-19 09:19:41.495] 07:19:41.494 [Python][TASK], '_run_once -> _run': tool_call Adding state to tool email_generator_tool. user_guid...d0934291, scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911.  Metadata: {"chat length#":64}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:40.709222
[2025-08-19 09:19:41.502] 07:19:41.501 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Starting email generation with content_re...t <NAME_EMAIL> with subject 'Test Email a6bb7510' .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:41.495036
[2025-08-19 09:19:41.508] 07:19:41.508 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] subject_hint: Test Email a6bb7510, sender: None, recipient: <EMAIL> .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:41.502042
[2025-08-19 09:19:41.512] 07:19:41.512 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] state.user_guid: 703af770-18d0-4382-af97-96b3d0934291 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:41.508036
[2025-08-19 09:19:41.515] 07:19:41.515 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Found user: TestUser .  Metadata: {"chat length#":65}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:41.512037
[2025-08-19 09:19:41.520] 07:19:41.520 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Initial final_sender: None .  Metadata: {"chat length#":66}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:41.516038
[2025-08-19 09:19:41.526] 07:19:41.525 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] No valid sender, checking user.email: <EMAIL> .  Metadata: {"chat length#":67}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:41.520046
[2025-08-19 09:19:41.532] 07:19:41.531 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Using user email as sender: <EMAIL> .  Metadata: {"chat length#":68}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:41.526034
[2025-08-19 09:19:41.539] 07:19:41.538 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Initial final_recipient: <EMAIL> .  Metadata: {"chat length#":69}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:41.532040
[2025-08-19 09:19:41.544] 07:19:41.544 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Starting email content generation with LLM .  Metadata: {"chat length#":70}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:41.539048
[2025-08-19 09:19:42.839] [GLOBAL_TEST] 07:19:42.839[DEBUG], '_run -> _arun': [EmailGeneratorTool] About to request user approval for email preview .  Metadata: {"chat length#":71}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:42.839707+00:00
[2025-08-19 09:19:42.840] 07:19:42.839[DEBUG], '_run -> _arun': [EmailGeneratorTool] About to request user approval for email preview .  Metadata: {"chat length#":71}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:42.839707+00:00
[2025-08-19 09:19:42.951] [GLOBAL_TEST] 07:19:42.951[DEBUG], '_run -> _arun': [EmailGeneratorTool] Requesting HITL approval for email .  Metadata: {"chat length#":72}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:42.951724+00:00
[2025-08-19 09:19:42.952] 07:19:42.951[DEBUG], '_run -> _arun': [EmailGeneratorTool] Requesting HITL approval for email .  Metadata: {"chat length#":72}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:42.951724+00:00
[2025-08-19 09:19:43.078] [GLOBAL_TEST] 07:19:43.078[DEBUG], '_run -> _arun': [EmailGeneratorTool] Email preview: From: <EMAIL>
[2025-08-19 09:19:43.078] To: <EMAIL>
[2025-08-19 09:19:43.078] Subject: Test Email a6bb7510

[2025-08-19 09:19:43.078] Dear API Team,

[2025-08-19 09:19:43.078] I hope thi... .  Metadata: {"chat length#":73}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:43.078713+00:00
[2025-08-19 09:19:43.079] 07:19:43.078[DEBUG], '_run -> _arun': [EmailGeneratorTool] Email preview: From: <EMAIL>
To: <EMAIL>
Subject: Test Email a6bb7510

Dear API Team,

I hope thi... .  Metadata: {"chat length#":73}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:43.078713+00:00
[2025-08-19 09:19:43.171] [GLOBAL_TEST] 07:19:43.171[DEBUG], '_arun -> request_human_in_the_loop': [LongRunningZairaRequest] HITL requested: Ik heb de volgende mail opgesteld. Wil je dat ik deze goedkeur voor verzending? (j/n)

[2025-08-19 09:19:43.171] From: api@ask .  Metadata: {"chat length#":74}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:43.171371+00:00
[2025-08-19 09:19:43.172] 07:19:43.171[DEBUG], '_arun -> request_human_in_the_loop': [LongRunningZairaRequest] HITL requested: Ik heb de volgende mail opgesteld. Wil je dat ik deze goedkeur voor verzending? (j/n)

From: api@ask .  Metadata: {"chat length#":74}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:43.171371+00:00
[2025-08-19 09:19:43.301] [GLOBAL_TEST] 07:19:43.300[DEBUG], '_arun -> request_human_in_the_loop': [LongRunningZairaRequest] Request GUID: aeda2478-67ba-4b5b-835f-ec1cf129e911 .  Metadata: {"chat length#":75}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:43.300375+00:00
[2025-08-19 09:19:43.301] 07:19:43.300[DEBUG], '_arun -> request_human_in_the_loop': [LongRunningZairaRequest] Request GUID: aeda2478-67ba-4b5b-835f-ec1cf129e911 .  Metadata: {"chat length#":75}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:43.300375+00:00
[2025-08-19 09:19:43.470] [GLOBAL_TEST] 07:19:43.470[DEBUG], '_arun -> request_human_in_the_loop': [LongRunningZairaRequest] Bot: Testing .  Metadata: {"chat length#":76}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:43.470373+00:00
[2025-08-19 09:19:43.471] 07:19:43.470[DEBUG], '_arun -> request_human_in_the_loop': [LongRunningZairaRequest] Bot: Testing .  Metadata: {"chat length#":76}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:43.470373+00:00
[2025-08-19 09:19:43.622] [GLOBAL_TEST] 07:19:43.622[DEBUG], '_arun -> request_human_in_the_loop': [LongRunningZairaRequest] Calling bot's request_human_in_the_loop_internal .  Metadata: {"chat length#":77}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:43.622379+00:00
[2025-08-19 09:19:43.623] 07:19:43.622[DEBUG], '_arun -> request_human_in_the_loop': [LongRunningZairaRequest] Calling bot's request_human_in_the_loop_internal .  Metadata: {"chat length#":77}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:43.622379+00:00
[2025-08-19 09:19:44.484] 07:19:44.482 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] About to request user approval for email preview .  Metadata: {"chat length#":78}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:44.477693
[2025-08-19 09:19:44.492] 07:19:44.492 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Requesting HITL approval for email .  Metadata: {"chat length#":79}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:44.486705
[2025-08-19 09:19:44.499] 07:19:44.497 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Email preview: From: <EMAIL>
[2025-08-19 09:19:44.499] To:...m
[2025-08-19 09:19:44.499] Subject: Test Email a6bb7510

[2025-08-19 09:19:44.499] Dear API Team,

[2025-08-19 09:19:44.499] I hope thi... .  Metadata: {"chat length#":80}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:44.493692
[2025-08-19 09:19:44.504] 07:19:44.504 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] HITL requested: Ik heb de volgende m...je dat ik deze goedkeur voor verzending? (j/n)

[2025-08-19 09:19:44.504] From: api@ask .  Metadata: {"chat length#":81}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:44.499692
[2025-08-19 09:19:44.509] 07:19:44.508 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Request GUID: aeda2478-67ba-4b5b-835f-ec1cf129e911 .  Metadata: {"chat length#":82}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:44.504691
[2025-08-19 09:19:44.518] 07:19:44.517 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Bot: Testing .  Metadata: {"chat length#":83}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:44.509695
[2025-08-19 09:19:44.524] 07:19:44.523 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Calling bot's request_human_in_the_loop_internal .  Metadata: {"chat length#":84}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:44.518700
[2025-08-19 09:19:45.497] [GLOBAL_TEST] 07:19:45.497[DEBUG], 'request_human_in_the_loop -> request_human_in_the_loop_internal': [TESTING BOT] Request: Ik heb de volgende mail opgesteld. Wil je dat ik deze goedkeur voor verzending? (j/n)

[2025-08-19 09:19:45.497] From: <EMAIL>
[2025-08-19 09:19:45.497] To: <EMAIL>
[2025-08-19 09:19:45.497] Subject: Test Email a6bb7510

[2025-08-19 09:19:45.497] Dear API Team,

[2025-08-19 09:19:45.497] I hope this email finds you well. As per your request, I am sending a test <NAME_EMAIL> with the subject 'Test Email a6bb7510'. Please let me know if you receive it successfully.

[2025-08-19 09:19:45.497] Thank you for your attention to this matter.

[2025-08-19 09:19:45.497] Best regards,
[2025-08-19 09:19:45.497] [Your Name]

[2025-08-19 09:19:45.497] -- 
[2025-08-19 09:19:45.497] Mail opgesteld met AskZaira .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:45.497061+00:00
[2025-08-19 09:19:45.497] 07:19:45.497[DEBUG], 'request_human_in_the_loop -> request_human_in_the_loop_internal': [TESTING BOT] Request: Ik heb de volgende mail opgesteld. Wil je dat ik deze goedkeur voor verzending? (j/n)

From: <EMAIL>
To: <EMAIL>
Subject: Test Email a6bb7510

Dear API Team,

I hope this email finds you well. As per your request, I am sending a test <NAME_EMAIL> with the subject 'Test Email a6bb7510'. Please let me know if you receive it successfully.

Thank you for your attention to this matter.

Best regards,
[Your Name]

-- 
Mail opgesteld met AskZaira .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:45.497061+00:00
[2025-08-19 09:19:45.584] [GLOBAL_TEST] 07:19:45.584[DEBUG], 'request_human_in_the_loop -> request_human_in_the_loop_internal': [TESTING BOT] Response: Ja, de mail ziet er goed uit. Je kunt deze verzenden. .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:45.584062+00:00
[2025-08-19 09:19:45.585] 07:19:45.584[DEBUG], 'request_human_in_the_loop -> request_human_in_the_loop_internal': [TESTING BOT] Response: Ja, de mail ziet er goed uit. Je kunt deze verzenden. .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:45.584062+00:00
[2025-08-19 09:19:45.671] [GLOBAL_TEST] 07:19:45.671[DEBUG], 'request_human_in_the_loop -> request_human_in_the_loop_internal': [TESTING BOT] Task GUID: aeda2478-67ba-4b5b-835f-ec1cf129e911 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:45.671072+00:00
[2025-08-19 09:19:45.672] 07:19:45.671[DEBUG], 'request_human_in_the_loop -> request_human_in_the_loop_internal': [TESTING BOT] Task GUID: aeda2478-67ba-4b5b-835f-ec1cf129e911 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:45.671072+00:00
[2025-08-19 09:19:45.784] [GLOBAL_TEST] 07:19:45.784[DEBUG], 'request_human_in_the_loop -> request_human_in_the_loop_internal': [TESTING BOT] User GUID: 8f02b140-e894-4b44-b2d4-454d89271da6 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:45.784492+00:00
[2025-08-19 09:19:45.785] 07:19:45.784[DEBUG], 'request_human_in_the_loop -> request_human_in_the_loop_internal': [TESTING BOT] User GUID: 8f02b140-e894-4b44-b2d4-454d89271da6 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:45.784492+00:00
[2025-08-19 09:19:46.004] [GLOBAL_TEST] 07:19:46.003[DEBUG], 'request_human_in_the_loop_internal -> on_message': Routing to HITL request aeda2478-67ba-4b5b-835f-ec1cf129e911 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:46.003495+00:00
[2025-08-19 09:19:46.004] 07:19:46.003[DEBUG], 'request_human_in_the_loop_internal -> on_message': Routing to HITL request aeda2478-67ba-4b5b-835f-ec1cf129e911 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:46.003495+00:00
[2025-08-19 09:19:46.132] [GLOBAL_TEST] 07:19:46.132[DEBUG], 'add_to_output -> add_processing_output': [LongRunningZairaRequest] Added 'email' to output_demands via processing_data .  Metadata: {"chat length#":85}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:46.132499+00:00
[2025-08-19 09:19:46.133] 07:19:46.132[DEBUG], 'add_to_output -> add_processing_output': [LongRunningZairaRequest] Added 'email' to output_demands via processing_data .  Metadata: {"chat length#":85}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:46.132499+00:00
[2025-08-19 09:19:46.278] [GLOBAL_TEST] 07:19:46.278[DEBUG], 'handle_approval -> add_to_output': [EmailGeneratorTool] Added EmailProcessingData to processing_data. Current demands: ['Testing', 'email'] .  Metadata: {"chat length#":86}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:46.278498+00:00
[2025-08-19 09:19:46.280] 07:19:46.278[DEBUG], 'handle_approval -> add_to_output': [EmailGeneratorTool] Added EmailProcessingData to processing_data. Current demands: ['Testing', 'email'] .  Metadata: {"chat length#":86}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:46.278498+00:00
[2025-08-19 09:19:46.399] [GLOBAL_TEST] 07:19:46.399[DEBUG], '_run -> _arun': [EmailGeneratorTool] HITL approval request completed .  Metadata: {"chat length#":87}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:46.399498+00:00
[2025-08-19 09:19:46.400] 07:19:46.399[DEBUG], '_run -> _arun': [EmailGeneratorTool] HITL approval request completed .  Metadata: {"chat length#":87}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:46.399498+00:00
[2025-08-19 09:19:46.540] [GLOBAL_TEST] 07:19:46.540[DEBUG], '_run -> _arun': [EmailGeneratorTool] Returning EmailProcessingData: {'processed_at': datetime.datetime(2025, 8, 19, 9, 19, 42, 706809), 'user_approved': True, 'output_sent': False, 'sent_at': None, 'processing_metadata': {'generated_at': '2025-08-19T07:19:42.706809+00:00', 'user_guid': '703af770-18d0-4382-af97-96b3d0934291'}, 'subject': 'Test Email a6bb7510', 'content': "Dear API Team,\n\nI hope this email finds you well. As per your request, I am sending a test <NAME_EMAIL> with the subject 'Test Email a6bb7510'. Please let me know if you receive it successfully.\n\nThank you for your attention to this matter.\n\nBest regards,\n[Your Name]", 'sender': '<EMAIL>', 'recipient': '<EMAIL>', 'content_generated': True, 'subject_generated': True, 'sender_validated': True, 'recipient_validated': True, 'cc_recipients': [], 'bcc_recipients': [], 'attachments': [], 'email_thread_id': None} .  Metadata: {"chat length#":88}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:46.540501+00:00
[2025-08-19 09:19:46.541] 07:19:46.540[DEBUG], '_run -> _arun': [EmailGeneratorTool] Returning EmailProcessingData: {'processed_at': datetime.datetime(2025, 8, 19, 9, 19, 42, 706809), 'user_approved': True, 'output_sent': False, 'sent_at': None, 'processing_metadata': {'generated_at': '2025-08-19T07:19:42.706809+00:00', 'user_guid': '703af770-18d0-4382-af97-96b3d0934291'}, 'subject': 'Test Email a6bb7510', 'content': "Dear API Team,\n\nI hope this email finds you well. As per your request, I am sending a test <NAME_EMAIL> with the subject 'Test Email a6bb7510'. Please let me know if you receive it successfully.\n\nThank you for your attention to this matter.\n\nBest regards,\n[Your Name]", 'sender': '<EMAIL>', 'recipient': '<EMAIL>', 'content_generated': True, 'subject_generated': True, 'sender_validated': True, 'recipient_validated': True, 'cc_recipients': [], 'bcc_recipients': [], 'attachments': [], 'email_thread_id': None} .  Metadata: {"chat length#":88}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:46.540501+00:00
[2025-08-19 09:19:47.706] [GLOBAL_TEST] 07:19:47.706[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  {
[2025-08-19 09:19:47.706]   "processed_at": "2025-08-19T09:19:42.706809",
[2025-08-19 09:19:47.706]   "user_approved": true,
[2025-08-19 09:19:47.706]   "output_sent": false,
[2025-08-19 09:19:47.706]   "sent_at": null,
[2025-08-19 09:19:47.706]   "processing_metadata": {
[2025-08-19 09:19:47.706]     "generated_at": "2025-08-19T07:19:42.706809+00:00",
[2025-08-19 09:19:47.706]     "user_guid": "703af770-18d0-4382-af97-96b3d0934291"
[2025-08-19 09:19:47.706]   },
[2025-08-19 09:19:47.706]   "subject": "Test Email a6bb7510",
[2025-08-19 09:19:47.706]   "content": "Dear API Team,\n\nI hope this email finds you well. As per your request, I am sending a test <NAME_EMAIL> with the subject 'Test Email a6bb7510'. Please let me know if you receive it successfully.\n\nThank you for your attention to this matter.\n\nBest regards,\n[Your Name]",
[2025-08-19 09:19:47.706]   "sender": "<EMAIL>",
[2025-08-19 09:19:47.706]   "recipient": "<EMAIL>",
[2025-08-19 09:19:47.706]   "content_generated": true,
[2025-08-19 09:19:47.706]   "subject_generated": true,
[2025-08-19 09:19:47.706]   "sender_validated": true,
[2025-08-19 09:19:47.706]   "recipient_validated": true,
[2025-08-19 09:19:47.706]   "cc_recipients": [],
[2025-08-19 09:19:47.706]   "bcc_recipients": [],
[2025-08-19 09:19:47.706]   "attachments": [],
[2025-08-19 09:19:47.706]   "email_thread_id": null
[2025-08-19 09:19:47.706] }.  Metadata: {"chat length#":90}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:47.706058+00:00
[2025-08-19 09:19:47.707] 07:19:47.706[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  {
  "processed_at": "2025-08-19T09:19:42.706809",
  "user_approved": true,
  "output_sent": false,
  "sent_at": null,
  "processing_metadata": {
    "generated_at": "2025-08-19T07:19:42.706809+00:00",
    "user_guid": "703af770-18d0-4382-af97-96b3d0934291"
  },
  "subject": "Test Email a6bb7510",
  "content": "Dear API Team,\n\nI hope this email finds you well. As per your request, I am sending a test <NAME_EMAIL> with the subject 'Test Email a6bb7510'. Please let me know if you receive it successfully.\n\nThank you for your attention to this matter.\n\nBest regards,\n[Your Name]",
  "sender": "<EMAIL>",
  "recipient": "<EMAIL>",
  "content_generated": true,
  "subject_generated": true,
  "sender_validated": true,
  "recipient_validated": true,
  "cc_recipients": [],
  "bcc_recipients": [],
  "attachments": [],
  "email_thread_id": null
}.  Metadata: {"chat length#":90}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:47.706058+00:00
[2025-08-19 09:19:47.824] [GLOBAL_TEST] 07:19:47.824[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: always_last [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":91}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:47.824062+00:00
[2025-08-19 09:19:47.825] 07:19:47.824[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: always_last [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":91}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:47.824062+00:00
[2025-08-19 09:19:47.949] [GLOBAL_TEST] 07:19:47.949[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) executing next always_call_LAST task: top_output_supervisor .  Metadata: {"chat length#":92}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:47.949060+00:00
[2025-08-19 09:19:47.950] 07:19:47.949[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) executing next always_call_LAST task: top_output_supervisor .  Metadata: {"chat length#":92}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:47.949060+00:00
[2025-08-19 09:19:48.447] [GLOBAL_TEST] 07:19:48.447[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_output_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  {
[2025-08-19 09:19:48.447]   "processed_at": "2025-08-19T09:19:42.706809",
[2025-08-19 09:19:48.447]   "user_approved": true,
[2025-08-19 09:19:48.447]   "output_sent": false,
[2025-08-19 09:19:48.447]   "sent_at": null,
[2025-08-19 09:19:48.447]   "processing_metadata": {
[2025-08-19 09:19:48.447]     "generated_at": "2025-08-19T07:19:42.706809+00:00",
[2025-08-19 09:19:48.447]     "user_guid": "703af770-18d0-4382-af97-96b3d0934291"
[2025-08-19 09:19:48.447]   },
[2025-08-19 09:19:48.447]   "subject": "Test Email a6bb7510",
[2025-08-19 09:19:48.447]   "content": "Dear API Team,\n\nI hope this email finds you well. As per your request, I am sending a test <NAME_EMAIL> with the subject 'Test Email a6bb7510'. Please let me know if you receive it successfully.\n\nThank you for your attention to this matter.\n\nBest regards,\n[Your Name]",
[2025-08-19 09:19:48.447]   "sender": "<EMAIL>",
[2025-08-19 09:19:48.447]   "recipient": "<EMAIL>",
[2025-08-19 09:19:48.447]   "content_generated": true,
[2025-08-19 09:19:48.447]   "subject_generated": true,
[2025-08-19 09:19:48.447]   "sender_validated": true,
[2025-08-19 09:19:48.447]   "recipient_validated": true,
[2025-08-19 09:19:48.447]   "cc_recipients": [],
[2025-08-19 09:19:48.447]   "bcc_recipients": [],
[2025-08-19 09:19:48.447]   "attachments": [],
[2025-08-19 09:19:48.447]   "email_thread_id": null
[2025-08-19 09:19:48.447] }.  Metadata: {"chat length#":93}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:48.447927+00:00
[2025-08-19 09:19:48.449] 07:19:48.447[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_output_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  {
  "processed_at": "2025-08-19T09:19:42.706809",
  "user_approved": true,
  "output_sent": false,
  "sent_at": null,
  "processing_metadata": {
    "generated_at": "2025-08-19T07:19:42.706809+00:00",
    "user_guid": "703af770-18d0-4382-af97-96b3d0934291"
  },
  "subject": "Test Email a6bb7510",
  "content": "Dear API Team,\n\nI hope this email finds you well. As per your request, I am sending a test <NAME_EMAIL> with the subject 'Test Email a6bb7510'. Please let me know if you receive it successfully.\n\nThank you for your attention to this matter.\n\nBest regards,\n[Your Name]",
  "sender": "<EMAIL>",
  "recipient": "<EMAIL>",
  "content_generated": true,
  "subject_generated": true,
  "sender_validated": true,
  "recipient_validated": true,
  "cc_recipients": [],
  "bcc_recipients": [],
  "attachments": [],
  "email_thread_id": null
}.  Metadata: {"chat length#":93}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:48.447927+00:00
[2025-08-19 09:19:48.701] [GLOBAL_TEST] 07:19:48.701[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor output_processing_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  {
[2025-08-19 09:19:48.701]   "processed_at": "2025-08-19T09:19:42.706809",
[2025-08-19 09:19:48.701]   "user_approved": true,
[2025-08-19 09:19:48.701]   "output_sent": false,
[2025-08-19 09:19:48.701]   "sent_at": null,
[2025-08-19 09:19:48.701]   "processing_metadata": {
[2025-08-19 09:19:48.701]     "generated_at": "2025-08-19T07:19:42.706809+00:00",
[2025-08-19 09:19:48.701]     "user_guid": "703af770-18d0-4382-af97-96b3d0934291"
[2025-08-19 09:19:48.701]   },
[2025-08-19 09:19:48.701]   "subject": "Test Email a6bb7510",
[2025-08-19 09:19:48.701]   "content": "Dear API Team,\n\nI hope this email finds you well. As per your request, I am sending a test <NAME_EMAIL> with the subject 'Test Email a6bb7510'. Please let me know if you receive it successfully.\n\nThank you for your attention to this matter.\n\nBest regards,\n[Your Name]",
[2025-08-19 09:19:48.701]   "sender": "<EMAIL>",
[2025-08-19 09:19:48.701]   "recipient": "<EMAIL>",
[2025-08-19 09:19:48.701]   "content_generated": true,
[2025-08-19 09:19:48.701]   "subject_generated": true,
[2025-08-19 09:19:48.701]   "sender_validated": true,
[2025-08-19 09:19:48.701]   "recipient_validated": true,
[2025-08-19 09:19:48.701]   "cc_recipients": [],
[2025-08-19 09:19:48.701]   "bcc_recipients": [],
[2025-08-19 09:19:48.701]   "attachments": [],
[2025-08-19 09:19:48.701]   "email_thread_id": null
[2025-08-19 09:19:48.701] }.  Metadata: {"chat length#":94}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:48.701304+00:00
[2025-08-19 09:19:48.702] 07:19:48.701[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor output_processing_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  {
  "processed_at": "2025-08-19T09:19:42.706809",
  "user_approved": true,
  "output_sent": false,
  "sent_at": null,
  "processing_metadata": {
    "generated_at": "2025-08-19T07:19:42.706809+00:00",
    "user_guid": "703af770-18d0-4382-af97-96b3d0934291"
  },
  "subject": "Test Email a6bb7510",
  "content": "Dear API Team,\n\nI hope this email finds you well. As per your request, I am sending a test <NAME_EMAIL> with the subject 'Test Email a6bb7510'. Please let me know if you receive it successfully.\n\nThank you for your attention to this matter.\n\nBest regards,\n[Your Name]",
  "sender": "<EMAIL>",
  "recipient": "<EMAIL>",
  "content_generated": true,
  "subject_generated": true,
  "sender_validated": true,
  "recipient_validated": true,
  "cc_recipients": [],
  "bcc_recipients": [],
  "attachments": [],
  "email_thread_id": null
}.  Metadata: {"chat length#":94}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:48.701304+00:00
[2025-08-19 09:19:48.713] 07:19:48.712 [Python][DEBUG], '_run_once -> _run': [TESTING BOT] Request: Ik heb de volgende mail opgesteld. Wil ....

[2025-08-19 09:19:48.713] Best regards,
[2025-08-19 09:19:48.713] [Your Name]

[2025-08-19 09:19:48.713] -- 
[2025-08-19 09:19:48.713] Mail opgesteld met AskZaira .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:47.577232
[2025-08-19 09:19:48.924] 07:19:48.923 [Python][DEBUG], '_run_once -> _run': [TESTING BOT] Response: Ja, de mail ziet er goed uit. Je kunt deze verzenden. .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:48.713328
[2025-08-19 09:19:48.940] 07:19:48.938 [Python][DEBUG], '_run_once -> _run': [TESTING BOT] Task GUID: aeda2478-67ba-4b5b-835f-ec1cf129e911 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:48.924337
[2025-08-19 09:19:48.951] 07:19:48.949 [Python][DEBUG], '_run_once -> _run': [TESTING BOT] User GUID: 8f02b140-e894-4b44-b2d4-454d89271da6 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:48.941401
[2025-08-19 09:19:48.964] 07:19:48.963 [Python][DEBUG], '_run_once -> _run': [LongRunningZairaRequest] Added 'email' to output_demands via processing_data .  Metadata: {"chat length#":95}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:48.957389
[2025-08-19 09:19:48.971] 07:19:48.971 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Added EmailProcessingData to processing_data. Current demands: ['Testing', 'email'] .  Metadata: {"chat length#":96}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:48.965403
[2025-08-19 09:19:48.977] 07:19:48.975 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] HITL approval request completed .  Metadata: {"chat length#":97}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:48.971387
[2025-08-19 09:19:48.987] 07:19:48.986 [Python][DEBUG], '_run_once -> _run': [EmailGeneratorTool] Returning EmailProcessingData: {'processe..._recipients': [], 'attachments': [], 'email_thread_id': None} .  Metadata: {"chat length#":98}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:48.978389
[2025-08-19 09:19:48.992] 07:19:48.991 [Python][TASK], '_run_once -> _run': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba...ipients": [],
[2025-08-19 09:19:48.992]   "attachments": [],
[2025-08-19 09:19:48.992]   "email_thread_id": null
[2025-08-19 09:19:48.992] }.  Metadata: {"chat length#":99}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:48.987388
[2025-08-19 09:19:49.017] 07:19:49.016 [Python][TASK], '_run_once -> _run': Supervisor top_output_supervisor [scheduled_guid: aeda2478-67b...ipients": [],
[2025-08-19 09:19:49.017]   "attachments": [],
[2025-08-19 09:19:49.017]   "email_thread_id": null
[2025-08-19 09:19:49.017] }.  Metadata: {"chat length#":102}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:49.010388
[2025-08-19 09:19:49.026] 07:19:49.025 [Python][TASK], '_run_once -> _run': Supervisor output_processing_supervisor [scheduled_guid: aeda2...ipients": [],
[2025-08-19 09:19:49.026]   "attachments": [],
[2025-08-19 09:19:49.026]   "email_thread_id": null
[2025-08-19 09:19:49.026] }.  Metadata: {"chat length#":103}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:49.017391
[2025-08-19 09:19:50.790] [GLOBAL_TEST] 07:19:50.790[DEBUG], 'wait_for_assertions -> direct_broadcast_assertion': [TestRealEmail] STEP 3: Checking for broadcasts since 2025-08-19 09:18:46.526802... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:50.790133+00:00
[2025-08-19 09:19:50.791] 07:19:50.790[DEBUG], 'wait_for_assertions -> direct_broadcast_assertion': [TestRealEmail] STEP 3: Checking for broadcasts since 2025-08-19 09:18:46.526802... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:50.790133+00:00
[2025-08-19 09:19:50.855] [GLOBAL_TEST] 07:19:50.855[DEBUG], 'wait_for_assertions -> direct_broadcast_assertion': [TestRealEmail] STEP 3: Found 0 broadcasts, has_broadcast=False .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:50.855152+00:00
[2025-08-19 09:19:50.856] 07:19:50.855[DEBUG], 'wait_for_assertions -> direct_broadcast_assertion': [TestRealEmail] STEP 3: Found 0 broadcasts, has_broadcast=False .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:50.855152+00:00
[2025-08-19 09:19:50.913] [GLOBAL_TEST] 07:19:50.913[DEBUG], 'test_real_email -> wait_for_assertions': [TestRealEmail] Poll 2 (30s/480s): Broadcasts found: 0 since 2025-08-19 09:18:46.526802 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:50.913106+00:00
[2025-08-19 09:19:50.914] 07:19:50.913[DEBUG], 'test_real_email -> wait_for_assertions': [TestRealEmail] Poll 2 (30s/480s): Broadcasts found: 0 since 2025-08-19 09:18:46.526802 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:50.913106+00:00
[2025-08-19 09:19:51.125] 07:19:51.125 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 3: Checking for broadcasts since 2025-08-19 09:18:46.526802... .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:51.106051
[2025-08-19 09:19:51.198] [GLOBAL_TEST] 07:19:51.198[DEBUG], 'llm_call_router_wrapper -> llm_call_router': output_processing_supervisor no always_call tasks found, going to END [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":104}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:51.198054+00:00
[2025-08-19 09:19:51.199] 07:19:51.198[DEBUG], 'llm_call_router_wrapper -> llm_call_router': output_processing_supervisor no always_call tasks found, going to END [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":104}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:51.198054+00:00
[2025-08-19 09:19:51.259] 07:19:51.259 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] STEP 3: Found 0 broadcasts, has_broadcast=False .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:51.125050
[2025-08-19 09:19:51.571] 07:19:51.571 [Python][DEBUG], '_run_once -> _run': [TestRealEmail] Poll 2 (30s/480s): Broadcasts found: 0 since 2025-08-19 09:18:46.526802 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:51.260052
[2025-08-19 09:19:55.469] [GLOBAL_TEST] 07:19:55.469[TASK], 'llm_call -> call_my_tool': tool_call Adding state to tool email_sender_tool. user_guid: 703af770-18d0-4382-af97-96b3d0934291, scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911.  Metadata: {"chat length#":106}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:55.469723+00:00
[2025-08-19 09:19:55.470] 07:19:55.469[TASK], 'llm_call -> call_my_tool': tool_call Adding state to tool email_sender_tool. user_guid: 703af770-18d0-4382-af97-96b3d0934291, scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911.  Metadata: {"chat length#":106}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:55.469723+00:00
[2025-08-19 09:19:56.293] [GLOBAL_TEST] 07:19:56.293[EVENT], 'send_email -> _send_via_smtp': Mail is verzonden via SMTP. .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:56.293683+00:00
[2025-08-19 09:19:56.294] 07:19:56.293[EVENT], 'send_email -> _send_via_smtp': Mail is verzonden via SMTP. .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:56.293683+00:00
[2025-08-19 09:19:56.367] LogFire.log: Invalid chat object. Missing required ZairaChat attributes (session_guid, user_guid, get_user)
[2025-08-19 09:19:56.368] [GLOBAL_TEST] 07:19:56.367[OUTPUT], 'top_output': Task finished with question of length: 881.
[2025-08-19 09:19:56.368] Call trace: 
[2025-08-19 09:19:56.368] top_level_supervisor: start
[2025-08-19 09:19:56.368] top_level_supervisor: goto quick_rag_task (always_call_FIRST)
[2025-08-19 09:19:56.368] quick_rag_task: llm_call
[2025-08-19 09:19:56.368] top_level_supervisor: goto quick_llm_task (always_call_FIRST)
[2025-08-19 09:19:56.368] quick_llm_task: llm_call
[2025-08-19 09:19:56.368] top_level_supervisor: goto quick_complexity_task (always_call_FIRST)
[2025-08-19 09:19:56.368] quick_complexity_task: llm_call
[2025-08-19 09:19:56.368] top_level_supervisor: goto email_generator_task (chosen task)
[2025-08-19 09:19:56.368] email_generator_task: tool email_generator_tool
[2025-08-19 09:19:56.368] top_level_supervisor: goto top_output_supervisor (always_call_LAST)
[2025-08-19 09:19:56.368] output_processing_supervisor: goto END
[2025-08-19 09:19:56.368] email_out: llm_call
[2025-08-19 09:19:56.368] email_out: tool email_sender_tool Question: {
[2025-08-19 09:19:56.368]   "processed_at": "2025-08-19T09:19:42.706809",
[2025-08-19 09:19:56.368]   "user_approved": true,
[2025-08-19 09:19:56.368]   "output_sent": false,
[2025-08-19 09:19:56.368]   "sent_at": null,
[2025-08-19 09:19:56.368]   "processing_metadata": {
[2025-08-19 09:19:56.368]     "generated_at": "2025-08-19T07:19:42.706809+00:00",
[2025-08-19 09:19:56.368]     "user_guid": "703af770-18d0-4382-af97-96b3d0934291"
[2025-08-19 09:19:56.368]   },
[2025-08-19 09:19:56.368]   "subject": "Test Email a6bb7510",
[2025-08-19 09:19:56.368]   "content": "Dear API Team,\n\nI hope this email finds you well. As per your request, I am sending a test <NAME_EMAIL> with the subject 'Test Email a6bb7510'. Please let me know if you receive it successfully.\n\nThank you for your attention to this matter.\n\nBest regards,\n[Your Name]",
[2025-08-19 09:19:56.368]   "sender": "<EMAIL>",
[2025-08-19 09:19:56.368]   "recipient": "<EMAIL>",
[2025-08-19 09:19:56.368]   "content_generated": true,
[2025-08-19 09:19:56.368]   "subject_generated": true,
[2025-08-19 09:19:56.368]   "sender_validated": true,
[2025-08-19 09:19:56.368]   "recipient_validated": true,
[2025-08-19 09:19:56.368]   "cc_recipients": [],
[2025-08-19 09:19:56.368]   "bcc_recipients": [],
[2025-08-19 09:19:56.368]   "attachments": [],
[2025-08-19 09:19:56.368]   "email_thread_id": null
[2025-08-19 09:19:56.368] }.  Metadata: {"chat length#":109}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:56.367546+00:00
[2025-08-19 09:19:56.368] 07:19:56.367[OUTPUT], 'top_output': Task finished with question of length: 881.
Call trace: 
top_level_supervisor: start
top_level_supervisor: goto quick_rag_task (always_call_FIRST)
quick_rag_task: llm_call
top_level_supervisor: goto quick_llm_task (always_call_FIRST)
quick_llm_task: llm_call
top_level_supervisor: goto quick_complexity_task (always_call_FIRST)
quick_complexity_task: llm_call
top_level_supervisor: goto email_generator_task (chosen task)
email_generator_task: tool email_generator_tool
top_level_supervisor: goto top_output_supervisor (always_call_LAST)
output_processing_supervisor: goto END
email_out: llm_call
email_out: tool email_sender_tool Question: {
  "processed_at": "2025-08-19T09:19:42.706809",
  "user_approved": true,
  "output_sent": false,
  "sent_at": null,
  "processing_metadata": {
    "generated_at": "2025-08-19T07:19:42.706809+00:00",
    "user_guid": "703af770-18d0-4382-af97-96b3d0934291"
  },
  "subject": "Test Email a6bb7510",
  "content": "Dear API Team,\n\nI hope this email finds you well. As per your request, I am sending a test <NAME_EMAIL> with the subject 'Test Email a6bb7510'. Please let me know if you receive it successfully.\n\nThank you for your attention to this matter.\n\nBest regards,\n[Your Name]",
  "sender": "<EMAIL>",
  "recipient": "<EMAIL>",
  "content_generated": true,
  "subject_generated": true,
  "sender_validated": true,
  "recipient_validated": true,
  "cc_recipients": [],
  "bcc_recipients": [],
  "attachments": [],
  "email_thread_id": null
}.  Metadata: {"chat length#":109}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:56.367546+00:00
[2025-08-19 09:19:56.452] [GLOBAL_TEST] 07:19:56.452[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  {
[2025-08-19 09:19:56.452]   "processed_at": "2025-08-19T09:19:42.706809",
[2025-08-19 09:19:56.452]   "user_approved": true,
[2025-08-19 09:19:56.452]   "output_sent": false,
[2025-08-19 09:19:56.452]   "sent_at": null,
[2025-08-19 09:19:56.452]   "processing_metadata": {
[2025-08-19 09:19:56.452]     "generated_at": "2025-08-19T07:19:42.706809+00:00",
[2025-08-19 09:19:56.452]     "user_guid": "703af770-18d0-4382-af97-96b3d0934291"
[2025-08-19 09:19:56.452]   },
[2025-08-19 09:19:56.452]   "subject": "Test Email a6bb7510",
[2025-08-19 09:19:56.452]   "content": "Dear API Team,\n\nI hope this email finds you well. As per your request, I am sending a test <NAME_EMAIL> with the subject 'Test Email a6bb7510'. Please let me know if you receive it successfully.\n\nThank you for your attention to this matter.\n\nBest regards,\n[Your Name]",
[2025-08-19 09:19:56.452]   "sender": "<EMAIL>",
[2025-08-19 09:19:56.452]   "recipient": "<EMAIL>",
[2025-08-19 09:19:56.452]   "content_generated": true,
[2025-08-19 09:19:56.452]   "subject_generated": true,
[2025-08-19 09:19:56.452]   "sender_validated": true,
[2025-08-19 09:19:56.452]   "recipient_validated": true,
[2025-08-19 09:19:56.452]   "cc_recipients": [],
[2025-08-19 09:19:56.452]   "bcc_recipients": [],
[2025-08-19 09:19:56.452]   "attachments": [],
[2025-08-19 09:19:56.452]   "email_thread_id": null
[2025-08-19 09:19:56.452] }.  Metadata: {"chat length#":110}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:56.452545+00:00
[2025-08-19 09:19:56.453] 07:19:56.452[TASK], 'ainvoke -> llm_call_router_wrapper': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911]  {
  "processed_at": "2025-08-19T09:19:42.706809",
  "user_approved": true,
  "output_sent": false,
  "sent_at": null,
  "processing_metadata": {
    "generated_at": "2025-08-19T07:19:42.706809+00:00",
    "user_guid": "703af770-18d0-4382-af97-96b3d0934291"
  },
  "subject": "Test Email a6bb7510",
  "content": "Dear API Team,\n\nI hope this email finds you well. As per your request, I am sending a test <NAME_EMAIL> with the subject 'Test Email a6bb7510'. Please let me know if you receive it successfully.\n\nThank you for your attention to this matter.\n\nBest regards,\n[Your Name]",
  "sender": "<EMAIL>",
  "recipient": "<EMAIL>",
  "content_generated": true,
  "subject_generated": true,
  "sender_validated": true,
  "recipient_validated": true,
  "cc_recipients": [],
  "bcc_recipients": [],
  "attachments": [],
  "email_thread_id": null
}.  Metadata: {"chat length#":110}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:56.452545+00:00
[2025-08-19 09:19:56.533] [GLOBAL_TEST] 07:19:56.532[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: always_last [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":111}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:56.532543+00:00
[2025-08-19 09:19:56.533] 07:19:56.532[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow stage: always_last [scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911] .  Metadata: {"chat length#":111}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:56.532543+00:00
[2025-08-19 09:19:56.600] [GLOBAL_TEST] 07:19:56.600[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow complete, going to END .  Metadata: {"chat length#":112}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:56.600547+00:00
[2025-08-19 09:19:56.601] 07:19:56.600[DEBUG], 'llm_call_router_wrapper -> llm_call_router': top_level_supervisor (CoT) workflow complete, going to END .  Metadata: {"chat length#":112}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:56.600547+00:00
[2025-08-19 09:19:56.603] 07:19:56.603 [Python][TASK], '_run_once -> _run': tool_call Adding state to tool email_sender_tool. user_guid: 7...d0934291, scheduled_guid: aeda2478-67ba-4b5b-835f-ec1cf129e911.  Metadata: {"chat length#":107}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:55.558718
[2025-08-19 09:19:56.852] 07:19:56.851 [Python][EVENT], '_run_once -> _run': Mail is verzonden via SMTP. .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:56.603545
[2025-08-19 09:19:56.869] 07:19:56.868 [Python][OUTPUT], 'top_output': Task finished with question of length: 881.
[2025-08-19 09:19:56.869] Call trace: 
[2025-08-19 09:19:56.869] top_l...ipients": [],
[2025-08-19 09:19:56.869]   "attachments": [],
[2025-08-19 09:19:56.869]   "email_thread_id": null
[2025-08-19 09:19:56.869] }.  Metadata: {"chat length#":113}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:56.852555
[2025-08-19 09:19:56.876] 07:19:56.875 [Python][TASK], '_run_once -> _run': Supervisor top_level_supervisor [scheduled_guid: aeda2478-67ba...ipients": [],
[2025-08-19 09:19:56.876]   "attachments": [],
[2025-08-19 09:19:56.876]   "email_thread_id": null
[2025-08-19 09:19:56.876] }.  Metadata: {"chat length#":114}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:56.869654
[2025-08-19 09:19:57.464] [GLOBAL_TEST] 07:19:57.464[DEBUG], 'send_response -> send_reply': MyBot extracted call_trace: ['top_level_supervisor: start', 'top_level_supervisor: goto quick_rag_task (always_call_FIRST)', 'quick_rag_task: llm_call', 'top_level_supervisor: goto quick_llm_task (always_call_FIRST)', 'quick_llm_task: llm_call', 'top_level_supervisor: goto quick_complexity_task (always_call_FIRST)', 'quick_complexity_task: llm_call', 'top_level_supervisor: goto email_generator_task (chosen task)', 'email_generator_task: tool email_generator_tool', 'top_level_supervisor: goto top_output_supervisor (always_call_LAST)', 'output_processing_supervisor: goto END', 'email_out: llm_call', 'email_out: tool email_sender_tool', 'top_level_supervisor: goto END'] .  Metadata: {"chat length#":117}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:57.464230+00:00
[2025-08-19 09:19:57.465] 07:19:57.464[DEBUG], 'send_response -> send_reply': MyBot extracted call_trace: ['top_level_supervisor: start', 'top_level_supervisor: goto quick_rag_task (always_call_FIRST)', 'quick_rag_task: llm_call', 'top_level_supervisor: goto quick_llm_task (always_call_FIRST)', 'quick_llm_task: llm_call', 'top_level_supervisor: goto quick_complexity_task (always_call_FIRST)', 'quick_complexity_task: llm_call', 'top_level_supervisor: goto email_generator_task (chosen task)', 'email_generator_task: tool email_generator_tool', 'top_level_supervisor: goto top_output_supervisor (always_call_LAST)', 'output_processing_supervisor: goto END', 'email_out: llm_call', 'email_out: tool email_sender_tool', 'top_level_supervisor: goto END'] .  Metadata: {"chat length#":117}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:57.464230+00:00
[2025-08-19 09:19:57.537] [GLOBAL_TEST] 07:19:57.537[DEBUG], 'send_response -> send_reply': MyBot final call_trace before message creation: ['top_level_supervisor: start', 'top_level_supervisor: goto quick_rag_task (always_call_FIRST)', 'quick_rag_task: llm_call', 'top_level_supervisor: goto quick_llm_task (always_call_FIRST)', 'quick_llm_task: llm_call', 'top_level_supervisor: goto quick_complexity_task (always_call_FIRST)', 'quick_complexity_task: llm_call', 'top_level_supervisor: goto email_generator_task (chosen task)', 'email_generator_task: tool email_generator_tool', 'top_level_supervisor: goto top_output_supervisor (always_call_LAST)', 'output_processing_supervisor: goto END', 'email_out: llm_call', 'email_out: tool email_sender_tool', 'top_level_supervisor: goto END'] .  Metadata: {"chat length#":118}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:57.537194+00:00
[2025-08-19 09:19:57.538] 07:19:57.537[DEBUG], 'send_response -> send_reply': MyBot final call_trace before message creation: ['top_level_supervisor: start', 'top_level_supervisor: goto quick_rag_task (always_call_FIRST)', 'quick_rag_task: llm_call', 'top_level_supervisor: goto quick_llm_task (always_call_FIRST)', 'quick_llm_task: llm_call', 'top_level_supervisor: goto quick_complexity_task (always_call_FIRST)', 'quick_complexity_task: llm_call', 'top_level_supervisor: goto email_generator_task (chosen task)', 'email_generator_task: tool email_generator_tool', 'top_level_supervisor: goto top_output_supervisor (always_call_LAST)', 'output_processing_supervisor: goto END', 'email_out: llm_call', 'email_out: tool email_sender_tool', 'top_level_supervisor: goto END'] .  Metadata: {"chat length#":118}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:57.537194+00:00
[2025-08-19 09:19:57.600] [GLOBAL_TEST] 07:19:57.600[EVENT], '_handle_response -> remove_request': Removed request aeda2478-67ba-4b5b-835f-ec1cf129e911 from user TestUser. Remaining requests: 0 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:57.600232+00:00
[2025-08-19 09:19:57.601] 07:19:57.600[EVENT], '_handle_response -> remove_request': Removed request aeda2478-67ba-4b5b-835f-ec1cf129e911 from user TestUser. Remaining requests: 0 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:57.600232+00:00
[2025-08-19 09:19:57.859] 07:19:57.858 [Python][DEBUG], '_run_once -> _run': MyBot extracted call_trace: ['top_level_supervisor: start', 't...t: tool email_sender_tool', 'top_level_supervisor: goto END'] .  Metadata: {"chat length#":120}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:57.841200
[2025-08-19 09:19:57.868] 07:19:57.867 [Python][DEBUG], '_run_once -> _run': MyBot final call_trace before message creation: ['top_level_su...t: tool email_sender_tool', 'top_level_supervisor: goto END'] .  Metadata: {"chat length#":121}. User 703af770-18d0-4382-af97-96b3d0934291 on session 8f02b140-e894-4b44-b2d4-454d89271da6 at 2025-08-19 07:19:57.859408
[2025-08-19 09:19:57.875] 07:19:57.873 [Python][EVENT], '_run_once -> _run': Removed request aeda2478-67ba-4b5b-835f-ec1cf129e911 from user TestUser. Remaining requests: 0 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:57.868406
[2025-08-19 09:19:57.950] [GLOBAL_TEST] 07:19:57.950[IMAP EMAIL DETECTED], '_attempt_idle_mode -> _run_idle_cycle': New email: * 286 EXISTS .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:57.950407+00:00
[2025-08-19 09:19:57.950] 07:19:57.950[IMAP EMAIL DETECTED], '_attempt_idle_mode -> _run_idle_cycle': New email: * 286 EXISTS .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:57.950407+00:00
[2025-08-19 09:19:59.106] [GLOBAL_TEST] 07:19:59.105[IMAP PROCESS], '_run_idle_cycle -> _process_specific_email': Re-selected INBOX: ('OK', [b'286']) .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.105962+00:00
[2025-08-19 09:19:59.106] 07:19:59.105[IMAP PROCESS], '_run_idle_cycle -> _process_specific_email': Re-selected INBOX: ('OK', [b'286']) .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.105962+00:00
[2025-08-19 09:19:59.111] [GLOBAL_TEST] 07:19:59.111[IMAP PROCESS], '_run_idle_cycle -> _process_specific_email': Processing specific email #286 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.111969+00:00
[2025-08-19 09:19:59.112] 07:19:59.111[IMAP PROCESS], '_run_idle_cycle -> _process_specific_email': Processing specific email #286 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.111969+00:00
[2025-08-19 09:19:59.256] [GLOBAL_TEST] 07:19:59.256[IMAP PROCESS], '_run_idle_cycle -> _process_specific_email': Fetched email #286: Test Email a6bb7510 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.256971+00:00
[2025-08-19 09:19:59.258] 07:19:59.256[IMAP PROCESS], '_run_idle_cycle -> _process_specific_email': Fetched email #286: Test Email a6bb7510 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.256971+00:00
[2025-08-19 09:19:59.269] [GLOBAL_TEST] 07:19:59.269[IMAP PROCESS], '_process_specific_email -> _process_email': Converted email to vector store: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\src\meltano\output\immediate_email_20250819_091959_Test Email a6bb7510.eml .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.269002+00:00
[2025-08-19 09:19:59.270] 07:19:59.269[IMAP PROCESS], '_process_specific_email -> _process_email': Converted email to vector store: C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\src\meltano\output\immediate_email_20250819_091959_Test Email a6bb7510.eml .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.269002+00:00
[2025-08-19 09:19:59.370] 07:19:59.369 [Python][IMAP IDLE], '_run_once -> _run': Starting IDLE mode (duration: 29.0 minutes) .  Metadata: {"chat length#":76}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:19:59.364506
[2025-08-19 09:19:59.378] 07:19:59.376 [Python][IMAP IDLE], '_run_once -> _run': Attempting IDLE mode .  Metadata: {"chat length#":77}. User 00000000-0000-0000-0000-000000000001 on session 593110e8-961f-4488-bcf1-7234e2a13229 at 2025-08-19 07:19:59.370502
[2025-08-19 09:19:59.384] 07:19:59.384 [Python][IMAP IDLE], '_run_once -> _run': Starting IDLE cycle for 300 seconds .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:59.378507
[2025-08-19 09:19:59.393] 07:19:59.393 [Python][IMAP IDLE], '_run_once -> _run': IDLE started: + IDLE accepted, awaiting DONE command. .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:59.384503
[2025-08-19 09:19:59.411] 07:19:59.411 [Python][IMAP EMAIL DETECTED], '_run_once -> _run': New email: * 286 EXISTS .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:59.394503
[2025-08-19 09:19:59.421] 07:19:59.420 [Python][IMAP PROCESS], '_run_once -> _run': Re-selected INBOX: ('OK', [b'286']) .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:59.412503
[2025-08-19 09:19:59.430] 07:19:59.430 [Python][IMAP PROCESS], '_run_once -> _run': Processing specific email #286 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:59.421505
[2025-08-19 09:19:59.436] 07:19:59.435 [Python][IMAP PROCESS], '_run_once -> _run': Fetched email #286: Test Email a6bb7510 .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:59.430499
[2025-08-19 09:19:59.443] 07:19:59.442 [Python][IMAP PROCESS], '_run_once -> _run': Converted email to vector store: C:\Users\<USER>\Documents\Agen...utput\immediate_email_20250819_091959_Test Email a6bb7510.eml .  Metadata: {"chat length#":-1}. User unknown on session unknown at 2025-08-19 07:19:59.436499
[2025-08-19 09:19:59.836] [GLOBAL_TEST] 07:19:59.836[OUTPUT], 'send_broadcast -> send_broadcast': Testing bot broadcast: New email received: b'Received: from DESKTOP-G31KM0N.Ecotax.local ([***************]) by askzaira.co... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.836702+00:00
[2025-08-19 09:19:59.837] 07:19:59.836[OUTPUT], 'send_broadcast -> send_broadcast': Testing bot broadcast: New email received: b'Received: from DESKTOP-G31KM0N.Ecotax.local ([***************]) by askzaira.co... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.836702+00:00
[2025-08-19 09:19:59.848] [GLOBAL_TEST] 07:19:59.848[OUTPUT], 'send_broadcast -> send_broadcast': Testing bot broadcast: New email received: b'Received: from DESKTOP-G31KM0N.Ecotax.local ([***************]) by askzaira.com with\r\n MailEnable ESMTPA; Tue, 19 Aug 2025 09:19:54 +0200\r\nContent-Type: multipart/mixed; boun... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.848652+00:00
[2025-08-19 09:19:59.849] 07:19:59.848[OUTPUT], 'send_broadcast -> send_broadcast': Testing bot broadcast: New email received: b'Received: from DESKTOP-G31KM0N.Ecotax.local ([***************]) by askzaira.com with\r\n MailEnable ESMTPA; Tue, 19 Aug 2025 09:19:54 +0200\r\nContent-Type: multipart/mixed; boun... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.848652+00:00
[2025-08-19 09:19:59.856] [GLOBAL_TEST] 07:19:59.856[DEBUG], 'send_broadcast_ALL -> send_broadcast': [TESTING BOT] Broadcast captured for testing: New email received: b'Received: from DESKTOP-G31KM0N.Ecotax.local ([***************]) by askzaira.co... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.856665+00:00
[2025-08-19 09:19:59.857] 07:19:59.856[DEBUG], 'send_broadcast_ALL -> send_broadcast': [TESTING BOT] Broadcast captured for testing: New email received: b'Received: from DESKTOP-G31KM0N.Ecotax.local ([***************]) by askzaira.co... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.856665+00:00
[2025-08-19 09:19:59.868] [GLOBAL_TEST] 07:19:59.868[OUTPUT], '_process_email -> send_broadcast_ALL': Sent email notification to Testing bot for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":4}. User 00000000-0000-0000-0000-000000000001 on session 294522a2-4bd7-4266-8a20-9ffa916cd4cb at 2025-08-19 07:19:59.868656+00:00
[2025-08-19 09:19:59.868] 07:19:59.868[OUTPUT], '_process_email -> send_broadcast_ALL': Sent email notification to Testing bot for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":4}. User 00000000-0000-0000-0000-000000000001 on session 294522a2-4bd7-4266-8a20-9ffa916cd4cb at 2025-08-19 07:19:59.868656+00:00
[2025-08-19 09:19:59.875] [GLOBAL_TEST] 07:19:59.875[TASK], '_process_email -> send_broadcast_ALL': Email notification broadcast completed for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":5}. User 00000000-0000-0000-0000-000000000001 on session 294522a2-4bd7-4266-8a20-9ffa916cd4cb at 2025-08-19 07:19:59.875653+00:00
[2025-08-19 09:19:59.875] 07:19:59.875[TASK], '_process_email -> send_broadcast_ALL': Email notification broadcast completed for user 00000000-0000-0000-0000-000000000001 .  Metadata: {"chat length#":5}. User 00000000-0000-0000-0000-000000000001 on session 294522a2-4bd7-4266-8a20-9ffa916cd4cb at 2025-08-19 07:19:59.875653+00:00
[2025-08-19 09:19:59.881] [GLOBAL_TEST] 07:19:59.881[IMAP PROCESS], '_run_idle_cycle -> _process_specific_email': Successfully processed email #286: Test Email a6bb7510 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.881648+00:00
[2025-08-19 09:19:59.882] 07:19:59.881[IMAP PROCESS], '_run_idle_cycle -> _process_specific_email': Successfully processed email #286: Test Email a6bb7510 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:19:59.881648+00:00
[2025-08-19 09:20:20.884] [GLOBAL_TEST] 07:20:20.884[DEBUG], 'wait_for_assertions -> direct_broadcast_assertion': [TestRealEmail] STEP 3: Checking for broadcasts since 2025-08-19 09:18:46.526802... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:20.884583+00:00
[2025-08-19 09:20:20.885] 07:20:20.884[DEBUG], 'wait_for_assertions -> direct_broadcast_assertion': [TestRealEmail] STEP 3: Checking for broadcasts since 2025-08-19 09:18:46.526802... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:20.884583+00:00
[2025-08-19 09:20:20.957] [GLOBAL_TEST] 07:20:20.957[DEBUG], 'wait_for_assertions -> direct_broadcast_assertion': [TestRealEmail] STEP 3: Found 1 broadcasts, has_broadcast=True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:20.957587+00:00
[2025-08-19 09:20:20.957] 07:20:20.957[DEBUG], 'wait_for_assertions -> direct_broadcast_assertion': [TestRealEmail] STEP 3: Found 1 broadcasts, has_broadcast=True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:20.957587+00:00
[2025-08-19 09:20:21.023] [GLOBAL_TEST] 07:20:21.023[DEBUG], 'test_real_email -> wait_for_assertions': [TestRealEmail] Poll 3 (60s/480s): Broadcasts found: 1 since 2025-08-19 09:18:46.526802 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.023586+00:00
[2025-08-19 09:20:21.024] 07:20:21.023[DEBUG], 'test_real_email -> wait_for_assertions': [TestRealEmail] Poll 3 (60s/480s): Broadcasts found: 1 since 2025-08-19 09:18:46.526802 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.023586+00:00
[2025-08-19 09:20:21.099] [GLOBAL_TEST] 07:20:21.099[DEBUG], 'test_real_email -> wait_for_assertions': [TestRealEmail] Assertion 'direct_broadcast_detection' passed after 60.1s (3 polls) .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.099624+00:00
[2025-08-19 09:20:21.099] 07:20:21.099[DEBUG], 'test_real_email -> wait_for_assertions': [TestRealEmail] Assertion 'direct_broadcast_detection' passed after 60.1s (3 polls) .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.099624+00:00
[2025-08-19 09:20:21.177] [GLOBAL_TEST] 07:20:21.177[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3: Broadcast polling completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.177592+00:00
[2025-08-19 09:20:21.178] 07:20:21.177[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3: Broadcast polling completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.177592+00:00
[2025-08-19 09:20:21.243] [GLOBAL_TEST] 07:20:21.243[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3: Broadcast detection result: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.243592+00:00
[2025-08-19 09:20:21.245] 07:20:21.243[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3: Broadcast detection result: True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.243592+00:00
[2025-08-19 09:20:21.356] [GLOBAL_TEST] 07:20:21.355[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3: [OK] Broadcast detected after 60.1s (3 polls) .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.355669+00:00
[2025-08-19 09:20:21.356] 07:20:21.355[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3: [OK] Broadcast detected after 60.1s (3 polls) .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.355669+00:00
[2025-08-19 09:20:21.507] [GLOBAL_TEST] 07:20:21.507[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3: Total broadcasts received: 0 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.507137+00:00
[2025-08-19 09:20:21.509] 07:20:21.507[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3: Total broadcasts received: 0 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.507137+00:00
[2025-08-19 09:20:21.621] [GLOBAL_TEST] 07:20:21.621[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ===== FINAL TEST SUMMARY ===== .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.621445+00:00
[2025-08-19 09:20:21.621] 07:20:21.621[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ===== FINAL TEST SUMMARY ===== .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.621445+00:00
[2025-08-19 09:20:21.719] [GLOBAL_TEST] 07:20:21.710[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1 - IMAP exists: [OK] .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.710503+00:00
[2025-08-19 09:20:21.720] 07:20:21.710[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1 - IMAP exists: [OK] .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.710503+00:00
[2025-08-19 09:20:21.802] [GLOBAL_TEST] 07:20:21.802[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5 - IMAP activation wait: [OK] .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.802476+00:00
[2025-08-19 09:20:21.803] 07:20:21.802[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 1.5 - IMAP activation wait: [OK] .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.802476+00:00
[2025-08-19 09:20:21.882] [GLOBAL_TEST] 07:20:21.882[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2 - Email sent: [OK] .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.882437+00:00
[2025-08-19 09:20:21.883] 07:20:21.882[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 2 - Email sent: [OK] .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.882437+00:00
[2025-08-19 09:20:21.969] [GLOBAL_TEST] 07:20:21.969[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3 - Broadcast reached: [OK] .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.969481+00:00
[2025-08-19 09:20:21.970] 07:20:21.969[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] STEP 3 - Broadcast reached: [OK] .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:21.969481+00:00
[2025-08-19 09:20:22.032] [GLOBAL_TEST] 07:20:22.032[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] Final broadcast count: 1 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.032439+00:00
[2025-08-19 09:20:22.033] 07:20:22.032[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] Final broadcast count: 1 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.032439+00:00
[2025-08-19 09:20:22.098] [GLOBAL_TEST] 07:20:22.097[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ===== RUNNING FINAL ASSERTIONS ===== .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.097446+00:00
[2025-08-19 09:20:22.099] 07:20:22.097[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ===== RUNNING FINAL ASSERTIONS ===== .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.097446+00:00
[2025-08-19 09:20:22.184] [GLOBAL_TEST] 07:20:22.184[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] Assertion 1: IMAP task exists = True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.184553+00:00
[2025-08-19 09:20:22.185] 07:20:22.184[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] Assertion 1: IMAP task exists = True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.184553+00:00
[2025-08-19 09:20:22.250] [GLOBAL_TEST] 07:20:22.250[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] Assertion 2: Email sent = True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.250552+00:00
[2025-08-19 09:20:22.251] 07:20:22.250[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] Assertion 2: Email sent = True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.250552+00:00
[2025-08-19 09:20:22.303] [GLOBAL_TEST] 07:20:22.303[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] Assertion 3: Broadcast reached = True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.303584+00:00
[2025-08-19 09:20:22.304] 07:20:22.303[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] Assertion 3: Broadcast reached = True .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.303584+00:00
[2025-08-19 09:20:22.372] [GLOBAL_TEST] 07:20:22.371[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ===== TEST COMPLETED SUCCESSFULLY ===== .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.371740+00:00
[2025-08-19 09:20:22.372] 07:20:22.371[DEBUG], 'wrapper -> test_real_email': [TestRealEmail] ===== TEST COMPLETED SUCCESSFULLY ===== .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.371740+00:00
[2025-08-19 09:20:22.436] [GLOBAL_TEST] 07:20:22.436[DEBUG], '_run -> wrapper': [TestRealEmail] Performing server cleanup... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.436721+00:00
[2025-08-19 09:20:22.436] 07:20:22.436[DEBUG], '_run -> wrapper': [TestRealEmail] Performing server cleanup... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.436721+00:00
[2025-08-19 09:20:22.535] [GLOBAL_TEST] 07:20:22.535[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Starting comprehensive server cleanup... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.535339+00:00
[2025-08-19 09:20:22.536] 07:20:22.535[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Starting comprehensive server cleanup... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.535339+00:00
[2025-08-19 09:20:22.621] [GLOBAL_TEST] 07:20:22.621[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Found 1 servers in global registry .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.621341+00:00
[2025-08-19 09:20:22.622] 07:20:22.621[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Found 1 servers in global registry .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.621341+00:00
[2025-08-19 09:20:22.708] [GLOBAL_TEST] 07:20:22.708[DEBUG], 'cleanup_servers -> cleanup_all_servers': Cleaning up 1 registered servers... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.708934+00:00
[2025-08-19 09:20:22.709] 07:20:22.708[DEBUG], 'cleanup_servers -> cleanup_all_servers': Cleaning up 1 registered servers... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.708934+00:00
[2025-08-19 09:20:22.798] [GLOBAL_TEST] 07:20:22.798[DEBUG], 'cleanup_servers -> cleanup_all_servers': Stopped site for APIEndpoint_0.0.0.0_40999 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.798966+00:00
[2025-08-19 09:20:22.799] 07:20:22.798[DEBUG], 'cleanup_servers -> cleanup_all_servers': Stopped site for APIEndpoint_0.0.0.0_40999 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:22.798966+00:00
[2025-08-19 09:20:24.810] [GLOBAL_TEST] 07:20:24.810[DEBUG], 'cleanup_servers -> cleanup_all_servers': Cleaned up runner for APIEndpoint_0.0.0.0_40999 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:24.810613+00:00
[2025-08-19 09:20:24.811] 07:20:24.810[DEBUG], 'cleanup_servers -> cleanup_all_servers': Cleaned up runner for APIEndpoint_0.0.0.0_40999 .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:24.810613+00:00
[2025-08-19 09:20:24.916] [GLOBAL_TEST] 07:20:24.916[DEBUG], 'cleanup_servers -> cleanup_all_servers': Server cleanup completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:24.916626+00:00
[2025-08-19 09:20:24.917] 07:20:24.916[DEBUG], 'cleanup_servers -> cleanup_all_servers': Server cleanup completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:24.916626+00:00
[2025-08-19 09:20:25.013] [GLOBAL_TEST] 07:20:25.013[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Global server cleanup completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:25.013353+00:00
[2025-08-19 09:20:25.014] 07:20:25.013[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Global server cleanup completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:25.013353+00:00
[2025-08-19 09:20:25.125] [GLOBAL_TEST] 07:20:25.125[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Running final port cleanup check... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:25.125345+00:00
[2025-08-19 09:20:25.126] 07:20:25.125[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Running final port cleanup check... .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:25.125345+00:00
[2025-08-19 09:20:25.247] [GLOBAL_TEST] 07:20:25.247[DEBUG], '_check_and_cleanup_ports -> _log_compact': [TR] Checking ports: [40999, 8083] .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:25.247047+00:00
[2025-08-19 09:20:25.249] 07:20:25.247[DEBUG], '_check_and_cleanup_ports -> _log_compact': [TR] Checking ports: [40999, 8083] .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:25.247047+00:00
[2025-08-19 09:20:27.385] [GLOBAL_TEST] 07:20:27.385[DEBUG], '_check_and_cleanup_single_port -> _log_compact': [TR] Port 40999 (ZAIRA_PYTHON_PORT) is available .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:27.385574+00:00
[2025-08-19 09:20:27.387] 07:20:27.385[DEBUG], '_check_and_cleanup_single_port -> _log_compact': [TR] Port 40999 (ZAIRA_PYTHON_PORT) is available .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:27.385574+00:00
[2025-08-19 09:20:29.509] [GLOBAL_TEST] 07:20:29.509[DEBUG], '_check_and_cleanup_single_port -> _log_compact': [TR] Port 8083 (RELAY_PORT) is available .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:29.509528+00:00
[2025-08-19 09:20:29.511] 07:20:29.509[DEBUG], '_check_and_cleanup_single_port -> _log_compact': [TR] Port 8083 (RELAY_PORT) is available .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:29.509528+00:00
[2025-08-19 09:20:29.624] [GLOBAL_TEST] 07:20:29.624[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Port cleanup completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:29.624520+00:00
[2025-08-19 09:20:29.625] 07:20:29.624[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Port cleanup completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:29.624520+00:00
[2025-08-19 09:20:29.742] [GLOBAL_TEST] 07:20:29.742[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Comprehensive server cleanup completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:29.742518+00:00
[2025-08-19 09:20:29.743] 07:20:29.742[DEBUG], 'wrapper -> cleanup_servers': [TestRealEmail] Comprehensive server cleanup completed .  Metadata: {"chat length#":-1}. User None on session None at 2025-08-19 07:20:29.742518+00:00

================================================================================
=== TEST DEBUG TRACE COMPLETED ===
Completed: 2025-08-19 09:20:31.744556
[2025-08-19 09:20:31.744] [TEST_ENV] Allowing background tasks 5 seconds to complete logging...
[2025-08-19 09:20:36.744] [TEST_ENV] Global test environment cleared
[2025-08-19 09:20:36.744] [TEST_ENV] Global test environment cleared
