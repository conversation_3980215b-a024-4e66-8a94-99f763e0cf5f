"""
Comprehensive unit tests for RetrievalManager
This test suite achieves 90%+ coverage for retrieval and embedding functionality
"""

import sys
import os
import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from typing import Dict, List, Any
import numpy as np
import json

# Add project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from imports import *
from managers.manager_retrieval import RetrievalManager

class TestRetrievalManagerSingleton:
    """Test RetrievalManager singleton pattern"""
    
    def test_retrieval_manager_singleton_creation(self):
        """Test RetrievalManager singleton creation"""
        # Reset singleton for test
        RetrievalManager._instance = None
        RetrievalManager._initialized = False
        
        manager1 = RetrievalManager()
        manager2 = RetrievalManager()
        
        # Should be same instance
        assert manager1 is manager2
        assert isinstance(manager1, RetrievalManager)
    
    def test_retrieval_manager_get_instance(self):
        """Test RetrievalManager get_instance method"""
        # Reset singleton for test
        RetrievalManager._instance = None
        RetrievalManager._initialized = False
        
        manager1 = RetrievalManager.get_instance()
        manager2 = RetrievalManager.get_instance()
        
        # Should be same instance
        assert manager1 is manager2
        assert isinstance(manager1, RetrievalManager)
    
    def test_retrieval_manager_initialization_state(self):
        """Test RetrievalManager initialization state"""
        # Reset singleton for test
        RetrievalManager._instance = None
        RetrievalManager._initialized = False
        
        manager = RetrievalManager.get_instance()
        
        # Should start uninitialized
        assert manager._initialized == False
    
    @pytest.mark.asyncio
    async def test_retrieval_manager_setup(self):
        """Test RetrievalManager setup process"""
        # Reset singleton for test
        RetrievalManager._instance = None
        RetrievalManager._initialized = False
        
        with patch('onnxruntime.InferenceSession') as mock_session, \
             patch('transformers.AutoTokenizer.from_pretrained') as mock_tokenizer, \
             patch('managers.manager_retrieval.BASE_DIR') as mock_base_dir:
            
            # Mock the model path
            mock_base_dir.return_value = Mock()
            mock_base_dir.return_value.__truediv__ = Mock(return_value="mock_path")
            
            mock_session.return_value = Mock()
            mock_tokenizer.return_value = Mock()
            
            await RetrievalManager.setup()
            
            # Should be initialized
            manager = RetrievalManager.get_instance()
            assert manager._initialized == True
            assert manager.session is not None
            assert manager.tokenizer is not None
    
    @pytest.mark.asyncio
    async def test_retrieval_manager_setup_idempotent(self):
        """Test RetrievalManager setup is idempotent"""
        # Reset singleton for test
        RetrievalManager._instance = None
        RetrievalManager._initialized = False
        
        with patch('onnxruntime.InferenceSession') as mock_session, \
             patch('transformers.AutoTokenizer.from_pretrained') as mock_tokenizer, \
             patch('managers.manager_retrieval.BASE_DIR') as mock_base_dir:
            
            mock_base_dir.return_value = Mock()
            mock_base_dir.return_value.__truediv__ = Mock(return_value="mock_path")
            mock_session.return_value = Mock()
            mock_tokenizer.return_value = Mock()
            
            # Setup multiple times
            await RetrievalManager.setup()
            await RetrievalManager.setup()
            await RetrievalManager.setup()
            
            # Should only initialize once
            assert mock_session.call_count == 1
            assert mock_tokenizer.call_count == 1
            
            manager = RetrievalManager.get_instance()
            assert manager._initialized == True

class TestRetrievalManagerDataProcessing:
    """Test RetrievalManager data processing functionality"""
    
    @pytest.mark.asyncio
    async def test_json_to_dict_string_input(self):
        """Test json_to_dict with string input"""
        json_string = '{"key1": "value1", "key2": {"nested": "value2"}}'
        
        result = await RetrievalManager.json_to_dict(json_string)
        
        assert isinstance(result, list)
        assert len(result) > 0
        
        # Should contain flattened elements
        ids = [item["id"] for item in result]
        assert "key1" in ids
        assert "key2.nested" in ids
    
    @pytest.mark.asyncio
    async def test_json_to_dict_dict_input(self):
        """Test json_to_dict with dictionary input"""
        data = {
            "simple": "value",
            "nested": {
                "inner": "nested_value"
            },
            "list_data": ["item1", "item2"]
        }
        
        result = await RetrievalManager.json_to_dict(data)
        
        assert isinstance(result, list)
        assert len(result) > 0
        
        # Verify flattened structure
        result_dict = {item["id"]: item["text"] for item in result}
        assert "simple" in result_dict
        assert "nested.inner" in result_dict
        assert "list_data[0]" in result_dict
        assert "list_data[1]" in result_dict
    
    @pytest.mark.asyncio
    async def test_json_to_dict_list_input(self):
        """Test json_to_dict with list input"""
        data = ["item1", "item2", {"nested": "value"}]
        
        result = await RetrievalManager.json_to_dict(data)
        
        assert isinstance(result, list)
        assert len(result) > 0
        
        # Should handle list indices
        ids = [item["id"] for item in result]
        assert "[0]" in ids
        assert "[1]" in ids
        assert "[2].nested" in ids
    
    @pytest.mark.asyncio
    async def test_json_to_dict_nested_structure(self):
        """Test json_to_dict with deeply nested structure"""
        data = {
            "level1": {
                "level2": {
                    "level3": "deep_value"
                }
            }
        }
        
        result = await RetrievalManager.json_to_dict(data)
        
        assert isinstance(result, list)
        result_dict = {item["id"]: item["text"] for item in result}
        assert "level1.level2.level3" in result_dict
        assert result_dict["level1.level2.level3"] == "deep_value"
    
    @pytest.mark.asyncio
    async def test_unstructured_to_list(self):
        """Test unstructured_to_list functionality"""
        # Mock unstructured elements
        mock_elements = [
            Mock(id="elem1", metadata=Mock(to_dict=Mock(return_value={"type": "text"}))),
            Mock(id="elem2", metadata="simple_metadata")
        ]
        
        # Configure string representation
        mock_elements[0].__str__ = Mock(return_value="Element 1 content")
        mock_elements[1].__str__ = Mock(return_value="Element 2 content")
        
        result = await RetrievalManager.unstructured_to_list(mock_elements)
        
        assert isinstance(result, list)
        assert len(result) == 2
        
        # Verify structure
        assert result[0]["id"] == "elem1"
        assert result[0]["text"] == "Element 1 content"
        assert result[0]["metadata"] == {"type": "text"}
        
        assert result[1]["id"] == "elem2"
        assert result[1]["text"] == "Element 2 content"
        assert result[1]["metadata"] == "simple_metadata"
    
    def test_safe_get_dict(self):
        """Test safe_get with dictionary"""
        data = {"key1": "value1", "key2": None}
        
        assert RetrievalManager.safe_get(data, "key1") == "value1"
        assert RetrievalManager.safe_get(data, "key2") is None
        assert RetrievalManager.safe_get(data, "nonexistent", "default") == "default"
    
    def test_safe_get_object(self):
        """Test safe_get with object"""
        obj = Mock()
        obj.attr1 = "value1"
        obj.attr2 = None
        
        assert RetrievalManager.safe_get(obj, "attr1") == "value1"
        assert RetrievalManager.safe_get(obj, "attr2") is None
        assert RetrievalManager.safe_get(obj, "nonexistent", "default") == "default"
    
    def test_safe_get_exception_handling(self):
        """Test safe_get exception handling"""
        # Object that raises exception on attribute access
        class ProblematicObject:
            def __getattribute__(self, name):
                if name == "problematic":
                    raise AttributeError("Simulated error")
                return super().__getattribute__(name)
        
        obj = ProblematicObject()
        result = RetrievalManager.safe_get(obj, "problematic", "default")
        assert result == "default"
    
    @pytest.mark.asyncio
    async def test_list_to_dict(self):
        """Test list_to_dict functionality"""
        input_list = [
            {"id": "key1", "text": "value1"},
            {"id": "key2", "text": "value2"},
            Mock(id="key3", text="value3")
        ]
        
        result = await RetrievalManager.list_to_dict(input_list)
        
        assert isinstance(result, dict)
        assert result["key1"] == "value1"
        assert result["key2"] == "value2"
        assert result["key3"] == "value3"
    
    @pytest.mark.asyncio
    async def test_elements_to_markdown_list(self):
        """Test elements_to_markdown with list input"""
        data = [
            {"title": "Title 1", "content": "Content 1"},
            {"title": "Title 2", "content": "Content 2"}
        ]
        
        result = await RetrievalManager.elements_to_markdown(data, "# Prefix\n")
        
        assert isinstance(result, str)
        assert "# Prefix" in result
        assert "**title**:" in result
        assert "**content**:" in result
        assert "Title 1" in result
        assert "Content 1" in result
    
    @pytest.mark.asyncio
    async def test_elements_to_markdown_dict(self):
        """Test elements_to_markdown with dict input"""
        data = {"title": "Test Title", "content": "Test Content"}
        
        result = await RetrievalManager.elements_to_markdown(data)
        
        assert isinstance(result, str)
        assert "**title**:" in result
        assert "**content**:" in result
        assert "Test Title" in result
        assert "Test Content" in result

class TestRetrievalManagerEmbeddings:
    """Test RetrievalManager embedding functionality"""
    
    @pytest.fixture
    async def setup_manager(self):
        """Setup RetrievalManager for testing"""
        # Reset singleton for test
        RetrievalManager._instance = None
        RetrievalManager._initialized = False
        
        with patch('onnxruntime.InferenceSession') as mock_session, \
             patch('transformers.AutoTokenizer.from_pretrained') as mock_tokenizer, \
             patch('managers.manager_retrieval.BASE_DIR') as mock_base_dir:
            
            mock_base_dir.return_value = Mock()
            mock_base_dir.return_value.__truediv__ = Mock(return_value="mock_path")
            
            # Setup mock tokenizer
            mock_tokenizer_instance = Mock()
            mock_tokenizer_instance.return_value = {
                "input_ids": np.array([[1, 2, 3, 4]]),
                "attention_mask": np.array([[1, 1, 1, 1]]),
                "token_type_ids": np.array([[0, 0, 0, 0]])
            }
            mock_tokenizer.return_value = mock_tokenizer_instance
            
            # Setup mock session
            mock_session_instance = Mock()
            mock_session_instance.run.return_value = [np.array([[0.1, 0.2, 0.3, 0.4]])]
            mock_session.return_value = mock_session_instance
            
            await RetrievalManager.setup()
            
            yield RetrievalManager.get_instance(), mock_tokenizer_instance, mock_session_instance
    
    @pytest.mark.asyncio
    async def test_get_embeddings_dense_string_input(self, setup_manager):
        """Test get_embeddings_dense with string input"""
        manager, mock_tokenizer, mock_session = setup_manager
        
        with patch('managers.manager_retrieval.EMBEDDING_SIZE', 512), \
             patch('managers.manager_retrieval.ZairaSettings') as mock_settings:
            
            # Mock ZairaSettings
            mock_embed_model = Mock()
            mock_embed_model.get_text_embedding.return_value = [0.5, 0.6, 0.7, 0.8]
            mock_settings.OllamaSettings.return_value.embed_model = mock_embed_model
            
            result = await RetrievalManager.get_embeddings_dense("test text")
            
            assert isinstance(result, list)
            assert len(result) == 4
            assert result == [0.5, 0.6, 0.7, 0.8]
    
    @pytest.mark.asyncio
    async def test_get_embeddings_dense_list_input(self, setup_manager):
        """Test get_embeddings_dense with list input"""
        manager, mock_tokenizer, mock_session = setup_manager
        
        with patch('managers.manager_retrieval.EMBEDDING_SIZE', 512), \
             patch('managers.manager_retrieval.ZairaSettings') as mock_settings:
            
            # Mock ZairaSettings for multiple texts
            mock_embed_model = Mock()
            mock_embed_model.get_text_embedding.side_effect = [
                [0.1, 0.2], [0.3, 0.4]
            ]
            mock_settings.OllamaSettings.return_value.embed_model = mock_embed_model
            
            result = await RetrievalManager.get_embeddings_dense(["text1", "text2"])
            
            # Should return first embedding (as per implementation)
            assert isinstance(result, list)
            assert result == [0.1, 0.2]
    
    @pytest.mark.asyncio
    async def test_get_embeddings_dense_invalid_input(self, setup_manager):
        """Test get_embeddings_dense with invalid input"""
        manager, mock_tokenizer, mock_session = setup_manager
        
        with patch('managers.manager_retrieval.EMBEDDING_SIZE', 512):
            
            # Should raise ValueError for invalid input
            with pytest.raises(ValueError) as exc_info:
                await RetrievalManager.get_embeddings_dense(123)
            
            assert "Input must be a string or list of strings" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_get_embeddings_dense_numpy_conversion(self, setup_manager):
        """Test get_embeddings_dense numpy array conversion"""
        manager, mock_tokenizer, mock_session = setup_manager
        
        with patch('managers.manager_retrieval.EMBEDDING_SIZE', 512), \
             patch('managers.manager_retrieval.ZairaSettings') as mock_settings:
            
            # Mock ZairaSettings with numpy array
            mock_embed_model = Mock()
            mock_embed_model.get_text_embedding.return_value = np.array([0.1, 0.2, 0.3])
            mock_settings.OllamaSettings.return_value.embed_model = mock_embed_model
            
            result = await RetrievalManager.get_embeddings_dense("test")
            
            # Should convert numpy array to list
            assert isinstance(result, list)
            assert result == [0.1, 0.2, 0.3]
    
    @pytest.mark.asyncio
    async def test_get_embeddings_sparse(self):
        """Test get_embeddings_sparse functionality"""
        with patch('managers.manager_retrieval.ZairaSettings') as mock_settings:
            
            # Mock sparse embedding model
            mock_sparse_model = Mock()
            mock_sparse_model.embed.return_value = [[0.1, 0.0, 0.3, 0.0, 0.5]]
            mock_settings.sparse_embed_model = mock_sparse_model
            
            result = await RetrievalManager.get_embeddings_sparse("test text")
            
            assert isinstance(result, list)
            assert result == [0.1, 0.0, 0.3, 0.0, 0.5]

class TestRetrievalManagerChunking:
    """Test RetrievalManager text chunking functionality"""
    
    @pytest.mark.asyncio
    async def test_chunk_text_string_input(self):
        """Test chunk_text with string input"""
        long_text = "This is a test sentence. " * 100  # Create long text
        
        with patch('managers.manager_retrieval.CHUNK_SIZE', 100), \
             patch('managers.manager_retrieval.CHUNK_OVERLAP', 20):
            
            result = await RetrievalManager.chunk_text(long_text)
            
            assert isinstance(result, list)
            assert len(result) > 1  # Should create multiple chunks
            
            # Each chunk should be reasonable size
            for chunk in result:
                assert isinstance(chunk, str)
                assert len(chunk) > 0
    
    @pytest.mark.asyncio
    async def test_chunk_text_list_input(self):
        """Test chunk_text with list input"""
        text_list = ["First paragraph.", "Second paragraph.", "Third paragraph."]
        
        with patch('managers.manager_retrieval.CHUNK_SIZE', 50), \
             patch('managers.manager_retrieval.CHUNK_OVERLAP', 10):
            
            result = await RetrievalManager.chunk_text(text_list)
            
            assert isinstance(result, list)
            assert len(result) > 0
            
            # Should combine list items
            combined_text = "\n\n".join(result)
            for item in text_list:
                assert item in combined_text
    
    @pytest.mark.asyncio
    async def test_chunk_text_overlap_validation(self):
        """Test chunk_text overlap validation"""
        text = "Short text that won't need chunking."
        
        # Test with overlap larger than chunk size
        with patch('managers.manager_retrieval.CHUNK_SIZE', 50), \
             patch('managers.manager_retrieval.CHUNK_OVERLAP', 100):
            
            result = await RetrievalManager.chunk_text(text)
            
            # Should handle invalid overlap gracefully
            assert isinstance(result, list)
            assert len(result) > 0
    
    @pytest.mark.asyncio
    async def test_chunk_text_custom_parameters(self):
        """Test chunk_text with custom parameters"""
        text = "Custom chunk size test. " * 20
        
        result = await RetrievalManager.chunk_text(text, chunk_size=100, chunk_overlap=25)
        
        assert isinstance(result, list)
        assert len(result) > 0
        
        # Verify chunks respect size limits
        for chunk in result:
            assert len(chunk) <= 150  # Allow some flexibility for word boundaries
    
    @pytest.mark.asyncio
    async def test_chunk_text_non_string_input(self):
        """Test chunk_text with non-string input"""
        data = {"key": "value", "number": 123}
        
        result = await RetrievalManager.chunk_text(data)
        
        assert isinstance(result, list)
        assert len(result) > 0
        
        # Should convert to string representation
        chunk_text = " ".join(result)
        assert "key" in chunk_text or str(data) in chunk_text
    
    @pytest.mark.asyncio
    async def test_chunk_multimodal_elements(self):
        """Test chunk_multimodal_elements functionality"""
        multimodal_data = {
            "all_elements": [
                {
                    "type": "NarrativeText",
                    "text": "This is regular text content."
                },
                {
                    "type": "Image",
                    "text": "",
                    "summary": "A chart showing data trends"
                },
                {
                    "type": "Table",
                    "text": "",
                    "summary": "Financial data table",
                    "markdown": "| Column 1 | Column 2 |\n|----------|----------|\n| Data 1   | Data 2   |"
                },
                {
                    "type": "NarrativeText",
                    "text": "Text following the table."
                }
            ]
        }
        
        with patch('managers.manager_retrieval.CHUNK_SIZE', 200), \
             patch('managers.manager_retrieval.CHUNK_OVERLAP', 50):
            
            result = await RetrievalManager.chunk_multimodal_elements(multimodal_data)
            
            assert isinstance(result, list)
            assert len(result) > 0
            
            # Should preserve multimodal markers
            combined_text = " ".join(result)
            assert "[IMAGE:" in combined_text
            assert "[TABLE:" in combined_text
            assert "chart showing data trends" in combined_text.lower()
    
    @pytest.mark.asyncio
    async def test_chunk_with_multimodal_awareness(self):
        """Test _chunk_with_multimodal_awareness functionality"""
        text = """Regular text paragraph.

[IMAGE: Test image content]

More regular text.

[TABLE: Test table]
| A | B |
|---|---|
| 1 | 2 |

Final paragraph."""
        
        result = await RetrievalManager._chunk_with_multimodal_awareness(text, 100, 20)
        
        assert isinstance(result, list)
        assert len(result) > 0
        
        # Should preserve multimodal boundaries
        combined_text = " ".join(result)
        assert "[IMAGE:" in combined_text
        assert "[TABLE:" in combined_text
    
    @pytest.mark.asyncio
    async def test_split_large_multimodal_paragraph(self):
        """Test _split_large_multimodal_paragraph functionality"""
        large_paragraph = "This is a very long sentence with multimodal content. " * 20
        
        result = await RetrievalManager._split_large_multimodal_paragraph(large_paragraph, 100)
        
        assert isinstance(result, list)
        assert len(result) > 1
        
        # Each chunk should be reasonably sized
        for chunk in result:
            assert isinstance(chunk, str)
            assert len(chunk) <= 120  # Allow some flexibility
    
    @pytest.mark.asyncio
    async def test_apply_overlap_to_chunks(self):
        """Test _apply_overlap_to_chunks functionality"""
        chunks = [
            "First chunk content goes here.",
            "Second chunk content goes here.",
            "Third chunk content goes here."
        ]
        
        result = await RetrievalManager._apply_overlap_to_chunks(chunks, 10)
        
        assert isinstance(result, list)
        assert len(result) == 3
        
        # First chunk should remain unchanged
        assert result[0] == chunks[0]
        
        # Subsequent chunks should have overlap
        for i in range(1, len(result)):
            assert len(result[i]) > len(chunks[i])

class TestRetrievalManagerErrorHandling:
    """Test RetrievalManager error handling"""
    
    @pytest.mark.asyncio
    async def test_setup_model_loading_error(self):
        """Test setup with model loading error"""
        # Reset singleton for test
        RetrievalManager._instance = None
        RetrievalManager._initialized = False
        
        with patch('onnxruntime.InferenceSession', side_effect=Exception("Model loading failed")):
            
            with pytest.raises(Exception) as exc_info:
                await RetrievalManager.setup()
            
            assert "Model loading failed" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_setup_tokenizer_loading_error(self):
        """Test setup with tokenizer loading error"""
        # Reset singleton for test
        RetrievalManager._instance = None
        RetrievalManager._initialized = False
        
        with patch('onnxruntime.InferenceSession') as mock_session, \
             patch('transformers.AutoTokenizer.from_pretrained', side_effect=Exception("Tokenizer failed")), \
             patch('managers.manager_retrieval.BASE_DIR') as mock_base_dir:
            
            mock_base_dir.return_value = Mock()
            mock_base_dir.return_value.__truediv__ = Mock(return_value="mock_path")
            mock_session.return_value = Mock()
            
            with pytest.raises(Exception) as exc_info:
                await RetrievalManager.setup()
            
            assert "Tokenizer failed" in str(exc_info.value)
    
    @pytest.mark.asyncio
    async def test_json_to_dict_invalid_json(self):
        """Test json_to_dict with invalid JSON string"""
        invalid_json = '{"incomplete": json'
        
        with pytest.raises(json.JSONDecodeError):
            await RetrievalManager.json_to_dict(invalid_json)
    
    @pytest.mark.asyncio
    async def test_embeddings_with_uninitialized_manager(self):
        """Test embeddings with uninitialized manager"""
        # Reset singleton to uninitialized state
        RetrievalManager._instance = None
        RetrievalManager._initialized = False
        
        manager = RetrievalManager.get_instance()
        manager.session = None
        manager.tokenizer = None
        
        with pytest.raises(AttributeError):
            await RetrievalManager.get_embeddings_dense("test")

class TestRetrievalManagerPerformance:
    """Test RetrievalManager performance characteristics"""
    
    @pytest.mark.asyncio
    async def test_chunking_performance(self):
        """Test chunking performance with large text"""
        import time
        
        # Create large text
        large_text = "Performance test sentence. " * 1000
        
        start_time = time.time()
        
        with patch('managers.manager_retrieval.CHUNK_SIZE', 1000), \
             patch('managers.manager_retrieval.CHUNK_OVERLAP', 200):
            
            result = await RetrievalManager.chunk_text(large_text)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should process quickly
        assert processing_time < 5.0, f"Chunking should be fast, took {processing_time:.3f}s"
        assert isinstance(result, list)
        assert len(result) > 0
    
    @pytest.mark.asyncio
    async def test_json_processing_performance(self):
        """Test JSON processing performance"""
        import time
        
        # Create complex nested structure
        complex_data = {
            f"level1_{i}": {
                f"level2_{j}": {
                    f"level3_{k}": f"value_{i}_{j}_{k}"
                    for k in range(5)
                }
                for j in range(5)
            }
            for i in range(10)
        }
        
        start_time = time.time()
        
        result = await RetrievalManager.json_to_dict(complex_data)
        
        end_time = time.time()
        processing_time = end_time - start_time
        
        # Should process quickly
        assert processing_time < 2.0, f"JSON processing should be fast, took {processing_time:.3f}s"
        assert isinstance(result, list)
        assert len(result) > 0
    
    def test_memory_efficiency(self):
        """Test RetrievalManager memory efficiency"""
        import sys
        
        manager = RetrievalManager.get_instance()
        
        # Check memory usage
        manager_size = sys.getsizeof(manager)
        
        # Should be reasonably sized
        assert manager_size < 5000, f"Manager should be memory efficient, uses {manager_size} bytes"

class TestRetrievalManagerIntegration:
    """Test RetrievalManager integration scenarios"""
    
    @pytest.mark.asyncio
    async def test_full_retrieval_pipeline(self):
        """Test full retrieval pipeline"""
        # Reset singleton for test
        RetrievalManager._instance = None
        RetrievalManager._initialized = False
        
        with patch('onnxruntime.InferenceSession') as mock_session, \
             patch('transformers.AutoTokenizer.from_pretrained') as mock_tokenizer, \
             patch('managers.manager_retrieval.BASE_DIR') as mock_base_dir, \
             patch('managers.manager_retrieval.ZairaSettings') as mock_settings:
            
            # Setup mocks
            mock_base_dir.return_value = Mock()
            mock_base_dir.return_value.__truediv__ = Mock(return_value="mock_path")
            
            mock_tokenizer_instance = Mock()
            mock_tokenizer_instance.return_value = {
                "input_ids": np.array([[1, 2, 3]]),
                "attention_mask": np.array([[1, 1, 1]]),
                "token_type_ids": np.array([[0, 0, 0]])
            }
            mock_tokenizer.return_value = mock_tokenizer_instance
            
            mock_session_instance = Mock()
            mock_session_instance.run.return_value = [np.array([[0.1, 0.2, 0.3]])]
            mock_session.return_value = mock_session_instance
            
            mock_embed_model = Mock()
            mock_embed_model.get_text_embedding.return_value = [0.1, 0.2, 0.3]
            mock_settings.OllamaSettings.return_value.embed_model = mock_embed_model
            
            # Setup manager
            await RetrievalManager.setup()
            
            # Test full pipeline
            text = "This is test text for the retrieval pipeline."
            
            # 1. Chunk text
            with patch('managers.manager_retrieval.CHUNK_SIZE', 50), \
                 patch('managers.manager_retrieval.CHUNK_OVERLAP', 10), \
                 patch('managers.manager_retrieval.EMBEDDING_SIZE', 512):
                
                chunks = await RetrievalManager.chunk_text(text)
                
                # 2. Generate embeddings for chunks
                embeddings = []
                for chunk in chunks:
                    embedding = await RetrievalManager.get_embeddings_dense(chunk)
                    embeddings.append(embedding)
                
                # 3. Process structured data
                json_data = {"content": text, "metadata": {"type": "test"}}
                structured = await RetrievalManager.json_to_dict(json_data)
                
                # Verify pipeline worked
                assert len(chunks) > 0
                assert len(embeddings) == len(chunks)
                assert len(structured) > 0
                
                for embedding in embeddings:
                    assert isinstance(embedding, list)
                    assert len(embedding) == 3
    
    @pytest.mark.asyncio
    async def test_multimodal_document_processing(self):
        """Test processing multimodal documents"""
        multimodal_doc = {
            "all_elements": [
                {"type": "Title", "text": "Document Title"},
                {"type": "NarrativeText", "text": "Introduction paragraph."},
                {"type": "Image", "summary": "Data visualization chart"},
                {"type": "Table", "summary": "Results table", "markdown": "| A | B |\n|---|---|\n| 1 | 2 |"},
                {"type": "NarrativeText", "text": "Conclusion paragraph."}
            ]
        }
        
        with patch('managers.manager_retrieval.CHUNK_SIZE', 200), \
             patch('managers.manager_retrieval.CHUNK_OVERLAP', 50):
            
            # Process multimodal elements
            chunks = await RetrievalManager.chunk_multimodal_elements(multimodal_doc)
            
            # Convert to markdown
            markdown = await RetrievalManager.elements_to_markdown(multimodal_doc["all_elements"])
            
            # Verify processing
            assert len(chunks) > 0
            assert isinstance(markdown, str)
            
            # Should preserve multimodal information
            combined_chunks = " ".join(chunks)
            assert "[IMAGE:" in combined_chunks
            assert "[TABLE:" in combined_chunks
            assert "Data visualization chart" in combined_chunks
    
    @pytest.mark.asyncio
    async def test_retrieval_with_rag_system(self):
        """Test RetrievalManager integration with RAG system"""
        # Simulate RAG workflow
        documents = [
            "Document 1 content about artificial intelligence.",
            "Document 2 content about machine learning algorithms.",
            "Document 3 content about neural networks."
        ]
        
        with patch('managers.manager_retrieval.CHUNK_SIZE', 100), \
             patch('managers.manager_retrieval.CHUNK_OVERLAP', 20):
            
            # 1. Chunk all documents
            all_chunks = []
            for doc in documents:
                chunks = await RetrievalManager.chunk_text(doc)
                all_chunks.extend(chunks)
            
            # 2. Convert to structured format
            structured_data = []
            for i, chunk in enumerate(all_chunks):
                element = {"id": f"chunk_{i}", "text": chunk}
                structured_data.append(element)
            
            # 3. Convert to dictionary format
            chunk_dict = await RetrievalManager.list_to_dict(structured_data)
            
            # Verify RAG preparation
            assert len(all_chunks) >= len(documents)
            assert len(structured_data) == len(all_chunks)
            assert len(chunk_dict) == len(all_chunks)
            
            # Should contain content from all documents
            combined_text = " ".join(chunk_dict.values())
            assert "artificial intelligence" in combined_text
            assert "machine learning" in combined_text
            assert "neural networks" in combined_text

class TestRetrievalManagerEdgeCases:
    """Test RetrievalManager edge cases"""
    
    @pytest.mark.asyncio
    async def test_empty_input_handling(self):
        """Test handling of empty inputs"""
        # Empty string
        result = await RetrievalManager.chunk_text("")
        assert isinstance(result, list)
        
        # Empty list
        result = await RetrievalManager.chunk_text([])
        assert isinstance(result, list)
        
        # Empty dict
        result = await RetrievalManager.json_to_dict({})
        assert isinstance(result, list)
        assert len(result) == 0
    
    @pytest.mark.asyncio
    async def test_very_large_input_handling(self):
        """Test handling of very large inputs"""
        # Very large text
        large_text = "Large text content. " * 10000
        
        with patch('managers.manager_retrieval.CHUNK_SIZE', 1000), \
             patch('managers.manager_retrieval.CHUNK_OVERLAP', 100):
            
            result = await RetrievalManager.chunk_text(large_text)
            
            assert isinstance(result, list)
            assert len(result) > 10  # Should create many chunks
    
    @pytest.mark.asyncio
    async def test_unicode_content_handling(self):
        """Test handling of Unicode content"""
        # Note: Project requires ASCII-only, but test robustness
        unicode_text = "Test content with special characters: áéíóú"
        
        # Should handle gracefully (even if it needs to be converted)
        try:
            result = await RetrievalManager.chunk_text(unicode_text)
            assert isinstance(result, list)
        except UnicodeEncodeError:
            # Acceptable if system enforces ASCII-only
            pass
    
    @pytest.mark.asyncio
    async def test_malformed_multimodal_data(self):
        """Test handling of malformed multimodal data"""
        malformed_data = {
            "all_elements": [
                {"type": "Image"},  # Missing summary
                {"summary": "No type field"},  # Missing type
                {"type": "Table", "text": "No summary"},  # Missing summary
                {}  # Empty element
            ]
        }
        
        # Should handle gracefully
        result = await RetrievalManager.chunk_multimodal_elements(malformed_data)
        assert isinstance(result, list)
    
    def test_safe_get_with_none_input(self):
        """Test safe_get with None input"""
        result = RetrievalManager.safe_get(None, "any_key", "default")
        assert result == "default"
    
    @pytest.mark.asyncio
    async def test_chunking_with_extreme_parameters(self):
        """Test chunking with extreme parameters"""
        text = "Test text for extreme parameters."
        
        # Very small chunk size
        result = await RetrievalManager.chunk_text(text, chunk_size=1, chunk_overlap=0)
        assert isinstance(result, list)
        
        # Chunk size larger than text
        result = await RetrievalManager.chunk_text(text, chunk_size=10000, chunk_overlap=0)
        assert isinstance(result, list)
        assert len(result) == 1  # Should be single chunk