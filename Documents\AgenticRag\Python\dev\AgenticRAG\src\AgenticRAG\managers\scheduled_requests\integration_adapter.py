from imports import *
from typing import Dict, List, Optional, Any
import asyncio
from datetime import datetime, timezone

from .core.factory import ScheduledRequestManagerFactory
from .core.persistence import ScheduledRequestPersistenceManager
from .security.validator import ScheduledRequestSecurityValidator
from .resources.rate_limiter import SystemRateLimiter
from .resources.quota_manager import SystemQuotaManager
from .monitoring.metrics_collector import MetricsCollector
from .utils.exceptions import (
    UserQuotaExceededError, UserRateLimitExceededError,
    UnauthorizedScheduledRequestError, SecurityValidationError
)
from .utils.helpers import validate_guid, get_user_friendly_error

if TYPE_CHECKING:
    from userprofiles.ScheduledZairaRequest import ScheduledZairaRequest
    from userprofiles.ZairaUser import ZairaUser

class ScheduledRequestIntegrationAdapter:
    """
    Integration adapter that provides backward compatibility while using new architecture
    """
    
    _instance: Optional['ScheduledRequestIntegrationAdapter'] = None
    _initialized: bool = False
    
    def __init__(self):
        # Core components
        self._factory: Optional[ScheduledRequestManagerFactory] = None
        self._persistence: Optional[ScheduledRequestPersistenceManager] = None
        self._security_validator: Optional[ScheduledRequestSecurityValidator] = None
        self._rate_limiter: Optional[SystemRateLimiter] = None
        self._quota_manager: Optional[SystemQuotaManager] = None
        self._metrics_collector: Optional[MetricsCollector] = None
    
    def __new__(cls):
        if cls._instance is None:
            cls._instance = super().__new__(cls)
        return cls._instance
    
    @classmethod
    def get_instance(cls) -> 'ScheduledRequestIntegrationAdapter':
        """Get singleton instance"""
        if cls._instance is None:
            cls._instance = cls()
        return cls._instance
    
    @classmethod
    async def setup(cls):
        """Initialize the integration adapter and all components"""
        instance = cls.get_instance()
        if instance._initialized:
            return
        
        try:
            # Initialize all components
            instance._factory = ScheduledRequestManagerFactory.get_instance()
            await instance._factory.setup()
            
            instance._persistence = ScheduledRequestPersistenceManager.get_instance()
            await instance._persistence.setup()
            
            instance._security_validator = ScheduledRequestSecurityValidator()
            
            instance._rate_limiter = SystemRateLimiter()
            await instance._rate_limiter.setup()
            
            instance._quota_manager = SystemQuotaManager()
            await instance._quota_manager.setup()
            
            instance._metrics_collector = MetricsCollector()
            await instance._metrics_collector.setup()
            
            instance._initialized = True
            LogFire.log("INIT", "ScheduledRequestIntegrationAdapter initialized successfully")
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to initialize ScheduledRequestIntegrationAdapter: {str(e)}")
            raise
    
    async def shutdown(self):
        """Shutdown all components"""
        try:
            if self._metrics_collector:
                await self._metrics_collector.shutdown()
            
            if self._quota_manager:
                await self._quota_manager.shutdown()
            
            if self._rate_limiter:
                await self._rate_limiter.shutdown()
            
            if self._factory:
                await self._factory.shutdown()
            
            LogFire.log("INIT", "ScheduledRequestIntegrationAdapter shutdown complete")
            
        except Exception as e:
            LogFire.log("ERROR", f"Error during adapter shutdown: {str(e)}")
    
    async def create_scheduled_request(self, scheduled_request: 'ScheduledZairaRequest', 
                                     client_ip: str = None) -> bool:
        """
        Create a new scheduled request using the new architecture
        
        Args:
            scheduled_request: The request to create
            client_ip: Client IP address for security validation
            
        Returns:
            bool: True if created successfully
        """
        try:
            user = scheduled_request.user
            user_guid = str(user.user_guid)
            
            # Security validation
            if client_ip:
                await self._security_validator.validate_ip_access(client_ip, user_guid)
            
            await self._security_validator.validate_user_authorization(user, "create")
            await self._security_validator.validate_request_content(scheduled_request)
            
            # Rate limiting
            await self._rate_limiter.check_user_rate_limit(
                user_guid, user.rank, "create_request"
            )
            
            # Quota management
            quota_manager = await self._quota_manager.get_user_quota_manager(user_guid, user.rank)
            operation_type = "create_recurring_task" if scheduled_request.schedule_type.value == "recurring" else "create_task"
            await quota_manager.reserve_quota(operation_type, estimated_memory_mb=10.0)  # Estimate 10MB per task
            
            try:
                # Get user-specific manager
                user_manager = await self._factory.get_user_manager(user_guid)
                
                # Create the request
                success = await user_manager.create_scheduled_request(scheduled_request)
                
                if success:
                    # Save to persistence
                    await self._persistence.save_task(scheduled_request, client_ip)
                    
                    # Record metrics
                    self._metrics_collector.record_request_created(
                        user_guid, scheduled_request.schedule_type.value
                    )
                    
                    LogFire.log("REQUEST", f"Successfully created scheduled request {scheduled_request.scheduled_guid}")
                    return True
                else:
                    # Release quota on failure
                    await quota_manager.release_quota(operation_type)
                    return False
                    
            except Exception as e:
                # Release quota on exception
                await quota_manager.release_quota(operation_type)
                raise
        
        except (UserQuotaExceededError, UserRateLimitExceededError, 
                UnauthorizedScheduledRequestError, SecurityValidationError) as e:
            # Record violation metrics
            if isinstance(e, UserQuotaExceededError):
                self._metrics_collector.record_quota_violation(
                    user_guid, e.quota_type, e.current_value, e.max_value
                )
            elif isinstance(e, UserRateLimitExceededError):
                self._metrics_collector.record_rate_limit_violation(
                    user_guid, e.rate_limit, e.retry_after
                )
            elif isinstance(e, SecurityValidationError):
                self._metrics_collector.record_security_violation(
                    user_guid, e.validation_type, e.details
                )
            
            LogFire.log("WARNING", f"Request creation blocked for user {user_guid}: {get_user_friendly_error(e)}")
            etc.helper_functions.exception_triggered(e, "Request creation blocked for user {user_guid}: {get_user_friendly_error(e)}")
            raise
        
        except Exception as e:
            LogFire.log("ERROR", f"Failed to create scheduled request: {str(e)}")
            return False
    
    async def cancel_scheduled_request(self, scheduled_guid: str, user: 'ZairaUser', 
                                     reason: str = "User requested cancellation") -> bool:
        """Cancel a scheduled request"""
        try:
            user_guid = str(user.user_guid)
            
            # Security validation
            await self._security_validator.validate_user_authorization(user, "delete", scheduled_guid)
            await self._security_validator.validate_request_ownership(user_guid, scheduled_guid)
            
            # Get user manager and cancel request
            user_manager = await self._factory.get_user_manager(user_guid)
            success = await user_manager.cancel_scheduled_request(scheduled_guid, reason)
            
            if success:
                # Update persistence
                await self._persistence.cancel_task(scheduled_guid, user_guid, reason)
                
                # Release quota
                quota_manager = await self._quota_manager.get_user_quota_manager(user_guid, user.rank)
                await quota_manager.release_quota("create_task")  # Generic release
                
                # Record metrics
                self._metrics_collector.record_request_cancelled(user_guid, reason)
                
                LogFire.log("REQUEST", f"Cancelled scheduled request {scheduled_guid}")
                return True
            
            return False
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to cancel request {scheduled_guid}: {str(e)}")
            return False
    
    async def get_user_scheduled_request_guids(self, user: 'ZairaUser') -> List[str]:
        """Get only the GUIDs of scheduled requests for a user"""
        try:
            user_guid = str(user.user_guid)
            LogFire.log("DEBUG", f"Getting scheduled request GUIDs for user {user_guid}")
            
            # Security validation
            await self._security_validator.validate_user_authorization(user, "read")
            LogFire.log("DEBUG", f"Security validation passed for user {user_guid}")
            
            # Get user manager
            user_manager = await self._factory.get_user_manager(user_guid)
            LogFire.log("DEBUG", f"Got user manager for user {user_guid}")
            
            # Get all request GUIDs
            request_guids = await user_manager.get_user_request_guids()
            LogFire.log("DEBUG", f"User manager returned {len(request_guids)} request GUIDs for user {user_guid}: {request_guids}")
            
            return request_guids
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get request GUIDs for user {user.user_guid}: {str(e)}")
            import traceback
            LogFire.log("ERROR", f"Traceback: {traceback.format_exc()}")
            return []
    
    async def get_user_scheduled_requests(self, user: 'ZairaUser', 
                                        include_completed: bool = False) -> List[Dict[str, Any]]:
        """Get all scheduled requests for a user"""
        try:
            user_guid = str(user.user_guid)
            
            # Security validation
            await self._security_validator.validate_user_authorization(user, "read")
            
            # Get user manager
            user_manager = await self._factory.get_user_manager(user_guid)
            
            requests = await user_manager.get_user_requests(include_completed)
            
            # Sanitize data before returning
            sanitized_requests = []
            for idx, request in enumerate(requests):
                sanitized_request = await self._security_validator.sanitize_request_data(request)
                sanitized_requests.append(sanitized_request)
            
            return sanitized_requests
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get requests for user {user.user_guid}: {str(e)}")
            return []
    
    # get_request_status method removed - use get_request_object to access attributes directly
    
    async def get_user_quota_status(self, user: 'ZairaUser') -> Dict[str, Any]:
        """Get user's quota status"""
        try:
            user_guid = str(user.user_guid)
            
            quota_manager = await self._quota_manager.get_user_quota_manager(user_guid, user.rank)
            quota_metrics = await quota_manager.get_quota_metrics()
            
            rate_status = await self._rate_limiter.get_user_rate_status(user_guid)
            
            return {
                'quota_metrics': quota_metrics,
                'rate_limit_status': rate_status,
                'user_guid': user_guid
            }
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get quota status for user {user.user_guid}: {str(e)}")
            return {}
    
    async def get_system_health(self) -> Dict[str, Any]:
        """Get overall system health status"""
        try:
            factory_health = await self._factory.get_system_health()
            quota_overview = await self._quota_manager.get_system_quota_overview()
            rate_metrics = await self._rate_limiter.get_system_rate_metrics()
            system_metrics = self._metrics_collector.get_current_metrics()
            
            return {
                'overall_status': self._calculate_overall_health(factory_health, quota_overview),
                'factory_health': factory_health,
                'quota_overview': quota_overview,
                'rate_limit_metrics': rate_metrics,
                'system_metrics': system_metrics,
                'timestamp': datetime.now(timezone.utc).isoformat()
            }
            
        except Exception as e:
            LogFire.log("ERROR", f"Failed to get system health: {str(e)}")
            return {'overall_status': 'error', 'error': str(e)}
    
    async def handle_user_login(self, user_guid: str):
        """Handle user login event - resume paused requests"""
        try:
            if validate_guid(user_guid):
                # This could trigger resumption of paused requests
                LogFire.log("USER", f"User {user_guid} logged in - checking for paused requests")
                
                # The factory will handle this automatically when user manager is requested
                
        except Exception as e:
            LogFire.log("ERROR", f"Error handling user login {user_guid}: {str(e)}")
    
    async def handle_user_logout(self, user_guid: str):
        """Handle user logout event - optionally cleanup user manager"""
        try:
            if validate_guid(user_guid):
                # Optionally remove user manager after some timeout
                # For now, let the factory's cleanup handle this
                LogFire.log("USER", f"User {user_guid} logged out")
                
        except Exception as e:
            LogFire.log("ERROR", f"Error handling user logout {user_guid}: {str(e)}")
    
    def _calculate_overall_health(self, factory_health: Dict[str, Any], 
                                quota_overview: Dict[str, Any]) -> str:
        """Calculate overall system health"""
        factory_status = factory_health.get('overall_status', 'unknown')
        quota_health = quota_overview.get('system_health', 'unknown')
        
        # Priority: critical > degraded > moderate > healthy
        statuses = [factory_status, quota_health]
        
        if 'critical' in statuses:
            return 'critical'
        elif 'unhealthy' in statuses or 'degraded' in statuses:
            return 'degraded'
        elif 'moderate' in statuses:
            return 'moderate'
        elif all(status == 'healthy' for status in statuses):
            return 'healthy'
        else:
            return 'unknown'
    
    # Backward compatibility methods for existing code
    async def save_task(self, scheduled_request: 'ScheduledZairaRequest') -> bool:
        """Backward compatibility method"""
        return await self.create_scheduled_request(scheduled_request)
    
    async def cancel_task(self, scheduled_guid: str, reason: str = "User requested cancellation") -> bool:
        """Backward compatibility method - requires user context"""
        LogFire.log("WARNING", "cancel_task called without user context - security risk")
        # This method should be deprecated in favor of cancel_scheduled_request
        return False
    
    async def get_active_requests(self, user_guid: Optional[str] = None) -> List[Dict[str, Any]]:
        """Backward compatibility method"""
        if user_guid:
            # Get requests from persistence (less secure, for legacy support)
            return await self._persistence.get_active_requests(user_guid)
        else:
            # System-wide requests - admin only
            return await self._persistence.get_active_requests()
    
    def is_initialized(self) -> bool:
        """Check if adapter is initialized"""
        return self._initialized
    
    async def force_cleanup(self):
        """Force cleanup of all components (emergency use)"""
        LogFire.log("WARNING", "Force cleanup initiated")
        
        try:
            if self._factory:
                await self._factory.force_cleanup_all_managers()
            
            LogFire.log("WARNING", "Force cleanup completed")
            
        except Exception as e:
            LogFire.log("ERROR", f"Error during force cleanup: {str(e)}")

async def get_integration_adapter() -> ScheduledRequestIntegrationAdapter:
    """Get the global integration adapter instance"""
    try:
        adapter = ScheduledRequestIntegrationAdapter.get_instance()
        if not adapter.is_initialized():
            LogFire.log("DEBUG", "Integration adapter not initialized, setting up now")
            await ScheduledRequestIntegrationAdapter.setup()
        return adapter
    except Exception as e:
        LogFire.log("ERROR", f"Failed to get integration adapter: {str(e)}")
        raise