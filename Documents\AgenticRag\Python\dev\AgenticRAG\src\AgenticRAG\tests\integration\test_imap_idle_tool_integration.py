from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from tasks.inputs.imap_idle_activate import SupervisorTask_IMAPIdleActivate, create_task_imap_idle_activate, start_30_minute_imap_session
from managers.manager_supervisors import SupervisorManager, SupervisorTaskState
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import ZairaUser
from endpoints.mybot_generic import MyBot_Generic

import pytest
import asyncio
from unittest.mock import Mock, patch, AsyncMock
import json


class TestIMAPIdleActivateIntegration:
    """Test integration of SupervisorTask_IMAPIdleActivate with supervisor framework"""
    
    def test_task_initialization(self):
        """Test that SupervisorTask_IMAPIdleActivate initializes properly"""
        task = SupervisorTask_IMAPIdleActivate(name="test_imap_idle", prompt_id="test_prompt")
        
        assert task.name == "test_imap_idle"
        assert task.prompt_id == "test_prompt"
        assert hasattr(task, 'llm_call')
    
    @patch('tasks.inputs.imap_idle_activate.ZairaUserManager')
    @pytest.mark.asyncio
    async def test_task_user_not_found(self, mock_user_manager):
        """Test task behavior when user is not found"""
        # Mock user manager to return None
        mock_user_manager.find_user = AsyncMock(return_value=None)
        
        task = SupervisorTask_IMAPIdleActivate(name="test_imap_idle", prompt_id="test_prompt")
        
        # Create mock state
        mock_state = Mock()
        mock_state.user_guid = "nonexistent-user-guid"
        
        result = await task.llm_call(mock_state)
        
        assert "User not found" in result
        assert "nonexistent-user-guid" in result
    
    @patch('tasks.inputs.imap_idle_activate.ZairaUserManager')
    @patch('tasks.inputs.imap_idle_activate.OAuth2Verifier')
    @patch('imaplib.IMAP4_SSL')
    @pytest.mark.asyncio
    async def test_task_30_minute_session_mock(self, mock_imap_ssl, mock_oauth, mock_user_manager):
        """Test 30-minute session with mocked IMAP connection"""
        # Mock user
        mock_user = Mock()
        mock_user.user_guid = "test-user-guid"
        mock_user_manager.find_user = AsyncMock(return_value=mock_user)
        
        # Mock OAuth tokens
        async def mock_get_token(identifier, token_type):
            token_map = {
                ("imap", "access_token"): "imap.gmail.com",
                ("imap", "refresh_token"): "<EMAIL>", 
                ("imap", "expires_in"): "993",
                ("imap", "token_type"): "test-password"
            }
            return token_map.get((identifier, token_type))
        
        mock_oauth.get_token = AsyncMock(side_effect=mock_get_token)
        
        # Mock IMAP connection
        mock_mail = Mock()
        mock_imap_ssl.return_value = mock_mail
        mock_mail.login = Mock()
        mock_mail.select = Mock()
        
        # Mock IDLE to simulate timeout after short period instead of 30 minutes
        def mock_idle():
            time.sleep(0.1)  # Very short sleep instead of 30 minutes
            return None
        
        mock_mail.idle = Mock(side_effect=mock_idle)
        mock_mail.response = Mock(return_value=('OK', []))
        mock_mail.logout = Mock()
        
        task = SupervisorTask_IMAPIdleActivate(name="test_imap_idle", prompt_id="test_prompt")
        
        # Create mock state
        mock_state = Mock()
        mock_state.user_guid = "test-user-guid"
        
        # Mock the _run_30_minute_session method to avoid actual 30-minute execution
        with patch.object(task, '_run_30_minute_session', return_value="Test session completed"):
            result = await task.llm_call(mock_state)
        
        # Just verify that the task completes and returns a string result
        assert isinstance(result, str)
        assert "Test session completed" in result


class TestIMAPIdleActivateWorkflow:
    """Test complete IMAP IDLE workflows"""
    
    @pytest.mark.asyncio
    async def test_create_task_imap_idle_activate(self):
        """Test create_task_imap_idle_activate function"""
        with patch('tasks.inputs.imap_idle_activate.SupervisorManager') as mock_sm:
            mock_task = Mock()
            mock_sm.register_task.return_value = mock_task
            
            result = await create_task_imap_idle_activate()
            
            assert result is not None
            mock_sm.register_task.assert_called_once()
    
    @patch('tasks.inputs.imap_idle_activate.SupervisorTask_IMAPIdleActivate')
    @pytest.mark.asyncio
    async def test_start_30_minute_imap_session(self, mock_task_class):
        """Test start_30_minute_imap_session function"""
        mock_task_instance = Mock()
        mock_task_instance.llm_call = AsyncMock(return_value="Session completed")
        mock_task_class.return_value = mock_task_instance
        
        user_guid = "test-user-guid"
        
        result = await start_30_minute_imap_session(user_guid)
        
        assert result == "Session completed"
        mock_task_instance.llm_call.assert_called_once()


class TestIMAPIdleActivateSystemIntegration:
    """Test system-level integration scenarios"""
    
    def test_task_conforms_to_supervisor_interface(self):
        """Test that task conforms to supervisor framework interface"""
        from managers.manager_supervisors import SupervisorTask_Base
        
        task = SupervisorTask_IMAPIdleActivate(name="test_imap_idle", prompt_id="test_prompt")
        
        # Should be instance of SupervisorTask_Base
        assert isinstance(task, SupervisorTask_Base)
        
        # Should have required supervisor attributes
        assert hasattr(task, 'name')
        assert hasattr(task, 'prompt_id')
        assert hasattr(task, 'llm_call')
        
        # Name and prompt_id should be strings
        assert isinstance(task.name, str)
        assert isinstance(task.prompt_id, str)
        assert len(task.name) > 0
        assert len(task.prompt_id) > 0
    
    @patch('tasks.inputs.imap_idle_activate.ZairaUserManager')
    @patch('tasks.inputs.imap_idle_activate.OAuth2Verifier')
    @pytest.mark.asyncio
    async def test_task_error_handling(self, mock_oauth, mock_user_manager):
        """Test task error handling"""
        # Mock user
        mock_user = Mock()
        mock_user.user_guid = "test-user-guid"
        mock_user_manager.find_user = AsyncMock(return_value=mock_user)
        
        # Mock OAuth to throw exception
        mock_oauth.get_token = AsyncMock(side_effect=Exception("OAuth failed"))
        
        task = SupervisorTask_IMAPIdleActivate(name="test_imap_idle", prompt_id="test_prompt")
        
        # Create mock state
        mock_state = Mock()
        mock_state.user_guid = "test-user-guid"
        
        # Should handle error gracefully
        result = await task.llm_call(mock_state)
        
        # Should return error message instead of crashing
        assert isinstance(result, str)
        assert len(result) > 0
    
    def test_task_integration_with_existing_patterns(self):
        """Test that task integrates well with existing codebase patterns"""
        # Test that task can be created with standard parameters
        task1 = SupervisorTask_IMAPIdleActivate(name="task1", prompt_id="prompt1")
        task2 = SupervisorTask_IMAPIdleActivate(name="task2", prompt_id="prompt2")
        
        # Tasks should be separate instances
        assert task1 is not task2
        assert task1.name != task2.name
        assert task1.prompt_id != task2.prompt_id
        
        # Both should be properly initialized
        assert hasattr(task1, 'llm_call')
        assert hasattr(task2, 'llm_call')
    
    @pytest.mark.asyncio
    async def test_supervisor_task_state_compatibility(self):
        """Test that SupervisorTaskState is properly handled"""
        task = SupervisorTask_IMAPIdleActivate(name="test_imap_idle", prompt_id="test_prompt")
        
        # Create mock state with required attributes
        mock_state = Mock(spec=SupervisorTaskState)
        mock_state.user_guid = "test-user-guid"
        mock_state.messages = []
        mock_state.sections = {}
        
        # Mock user manager to return None (user not found case)
        with patch('tasks.inputs.imap_idle_activate.ZairaUserManager') as mock_user_manager:
            mock_user_manager.find_user = AsyncMock(return_value=None)
            
            result = await task.llm_call(mock_state)
            
            # Should handle the state properly and return error message
            assert "User not found" in result
            assert mock_state.user_guid in result


class TestIMAPIdleActivateCompatibility:
    """Test compatibility with existing email system components"""
    
    def test_compatibility_with_supervisor_manager(self):
        """Test compatibility with SupervisorManager"""
        from managers.manager_supervisors import SupervisorManager
        
        # Should be able to register task with supervisor manager
        task = SupervisorTask_IMAPIdleActivate(name="test_imap_idle", prompt_id="Task_IMAP_Idle")
        
        # Task should have proper attributes for registration
        assert hasattr(task, 'name')
        assert hasattr(task, 'prompt_id')
        assert hasattr(task, 'llm_call')
    
    def test_compatibility_with_existing_imap_infrastructure(self):
        """Test compatibility with existing IMAP infrastructure"""
        # Should be able to import related components
        from endpoints.oauth._verifier_ import OAuth2Verifier
        
        # Task should work with these components
        task = SupervisorTask_IMAPIdleActivate(name="test_imap_idle", prompt_id="test_prompt")
        assert task is not None
        
        # Should be able to create task instance
        assert isinstance(task, SupervisorTask_IMAPIdleActivate)
    
    def test_integration_with_user_management(self):
        """Test integration with user management system"""
        from managers.manager_users import ZairaUserManager
        from userprofiles.ZairaUser import ZairaUser
        
        # Task should work with user management components
        task = SupervisorTask_IMAPIdleActivate(name="test_imap_idle", prompt_id="test_prompt")
        
        # Should have access to user management functionality
        assert hasattr(ZairaUserManager, 'find_user')
        
        # User class should be compatible
        assert hasattr(ZairaUser, 'user_guid')