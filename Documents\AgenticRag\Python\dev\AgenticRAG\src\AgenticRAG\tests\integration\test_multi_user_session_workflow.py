"""
Integration tests for multi-user session management workflow
This test suite covers complete multi-user scenarios:
User Registration -> Session Creation -> Concurrent Access -> Session Management -> Cleanup
"""

import sys
import os
import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from typing import Dict, List, Any
from datetime import datetime, timedelta
from uuid import uuid4

# Add project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from imports import *
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import <PERSON>aira<PERSON>ser, PERMISSION_LEVELS
from tasks.etc.task_chat_session import create_task_manage_chat_sessions
from managers.manager_supervisors import SupervisorManager
from managers.manager_postgreSQL import PostgreSQLManager

class TestMultiUserSessionWorkflow:
    """Test complete multi-user session management workflow"""
    
    @pytest.fixture
    async def setup_multi_user_environment(self):
        """Set up test environment for multi-user operations"""
        with patch('managers.manager_users.PostgreSQLManager') as mock_db, \
             patch('managers.manager_supervisors.SupervisorManager') as mock_supervisor, \
             patch('tasks.etc.task_chat_session.PostgreSQLManager') as mock_session_db:
            
            # Mock database
            mock_db_instance = Mock()
            mock_db.get_instance.return_value = mock_db_instance
            mock_session_db.get_instance.return_value = mock_db_instance
            mock_connection = Mock()
            mock_db_instance.get_connection.return_value.__aenter__.return_value = mock_connection
            mock_db_instance.get_connection.return_value.__aexit__.return_value = None
            
            # Mock supervisor
            mock_supervisor_instance = Mock()
            mock_supervisor.get_instance.return_value = mock_supervisor_instance
            
            # Create test users
            test_users = []
            for i in range(3):
                user = ZairaUser(
                    username=f"testuser{i}",
                    rank=PERMISSION_LEVELS.USER,
                    guid=uuid4(),
                    device_guid=uuid4()
                )
                user.email = f"testuser{i}@example.com"
                test_users.append(user)
            
            yield {
                'db': mock_db,
                'supervisor': mock_supervisor,
                'session_db': mock_session_db,
                'test_users': test_users,
                'db_instance': mock_db_instance,
                'connection': mock_connection
            }
    
    @pytest.mark.asyncio
    async def test_multi_user_registration_workflow(self, setup_multi_user_environment):
        """Test multi-user registration workflow"""
        env = setup_multi_user_environment
        
        # Reset singleton for test
        ZairaUserManager._instance = None
        ZairaUserManager._initialized = False
        
        # Create user manager
        user_manager = ZairaUserManager.get_instance()
        await ZairaUserManager.setup()
        
        # Mock database operations
        env['connection'].fetchrow.return_value = None  # User doesn't exist
        env['connection'].execute.return_value = None  # Insert successful
        
        # Test concurrent user registration
        registration_requests = []
        for user in env['test_users']:
            task = asyncio.create_task(user_manager.add_user(user))
            registration_requests.append(task)
        
        # Wait for all registrations to complete
        results = await asyncio.gather(*registration_requests, return_exceptions=True)
        
        # Verify all registrations succeeded
        for result in results:
            assert not isinstance(result, Exception)
        
        # Verify database was called for each user
        assert env['connection'].execute.call_count == len(env['test_users'])
        
        # Verify all users were added to in-memory storage
        assert len(user_manager.users) == len(env['test_users'])
        
        # Verify each user is accessible
        for user in env['test_users']:
            retrieved_user = await user_manager.find_user(user.user_guid)
            assert retrieved_user is not None
            assert retrieved_user.user_guid == user.user_guid
            assert retrieved_user.username == user.username
    
    @pytest.mark.asyncio
    async def test_multi_user_session_creation_workflow(self, setup_multi_user_environment):
        """Test multi-user session creation workflow"""
        env = setup_multi_user_environment
        
        # Reset singleton for test
        ZairaUserManager._instance = None
        ZairaUserManager._initialized = False
        
        # Create user manager
        user_manager = ZairaUserManager.get_instance()
        await ZairaUserManager.setup()
        
        # Add test users to manager
        for user in env['test_users']:
            user_manager.users[user.user_guid] = user
        
        # Mock session database operations
        env['connection'].fetchrow.return_value = {'session_guid': 'test-session-123'}
        env['connection'].fetch.return_value = []
        
        # Test concurrent session creation
        session_requests = []
        for user in env['test_users']:
            # Create session creation task
            task = asyncio.create_task(self._create_user_session(user, env['connection']))
            session_requests.append(task)
        
        # Wait for all sessions to be created
        session_results = await asyncio.gather(*session_requests, return_exceptions=True)
        
        # Verify all sessions were created successfully
        for result in session_results:
            assert not isinstance(result, Exception)
            assert 'session_guid' in result
        
        # Verify database was called for each session
        assert env['connection'].fetchrow.call_count == len(env['test_users'])
        
        # Verify unique sessions were created
        session_guids = [result['session_guid'] for result in session_results]
        assert len(set(session_guids)) == len(session_guids)  # All unique
    
    async def _create_user_session(self, user: ZairaUser, connection: Mock) -> Dict[str, Any]:
        """Helper method to create a user session"""
        session_guid = str(uuid4())
        connection.fetchrow.return_value = {'session_guid': session_guid}
        
        # Simulate session creation
        session_data = {
            'session_guid': session_guid,
            'user_guid': user.user_guid,
            'created_at': datetime.now(),
            'last_activity': datetime.now(),
            'status': 'active'
        }
        
        return session_data
    
    @pytest.mark.asyncio
    async def test_concurrent_user_message_processing_workflow(self, setup_multi_user_environment):
        """Test concurrent user message processing workflow"""
        env = setup_multi_user_environment
        
        # Reset singleton for test
        ZairaUserManager._instance = None
        ZairaUserManager._initialized = False
        
        # Create user manager
        user_manager = ZairaUserManager.get_instance()
        await ZairaUserManager.setup()
        
        # Add test users to manager
        for user in env['test_users']:
            user_manager.users[user.user_guid] = user
        
        # Mock supervisor responses
        env['supervisor'].get_instance.return_value.route_to_task = AsyncMock(
            side_effect=lambda task_type, content: f"Processed {task_type}: {content}"
        )
        
        # Create concurrent message processing tasks
        message_requests = []
        for i, user in enumerate(env['test_users']):
            message_content = f"Message {i} from user {user.username}"
            task = asyncio.create_task(
                self._process_user_message(user, message_content, env['supervisor'])
            )
            message_requests.append(task)
        
        # Wait for all messages to be processed
        message_results = await asyncio.gather(*message_requests, return_exceptions=True)
        
        # Verify all messages were processed successfully
        for i, result in enumerate(message_results):
            assert not isinstance(result, Exception)
            assert f"Message {i}" in result
            assert f"testuser{i}" in result
        
        # Verify supervisor was called for each message
        supervisor_calls = env['supervisor'].get_instance.return_value.route_to_task.call_args_list
        assert len(supervisor_calls) == len(env['test_users'])
        
        # Verify each call had correct parameters
        for i, call in enumerate(supervisor_calls):
            assert call[0][1] == f"Message {i} from user testuser{i}"
    
    async def _process_user_message(self, user: ZairaUser, message: str, supervisor_mock: Mock) -> str:
        """Helper method to process a user message"""
        # Simulate message processing through supervisor
        result = await supervisor_mock.get_instance.return_value.route_to_task(
            "message_processing", message
        )
        return result
    
    @pytest.mark.asyncio
    async def test_user_session_isolation_workflow(self, setup_multi_user_environment):
        """Test user session isolation workflow"""
        env = setup_multi_user_environment
        
        # Reset singleton for test
        ZairaUserManager._instance = None
        ZairaUserManager._initialized = False
        
        # Create user manager
        user_manager = ZairaUserManager.get_instance()
        await ZairaUserManager.setup()
        
        # Add test users to manager
        for user in env['test_users']:
            user_manager.users[user.user_guid] = user
        
        # Mock session data for each user
        user_sessions = {}
        for i, user in enumerate(env['test_users']):
            session_data = {
                'session_guid': f'session-{i}',
                'user_guid': user.user_guid,
                'chat_history': [
                    {'role': 'user', 'content': f'User {i} message 1'},
                    {'role': 'assistant', 'content': f'Response to user {i} message 1'}
                ],
                'context': {'user_preference': f'preference_{i}'},
                'created_at': datetime.now(),
                'last_activity': datetime.now()
            }
            user_sessions[user.user_guid] = session_data
        
        # Test session isolation
        isolation_requests = []
        for user in env['test_users']:
            task = asyncio.create_task(
                self._verify_session_isolation(user, user_sessions[user.user_guid], user_sessions)
            )
            isolation_requests.append(task)
        
        # Wait for all isolation tests to complete
        isolation_results = await asyncio.gather(*isolation_requests, return_exceptions=True)
        
        # Verify all isolation tests passed
        for result in isolation_results:
            assert not isinstance(result, Exception)
            assert result == True  # Isolation verified
    
    async def _verify_session_isolation(self, user: ZairaUser, user_session: Dict[str, Any], all_sessions: Dict[str, Dict[str, Any]]) -> bool:
        """Helper method to verify session isolation"""
        # Verify user can only access their own session
        accessible_session = user_session
        
        # Verify user session contains correct data
        assert accessible_session['user_guid'] == user.user_guid
        assert accessible_session['session_guid'] == all_sessions[user.user_guid]['session_guid']
        
        # Verify user cannot access other users' sessions
        for other_user_guid, other_session in all_sessions.items():
            if other_user_guid != user.user_guid:
                # In a real implementation, this would be an access control check
                assert other_session['user_guid'] != user.user_guid
                assert other_session['session_guid'] != accessible_session['session_guid']
        
        return True
    
    @pytest.mark.asyncio
    async def test_user_permission_workflow(self, setup_multi_user_environment):
        """Test user permission workflow"""
        env = setup_multi_user_environment
        
        # Reset singleton for test
        ZairaUserManager._instance = None
        ZairaUserManager._initialized = False
        
        # Create user manager
        user_manager = ZairaUserManager.get_instance()
        await ZairaUserManager.setup()
        
        # Create users with different permission levels
        admin_user = ZairaUser(
            username="admin_user",
            rank=PERMISSION_LEVELS.ADMIN,
            guid=uuid4(),
            device_guid=uuid4()
        )
        admin_user.email = "<EMAIL>"
        
        regular_user = ZairaUser(
            username="regular_user",
            rank=PERMISSION_LEVELS.USER,
            guid=uuid4(),
            device_guid=uuid4()
        )
        regular_user.email = "<EMAIL>"
        
        guest_user = ZairaUser(
            username="guest_user",
            rank=PERMISSION_LEVELS.GUEST,
            guid=uuid4(),
            device_guid=uuid4()
        )
        guest_user.email = "<EMAIL>"
        
        users_with_permissions = [admin_user, regular_user, guest_user]
        
        # Add users to manager
        for user in users_with_permissions:
            user_manager.users[user.user_guid] = user
        
        # Test permission-based access
        permission_requests = []
        for user in users_with_permissions:
            task = asyncio.create_task(
                self._test_user_permissions(user)
            )
            permission_requests.append(task)
        
        # Wait for all permission tests to complete
        permission_results = await asyncio.gather(*permission_requests, return_exceptions=True)
        
        # Verify permission tests
        for i, result in enumerate(permission_results):
            assert not isinstance(result, Exception)
            user = users_with_permissions[i]
            
            if user.permission_level == PERMISSION_LEVELS.ADMIN:
                assert result['admin_access'] == True
                assert result['user_access'] == True
                assert result['guest_access'] == True
            elif user.permission_level == PERMISSION_LEVELS.USER:
                assert result['admin_access'] == False
                assert result['user_access'] == True
                assert result['guest_access'] == True
            else:  # GUEST
                assert result['admin_access'] == False
                assert result['user_access'] == False
                assert result['guest_access'] == True
    
    async def _test_user_permissions(self, user: ZairaUser) -> Dict[str, bool]:
        """Helper method to test user permissions"""
        # Simulate permission checks
        permissions = {
            'admin_access': user.permission_level.value >= PERMISSION_LEVELS.ADMIN.value,
            'user_access': user.permission_level.value >= PERMISSION_LEVELS.USER.value,
            'guest_access': user.permission_level.value >= PERMISSION_LEVELS.GUEST.value
        }
        
        return permissions
    
    @pytest.mark.asyncio
    async def test_user_session_cleanup_workflow(self, setup_multi_user_environment):
        """Test user session cleanup workflow"""
        env = setup_multi_user_environment
        
        # Reset singleton for test
        ZairaUserManager._instance = None
        ZairaUserManager._initialized = False
        
        # Create user manager
        user_manager = ZairaUserManager.get_instance()
        await ZairaUserManager.setup()
        
        # Add test users to manager
        for user in env['test_users']:
            user_manager.users[user.user_guid] = user
        
        # Mock session cleanup operations
        env['connection'].execute.return_value = None
        env['connection'].fetch.return_value = [
            {
                'session_guid': f'old-session-{i}',
                'user_guid': user.user_guid,
                'last_activity': datetime.now() - timedelta(hours=2)
            }
            for i, user in enumerate(env['test_users'])
        ]
        
        # Test session cleanup
        cleanup_cutoff = datetime.now() - timedelta(hours=1)
        
        # Simulate cleanup task
        cleanup_task = asyncio.create_task(
            self._cleanup_old_sessions(cleanup_cutoff, env['connection'])
        )
        
        cleanup_result = await cleanup_task
        
        # Verify cleanup was performed
        assert cleanup_result == True
        
        # Verify database was called for cleanup
        env['connection'].execute.assert_called()
        
        # Verify cleanup query
        cleanup_calls = env['connection'].execute.call_args_list
        cleanup_query = cleanup_calls[-1][0][0]
        assert "DELETE" in cleanup_query or "UPDATE" in cleanup_query
        assert "last_activity" in cleanup_query
    
    async def _cleanup_old_sessions(self, cutoff_time: datetime, connection: Mock) -> bool:
        """Helper method to cleanup old sessions"""
        # Simulate session cleanup
        await asyncio.sleep(0.1)  # Simulate cleanup work
        
        # Mock cleanup operation
        connection.execute("DELETE FROM user_sessions WHERE last_activity < $1", cutoff_time)
        
        return True
    
    @pytest.mark.asyncio
    async def test_user_load_balancing_workflow(self, setup_multi_user_environment):
        """Test user load balancing workflow"""
        env = setup_multi_user_environment
        
        # Reset singleton for test
        ZairaUserManager._instance = None
        ZairaUserManager._initialized = False
        
        # Create user manager
        user_manager = ZairaUserManager.get_instance()
        await ZairaUserManager.setup()
        
        # Add test users to manager
        for user in env['test_users']:
            user_manager.users[user.user_guid] = user
        
        # Create multiple concurrent requests per user
        requests_per_user = 5
        all_requests = []
        
        for user in env['test_users']:
            for i in range(requests_per_user):
                task = asyncio.create_task(
                    self._simulate_user_request(user, i, env['supervisor'])
                )
                all_requests.append(task)
        
        # Execute all requests concurrently
        start_time = datetime.now()
        results = await asyncio.gather(*all_requests, return_exceptions=True)
        end_time = datetime.now()
        
        # Verify all requests completed successfully
        successful_results = [r for r in results if not isinstance(r, Exception)]
        assert len(successful_results) == len(env['test_users']) * requests_per_user
        
        # Verify reasonable completion time (load balancing working)
        execution_time = (end_time - start_time).total_seconds()
        assert execution_time < 10.0  # Should complete within 10 seconds
        
        # Verify supervisor was called for each request
        supervisor_calls = env['supervisor'].get_instance.return_value.route_to_task.call_args_list
        assert len(supervisor_calls) == len(env['test_users']) * requests_per_user
        
        # Verify requests were distributed across users
        user_request_counts = {}
        for result in successful_results:
            user_guid = result['user_guid']
            user_request_counts[user_guid] = user_request_counts.get(user_guid, 0) + 1
        
        # Each user should have exactly requests_per_user requests
        for user in env['test_users']:
            assert user_request_counts[user.user_guid] == requests_per_user
    
    async def _simulate_user_request(self, user: ZairaUser, request_id: int, supervisor_mock: Mock) -> Dict[str, Any]:
        """Helper method to simulate a user request"""
        # Mock supervisor response
        supervisor_mock.get_instance.return_value.route_to_task = AsyncMock(
            return_value=f"Response for user {user.username} request {request_id}"
        )
        
        # Simulate request processing
        request_data = f"Request {request_id} from {user.username}"
        response = await supervisor_mock.get_instance.return_value.route_to_task(
            "user_request", request_data
        )
        
        return {
            'user_guid': user.user_guid,
            'request_id': request_id,
            'response': response,
            'processed_at': datetime.now()
        }
    
    @pytest.mark.asyncio
    async def test_user_state_consistency_workflow(self, setup_multi_user_environment):
        """Test user state consistency workflow"""
        env = setup_multi_user_environment
        
        # Reset singleton for test
        ZairaUserManager._instance = None
        ZairaUserManager._initialized = False
        
        # Create user manager
        user_manager = ZairaUserManager.get_instance()
        await ZairaUserManager.setup()
        
        # Add test users to manager
        for user in env['test_users']:
            user_manager.users[user.user_guid] = user
        
        # Create concurrent state modification tasks
        state_requests = []
        for user in env['test_users']:
            # Multiple tasks modifying the same user state
            for i in range(3):
                task = asyncio.create_task(
                    self._modify_user_state(user, f"update_{i}", user_manager)
                )
                state_requests.append(task)
        
        # Execute all state modifications concurrently
        state_results = await asyncio.gather(*state_requests, return_exceptions=True)
        
        # Verify all state modifications completed successfully
        for result in state_results:
            assert not isinstance(result, Exception)
            assert result == True  # State modification successful
        
        # Verify user state consistency
        for user in env['test_users']:
            current_user = user_manager.users[user.user_guid]
            assert current_user.user_guid == user.user_guid
            assert current_user.username == user.username
            assert current_user.permission_level == user.permission_level
    
    async def _modify_user_state(self, user: ZairaUser, modification: str, user_manager: ZairaUserManager) -> bool:
        """Helper method to modify user state"""
        # Simulate state modification
        await asyncio.sleep(0.1)  # Simulate processing time
        
        # Mock state modification (in real implementation, this would be atomic)
        current_user = user_manager.users[user.user_guid]
        
        # Simulate modification (update last activity)
        current_user.last_activity = datetime.now()
        
        # Update in manager
        user_manager.users[user.user_guid] = current_user
        
        return True

class TestUserSessionIntegration:
    """Test user session integration with other components"""
    
    @pytest.mark.asyncio
    async def test_user_session_with_chat_management_integration(self):
        """Test user session integration with chat management"""
        with patch('tasks.etc.task_chat_session.PostgreSQLManager') as mock_db, \
             patch('tasks.etc.task_chat_session.SupervisorTask_SingleAgent') as mock_task_class:
            
            # Mock database
            mock_db_instance = Mock()
            mock_db.get_instance.return_value = mock_db_instance
            mock_connection = Mock()
            mock_db_instance.get_connection.return_value.__aenter__.return_value = mock_connection
            mock_db_instance.get_connection.return_value.__aexit__.return_value = None
            
            # Mock task
            mock_task_instance = Mock()
            mock_task_class.return_value = mock_task_instance
            mock_task_instance.llm_call = AsyncMock(return_value="Chat session managed successfully")
            
            # Create chat session management task
            chat_task = await create_task_manage_chat_sessions()
            
            # Verify task was created
            assert chat_task is not None
            
            # Test task execution
            test_input = "manage chat session for user"
            result = await chat_task.llm_call(test_input)
            
            # Verify task executed
            mock_task_instance.llm_call.assert_called_once_with(test_input)
            assert "Chat session managed successfully" in result
    
    @pytest.mark.asyncio
    async def test_user_session_with_supervisor_integration(self):
        """Test user session integration with supervisor system"""
        with patch('managers.manager_users.PostgreSQLManager') as mock_db, \
             patch('managers.manager_supervisors.SupervisorManager') as mock_supervisor:
            
            # Mock database
            mock_db_instance = Mock()
            mock_db.get_instance.return_value = mock_db_instance
            
            # Mock supervisor
            mock_supervisor_instance = Mock()
            mock_supervisor.get_instance.return_value = mock_supervisor_instance
            mock_supervisor_instance.route_to_task = AsyncMock(return_value="Task completed for user session")
            
            # Reset singleton
            ZairaUserManager._instance = None
            ZairaUserManager._initialized = False
            
            # Create user manager
            user_manager = ZairaUserManager.get_instance()
            await ZairaUserManager.setup()
            
            # Create test user
            test_user = ZairaUser(
                username="integration_user",
                rank=PERMISSION_LEVELS.USER,
                guid=uuid4(),
                device_guid=uuid4()
            )
            
            # Add user to manager
            user_manager.users[test_user.user_guid] = test_user
            
            # Test supervisor integration
            task_result = await mock_supervisor_instance.route_to_task(
                "user_session_task", 
                f"Process session for user {test_user.username}"
            )
            
            # Verify supervisor was called
            mock_supervisor_instance.route_to_task.assert_called_once()
            assert "Task completed for user session" in task_result
            
            # Verify user is still accessible
            retrieved_user = await user_manager.find_user(test_user.user_guid)
            assert retrieved_user is not None
            assert retrieved_user.user_guid == test_user.user_guid