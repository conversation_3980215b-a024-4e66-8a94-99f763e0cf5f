from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import AsyncMock, patch, MagicMock
from managers.manager_system_user import SystemUserManager

class TestSystemUserStartupTasks:
    """Test startup task execution functionality"""
    
    @pytest.fixture
    def system_user_manager(self):
        """Get SystemUserManager instance"""
        return SystemUserManager.get_instance()
    
    @pytest.mark.asyncio
    async def test_execute_startup_requests_with_imap_configured(self, system_user_manager):
        """Test startup tasks execution when IMAP is configured"""
        # Mock exception_triggered to prevent exit()
        with patch('etc.helper_functions.exception_triggered'):
            # Mock OAuth2Verifier to return a valid IMAP token
            with patch('endpoints.oauth._verifier_.OAuth2Verifier.get_instance') as mock_verifier_class:
                mock_verifier = AsyncMock()
                mock_verifier.get_token.return_value = "valid_imap_token"
                mock_verifier_class.return_value = mock_verifier
                
                # Mock SupervisorManager and IMAP task
                with patch('managers.manager_supervisors.SupervisorManager.get_instance') as mock_supervisor_class:
                    mock_supervisor = AsyncMock()
                    mock_imap_task = AsyncMock()
                    mock_supervisor.get_task.return_value = mock_imap_task
                    mock_supervisor_class.return_value = mock_supervisor
                    
                    # Mock get_system_user
                    with patch.object(system_user_manager, 'get_system_user', return_value=AsyncMock()) as mock_get_user:
                        # Execute startup tasks
                        await system_user_manager._execute_startup_requests()
                        
                        # Verify IMAP token was checked
                        mock_verifier.get_token.assert_called_with("imap", "access_token")
                        
                        # Verify IMAP task was retrieved and executed
                        mock_supervisor.get_task.assert_called_with("imap_idle_30_minute_session")
                        mock_get_user.assert_called_once()
                        mock_imap_task.call_task_with_query.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_execute_startup_requests_without_imap_configured(self, system_user_manager):
        """Test startup tasks execution when IMAP is not configured"""
        # Mock OAuth2Verifier to return empty IMAP token
        with patch('endpoints.oauth._verifier_.OAuth2Verifier.get_instance') as mock_verifier_class:
            mock_verifier = AsyncMock()
            mock_verifier.get_token.return_value = ""
            mock_verifier_class.return_value = mock_verifier
            
            # Mock SupervisorManager
            with patch('managers.manager_supervisors.SupervisorManager.get_instance') as mock_supervisor_class:
                mock_supervisor = AsyncMock()
                mock_supervisor_class.return_value = mock_supervisor
                
                # Execute startup tasks
                await system_user_manager._execute_startup_requests()
                
                # Verify IMAP token was checked
                mock_verifier.get_token.assert_called_with("imap", "access_token")
                
                # Verify IMAP task was NOT retrieved (since IMAP not configured)
                mock_supervisor.get_task.assert_not_called()
    
    @pytest.mark.asyncio
    async def test_execute_startup_task_by_name_success(self, system_user_manager):
        """Test executing a startup task by name successfully"""
        # Mock exception_triggered to prevent exit()
        with patch('etc.helper_functions.exception_triggered'):
            # Mock SupervisorManager and task
            with patch('managers.manager_supervisors.SupervisorManager.get_instance') as mock_supervisor_class:
                mock_supervisor = AsyncMock()
                mock_task = AsyncMock()
                mock_supervisor.get_task.return_value = mock_task
                mock_supervisor_class.return_value = mock_supervisor
                
                # Mock get_system_user
                with patch.object(system_user_manager, 'get_system_user', return_value=AsyncMock()) as mock_get_user:
                    # Execute startup task by name
                    result = await system_user_manager.execute_startup_task_by_name("test_task", "Test query")
                    
                    # Verify task was retrieved and executed
                    mock_supervisor.get_task.assert_called_with("test_task")
                    mock_get_user.assert_called_once()
                    mock_task.call_task_with_query.assert_called_once()
                    assert result is True
    
    @pytest.mark.asyncio
    async def test_execute_startup_task_by_name_not_found(self, system_user_manager):
        """Test executing a startup task by name when task not found"""
        # Mock SupervisorManager to return None (task not found)
        with patch('managers.manager_supervisors.SupervisorManager.get_instance') as mock_supervisor_class:
            mock_supervisor = AsyncMock()
            mock_supervisor.get_task.return_value = None
            mock_supervisor_class.return_value = mock_supervisor
            
            # Execute startup task by name
            result = await system_user_manager.execute_startup_task_by_name("nonexistent_task")
            
            # Verify task was attempted to be retrieved
            mock_supervisor.get_task.assert_called_with("nonexistent_task")
            assert result is False
    
    @pytest.mark.asyncio
    async def test_execute_imap_idle_startup_success(self, system_user_manager):
        """Test IMAP IDLE startup execution success"""
        # Mock SupervisorManager and IMAP task
        with patch('managers.manager_supervisors.SupervisorManager.get_instance') as mock_supervisor_class:
            mock_supervisor = AsyncMock()
            mock_imap_task = AsyncMock()
            mock_supervisor.get_task.return_value = mock_imap_task
            mock_supervisor_class.return_value = mock_supervisor
            
            # Execute IMAP idle startup
            await system_user_manager._execute_imap_idle_startup()
            
            # Verify IMAP task was retrieved and executed
            mock_supervisor.get_task.assert_called_with("imap_idle_30_minute_session")
            mock_imap_task.call_task_with_query.assert_called_with("Execute the task", None)
    
    @pytest.mark.asyncio
    async def test_execute_imap_idle_startup_task_not_found(self, system_user_manager):
        """Test IMAP IDLE startup when task not found"""
        # Mock SupervisorManager to return None (task not found)
        with patch('managers.manager_supervisors.SupervisorManager.get_instance') as mock_supervisor_class:
            mock_supervisor = AsyncMock()
            mock_supervisor.get_task.return_value = None
            mock_supervisor_class.return_value = mock_supervisor
            
            # Execute IMAP idle startup (should handle gracefully)
            await system_user_manager._execute_imap_idle_startup()
            
            # Verify task was attempted to be retrieved
            mock_supervisor.get_task.assert_called_with("imap_idle_30_minute_session")
    
    @pytest.mark.asyncio
    async def test_execute_startup_requests_handles_exceptions(self, system_user_manager):
        """Test that startup tasks handle exceptions gracefully"""
        # Mock OAuth2Verifier to raise an exception
        with patch('endpoints.oauth._verifier_.OAuth2Verifier.get_instance') as mock_verifier_class:
            mock_verifier_class.side_effect = Exception("OAuth error")
            
            # Mock exception_triggered to verify error handling
            with patch('etc.helper_functions.exception_triggered') as mock_exception:
                # Execute startup tasks (should not raise exception)
                await system_user_manager._execute_startup_requests()
                
                # Verify exception was handled
                mock_exception.assert_called_once()