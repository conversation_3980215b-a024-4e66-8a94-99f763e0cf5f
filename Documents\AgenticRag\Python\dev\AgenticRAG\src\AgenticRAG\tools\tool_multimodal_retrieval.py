from imports import *
from langchain_core.tools import BaseTool
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field

class MultimodalSearchInput(BaseModel):
    """Input for multimodal search tool."""
    query: str = Field(..., description="Search query for multimodal content")
    content_types: List[str] = Field(
        default=["text", "image", "table"], 
        description="Types of content to search: text, image, table"
    )
    max_results: int = Field(default=5, description="Maximum number of results to return")
    include_assets: bool = Field(default=True, description="Include asset paths in results")
    filter_by_doc: Optional[str] = Field(default=None, description="Filter by specific document ID")

class MultimodalRetrievalTool(BaseTool):
    """Tool for retrieving multimodal content from vector store."""
    
    name: str = "multimodal_retrieval"
    description: str = """
    Search for multimodal content including text, images, and tables from documents.
    
    This tool can:
    - Search across text content, image descriptions, and table summaries
    - Filter results by content type (text, image, table)
    - Return asset paths for images when available
    - Provide structured metadata for retrieved content
    
    Use this tool when you need to find specific information that might be in:
    - Document text passages
    - Images or figures with descriptions
    - Tables with data or summaries
    """
    
    args_schema: type = MultimodalSearchInput
    
    def _run(self, query: str, content_types: List[str] = None, max_results: int = 5, 
             include_assets: bool = True, filter_by_doc: Optional[str] = None) -> str:
        raise NotImplementedError("Use async version")
    
    async def _arun(self, query: str, content_types: List[str] = None, max_results: int = 5, 
                   include_assets: bool = True, filter_by_doc: Optional[str] = None) -> str:
        """Perform multimodal search and return structured results."""
        try:
            content_types = content_types or ["text", "image", "table"]
            
            # Get the index from Globals
            from imports import Globals
            from llama_index.core.vector_stores import MetadataFilters, MetadataFilter, FilterOperator
            
            # Check if index is available
            index = Globals.get_index()
            if index is None:
                print(f"[DEBUG] No index available, returning empty results")
                return f"No multimodal content found for query: '{query}' (Index not initialized)"
            
            # Build filters for content types and document filtering
            filters = []
            
            # Add content type filters
            if len(content_types) < 3:  # If not searching all types
                content_filters = []
                for ct in content_types:
                    if ct == "image":
                        content_filters.append(MetadataFilter(key="content_type", value="image_summary", operator=FilterOperator.EQ))
                    elif ct == "table":
                        content_filters.append(MetadataFilter(key="content_type", value="table_summary", operator=FilterOperator.EQ))
                    elif ct == "text":
                        # For text, we want items that don't have a specific content_type or have content_type = "text"
                        pass  # Will be handled by not having content_type filter
                
                if content_filters:
                    filters.extend(content_filters)
            
            # Add document filter if specified
            if filter_by_doc:
                filters.append(MetadataFilter(key="multimodal_doc_id", value=filter_by_doc, operator=FilterOperator.EQ))
            
            metadata_filters = MetadataFilters(filters=filters) if filters else None
            
            # Perform search using the index
            print(f"[DEBUG] Performing search with query: '{query}', top_k: {max_results * 2}")
            retriever = index.as_retriever(
                filters=metadata_filters,
                similarity_top_k=max_results * 2
            )
            
            search_nodes = await retriever.aretrieve(query)
            print(f"[DEBUG] Search returned {len(search_nodes)} nodes")
            
            # Process and filter results by content type
            filtered_results = []
            for node in search_nodes:
                # Convert node to result format
                result = {
                    "content": node.text,
                    "metadata": node.metadata,
                    "score": node.score if hasattr(node, 'score') else 0.0
                }
                
                # Continue with existing logic
                metadata = result.get("metadata", {})
                content_type = metadata.get("content_type", "text")
                
                # Debug: Print what we're finding
                print(f"[DEBUG] Found result with content_type: '{content_type}', metadata: {metadata}")
                
                # Check if this content type is requested
                type_match = False
                if "image" in content_types and "image" in content_type:
                    type_match = True
                elif "table" in content_types and "table" in content_type:
                    type_match = True
                elif "text" in content_types and ("text" in content_type or content_type == "text"):
                    type_match = True
                
                if type_match:
                    processed_result = await self._process_search_result(result, include_assets)
                    filtered_results.append(processed_result)
                    print(f"[DEBUG] Added result to filtered_results: {processed_result.get('content_type', 'unknown')}")
                
                if len(filtered_results) >= max_results:
                    break
            
            # Format results for output
            return self._format_results(filtered_results, query, content_types)
            
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "multimodal_retrieval", None)
            return f"Error performing multimodal search: {str(e)}"
    
    async def _process_search_result(self, result: Dict, include_assets: bool) -> Dict[str, Any]:
        """Process a search result and extract relevant information."""
        try:
            metadata = result.get("metadata", {})
            content = result.get("content", "")
            score = result.get("score", 0.0)
            
            processed = {
                "content": content,
                "score": score,
                "content_type": metadata.get("content_type", "text"),
                "source": {
                    "file_name": metadata.get("file_name", ""),
                    "file_path": metadata.get("file_path", ""),
                    "doc_id": metadata.get("multimodal_doc_id", "")
                }
            }
            
            # Add specific metadata based on content type
            content_type = metadata.get("content_type", "text")
            
            if content_type == "image_summary":
                processed["image_info"] = {
                    "image_index": metadata.get("image_index", 0),
                    "element_id": metadata.get("element_id", ""),
                    "has_asset": metadata.get("has_asset", False)
                }
                
                if include_assets and metadata.get("asset_path"):
                    asset_path = metadata.get("asset_path")
                    processed["image_info"]["asset_path"] = asset_path
                    
                    # Try to read and encode the image file
                    try:
                        import base64
                        import os
                        if os.path.exists(asset_path):
                            with open(asset_path, "rb") as image_file:
                                image_data = base64.b64encode(image_file.read()).decode('utf-8')
                                processed["image_info"]["image_data"] = image_data
                                processed["image_info"]["mime_type"] = "image/png"
                                print(f"[DEBUG] Successfully encoded image: {asset_path}")
                    except Exception as e:
                        print(f"[DEBUG] Could not read image file {asset_path}: {e}")
                        processed["image_info"]["image_read_error"] = str(e)
            
            elif content_type == "table_summary":
                processed["table_info"] = {
                    "table_index": metadata.get("table_index", 0),
                    "element_id": metadata.get("element_id", ""),
                    "has_structure": metadata.get("has_structure", False),
                    "num_columns": metadata.get("num_columns", 0),
                    "num_rows": metadata.get("num_rows", 0)
                }
            
            else:  # text content
                processed["text_info"] = {
                    "chunk_index": metadata.get("chunk_index", 0),
                    "chunk_id": metadata.get("chunk_id", ""),
                    "has_images": metadata.get("has_images", False),
                    "has_tables": metadata.get("has_tables", False),
                    "image_count": metadata.get("image_count", 0),
                    "table_count": metadata.get("table_count", 0)
                }
            
            return processed
            
        except Exception as e:
            return {
                "content": str(result),
                "score": 0.0,
                "content_type": "error",
                "error": str(e)
            }
    
    def _format_results(self, results: List[Dict], query: str, content_types: List[str]) -> str:
        """Format search results for output."""
        if not results:
            return f"No multimodal content found for query: '{query}'"
        
        output = [f"Found {len(results)} multimodal results for query: '{query}'"]
        output.append(f"Content types searched: {', '.join(content_types)}")
        output.append("")
        
        # Track image paths for Discord attachment
        image_paths = []
        
        for i, result in enumerate(results, 1):
            content_type = result.get("content_type", "text")
            score = result.get("score", 0.0)
            content = result.get("content", "")
            source = result.get("source", {})
            
            output.append(f"Result {i} (Score: {score:.3f})")
            output.append(f"Type: {content_type.upper()}")
            output.append(f"Source: {source.get('file_name', 'Unknown')}")
            
            if content_type == "image_summary":
                image_info = result.get("image_info", {})
                output.append(f"Image Index: {image_info.get('image_index', 'N/A')}")
                if image_info.get("has_asset") and image_info.get("asset_path"):
                    asset_path = image_info.get('asset_path')
                    output.append(f"Asset Path: {asset_path}")
                    
                    # Track the image path for Discord attachment
                    import os
                    if os.path.exists(asset_path):
                        image_paths.append(asset_path)
                        output.append(f"IMAGE_ATTACHMENT_PATH: {asset_path}")
                        
                output.append(f"Description: {content}")
            
            elif content_type == "table_summary":
                table_info = result.get("table_info", {})
                output.append(f"Table Index: {table_info.get('table_index', 'N/A')}")
                output.append(f"Dimensions: {table_info.get('num_rows', 'N/A')} rows x {table_info.get('num_columns', 'N/A')} columns")
                output.append(f"Summary: {content}")
            
            else:  # text content
                text_info = result.get("text_info", {})
                if text_info.get("has_images") or text_info.get("has_tables"):
                    output.append(f"Contains: {text_info.get('image_count', 0)} images, {text_info.get('table_count', 0)} tables")
                output.append(f"Content: {content[:500]}{'...' if len(content) > 500 else ''}")
            
            output.append("")
        
        return "\n".join(output)

class MultimodalAssetInput(BaseModel):
    """Input for multimodal asset retrieval tool."""
    doc_id: str = Field(..., description="Document ID containing the asset")
    element_id: str = Field(..., description="Element ID within the document")
    asset_type: str = Field(default="image", description="Type of asset to retrieve (image, table)")

class MultimodalAssetRetrieval(BaseTool):
    """Tool for retrieving asset files from multimodal documents."""
    
    name: str = "multimodal_asset_retrieval"
    description: str = """
    Retrieve asset files (images, tables) from multimodal documents.
    
    This tool can:
    - Get asset paths for images extracted from documents
    - Retrieve table markdown or HTML content
    - Access metadata about multimodal elements
    
    Use this tool when you need to:
    - View or analyze extracted images
    - Access structured table data
    - Get detailed metadata about multimodal content
    """
    
    args_schema: type = MultimodalAssetInput
    
    def _run(self, doc_id: str, element_id: str, asset_type: str = "image") -> str:
        raise NotImplementedError("Use async version")
    
    async def _arun(self, doc_id: str, element_id: str, asset_type: str = "image") -> str:
        """Retrieve asset information for a specific multimodal element."""
        try:
            # For now, search by document ID and element ID in the vector store
            from imports import Globals
            from llama_index.core.vector_stores import MetadataFilters, MetadataFilter, FilterOperator
            
            # Check if index is available
            index = Globals.get_index()
            if index is None:
                return f"No index available to search for asset {doc_id}/{element_id}"
            
            # Build filters to find the specific asset
            filters = [
                MetadataFilter(key="multimodal_doc_id", value=doc_id, operator=FilterOperator.EQ),
                MetadataFilter(key="element_id", value=element_id, operator=FilterOperator.EQ)
            ]
            
            if asset_type == "image":
                filters.append(MetadataFilter(key="content_type", value="image_summary", operator=FilterOperator.EQ))
            elif asset_type == "table":
                filters.append(MetadataFilter(key="content_type", value="table_summary", operator=FilterOperator.EQ))
            
            metadata_filters = MetadataFilters(filters=filters)
            
            # Search for the specific asset
            retriever = index.as_retriever(
                filters=metadata_filters,
                similarity_top_k=1
            )
            
            nodes = await retriever.aretrieve(f"{asset_type} {element_id}")
            
            if not nodes:
                return f"No {asset_type} asset found for document {doc_id}, element {element_id}"
            
            # Get the first matching node
            node = nodes[0]
            metadata = node.metadata
            
            if asset_type == "image":
                asset_path = metadata.get("asset_path", "")
                if asset_path:
                    # Try to read and encode the image
                    try:
                        import base64
                        import os
                        if os.path.exists(asset_path):
                            with open(asset_path, "rb") as image_file:
                                image_data = base64.b64encode(image_file.read()).decode('utf-8')
                                result = f"Image Asset Retrieved:\n"
                                result += f"Document: {doc_id}\n"
                                result += f"Element: {element_id}\n"
                                result += f"File Path: {asset_path}\n"
                                result += f"Description: {node.text}\n"
                                result += f"MIME Type: image/png\n"
                                result += f"Base64 Image Data: data:image/png;base64,{image_data}\n"
                                result += "Note: The image data above can be used directly in HTML img tags or decoded to save as a file."
                                return result
                        else:
                            return f"Image file not found at path: {asset_path}"
                    except Exception as e:
                        return f"Error reading image file {asset_path}: {e}"
                else:
                    return f"No asset path found for image {element_id} in document {doc_id}"
            
            else:
                return f"Asset type '{asset_type}' not fully supported yet. Use 'image'."
                
        except Exception as e:
            from etc.helper_functions import exception_triggered
            exception_triggered(e, "multimodal_asset_retrieval", None)
            return f"Error retrieving asset: {str(e)}"

# Helper function to extract image paths from tool responses
def extract_image_paths_from_response(response_text: str) -> List[str]:
    """
    Extract image file paths from multimodal tool responses.
    
    Returns:
        List of image file paths found in the response
    """
    import re
    
    # Look for IMAGE_ATTACHMENT_PATH markers
    image_path_pattern = r'IMAGE_ATTACHMENT_PATH: (.+?)(?:\n|$)'
    image_paths = re.findall(image_path_pattern, response_text)
    
    # Clean up the paths
    cleaned_paths = []
    for path in image_paths:
        path = path.strip()
        if path:
            cleaned_paths.append(path)
    
    return cleaned_paths

# Helper function to extract base64 image data from tool responses
def extract_image_data_from_response(response_text: str) -> Dict[str, Any]:
    """
    Extract base64 image data from multimodal tool responses.
    
    Returns:
        Dict with keys: 'has_image', 'image_data', 'mime_type', 'data_url'
    """
    result = {
        'has_image': False,
        'image_data': None,
        'mime_type': None,
        'data_url': None
    }
    
    # Look for base64 image data in the response
    if 'Base64 Image Data:' in response_text:
        try:
            # Extract the data URL
            lines = response_text.split('\n')
            for line in lines:
                if line.strip().startswith('Base64 Image Data:'):
                    data_url = line.split('Base64 Image Data:', 1)[1].strip()
                    result['data_url'] = data_url
                    result['has_image'] = True
                    
                    # Extract MIME type and base64 data
                    if data_url.startswith('data:'):
                        parts = data_url.split(',', 1)
                        if len(parts) == 2:
                            header = parts[0]  # e.g., "data:image/png;base64"
                            result['image_data'] = parts[1]  # actual base64 data
                            
                            # Extract MIME type
                            if ';' in header:
                                mime_part = header.split(';')[0]
                                result['mime_type'] = mime_part.replace('data:', '')
                    break
        except Exception as e:
            print(f"Error extracting image data: {e}")
    
    return result

def save_image_from_response(response_text: str, output_path: str) -> bool:
    """
    Save an image from a multimodal tool response to a file.
    
    Args:
        response_text: The response text from a multimodal tool
        output_path: Path where to save the image file
        
    Returns:
        True if successful, False otherwise
    """
    try:
        import base64
        import os
        
        # Extract image data
        image_info = extract_image_data_from_response(response_text)
        
        if not image_info['has_image']:
            print("No image data found in response")
            return False
        
        # Decode and save the image
        image_data = base64.b64decode(image_info['image_data'])
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        
        with open(output_path, 'wb') as f:
            f.write(image_data)
        
        print(f"Image saved to: {output_path}")
        return True
        
    except Exception as e:
        print(f"Error saving image: {e}")
        return False

# Create tool instances
multimodal_retrieval_tool = MultimodalRetrievalTool()
multimodal_asset_tool = MultimodalAssetRetrieval()