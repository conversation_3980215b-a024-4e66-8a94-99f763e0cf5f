<div class="account-container">
    <div class="account-header">
        <div class="profile-avatar">
            <span class="avatar-initials">{initials}</span>
        </div>
        <h1 class="account-name">{display_name}</h1>
        <p class="account-subtitle">Account Settings</p>
        <div class="status-badge {status_badge_class}">
            {status_badge_text}
        </div>
    </div>
    
    <div class="account-settings-grid">
        <!-- Preferences Section -->
        <div class="settings-card card">
            <h3 class="card-title">Preferences</h3>
            <form id="preferencesForm" onsubmit="updateAccountPreferences(event)">
                <div class="form-group">
                    <label class="form-label">Language</label>
                    <select id="preferred_language" class="form-input">
                        {language_options}
                    </select>
                </div>
                
                <div class="form-group">
                    <label class="form-label">Zaira Voice</label>
                    <select id="zaira_voice" class="form-input">
                        {voice_options}
                    </select>
                    <small class="form-help">Choose the AI response style that best fits your needs.</small>
                </div>
                
                <div class="form-group">
                    <label class="form-checkbox">
                        <input type="checkbox" id="enable_followup_questions" {followup_checked}>
                        <span class="form-checkbox-label">Enable follow-up questions</span>
                    </label>
                    <small class="form-help">Allow Zaira to ask clarifying questions to better understand your requests.</small>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Save Preferences</button>
                </div>
            </form>
        </div>
        
        <!-- Company Information (for SYSTEM user) -->
        {company_section}
        
        <!-- Privacy & Data Section -->
        <div class="settings-card card">
            <h3 class="card-title">Privacy & Data</h3>
            <form id="privacyForm" onsubmit="updatePrivacySettings(event)">
                <div class="form-group">
                    <label class="form-label">Clear Chat History Interval</label>
                    <select id="chat_history_interval" class="form-input">
                        {interval_options}
                    </select>
                    <small class="form-help">Automatically clear chat history at specified intervals for privacy.</small>
                </div>
                
                <div class="form-actions-grid">
                    <button type="button" class="btn btn-warning" onclick="clearConnectors()">Clear Connectors</button>
                    <button type="button" class="btn btn-danger" onclick="clearAllData()">Clear Data</button>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Save Privacy Settings</button>
                </div>
            </form>
        </div>
        
        <!-- Security Section -->
        <div class="settings-card card">
            <h3 class="card-title">Security</h3>
            <form id="securityForm" onsubmit="updateSecuritySettings(event)">
                <div class="form-group">
                    <label class="form-checkbox">
                        <input type="checkbox" id="two_factor_enabled" {two_factor_checked}>
                        <span class="form-checkbox-label">Enable 2-factor authentication</span>
                    </label>
                    <small class="form-help">Add an extra layer of security to your account with 2FA.</small>
                </div>
                
                <div class="security-actions">
                    <button type="button" class="btn btn-warning" onclick="logoutAllDevices()">Log out of all devices</button>
                </div>
                
                <div class="form-actions">
                    <button type="submit" class="btn btn-primary">Save Security Settings</button>
                </div>
            </form>
        </div>
    </div>
</div>