from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
from unittest.mock import patch, MagicMock, AsyncMock, call
from pathlib import Path
from tempfile import TemporaryDirectory

# Import the module we're testing
import etc.setup as setup_mod

class TestSetup:
    """Test suite for setup.py"""

    def test_imports_successful(self):
        """Test that all imports in setup.py are successful"""
        # If we can import the module, all imports are successful
        assert setup_mod is not None

    def test_module_has_expected_functions(self):
        """Test setup.py has expected functions"""
        expected_functions = ['init', 'late_init', 'generateEmbedding', 'loadEmbedding']
        
        for func_name in expected_functions:
            assert hasattr(setup_mod, func_name), f"Function {func_name} not found"
            assert callable(getattr(setup_mod, func_name)), f"Function {func_name} is not callable"

    @patch('etc.setup.is_claude_environment')
    def test_conditional_imports_claude_environment(self, mock_is_claude):
        """Test conditional imports in Claude environment"""
        mock_is_claude.return_value = True
        
        # Re-import to test conditional logic
        import importlib
        importlib.reload(setup_mod)
        
        # Check that mock classes are created
        assert hasattr(setup_mod, 'MyDiscordBot')
        assert hasattr(setup_mod, 'MyTeamsBot')
        assert hasattr(setup_mod, 'MySlackBot')
        assert hasattr(setup_mod, 'MyWhatsappBot')

    @patch('etc.setup.is_claude_environment')
    def test_conditional_imports_non_claude_environment(self, mock_is_claude):
        """Test conditional imports in non-Claude environment"""
        mock_is_claude.return_value = False
        
        # Check that the bot classes are available
        assert hasattr(setup_mod, 'MyDiscordBot')
        assert hasattr(setup_mod, 'MyTeamsBot')
        assert hasattr(setup_mod, 'MySlackBot')
        assert hasattr(setup_mod, 'MyWhatsappBot')

    def test_mock_bot_class_methods(self):
        """Test MockBot class has expected methods"""
        # Test that mock bot classes exist and have required methods
        # We test the actual MockBot class from the source
        import etc.setup
        
        # Create a mock bot like the one in setup.py
        class TestMockBot:
            @staticmethod
            async def setup(): pass
            @staticmethod
            async def late_setup(): pass
        
        # Check that our test mock has the expected methods
        assert hasattr(TestMockBot, 'setup')
        assert hasattr(TestMockBot, 'late_setup')
        assert callable(TestMockBot.setup)
        assert callable(TestMockBot.late_setup)

    @pytest.mark.asyncio
    @patch('etc.setup.LogFire')
    @patch('etc.setup.ZairaSettings')
    @patch('etc.setup.OptimumEmbedding')
    @patch('etc.setup.BASE_DIR')
    @patch('etc.setup.SparseTextEmbedding')
    @patch('langchain_openai.ChatOpenAI')
    @patch('etc.setup.PostgreSQLManager')
    @patch('etc.setup.APIEndpoint')
    @patch('etc.setup.OAuth2Verifier')
    @patch('etc.setup.Globals')
    @patch('etc.setup.QDrantManager')
    @patch('etc.setup.RetrievalManager')
    @patch('etc.setup.MeltanoManager')
    @patch('etc.setup.SupervisorManager')
    @patch('etc.setup.AgnoManager')
    @patch('etc.setup.ScheduledRequestPersistenceManager.setup')
    @patch('etc.setup.SystemUserManager')
    @patch('etc.setup.generateEmbedding')
    @patch('etc.setup.late_init')
    async def test_init_new_project(self, mock_late_init, mock_generate_embedding, 
                                   mock_system_user_manager, mock_scheduled_request_persistence_manager_setup,
                                   mock_agno_manager, mock_supervisor_manager, 
                                   mock_meltano_manager, mock_retrieval_manager,
                                   mock_qdrant_manager, mock_globals, mock_oauth2_verifier,
                                   mock_api_endpoint, mock_postgresql_manager,
                                   mock_chat_openai, mock_sparse_text_embedding,
                                   mock_base_dir, mock_optimum_embedding, 
                                   mock_zaira_settings, mock_logfire):
        """Test init function with new project"""
        # Setup mocks - create a real Path-like object that supports / operations
        mock_base_path = MagicMock()
        mock_base_path.__truediv__ = MagicMock(return_value=Path('/test/path/bge_onnx'))
        mock_base_dir.return_value = mock_base_path
        
        mock_postgresql_manager.setup = AsyncMock()
        mock_postgresql_manager.create_database = AsyncMock()
        mock_postgresql_manager.connect_to_database = AsyncMock()
        mock_api_endpoint.setup = AsyncMock()
        mock_oauth2_verifier.setup = AsyncMock()
        mock_oauth2_verifier.get_token = AsyncMock(return_value='test_token')
        mock_globals.set_debug_values = MagicMock()
        mock_qdrant_manager.setup = AsyncMock()
        mock_retrieval_manager.setup = AsyncMock()
        mock_meltano_manager.setup = AsyncMock()
        mock_supervisor_manager.setup = AsyncMock()
        mock_agno_manager.setup = AsyncMock()
        mock_scheduled_request_persistence_manager_setup.return_value = AsyncMock()
        mock_system_user_manager.setup = AsyncMock()
        mock_generate_embedding.return_value = AsyncMock()
        mock_globals.set_index = MagicMock()
        mock_late_init.return_value = AsyncMock()
        mock_logfire.setup = AsyncMock()
        
        # Mock the Path constructor to return a mock that has exists() and is_dir() methods
        with patch('etc.setup.MyDiscordBot') as mock_discord_bot, \
             patch('etc.setup.MyTeamsBot') as mock_teams_bot, \
             patch('etc.setup.MySlackBot') as mock_slack_bot, \
             patch('etc.setup.MyWhatsappBot') as mock_whatsapp_bot, \
             patch('etc.setup.Path') as mock_path_constructor:
            
            mock_discord_bot.setup = AsyncMock()
            mock_teams_bot.setup = AsyncMock()
            mock_slack_bot.setup = AsyncMock()
            mock_whatsapp_bot.setup = AsyncMock()
            
            # Create a mock folder that behaves like a path
            mock_folder = MagicMock()
            mock_folder.exists.return_value = True
            mock_folder.is_dir.return_value = True
            mock_path_constructor.return_value = mock_folder
            
            # Call the function
            await setup_mod.init(
                newProject=True,
                DATA_DIR='test_data',
                PERSIST_DIR='test_persist',
                parsers=MagicMock()
            )
            
            # Verify setup calls
            mock_logfire.setup.assert_called_once()
            mock_postgresql_manager.setup.assert_called_once()
            mock_api_endpoint.setup.assert_called_once()
            mock_oauth2_verifier.setup.assert_called_once()
            mock_qdrant_manager.setup.assert_called_once()
            mock_retrieval_manager.setup.assert_called_once()
            mock_meltano_manager.setup.assert_called_once()
            mock_supervisor_manager.setup.assert_called_once()
            mock_agno_manager.setup.assert_called_once()
            mock_system_user_manager.setup.assert_called_once()
            mock_generate_embedding.assert_called_once()
            mock_late_init.assert_called_once()

    @pytest.mark.asyncio
    @patch('etc.setup.LogFire')
    @patch('etc.setup.ZairaSettings')
    @patch('etc.setup.OptimumEmbedding')
    @patch('etc.setup.BASE_DIR')
    @patch('etc.setup.SparseTextEmbedding')
    @patch('langchain_openai.ChatOpenAI')
    @patch('etc.setup.PostgreSQLManager')
    @patch('etc.setup.APIEndpoint')
    @patch('etc.setup.OAuth2Verifier')
    @patch('etc.setup.Globals')
    @patch('etc.setup.QDrantManager')
    @patch('etc.setup.RetrievalManager')
    @patch('etc.setup.MeltanoManager')
    @patch('etc.setup.SupervisorManager')
    @patch('etc.setup.AgnoManager')
    @patch('etc.setup.ScheduledRequestPersistenceManager.setup')
    @patch('etc.setup.SystemUserManager')
    @patch('etc.setup.loadEmbedding')
    @patch('etc.setup.late_init')
    async def test_init_existing_project(self, mock_late_init, mock_load_embedding,
                                        mock_system_user_manager, mock_scheduled_request_persistence_manager_setup2,
                                        mock_agno_manager, mock_supervisor_manager,
                                        mock_meltano_manager, mock_retrieval_manager,
                                        mock_qdrant_manager, mock_globals, mock_oauth2_verifier,
                                        mock_api_endpoint, mock_postgresql_manager,
                                        mock_chat_openai, mock_sparse_text_embedding,
                                        mock_base_dir, mock_optimum_embedding,
                                        mock_zaira_settings, mock_logfire):
        """Test init function with existing project"""
        # Setup mocks - create a real Path-like object that supports / operations
        mock_base_path = MagicMock()
        mock_base_path.__truediv__ = MagicMock(return_value=Path('/test/path/bge_onnx'))
        mock_base_dir.return_value = mock_base_path
        
        mock_postgresql_manager.setup = AsyncMock()
        mock_postgresql_manager.create_database = AsyncMock()
        mock_postgresql_manager.connect_to_database = AsyncMock()
        mock_api_endpoint.setup = AsyncMock()
        mock_oauth2_verifier.setup = AsyncMock()
        mock_oauth2_verifier.get_token = AsyncMock(return_value='test_token')
        mock_globals.set_debug_values = MagicMock()
        mock_qdrant_manager.setup = AsyncMock()
        mock_retrieval_manager.setup = AsyncMock()
        mock_meltano_manager.setup = AsyncMock()
        mock_supervisor_manager.setup = AsyncMock()
        mock_agno_manager.setup = AsyncMock()
        mock_scheduled_request_persistence_manager_setup.return_value = AsyncMock()
        mock_system_user_manager.setup = AsyncMock()
        mock_load_embedding.return_value = AsyncMock()
        mock_globals.set_index = MagicMock()
        mock_late_init.return_value = AsyncMock()
        mock_logfire.setup = AsyncMock()
        
        # Mock the Path constructor to return a mock that has exists() and is_dir() methods
        with patch('etc.setup.MyDiscordBot') as mock_discord_bot, \
             patch('etc.setup.MyTeamsBot') as mock_teams_bot, \
             patch('etc.setup.MySlackBot') as mock_slack_bot, \
             patch('etc.setup.MyWhatsappBot') as mock_whatsapp_bot, \
             patch('etc.setup.Path') as mock_path_constructor:
            
            mock_discord_bot.setup = AsyncMock()
            mock_teams_bot.setup = AsyncMock()
            mock_slack_bot.setup = AsyncMock()
            mock_whatsapp_bot.setup = AsyncMock()
            
            # Create a mock folder that behaves like a path
            mock_folder = MagicMock()
            mock_folder.exists.return_value = True
            mock_folder.is_dir.return_value = True
            mock_path_constructor.return_value = mock_folder
            
            # Call the function
            await setup_mod.init(
                newProject=False,
                DATA_DIR='test_data',
                PERSIST_DIR='test_persist',
                parsers=MagicMock()
            )
            
            # Verify load_embedding was called instead of generate_embedding
            mock_load_embedding.assert_called_once()

    @pytest.mark.asyncio
    @patch('etc.setup.create_top_level_supervisor')
    @patch('etc.setup.create_top_output_supervisor')
    @patch('etc.setup.APIEndpoint')
    @patch('etc.setup.MySlackBot')
    @patch('etc.setup.ScheduledRequestPersistenceManager')
    @patch('etc.setup.SystemUserManager')
    @patch('etc.setup.LogFire')
    async def test_late_init(self, mock_logfire, mock_system_user_manager,
                            mock_scheduled_request_persistence_manager, mock_slack_bot,
                            mock_api_endpoint, mock_create_top_output_supervisor,
                            mock_create_top_level_supervisor):
        """Test late_init function"""
        # Setup mocks
        mock_create_top_level_supervisor.return_value = AsyncMock()
        mock_create_top_output_supervisor.return_value = AsyncMock()
        mock_api_endpoint.late_setup = AsyncMock()
        mock_slack_bot.late_setup = AsyncMock()
        mock_scheduled_request_persistence_manager.late_setup = AsyncMock()
        mock_system_user_manager.late_setup = AsyncMock()
        
        # Call the function
        await setup_mod.late_init()
        
        # Verify calls
        mock_create_top_level_supervisor.assert_called_once()
        mock_create_top_output_supervisor.assert_called_once()
        mock_api_endpoint.late_setup.assert_called_once()
        mock_slack_bot.late_setup.assert_called_once()
        mock_scheduled_request_persistence_manager.late_setup.assert_called_once()
        mock_system_user_manager.late_setup.assert_called_once()
        mock_logfire.log.assert_called_once_with('INIT', 'Setup has completed.')

    @pytest.mark.asyncio
    @patch('managers.manager_prompts.PromptManager')
    @patch('etc.setup.etc.helper_functions.folder_has_files')
    @patch('etc.setup.SimpleDirectoryReader')
    @patch('etc.setup.Globals')
    @patch('etc.setup.PostgreSQLManager')
    @patch('subprocess.run')
    @patch('etc.setup.QDrantManager')
    @patch('etc.setup.ZairaSettings')
    @patch('etc.setup.QdrantVectorStore')
    @patch('etc.setup.StorageContext')
    @patch('etc.setup.VectorStoreIndex')
    @patch('etc.setup.etc.helper_functions.exception_triggered')
    async def test_generate_embedding(self, mock_exception_triggered, mock_vector_store_index,
                                     mock_storage_context, mock_qdrant_vector_store,
                                     mock_zaira_settings, mock_qdrant_manager,
                                     mock_subprocess_run, mock_postgresql_manager,
                                     mock_globals, mock_simple_directory_reader,
                                     mock_folder_has_files, mock_prompt_manager):
        """Test generateEmbedding function"""
        # Setup mocks
        mock_prompt_manager.setDefaultPrompts = AsyncMock()
        mock_folder_has_files.return_value = True
        mock_documents = [MagicMock()]
        mock_simple_directory_reader.return_value.load_data.return_value = mock_documents
        mock_globals.is_docker.return_value = False
        mock_postgresql_manager.delete_database = AsyncMock()
        mock_postgresql_manager.create_database = AsyncMock()
        mock_subprocess_run.return_value = MagicMock()
        
        mock_qdrant_client = MagicMock()
        mock_qdrant_client.delete_collection = AsyncMock()
        mock_qdrant_client.create_collection = AsyncMock()
        mock_qdrant_manager.GetAsyncClient.return_value = mock_qdrant_client
        mock_qdrant_manager.GetClient.return_value = MagicMock()
        
        mock_zaira_settings.sparse_embed_model = MagicMock()
        mock_zaira_settings.OllamaSettings.return_value.embed_model = MagicMock()
        
        mock_vector_store = MagicMock()
        mock_qdrant_vector_store.return_value = mock_vector_store
        mock_storage_context_instance = MagicMock()
        mock_storage_context.from_defaults.return_value = mock_storage_context_instance
        
        mock_index = MagicMock()
        mock_index.set_index_id = MagicMock()
        mock_index.storage_context.persist = MagicMock()
        mock_vector_store_index.from_documents.return_value = mock_index
        
        with patch('builtins.print') as mock_print:
            # Call the function
            result = await setup_mod.generateEmbedding(
                DATA_DIR='test_data',
                PERSIST_DIR='test_persist',
                parsers=MagicMock()
            )
            
            # Verify calls
            mock_prompt_manager.setDefaultPrompts.assert_called_once()
            mock_folder_has_files.assert_called_once_with('test_data')
            mock_postgresql_manager.delete_database.assert_called_once_with('meltanodb')
            mock_postgresql_manager.create_database.assert_called_once_with('meltanodb')
            mock_qdrant_client.delete_collection.assert_called_once()
            mock_qdrant_client.create_collection.assert_called_once()
            mock_vector_store_index.from_documents.assert_called_once()
            mock_index.set_index_id.assert_called_once_with(index_id='mainIndex')
            mock_index.storage_context.persist.assert_called_once()
            mock_print.assert_called()
            
            assert result is mock_index

    @pytest.mark.asyncio
    @patch('managers.manager_prompts.PromptManager')
    @patch('etc.setup.QDrantManager')
    @patch('etc.setup.ZairaSettings')
    @patch('etc.setup.QdrantVectorStore')
    @patch('etc.setup.StorageContext')
    @patch('etc.setup.VectorStoreIndex')
    @patch('etc.setup.etc.helper_functions.folder_has_files')
    @patch('etc.setup.SimpleDirectoryReader')
    async def test_load_embedding(self, mock_simple_directory_reader, mock_folder_has_files,
                                 mock_vector_store_index, mock_storage_context,
                                 mock_qdrant_vector_store, mock_zaira_settings,
                                 mock_qdrant_manager, mock_prompt_manager):
        """Test loadEmbedding function"""
        # Setup mocks
        mock_prompt_manager.loadDefaultPrompts = AsyncMock()
        mock_qdrant_manager.GetClient.return_value = MagicMock()
        mock_qdrant_manager.GetAsyncClient.return_value = MagicMock()
        mock_zaira_settings.sparse_embed_model = MagicMock()
        mock_zaira_settings.OllamaSettings.return_value.embed_model = MagicMock()
        
        mock_vector_store = MagicMock()
        mock_qdrant_vector_store.return_value = mock_vector_store
        mock_storage_context_instance = MagicMock()
        mock_storage_context.from_defaults.return_value = mock_storage_context_instance
        
        mock_folder_has_files.return_value = True
        mock_documents = [MagicMock()]
        mock_simple_directory_reader.return_value.load_data.return_value = mock_documents
        
        mock_index = MagicMock()
        mock_index.set_index_id = MagicMock()
        mock_vector_store_index.from_vector_store.return_value = mock_index
        
        with patch('builtins.print') as mock_print:
            # Call the function
            result = await setup_mod.loadEmbedding(
                DATA_DIR='test_data',
                PERSIST_DIR='test_persist',
                parsers=MagicMock()
            )
            
            # Verify calls
            mock_prompt_manager.loadDefaultPrompts.assert_called_once()
            mock_qdrant_vector_store.assert_called()
            mock_storage_context.from_defaults.assert_called_once()
            mock_folder_has_files.assert_called_once_with('test_data')
            mock_vector_store_index.from_vector_store.assert_called_once()
            mock_index.set_index_id.assert_called_once_with(index_id='mainIndex')
            mock_print.assert_called()
            
            assert result is mock_index

    @patch('etc.setup.OptimumEmbedding')
    @patch('etc.setup.BASE_DIR')
    def test_optimum_embedding_creation(self, mock_base_dir, mock_optimum_embedding):
        """Test OptimumEmbedding creation logic"""
        mock_base_dir.return_value = Path('/test/path')
        mock_folder = MagicMock()
        mock_folder.exists.return_value = False
        mock_folder.is_dir.return_value = False
        
        with patch('pathlib.Path') as mock_path_class:
            mock_path_class.return_value = mock_folder
            
            # This would be called in the init function
            if not mock_folder.exists() or not mock_folder.is_dir():
                mock_optimum_embedding.create_and_save_optimum_model.assert_not_called()

    def test_logging_configuration(self):
        """Test logging configuration components"""
        import logging
        from sys import stdout
        
        # Test that logging components are available
        assert logging.basicConfig is not None
        assert logging.getLogger is not None
        assert logging.StreamHandler is not None
        assert stdout is not None

    def test_constants_and_imports(self):
        """Test that required constants and imports are available"""
        # Test that required constants exist
        assert hasattr(setup_mod, 'BASE_DIR') or 'BASE_DIR' in str(setup_mod)
        assert hasattr(setup_mod, 'EMBEDDING_MODEL') or 'EMBEDDING_MODEL' in str(setup_mod)
        assert hasattr(setup_mod, 'TIMEOUT_LIMIT') or 'TIMEOUT_LIMIT' in str(setup_mod)
        assert hasattr(setup_mod, 'LLM_MODEL') or 'LLM_MODEL' in str(setup_mod)
        assert hasattr(setup_mod, 'EMBEDDING_SIZE') or 'EMBEDDING_SIZE' in str(setup_mod)

    def test_zaira_settings_configuration(self):
        """Test ZairaSettings configuration in init"""
        # Test that ZairaSettings is referenced
        import inspect
        setup_source = inspect.getsource(setup_mod)
        assert 'ZairaSettings' in setup_source

    def test_database_names_compliance(self):
        """Test that only allowed database names are used"""
        # Check that only 'vectordb' and 'meltanodb' are used
        import inspect
        setup_source = inspect.getsource(setup_mod)
        
        # Should contain vectordb and meltanodb references
        assert 'vectordb' in setup_source
        assert 'meltanodb' in setup_source
        
        # Should not contain other database names
        forbidden_names = ['mydb', 'testdb']
        for name in forbidden_names:
            assert name not in setup_source

    def test_async_function_definitions(self):
        """Test that functions are properly defined as async"""
        import inspect
        
        async_functions = ['init', 'late_init', 'generateEmbedding', 'loadEmbedding']
        
        for func_name in async_functions:
            func = getattr(setup_mod, func_name)
            assert inspect.iscoroutinefunction(func), f"Function {func_name} should be async"

    def test_manager_setup_sequence(self):
        """Test that manager setup follows the correct sequence"""
        # This is tested implicitly in the init tests, but we can verify
        # the sequence is logical by checking the imports
        manager_imports = [
            'PostgreSQLManager',
            'APIEndpoint',
            'OAuth2Verifier',
            'QDrantManager',
            'RetrievalManager',
            'MeltanoManager',
            'SupervisorManager',
            'AgnoManager',
            'SystemUserManager'
        ]
        
        import inspect
        setup_source = inspect.getsource(setup_mod)
        for manager in manager_imports:
            assert manager in setup_source, f"Manager {manager} not found in imports"

    def test_error_handling_in_generate_embedding(self):
        """Test error handling in generateEmbedding"""
        # Test that exception_triggered is available for error handling
        import inspect
        setup_source = inspect.getsource(setup_mod)
        assert 'exception_triggered' in setup_source