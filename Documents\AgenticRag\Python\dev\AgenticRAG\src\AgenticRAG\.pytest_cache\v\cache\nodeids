["tests/integration/test_multimodal_workflow.py::TestMultimodalPerformance::test_chunking_performance", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_asset_management_workflow", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_end_to_end_storage_workflow", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_error_handling_workflow", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_meltano_multimodal_integration", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_multimodal_chunking_workflow", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_multimodal_search_workflow", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_table_summarization_workflow", "tests/integration/test_multimodal_workflow.py::TestMultimodalWorkflow::test_vision_model_integration", "tests/integration/test_whatsapp_workflow.py::TestWhatsAppWorkflow::test_webhook_verification_workflow", "tests/test_real/test_real_email.py::TestRealEmail::test_real_email", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_agenda_generator_generate_event_details_exception", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_agenda_generator_generate_event_details_fallback", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_agenda_generator_generate_event_details_success", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_agenda_generator_tool_exception_handling", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_agenda_generator_tool_initialization", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_agenda_generator_tool_successful_generation", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_agenda_planner_generate_content_exception", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_agenda_planner_generate_content_success", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_agenda_planner_tool_exception_handling", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_agenda_planner_tool_initialization", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_agenda_planner_tool_run_not_implemented", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_agenda_planner_tool_successful_generation", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_agenda_planning_tool_decorator_exception", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_agenda_planning_tool_decorator_success", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_create_supervisor_agenda_planner", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_event_generation_tool_decorator_exception", "tests/unit/email_agenda/test_task_agenda_planner_refactored.py::TestAgendaPlannerRefactored::test_event_generation_tool_decorator_success", "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_create_task_email_generator", "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_missing_recipient", "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_missing_user_email_with_callback", "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_no_user_email", "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_state_sections_missing", "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_subject_generation_missing", "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_tool_basic_functionality", "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_tool_exception_handling", "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_tool_initialization", "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_tool_instance", "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_tool_run_not_implemented", "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_user_cancellation", "tests/unit/email_agenda/test_task_email_generator.py::TestEmailGeneratorTask::test_email_generator_user_rejection", "tests/unit/email_agenda/test_task_imap.py::TestIMAPTask::test_create_task_imap_receiver", "tests/unit/email_agenda/test_task_imap.py::TestIMAPTask::test_supervisor_task_imap_callback_response_attribute", "tests/unit/email_agenda/test_task_imap.py::TestIMAPTask::test_supervisor_task_imap_initialization", "tests/unit/email_agenda/test_task_imap.py::TestIMAPTask::test_supervisor_task_imap_llm_call_login_fallback", "tests/unit/email_agenda/test_task_imap.py::TestIMAPTask::test_supervisor_task_imap_llm_call_no_emails", "tests/unit/email_agenda/test_task_imap.py::TestIMAPTask::test_supervisor_task_imap_llm_call_non_ssl_connection", "tests/unit/email_agenda/test_task_imap.py::TestIMAPTask::test_supervisor_task_imap_llm_call_selective_saving", "tests/unit/email_agenda/test_task_imap.py::TestIMAPTask::test_supervisor_task_imap_llm_call_ssl_connection", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_agenda_sender_direct_tool_initialization", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_agenda_sender_direct_tool_success", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_agenda_sender_tool_approval_cancelled", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_agenda_sender_tool_creation_failure", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_agenda_sender_tool_exception_handling", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_agenda_sender_tool_initialization", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_agenda_sender_tool_run_not_implemented", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_agenda_sender_tool_successful_creation", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_calendar_create_event_input_schema", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_create_calendar_event_missing_required_fields", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_create_calendar_event_no_bearer_token", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_create_calendar_event_no_imports", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_create_calendar_event_oauth_failure", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_create_calendar_event_success", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_direct_calendar_create_tool_exception", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_direct_calendar_create_tool_success", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_format_event_preview", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_format_event_preview_exception", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_get_calendar_tools_no_bearer_token", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_get_calendar_tools_no_imports", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_get_calendar_tools_oauth_exception", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_get_calendar_tools_success", "tests/unit/email_agenda/test_task_out_agenda.py::TestAgendaOutputTask::test_supervisor_task_agenda_initialization", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_create_out_task_email", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_create_supervisor_email_sender", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_email_approval_state_validation", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_email_client_send_email_unsupported_provider", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_email_client_smart_smtp_login_success", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_email_sender_direct_tool_functionality", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_email_sender_direct_tool_initialization", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_email_sender_direct_tool_run_not_implemented", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_email_sender_state_update_on_success", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_email_sender_tool_email_not_approved", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_email_sender_tool_exception_handling", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_email_sender_tool_gmail_configuration", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_email_sender_tool_initialization", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_email_sender_tool_instances", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_email_sender_tool_run_not_implemented", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_gmail_api_integration_scenarios[api_error-True-smtp_fallback]", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_gmail_api_integration_scenarios[api_no_token-False-smtp_fallback]", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_gmail_api_integration_scenarios[api_success-True-gmail_api]", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_smtp_configuration_variants[plain_port_25-25-False]", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_smtp_configuration_variants[ssl_port_465-465-True]", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_smtp_configuration_variants[starttls_port_587-587-False]", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_smtp_error_handling_scenarios[535_auth_error-2-retry_with_fallback]", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_smtp_error_handling_scenarios[550_non_auth_error-0-immediate_failure]", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_smtp_error_handling_scenarios[connection_timeout-1-single_retry]", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_supervisor_task_email_integration", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_supervisor_task_email_llm_call", "tests/unit/email_agenda/test_task_out_email.py::TestEmailSenderTask::test_supervisor_task_email_llm_call_no_task", "tests/unit/test_agenda_oauth_scopes.py::TestAgendaOAuthScopes::test_agenda_sender_tool_uses_filtered_scopes", "tests/unit/test_agenda_oauth_scopes.py::TestAgendaOAuthScopes::test_gcalendar_scopes_filtering", "tests/unit/test_agenda_oauth_scopes.py::TestAgendaOAuthScopes::test_scope_filtering_logic", "tests/unit/test_agenda_oauth_scopes.py::TestAgendaOAuthScopes::test_verify_oauth_verifier_setup", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_ask_delayed_endpoint", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_ask_delayed_missing_params", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_ask_endpoint", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_ask_url_endpoint", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_convert_sql_to_vectorstore", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_dashboard_endpoint", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_dashboard_unauthorized", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_embedding_onnx_missing_input", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_embedding_onnx_success", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_embedding_openai_invalid_input", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_embedding_openai_success", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_handle_file_upload_no_files", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_handle_file_upload_success", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_home_endpoint", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_initialization", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_late_setup", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_load_content_dashboard", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_login_endpoint", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_restart_endpoint_success", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_restart_endpoint_unauthorized", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_setup", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_singleton_pattern", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_slack_events_normal", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_slack_events_verification", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_start_app", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_validate_login_failure", "tests/unit/test_api_endpoint.py::TestAPIEndpoint::test_validate_login_success", "tests/unit/test_api_endpoint.py::TestAPIEndpointMiddleware::test_ip_check_middleware", "tests/unit/test_api_endpoint.py::TestAPIEndpointMiddleware::test_ip_check_middleware_no_cf_header", "tests/unit/test_api_endpoint.py::TestAPIEndpointMiddleware::test_logfire_middleware", "tests/unit/test_authentication_oauth.py::TestOAuthErrorHandling::test_invalid_client_credentials", "tests/unit/test_authentication_oauth.py::TestOAuthErrorHandling::test_network_timeout_handling", "tests/unit/test_authentication_oauth.py::TestOAuthErrorHandling::test_scope_insufficient_error", "tests/unit/test_authentication_oauth.py::TestOAuthSecurity::test_csrf_protection", "tests/unit/test_authentication_oauth.py::TestOAuthSecurity::test_token_expiration_validation", "tests/unit/test_authentication_oauth.py::TestOAuthUnified::test_environment_token_scenarios[empty_env_var--EMPTY_KEY-default_value]", "tests/unit/test_authentication_oauth.py::TestOAuthUnified::test_environment_token_scenarios[missing_env_var-None-MISSING_KEY-default_value]", "tests/unit/test_authentication_oauth.py::TestOAuthUnified::test_environment_token_scenarios[valid_env_var-test_value-TEST_KEY-test_value]", "tests/unit/test_authentication_oauth.py::TestOAuthUnified::test_token_refresh_mechanism", "tests/unit/test_authentication_oauth.py::TestOAuthUnified::test_token_verification_scenarios[expired_token-database_response2-None]", "tests/unit/test_authentication_oauth.py::TestOAuthUnified::test_token_verification_scenarios[invalid_token-None-None]", "tests/unit/test_authentication_oauth.py::TestOAuthUnified::test_token_verification_scenarios[valid_token-database_response0-token_data]", "tests/unit/test_authentication_oauth.py::TestServiceSpecificOAuth::test_oauth_app_configuration", "tests/unit/test_authentication_oauth.py::TestServiceSpecificOAuth::test_oauth_token_storage", "tests/unit/test_authentication_oauth.py::TestServiceSpecificOAuth::test_oauth_verifier_setup", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_initialization", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_member_join", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_member_join_wrong_guild", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_message_no_exclamation", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_message_processing", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_message_with_attachments", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_message_with_reply", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_message_wrong_guild", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_on_ready", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_run_discord_bot_internal_no_client", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_run_discord_bot_internal_with_client", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_send_a_discord_message", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_send_discord_broadcast", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_send_discord_broadcast_no_client", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_setup_debug_mode", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_setup_no_token", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_setup_production_mode", "tests/unit/test_discord_endpoint.py::TestMyDiscordBot::test_singleton_pattern", "tests/unit/test_discord_endpoint.py::TestMyDiscordBotEdgeCases::test_private_message_handling", "tests/unit/test_discord_endpoint.py::TestMyDiscordBotEdgeCases::test_user_creation_on_message", "tests/unit/test_discord_endpoint.py::TestMyDiscordBotThreading::test_exception_handling", "tests/unit/test_discord_endpoint.py::TestMyDiscordBotThreading::test_threading_setup", "tests/unit/test_discord_endpoint.py::TestMyDiscordClient::test_on_guild_join", "tests/unit/test_discord_endpoint.py::TestMyDiscordClient::test_on_member_join", "tests/unit/test_discord_endpoint.py::TestMyDiscordClient::test_on_member_leave", "tests/unit/test_discord_endpoint.py::TestMyDiscordClient::test_on_message", "tests/unit/test_discord_endpoint.py::TestMyDiscordClient::test_on_message_bot_user", "tests/unit/test_discord_endpoint.py::TestMyDiscordClient::test_on_ready", "tests/unit/test_discord_reply_functionality.py::TestDiscordReplyFunctionality::test_mybot_generic_handles_reply_context", "tests/unit/test_discord_reply_functionality.py::TestDiscordReplyFunctionality::test_no_reply_context_fallback", "tests/unit/test_discord_reply_functionality.py::TestDiscordReplyFunctionality::test_parent_message_id_preservation", "tests/unit/test_discord_reply_functionality.py::TestDiscordReplyFunctionality::test_reply_context_class_methods", "tests/unit/test_discord_reply_functionality.py::TestDiscordReplyFunctionality::test_reply_context_creation_with_reply", "tests/unit/test_discord_reply_functionality.py::TestDiscordReplyFunctionality::test_reply_context_creation_without_reply", "tests/unit/test_discord_reply_functionality.py::TestDiscordReplyFunctionality::test_reply_context_message_formatting", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_call_cmd_debug_docker_environment", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_call_cmd_debug_non_docker_error", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_call_cmd_debug_non_docker_success", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_call_network_docker_docker_environment_error", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_call_network_docker_docker_environment_success", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_call_network_docker_non_docker_environment", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_convert_key_value_to_string_empty_kwargs", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_convert_key_value_to_string_multiple_pairs", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_convert_key_value_to_string_single_pair", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_convert_key_value_to_string_underscore_replacement", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_create_html_out_docker", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_create_html_out_non_docker", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_exception_triggered_debug_mode", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_exception_triggered_production_mode", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_exception_triggered_with_user", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_folder_has_files_empty_folder", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_folder_has_files_existing_folder_with_files", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_folder_has_files_existing_folder_without_files", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_folder_has_files_nonexistent_folder", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_get_any_message_as_type_contains_expected_types", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_get_any_message_as_type_returns_tuple", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_get_password_consistent_output", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_get_password_different_users", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_get_password_empty_username", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_get_password_unicode_username", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_get_value_from_env_existing_value", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_get_value_from_env_list_parsing", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_get_value_from_env_list_parsing_empty_items", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_get_value_from_env_list_parsing_no_commas", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_get_value_from_env_missing_value_list_default", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_get_value_from_env_missing_value_string_default", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_handle_asyncio_task_result_errors_no_exception", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_handle_asyncio_task_result_errors_with_exception", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_is_claude_environment_with_anthropic_user_id", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_is_claude_environment_with_both_set", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_is_claude_environment_with_claude_code", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_is_claude_environment_with_empty_values", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_is_claude_environment_with_none_set", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_save_to_env_add_new_key", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_save_to_env_error_handling", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_save_to_env_new_file", "tests/unit/test_etc_helper_functions.py::TestHelperFunctions::test_save_to_env_update_existing_key", "tests/unit/test_excel_analyzer_integration.py::TestExcelAnalyzerIntegration::test_excel_analyzer_supervisor_creation", "tests/unit/test_excel_analyzer_integration.py::TestExcelAnalyzerIntegration::test_excel_analyzer_tool_creation", "tests/unit/test_excel_analyzer_integration.py::TestExcelAnalyzerIntegration::test_excel_analyzer_tool_with_sample_data", "tests/unit/test_excel_analyzer_integration.py::TestExcelAnalyzerIntegration::test_oauth_verifier_integration", "tests/unit/test_excel_analyzer_integration.py::TestExcelAnalyzerIntegration::test_prompt_registration", "tests/unit/test_excel_analyzer_simple.py::TestExcelAnalyzerSimple::test_langchain_pandas_import", "tests/unit/test_excel_analyzer_simple.py::TestExcelAnalyzerSimple::test_pandas_functionality", "tests/unit/test_excel_analyzer_simple.py::TestExcelAnalyzerSimple::test_prompt_registration", "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreEnd2End::test_complete_pipeline_integration", "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_ensure_managers_initialized_index_failure", "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_ensure_managers_initialized_success", "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_handle_file_upload_complete_success", "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_handle_file_upload_manager_initialization_failure", "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_handle_file_upload_no_filename", "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_handle_file_upload_no_files_field", "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_process_files_to_vectorstore_conversion_exception", "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_process_files_to_vectorstore_files_not_processed", "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_process_files_to_vectorstore_success", "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_validate_upload_dependencies_embedding_service_failure", "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_validate_upload_dependencies_file_too_large", "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_validate_upload_dependencies_success", "tests/unit/test_file_upload_vectorstore_integration.py::TestFileUploadVectorStoreIntegration::test_validate_upload_dependencies_unsupported_file_type", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleIntegration::test_integration_with_supervisor_state", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_format_responses_fallback", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_general_exception_handling", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_invalid_action_error", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_restart_action_success", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_scheduler_exception_handling", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_start_action_success", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_status_action_success", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_stop_action_success", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_supervisor_task_state_integration", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_sync_run_raises_error", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_tool_instance_creation", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_tool_instantiation", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleTool::test_user_not_found_error", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleToolInput::test_default_duration_minutes", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleToolInput::test_duration_minutes_validation", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleToolInput::test_invalid_action_validation", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleToolInput::test_missing_user_guid", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleToolInput::test_valid_actions", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleToolInput::test_valid_input_creation", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleToolIntegration::test_tool_can_be_imported", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleToolIntegration::test_tool_follows_basetool_pattern", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleToolIntegration::test_tool_integration_with_email_checker", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleToolOutput::test_output_with_all_fields", "tests/unit/test_imap_idle_tool.py::TestIMAPIdleToolOutput::test_valid_output_creation", "tests/unit/test_imap_idle_tool.py::TestSupervisorTaskIMAPIdleActivate::test_class_attributes", "tests/unit/test_imap_idle_tool.py::TestSupervisorTaskIMAPIdleActivate::test_create_task_function", "tests/unit/test_imap_idle_tool.py::TestSupervisorTaskIMAPIdleActivate::test_start_30_minute_session", "tests/unit/test_imap_idle_tool.py::TestSupervisorTaskIMAPIdleActivate::test_valid_instantiation", "tests/unit/test_manager_agno.py::TestAgnoManager::test_CreateAgent_docker_environment", "tests/unit/test_manager_agno.py::TestAgnoManager::test_CreateAgent_local_environment", "tests/unit/test_manager_agno.py::TestAgnoManager::test_CreateAgent_not_initialized", "tests/unit/test_manager_agno.py::TestAgnoManager::test_CreateAgent_with_empty_prompt", "tests/unit/test_manager_agno.py::TestAgnoManager::test_CreateAgent_with_none_knowledge_base", "tests/unit/test_manager_agno.py::TestAgnoManager::test_RunAgent_with_message_response", "tests/unit/test_manager_agno.py::TestAgnoManager::test_RunAgent_with_run_response", "tests/unit/test_manager_agno.py::TestAgnoManager::test_RunAgent_with_string_response", "tests/unit/test_manager_agno.py::TestAgnoManager::test_class_attributes", "tests/unit/test_manager_agno.py::TestAgnoManager::test_get_instance_creates_instance", "tests/unit/test_manager_agno.py::TestAgnoManager::test_init_knowledge_base", "tests/unit/test_manager_agno.py::TestAgnoManager::test_init_knowledge_base_with_different_urls", "tests/unit/test_manager_agno.py::TestAgnoManager::test_init_vector_db_docker_environment", "tests/unit/test_manager_agno.py::TestAgnoManager::test_init_vector_db_local_environment", "tests/unit/test_manager_agno.py::TestAgnoManager::test_init_vector_db_with_custom_constants", "tests/unit/test_manager_agno.py::TestAgnoManager::test_initial_state", "tests/unit/test_manager_agno.py::TestAgnoManager::test_setup_already_initialized", "tests/unit/test_manager_agno.py::TestAgnoManager::test_setup_first_time", "tests/unit/test_manager_agno.py::TestAgnoManager::test_setup_with_commented_code", "tests/unit/test_manager_agno.py::TestAgnoManager::test_singleton_instance_persistence", "tests/unit/test_manager_agno.py::TestAgnoManager::test_singleton_pattern", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_breakpoint_prevention_in_tests", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_complete_workflow", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_create_logentries_table_exception", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_create_logentries_table_no_connection", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_create_logentries_table_success", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_debug_values_integration", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_get_caller_name_shallow_stack", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_get_caller_name_with_stack", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_initial_state", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_log_creates_queue_first_time", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_log_get_caller_name_integration", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_log_internal_database_error", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_log_internal_no_database_connection", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_log_internal_with_user", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_log_internal_without_user", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_log_queue_worker_processes_entries", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_log_with_custom_source", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_log_with_running_loop", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_log_worker_exception_handling", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_logfire_middleware", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_setup_already_initialized", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_setup_first_time", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_singleton_pattern", "tests/unit/test_manager_logfire.py::TestLogFireManager::test_type_checking_imports", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_ConvertFilesToVectorStore_file_error", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_ConvertFilesToVectorStore_file_metadata_creation", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_ConvertSQLToVectorStore", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_ConvertSQLToVectorStore_metadata_creation", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_ConvertSQLToVectorStore_record_processing", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_ConvertSQLToVectorStore_with_user", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_RunTap_docker_environment", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_RunTap_local_environment", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_RunUtility", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_class_attributes", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_directory_entries_filtering", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_empty_folder_processing", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_initial_state", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_setup_already_initialized", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_setup_first_time", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_singleton_pattern", "tests/unit/test_manager_meltano.py::TestMeltanoManager::test_uuid_generation_consistency", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_close_all_connections_empty", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_close_all_connections_with_errors", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_close_all_connections_with_mixed_connections", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_close_connection_no_existing_connection", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_close_connection_with_already_closed_direct", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_close_connection_with_already_closed_pool", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_close_connection_with_attribute_error", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_close_connection_with_direct_connection", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_close_connection_with_os_error", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_close_connection_with_pool", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_close_connection_with_runtime_error", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_commit_transaction_no_dbname", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_commit_transaction_with_pool", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_commit_transaction_without_pool", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_configuration_attributes", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_connect_to_database_direct_success", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_connect_to_database_existing_connection_different_mode", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_connect_to_database_existing_connection_same_mode", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_connect_to_database_exponential_backoff", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_connect_to_database_max_retries_exceeded", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_connect_to_database_pool_success", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_connect_to_database_retry_logic", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_connect_to_database_retry_with_connection_refused_error", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_connect_to_database_retry_with_gaier<PERSON>r", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_connect_to_database_retry_with_os_error", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_create_database_already_exists", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_create_database_error", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_create_database_success", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_delete_database_error", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_delete_database_not_exists", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_delete_database_success", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_delete_database_with_active_connections", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_environment_configuration", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_many_no_connection_found", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_many_no_pool_found", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_many_with_direct_connection", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_many_with_pool", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_non_query_no_connection_found", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_non_query_no_pool_found", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_non_query_with_direct_connection", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_non_query_with_pool", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_non_query_with_pool_and_params", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_non_query_with_pool_none_params", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_query_with_closed_pool_recreation", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_query_with_direct_connection_creation", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_query_with_direct_connection_creation_failure", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_query_with_direct_connection_success", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_query_with_pool_and_params", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_query_with_pool_creation_failure", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_query_with_pool_creation_when_none", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_query_with_pool_fallback_to_direct", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_query_with_pool_health_check_failure", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_query_with_pool_none_params", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_execute_query_with_pool_success", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_get_connection_method", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_get_instance_method", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_get_table_names_default_schema", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_get_table_names_exception_handling", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_get_table_names_no_connection_found", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_get_table_names_no_pool_found", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_get_table_names_with_custom_schema", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_get_table_names_with_direct_connection", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_get_table_names_with_pool", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_reconnect_with_custom_pool_sizes", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_reconnect_with_direct_mode", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_reconnect_with_pool_mode", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_register_exit_hooks_duplicate_prevention", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_register_exit_hooks_first_time", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_rollback_transaction_no_dbname", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_rollback_transaction_with_pool", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_rollback_transaction_without_pool", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_safe_get_connection_mode", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_safe_get_connection_with_closed_direct_connection", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_safe_get_connection_with_closed_pool", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_safe_get_connection_with_direct_connection", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_safe_get_connection_with_pool", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_safe_remove_connection_nonexistent", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_safe_remove_connection_with_direct_connection", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_safe_remove_connection_with_pool", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_safe_set_connection_mode", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_safe_set_connection_with_direct_connection", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_safe_set_connection_with_pool", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_setup_already_initialized", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_setup_first_time", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_signal_handler_registration", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_singleton_initialization", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_singleton_pattern", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_start_transaction_no_connection_found", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_start_transaction_no_pool_found", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_start_transaction_with_direct_connection", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_start_transaction_with_pool", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_sync_cleanup_with_attribute_error_during_loop_operations", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_sync_cleanup_with_exception", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_sync_cleanup_with_no_loop", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_sync_cleanup_with_os_error_during_loop_operations", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_sync_cleanup_with_running_loop", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_sync_cleanup_with_runtime_error_during_loop_creation", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_test_connection_failure", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_test_connection_with_direct_success", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_test_connection_with_pool_success", "tests/unit/test_manager_postgreSQL_comprehensive.py::TestPostgreSQLManagerComprehensive::test_using_pool_method", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_close_all_connections_comprehensive", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_close_connection_already_closed_direct", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_close_connection_already_closed_pool", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_close_connection_direct_success", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_close_connection_pool_success", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_close_connection_with_errors", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_commit_transaction_with_pool", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_commit_transaction_without_pool", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_connect_to_database_direct_success", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_connect_to_database_max_retries_exceeded", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_connect_to_database_pool_success", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_connect_to_database_retry_logic", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_create_database_already_exists", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_create_database_error", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_create_database_success", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_delete_database_active_connections", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_delete_database_not_exists", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_delete_database_success", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_execute_many_with_direct_connection", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_execute_many_with_pool", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_execute_non_query_no_connection_found", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_execute_non_query_no_pool_found", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_execute_non_query_with_direct_connection", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_execute_non_query_with_params", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_execute_non_query_with_pool", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_execute_query_create_connection_on_demand", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_execute_query_with_direct_connection_success", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_execute_query_with_params", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_execute_query_with_pool_success", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_exit_hooks_registration", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_get_table_names_exception_handling", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_get_table_names_no_connection_found", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_get_table_names_no_pool_found", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_get_table_names_with_custom_schema", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_get_table_names_with_direct_connection", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_get_table_names_with_pool", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_host_configuration", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_initial_state", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_port_configuration", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_reconnect_with_existing_mode", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_rollback_transaction_with_pool", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_rollback_transaction_without_pool", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_safe_connection_methods", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_safe_connection_with_closed_direct", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_safe_connection_with_closed_pool", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_setup_already_initialized", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_setup_first_time", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_singleton_pattern", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_start_transaction_no_connection_found", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_start_transaction_no_pool_found", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_start_transaction_with_direct_connection", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_start_transaction_with_pool", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_test_connection_failure", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_test_connection_with_direct_success", "tests/unit/test_manager_postgreSQL_improved.py::TestPostgreSQLManagerImproved::test_test_connection_with_pool_success", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_cleanup_scenarios", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_close_all_connections_with_close_errors", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_close_all_connections_with_pools_and_direct", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_close_connection_already_closed_direct", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_close_connection_already_closed_pool", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_close_connection_direct_success", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_close_connection_pool_success", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_close_connection_with_errors", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_commit_transaction_no_dbname", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_commit_transaction_with_pool", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_commit_transaction_without_pool", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_connect_to_database_direct_success", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_connect_to_database_existing_connection", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_connect_to_database_max_retries_exceeded", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_connect_to_database_pool_success", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_connect_to_database_retry_logic", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_connection_cleanup_states[already_closed-True]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_connection_cleanup_states[error_on_close-False]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_connection_cleanup_states[healthy-True]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_connection_error_scenarios[CannotConnectNowError-retry_logic]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_connection_error_scenarios[Exception-general_error_handling]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_connection_error_scenarios[gaierror-connection_failure]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_connection_management_modes[pool_mode-True]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_connection_management_modes[safe_connection_mode-False]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_create_database_already_exists", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_create_database_error", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_create_database_success", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_database_error_handling_integration", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_database_name_validation[invalid_db-False]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_database_name_validation[meltanodb-True]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_database_name_validation[vectordb-True]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_delete_database_active_connections", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_delete_database_error", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_delete_database_not_exists", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_delete_database_success", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_many_no_connection_found", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_many_no_pool_found", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_many_with_direct_connection", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_many_with_pool", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_non_query_no_connection_found", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_non_query_no_pool_found", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_non_query_with_direct_connection", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_non_query_with_params", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_non_query_with_pool", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_query_create_connection_on_demand", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_query_pool_fallback_to_direct", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_query_pool_health_check_failure", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_query_with_closed_pool", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_query_with_direct_connection_success", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_query_with_params", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_execute_query_with_pool_success", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_exit_hooks_registration", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_get_table_names_exception_handling", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_get_table_names_no_connection_found", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_get_table_names_no_pool_found", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_get_table_names_with_custom_schema", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_get_table_names_with_direct_connection", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_get_table_names_with_pool", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_host_configuration[None-localhost]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_host_configuration[host.docker.internal-host.docker.internal]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_host_configuration[localhost-localhost]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_initial_state", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_port_configuration[5432-5432]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_port_configuration[5433-5433]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_port_configuration[None-5432]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_reconnect_with_existing_mode", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_rollback_transaction_no_dbname", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_rollback_transaction_with_pool", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_rollback_transaction_without_pool", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_safe_connection_methods", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_safe_connection_with_closed_direct", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_safe_connection_with_closed_pool", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_scheduled_task_persistence_integration", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_setup_scenarios[False-True]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_setup_scenarios[True-False]", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_singleton_pattern", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_start_transaction_no_connection_found", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_start_transaction_no_pool_found", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_start_transaction_with_direct_connection", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_start_transaction_with_pool", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_test_connection_failure", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_test_connection_with_direct_success", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_test_connection_with_pool_success", "tests/unit/test_manager_postgreSQL_unified.py::TestPostgreSQLManagerUnified::test_vector_operations_integration", "tests/unit/test_manager_prompts_comprehensive.py::TestPromptManagerIntegration::test_prompt_manager_with_supervisors", "tests/unit/test_manager_prompts_comprehensive.py::TestPromptManagerPrompts::test_prompt_manager_supervisor_prompts", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_concurrent_access", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_critical_prompts_exist", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_database_connection_error", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_full_initialization_workflow", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_get_prompt_case_sensitivity", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_get_prompt_empty_string", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_get_prompt_identifier_and_key_empty_string", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_get_prompt_identifier_and_key_none", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_get_prompt_identifier_and_key_not_found", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_get_prompt_identifier_and_key_success", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_get_prompt_none", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_get_prompt_not_found", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_get_prompt_success", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_initial_state", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_loadDefaultPrompts_error_handling", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_loadDefaultPrompts_partial_loading", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_loadDefaultPrompts_success", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_meltano_manager_unavailable", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_prompt_content_validation", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_prompt_formatting_consistency", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_prompt_identifier_retrieval_performance", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_prompt_keys_format", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_prompt_retrieval_performance", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_prompt_update_and_reload_workflow", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_prompt_with_special_characters", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_prompts_dictionary_definition", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_setDefaultPrompts_MemoryOnly_error_handling", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_setDefaultPrompts_MemoryOnly_success", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_setDefaultPrompts_success", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_setup_already_initialized", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_setup_first_time", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_singleton_pattern", "tests/unit/test_manager_prompts_improved.py::TestPromptManagerImproved::test_token_values_definition", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_client_port_configuration", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_create_qdrant_async_client_initialization", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_create_qdrant_client_initialization", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_enhance_text_with_multimodal_context", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_get_async_client_cached", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_get_async_client_docker_environment", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_get_async_client_local_environment", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_get_client_cached", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_get_client_docker_environment", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_get_client_local_environment", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_get_document_assets", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_initialization_state", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_search_multimodal", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_search_multimodal_has_tables_filter", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_setup", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_singleton_pattern", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_upsert_method", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_upsert_multimodal", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_upsert_multiple", "tests/unit/test_manager_qdrant.py::TestQDrantManager::test_upsert_no_insert", "tests/unit/test_manager_supervisors_comprehensive.py::TestIntegration::test_chain_of_thought_with_regular_requests", "tests/unit/test_manager_supervisors_comprehensive.py::TestIntegration::test_concurrent_task_management", "tests/unit/test_manager_supervisors_comprehensive.py::TestIntegration::test_full_supervisor_task_flow", "tests/unit/test_manager_supervisors_comprehensive.py::TestIntegration::test_memory_management", "tests/unit/test_manager_supervisors_comprehensive.py::TestIntegration::test_model_validation_and_error_handling", "tests/unit/test_manager_supervisors_comprehensive.py::TestIntegration::test_task_transfer_between_supervisors", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_add_task_to_nonexistent_supervisor", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_add_task_to_supervisor", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_find_task", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_get_all_requests", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_get_supervisor", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_get_task", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_register_supervisor", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_register_task", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_setup", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_singleton_pattern", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_transfer_task", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_transfer_task_not_found", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorManager::test_transfer_task_supervisor_not_found", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSection::test_default_constructor", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSection::test_from_values_with_list_description", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSection::test_from_values_with_string_description", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSection::test_get_description_with_list", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSection::test_get_description_with_string", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSection::test_str_representation_with_list", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSection::test_str_representation_with_string", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_add_task_maintains_order", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_add_task_replaces_existing", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_add_task_with_priority", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_add_task_without_priority", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_call_supervisor", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_call_supervisor_with_state", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_compile_with_always_call_first_requests", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_compile_with_always_call_last_requests", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_compile_with_conditional_requests", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_compile_with_no_requests", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_find_task_by_guid", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_get_requests", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_get_requests_with_priorities", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_get_task_by_name", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_get_task_priority", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_get_tools", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_has_task", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_llm_call_router_with_always_call_last_requests", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_llm_call_router_with_end_decision", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_llm_call_router_with_requests", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_remove_task_not_found", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_remove_task_success", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_supervisor_initialization", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_supervisor_repr", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisor::test_update_task_priority", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_router_completed_task_validation", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_router_invalid_task_validation", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_router_reasoning_error_fallback", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_router_with_always_call_first", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_router_with_always_call_last", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_router_with_first_call", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_router_with_reasoning_enabled", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_supervisor_compile", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorSupervisorChainOfThought::test_cot_supervisor_initialization", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_base_task_initialization", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_base_task_repr", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_call_task_with_query", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_compile_default", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_get_prompt_with_id", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_get_prompt_with_prefix", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_get_prompt_without_id", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_get_tools_default", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_llm_call_not_implemented", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_llm_call_wrapper_command_result", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_llm_call_wrapper_none_result", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskBase::test_llm_call_wrapper_success", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskChainOfThought::test_cot_task_initialization", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskChainOfThought::test_get_cot_prompt_with_custom_suffix", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskChainOfThought::test_get_cot_prompt_with_default_suffix", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskChainOfThought::test_get_cot_prompt_with_prefix", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskChainOfThought::test_llm_call_no_content_attribute", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskChainOfThought::test_llm_call_with_reasoning_extraction", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskChainOfThought::test_llm_call_without_reasoning_content", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskCreateAgent::test_compile_default", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskCreateAgent::test_create_agent_get_tools", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskCreateAgent::test_create_agent_initialization", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskCreateAgent::test_llm_call_error_handling", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskCreateAgent::test_llm_call_invalid_response_format", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskCreateAgent::test_llm_call_success", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskCreateAgent::test_llm_call_with_conversation_history", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_compile_default_with_hitl", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_compile_default_without_hitl", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_get_tools", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_llm_call_with_hitl_no", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_llm_call_with_hitl_yes", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_llm_call_with_tools", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_llm_call_without_tools", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskSingleAgent::test_single_agent_initialization", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskState::test_state_creation_with_defaults", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskState::test_state_creation_with_values", "tests/unit/test_manager_supervisors_comprehensive.py::TestSupervisorTaskState::test_state_sections_dict", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_add_user_duplicate_guid", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_add_user_persistence_failure", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_add_user_success", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_concurrent_access", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_create_guid", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_find_user_not_found", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_find_user_success", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_get_user_by_username_not_found", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_get_user_by_username_success", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_get_user_multiple_users", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_lock_usage", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_manager_initialization", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_remove_user_not_found", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_remove_user_success", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_singleton_pattern", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_update_user_not_found", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManager::test_update_user_success", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_empty_manager_operations", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_ensure_models_rebuilt", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_ensure_models_rebuilt_error", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_find_users_by_rank", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_find_users_by_rank_empty", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_invalid_guid_formats", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_list_users", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_list_users_empty", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_update_user_invalid_attribute", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_user_exists_false", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerEdgeCases::test_user_exists_true", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerLogging::test_logger_initialization", "tests/unit/test_manager_users_comprehensive.py::TestZairaUserManagerLogging::test_logging_on_operations", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_cleanup_assets", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_encode_image_to_base64", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_encode_image_to_base64_nonexistent", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_extract_table_key_info", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_extract_table_key_info_invalid", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_get_asset_path", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_get_surrounding_context", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_get_surrounding_context_edge_cases", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_infer_column_types", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_is_date", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_is_numeric", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_save_image_asset", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_setup_idempotent", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_setup_initialization", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_singleton_instance_creation", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_singleton_pattern", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_structured_table_to_markdown", "tests/unit/test_multimodal_manager.py::TestMultimodalManager::test_structured_table_to_markdown_empty", "tests/unit/test_multimodal_manager.py::TestMultimodalManagerIntegration::test_extract_multimodal_elements_integration", "tests/unit/test_multivector_retriever.py::TestDocumentStoreManager::test_document_metadata_serialization", "tests/unit/test_multivector_retriever.py::TestDocumentStoreManager::test_document_store_manager_singleton", "tests/unit/test_multivector_retriever.py::TestDocumentStoreManager::test_document_store_setup", "tests/unit/test_multivector_retriever.py::TestIntegrationScenarios::test_document_workflow_structure", "tests/unit/test_multivector_retriever.py::TestIntegrationScenarios::test_error_handling_structure", "tests/unit/test_multivector_retriever.py::TestIntegrationScenarios::test_performance_considerations", "tests/unit/test_multivector_retriever.py::TestMultiVectorRetrieverLogic::test_batch_processing_logic", "tests/unit/test_multivector_retriever.py::TestMultiVectorRetrieverLogic::test_content_preparation_logic", "tests/unit/test_multivector_retriever.py::TestMultiVectorRetrieverLogic::test_content_type_filtering", "tests/unit/test_multivector_retriever.py::TestMultiVectorRetrieverLogic::test_content_type_validation", "tests/unit/test_multivector_retriever.py::TestMultiVectorRetrieverLogic::test_document_id_generation", "tests/unit/test_multivector_retriever.py::TestMultiVectorRetrieverLogic::test_metadata_structure", "tests/unit/test_multivector_retriever.py::TestMultiVectorRetrieverLogic::test_search_result_formatting", "tests/unit/test_mybot_generic.py::TestChannelType::test_channel_type_constants", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_mybot_generic_creation", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_mybot_generic_pydantic_config", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_member_join_discord", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_member_join_python_call", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_member_join_teams", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_member_join_whatsapp", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_message", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_on_ready", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_request_human_in_the_loop_discord", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_request_human_in_the_loop_halt_until_response", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_request_human_in_the_loop_python", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_request_human_in_the_loop_teams", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_request_human_in_the_loop_whatsapp", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_broadcast_discord", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_broadcast_other_platforms", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_broadcast_python", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_discord_long_message", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_discord_short_message", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_python", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_teams", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_whatsapp", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_whatsapp_long_message", "tests/unit/test_mybot_generic.py::TestMyBotGeneric::test_send_reply_without_chat_history", "tests/unit/test_mybot_generic.py::TestMyBotGenericEdgeCases::test_creation_with_kwargs", "tests/unit/test_mybot_generic.py::TestMyBotGenericEdgeCases::test_unknown_platform_handling", "tests/unit/test_mybot_generic.py::TestMyBotGenericTextSplitting::test_text_splitting_last_chunk_rebalancing", "tests/unit/test_mybot_generic.py::TestMyBotGenericTextSplitting::test_text_splitting_no_sentences", "tests/unit/test_mybot_generic.py::TestMyBotGenericTextSplitting::test_text_splitting_sentence_boundaries", "tests/unit/test_mybot_generic.py::TestMyBotGenericTextSplitting::test_text_splitting_short_text", "tests/unit/test_mybot_generic.py::TestReplyContext::test_reply_context_create_no_reply", "tests/unit/test_mybot_generic.py::TestReplyContext::test_reply_context_create_reply", "tests/unit/test_mybot_generic.py::TestReplyContext::test_reply_context_format_reply_context_for_ai", "tests/unit/test_mybot_generic.py::TestReplyContext::test_reply_context_format_reply_context_for_ai_no_author", "tests/unit/test_mybot_generic.py::TestReplyContext::test_reply_context_format_reply_context_for_ai_no_content", "tests/unit/test_mybot_generic.py::TestReplyContext::test_reply_context_get_parent_message_id", "tests/unit/test_mybot_generic.py::TestReplyContext::test_reply_context_validation", "tests/unit/test_output_sender_system_integration.py::TestOutputSenderSystemIntegration::test_output_sender_includes_system_task", "tests/unit/test_output_sender_system_integration.py::TestOutputSenderSystemIntegration::test_system_task_creation", "tests/unit/test_output_sender_system_integration.py::TestOutputSenderSystemIntegration::test_system_task_llm_call_executes_output_tasks", "tests/unit/test_prompt_references.py::TestPromptReferences::test_all_prompts_have_references", "tests/unit/test_prompt_references.py::TestPromptReferences::test_generate_prompt_usage_report", "tests/unit/test_prompt_references.py::TestPromptReferences::test_no_duplicate_prompt_references", "tests/unit/test_prompt_references.py::TestPromptReferences::test_prompt_content_quality", "tests/unit/test_prompt_references.py::TestPromptReferences::test_prompt_naming_consistency", "tests/unit/test_prompt_references.py::TestPromptReferences::test_prompt_reference_exactly_once", "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_creation_with_llm_parsing", "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_daily_schedule_tool", "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_extract_parsed_info_from_llm_result", "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_fallback_to_regex_parsing", "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_generic_schedule_tool", "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_llm_parsing_agent_creation", "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_monthly_schedule_tool", "tests/unit/test_schedule_parsing_llm.py::TestScheduleParsingLLM::test_recurring_schedule_tool", "tests/unit/test_scheduled_requests_unified.py::TestScheduledRequestsUnified::test_task_routing_logic[default_route-unknown_task-general_supervisor]", "tests/unit/test_scheduled_requests_unified.py::TestScheduledRequestsUnified::test_task_routing_logic[priority_route-urgent_task-priority_supervisor]", "tests/unit/test_scheduled_requests_unified.py::TestScheduledRequestsUnified::test_task_routing_logic[standard_route-email_task-email_supervisor]", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_cancel_task_db_unavailable", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_cancel_task_error_handling", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_cancel_task_success", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_cleanup_old_tasks_db_unavailable", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_cleanup_old_tasks_error_handling", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_cleanup_old_tasks_success", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_create_tables_connection_error", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_create_tables_index_creation_error", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_create_tables_missing_columns", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_create_tables_success", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_create_tables_table_not_exists", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_execute_single_operation_connection_error", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_execute_single_operation_no_pool", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_execute_single_operation_success", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_execute_single_operation_with_params", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_get_active_task_not_found", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_get_active_task_success", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_get_active_tasks_db_unavailable", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_get_active_tasks_empty", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_get_active_tasks_success", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_get_all_active_tasks", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_get_all_active_tasks_empty", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_get_instance_creates_new_instance", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_get_paused_tasks_count", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_get_paused_tasks_count_empty", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_get_paused_tasks_info", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_get_paused_tasks_info_empty", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_get_persistence_manager", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_get_persistence_manager_singleton", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_late_setup_error_handling", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_late_setup_success", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_load_task_db_unavailable", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_load_task_error_handling", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_load_task_not_found", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_load_task_success", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_persistence_manager_singleton_pattern", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_recover_tasks_db_unavailable", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_recover_tasks_error_handling", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_recover_tasks_no_tasks", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_recover_tasks_success", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_recreate_task_from_data_error_handling", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_recreate_task_from_data_success", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_recreate_task_from_data_user_not_found", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_resume_user_tasks_error_handling", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_resume_user_tasks_no_paused_tasks", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_resume_user_tasks_success", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_save_task_db_unavailable", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_save_task_error_handling", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_save_task_success", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_save_task_with_json_data", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_setup_already_initialized", "tests/unit/test_scheduled_tasks_improved.py::TestScheduledTasksImproved::test_setup_first_time_initialization", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_complete_task_lifecycle_integration", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_error_handling_integration", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_manager_setup_scenarios[False-True]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_manager_setup_scenarios[True-False]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_persistence_manager_singleton_pattern", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_persistence_scenarios[connection_error-database_response4-error_handled]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_persistence_scenarios[load_empty-None-no_data]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_persistence_scenarios[load_success-database_response2-data_returned]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_persistence_scenarios[save_failure-False-failure]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_persistence_scenarios[save_success-True-success]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_recurring_task_bug_fixes[duplicate_execution-task_config0-execution_lock]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_recurring_task_bug_fixes[missed_execution-task_config1-catch_up_logic]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_recurring_task_bug_fixes[timezone_drift-task_config2-timezone_normalization]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_recurring_task_scheduling[schedule_config0-2024-01-01 12:00:00-2024-01-01 12:05:00]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_recurring_task_scheduling[schedule_config1-2024-01-01 12:00:00-2024-01-01 13:05:00]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_recurring_task_scheduling[schedule_config2-2024-01-01 12:00:00-2024-01-02 12:00:00]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_restart_persistence_scenarios[clean_restart-task_states0-all_tasks_restored]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_restart_persistence_scenarios[crash_recovery-task_states1-partial_restoration]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_restart_persistence_scenarios[maintenance_restart-task_states2-paused_tasks_maintained]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_task_creation_variants[ONCE-0.0-one_time_task]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_task_creation_variants[RECURRING-300.0-recurring_task]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_task_creation_variants[RECURRING-86400.0-daily_task]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_task_routing_logic[default_route-unknown_task-general_supervisor]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_task_routing_logic[priority_route-urgent_task-priority_supervisor]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_task_routing_logic[standard_route-email_task-email_supervisor]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_task_state_operations[active-cancel-task_cancelled]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_task_state_operations[active-update-task_updated]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_task_state_operations[cancelled-reactivate-task_reactivated]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_task_state_operations[completed-cleanup-task_removed]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_user_login_persistence_scenarios[existing_user-regular_login-update_persistence]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_user_login_persistence_scenarios[new_user-first_login-create_persistence]", "tests/unit/test_scheduled_tasks_unified.py::TestScheduledTasksUnified::test_user_login_persistence_scenarios[returning_user-login_after_break-restore_tasks]", "tests/unit/test_signature_functionality.py::TestEmailProcessingDataSignature::test_email_preview_with_signature", "tests/unit/test_signature_functionality.py::TestEmailProcessingDataSignature::test_email_preview_without_signature", "tests/unit/test_signature_functionality.py::TestEmailProcessingDataSignature::test_email_processing_data_with_signature", "tests/unit/test_signature_functionality.py::TestSignatureIntegration::test_signature_manager_get_user_signature", "tests/unit/test_signature_functionality.py::TestSignatureIntegration::test_signature_manager_prepare_signature_for_email", "tests/unit/test_signature_functionality.py::TestSignatureManager::test_generate_html_signature_text_only", "tests/unit/test_signature_functionality.py::TestSignatureManager::test_get_default_signature_hg_sports", "tests/unit/test_signature_functionality.py::TestSignatureManager::test_image_to_base64_file_not_found", "tests/unit/test_signature_functionality.py::TestSignatureManager::test_image_to_base64_success", "tests/unit/test_signature_functionality.py::TestSignatureManager::test_signature_manager_initialization", "tests/unit/test_signature_functionality.py::TestSignatureManager::test_signature_manager_singleton", "tests/unit/test_signature_functionality.py::TestZairaUserSignatureFields::test_custom_signature_assignment", "tests/unit/test_signature_functionality.py::TestZairaUserSignatureFields::test_zaira_user_custom_signature", "tests/unit/test_signature_functionality.py::TestZairaUserSignatureFields::test_zaira_user_signature_fields", "tests/unit/test_signature_functionality.py::TestZairaUserSignatureFields::test_zaira_user_signature_fields_structure", "tests/unit/test_slack_endpoint.py::TestMySlackBot::test_get_instance_method", "tests/unit/test_slack_endpoint.py::TestMySlackBot::test_initialization", "tests/unit/test_slack_endpoint.py::TestMySlackBot::test_late_setup_debug_mode", "tests/unit/test_slack_endpoint.py::TestMySlackBot::test_late_setup_no_client", "tests/unit/test_slack_endpoint.py::TestMySlackBot::test_late_setup_with_client", "tests/unit/test_slack_endpoint.py::TestMySlackBot::test_multiple_instantiation", "tests/unit/test_slack_endpoint.py::TestMySlackBot::test_respond_method", "tests/unit/test_slack_endpoint.py::TestMySlackBot::test_setup_debug_mode", "tests/unit/test_slack_endpoint.py::TestMySlackBot::test_setup_no_token", "tests/unit/test_slack_endpoint.py::TestMySlackBot::test_setup_with_token", "tests/unit/test_slack_endpoint.py::TestMySlackBot::test_singleton_pattern", "tests/unit/test_slack_endpoint.py::TestMySlackBotEdgeCases::test_bot_generic_integration", "tests/unit/test_slack_endpoint.py::TestMySlackBotEdgeCases::test_client_lifecycle", "tests/unit/test_slack_endpoint.py::TestMySlackBotEdgeCases::test_concurrent_setup_calls", "tests/unit/test_slack_endpoint.py::TestMySlackBotEdgeCases::test_exception_handling_in_late_setup", "tests/unit/test_slack_endpoint.py::TestMySlackBotEdgeCases::test_exception_handling_in_setup", "tests/unit/test_slack_endpoint.py::TestMySlackBotEdgeCases::test_respond_with_different_event_types", "tests/unit/test_slack_endpoint.py::TestMySlackBotIntegration::test_class_attributes", "tests/unit/test_slack_endpoint.py::TestMySlackBotIntegration::test_cleanup_on_error", "tests/unit/test_slack_endpoint.py::TestMySlackBotIntegration::test_full_initialization_flow", "tests/unit/test_slack_endpoint.py::TestMySlackBotIntegration::test_token_validation_flow", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorCriticalEdgeCases::test_concurrent_task_execution_safety", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorCriticalEdgeCases::test_malformed_state_handling", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorCriticalEdgeCases::test_memory_leak_prevention", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorCriticalEdgeCases::test_state_mutation_isolation", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_always_call_first_routing", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_always_call_last_routing", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_basic_router_functionality", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_completed_tasks_exclusion", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_empty_requests_list", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_empty_tasks_list", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_error_handling_invalid_task", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_router_wrapper_functionality", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_router_wrapper_none_return", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor::test_router_wrapper_string_return", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_always_call_first_priority", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_always_call_first_sequence", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_debug_logging", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_disabled_reasoning", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_empty_reasoning_response", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_model_error_handling", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_reasoning_with_model_response", "tests/unit/test_supervisor_comprehensive.py::TestSupervisorSupervisor_ChainOfThought::test_cot_task_exclusion_logic", "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_change_chat_session_invalid_guid", "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_change_chat_session_not_found", "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_change_chat_session_success", "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_change_chat_session_user_not_found", "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_list_chat_sessions_empty", "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_list_chat_sessions_exception_handling", "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_list_chat_sessions_success", "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_list_chat_sessions_user_not_found", "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_new_chat_session_exception_handling", "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_new_chat_session_success", "tests/unit/test_task_chat_session.py::TestChatSessionTools::test_new_chat_session_user_not_found", "tests/unit/test_task_chat_session.py::TestCreateTaskFunction::test_create_task_manage_chat_sessions", "tests/unit/test_task_chat_session.py::TestSupervisorTaskChangeSession::test_class_creation", "tests/unit/test_task_chat_session.py::TestSupervisorTaskChangeSession::test_llm_call_delegates_to_parent", "tests/unit/test_task_chat_session.py::TestUUIDHandling::test_invalid_uuid_string", "tests/unit/test_task_chat_session.py::TestUUIDHandling::test_uuid_string_conversion", "tests/unit/test_tasks_comprehensive.py::TestAgendaPlanner::test_create_supervisor_agenda_planner", "tests/unit/test_tasks_comprehensive.py::TestRetrievalSupervisor::test_create_supervisor_retrieval", "tests/unit/test_tasks_comprehensive.py::TestRetrievalSupervisor::test_retrieval_supervisor_configuration", "tests/unit/test_tasks_comprehensive.py::TestTaskIntegration::test_supervisor_task_coordination", "tests/unit/test_tasks_comprehensive.py::TestTaskPerformance::test_supervisor_task_management_performance", "tests/unit/test_tasks_comprehensive.py::TestTopLevelSupervisor::test_create_top_level_supervisor_success", "tests/unit/test_tasks_comprehensive.py::TestTopLevelSupervisor::test_supervisor_compilation", "tests/unit/test_tasks_comprehensive.py::TestTopLevelSupervisor::test_supervisor_name_and_prompt", "tests/unit/test_tasks_comprehensive.py::TestTopLevelSupervisor::test_supervisor_task_registration", "tests/unit/test_tasks_comprehensive.py::TestTopLevelSupervisor::test_task_priorities", "tests/unit/test_tasks_comprehensive.py::TestTopOutputSupervisor::test_create_top_output_supervisor_success", "tests/unit/test_tasks_comprehensive.py::TestTopOutputSupervisor::test_output_supervisor_compilation", "tests/unit/test_tasks_comprehensive.py::TestTopOutputSupervisor::test_output_supervisor_registration", "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_initialization", "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_on_members_added_activity", "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_on_members_added_activity_multiple_members", "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_on_message_activity", "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_on_message_activity_new_user", "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_send_a_teams_message", "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_send_teams_broadcast_not_implemented", "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_setup_debug_mode", "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_setup_production_mode", "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_singleton_pattern", "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_teams_auth_endpoint", "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_teams_logout_endpoint", "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_teams_messages_endpoint", "tests/unit/test_teams_endpoint.py::TestMyTeamsBot::test_teams_messages_endpoint_exception", "tests/unit/test_teams_endpoint.py::TestMyTeamsBotEdgeCases::test_channel_type_detection", "tests/unit/test_teams_endpoint.py::TestMyTeamsBotEdgeCases::test_concurrent_message_processing", "tests/unit/test_teams_endpoint.py::TestMyTeamsBotEdgeCases::test_empty_message_handling", "tests/unit/test_teams_endpoint.py::TestMyTeamsBotEdgeCases::test_exception_handling_in_setup", "tests/unit/test_teams_endpoint.py::TestMyTeamsBotEdgeCases::test_missing_activity_properties", "tests/unit/test_teams_endpoint.py::TestMyTeamsBotEdgeCases::test_on_message_activity_exception_handling", "tests/unit/test_teams_endpoint.py::TestOnTurnError::test_on_turn_error_function", "tests/unit/test_teams_endpoint.py::TestOnTurnError::test_on_turn_error_send_activity_exception", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_message_payload_structure", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_on_message_new_user", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_on_message_short_text", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_on_ready", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_process_webhook_empty_data", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_process_webhook_multiple_messages", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_process_webhook_no_messages", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_process_webhook_valid_message", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_send_whatsapp_message_api_error", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_send_whatsapp_message_connection_error", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_send_whatsapp_message_empty", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_send_whatsapp_message_no_recipient", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_send_whatsapp_message_success", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_send_whatsapp_message_too_long", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_setup_oauth_configuration", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_singleton_pattern", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_client_creation", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_verify_invalid_token", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_verify_missing_params", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_verify_success", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_webhook_exception", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_webhook_invalid_content_type", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_webhook_invalid_json", "tests/unit/test_whatsapp_endpoint.py::TestWhatsAppEndpoint::test_whatsapp_webhook_valid_json", "tests/unit/test_zaira_message.py::TestZairaMessage::test_computed_fields", "tests/unit/test_zaira_message.py::TestZairaMessage::test_content_hash_generation", "tests/unit/test_zaira_message.py::TestZairaMessage::test_create_assistant_message", "tests/unit/test_zaira_message.py::TestZairaMessage::test_create_function_message", "tests/unit/test_zaira_message.py::TestZairaMessage::test_create_system_message", "tests/unit/test_zaira_message.py::TestZairaMessage::test_create_tool_message", "tests/unit/test_zaira_message.py::TestZairaMessage::test_create_user_message", "tests/unit/test_zaira_message.py::TestZairaMessage::test_direct_creation", "tests/unit/test_zaira_message.py::TestZairaMessage::test_empty_content_handling", "tests/unit/test_zaira_message.py::TestZairaMessage::test_message_role_validation", "tests/unit/test_zaira_message.py::TestZairaMessage::test_tags_and_categories_validation", "tests/unit/test_zaira_message.py::TestZairaMessage::test_to_anthropic_format", "tests/unit/test_zaira_message.py::TestZairaMessage::test_to_langchain_assistant_message", "tests/unit/test_zaira_message.py::TestZairaMessage::test_to_langchain_function_message", "tests/unit/test_zaira_message.py::TestZairaMessage::test_to_langchain_system_message", "tests/unit/test_zaira_message.py::TestZairaMessage::test_to_langchain_tool_message", "tests/unit/test_zaira_message.py::TestZairaMessage::test_to_langchain_unknown_role_fallback", "tests/unit/test_zaira_message.py::TestZairaMessage::test_to_langchain_user_message", "tests/unit/test_zaira_message.py::TestZairaMessage::test_to_minimal_dict", "tests/unit/test_zaira_message.py::TestZairaMessage::test_to_openai_format", "tests/unit/test_zaira_message.py::TestZairaMessage::test_to_openai_format_function_with_metadata", "tests/unit/test_zaira_message.py::TestZairaMessage::test_to_openai_format_tool_with_metadata", "tests/unit/test_zaira_message.py::TestZairaMessageEdgeCases::test_string_representations", "tests/unit/test_zaira_message.py::TestZairaMessageEdgeCases::test_to_dict_options", "tests/unit/test_zaira_message.py::TestZairaMessageEdgeCases::test_to_json", "tests/unit/test_zaira_message.py::TestZairaMessageEdgeCases::test_unicode_content", "tests/unit/test_zaira_message.py::TestZairaMessageEdgeCases::test_very_long_content", "tests/unit/test_zaira_message.py::TestZairaMessageFactoryMethods::test_from_langchain_ai_message", "tests/unit/test_zaira_message.py::TestZairaMessageFactoryMethods::test_from_langchain_human_message", "tests/unit/test_zaira_message.py::TestZairaMessageFactoryMethods::test_from_openai_format", "tests/unit/test_zaira_message.py::TestZairaMessageFactoryMethods::test_from_openai_format_with_function_name", "tests/unit/test_zaira_message.py::TestZairaMessageFactoryMethods::test_from_openai_format_with_tool_call_id", "tests/unit/test_zaira_message.py::TestZairaMessageUtilityMethods::test_add_categories", "tests/unit/test_zaira_message.py::TestZairaMessageUtilityMethods::test_add_tags", "tests/unit/test_zaira_message.py::TestZairaMessageUtilityMethods::test_calculate_cost", "tests/unit/test_zaira_message.py::TestZairaMessageUtilityMethods::test_mark_failed", "tests/unit/test_zaira_message.py::TestZairaMessageUtilityMethods::test_mark_processed", "tests/unit/test_zaira_message.py::TestZairaMessageUtilityMethods::test_role_check_methods", "tests/unit/test_zaira_message.py::TestZairaMessageUtilityMethods::test_update_metadata", "tests/unit/test_zaira_message.py::TestZairaMessageValidation::test_empty_content_validation_for_assistant_message", "tests/unit/test_zaira_message.py::TestZairaMessageValidation::test_empty_content_validation_for_user_message", "tests/unit/test_zaira_message.py::TestZairaMessageValidation::test_enum_field_validators_with_non_string_input", "tests/unit/test_zaira_message.py::TestZairaMessageValidation::test_invalid_role_validation", "tests/unit/test_zaira_message.py::TestZairaMessageValidation::test_priority_enum_passthrough", "tests/unit/test_zaira_message.py::TestZairaMessageValidation::test_priority_string_validation", "tests/unit/test_zaira_message.py::TestZairaMessageValidation::test_role_enum_passthrough", "tests/unit/test_zaira_message.py::TestZairaMessageValidation::test_status_enum_passthrough", "tests/unit/test_zaira_message.py::TestZairaMessageValidation::test_status_string_validation", "tests/unit/test_zaira_user_multimodal.py::TestZairaUserMultimodal::test_is_image_file_invalid_extensions", "tests/unit/test_zaira_user_multimodal.py::TestZairaUserMultimodal::test_is_image_file_valid_extensions", "tests/unit/test_zaira_user_multimodal.py::TestZairaUserMultimodal::test_is_multimodal_enabled_user_rank", "tests/unit/test_zaira_user_multimodal.py::TestZairaUserMultimodal::test_process_image_attachment_failure", "tests/unit/test_zaira_user_multimodal.py::TestZairaUserMultimodal::test_process_image_attachment_success"]