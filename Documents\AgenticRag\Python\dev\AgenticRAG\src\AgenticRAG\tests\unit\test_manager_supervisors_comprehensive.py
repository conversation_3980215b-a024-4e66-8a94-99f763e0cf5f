"""
Comprehensive unit tests for SupervisorManager and related classes
This test suite achieves 90%+ coverage by testing all critical paths and edge cases
"""

import sys
import os
import pytest
import asyncio
from unittest.mock import AsyncMock, Mock, patch, MagicMock
from uuid import UUID, uuid4
from typing import Dict, List, Any

# Add project root to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '../../'))

from imports import *
from managers.manager_supervisors import (
    SupervisorManager, 
    SupervisorSupervisor, 
    SupervisorTask_Base, 
    SupervisorTask_SingleAgent,
    SupervisorTask_Create_agent,
    SupervisorTask_ChainOfThought,
    SupervisorSupervisor_ChainOfThought,
    SupervisorSection, 
    SupervisorTaskState, 
    SupervisorTaskInput,
    SupervisorTaskOutput,
    SupervisorRouteState,
    SupervisorTaskConfig
)
from langchain_core.messages import HumanMessage, SystemMessage, ToolMessage
from langchain_core.tools import BaseTool
from langchain_core.language_models.base import BaseLanguageModel
from langgraph.types import Command
from pydantic import ValidationError

class TestSupervisorSection:
    """Test SupervisorSection model and methods"""
    
    def test_default_constructor(self):
        """Test default constructor with no arguments"""
        section = SupervisorSection.default()
        assert section.name is None
        assert section.description is None
        assert section.description_list == []
    
    def test_from_values_with_string_description(self):
        """Test creating section with string description"""
        section = SupervisorSection.from_values("test_section", "Test description")
        assert section.name == "test_section"
        assert section.description == "Test description"
        assert section.description_list == []
    
    def test_from_values_with_list_description(self):
        """Test creating section with list description"""
        desc_list = ["Item 1", "Item 2", "Item 3"]
        section = SupervisorSection.from_values("test_section", desc_list)
        assert section.name == "test_section"
        assert section.description == ""
        assert section.description_list == desc_list
    
    def test_get_description_with_string(self):
        """Test get_description returns string when description is set"""
        section = SupervisorSection(name="test", description="Test description")
        assert section.get_description() == "Test description"
    
    def test_get_description_with_list(self):
        """Test get_description returns list when description is empty"""
        section = SupervisorSection(name="test", description="", description_list=["Item 1", "Item 2"])
        assert section.get_description() == ["Item 1", "Item 2"]
    
    def test_str_representation_with_string(self):
        """Test string representation with string description"""
        section = SupervisorSection(name="test", description="Test description")
        assert str(section) == "Test description"
    
    def test_str_representation_with_list(self):
        """Test string representation with list description"""
        section = SupervisorSection(name="test", description="", description_list=["Item 1", "Item 2"])
        assert str(section) == "Item 1, Item 2"

class TestSupervisorTaskState:
    """Test SupervisorTaskState model and validation"""
    
    def test_state_creation_with_defaults(self):
        """Test creating state with default values"""
        state = SupervisorTaskState()
        assert state.original_input is None
        assert state.additional_input is None
        assert state.user_guid is None
        assert state.messages is None
        assert state.scheduled_guid is None
        assert state.sections == {}
        assert state.completed_sections == []
        assert state.completed_tasks == []
        assert state.call_trace == []
        assert state.reasoning_steps == []
        assert state.conversation_history == []
    
    def test_state_creation_with_values(self):
        """Test creating state with specific values"""
        test_guid = uuid4()
        test_messages = [HumanMessage(content="Test message")]
        
        state = SupervisorTaskState(
            original_input="Test input",
            user_guid="test-user-123",
            scheduled_guid=test_guid,
            messages=test_messages,
            call_trace=["step1", "step2"],
            reasoning_steps=["reason1", "reason2"]
        )
        
        assert state.original_input == "Test input"
        assert state.user_guid == "test-user-123"
        assert state.scheduled_guid == test_guid
        assert state.messages == test_messages
        assert state.call_trace == ["step1", "step2"]
        assert state.reasoning_steps == ["reason1", "reason2"]
    
    def test_state_sections_dict(self):
        """Test sections dictionary functionality"""
        state = SupervisorTaskState()
        section1 = SupervisorSection(name="section1", description="Description 1")
        section2 = SupervisorSection(name="section2", description="Description 2")
        
        state.sections["section1"] = section1
        state.sections["section2"] = section2
        
        assert len(state.sections) == 2
        assert state.sections["section1"].name == "section1"
        assert state.sections["section2"].name == "section2"

class TestSupervisorTaskBase:
    """Test SupervisorTask_Base abstract class"""
    
    @pytest.fixture
    def mock_model(self):
        """Mock language model for testing"""
        model = Mock(spec=BaseLanguageModel)
        model.ainvoke = AsyncMock(return_value=Mock(content="Test response"))
        return model
    
    @pytest.fixture
    def mock_prompt_manager(self):
        """Mock prompt manager"""
        with patch('managers.manager_supervisors.PromptManager') as mock_pm:
            mock_pm.get_prompt.return_value = "Test prompt"
            yield mock_pm
    
    def test_base_task_initialization(self, mock_model, mock_prompt_manager):
        """Test base task initialization"""
        task = SupervisorTask_Base(
            name="test_task",
            prompt="Test prompt",
            prompt_id="test_prompt_id",
            model=mock_model
        )
        
        assert task.name == "test_task"
        assert task._prompt == "Test prompt"
        assert task.prompt_id == "test_prompt_id"
        assert task.model == mock_model
        assert task.scheduled_guid is None
        assert task.always_call_FIRST == False
        assert task.always_call_LAST == False
        assert task.langgraph is not None
        assert task.thread_config is not None
        assert task.thread_config["thread_id"] is not None
    
    def test_base_task_repr(self, mock_model):
        """Test base task string representation"""
        test_guid = uuid4()
        task = SupervisorTask_Base(
            name="test_task",
            scheduled_guid=test_guid,
            model=mock_model
        )
        
        assert repr(task) == f"Task({test_guid}, test_task)"
    
    def test_get_tools_default(self, mock_model):
        """Test get_tools returns empty list for base class"""
        task = SupervisorTask_Base(name="test_task", model=mock_model)
        assert task.get_tools() == []
    
    def test_get_prompt_with_id(self, mock_model, mock_prompt_manager):
        """Test get_prompt with prompt_id"""
        task = SupervisorTask_Base(
            name="test_task",
            prompt_id="test_prompt_id",
            model=mock_model
        )
        
        result = task.get_prompt()
        mock_prompt_manager.get_prompt.assert_called_once_with("test_prompt_id")
        assert result == "Test prompt"
    
    def test_get_prompt_with_prefix(self, mock_model, mock_prompt_manager):
        """Test get_prompt with Zaira personality prefix"""
        mock_prompt_manager.get_prompt.side_effect = lambda x: {
            "test_prompt_id": "Test prompt",
            "AskZaira_Prompt": "Zaira personality: "
        }[x]
        
        task = SupervisorTask_Base(
            name="test_task",
            prompt_id="test_prompt_id",
            model=mock_model
        )
        
        result = task.get_prompt(prefix_zaira_personality=True)
        assert result == "Zaira personality: Test prompt"
    
    def test_get_prompt_without_id(self, mock_model, mock_prompt_manager):
        """Test get_prompt without prompt_id uses direct prompt"""
        task = SupervisorTask_Base(
            name="test_task",
            prompt="Direct prompt",
            model=mock_model
        )
        
        result = task.get_prompt()
        mock_prompt_manager.get_prompt.assert_not_called()
        assert result == "Direct prompt"
    
    @pytest.mark.asyncio
    async def test_llm_call_not_implemented(self, mock_model):
        """Test llm_call raises NotImplementedError for base class"""
        task = SupervisorTask_Base(name="test_task", model=mock_model)
        state = SupervisorTaskState()
        
        with pytest.raises(RuntimeError, match="NOT_IMPLEMENTED"):
            await task.llm_call(state)
    
    @pytest.mark.asyncio
    async def test_llm_call_wrapper_success(self, mock_model):
        """Test llm_call_wrapper with successful execution"""
        class TestTask(SupervisorTask_Base):
            async def llm_call(self, state):
                return "Test result"
        
        task = TestTask(name="test_task", model=mock_model)
        state = SupervisorTaskState(
            user_guid="test-user-123",
            messages=[HumanMessage(content="Test message")],
            call_trace=["initial"],
            completed_tasks=["task1"]
        )
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user = Mock()
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('managers.manager_supervisors.Globals') as mock_globals:
                mock_globals.is_debug_values.return_value = False
                
                result = await task.llm_call_wrapper(state)
                
                assert isinstance(result, Command)
                assert result.update["call_trace"] == ["initial", "test_task: llm_call"]
                assert result.update["messages"] == "Test result"
                assert result.update["completed_tasks"] == ["task1", "test_task"]
    
    @pytest.mark.asyncio
    async def test_llm_call_wrapper_none_result(self, mock_model):
        """Test llm_call_wrapper with None result"""
        class TestTask(SupervisorTask_Base):
            async def llm_call(self, state):
                return None
        
        task = TestTask(name="test_task", model=mock_model)
        state = SupervisorTaskState(
            call_trace=["initial"],
            completed_tasks=["task1"]
        )
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user = Mock()
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('managers.manager_supervisors.Globals') as mock_globals:
                mock_globals.is_debug_values.return_value = False
                
                result = await task.llm_call_wrapper(state)
                
                assert isinstance(result, Command)
                assert result.update["call_trace"] == ["initial", "test_task: llm_call"]
                assert "messages" not in result.update
                assert result.update["completed_tasks"] == ["task1", "test_task"]
    
    @pytest.mark.asyncio
    async def test_llm_call_wrapper_command_result(self, mock_model):
        """Test llm_call_wrapper with Command result"""
        class TestTask(SupervisorTask_Base):
            async def llm_call(self, state):
                return Command(update={"test": "value"})
        
        task = TestTask(name="test_task", model=mock_model)
        state = SupervisorTaskState(user_guid="test-user-123")
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user = Mock()
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('managers.manager_supervisors.Globals') as mock_globals:
                mock_globals.is_debug_values.return_value = False
                
                result = await task.llm_call_wrapper(state)
                
                assert isinstance(result, Command)
                assert result.update["test"] == "value"
    
    def test_compile_default(self, mock_model):
        """Test compile_default method"""
        task = SupervisorTask_Base(name="test_task", model=mock_model)
        compiled_task = task.compile_default()
        
        assert compiled_task == task
        assert task.compiled_langgraph is not None
    
    @pytest.mark.asyncio
    async def test_call_task_with_query(self, mock_model):
        """Test call_task_with_query method"""
        class TestTask(SupervisorTask_Base):
            async def llm_call(self, state):
                return Mock(content="Test response")
        
        task = TestTask(name="test_task", model=mock_model)
        task.compile_default()
        
        mock_user = Mock()
        mock_user.GUID = "test-user-123"
        
        with patch('managers.manager_supervisors.etc.helper_functions.get_any_message_as_type') as mock_msg_type:
            mock_msg_type.return_value = type(Mock(content="Test response"))
            
            result = await task.call_task_with_query("Test query", mock_user)
            
            assert result["result"] == "Test response"
            assert "call_trace" in result
            assert len(result["call_trace"]) > 0

class TestSupervisorTaskSingleAgent:
    """Test SupervisorTask_SingleAgent implementation"""
    
    @pytest.fixture
    def mock_model(self):
        """Mock language model for testing"""
        model = Mock(spec=BaseLanguageModel)
        model.ainvoke = AsyncMock(return_value=Mock(content="Test response", tool_calls=[]))
        model.bind_tools = Mock(return_value=model)
        return model
    
    @pytest.fixture
    def mock_tool(self):
        """Mock tool for testing"""
        tool = Mock(spec=BaseTool)
        tool.name = "test_tool"
        tool.description = "Test tool description"
        tool.ainvoke = AsyncMock(return_value="Tool result")
        return tool
    
    @pytest.fixture
    def mock_prompt_manager(self):
        """Mock prompt manager"""
        with patch('managers.manager_supervisors.PromptManager') as mock_pm:
            mock_pm.get_prompt.return_value = "Test prompt"
            yield mock_pm
    
    def test_single_agent_initialization(self, mock_model, mock_tool):
        """Test SingleAgent task initialization"""
        task = SupervisorTask_SingleAgent(
            name="test_task",
            prompt="Test prompt",
            tools=[mock_tool],
            enforce_HITL_before_tool_call=True,
            model=mock_model
        )
        
        assert task.name == "test_task"
        assert task._prompt == "Test prompt"
        assert task._tools == [mock_tool]
        assert task.enforce_HITL_before_tool_call == True
        assert task.model == mock_model
        assert task.callback_response == ""
    
    def test_get_tools(self, mock_model, mock_tool):
        """Test get_tools method"""
        task = SupervisorTask_SingleAgent(
            name="test_task",
            tools=[mock_tool],
            model=mock_model
        )
        
        assert task.get_tools() == [mock_tool]
    
    @pytest.mark.asyncio
    async def test_llm_call_without_tools(self, mock_model, mock_prompt_manager):
        """Test llm_call without tools"""
        task = SupervisorTask_SingleAgent(
            name="test_task",
            prompt="Test prompt",
            tools=[],
            model=mock_model
        )
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            original_input="Test input",
            call_trace=["initial"],
            completed_tasks=["task1"]
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('managers.manager_supervisors.datetime') as mock_datetime:
                mock_datetime.now.return_value = Mock()
                mock_datetime.timezone.utc = Mock()
                
                result = await task.llm_call(state)
                
                assert isinstance(result, Command)
                assert result.update["call_trace"] == ["initial", "test_task: llm_call"]
                assert result.update["completed_tasks"] == ["task1", "test_task"]
    
    @pytest.mark.asyncio
    async def test_llm_call_with_tools(self, mock_model, mock_tool, mock_prompt_manager):
        """Test llm_call with tools"""
        mock_model.tool_calls = []
        mock_response = Mock()
        mock_response.tool_calls = [
            {
                "name": "test_tool",
                "args": {"query": "test query"},
                "id": "tool_call_123"
            }
        ]
        mock_model.ainvoke.return_value = mock_response
        
        task = SupervisorTask_SingleAgent(
            name="test_task",
            prompt="Test prompt",
            tools=[mock_tool],
            model=mock_model
        )
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            original_input="Test input",
            call_trace=["initial"],
            completed_tasks=["task1"]
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('managers.manager_supervisors.datetime') as mock_datetime:
                mock_datetime.now.return_value = Mock()
                mock_datetime.timezone.utc = Mock()
                
                with patch('managers.manager_supervisors.LogFire') as mock_logfire:
                    result = await task.llm_call(state)
                    
                    assert isinstance(result, Command)
                    assert mock_tool.ainvoke.called
                    assert result.update["call_trace"] == ["initial", "test_task: tool test_tool"]
                    assert result.update["completed_tasks"] == ["task1", "test_task"]
    
    @pytest.mark.asyncio
    async def test_llm_call_with_hitl_yes(self, mock_model, mock_tool, mock_prompt_manager):
        """Test llm_call with HITL enforcement - user says yes"""
        mock_model.tool_calls = []
        mock_response = Mock()
        mock_response.tool_calls = [
            {
                "name": "test_tool",
                "args": {"query": "test query"},
                "id": "tool_call_123"
            }
        ]
        mock_model.ainvoke.return_value = mock_response
        
        task = SupervisorTask_SingleAgent(
            name="test_task",
            prompt="Test prompt",
            tools=[mock_tool],
            enforce_HITL_before_tool_call=True,
            model=mock_model
        )
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            original_input="Test input",
            call_trace=["initial"],
            completed_tasks=["task1"],
            scheduled_guid=uuid4()
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        mock_user.my_tasks = {state.scheduled_guid: Mock()}
        
        # Mock the callback to set response to "yes"
        async def mock_callback(task_obj, response):
            task.callback_response = "yes"
        
        mock_user.my_tasks[state.scheduled_guid].request_human_in_the_loop = AsyncMock(side_effect=mock_callback)
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('managers.manager_supervisors.datetime') as mock_datetime:
                mock_datetime.now.return_value = Mock()
                mock_datetime.timezone.utc = Mock()
                
                with patch('managers.manager_supervisors.LogFire') as mock_logfire:
                    result = await task.llm_call(state)
                    
                    assert isinstance(result, Command)
                    assert mock_tool.ainvoke.called
                    assert mock_user.my_tasks[state.scheduled_guid].request_human_in_the_loop.called
    
    @pytest.mark.asyncio
    async def test_llm_call_with_hitl_no(self, mock_model, mock_tool, mock_prompt_manager):
        """Test llm_call with HITL enforcement - user says no"""
        mock_model.tool_calls = []
        mock_response = Mock()
        mock_response.tool_calls = [
            {
                "name": "test_tool",
                "args": {"query": "test query"},
                "id": "tool_call_123"
            }
        ]
        mock_model.ainvoke.return_value = mock_response
        
        task = SupervisorTask_SingleAgent(
            name="test_task",
            prompt="Test prompt",
            tools=[mock_tool],
            enforce_HITL_before_tool_call=True,
            model=mock_model
        )
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            original_input="Test input",
            call_trace=["initial"],
            completed_tasks=["task1"],
            scheduled_guid=uuid4()
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        mock_user.my_tasks = {state.scheduled_guid: Mock()}
        
        # Mock the callback to set response to "no"
        async def mock_callback(task_obj, response):
            task.callback_response = "no"
        
        mock_user.my_tasks[state.scheduled_guid].request_human_in_the_loop = AsyncMock(side_effect=mock_callback)
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('managers.manager_supervisors.datetime') as mock_datetime:
                mock_datetime.now.return_value = Mock()
                mock_datetime.timezone.utc = Mock()
                
                with patch('managers.manager_supervisors.LogFire') as mock_logfire:
                    result = await task.llm_call(state)
                    
                    assert isinstance(result, Command)
                    assert not mock_tool.ainvoke.called
                    assert mock_user.my_tasks[state.scheduled_guid].request_human_in_the_loop.called
    
    def test_compile_default_without_hitl(self, mock_model, mock_tool):
        """Test compile_default without HITL"""
        task = SupervisorTask_SingleAgent(
            name="test_task",
            tools=[mock_tool],
            enforce_HITL_before_tool_call=False,
            model=mock_model
        )
        
        compiled_task = task.compile_default()
        
        assert compiled_task == task
        assert task.compiled_langgraph is not None
    
    def test_compile_default_with_hitl(self, mock_model, mock_tool):
        """Test compile_default with HITL"""
        task = SupervisorTask_SingleAgent(
            name="test_task",
            tools=[mock_tool],
            enforce_HITL_before_tool_call=True,
            model=mock_model
        )
        
        compiled_task = task.compile_default()
        
        assert compiled_task == task
        assert task.compiled_langgraph is not None

class TestSupervisorTaskChainOfThought:
    """Test SupervisorTask_ChainOfThought implementation"""
    
    @pytest.fixture
    def mock_model(self):
        """Mock language model for testing"""
        model = Mock(spec=BaseLanguageModel)
        model.ainvoke = AsyncMock(return_value=Mock(content="1. ANALYSIS: Test analysis\n2. CONTEXT: Test context\n3. APPROACH: Test approach\n4. ACTION: Test action\n5. VERIFICATION: Test verification"))
        return model
    
    @pytest.fixture
    def mock_prompt_manager(self):
        """Mock prompt manager"""
        with patch('managers.manager_supervisors.PromptManager') as mock_pm:
            mock_pm.get_prompt.return_value = "Test prompt"
            yield mock_pm
    
    def test_cot_task_initialization(self, mock_model):
        """Test CoT task initialization"""
        task = SupervisorTask_ChainOfThought(
            name="test_cot_task",
            prompt="Test prompt",
            prompt_id="test_prompt_id",
            cot_prompt_suffix="Custom CoT suffix",
            model=mock_model
        )
        
        assert task.name == "test_cot_task"
        assert task._prompt == "Test prompt"
        assert task.prompt_id == "test_prompt_id"
        assert task._cot_prompt_suffix == "Custom CoT suffix"
        assert task.enable_reasoning_trace == True
        assert task.model == mock_model
    
    def test_get_cot_prompt_with_custom_suffix(self, mock_model, mock_prompt_manager):
        """Test get_cot_prompt with custom suffix"""
        task = SupervisorTask_ChainOfThought(
            name="test_cot_task",
            prompt_id="test_prompt_id",
            cot_prompt_suffix="Custom CoT suffix",
            model=mock_model
        )
        
        result = task.get_cot_prompt()
        assert result == "Test prompt\n\nCustom CoT suffix"
    
    def test_get_cot_prompt_with_default_suffix(self, mock_model, mock_prompt_manager):
        """Test get_cot_prompt with default suffix"""
        task = SupervisorTask_ChainOfThought(
            name="test_cot_task",
            prompt_id="test_prompt_id",
            model=mock_model
        )
        
        result = task.get_cot_prompt()
        assert "step-by-step reasoning" in result
        assert "1. ANALYSIS" in result
        assert "2. CONTEXT" in result
        assert "3. APPROACH" in result
        assert "4. ACTION" in result
        assert "5. VERIFICATION" in result
    
    def test_get_cot_prompt_with_prefix(self, mock_model, mock_prompt_manager):
        """Test get_cot_prompt with Zaira personality prefix"""
        mock_prompt_manager.get_prompt.side_effect = lambda x: {
            "test_prompt_id": "Test prompt",
            "AskZaira_Prompt": "Zaira personality: "
        }[x]
        
        task = SupervisorTask_ChainOfThought(
            name="test_cot_task",
            prompt_id="test_prompt_id",
            cot_prompt_suffix="Custom CoT suffix",
            model=mock_model
        )
        
        result = task.get_cot_prompt(prefix_zaira_personality=True)
        assert result == "Zaira personality: Test prompt\n\nCustom CoT suffix"
    
    @pytest.mark.asyncio
    async def test_llm_call_with_reasoning_extraction(self, mock_model, mock_prompt_manager):
        """Test llm_call with reasoning step extraction"""
        task = SupervisorTask_ChainOfThought(
            name="test_cot_task",
            prompt_id="test_prompt_id",
            model=mock_model
        )
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            original_input="Test input",
            call_trace=["initial"],
            completed_tasks=["task1"]
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('builtins.print') as mock_print:
                result = await task.llm_call(state)
                
                assert isinstance(result, Command)
                assert result.update["call_trace"] == ["initial", "test_cot_task: llm_call_cot"]
                assert result.update["completed_tasks"] == ["task1", "test_cot_task"]
                assert len(result.update["reasoning_steps"]) > 0
                assert "test_cot_task: Test analysis" in result.update["reasoning_steps"]
                
                # Verify print statements were called for CoT display
                mock_print.assert_called()
    
    @pytest.mark.asyncio
    async def test_llm_call_without_reasoning_content(self, mock_model, mock_prompt_manager):
        """Test llm_call without reasoning content"""
        mock_model.ainvoke.return_value = Mock(content="Simple response without reasoning steps")
        
        task = SupervisorTask_ChainOfThought(
            name="test_cot_task",
            prompt_id="test_prompt_id",
            model=mock_model
        )
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            original_input="Test input",
            call_trace=["initial"],
            completed_tasks=["task1"]
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('builtins.print') as mock_print:
                result = await task.llm_call(state)
                
                assert isinstance(result, Command)
                assert result.update["call_trace"] == ["initial", "test_cot_task: llm_call_cot"]
                assert result.update["completed_tasks"] == ["task1", "test_cot_task"]
                assert result.update["reasoning_steps"] == []
    
    @pytest.mark.asyncio
    async def test_llm_call_no_content_attribute(self, mock_model, mock_prompt_manager):
        """Test llm_call when response has no content attribute"""
        mock_model.ainvoke.return_value = Mock(spec=[])  # No content attribute
        
        task = SupervisorTask_ChainOfThought(
            name="test_cot_task",
            prompt_id="test_prompt_id",
            model=mock_model
        )
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            original_input="Test input",
            call_trace=["initial"],
            completed_tasks=["task1"]
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            result = await task.llm_call(state)
            
            assert isinstance(result, Command)
            assert result.update["reasoning_steps"] == []

class TestSupervisorSupervisor:
    """Test SupervisorSupervisor implementation"""
    
    @pytest.fixture
    def mock_model(self):
        """Mock language model for testing"""
        model = Mock(spec=BaseLanguageModel)
        model.ainvoke = AsyncMock(return_value=Mock(content="Test response"))
        model.with_structured_output.return_value = model
        return model
    
    @pytest.fixture
    def mock_prompt_manager(self):
        """Mock prompt manager"""
        with patch('managers.manager_supervisors.PromptManager') as mock_pm:
            mock_pm.get_prompt.return_value = "Test prompt"
            yield mock_pm
    
    @pytest.fixture
    def mock_supervisor_manager(self):
        """Mock SupervisorManager"""
        with patch('managers.manager_supervisors.SupervisorManager') as mock_sm:
            mock_instance = Mock()
            mock_instance.default_model = Mock(spec=BaseLanguageModel)
            mock_sm.get_instance.return_value = mock_instance
            yield mock_sm
    
    def test_supervisor_initialization(self, mock_model, mock_supervisor_manager):
        """Test supervisor initialization"""
        supervisor = SupervisorSupervisor(
            name="test_supervisor",
            prompt="Test prompt",
            prompt_id="test_prompt_id",
            model=mock_model
        )
        
        assert supervisor.name == "test_supervisor"
        assert supervisor._prompt == "Test prompt"
        assert supervisor.prompt_id == "test_prompt_id"
        assert supervisor.model == mock_model
        assert supervisor._requests == []
        assert supervisor._task_map == {}
        assert supervisor._lock is not None
        assert supervisor.thread_config["recursion_limit"] == 2500
    
    def test_supervisor_repr(self, mock_model, mock_supervisor_manager):
        """Test supervisor string representation"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        # Add some tasks
        task1 = Mock()
        task1.scheduled_guid = uuid4()
        task2 = Mock()
        task2.scheduled_guid = uuid4()
        
        supervisor._requests = [(1, task1), (2, task2)]
        
        repr_str = repr(supervisor)
        assert "Supervisor(test_supervisor" in repr_str
        assert str(task1.scheduled_guid) in repr_str
        assert str(task2.scheduled_guid) in repr_str
    
    def test_add_task_with_priority(self, mock_model, mock_supervisor_manager):
        """Test adding task with specific priority"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        task = Mock()
        task.name = "test_task"
        task.scheduled_guid = uuid4()
        
        supervisor.add_task(task, priority=5)
        
        assert len(supervisor._requests) == 1
        assert supervisor._requests[0] == (5, task)
        assert supervisor._task_map["test_task"] == (5, task)
    
    def test_add_task_without_priority(self, mock_model, mock_supervisor_manager):
        """Test adding task without priority (uses default)"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        task = Mock()
        task.name = "test_task"
        task.scheduled_guid = uuid4()
        
        supervisor.add_task(task)
        
        assert len(supervisor._requests) == 1
        assert supervisor._requests[0][0] == 100  # Default priority
        assert supervisor._requests[0][1] == task
    
    def test_add_task_replaces_existing(self, mock_model, mock_supervisor_manager):
        """Test adding task replaces existing task with same name"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        task1 = Mock()
        task1.name = "test_task"
        task1.scheduled_guid = uuid4()
        
        task2 = Mock()
        task2.name = "test_task"
        task2.scheduled_guid = uuid4()
        
        supervisor.add_task(task1, priority=5)
        supervisor.add_task(task2, priority=10)
        
        assert len(supervisor._requests) == 1
        assert supervisor._requests[0] == (10, task2)
        assert supervisor._task_map["test_task"] == (10, task2)
    
    def test_add_task_maintains_order(self, mock_model, mock_supervisor_manager):
        """Test adding tasks maintains priority order"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        task1 = Mock()
        task1.name = "task1"
        task1.scheduled_guid = uuid4()
        
        task2 = Mock()
        task2.name = "task2"
        task2.scheduled_guid = uuid4()
        
        task3 = Mock()
        task3.name = "task3"
        task3.scheduled_guid = uuid4()
        
        supervisor.add_task(task2, priority=10)
        supervisor.add_task(task1, priority=5)
        supervisor.add_task(task3, priority=15)
        
        assert len(supervisor._requests) == 3
        assert supervisor._requests[0] == (5, task1)
        assert supervisor._requests[1] == (10, task2)
        assert supervisor._requests[2] == (15, task3)
    
    def test_remove_task_success(self, mock_model, mock_supervisor_manager):
        """Test removing task successfully"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        task = Mock()
        task.name = "test_task"
        task.scheduled_guid = uuid4()
        
        supervisor.add_task(task, priority=5)
        removed_task = supervisor.remove_task(task.scheduled_guid)
        
        assert removed_task == task
        assert len(supervisor._requests) == 0
        assert "test_task" not in supervisor._task_map
    
    def test_remove_task_not_found(self, mock_model, mock_supervisor_manager):
        """Test removing task that doesn't exist"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        non_existent_guid = uuid4()
        removed_task = supervisor.remove_task(non_existent_guid)
        
        assert removed_task is None
    
    def test_update_task_priority(self, mock_model, mock_supervisor_manager):
        """Test updating task priority"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        task = Mock()
        task.name = "test_task"
        task.scheduled_guid = uuid4()
        
        supervisor.add_task(task, priority=5)
        supervisor.update_task_priority(task.scheduled_guid, 10)
        
        assert len(supervisor._requests) == 1
        assert supervisor._requests[0] == (10, task)
        assert supervisor._task_map["test_task"] == (10, task)
    
    def test_get_requests(self, mock_model, mock_supervisor_manager):
        """Test getting all tasks"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        task1 = Mock()
        task1.name = "task1"
        task1.scheduled_guid = uuid4()
        
        task2 = Mock()
        task2.name = "task2"
        task2.scheduled_guid = uuid4()
        
        supervisor.add_task(task1, priority=5)
        supervisor.add_task(task2, priority=10)
        
        tasks = supervisor.get_requests()
        
        assert len(tasks) == 2
        assert task1 in tasks
        assert task2 in tasks
    
    def test_get_requests_with_priorities(self, mock_model, mock_supervisor_manager):
        """Test getting tasks with priorities"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        task1 = Mock()
        task1.name = "task1"
        task1.scheduled_guid = uuid4()
        
        task2 = Mock()
        task2.name = "task2"
        task2.scheduled_guid = uuid4()
        
        supervisor.add_task(task1, priority=5)
        supervisor.add_task(task2, priority=10)
        
        tasks_with_priorities = supervisor.get_requests_with_priorities()
        
        assert len(tasks_with_priorities) == 2
        assert (5, task1) in tasks_with_priorities
        assert (10, task2) in tasks_with_priorities
    
    def test_has_task(self, mock_model, mock_supervisor_manager):
        """Test checking if task exists"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        task = Mock()
        task.name = "test_task"
        task.scheduled_guid = uuid4()
        
        supervisor.add_task(task, priority=5)
        
        assert supervisor.has_task(task.scheduled_guid) == True
        assert supervisor.has_task(uuid4()) == False
    
    def test_get_task_by_name(self, mock_model, mock_supervisor_manager):
        """Test getting task by name"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        task = Mock()
        task.name = "test_task"
        task.scheduled_guid = uuid4()
        
        supervisor.add_task(task, priority=5)
        
        found_task = supervisor.get_task("test_task")
        assert found_task == task
        
        not_found_task = supervisor.get_task("nonexistent_task")
        assert not_found_task is None
    
    def test_find_task_by_guid(self, mock_model, mock_supervisor_manager):
        """Test finding task by scheduled_guid"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        task = Mock()
        task.name = "test_task"
        task.scheduled_guid = uuid4()
        
        supervisor.add_task(task, priority=5)
        
        found_task = supervisor.find_task(task.scheduled_guid)
        assert found_task == task
        
        not_found_task = supervisor.find_task(uuid4())
        assert not_found_task is None
    
    def test_get_task_priority(self, mock_model, mock_supervisor_manager):
        """Test getting task priority"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        task = Mock()
        task.name = "test_task"
        task.scheduled_guid = uuid4()
        
        supervisor.add_task(task, priority=5)
        
        priority = supervisor.get_task_priority(task.scheduled_guid)
        assert priority == 5
        
        not_found_priority = supervisor.get_task_priority(uuid4())
        assert not_found_priority is None
    
    def test_get_tools(self, mock_model, mock_supervisor_manager):
        """Test get_tools returns empty list for supervisor"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        assert supervisor.get_tools() == []
    
    @pytest.mark.asyncio
    async def test_llm_call_router_with_requests(self, mock_model, mock_supervisor_manager, mock_prompt_manager):
        """Test llm_call_router with available tasks"""
        mock_model.ainvoke.return_value = SupervisorRouteState(step="task1")
        
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        # Add some tasks
        task1 = Mock()
        task1.name = "task1"
        task1.always_call_FIRST = False
        task1.always_call_LAST = False
        task1.get_prompt.return_value = "Task 1 prompt"
        
        task2 = Mock()
        task2.name = "task2"
        task2.always_call_FIRST = False
        task2.always_call_LAST = False
        task2.get_prompt.return_value = "Task 2 prompt"
        
        supervisor.add_task(task1)
        supervisor.add_task(task2)
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            messages=[HumanMessage(content="Test message")],
            call_trace=["initial"],
            completed_tasks=[]
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            result = await supervisor.llm_call_router(state)
            
            assert isinstance(result, Command)
            assert result.goto == "task1"
            assert result.update["call_trace"] == ["initial", "test_supervisor: goto task1"]
            assert result.update["completed_tasks"] == ["test_supervisor"]
    
    @pytest.mark.asyncio
    async def test_llm_call_router_with_end_decision(self, mock_model, mock_supervisor_manager, mock_prompt_manager):
        """Test llm_call_router with END decision"""
        mock_model.ainvoke.return_value = SupervisorRouteState(step="END")
        
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            messages=[HumanMessage(content="Test message")],
            call_trace=["initial"],
            completed_tasks=[]
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('builtins.print') as mock_print:
                result = await supervisor.llm_call_router(state)
                
                assert isinstance(result, Command)
                assert result.goto == "END"
                assert result.update["call_trace"] == ["initial", "test_supervisor: goto END"]
                assert result.update["completed_tasks"] == ["test_supervisor"]
    
    @pytest.mark.asyncio
    async def test_llm_call_router_with_always_call_last_requests(self, mock_model, mock_supervisor_manager, mock_prompt_manager):
        """Test llm_call_router with always_call_LAST tasks"""
        mock_model.ainvoke.return_value = SupervisorRouteState(step="END")
        
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        # Add always_call_LAST task
        task = Mock()
        task.name = "always_last_task"
        task.always_call_FIRST = False
        task.always_call_LAST = True
        task.get_prompt.return_value = "Always last task prompt"
        
        supervisor.add_task(task)
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            messages=[HumanMessage(content="Test message")],
            call_trace=["initial"],
            completed_tasks=[]
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('builtins.print') as mock_print:
                result = await supervisor.llm_call_router(state)
                
                assert isinstance(result, Command)
                assert result.goto == "always_last_task"
                assert result.update["call_trace"] == ["initial", "test_supervisor: goto always_last_task (always_call)"]
                assert result.update["completed_tasks"] == ["test_supervisor"]
    
    def test_compile_with_conditional_requests(self, mock_model, mock_supervisor_manager):
        """Test compile method with conditional tasks"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        # Add conditional tasks
        task1 = Mock()
        task1.name = "task1"
        task1.always_call_FIRST = False
        task1.always_call_LAST = False
        task1.compiled_langgraph = None
        task1.compile_default.return_value = task1
        task1.llm_call_internal = AsyncMock()
        
        task2 = Mock()
        task2.name = "task2"
        task2.always_call_FIRST = False
        task2.always_call_LAST = False
        task2.compiled_langgraph = None
        task2.compile_default.return_value = task2
        task2.llm_call_internal = AsyncMock()
        
        supervisor.add_task(task1)
        supervisor.add_task(task2)
        
        compiled_supervisor = supervisor.compile()
        
        assert compiled_supervisor == supervisor
        assert supervisor.compiled_langgraph is not None
        assert task1.compile_default.called
        assert task2.compile_default.called
    
    def test_compile_with_always_call_first_requests(self, mock_model, mock_supervisor_manager):
        """Test compile method with always_call_FIRST tasks"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        # Add always_call_FIRST task
        task = Mock()
        task.name = "always_first_task"
        task.always_call_FIRST = True
        task.always_call_LAST = False
        task.compiled_langgraph = None
        task.compile_default.return_value = task
        task.llm_call_internal = AsyncMock()
        
        supervisor.add_task(task)
        
        compiled_supervisor = supervisor.compile()
        
        assert compiled_supervisor == supervisor
        assert supervisor.compiled_langgraph is not None
        assert task.compile_default.called
    
    def test_compile_with_always_call_last_requests(self, mock_model, mock_supervisor_manager):
        """Test compile method with always_call_LAST tasks"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        # Add always_call_LAST task
        task = Mock()
        task.name = "always_last_task"
        task.always_call_FIRST = False
        task.always_call_LAST = True
        task.compiled_langgraph = None
        task.compile_default.return_value = task
        task.llm_call_internal = AsyncMock()
        
        supervisor.add_task(task)
        
        compiled_supervisor = supervisor.compile()
        
        assert compiled_supervisor == supervisor
        assert supervisor.compiled_langgraph is not None
        assert task.compile_default.called
    
    def test_compile_with_no_requests(self, mock_model, mock_supervisor_manager):
        """Test compile method with no tasks"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        
        compiled_supervisor = supervisor.compile()
        
        assert compiled_supervisor == supervisor
        assert supervisor.compiled_langgraph is not None
    
    @pytest.mark.asyncio
    async def test_call_supervisor(self, mock_model, mock_supervisor_manager):
        """Test call_supervisor method"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        supervisor.llm_call_internal = AsyncMock(return_value=Command(update={"messages": [Mock(content="Test result")]}))
        
        mock_user = Mock()
        mock_user.GUID = "test-user-123"
        test_guid = uuid4()
        
        with patch('managers.manager_supervisors.etc.helper_functions.get_any_message_as_type') as mock_msg_type:
            mock_msg_type.return_value = type(Mock(content="Test result"))
            
            result = await supervisor.call_supervisor("Test query", mock_user, test_guid)
            
            assert result["result"] == "Test result"
            assert "call_trace" in result
    
    @pytest.mark.asyncio
    async def test_call_supervisor_with_state(self, mock_model, mock_supervisor_manager):
        """Test call_supervisor_with_state method"""
        supervisor = SupervisorSupervisor(name="test_supervisor", model=mock_model)
        supervisor.llm_call_internal = AsyncMock(return_value=Command(update={"messages": [Mock(content="Test result")]}))
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            original_input="Test input"
        )
        
        with patch('managers.manager_supervisors.etc.helper_functions.get_any_message_as_type') as mock_msg_type:
            mock_msg_type.return_value = type(Mock(content="Test result"))
            
            result = await supervisor.call_supervisor_with_state(state)
            
            assert result["result"] == "Test result"
            assert "call_trace" in result

class TestSupervisorManager:
    """Test SupervisorManager singleton and methods"""
    
    def test_singleton_pattern(self):
        """Test SupervisorManager singleton pattern"""
        manager1 = SupervisorManager.get_instance()
        manager2 = SupervisorManager.get_instance()
        
        assert manager1 is manager2
        assert isinstance(manager1, SupervisorManager)
    
    @pytest.mark.asyncio
    async def test_setup(self):
        """Test SupervisorManager setup"""
        with patch('managers.manager_supervisors.ZairaSettings') as mock_settings:
            mock_settings.llm = Mock(spec=BaseLanguageModel)
            
            await SupervisorManager.setup()
            
            manager = SupervisorManager.get_instance()
            assert manager._initialized == True
            assert manager.default_model == mock_settings.llm
            assert manager._supervisors == {}
            assert manager._all_requests == {}
            assert manager._lock is not None
    
    def test_register_task(self):
        """Test registering a task"""
        manager = SupervisorManager.get_instance()
        
        task = Mock()
        task.name = "test_task"
        task.scheduled_guid = uuid4()
        
        registered_task = SupervisorManager.register_task(task)
        
        assert registered_task == task
        assert manager._all_requests["test_task"] == task
    
    def test_register_supervisor(self):
        """Test registering a supervisor"""
        manager = SupervisorManager.get_instance()
        
        supervisor = Mock()
        supervisor.name = "test_supervisor"
        
        registered_supervisor = SupervisorManager.register_supervisor(supervisor)
        
        assert registered_supervisor == supervisor
        assert manager._supervisors["test_supervisor"] == supervisor
    
    def test_get_supervisor(self):
        """Test getting supervisor by name"""
        manager = SupervisorManager.get_instance()
        
        supervisor = Mock()
        supervisor.name = "test_supervisor"
        
        SupervisorManager.register_supervisor(supervisor)
        
        found_supervisor = SupervisorManager.get_supervisor("test_supervisor")
        assert found_supervisor == supervisor
        
        not_found_supervisor = SupervisorManager.get_supervisor("nonexistent_supervisor")
        assert not_found_supervisor is None
    
    def test_add_task_to_supervisor(self):
        """Test adding task to supervisor"""
        task = Mock()
        task.name = "test_task"
        task.scheduled_guid = uuid4()
        
        supervisor = Mock()
        supervisor.name = "test_supervisor"
        supervisor.add_task = Mock()
        
        SupervisorManager.register_supervisor(supervisor)
        
        SupervisorManager.add_task_to_supervisor(task, "test_supervisor", 5)
        
        supervisor.add_task.assert_called_once_with(task, 5)
    
    def test_add_task_to_nonexistent_supervisor(self):
        """Test adding task to nonexistent supervisor raises error"""
        task = Mock()
        task.name = "test_task"
        task.scheduled_guid = uuid4()
        
        with pytest.raises(ValueError, match="Supervisor 'nonexistent_supervisor' not found"):
            SupervisorManager.add_task_to_supervisor(task, "nonexistent_supervisor", 5)
    
    def test_transfer_task(self):
        """Test transferring task between supervisors"""
        test_guid = uuid4()
        
        task = Mock()
        task.scheduled_guid = test_guid
        
        src_supervisor = Mock()
        src_supervisor.name = "src_supervisor"
        src_supervisor.remove_task.return_value = task
        
        dst_supervisor = Mock()
        dst_supervisor.name = "dst_supervisor"
        dst_supervisor.add_task = Mock()
        
        SupervisorManager.register_supervisor(src_supervisor)
        SupervisorManager.register_supervisor(dst_supervisor)
        
        SupervisorManager.transfer_task(test_guid, "src_supervisor", "dst_supervisor", 10)
        
        src_supervisor.remove_task.assert_called_once_with(test_guid)
        dst_supervisor.add_task.assert_called_once_with(task, 10)
    
    def test_transfer_task_not_found(self):
        """Test transferring nonexistent task raises error"""
        test_guid = uuid4()
        
        src_supervisor = Mock()
        src_supervisor.name = "src_supervisor"
        src_supervisor.remove_task.return_value = None
        
        dst_supervisor = Mock()
        dst_supervisor.name = "dst_supervisor"
        
        SupervisorManager.register_supervisor(src_supervisor)
        SupervisorManager.register_supervisor(dst_supervisor)
        
        with pytest.raises(ValueError, match=f"Task {test_guid} not found in src_supervisor"):
            SupervisorManager.transfer_task(test_guid, "src_supervisor", "dst_supervisor", 10)
    
    def test_transfer_task_supervisor_not_found(self):
        """Test transferring task with nonexistent supervisor raises error"""
        test_guid = uuid4()
        
        with pytest.raises(ValueError, match="One or both supervisors not found"):
            SupervisorManager.transfer_task(test_guid, "nonexistent_src", "nonexistent_dst")
    
    def test_find_task(self):
        """Test finding task by scheduled_guid"""
        test_guid = uuid4()
        
        task = Mock()
        task.name = "test_task"
        task.scheduled_guid = test_guid
        
        SupervisorManager.register_task(task)
        
        found_task = SupervisorManager.find_task(test_guid)
        assert found_task == task
        
        not_found_task = SupervisorManager.find_task(uuid4())
        assert not_found_task is None
    
    def test_get_task(self):
        """Test getting task by name"""
        task = Mock()
        task.name = "test_task"
        task.scheduled_guid = uuid4()
        
        SupervisorManager.register_task(task)
        
        found_task = SupervisorManager.get_task("test_task")
        assert found_task == task
        
        not_found_task = SupervisorManager.get_task("nonexistent_task")
        assert not_found_task is None
    
    def test_get_all_requests(self):
        """Test getting all tasks"""
        task1 = Mock()
        task1.name = "task1"
        task1.scheduled_guid = uuid4()
        
        task2 = Mock()
        task2.name = "task2"
        task2.scheduled_guid = uuid4()
        
        SupervisorManager.register_task(task1)
        SupervisorManager.register_task(task2)
        
        all_requests = SupervisorManager.get_all_requests()
        
        assert len(all_requests) >= 2
        assert task1 in all_requests
        assert task2 in all_requests

class TestSupervisorTaskCreateAgent:
    """Test SupervisorTask_Create_agent implementation"""
    
    @pytest.fixture
    def mock_model(self):
        """Mock language model for testing"""
        model = Mock(spec=BaseLanguageModel)
        model.ainvoke = AsyncMock(return_value=Mock(content="Test response"))
        return model
    
    @pytest.fixture
    def mock_tool(self):
        """Mock tool for testing"""
        tool = Mock(spec=BaseTool)
        tool.name = "test_tool"
        tool.description = "Test tool description"
        tool.ainvoke = AsyncMock(return_value="Tool result")
        return tool
    
    @pytest.fixture
    def mock_prompt_manager(self):
        """Mock prompt manager"""
        with patch('managers.manager_supervisors.PromptManager') as mock_pm:
            mock_pm.get_prompt.return_value = "Test prompt"
            yield mock_pm
    
    def test_create_agent_initialization(self, mock_model, mock_tool):
        """Test Create_agent task initialization"""
        task = SupervisorTask_Create_agent(
            name="test_create_agent",
            prompt="Test prompt",
            prompt_id="test_prompt_id",
            tools=[mock_tool],
            enforce_HITL_before_tool_call=True,
            model=mock_model
        )
        
        assert task.name == "test_create_agent"
        assert task._prompt == "Test prompt"
        assert task.prompt_id == "test_prompt_id"
        assert task._tools == [mock_tool]
        assert task.enforce_HITL_before_tool_call == True
        assert task.model == mock_model
    
    def test_create_agent_get_tools(self, mock_model, mock_tool):
        """Test get_tools method"""
        task = SupervisorTask_Create_agent(
            name="test_create_agent",
            tools=[mock_tool],
            model=mock_model
        )
        
        assert task.get_tools() == [mock_tool]
    
    @pytest.mark.asyncio
    async def test_llm_call_success(self, mock_model, mock_tool, mock_prompt_manager):
        """Test llm_call successful execution"""
        with patch('managers.manager_supervisors.create_react_agent') as mock_create_agent:
            mock_agent = Mock()
            mock_agent.ainvoke = AsyncMock(return_value={
                "messages": [Mock(content="Agent response")]
            })
            mock_create_agent.return_value = mock_agent
            
            task = SupervisorTask_Create_agent(
                name="test_create_agent",
                prompt_id="test_prompt_id",
                tools=[mock_tool],
                model=mock_model
            )
            
            state = SupervisorTaskState(
                user_guid="test-user-123",
                original_input="Test input",
                call_trace=["initial"],
                completed_tasks=["task1"]
            )
            
            mock_user = Mock()
            mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
            mock_user.username = "test_user"
            
            with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
                mock_user_mgr.find_user.return_value = mock_user
                
                result = await task.llm_call(state)
                
                assert isinstance(result, Command)
                assert result.update["call_trace"] == ["initial", "test_create_agent: create_react_agent"]
                assert result.update["completed_tasks"] == ["task1", "test_create_agent"]
                mock_create_agent.assert_called_once_with(mock_model, [mock_tool])
    
    @pytest.mark.asyncio
    async def test_llm_call_with_conversation_history(self, mock_model, mock_tool, mock_prompt_manager):
        """Test llm_call with conversation history"""
        with patch('managers.manager_supervisors.create_react_agent') as mock_create_agent:
            mock_agent = Mock()
            mock_agent.ainvoke = AsyncMock(return_value={
                "messages": [Mock(content="Agent response")]
            })
            mock_create_agent.return_value = mock_agent
            
            task = SupervisorTask_Create_agent(
                name="test_create_agent",
                prompt_id="test_prompt_id",
                tools=[mock_tool],
                model=mock_model
            )
            
            state = SupervisorTaskState(
                user_guid="test-user-123",
                original_input="Test input",
                call_trace=["initial"],
                completed_tasks=["task1"],
                conversation_history=[HumanMessage(content="Previous message")]
            )
            
            mock_user = Mock()
            mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
            mock_user.username = "test_user"
            
            with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
                mock_user_mgr.find_user.return_value = mock_user
                
                result = await task.llm_call(state)
                
                assert isinstance(result, Command)
                mock_agent.ainvoke.assert_called_once()
                # Check that conversation history was included
                call_args = mock_agent.ainvoke.call_args[0][0]
                assert "messages" in call_args
                # Should include chat history + system message + conversation history
                assert len(call_args["messages"]) >= 3
    
    @pytest.mark.asyncio
    async def test_llm_call_error_handling(self, mock_model, mock_tool, mock_prompt_manager):
        """Test llm_call error handling"""
        with patch('managers.manager_supervisors.create_react_agent') as mock_create_agent:
            mock_create_agent.side_effect = Exception("Test error")
            
            task = SupervisorTask_Create_agent(
                name="test_create_agent",
                prompt_id="test_prompt_id",
                tools=[mock_tool],
                model=mock_model
            )
            
            state = SupervisorTaskState(
                user_guid="test-user-123",
                original_input="Test input",
                call_trace=["initial"],
                completed_tasks=["task1"]
            )
            
            mock_user = Mock()
            mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
            mock_user.username = "test_user"
            
            with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
                mock_user_mgr.find_user.return_value = mock_user
                
                with patch('managers.manager_supervisors.LogFire') as mock_logfire:
                    result = await task.llm_call(state)
                    
                    assert isinstance(result, Command)
                    assert result.update["call_trace"] == ["initial", "test_create_agent: error"]
                    assert result.update["completed_tasks"] == ["task1", "test_create_agent"]
                    assert "Error processing request" in result.update["messages"]
                    mock_logfire.log.assert_called_once()
    
    @pytest.mark.asyncio
    async def test_llm_call_invalid_response_format(self, mock_model, mock_tool, mock_prompt_manager):
        """Test llm_call with invalid response format"""
        with patch('managers.manager_supervisors.create_react_agent') as mock_create_agent:
            mock_agent = Mock()
            mock_agent.ainvoke = AsyncMock(return_value={"invalid": "response"})
            mock_create_agent.return_value = mock_agent
            
            task = SupervisorTask_Create_agent(
                name="test_create_agent",
                prompt_id="test_prompt_id",
                tools=[mock_tool],
                model=mock_model
            )
            
            state = SupervisorTaskState(
                user_guid="test-user-123",
                original_input="Test input",
                call_trace=["initial"],
                completed_tasks=["task1"]
            )
            
            mock_user = Mock()
            mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
            mock_user.username = "test_user"
            
            with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
                mock_user_mgr.find_user.return_value = mock_user
                
                result = await task.llm_call(state)
                
                assert isinstance(result, Command)
                assert result.update["call_trace"] == ["initial", "test_create_agent: create_react_agent"]
                assert result.update["completed_tasks"] == ["task1", "test_create_agent"]
                # Should fallback to string representation
                assert "invalid" in result.update["messages"]
    
    def test_compile_default(self, mock_model, mock_tool):
        """Test compile_default method"""
        task = SupervisorTask_Create_agent(
            name="test_create_agent",
            tools=[mock_tool],
            model=mock_model
        )
        
        compiled_task = task.compile_default()
        
        assert compiled_task == task
        assert task.compiled_langgraph is not None

class TestSupervisorSupervisorChainOfThought:
    """Test SupervisorSupervisor_ChainOfThought implementation"""
    
    @pytest.fixture
    def mock_model(self):
        """Mock language model for testing"""
        model = Mock(spec=BaseLanguageModel)
        model.ainvoke = AsyncMock(return_value=Mock(content="1. ANALYSIS: Test analysis\n2. CONTEXT: Test context\n3. APPROACH: Test approach"))
        model.with_structured_output.return_value = model
        return model
    
    @pytest.fixture
    def mock_prompt_manager(self):
        """Mock prompt manager"""
        with patch('managers.manager_supervisors.PromptManager') as mock_pm:
            mock_pm.get_prompt.return_value = "Test prompt"
            yield mock_pm
    
    @pytest.fixture
    def mock_supervisor_manager(self):
        """Mock SupervisorManager"""
        with patch('managers.manager_supervisors.SupervisorManager') as mock_sm:
            mock_instance = Mock()
            mock_instance.default_model = Mock(spec=BaseLanguageModel)
            mock_sm.get_instance.return_value = mock_instance
            yield mock_sm
    
    def test_cot_supervisor_initialization(self, mock_model, mock_supervisor_manager):
        """Test CoT supervisor initialization"""
        supervisor = SupervisorSupervisor_ChainOfThought(
            name="test_cot_supervisor",
            prompt="Test prompt",
            prompt_id="test_prompt_id",
            model=mock_model
        )
        
        assert supervisor.name == "test_cot_supervisor"
        assert supervisor._prompt == "Test prompt"
        assert supervisor.prompt_id == "test_prompt_id"
        assert supervisor.enable_cot_routing == True
        assert supervisor.model == mock_model
    
    def test_cot_supervisor_compile(self, mock_model, mock_supervisor_manager):
        """Test CoT supervisor compile method"""
        supervisor = SupervisorSupervisor_ChainOfThought(
            name="test_cot_supervisor",
            model=mock_model
        )
        
        # Add a task
        task = Mock()
        task.name = "test_task"
        task.always_call_FIRST = False
        task.always_call_LAST = False
        task.compiled_langgraph = None
        task.compile_default.return_value = task
        task.llm_call_internal = AsyncMock()
        
        supervisor.add_task(task)
        
        compiled_supervisor = supervisor.compile()
        
        assert compiled_supervisor == supervisor
        assert supervisor.compiled_langgraph is not None
        assert task.compile_default.called
    
    @pytest.mark.asyncio
    async def test_cot_router_with_first_call(self, mock_model, mock_supervisor_manager, mock_prompt_manager):
        """Test CoT router with first call (no completed tasks)"""
        mock_model.ainvoke.return_value = SupervisorRouteState(step="task1")
        
        supervisor = SupervisorSupervisor_ChainOfThought(
            name="test_cot_supervisor",
            model=mock_model
        )
        
        # Add conditional task
        task = Mock()
        task.name = "task1"
        task.always_call_FIRST = False
        task.always_call_LAST = False
        task.get_prompt.return_value = "Task 1 prompt"
        task.get_tools.return_value = []
        
        supervisor.add_task(task)
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            messages=[HumanMessage(content="Test message")],
            call_trace=["initial"],
            completed_tasks=[]
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('builtins.print') as mock_print:
                result = await supervisor.llm_call_router(state)
                
                assert isinstance(result, Command)
                assert result.goto == "task1"
                assert result.update["call_trace"] == ["initial", "test_cot_supervisor: goto task1"]
                assert result.update["completed_tasks"] == ["test_cot_supervisor"]
                assert len(result.update["reasoning_steps"]) > 0
    
    @pytest.mark.asyncio
    async def test_cot_router_with_always_call_first(self, mock_model, mock_supervisor_manager, mock_prompt_manager):
        """Test CoT router with always_call_FIRST tasks"""
        supervisor = SupervisorSupervisor_ChainOfThought(
            name="test_cot_supervisor",
            model=mock_model
        )
        
        # Add always_call_FIRST task
        task = Mock()
        task.name = "always_first_task"
        task.always_call_FIRST = True
        task.always_call_LAST = False
        task.get_prompt.return_value = "Always first task prompt"
        task.get_tools.return_value = []
        
        supervisor.add_task(task)
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            messages=[HumanMessage(content="Test message")],
            call_trace=["initial"],
            completed_tasks=[]
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('builtins.print') as mock_print:
                result = await supervisor.llm_call_router(state)
                
                assert isinstance(result, Command)
                assert result.goto == "always_first_task"
                assert result.update["call_trace"] == ["initial", "test_cot_supervisor: goto always_first_task (always_call_FIRST)"]
                assert result.update["completed_tasks"] == ["test_cot_supervisor"]
    
    @pytest.mark.asyncio
    async def test_cot_router_with_always_call_last(self, mock_model, mock_supervisor_manager, mock_prompt_manager):
        """Test CoT router with always_call_LAST tasks when ending"""
        mock_model.ainvoke.return_value = SupervisorRouteState(step="END")
        
        supervisor = SupervisorSupervisor_ChainOfThought(
            name="test_cot_supervisor",
            model=mock_model
        )
        
        # Add always_call_LAST task
        task = Mock()
        task.name = "always_last_task"
        task.always_call_FIRST = False
        task.always_call_LAST = True
        task.get_prompt.return_value = "Always last task prompt"
        task.get_tools.return_value = []
        
        supervisor.add_task(task)
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            messages=[HumanMessage(content="Test message")],
            call_trace=["initial"],
            completed_tasks=[]
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('builtins.print') as mock_print:
                result = await supervisor.llm_call_router(state)
                
                assert isinstance(result, Command)
                assert result.goto == "always_last_task"
                assert result.update["call_trace"] == ["initial", "test_cot_supervisor: goto always_last_task (always_call_LAST)"]
                assert result.update["completed_tasks"] == ["test_cot_supervisor"]
    
    @pytest.mark.asyncio
    async def test_cot_router_with_reasoning_enabled(self, mock_model, mock_supervisor_manager, mock_prompt_manager):
        """Test CoT router with reasoning enabled"""
        # Mock two separate calls - first for reasoning, second for structured output
        mock_model.ainvoke.side_effect = [
            Mock(content="Detailed reasoning about task selection"),
            SupervisorRouteState(step="task1")
        ]
        
        supervisor = SupervisorSupervisor_ChainOfThought(
            name="test_cot_supervisor",
            model=mock_model
        )
        supervisor.enable_cot_routing = True
        
        # Add conditional task
        task = Mock()
        task.name = "task1"
        task.always_call_FIRST = False
        task.always_call_LAST = False
        task.get_prompt.return_value = "Task 1 prompt"
        task.get_tools.return_value = []
        
        supervisor.add_task(task)
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            messages=[HumanMessage(content="Test message")],
            call_trace=["initial"],
            completed_tasks=[]
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('builtins.print') as mock_print:
                result = await supervisor.llm_call_router(state)
                
                assert isinstance(result, Command)
                assert result.goto == "task1"
                assert result.update["call_trace"] == ["initial", "test_cot_supervisor: goto task1"]
                assert result.update["completed_tasks"] == ["test_cot_supervisor"]
                # Check that reasoning steps were added
                assert len(result.update["reasoning_steps"]) > 0
                assert "CoT routing to task1" in result.update["reasoning_steps"][0]
    
    @pytest.mark.asyncio
    async def test_cot_router_reasoning_error_fallback(self, mock_model, mock_supervisor_manager, mock_prompt_manager):
        """Test CoT router fallback when reasoning fails"""
        # First call fails, second call succeeds
        mock_model.ainvoke.side_effect = [
            Exception("Reasoning failed"),
            SupervisorRouteState(step="task1")
        ]
        
        supervisor = SupervisorSupervisor_ChainOfThought(
            name="test_cot_supervisor",
            model=mock_model
        )
        supervisor.enable_cot_routing = True
        
        # Add conditional task
        task = Mock()
        task.name = "task1"
        task.always_call_FIRST = False
        task.always_call_LAST = False
        task.get_prompt.return_value = "Task 1 prompt"
        task.get_tools.return_value = []
        
        supervisor.add_task(task)
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            messages=[HumanMessage(content="Test message")],
            call_trace=["initial"],
            completed_tasks=[]
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('builtins.print') as mock_print:
                result = await supervisor.llm_call_router(state)
                
                assert isinstance(result, Command)
                assert result.goto == "task1"
                # Should have printed error message
                mock_print.assert_called()
    
    @pytest.mark.asyncio
    async def test_cot_router_invalid_task_validation(self, mock_model, mock_supervisor_manager, mock_prompt_manager):
        """Test CoT router validation of invalid task names"""
        mock_model.ainvoke.return_value = SupervisorRouteState(step="nonexistent_task")
        
        supervisor = SupervisorSupervisor_ChainOfThought(
            name="test_cot_supervisor",
            model=mock_model
        )
        
        # Add valid task
        task = Mock()
        task.name = "valid_task"
        task.always_call_FIRST = False
        task.always_call_LAST = False
        task.get_prompt.return_value = "Valid task prompt"
        task.get_tools.return_value = []
        
        supervisor.add_task(task)
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            messages=[HumanMessage(content="Test message")],
            call_trace=["initial"],
            completed_tasks=[]
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('builtins.print') as mock_print:
                result = await supervisor.llm_call_router(state)
                
                assert isinstance(result, Command)
                assert result.goto == "END"  # Should be forced to END
                # Should have printed warning
                mock_print.assert_called()
    
    @pytest.mark.asyncio
    async def test_cot_router_completed_task_validation(self, mock_model, mock_supervisor_manager, mock_prompt_manager):
        """Test CoT router validation of already completed tasks"""
        mock_model.ainvoke.return_value = SupervisorRouteState(step="completed_task")
        
        supervisor = SupervisorSupervisor_ChainOfThought(
            name="test_cot_supervisor",
            model=mock_model
        )
        
        # Add task
        task = Mock()
        task.name = "completed_task"
        task.always_call_FIRST = False
        task.always_call_LAST = False
        task.get_prompt.return_value = "Completed task prompt"
        task.get_tools.return_value = []
        
        supervisor.add_task(task)
        
        state = SupervisorTaskState(
            user_guid="test-user-123",
            messages=[HumanMessage(content="Test message")],
            call_trace=["initial"],
            completed_tasks=["completed_task"]  # Task already completed
        )
        
        mock_user = Mock()
        mock_user.get_chat_history.return_value = [HumanMessage(content="Test message")]
        mock_user.username = "test_user"
        
        with patch('managers.manager_supervisors.ZairaUserManager') as mock_user_mgr:
            mock_user_mgr.find_user.return_value = mock_user
            
            with patch('builtins.print') as mock_print:
                result = await supervisor.llm_call_router(state)
                
                assert isinstance(result, Command)
                assert result.goto == "END"  # Should be forced to END
                # Should have printed warning
                mock_print.assert_called()

# Integration tests
class TestIntegration:
    """Integration tests for supervisor system"""
    
    @pytest.mark.asyncio
    async def test_full_supervisor_task_flow(self):
        """Test complete flow from task creation to execution"""
        with patch('managers.manager_supervisors.SupervisorManager') as mock_sm:
            mock_instance = Mock()
            mock_instance.default_model = Mock(spec=BaseLanguageModel)
            mock_sm.get_instance.return_value = mock_instance
            
            # Create supervisor
            supervisor = SupervisorSupervisor(name="test_supervisor")
            
            # Create task
            task = SupervisorTask_Base(name="test_task")
            
            # Mock task execution
            task.llm_call = AsyncMock(return_value="Task result")
            task.compile_default()
            
            # Add task to supervisor
            supervisor.add_task(task)
            supervisor.compile()
            
            # Verify task was added
            assert supervisor.has_task(task.scheduled_guid)
            assert supervisor.get_task("test_task") == task
            
            # Register with manager
            SupervisorManager.register_supervisor(supervisor)
            SupervisorManager.register_task(task)
            
            # Verify registration
            assert SupervisorManager.get_supervisor("test_supervisor") == supervisor
            assert SupervisorManager.get_task("test_task") == task
    
    @pytest.mark.asyncio
    async def test_task_transfer_between_supervisors(self):
        """Test transferring tasks between supervisors"""
        with patch('managers.manager_supervisors.SupervisorManager') as mock_sm:
            mock_instance = Mock()
            mock_instance.default_model = Mock(spec=BaseLanguageModel)
            mock_sm.get_instance.return_value = mock_instance
            
            # Create supervisors
            supervisor1 = SupervisorSupervisor(name="supervisor1")
            supervisor2 = SupervisorSupervisor(name="supervisor2")
            
            # Create task
            task = SupervisorTask_Base(name="test_task")
            task.scheduled_guid = uuid4()
            
            # Add task to first supervisor
            supervisor1.add_task(task, priority=5)
            
            # Register supervisors
            SupervisorManager.register_supervisor(supervisor1)
            SupervisorManager.register_supervisor(supervisor2)
            
            # Verify initial state
            assert supervisor1.has_task(task.scheduled_guid)
            assert not supervisor2.has_task(task.scheduled_guid)
            
            # Transfer task
            SupervisorManager.transfer_task(task.scheduled_guid, "supervisor1", "supervisor2", 10)
            
            # Verify transfer
            assert not supervisor1.has_task(task.scheduled_guid)
            assert supervisor2.has_task(task.scheduled_guid)
            assert supervisor2.get_task_priority(task.scheduled_guid) == 10
    
    @pytest.mark.asyncio
    async def test_chain_of_thought_with_regular_requests(self):
        """Test CoT supervisor with regular tasks"""
        with patch('managers.manager_supervisors.SupervisorManager') as mock_sm:
            mock_instance = Mock()
            mock_instance.default_model = Mock(spec=BaseLanguageModel)
            mock_sm.get_instance.return_value = mock_instance
            
            # Create CoT supervisor
            supervisor = SupervisorSupervisor_ChainOfThought(name="cot_supervisor")
            
            # Create CoT task
            cot_task = SupervisorTask_ChainOfThought(name="cot_task")
            cot_task.llm_call = AsyncMock(return_value=Command(update={"reasoning_steps": ["Step 1", "Step 2"]}))
            cot_task.compile_default()
            
            # Create regular task
            regular_task = SupervisorTask_Base(name="regular_task")
            regular_task.llm_call = AsyncMock(return_value="Regular result")
            regular_task.compile_default()
            
            # Add tasks to supervisor
            supervisor.add_task(cot_task)
            supervisor.add_task(regular_task)
            
            # Verify tasks were added
            assert len(supervisor.get_requests()) == 2
            assert supervisor.get_task("cot_task") == cot_task
            assert supervisor.get_task("regular_task") == regular_task
    
    def test_model_validation_and_error_handling(self):
        """Test model validation and error handling"""
        # Test with invalid model type
        with pytest.raises(TypeError):
            SupervisorTask_Base(name="test_task", model="invalid_model")
        
        # Test with None model (should use default)
        with patch('managers.manager_supervisors.SupervisorManager') as mock_sm:
            mock_instance = Mock()
            mock_instance.default_model = Mock(spec=BaseLanguageModel)
            mock_sm.get_instance.return_value = mock_instance
            
            task = SupervisorTask_Base(name="test_task", model=None)
            assert task.model == mock_instance.default_model
    
    def test_concurrent_task_management(self):
        """Test thread-safe task management"""
        import threading
        import time
        
        with patch('managers.manager_supervisors.SupervisorManager') as mock_sm:
            mock_instance = Mock()
            mock_instance.default_model = Mock(spec=BaseLanguageModel)
            mock_sm.get_instance.return_value = mock_instance
            
            supervisor = SupervisorSupervisor(name="concurrent_supervisor")
            
            # Function to add tasks concurrently
            def add_requests(start_index, count):
                for i in range(start_index, start_index + count):
                    task = Mock()
                    task.name = f"task_{i}"
                    task.scheduled_guid = uuid4()
                    supervisor.add_task(task, priority=i)
                    time.sleep(0.001)  # Small delay to increase chance of race conditions
            
            # Create multiple threads
            threads = []
            for i in range(5):
                thread = threading.Thread(target=add_requests, args=(i * 10, 10))
                threads.append(thread)
                thread.start()
            
            # Wait for all threads to complete
            for thread in threads:
                thread.join()
            
            # Verify all tasks were added correctly
            assert len(supervisor.get_requests()) == 50
            priorities = [priority for priority, _ in supervisor.get_requests_with_priorities()]
            assert priorities == sorted(priorities)  # Should be in order
    
    def test_memory_management(self):
        """Test memory management and cleanup"""
        with patch('managers.manager_supervisors.SupervisorManager') as mock_sm:
            mock_instance = Mock()
            mock_instance.default_model = Mock(spec=BaseLanguageModel)
            mock_sm.get_instance.return_value = mock_instance
            
            supervisor = SupervisorSupervisor(name="memory_supervisor")
            
            # Add many tasks
            task_guids = []
            for i in range(100):
                task = Mock()
                task.name = f"task_{i}"
                task.scheduled_guid = uuid4()
                task_guids.append(task.scheduled_guid)
                supervisor.add_task(task, priority=i)
            
            # Verify all tasks are present
            assert len(supervisor.get_requests()) == 100
            
            # Remove all tasks
            for guid in task_guids:
                removed_task = supervisor.remove_task(guid)
                assert removed_task is not None
            
            # Verify all tasks are removed
            assert len(supervisor.get_requests()) == 0
            assert len(supervisor._task_map) == 0