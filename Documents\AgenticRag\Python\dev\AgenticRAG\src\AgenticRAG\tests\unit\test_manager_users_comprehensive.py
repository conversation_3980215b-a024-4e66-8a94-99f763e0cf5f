from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
from managers.manager_users import ZairaUserManager
from userprofiles.ZairaUser import PERMISSION_LEVELS
from userprofiles.ZairaUser import ZairaUser
import pytest
from unittest.mock import AsyncMock, MagicMock, patch
from uuid import uuid4, UUID
import asyncio

class TestZairaUserManager:
    """Comprehensive test class for ZairaUserManager"""
    
    def setup_method(self):
        """Set up test fixtures and reset singleton"""
        # Reset the singleton instance for each test
        ZairaUserManager._instance = None
        self.test_username = "test_user"
        self.test_rank = PERMISSION_LEVELS.USER
        self.test_guid = uuid4()
        self.test_device_guid = uuid4()
    
    def teardown_method(self):
        """Clean up after each test"""
        # Reset the singleton instance
        ZairaUserManager._instance = None
    
    def test_singleton_pattern(self):
        """Test that ZairaUserManager follows singleton pattern"""
        manager1 = ZairaUserManager.get_instance()
        manager2 = ZairaUserManager.get_instance()
        
        assert manager1 is manager2
        assert isinstance(manager1, ZairaUserManager)
    
    def test_manager_initialization(self):
        """Test ZairaUserManager initialization"""
        manager = ZairaUserManager.get_instance()
        
        assert hasattr(manager, 'users')
        assert isinstance(manager.users, dict)
        assert hasattr(manager, 'lock')
        assert isinstance(manager.lock, asyncio.Lock)
        assert len(manager.users) == 0
    
    @pytest.mark.asyncio
    async def test_add_user_success(self):
        """Test successful user addition"""
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class, \
             patch('managers.scheduled_requests.ScheduledRequestPersistenceManager.get_instance') as mock_get_persistence:
            
            mock_user = MagicMock(spec=ZairaUser)
            mock_user_class.return_value = mock_user
            
            mock_persistence = AsyncMock()
            mock_get_persistence.return_value = mock_persistence
            
            user = await ZairaUserManager.add_user(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            assert user == mock_user
            mock_user_class.assert_called_once_with(
                self.test_username, self.test_rank, self.test_guid, self.test_device_guid
            )
            
            # Verify user was added to manager
            manager = ZairaUserManager.get_instance()
            assert str(self.test_guid) in manager.users
            assert manager.users[str(self.test_guid)] == mock_user
    
    @pytest.mark.asyncio
    async def test_add_user_duplicate_guid(self):
        """Test adding user with duplicate GUID raises error"""
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class:
            mock_user = MagicMock(spec=ZairaUser)
            mock_user_class.return_value = mock_user
            
            # Add user first time
            await ZairaUserManager.add_user(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            # Try to add user with same GUID
            with pytest.raises(ValueError, match=f"User with GUID {self.test_guid} already exists"):
                await ZairaUserManager.add_user(
                    username="another_user",
                    rank=self.test_rank,
                    guid=self.test_guid,  # Same GUID
                    device_guid=uuid4()
                )
    
    @pytest.mark.asyncio
    async def test_add_user_persistence_failure(self):
        """Test user addition when persistence manager fails"""
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class, \
             patch('managers.scheduled_requests.ScheduledRequestPersistenceManager.get_instance') as mock_get_persistence:
            
            mock_user = MagicMock(spec=ZairaUser)
            mock_user_class.return_value = mock_user
            
            mock_persistence = AsyncMock()
            mock_get_persistence.return_value = mock_persistence
            
            # Should not raise exception, just log warning
            user = await ZairaUserManager.add_user(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            assert user == mock_user
            # User should still be added despite persistence failure
            manager = ZairaUserManager.get_instance()
            assert str(self.test_guid) in manager.users
    
    def test_create_guid(self):
        """Test GUID creation"""
        guid1 = ZairaUserManager.create_guid()
        guid2 = ZairaUserManager.create_guid()
        
        assert isinstance(guid1, UUID)
        assert isinstance(guid2, UUID)
        assert guid1 != guid2  # Should be unique
    
    @pytest.mark.asyncio
    async def test_remove_user_success(self):
        """Test successful user removal"""
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class:
            mock_user = MagicMock(spec=ZairaUser)
            mock_user_class.return_value = mock_user
            
            # Add user first
            await ZairaUserManager.add_user(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            # Remove user
            result = await ZairaUserManager.remove_user(str(self.test_guid))
            
            assert result is True
            
            # Verify user was removed
            manager = ZairaUserManager.get_instance()
            assert str(self.test_guid) not in manager.users
    
    @pytest.mark.asyncio
    async def test_remove_user_not_found(self):
        """Test removing non-existent user"""
        nonexistent_guid = str(uuid4())
        
        result = await ZairaUserManager.remove_user(nonexistent_guid)
        
        assert result is False
    
    @pytest.mark.asyncio
    async def test_find_user_success(self):
        """Test successful user lookup by GUID"""
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class:
            mock_user = MagicMock(spec=ZairaUser)
            mock_user_class.return_value = mock_user
            
            # Add user first
            await ZairaUserManager.add_user(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            # Find user
            found_user = await ZairaUserManager.find_user(str(self.test_guid))
            
            assert found_user == mock_user
    
    @pytest.mark.asyncio
    async def test_find_user_not_found(self):
        """Test user lookup when user doesn't exist"""
        nonexistent_guid = str(uuid4())
        
        found_user = await ZairaUserManager.find_user(nonexistent_guid)
        
        assert found_user is None
    
    @pytest.mark.asyncio
    async def test_get_user_by_username_success(self):
        """Test successful user lookup by username"""
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class:
            mock_user = MagicMock(spec=ZairaUser)
            mock_user.username = self.test_username
            mock_user_class.return_value = mock_user
            
            # Add user first
            await ZairaUserManager.add_user(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            # Find user by username
            found_user = await ZairaUserManager.get_user(self.test_username)
            
            assert found_user == mock_user
    
    @pytest.mark.asyncio
    async def test_get_user_by_username_not_found(self):
        """Test user lookup by username when user doesn't exist"""
        found_user = await ZairaUserManager.get_user("nonexistent_user")
        
        assert found_user is None
    
    @pytest.mark.asyncio
    async def test_get_user_multiple_users(self):
        """Test user lookup by username with multiple users"""
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class:
            # Create multiple users
            user1_guid = uuid4()
            user2_guid = uuid4()
            
            mock_user1 = MagicMock(spec=ZairaUser)
            mock_user1.username = "user1"
            mock_user2 = MagicMock(spec=ZairaUser)
            mock_user2.username = "user2"
            
            # Mock the constructor to return different users
            def side_effect(username, rank, guid, device_guid):
                if username == "user1":
                    return mock_user1
                elif username == "user2":
                    return mock_user2
            
            mock_user_class.side_effect = side_effect
            
            # Add both users
            await ZairaUserManager.add_user("user1", self.test_rank, user1_guid, uuid4())
            await ZairaUserManager.add_user("user2", self.test_rank, user2_guid, uuid4())
            
            # Find specific user
            found_user = await ZairaUserManager.get_user("user1")
            assert found_user == mock_user1
            
            found_user = await ZairaUserManager.get_user("user2")
            assert found_user == mock_user2
    
    @pytest.mark.asyncio
    async def test_update_user_success(self):
        """Test successful user update"""
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class:
            mock_user = MagicMock(spec=ZairaUser)
            # Configure mock to have the attributes we want to set
            mock_user.real_name = None
            mock_user.email = None
            mock_user_class.return_value = mock_user
            
            # Add user first
            await ZairaUserManager.add_user(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            # Update user
            updated_user = await ZairaUserManager.update_user(
                str(self.test_guid),
                real_name="John Doe",
                email="<EMAIL>"
            )
            
            assert updated_user == mock_user
            # Verify attributes were set
            assert mock_user.real_name == "John Doe"
            assert mock_user.email == "<EMAIL>"
    
    @pytest.mark.asyncio
    async def test_update_user_not_found(self):
        """Test updating non-existent user"""
        nonexistent_guid = str(uuid4())
        
        updated_user = await ZairaUserManager.update_user(
            nonexistent_guid,
            real_name="John Doe"
        )
        
        assert updated_user is None
    
    @pytest.mark.asyncio
    async def test_concurrent_access(self):
        """Test concurrent access to user manager"""
        async def add_user_task(username, guid):
            with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class:
                mock_user = MagicMock(spec=ZairaUser)
                mock_user_class.return_value = mock_user
                return await ZairaUserManager.add_user(
                    username=username,
                    rank=self.test_rank,
                    guid=guid,
                    device_guid=uuid4()
                )
        
        # Create multiple concurrent tasks
        tasks = []
        guids = [uuid4() for _ in range(5)]
        for i, guid in enumerate(guids):
            task = asyncio.create_task(add_user_task(f"user_{i}", guid))
            tasks.append(task)
        
        # Wait for all tasks to complete
        results = await asyncio.gather(*tasks)
        
        # Verify all users were added
        manager = ZairaUserManager.get_instance()
        assert len(manager.users) == 5
        for guid in guids:
            assert str(guid) in manager.users
    
    @pytest.mark.asyncio
    async def test_lock_usage(self):
        """Test that lock is properly used for thread safety"""
        manager = ZairaUserManager.get_instance()
        
        # Test that lock exists and is an asyncio.Lock
        assert hasattr(manager, 'lock')
        assert isinstance(manager.lock, asyncio.Lock)
        
        # Test lock acquisition in different methods
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class:
            mock_user = MagicMock(spec=ZairaUser)
            mock_user_class.return_value = mock_user
            
            # These operations should work without deadlock
            await ZairaUserManager.add_user(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            found_user = await ZairaUserManager.find_user(str(self.test_guid))
            assert found_user == mock_user
            
            await ZairaUserManager.remove_user(str(self.test_guid))

class TestZairaUserManagerLogging:
    """Test logging functionality in ZairaUserManager"""
    
    def setup_method(self):
        """Reset singleton"""
        ZairaUserManager._instance = None
    
    def teardown_method(self):
        """Clean up"""
        ZairaUserManager._instance = None
    
    def test_logger_initialization(self):
        """Test that logger is properly initialized"""
        manager = ZairaUserManager.get_instance()
        
        assert hasattr(ZairaUserManager, 'logger')
        assert ZairaUserManager.logger.name == 'managers.manager_users'
    
    @pytest.mark.asyncio
    async def test_logging_on_operations(self):
        """Test that operations are properly logged"""
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class, \
             patch.object(ZairaUserManager, 'logger') as mock_logger:
            
            mock_user = MagicMock(spec=ZairaUser)
            mock_user_class.return_value = mock_user
            
            guid = uuid4()
            
            # Test add_user logging
            await ZairaUserManager.add_user(
                username="test_user",
                rank=PERMISSION_LEVELS.USER,
                guid=guid,
                device_guid=uuid4()
            )
            
            # Verify logging calls
            mock_logger.info.assert_any_call("Creating a new singleton instance of ZairaUserManager.")
            mock_logger.info.assert_any_call(f"User added: test_user with GUID: {guid}")
            
            # Test find_user logging
            await ZairaUserManager.find_user(str(guid))
            mock_logger.info.assert_any_call(f"User found: GUID {guid}")
            
            # Test remove_user logging
            await ZairaUserManager.remove_user(str(guid))
            mock_logger.info.assert_any_call(f"User removed: GUID {guid}")

class TestZairaUserManagerEdgeCases:
    """Test edge cases and error conditions"""
    
    def setup_method(self):
        """Reset singleton and setup test data"""
        ZairaUserManager._instance = None
        self.test_username = "test_user"
        self.test_rank = PERMISSION_LEVELS.USER
        self.test_guid = uuid4()
        self.test_device_guid = uuid4()
    
    def teardown_method(self):
        """Clean up"""
        ZairaUserManager._instance = None
    
    @pytest.mark.asyncio
    async def test_empty_manager_operations(self):
        """Test operations on empty manager"""
        # Test find on empty manager
        result = await ZairaUserManager.find_user(str(uuid4()))
        assert result is None
        
        # Test get_user on empty manager
        result = await ZairaUserManager.get_user("nonexistent")
        assert result is None
        
        # Test remove on empty manager
        result = await ZairaUserManager.remove_user(str(uuid4()))
        assert result is False
        
        # Test update on empty manager
        result = await ZairaUserManager.update_user(str(uuid4()), name="test")
        assert result is None
    
    @pytest.mark.asyncio
    async def test_invalid_guid_formats(self):
        """Test handling of invalid GUID formats"""
        # These should not raise exceptions but return None/False
        result = await ZairaUserManager.find_user("invalid-guid")
        assert result is None
        
        result = await ZairaUserManager.remove_user("invalid-guid")
        assert result is False
        
        result = await ZairaUserManager.update_user("invalid-guid", name="test")
        assert result is None

    @pytest.mark.asyncio
    async def test_ensure_models_rebuilt(self):
        """Test _ensure_models_rebuilt method"""
        # Reset the flag to test the rebuild process
        ZairaUserManager._models_rebuilt = False
        
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class, \
             patch('userprofiles.LongRunningZairaRequest.LongRunningZairaRequest') as mock_request_class:
            
            mock_user_class.model_rebuild = MagicMock()
            mock_request_class.model_rebuild = MagicMock()
            
            # Test rebuild
            await ZairaUserManager._ensure_models_rebuilt()
            
            # Verify rebuild was called
            mock_user_class.model_rebuild.assert_called_once()
            mock_request_class.model_rebuild.assert_called_once()
            
            # Verify flag was set
            assert ZairaUserManager._models_rebuilt is True
            
            # Test that subsequent calls don't rebuild
            mock_user_class.model_rebuild.reset_mock()
            mock_request_class.model_rebuild.reset_mock()
            
            await ZairaUserManager._ensure_models_rebuilt()
            
            # Should not be called again
            mock_user_class.model_rebuild.assert_not_called()
            mock_request_class.model_rebuild.assert_not_called()

    @pytest.mark.asyncio
    async def test_ensure_models_rebuilt_error(self):
        """Test _ensure_models_rebuilt method with import error"""
        # Reset the flag to test the rebuild process
        ZairaUserManager._models_rebuilt = False
        
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class, \
             patch('builtins.print') as mock_print:
            
            mock_user_class.model_rebuild = MagicMock(side_effect=ImportError("Test import error"))
            
            # Test rebuild with error - should not raise exception
            await ZairaUserManager._ensure_models_rebuilt()
            
            # Verify warning was printed
            mock_print.assert_called_once()
            assert "Warning: Failed to rebuild Pydantic models" in mock_print.call_args[0][0]

    @pytest.mark.asyncio
    async def test_update_user_invalid_attribute(self):
        """Test updating user with invalid attribute"""
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class:
            mock_user = MagicMock(spec=ZairaUser)
            mock_user_class.return_value = mock_user
            
            # Add user first
            await ZairaUserManager.add_user(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            # Try to update with invalid attribute
            updated_user = await ZairaUserManager.update_user(
                str(self.test_guid),
                nonexistent_attribute="value"
            )
            
            # Should return the user but not set the attribute
            assert updated_user == mock_user
            assert not hasattr(mock_user, 'nonexistent_attribute')

    @pytest.mark.asyncio
    async def test_list_users(self):
        """Test listing all users"""
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class:
            mock_user1 = MagicMock(spec=ZairaUser)
            mock_user2 = MagicMock(spec=ZairaUser)
            
            # Mock the constructor to return different users
            def side_effect(username, rank, guid, device_guid):
                if username == "user1":
                    return mock_user1
                elif username == "user2":
                    return mock_user2
                return MagicMock(spec=ZairaUser)
            
            mock_user_class.side_effect = side_effect
            
            # Add multiple users
            guid1 = uuid4()
            guid2 = uuid4()
            
            await ZairaUserManager.add_user("user1", self.test_rank, guid1, uuid4())
            await ZairaUserManager.add_user("user2", self.test_rank, guid2, uuid4())
            
            # List users
            users_list = await ZairaUserManager.list_users()
            
            assert len(users_list) == 2
            assert mock_user1 in users_list
            assert mock_user2 in users_list

    @pytest.mark.asyncio
    async def test_list_users_empty(self):
        """Test listing users when none exist"""
        users_list = await ZairaUserManager.list_users()
        
        assert len(users_list) == 0
        assert users_list == []

    @pytest.mark.asyncio
    async def test_find_users_by_rank(self):
        """Test finding users by rank"""
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class:
            mock_user_admin = MagicMock(spec=ZairaUser)
            mock_user_admin.rank = PERMISSION_LEVELS.ADMIN
            mock_user_regular = MagicMock(spec=ZairaUser)
            mock_user_regular.rank = PERMISSION_LEVELS.USER
            
            # Mock the constructor to return different users
            def side_effect(username, rank, guid, device_guid):
                if rank == PERMISSION_LEVELS.ADMIN:
                    return mock_user_admin
                elif rank == PERMISSION_LEVELS.USER:
                    return mock_user_regular
                return MagicMock(spec=ZairaUser)
            
            mock_user_class.side_effect = side_effect
            
            # Add users with different ranks
            guid1 = uuid4()
            guid2 = uuid4()
            
            await ZairaUserManager.add_user("admin", PERMISSION_LEVELS.ADMIN, guid1, uuid4())
            await ZairaUserManager.add_user("user", PERMISSION_LEVELS.USER, guid2, uuid4())
            
            # Find admin users
            admin_users = await ZairaUserManager.find_users_by_rank(PERMISSION_LEVELS.ADMIN)
            assert len(admin_users) == 1
            assert mock_user_admin in admin_users
            
            # Find regular users
            regular_users = await ZairaUserManager.find_users_by_rank(PERMISSION_LEVELS.USER)
            assert len(regular_users) == 1
            assert mock_user_regular in regular_users

    @pytest.mark.asyncio
    async def test_find_users_by_rank_empty(self):
        """Test finding users by rank when none exist"""
        users_with_rank = await ZairaUserManager.find_users_by_rank(PERMISSION_LEVELS.ADMIN)
        
        assert len(users_with_rank) == 0
        assert users_with_rank == []

    @pytest.mark.asyncio
    async def test_user_exists_true(self):
        """Test user_exists method when user exists"""
        with patch('userprofiles.ZairaUser.ZairaUser') as mock_user_class:
            mock_user = MagicMock(spec=ZairaUser)
            mock_user_class.return_value = mock_user
            
            # Add user first
            await ZairaUserManager.add_user(
                username=self.test_username,
                rank=self.test_rank,
                guid=self.test_guid,
                device_guid=self.test_device_guid
            )
            
            # Check if user exists
            exists = await ZairaUserManager.user_exists(str(self.test_guid))
            assert exists is True

    @pytest.mark.asyncio
    async def test_user_exists_false(self):
        """Test user_exists method when user doesn't exist"""
        nonexistent_guid = str(uuid4())
        
        exists = await ZairaUserManager.user_exists(nonexistent_guid)
        assert exists is False