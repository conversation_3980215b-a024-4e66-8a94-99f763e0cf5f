=== DEBUG TRACE STARTED at 2025-08-18 19:56:22.415470 ===
[2025-08-18 19:56:30.309] Traceback (most recent call last):
[2025-08-18 19:56:30.310]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\test_excel_analyzer_fix.py", line 122, in main
    [2025-08-18 19:56:30.310] tool_test_passed = await test_excel_analyzer_tool()
                       [2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.311] ^[2025-08-18 19:56:30.312] ^[2025-08-18 19:56:30.312] ^[2025-08-18 19:56:30.312] ^[2025-08-18 19:56:30.312] ^[2025-08-18 19:56:30.312] ^[2025-08-18 19:56:30.312] ^[2025-08-18 19:56:30.312] ^
[2025-08-18 19:56:30.312]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\test_excel_analyzer_fix.py", line 18, in test_excel_analyzer_tool
    [2025-08-18 19:56:30.312] from tasks.processing.task_excel_analyzer import ExcelAnalyzerTool
[2025-08-18 19:56:30.312]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\tasks\processing\task_excel_analyzer.py", line 12, in <module>
    [2025-08-18 19:56:30.312] from managers.manager_supervisors import SupervisorManager, SupervisorSupervisor, SupervisorTask_SingleAgent, SupervisorTask_Create_agent, SupervisorTaskState
[2025-08-18 19:56:30.312]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\managers\manager_supervisors.py", line 34, in <module>
    [2025-08-18 19:56:30.313] from managers.manager_users import ZairaUserManager
[2025-08-18 19:56:30.313]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\managers\manager_users.py", line 8, in <module>
    [2025-08-18 19:56:30.313] from userprofiles.ZairaUser import PERMISSION_LEVELS
[2025-08-18 19:56:30.313]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\__init__.py", line 4, in <module>
    [2025-08-18 19:56:30.313] from .ZairaUser import ZairaUser
[2025-08-18 19:56:30.313]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\userprofiles\ZairaUser.py", line 14, in <module>
    [2025-08-18 19:56:30.313] from managers.manager_multimodal import MultimodalManager
[2025-08-18 19:56:30.314]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\managers\manager_multimodal.py", line 10, in <module>
    [2025-08-18 19:56:30.314] from unstructured.partition.pdf import partition_pdf
[2025-08-18 19:56:30.314]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured\partition\pdf.py", line 19, in <module>
    [2025-08-18 19:56:30.314] from unstructured_inference.inference.layout import DocumentLayout
[2025-08-18 19:56:30.314]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured_inference\inference\layout.py", line 18, in <module>
    [2025-08-18 19:56:30.314] from unstructured_inference.models.base import get_model
[2025-08-18 19:56:30.315]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured_inference\models\base.py", line 7, in <module>
    [2025-08-18 19:56:30.315] from unstructured_inference.models.detectron2onnx import (
[2025-08-18 19:56:30.315]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\unstructured_inference\models\detectron2onnx.py", line 6, in <module>
    [2025-08-18 19:56:30.315] import onnxruntime
[2025-08-18 19:56:30.315]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\onnxruntime\__init__.py", line 61, in <module>
    [2025-08-18 19:56:30.315] raise import_capi_exception
[2025-08-18 19:56:30.315]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\onnxruntime\__init__.py", line 24, in <module>
    [2025-08-18 19:56:30.316] from onnxruntime.capi._pybind_state import (
[2025-08-18 19:56:30.316]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\.venv\Lib\site-packages\onnxruntime\capi\_pybind_state.py", line 32, in <module>
    [2025-08-18 19:56:30.316] from .onnxruntime_pybind11_state import *  # noqa
    [2025-08-18 19:56:30.316] ^[2025-08-18 19:56:30.316] ^[2025-08-18 19:56:30.316] ^[2025-08-18 19:56:30.316] ^[2025-08-18 19:56:30.316] ^[2025-08-18 19:56:30.316] ^[2025-08-18 19:56:30.316] ^[2025-08-18 19:56:30.316] ^[2025-08-18 19:56:30.316] ^[2025-08-18 19:56:30.316] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^[2025-08-18 19:56:30.317] ^
[2025-08-18 19:56:30.317] ImportError[2025-08-18 19:56:30.317] : [2025-08-18 19:56:30.317] DLL load failed while importing onnxruntime_pybind11_state: A dynamic link library (DLL) initialization routine failed.

[2025-08-18 19:56:30.317] During handling of the above exception, another exception occurred:

[2025-08-18 19:56:30.317] Traceback (most recent call last):
[2025-08-18 19:56:30.317]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\test_excel_analyzer_fix.py", line 143, in <module>
    [2025-08-18 19:56:30.318] result = asyncio.run(asyncio.wait_for(main(), timeout=30.0))
             [2025-08-18 19:56:30.318] ^[2025-08-18 19:56:30.318] ^[2025-08-18 19:56:30.318] ^[2025-08-18 19:56:30.318] ^[2025-08-18 19:56:30.318] ^[2025-08-18 19:56:30.318] ^[2025-08-18 19:56:30.318] ^[2025-08-18 19:56:30.318] ^[2025-08-18 19:56:30.318] ^[2025-08-18 19:56:30.318] ^[2025-08-18 19:56:30.318] ^[2025-08-18 19:56:30.318] ^[2025-08-18 19:56:30.318] ^[2025-08-18 19:56:30.318] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.319] ^[2025-08-18 19:56:30.320] ^[2025-08-18 19:56:30.320] ^[2025-08-18 19:56:30.320] ^[2025-08-18 19:56:30.320] ^[2025-08-18 19:56:30.320] ^[2025-08-18 19:56:30.320] ^
[2025-08-18 19:56:30.320]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\runners.py", line 190, in run
    [2025-08-18 19:56:30.320] return runner.run(main)
           [2025-08-18 19:56:30.320] ^[2025-08-18 19:56:30.321] ^[2025-08-18 19:56:30.321] ^[2025-08-18 19:56:30.321] ^[2025-08-18 19:56:30.321] ^[2025-08-18 19:56:30.321] ^[2025-08-18 19:56:30.321] ^[2025-08-18 19:56:30.321] ^[2025-08-18 19:56:30.321] ^[2025-08-18 19:56:30.321] ^[2025-08-18 19:56:30.321] ^[2025-08-18 19:56:30.321] ^[2025-08-18 19:56:30.321] ^[2025-08-18 19:56:30.321] ^[2025-08-18 19:56:30.321] ^[2025-08-18 19:56:30.321] ^
[2025-08-18 19:56:30.321]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\runners.py", line 118, in run
    [2025-08-18 19:56:30.321] return self._loop.run_until_complete(task)
           [2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.322] ^[2025-08-18 19:56:30.323] ^[2025-08-18 19:56:30.323] ^
[2025-08-18 19:56:30.323]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\base_events.py", line 654, in run_until_complete
    [2025-08-18 19:56:30.323] return future.result()
           [2025-08-18 19:56:30.323] ^[2025-08-18 19:56:30.323] ^[2025-08-18 19:56:30.323] ^[2025-08-18 19:56:30.323] ^[2025-08-18 19:56:30.323] ^[2025-08-18 19:56:30.323] ^[2025-08-18 19:56:30.323] ^[2025-08-18 19:56:30.323] ^[2025-08-18 19:56:30.323] ^[2025-08-18 19:56:30.323] ^[2025-08-18 19:56:30.324] ^[2025-08-18 19:56:30.324] ^[2025-08-18 19:56:30.324] ^[2025-08-18 19:56:30.324] ^[2025-08-18 19:56:30.324] ^
[2025-08-18 19:56:30.324]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\asyncio\tasks.py", line 489, in wait_for
    [2025-08-18 19:56:30.336] return fut.result()
           [2025-08-18 19:56:30.336] ^[2025-08-18 19:56:30.336] ^[2025-08-18 19:56:30.336] ^[2025-08-18 19:56:30.336] ^[2025-08-18 19:56:30.336] ^[2025-08-18 19:56:30.336] ^[2025-08-18 19:56:30.336] ^[2025-08-18 19:56:30.336] ^[2025-08-18 19:56:30.336] ^[2025-08-18 19:56:30.336] ^[2025-08-18 19:56:30.336] ^[2025-08-18 19:56:30.336] ^
[2025-08-18 19:56:30.336]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\test_excel_analyzer_fix.py", line 135, in main
    [2025-08-18 19:56:30.336] print(f"\n✗ Test execution failed: {e}")
[2025-08-18 19:56:30.337]   File "C:\Users\<USER>\Documents\AgenticRag\Python\dev\AgenticRAG\src\AgenticRAG\etc\debug_stdout_capture.py", line 47, in write
    [2025-08-18 19:56:30.337] self.original_stream.write(text)
[2025-08-18 19:56:30.337]   File "C:\Users\<USER>\AppData\Local\Programs\Python\Python311\Lib\encodings\cp1252.py", line 19, in encode
    [2025-08-18 19:56:30.349] return codecs.charmap_encode(input,self.errors,encoding_table)[0]
           [2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.349] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^[2025-08-18 19:56:30.350] ^
[2025-08-18 19:56:30.350] UnicodeEncodeError[2025-08-18 19:56:30.350] : [2025-08-18 19:56:30.350] 'charmap' codec can't encode character '\u2717' in position 2: character maps to <undefined>
