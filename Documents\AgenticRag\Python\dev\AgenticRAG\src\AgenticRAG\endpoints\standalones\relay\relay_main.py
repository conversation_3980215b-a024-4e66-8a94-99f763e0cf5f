import sys
from pathlib import Path

"""
One-time bridge for receiving OAuth commands and re-establishing connection with the correct web endpoint
"""

# Add the parent directory to Python path to find imports.py
sys.path.insert(0, str(Path(__file__).parent.parent.parent))

from imports import *

import aiohttp
from aiohttp import web
import asyncio
from urllib.parse import unquote, urlencode
import logging
import json

# Import LogFire for standardized logging
from managers.manager_logfire import LogFire

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger("oauth_proxy")

def extract_query_params(request: web.Request) -> dict:
    # Check if raw_path is bytes or string and handle accordingly
    if isinstance(request.raw_path, bytes):
        raw_path = unquote(request.raw_path.decode('utf-8'))
    else:
        raw_path = unquote(request.raw_path)

    # Look for the '?' character manually
    if '?' not in raw_path:
        return {}

    path_part, query_string = raw_path.split('?', 1)

    # Basic split and parse
    params = {}
    for pair in query_string.split('&'):
        if '=' in pair:
            key, value = pair.split('=', 1)
            params[key] = value
        elif pair:
            params[pair] = ''

    return params

def flatten_dict(data: dict, parent_key: str = '', sep: str = '.') -> dict:
    """Flatten nested dictionary for query parameters"""
    items = []
    for k, v in data.items():
        new_key = f"{parent_key}{sep}{k}" if parent_key else k
        if isinstance(v, dict):
            items.extend(flatten_dict(v, new_key, sep=sep).items())
        elif isinstance(v, list):
            for i, item in enumerate(v):
                if isinstance(item, dict):
                    items.extend(flatten_dict(item, f"{new_key}[{i}]", sep=sep).items())
                else:
                    items.append((f"{new_key}[{i}]", str(item)))
        else:
            items.append((new_key, str(v)))
    return dict(items)

def unflatten_dict(flat_dict: dict, sep: str = '.') -> dict:
    """Convert flattened dictionary back to nested structure"""
    result = {}
    
    for key, value in flat_dict.items():
        # Handle array notation like tags[0], tags[1]
        if '[' in key and key.endswith(']'):
            # Extract array name and index
            array_part, bracket_part = key.split('[', 1)
            index_str = bracket_part.rstrip(']')
            
            # Handle nested array paths like user.tags[0]
            if sep in array_part:
                # Navigate to the nested location
                parts = array_part.split(sep)
                current = result
                for part in parts[:-1]:
                    if part not in current:
                        current[part] = {}
                    current = current[part]
                
                # Initialize array if needed
                array_name = parts[-1]
                if array_name not in current:
                    current[array_name] = []
                
                # Ensure array is large enough
                try:
                    index = int(index_str)
                    while len(current[array_name]) <= index:
                        current[array_name].append(None)
                    current[array_name][index] = value
                except ValueError:
                    # Non-numeric index, treat as object key
                    if not isinstance(current[array_name], dict):
                        current[array_name] = {}
                    current[array_name][index_str] = value
            else:
                # Simple array like tags[0]
                if array_part not in result:
                    result[array_part] = []
                
                try:
                    index = int(index_str)
                    while len(result[array_part]) <= index:
                        result[array_part].append(None)
                    result[array_part][index] = value
                except ValueError:
                    # Non-numeric index, treat as object key
                    if not isinstance(result[array_part], dict):
                        result[array_part] = {}
                    result[array_part][index_str] = value
        
        # Handle dot notation like user.name, user.details.age
        elif sep in key:
            parts = key.split(sep)
            current = result
            
            # Navigate through nested structure
            for part in parts[:-1]:
                if part not in current:
                    current[part] = {}
                elif not isinstance(current[part], dict):
                    # Convert to dict if not already
                    current[part] = {}
                current = current[part]
            
            # Set the final value
            current[parts[-1]] = value
        
        # Simple key without nesting
        else:
            result[key] = value
    
    return result

async def handle_convert_endpoint(request: web.Request) -> web.Response:
    """Convert POST data to GET redirect with query parameters"""
    try:
        # Extract target path from /convert/{target_path}
        path = request.match_info.get('tail', '')
        if not path:
            LogFire.log("ERROR", "Convert endpoint called without target path", severity="error")
            return web.HTTPBadRequest(reason="Target path required")
        
        LogFire.log("DEBUG", f"Convert endpoint called for target path: {path}")
        
        # Parse POST data
        content_type = request.headers.get('content-type', '').lower()
        post_data = {}
        
        try:
            if 'application/json' in content_type:
                text_data = await request.text()
                if text_data.strip():
                    post_data = json.loads(text_data)
            elif 'application/x-www-form-urlencoded' in content_type or 'multipart/form-data' in content_type:
                post_data = dict(await request.post())
            else:
                # Try to parse as text
                text_data = await request.text()
                if text_data.strip():
                    try:
                        post_data = json.loads(text_data)
                    except json.JSONDecodeError:
                        # Treat as raw text
                        post_data = {'data': text_data}
        except (json.JSONDecodeError, ValueError) as e:
            LogFire.log("ERROR", f"Failed to parse POST data: {e}", severity="error")
            return web.HTTPBadRequest(reason="Invalid POST data format")
        
        # Flatten nested data for query parameters
        flattened_data = flatten_dict(post_data) if isinstance(post_data, dict) else {'data': str(post_data)}
        
        # Get existing query parameters from the original request
        existing_params = dict(request.query)
        
        # Extract 'target' parameter from POST data or query parameters (silently)
        target_base_url = None
        if isinstance(post_data, dict) and 'target' in post_data:
            target_base_url = post_data.pop('target')  # Remove from post_data to avoid including in query params
        elif 'target' in existing_params:
            target_base_url = existing_params.pop('target')  # Remove from existing_params to avoid including in query params
        
        # Re-flatten data after potentially removing 'target'
        flattened_data = flatten_dict(post_data) if isinstance(post_data, dict) else {'data': str(post_data)}
        
        # Combine existing and new parameters (new ones override existing)
        combined_params = {**existing_params, **flattened_data}
        
        # Build target URL
        query_string = urlencode(combined_params) if combined_params else ""
        
        if target_base_url:
            # Use provided target parameter as base URL
            # Normalize target URL (ensure no trailing slash for consistent behavior)
            target_base_url = target_base_url.rstrip('/')
            target_url = f"{target_base_url}/{path}"
            if query_string:
                target_url += f"?{query_string}"
            LogFire.log("DEBUG", f"Using provided target base URL: {target_base_url}")
        else:
            # Determine the correct public domain and scheme using existing logic
            # Check for proxy headers first, then fall back to Referer
            forwarded_host = request.headers.get('X-Forwarded-Host')
            forwarded_proto = request.headers.get('X-Forwarded-Proto')
            
            if forwarded_host:
                # Use forwarded headers from proxy/load balancer
                scheme = forwarded_proto or 'https'
                host = forwarded_host
            elif 'Referer' in request.headers:
                # Extract domain from Referer header
                from urllib.parse import urlparse
                referer_url = urlparse(request.headers['Referer'])
                scheme = referer_url.scheme
                host = referer_url.netloc
            else:
                # Fallback - use relay.askzaira.com as default
                scheme = 'https'
                host = 'relay.askzaira.com'
            
            target_url = f"{scheme}://{host}/{path}"
            if query_string:
                target_url += f"?{query_string}"
        
        LogFire.log("DEBUG", f"Converting POST to GET redirect: {target_url}")
        
        # Return redirect response
        return web.HTTPFound(location=target_url)
        
    except Exception as e:
        from etc import helper_functions
        helper_functions.exception_triggered(e, "convert_endpoint", None)
        return web.HTTPInternalServerError(reason="Internal server error")

async def handle_convert_post_endpoint(request: web.Request) -> web.Response:
    """Convert GET query parameters to POST request with JSON body"""
    try:
        # Extract target path from /convertpost/{target_path}
        path = request.match_info.get('tail', '')
        if not path:
            LogFire.log("ERROR", "Convert POST endpoint called without target path", severity="error")
            return web.HTTPBadRequest(reason="Target path required")
        
        LogFire.log("DEBUG", f"Convert POST endpoint called for target path: {path}")
        
        # Get query parameters
        query_params = dict(request.query)
        
        # Extract 'target' parameter from query parameters (silently)
        target_base_url = None
        if 'target' in query_params:
            target_base_url = query_params.pop('target')  # Remove from query_params to avoid including in POST body
        
        if not query_params:
            LogFire.log("DEBUG", "No query parameters found, sending empty POST body")
            post_data = {}
        else:
            # Convert flattened parameters back to nested structure
            post_data = unflatten_dict(query_params)
        
        LogFire.log("DEBUG", f"Converted query params to JSON: {json.dumps(post_data)}")
        
        # Build target URL for internal POST request
        if target_base_url:
            # Use provided target parameter as base URL
            # Normalize target URL (ensure no trailing slash for consistent behavior)
            target_base_url = target_base_url.rstrip('/')
            target_url = f"{target_base_url}/{path}"
            LogFire.log("DEBUG", f"Using provided target base URL for POST: {target_base_url}")
        else:
            # Use existing logic with internal Docker address
            target_url = f"http://host.docker.internal:{ZAIRA_PYTHON_PORT}/{path}"
        
        try:
            # Make internal POST request
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    url=target_url,
                    json=post_data,
                    headers={'Content-Type': 'application/json'},
                    timeout=aiohttp.ClientTimeout(total=30)
                ) as response:
                    # Get response data
                    response_body = await response.read()
                    response_headers = dict(response.headers)
                    
                    # Remove hop-by-hop headers from response
                    response_headers.pop('Connection', None)
                    response_headers.pop('Transfer-Encoding', None)
                    response_headers.pop('Content-Length', None)
                    
                    LogFire.log("DEBUG", f"Successfully converted GET to POST request (status: {response.status})")
                    
                    # Return the response from the target endpoint
                    return web.Response(
                        body=response_body,
                        status=response.status,
                        headers=response_headers
                    )
                    
        except (aiohttp.ClientConnectorError, aiohttp.ClientError) as e:
            LogFire.log("ERROR", f"Failed to make internal POST request to {target_url}: {e}", severity="error")
            return web.HTTPServiceUnavailable(reason=f"Failed to connect to internal service: {e}")
        
    except Exception as e:
        from etc import helper_functions
        helper_functions.exception_triggered(e, "convert_post_endpoint", None)
        return web.HTTPInternalServerError(reason="Internal server error")

async def handle_endpoint_relay(request: web.Request, target_url_suffix: str, endpoint_name: str):
    """Generic handler for relaying to internal service with port increment logic"""
    try:
        max_consecutive_failures = 3
        
        # Reset to ZAIRA_PYTHON_PORT for each new webhook call
        starting_port = ZAIRA_PYTHON_PORT
        
        consecutive_failures = 0
        attempt = 0
        
        while consecutive_failures < max_consecutive_failures:
            # Calculate current port to try
            port = starting_port + attempt
            target_url = f"http://host.docker.internal:{port}/{target_url_suffix}{request.query_string and '?' + request.query_string or ''}"
            
            try:
                logger.info(f"Relaying {request.method} request to {target_url}")
                
                # Get request body and headers
                body = await request.read()
                headers = dict(request.headers)
                
                # Remove hop-by-hop headers
                headers.pop('Host', None)
                headers.pop('Connection', None)
                headers.pop('Content-Length', None)
                
                async with aiohttp.ClientSession() as session:
                    async with session.request(
                        method=request.method,
                        url=target_url,
                        data=body,
                        headers=headers,
                        timeout=aiohttp.ClientTimeout(total=10)
                    ) as response:
                        # Successfully relayed
                        response_body = await response.read()
                        response_headers = dict(response.headers)
                        
                        # Remove hop-by-hop headers from response
                        response_headers.pop('Connection', None)
                        response_headers.pop('Transfer-Encoding', None)
                        
                        logger.info(f"Successfully relayed {request.method} request to {target_url}")
                        return web.Response(
                            body=response_body,
                            status=response.status,
                            headers=response_headers
                        )
                        
            except (aiohttp.ClientConnectorError, aiohttp.ClientError) as e:
                logger.warning(f"Failed to connect to {target_url}: {e}")
                consecutive_failures += 1
                
                # If this is the first attempt and it fails, raise exception
                if attempt == 0:
                    logger.error(f"{endpoint_name} relay failed on first attempt to port {port}")
                    raise Exception(f"{endpoint_name} relay failed on first attempt to port {port}: {e}")
                
                # Check if we've hit the consecutive failure limit
                if consecutive_failures >= max_consecutive_failures:
                    logger.error(f"{endpoint_name} relay stopped after {consecutive_failures} consecutive failures")
                    break
                
                # Continue to next port
                attempt += 1
                continue
                
        # If we've hit the consecutive failure limit
        logger.error(f"{endpoint_name} relay failed after {consecutive_failures} consecutive failures starting from port {starting_port}")
        return web.HTTPServiceUnavailable(reason=f"Stopped after {consecutive_failures} consecutive failures")
        
    except Exception as e:
        logger.error(f"Error in {endpoint_name} relay: {e}")
        etc.helper_functions.exception_triggered(e)
        return web.HTTPInternalServerError(reason=str(e))

async def handle_whatsapp_webhook(request: web.Request):
    """Handle WhatsApp webhook by relaying to internal service with port increment logic"""
    return await handle_endpoint_relay(request, "whatsapp/webhook", "WhatsApp webhook")

async def handle_teams_messages(request: web.Request):
    """Handle Teams API messages by relaying to internal service with port increment logic"""
    return await handle_endpoint_relay(request, "teams/api/messages", "Teams API")

async def handle_proxy(request: web.Request):
    try:
        params = extract_query_params(request)
        state = params.get("state")
        target_url = None
        
        if state:
            # Handle requests with state parameter (existing logic)
            split = state.split("-", 1)
            client_id = split[0]
            identifier = split[1]
            if client_id.lower() == "python":
                target_url = f"{Globals.get_endpoint_address()}{request.path_qs}"
            else:
                target_url = f"https://{client_id}.askzaira.com{request.path_qs}"

        if target_url:
            logger.info(f"Proxying {request.method} request to {target_url}")

            # Get request body and headers
            body = await request.read()
            headers = dict(request.headers)
            
            # Remove hop-by-hop headers
            headers.pop('Host', None)
            headers.pop('Connection', None)
            headers.pop('Content-Length', None)
            
            try:
                return web.HTTPFound(location=target_url)
                # async with aiohttp.ClientSession() as session:
                #     async with session.request(
                #         method=request.method,
                #         url=target_url,
                #         data=body,
                #         headers=headers
                #         #timeout=aiohttp.ClientTimeout(total=10)
                #     ) as response:
                #         # Successfully relayed
                #         response_body = await response.read()
                #         response_headers = dict(response.headers)
                        
                #         # Remove hop-by-hop headers from response
                #         response_headers.pop('Connection', None)
                #         response_headers.pop('Transfer-Encoding', None)
                        
                #         # Handle Location header for redirects from internal service
                #         if 'Location' in response_headers:
                #             location = response_headers['Location']
                #             # If the internal service is redirecting to itself, keep the proxy URL
                #             internal_address = Globals.get_endpoint_address()
                #             logger.info(f"DEBUG: Original Location: {location}, Internal address: {internal_address}, Status: {response.status}")
                            
                #             if location.startswith(internal_address):
                #                 # Get the current request's host and scheme to build proxy address
                #                 proxy_scheme = request.scheme
                #                 proxy_host = request.host
                #                 proxy_address = f"{proxy_scheme}://{proxy_host}"
                                
                #                 # Replace internal address with proxy address for browser
                #                 relay_location = location.replace(internal_address, proxy_address)
                #                 response_headers['Location'] = relay_location
                #                 logger.info(f"DEBUG: Redirecting location from {location} to {relay_location}")
                #             else:
                #                 logger.info(f"DEBUG: Location {location} does not start with internal address {internal_address} - leaving unchanged")
                #         else:
                #             logger.info(f"DEBUG: No Location header in response with status {response.status}")
                        
                #         logger.info(f"Successfully proxied {request.method} request (status: {response.status}) with headers: {list(response_headers.keys())}")
                #         return web.Response(
                #             body=response_body,
                #             status=response.status,
                #             headers=response_headers
                #         )
                        
            except (aiohttp.ClientConnectorError, aiohttp.ClientError) as e:
                logger.error(f"Failed to proxy {request.method} request to {target_url}: {e}")
                # Fallback to HTML redirect for browser requests
                html_content = f"""
                <!DOCTYPE html>
                <html lang="en">
                <head>
                    <meta charset="UTF-8" />
                    <title>Redirecting...</title>
                    <script>
                        // Redirect immediately
                        window.location.href = "{target_url}";
                    </script>
                </head>
                <body>
                    <p>Token aanvragen... Klik <a href='{target_url}'>hier</a> als dit niet automatisch laadt.</p>
                </body>
                </html>
                """
                return web.Response(text=html_content, content_type='text/html')
                
    except Exception as e:
        etc.helper_functions.exception_triggered(e, "relay_proxy")
        return web.HTTPInternalServerError(reason=str(e))
    
    # If we reach here, no valid route was found
    raise web.HTTPNotFound(reason=f"No mapping found for path: {request.path}")

async def favicon(request: web.Request) -> web.Response:
    return await Globals.favicon_endpoint()

async def create_app():
    app = web.Application()
    # Add WhatsApp webhook route first (more specific)
    app.router.add_route("*", "/whatsapp/webhook", handle_whatsapp_webhook)
    # Add Teams API messages route
    app.router.add_route("*", "/teams/api/messages", handle_teams_messages)
    # Add convert endpoint for POST-to-GET conversion
    app.router.add_route("POST", "/convert/{tail:.*}", handle_convert_endpoint)
    # Add convertpost endpoint for GET-to-POST conversion
    app.router.add_route("GET", "/convert/{tail:.*}", handle_convert_post_endpoint)
    from endpoints.api_endpoint import APIEndpoint
    app.router.add_route("*", '/favicon.ico', favicon)
    # Add catch-all route last
    app.router.add_route("*", "/{tail:.*}", handle_proxy)
    return app

runner = None
async def main():
    app = await create_app()
    runner = web.AppRunner(app)
    await runner.setup()
    from globals import RELAY_PORT, ZAIRA_PYTHON_PORT
    site = web.TCPSite(runner, '0.0.0.0', RELAY_PORT)
    await site.start()
    LogFire.log("INIT", f"Starting OAuth External. Listening on {Globals.get_endpoint_address()}:{RELAY_PORT}.")
    # OAuth External startup already handled by LogFire.log above

    # Run forever until interrupted
    if not Globals.is_debug():
        try:
            while True:
                await asyncio.sleep(3600)
        except KeyboardInterrupt:
            LogFire.log("INIT", "Shutting down OAuth External server...")
            # OAuth External shutdown already handled by LogFire.log above
        cleanup() # Cleaned called manually if it is debug

async def cleanup():
    await runner.cleanup()

if __name__ == "__main__":
    try:
        Globals.set_debug(False)
        asyncio.run(main())
    except Exception as e:
        LogFire.log("ERROR", f"OAuth External server error: {e}", severity="error")
        # OAuth External error already handled by LogFire.log above
