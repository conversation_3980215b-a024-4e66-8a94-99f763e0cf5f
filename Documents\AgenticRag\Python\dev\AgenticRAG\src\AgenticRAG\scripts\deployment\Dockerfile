# Use the official Python base image
FROM python:3.11-slim

# Set working directory
WORKDIR /app

# Install git (needed for pip to clone repos)
RUN apt-get update && apt-get install -y \
	git \
    libgl1 \
    libglib2.0-0 \
    libsm6 \
    libxrender1 \
    libxext6 \
    poppler-utils \
    tesseract-ocr \
    pandoc \
    libmagic1 \
	docker.io \
	libmagic1 \
	libmagic-dev \
	&& rm -rf /var/lib/apt/lists/*

# Copy dependency list and install
COPY scripts/deployment/requirements1.txt .
COPY scripts/deployment/requirements2.txt .
COPY scripts/deployment/requirements3.txt .
RUN pip install -r requirements1.txt
RUN pip install -r requirements2.txt
RUN pip install -r requirements3.txt
RUN playwright install chromium
RUN playwright install-deps chromium

# Copy application code
COPY . .

# Expose the port the app runs on
EXPOSE 41000

# Command to run the app
CMD ["python", "production.py"]
