from sys import path as sys_path
from os import path as os_path
sys_path.insert(0, os_path.join(os_path.dirname(__file__), '../../src'))

from imports import *
import pytest
import unittest
from unittest.mock import AsyncMock, MagicMock, patch, mock_open
from aiohttp import web
from endpoints.api_endpoint import APIEndpoint
from uuid import uuid4
import json

class TestAPIEndpoint:
    """Comprehensive test suite for APIEndpoint"""
    
    def setup_method(self):
        """Setup test fixtures"""
        # Reset singleton instance
        APIEndpoint._instance = None
        self.endpoint = APIEndpoint()
        
    def test_singleton_pattern(self):
        """Test that APIEndpoint follows singleton pattern"""
        endpoint1 = APIEndpoint()
        endpoint2 = APIEndpoint()
        endpoint3 = APIEndpoint.get_instance()
        
        assert endpoint1 is endpoint2
        assert endpoint2 is endpoint3
        assert endpoint1._instance is not None
        
    def test_initialization(self):
        """Test APIEndpoint initialization"""
        endpoint = APIEndpoint()
        
        assert hasattr(endpoint, 'initialized')
        assert endpoint.initialized == True
        assert endpoint.agent is None
        assert endpoint.aio_app is None
        assert endpoint.bot_generic is None
        assert endpoint.restart_Task is None
        
    @pytest.mark.asyncio
    async def test_setup(self):
        """Test APIEndpoint setup method"""
        with patch('endpoints.api_endpoint.web.Application') as mock_app, \
             patch('endpoints.api_endpoint.MyBot_Generic') as mock_bot:
            
            mock_app_instance = MagicMock()
            mock_app.return_value = mock_app_instance
            mock_bot_instance = MagicMock()
            mock_bot.return_value = mock_bot_instance
            
            await APIEndpoint.setup()
            
            instance = APIEndpoint.get_instance()
            assert instance.aio_app == mock_app_instance
            assert instance.bot_generic == mock_bot_instance
            
            # Verify routes were added
            mock_app_instance.add_routes.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_start_app(self):
        """Test start_app method"""
        with patch('endpoints.api_endpoint.web.AppRunner') as mock_runner, \
             patch('endpoints.api_endpoint.web.TCPSite') as mock_site:
            
            mock_runner_instance = MagicMock()
            mock_runner.return_value = mock_runner_instance
            mock_runner_instance.setup = AsyncMock()
            
            mock_site_instance = MagicMock()
            mock_site.return_value = mock_site_instance
            mock_site_instance.start = AsyncMock()
            
            app = MagicMock()
            await self.endpoint.start_app(app, "localhost", 8080)
            
            mock_runner.assert_called_once_with(app)
            mock_runner_instance.setup.assert_called_once()
            mock_site.assert_called_once_with(mock_runner_instance, "localhost", 8080, ssl_context=None)
            mock_site_instance.start.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_late_setup(self):
        """Test late_setup method"""
        with patch.object(APIEndpoint, 'get_instance') as mock_get_instance, \
             patch('endpoints.api_endpoint.get_value_from_env') as mock_get_env:
            
            mock_instance = MagicMock()
            mock_instance.start_app = AsyncMock()
            mock_instance.aio_app = MagicMock()
            mock_instance.aio_app.router.routes.return_value = []
            mock_get_instance.return_value = mock_instance
            mock_get_env.return_value = "8080"
            
            await APIEndpoint.late_setup()
            
            mock_instance.start_app.assert_called_once_with(
                mock_instance.aio_app, host="0.0.0.0", port=unittest.mock.ANY
            )
            
    @pytest.mark.asyncio
    async def test_home_endpoint(self):
        """Test home endpoint"""
        request = MagicMock()
        request.__getitem__.return_value = "127.0.0.1"
        
        with patch('builtins.print') as mock_print:
            response = await self.endpoint.home(request)
            
            assert isinstance(response, web.Response)
            assert response.content_type == 'text/html'
            mock_print.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_login_endpoint(self):
        """Test login endpoint"""
        request = MagicMock()
        request.__getitem__.return_value = "127.0.0.1"
        
        with patch.object(self.endpoint, 'load_content_login', return_value="") as mock_load, \
             patch('etc.helper_functions.create_html_out', return_value="<html></html>") as mock_create:
            
            response = await self.endpoint.login(request)
            
            assert isinstance(response, web.Response)
            assert response.content_type == 'text/html'
            mock_load.assert_called_once_with(request)
            mock_create.assert_called_once_with("login", "")
            
    @pytest.mark.asyncio
    async def test_validate_login_success(self):
        """Test validate_login with correct credentials"""
        request = MagicMock()
        request.json = AsyncMock(return_value={
            "username": "proxyhttpaio",
            "password": "test_password"
        })
        
        with patch('endpoints.api_endpoint.Globals.is_docker', return_value=False), \
             patch('etc.helper_functions.get_password', return_value="test_password"):
            
            response = await self.endpoint.validate_login(request)
            
            assert isinstance(response, web.Response)
            response_data = json.loads(response.text)
            assert response_data['success'] == True
            
    @pytest.mark.asyncio
    async def test_validate_login_failure(self):
        """Test validate_login with incorrect credentials"""
        request = MagicMock()
        request.json = AsyncMock(return_value={
            "username": "wrong_user",
            "password": "wrong_password"
        })
        
        with patch('endpoints.api_endpoint.Globals.is_docker', return_value=False):
            response = await self.endpoint.validate_login(request)
            
            assert isinstance(response, web.Response)
            response_data = json.loads(response.text)
            assert response_data['success'] == False
            assert response.status == 401
            
    @pytest.mark.asyncio
    async def test_dashboard_endpoint(self):
        """Test dashboard endpoint"""
        request = MagicMock()
        request.__getitem__.return_value = "127.0.0.1"
        request.content_type = 'application/json'
        request.json = AsyncMock(return_value={
            "username": "proxyhttpaio",
            "password": "test_password"
        })
        
        with patch('endpoints.api_endpoint.Globals.is_docker', return_value=False), \
             patch('etc.helper_functions.get_password', return_value="test_password"), \
             patch.object(self.endpoint, 'load_content_dashboard', return_value="<div></div>") as mock_load, \
             patch('etc.helper_functions.create_html_out', return_value="<html></html>") as mock_create:
            
            response = await self.endpoint.dashboard(request)
            
            assert isinstance(response, web.Response)
            assert response.content_type == 'text/html'
            mock_load.assert_called_once_with(request)
            mock_create.assert_called_once_with("dashboard", "<div></div>")
            
    @pytest.mark.asyncio
    async def test_dashboard_unauthorized(self):
        """Test dashboard endpoint with unauthorized user"""
        request = MagicMock()
        request.content_type = 'application/json'
        request.json = AsyncMock(return_value={
            "username": "wrong_user",
            "password": "wrong_password"
        })
        
        with patch('endpoints.api_endpoint.Globals.is_docker', return_value=False):
            response = await self.endpoint.dashboard(request)
            
            assert isinstance(response, web.Response)
            response_data = json.loads(response.text)
            assert response_data['success'] == False
            assert response.status == 401
            
    @pytest.mark.asyncio
    async def test_ask_endpoint(self):
        """Test ask endpoint"""
        request = MagicMock()
        request.__getitem__.return_value = "127.0.0.1"
        
        # Mock query as a MagicMock to avoid read-only issues
        mock_query = MagicMock()
        mock_query.get = MagicMock(return_value="test question")
        request.query = mock_query
        
        mock_query_engine = MagicMock()
        mock_query_engine.query.return_value = "test response"
        
        with patch('endpoints.api_endpoint.Globals.get_query_engine_default', return_value=mock_query_engine):
            response = await self.endpoint.ask(request)
            
            assert isinstance(response, web.Response)
            response_data = json.loads(response.text)
            assert response_data['response'] == "test response"
            
    @pytest.mark.asyncio
    async def test_ask_url_endpoint(self):
        """Test ask_url endpoint"""
        request = MagicMock()
        request.__getitem__.return_value = "127.0.0.1"
        
        # Mock query as a MagicMock to avoid read-only issues
        mock_query = MagicMock()
        mock_query.get = MagicMock(return_value="test question")
        request.query = mock_query
        
        mock_query_engine = MagicMock()
        mock_query_engine.query.return_value = "test response"
        
        with patch('endpoints.api_endpoint.Globals.get_query_engine_default', return_value=mock_query_engine):
            response = await self.endpoint.ask_url(request)
            
            assert isinstance(response, web.Response)
            assert response.content_type == 'text/html'
            assert "test response" in response.text
            
    @pytest.mark.asyncio
    async def test_ask_delayed_endpoint(self):
        """Test ask_delayed endpoint"""
        request = MagicMock()
        request.__getitem__.return_value = "127.0.0.1"
        
        # Mock query as a MagicMock to avoid read-only issues
        test_guid = str(uuid4())
        mock_query = MagicMock()
        mock_query.get = MagicMock(side_effect=lambda k, default="": {"query": "test question", "guid": test_guid}.get(k, default))
        request.query = mock_query
        
        mock_user = MagicMock()
        mock_task = MagicMock()
        mock_task.scheduled_guid = str(uuid4())
        mock_user.on_message = AsyncMock(return_value=mock_task)
        
        with patch('managers.manager_users.ZairaUserManager.find_user', return_value=mock_user):
            response = await self.endpoint.ask_delayed(request)
            
            assert isinstance(response, web.Response)
            response_data = json.loads(response.text)
            assert response_data['message'] == 'Task started!'
            assert 'scheduled_guid' in response_data
            
    @pytest.mark.asyncio
    async def test_ask_delayed_missing_params(self):
        """Test ask_delayed endpoint with missing parameters"""
        request = MagicMock()
        
        # Mock query as a MagicMock to avoid read-only issues
        mock_query = MagicMock()
        mock_query.get = MagicMock(return_value="")
        request.query = mock_query
        
        response = await self.endpoint.ask_delayed(request)
        
        assert isinstance(response, web.Response)
        assert response.status == 400
        response_data = json.loads(response.text)
        assert 'error' in response_data
        
    @pytest.mark.asyncio
    async def test_slack_events_verification(self):
        """Test slack_events endpoint with URL verification"""
        request = MagicMock()
        request.json = AsyncMock(return_value={
            'type': 'url_verification',
            'challenge': 'test_challenge'
        })
        
        response = await self.endpoint.slack_events(request)
        
        assert isinstance(response, web.Response)
        response_data = json.loads(response.text)
        assert response_data['challenge'] == 'test_challenge'
        
    @pytest.mark.asyncio
    async def test_slack_events_normal(self):
        """Test slack_events endpoint with normal event"""
        request = MagicMock()
        request.json = AsyncMock(return_value={
            'type': 'event_callback',
            'event': {'type': 'message', 'text': 'test'}
        })
        
        response = await self.endpoint.slack_events(request)
        
        assert isinstance(response, web.Response)
        response_data = json.loads(response.text)
        assert response_data['status'] == 'ok'
        
    @pytest.mark.asyncio
    async def test_embedding_openai_success(self):
        """Test embedding_openai endpoint success"""
        request = MagicMock()
        request.json = AsyncMock(return_value={
            'input': ['test text'],
            'model': 'test-model'
        })
        
        mock_embeddings = [[0.1, 0.2, 0.3]]
        
        with patch('managers.manager_retrieval.RetrievalManager.get_embeddings_dense', new_callable=AsyncMock, return_value=mock_embeddings):
            response = await self.endpoint.embedding_openai(request)
            
            assert isinstance(response, web.Response)
            response_data = json.loads(response.text)
            assert response_data['object'] == 'list'
            assert len(response_data['data']) == 1
            assert response_data['data'][0]['embedding'] == [0.1, 0.2, 0.3]
            
    @pytest.mark.asyncio
    async def test_embedding_openai_invalid_input(self):
        """Test embedding_openai endpoint with invalid input"""
        request = MagicMock()
        request.json = AsyncMock(return_value={
            'input': 123,  # Invalid input type
            'model': 'test-model'
        })
        
        response = await self.endpoint.embedding_openai(request)
        
        assert isinstance(response, web.Response)
        assert response.status == 400
        response_data = json.loads(response.text)
        assert 'error' in response_data
        
    @pytest.mark.asyncio
    async def test_embedding_onnx_success(self):
        """Test embedding_onnx endpoint success"""
        request = MagicMock()
        request.json = AsyncMock(return_value={
            'input': ['test text']
        })
        
        mock_embeddings = [MagicMock()]
        mock_embeddings[0].tolist.return_value = [0.1, 0.2, 0.3]
        
        with patch('managers.manager_retrieval.RetrievalManager.get_embeddings_dense', new_callable=AsyncMock, return_value=mock_embeddings):
            response = await self.endpoint.embedding_onnx(request)
            
            assert isinstance(response, web.Response)
            response_data = json.loads(response.text)
            assert len(response_data['data']) == 1
            assert response_data['data'][0]['embedding'] == [0.1, 0.2, 0.3]
            
    @pytest.mark.asyncio
    async def test_embedding_onnx_missing_input(self):
        """Test embedding_onnx endpoint with missing input"""
        request = MagicMock()
        request.json = AsyncMock(return_value={})
        
        response = await self.endpoint.embedding_onnx(request)
        
        assert isinstance(response, web.Response)
        assert response.status == 400
        response_data = json.loads(response.text)
        assert 'error' in response_data
        
    @pytest.mark.asyncio
    async def test_handle_file_upload_success(self):
        """Test handle_file_upload endpoint success"""
        request = MagicMock()
        
        # Mock multipart reader
        mock_reader = MagicMock()
        mock_field = MagicMock()
        mock_field.name = 'files'
        mock_field.filename = 'test.pdf'
        mock_field.read_chunk = AsyncMock(side_effect=[b'test data', b''])
        mock_reader.next = AsyncMock(side_effect=[mock_field, None])
        request.multipart = AsyncMock(return_value=mock_reader)
        
        with patch('endpoints.api_endpoint.Globals.is_docker', return_value=False), \
             patch('endpoints.api_endpoint.getcwd', return_value='/test'), \
             patch('endpoints.api_endpoint.os_makedirs') as mock_makedirs, \
             patch('builtins.open', mock_open()) as mock_file, \
             patch('managers.manager_meltano.MeltanoManager.ConvertFilesToVectorStore') as mock_convert:
            
            mock_convert.return_value = AsyncMock()
            
            response = await self.endpoint.handle_file_upload(request)
            
            assert isinstance(response, web.Response)
            assert "succesvol verwerkt" in response.text
            mock_makedirs.assert_called_once()
            mock_file.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_handle_file_upload_no_files(self):
        """Test handle_file_upload endpoint with no files"""
        request = MagicMock()
        
        # Mock multipart reader with no files
        mock_reader = MagicMock()
        mock_field = MagicMock()
        mock_field.name = 'files'
        mock_field.filename = None
        mock_reader.next = AsyncMock(return_value=mock_field)
        request.multipart = AsyncMock(return_value=mock_reader)
        
        response = await self.endpoint.handle_file_upload(request)
        
        assert isinstance(response, web.Response)
        assert response.status == 400
        assert "No files uploaded" in response.text
        
    @pytest.mark.asyncio
    async def test_convert_sql_to_vectorstore(self):
        """Test convert_sql_to_vectorstore endpoint"""
        request = MagicMock()
        
        with patch('endpoints.api_endpoint.Globals.is_docker', return_value=False), \
             patch('etc.helper_functions.call_network_docker') as mock_call, \
             patch('managers.manager_meltano.MeltanoManager.ConvertSQLToVectorStore') as mock_convert:
            
            mock_convert.return_value = AsyncMock()
            
            response = await self.endpoint.convert_sql_to_vectorstore(request)
            
            assert isinstance(response, web.Response)
            assert "conversion started" in response.text
            mock_call.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_restart_endpoint_success(self):
        """Test restart endpoint with correct password"""
        request = MagicMock()
        request.json = AsyncMock(return_value={
            'admin-userpass': 'test_password'
        })
        
        with patch('endpoints.api_endpoint.Globals.is_docker', return_value=True), \
             patch('etc.helper_functions.get_value_from_env', return_value='test_user'), \
             patch('etc.helper_functions.get_password', return_value='test_password'), \
             patch('asyncio.create_task') as mock_create_task:
            
            mock_task = MagicMock()
            mock_task.add_done_callback = MagicMock()
            mock_create_task.return_value = mock_task
            
            response = await self.endpoint.restart(request)
            
            assert isinstance(response, web.Response)
            response_data = json.loads(response.text)
            assert response_data['success'] == True
            mock_create_task.assert_called_once()
            
    @pytest.mark.asyncio
    async def test_restart_endpoint_unauthorized(self):
        """Test restart endpoint with wrong password"""
        request = MagicMock()
        request.json = AsyncMock(return_value={
            'admin-userpass': 'wrong_password'
        })
        
        with patch('endpoints.api_endpoint.Globals.is_docker', return_value=True), \
             patch('etc.helper_functions.get_value_from_env', return_value='test_user'), \
             patch('etc.helper_functions.get_password', return_value='correct_password'):
            
            response = await self.endpoint.restart(request)
            
            assert isinstance(response, web.Response)
            assert response.status == 401
            response_data = json.loads(response.text)
            assert response_data['success'] == False
            
    @pytest.mark.asyncio
    async def test_load_content_dashboard(self):
        """Test load_content_dashboard method"""
        request = MagicMock()
        
        mock_verifier = MagicMock()
        mock_verifier.apps = {
            'test_app': MagicMock(section='comm')
        }
        
        with patch('endpoints.oauth._verifier_.OAuth2Verifier.get_instance', return_value=mock_verifier), \
             patch('endpoints.oauth._verifier_.OAuth2Verifier.get_token', return_value='test_token'), \
             patch('endpoints.api_endpoint.Globals.get_endpoint_address', return_value='http://test.com'):
            
            result = await self.endpoint.load_content_connectors(request)
            
            assert isinstance(result, str)
            assert 'test_app' in result
            assert 'Communicatie Platformen' in result
            
class TestAPIEndpointMiddleware:
    """Test middleware functions"""
    
    @pytest.mark.asyncio
    async def test_logfire_middleware(self):
        """Test logfire middleware"""
        request = MagicMock()
        handler = AsyncMock(return_value=web.Response())
        
        with patch('managers.manager_logfire.LogFire.logfire_middleware', return_value=web.Response()) as mock_logfire:
            from endpoints.api_endpoint import logfire_middleware
            
            response = await logfire_middleware(request, handler)
            
            assert isinstance(response, web.Response)
            mock_logfire.assert_called_once_with(request, handler)
            
    @pytest.mark.asyncio
    async def test_ip_check_middleware(self):
        """Test IP check middleware"""
        request = MagicMock()
        request.headers = {
            'CF-Connecting-IP': '***********, ********'
        }
        request.remote = '127.0.0.1'
        
        # Mock the dictionary-like behavior for request
        request_data = {}
        request.__getitem__ = lambda self, key: request_data[key]
        request.__setitem__ = lambda self, key, value: request_data.update({key: value})
        
        handler = AsyncMock(return_value=web.Response())
        
        from endpoints.api_endpoint import ip_check_middleware
        
        response = await ip_check_middleware(request, handler)
        
        assert isinstance(response, web.Response)
        assert request_data['real_ip'] == '***********'
        handler.assert_called_once_with(request)
        
    @pytest.mark.asyncio
    async def test_ip_check_middleware_no_cf_header(self):
        """Test IP check middleware without CF header"""
        request = MagicMock()
        request.headers = {}
        request.remote = '127.0.0.1'
        
        # Mock the dictionary-like behavior for request
        request_data = {}
        request.__getitem__ = lambda self, key: request_data[key]
        request.__setitem__ = lambda self, key, value: request_data.update({key: value})
        
        handler = AsyncMock(return_value=web.Response())
        
        from endpoints.api_endpoint import ip_check_middleware
        
        response = await ip_check_middleware(request, handler)
        
        assert isinstance(response, web.Response)
        assert request_data['real_ip'] == '127.0.0.1'
        handler.assert_called_once_with(request)
